# TurdParty Devcontainer Usage Guide

## Quick Start

The devcontainer is now properly configured and should work without the VS Code server permission issues you were experiencing.

### Starting the Devcontainer

1. **From VS Code:**
   - Open the project in VS Code
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
   - Type "Dev Containers: Reopen in Container"
   - Select the option and wait for the container to start

2. **From Command Line:**
   ```bash
   # Start the devcontainer
   docker compose -f .devcontainer/docker-compose.dev.yml up -d
   
   # Connect to the running container
   docker exec -it turdpartycollab_devcontainer zsh
   ```

### What Was Fixed

The previous VS Code server permission issues were resolved by:

1. **Added proper volume mounts** for VS Code server data:
   - `devcontainer-vscode-extensions:/home/<USER>/.vscode-server/extensions`
   - `devcontainer-vscode-data:/home/<USER>/.vscode-server/data`

2. **Created directories during build** with proper permissions:
   - `/home/<USER>/.vscode-server/data/Machine`
   - `/home/<USER>/.vscode-server/extensions`
   - `/home/<USER>/.vscode-server/bin`

3. **Set correct ownership** for all VS Code server directories to `vscode:vscode`

### Container Features

- **Base Image:** Microsoft's official Python 3.10 devcontainer
- **User:** `vscode` (non-root)
- **Shell:** zsh with oh-my-zsh
- **Docker-in-Docker:** Enabled with privileged mode
- **Development Tools:** Python, Node.js, Docker, testing frameworks
- **Utilities:** fzf, mc (midnight commander), git, vim, nano

### Development Workflow

Once connected to the devcontainer, you can use the built-in aliases:

```bash
# Service management
tp-start          # Start all services
tp-stop           # Stop all services
tp-restart        # Restart all services
tp-logs           # View logs

# Testing
tp-test           # Run all tests
tp-test-unit      # Run unit tests only
tp-test-integration # Run integration tests
tp-test-behave    # Run BDD tests
tp-test-e2e       # Run E2E tests

# Code quality
tp-format         # Format code with ruff
tp-lint           # Lint code
tp-fix            # Fix and format code

# Quick navigation
cdapi             # Go to /workspace/api
cdfrontend        # Go to /workspace/frontend
cdconfig          # Go to /workspace/config
cddocker          # Go to /workspace/docker
```

### Development Helper Script

Use the `./dev.sh` script for common tasks:

```bash
./dev.sh start     # Start all services
./dev.sh stop      # Stop all services
./dev.sh test      # Run all tests
./dev.sh health    # Check service health
./dev.sh inject test_injection.sh  # Test file injection
```

### Port Forwarding

The following ports are automatically forwarded:
- **8000** - TurdParty API
- **3000** - Frontend
- **5601** - Kibana
- **9200** - Elasticsearch
- **5000** - Logstash

### Troubleshooting

If you encounter issues:

1. **Container won't start:**
   ```bash
   docker compose -f .devcontainer/docker-compose.dev.yml down
   docker compose -f .devcontainer/docker-compose.dev.yml build --no-cache
   docker compose -f .devcontainer/docker-compose.dev.yml up -d
   ```

2. **VS Code connection issues:**
   - Ensure the container is running: `docker ps | grep turdpartycollab_devcontainer`
   - Check container logs: `docker logs turdpartycollab_devcontainer`
   - Try "Dev Containers: Rebuild Container" in VS Code

3. **Permission issues:**
   - The container runs as the `vscode` user
   - All workspace files should be owned by `vscode:vscode`
   - VS Code server directories are properly configured

### Network Configuration

The devcontainer connects to the `turdpartycollab_net` network, allowing communication with other TurdParty services when they're running.

### Persistent Data

The following data is persisted across container restarts:
- VS Code extensions (`devcontainer-vscode-extensions` volume)
- VS Code server data (`devcontainer-vscode-data` volume)
- Command history (`devcontainer-bashhistory` volume)
- Workspace files (bind mount to host)
