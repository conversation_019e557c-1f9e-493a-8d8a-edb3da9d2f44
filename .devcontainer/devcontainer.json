{
  "name": "TurdParty Development Environment",
  "dockerComposeFile": "docker-compose.dev.yml",
  "service": "devcontainer",
  "workspaceFolder": "/workspace",
  "shutdownAction": "stopCompose",

  // Configure tool-specific properties
  "customizations": {
    "vscode": {
      "extensions": [
        // Python extensions
        "ms-python.python",
        "ms-python.pylance",
        "ms-python.debugpy",

        // Docker extensions
        "ms-azuretools.vscode-docker",

        // API development
        "humao.rest-client",

        // Frontend development
        "esbenp.prettier-vscode",

        // Git and collaboration
        "eamodio.gitlens",
        "github.vscode-pull-request-github",

        // Utilities
        "redhat.vscode-yaml",
        "streetsidesoftware.code-spell-checker",
        "ms-playwright.playwright"
      ],
      "settings": {
        "python.defaultInterpreterPath": "/usr/local/bin/python",
        "python.linting.enabled": false,
        "[python]": {
          "editor.formatOnSave": true,
          "editor.codeActionsOnSave": {
            "source.organizeImports": "explicit"
          },
          "editor.defaultFormatter": "ms-python.python"
        },
        "editor.formatOnSave": true,
        "files.exclude": {
          "**/__pycache__": true,
          "**/.pytest_cache": true,
          "**/node_modules": true,
          "**/.git": false
        },
        "docker.dockerPath": "/usr/local/bin/docker",
        "terminal.integrated.defaultProfile.linux": "zsh"
      }
    }
  },

  // Use 'forwardPorts' to make a list of ports inside the container available locally
  "forwardPorts": [
    8000,  // API
    3000,  // Frontend
    5601,  // Kibana
    9200,  // Elasticsearch
    5000   // Logstash
  ],

  // Port attributes
  "portsAttributes": {
    "8000": {
      "label": "TurdParty API",
      "onAutoForward": "notify"
    },
    "3000": {
      "label": "Frontend",
      "onAutoForward": "silent"
    },
    "5601": {
      "label": "Kibana",
      "onAutoForward": "silent"
    },
    "9200": {
      "label": "Elasticsearch",
      "onAutoForward": "silent"
    },
    "5000": {
      "label": "Logstash",
      "onAutoForward": "silent"
    }
  },

  // Use 'postCreateCommand' to run commands after the container is created
  "postCreateCommand": ".devcontainer/post-create.sh",

  // Use 'postStartCommand' to run commands after the container starts
  "postStartCommand": ".devcontainer/post-start.sh",

  // Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root
  "remoteUser": "vscode",

  // Environment variables
  "containerEnv": {
    "PYTHONPATH": "/workspace",
    "DOCKER_BUILDKIT": "1",
    "COMPOSE_DOCKER_CLI_BUILD": "1"
  },

  // Mount the Docker socket for Docker-in-Docker
  "mounts": [
    "source=/var/run/docker.sock,target=/var/run/docker-host.sock,type=bind"
  ]
}
