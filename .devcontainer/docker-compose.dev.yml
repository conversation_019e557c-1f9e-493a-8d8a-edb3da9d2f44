services:
  devcontainer:
    container_name: turdpartycollab_devcontainer
    build:
      context: ..
      dockerfile: docker/Dockerfile.dev
    volumes:
      # Mount the workspace
      - ..:/workspace:cached

      # Mount Docker socket for Docker-in-Docker
      - /var/run/docker.sock:/var/run/docker-host.sock

      # Persist command history
      - devcontainer-bashhistory:/commandhistory

      # Persist VS Code extensions and data
      - devcontainer-vscode-extensions:/home/<USER>/.vscode-server/extensions
      - devcontainer-vscode-data:/home/<USER>/.vscode-server/data
    
    environment:
      # Development environment variables
      - PYTHONPATH=/workspace
      - DEBUG=true
      - TEST_MODE=true
      - DOCKER_BUILDKIT=1
      - COMPOSE_DOCKER_CLI_BUILD=1
      
      # ELK Stack configuration
      - ELASTICSEARCH_HOST=elasticsearch
      - ELASTICSEARCH_PORT=9200
      - LOGSTASH_HOST=logstash
      - LOGSTASH_PORT=5000
      
      # API configuration
      - API_PREFIX=/api/v1
      - FILE_UPLOAD_DIR=/workspace/uploads
    
    networks:
      - turdpartycollab_net
    
    # Keep container running
    command: sleep infinity
    
    # Enable privileged mode for Docker-in-Docker
    privileged: true
    
    # Add capabilities for Docker
    cap_add:
      - SYS_ADMIN
    
    # Security options for Docker-in-Docker
    security_opt:
      - seccomp:unconfined
    
    # Restart policy
    restart: unless-stopped

volumes:
  devcontainer-bashhistory:
    name: turdpartycollab_devcontainer_bashhistory
  devcontainer-vscode-extensions:
    name: turdpartycollab_devcontainer_vscode_extensions
  devcontainer-vscode-data:
    name: turdpartycollab_devcontainer_vscode_data

networks:
  turdpartycollab_net:
    name: turdpartycollab_net
    driver: bridge
