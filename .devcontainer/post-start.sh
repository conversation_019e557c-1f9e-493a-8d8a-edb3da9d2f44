#!/bin/bash

# Post-start script for TurdParty devcontainer
echo "🔄 TurdParty devcontainer post-start setup..."

# Ensure Docker daemon is running
echo "🐳 Checking Docker daemon..."
if ! docker info > /dev/null 2>&1; then
    echo "⚠️  Docker daemon not running, attempting to start..."
    sudo service docker start
    sleep 5
fi

# Check if Docker is now available
if docker info > /dev/null 2>&1; then
    echo "✅ Docker daemon is running"
else
    echo "❌ Docker daemon failed to start"
fi

# Set proper permissions for workspace
sudo chown -R vscode:vscode /workspace

# Check if services are already running
echo "🔍 Checking service status..."
if docker-compose ps | grep -q "Up"; then
    echo "✅ Some services are already running"
else
    echo "💡 No services running. Use './dev.sh start' to start services"
fi

# Display helpful information
echo ""
echo "🎯 TurdParty Development Environment Ready!"
echo ""
echo "📋 Available commands:"
echo "   ./dev.sh start     # Start all services"
echo "   ./dev.sh stop      # Stop all services"
echo "   ./dev.sh health    # Check service health"
echo "   ./dev.sh logs      # View service logs"
echo "   ./dev.sh test      # Run tests"
echo "   ./dev.sh inject <file>  # Test file injection"
echo ""
echo "🔧 Development aliases:"
echo "   tp-start          # Start services"
echo "   tp-stop           # Stop services"
echo "   tp-logs           # View logs"
echo "   tp-test           # Run tests"
echo "   tp-format         # Format code"
echo ""
echo "🌐 Service URLs (when running):"
echo "   API: http://localhost:8000"
echo "   API Documentation: http://localhost:8000/docs"
echo "   Kibana: http://localhost:5601"
echo "   Elasticsearch: http://localhost:9200"
echo ""
echo "📁 Important directories:"
echo "   /workspace/api     # API source code"
echo "   /workspace/config  # Configuration files"
echo "   /workspace/uploads # File upload directory"
echo "   /workspace/logs    # Log files"
