#!/bin/bash

# Test script to verify devcontainer setup
echo "🧪 Testing TurdParty devcontainer setup..."

# Test 1: Check if we're in the right directory
echo "📁 Current directory: $(pwd)"
if [ "$(pwd)" = "/workspace" ]; then
    echo "✅ Working directory is correct"
else
    echo "❌ Working directory is incorrect"
fi

# Test 2: Check Python
echo "🐍 Testing Python..."
python3 --version
if [ $? -eq 0 ]; then
    echo "✅ Python is working"
else
    echo "❌ Python is not working"
fi

# Test 3: Check pip
echo "📦 Testing pip..."
pip --version
if [ $? -eq 0 ]; then
    echo "✅ pip is working"
else
    echo "❌ pip is not working"
fi

# Test 4: Check Node.js
echo "📦 Testing Node.js..."
node --version
if [ $? -eq 0 ]; then
    echo "✅ Node.js is working"
else
    echo "❌ Node.js is not working"
fi

# Test 5: Check npm
echo "📦 Testing npm..."
npm --version
if [ $? -eq 0 ]; then
    echo "✅ npm is working"
else
    echo "❌ npm is not working"
fi

# Test 6: Check Docker
echo "🐳 Testing Docker..."
docker --version
if [ $? -eq 0 ]; then
    echo "✅ Docker CLI is available"
else
    echo "❌ Docker CLI is not available"
fi

# Test 7: Check Docker daemon
echo "🐳 Testing Docker daemon..."
docker info > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Docker daemon is running"
else
    echo "❌ Docker daemon is not running"
fi

# Test 8: Check git
echo "🔧 Testing git..."
git --version
if [ $? -eq 0 ]; then
    echo "✅ git is working"
else
    echo "❌ git is not working"
fi

# Test 9: Check zsh
echo "🐚 Testing zsh..."
zsh --version
if [ $? -eq 0 ]; then
    echo "✅ zsh is working"
else
    echo "❌ zsh is not working"
fi

# Test 10: Check fzf
echo "🔍 Testing fzf..."
which fzf > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ fzf is available"
else
    echo "❌ fzf is not available"
fi

# Test 11: Check mc
echo "📁 Testing mc..."
which mc > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ mc is available"
else
    echo "❌ mc is not available"
fi

# Test 12: Check workspace permissions
echo "🔐 Testing workspace permissions..."
if [ -w "/workspace" ]; then
    echo "✅ Workspace is writable"
else
    echo "❌ Workspace is not writable"
fi

echo ""
echo "🎯 Devcontainer setup test complete!"
