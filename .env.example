# TurdParty Environment Configuration

# =============================================================================
# Vagrant Configuration
# =============================================================================

# Vagrant Mode: 'host' (default) or 'container'
# - host: Use vagrant serve running on the host system (port 40000)
# - container: Use the Docker container vagrant service
VAGRANT_MODE=host

# Vagrant gRPC Port (default: 40000)
# This port is used for communication between TurdParty and Vagrant VMs
VAGRANT_GRPC_PORT=40000

# Vagrant Host (when using host mode)
# Set to the IP address where vagrant serve is running
VAGRANT_HOST=localhost

# =============================================================================
# API Configuration
# =============================================================================
DEBUG=true
TEST_MODE=true
API_PREFIX=/api/v1
FILE_UPLOAD_DIR=/app/uploads

# ELK Stack Configuration
ELASTICSEARCH_HOST=elasticsearch
ELASTICSEARCH_PORT=9200
LOGSTASH_HOST=logstash
LOGSTASH_PORT=5000

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Application Settings
PYTHONPATH=/app
