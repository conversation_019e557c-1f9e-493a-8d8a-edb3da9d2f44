#!/bin/bash

# TurdParty Pre-Push Hook
# Discourages pushing to GitHub during business hours (7am-18:00 CET)
# Local commits are always allowed

set -e

# ANSI color codes
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print banner
print_banner() {
    echo ""
    print_colored $CYAN "╔══════════════════════════════════════════════════════════════╗"
    print_colored $CYAN "║                    🔬 TurdParty Pre-Push Hook                ║"
    print_colored $CYAN "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to get current CET time
get_cet_time() {
    # Try different timezone formats
    if command -v timedatectl >/dev/null 2>&1; then
        # Use timedatectl if available (systemd systems)
        TZ='Europe/Berlin' date '+%H'
    elif [ -f /usr/share/zoneinfo/Europe/Berlin ]; then
        # Use zoneinfo if available
        TZ='Europe/Berlin' date '+%H'
    else
        # Fallback: calculate CET from UTC
        local utc_hour=$(date -u '+%H')
        local cet_hour=$((utc_hour + 1))  # CET is UTC+1 (simplified, ignoring DST)
        if [ $cet_hour -ge 24 ]; then
            cet_hour=$((cet_hour - 24))
        fi
        echo $cet_hour
    fi
}

# Function to check if it's business hours
is_business_hours() {
    local current_hour=$(get_cet_time)
    
    # Remove leading zero for comparison
    current_hour=$((10#$current_hour))
    
    # Business hours: 7am (07) to 6pm (18) CET
    if [ $current_hour -ge 7 ] && [ $current_hour -lt 18 ]; then
        return 0  # true - it is business hours
    else
        return 1  # false - it is not business hours
    fi
}

# Function to check if pushing to GitHub
is_github_push() {
    local remote_url="$1"
    
    # Check if the remote URL contains github.com
    if echo "$remote_url" | grep -q "github\.com"; then
        return 0  # true - pushing to GitHub
    else
        return 1  # false - not pushing to GitHub
    fi
}

# Function to get day of week
get_day_of_week() {
    date '+%u'  # 1=Monday, 7=Sunday
}

# Function to check if it's a weekend
is_weekend() {
    local day=$(get_day_of_week)
    if [ $day -eq 6 ] || [ $day -eq 7 ]; then  # Saturday or Sunday
        return 0  # true - it is weekend
    else
        return 1  # false - it is not weekend
    fi
}

# Main hook logic
main() {
    print_banner
    
    # Read the push details
    local remote="$1"
    local url="$2"
    
    print_colored $BLUE "🔍 Checking push destination: $remote ($url)"
    
    # Check if this is a GitHub push
    if ! is_github_push "$url"; then
        print_colored $GREEN "✅ Non-GitHub remote detected. Push allowed."
        echo ""
        exit 0
    fi
    
    print_colored $YELLOW "🐙 GitHub push detected!"
    
    # Check if it's weekend
    if is_weekend; then
        print_colored $GREEN "🎉 Weekend detected! Push allowed."
        echo ""
        exit 0
    fi
    
    # Check if it's business hours
    if is_business_hours; then
        local current_hour=$(get_cet_time)
        print_colored $RED "⚠️  BUSINESS HOURS DETECTED!"
        echo ""
        print_colored $YELLOW "Current time: ${current_hour}:00 CET"
        print_colored $YELLOW "Business hours: 07:00 - 18:00 CET (Monday-Friday)"
        echo ""
        print_colored $RED "🚫 Pushing to GitHub during business hours is discouraged!"
        echo ""
        print_colored $CYAN "💡 Recommendations:"
        print_colored $CYAN "   • Continue working locally (local commits are fine)"
        print_colored $CYAN "   • Push after 18:00 CET or before 07:00 CET"
        print_colored $CYAN "   • Push during weekends"
        print_colored $CYAN "   • Use 'git push --no-verify' to override (if urgent)"
        echo ""
        
        # Ask for confirmation
        print_colored $PURPLE "❓ Do you really want to push during business hours? [y/N]: "
        read -r response
        
        case "$response" in
            [yY]|[yY][eE][sS])
                print_colored $YELLOW "⚠️  Proceeding with business hours push..."
                print_colored $YELLOW "📝 Consider adding a note about urgency in your commit message."
                echo ""
                ;;
            *)
                print_colored $RED "❌ Push cancelled. Work locally and push later!"
                echo ""
                print_colored $CYAN "💡 You can continue working with local commits:"
                print_colored $CYAN "   git add ."
                print_colored $CYAN "   git commit -m \"your message\""
                echo ""
                exit 1
                ;;
        esac
    else
        local current_hour=$(get_cet_time)
        print_colored $GREEN "✅ Outside business hours (${current_hour}:00 CET). Push allowed!"
        echo ""
    fi
    
    print_colored $GREEN "🚀 Proceeding with GitHub push..."
    echo ""
}

# Run the main function with all arguments
main "$@"
