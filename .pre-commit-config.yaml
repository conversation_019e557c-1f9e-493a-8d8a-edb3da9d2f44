repos:
  # Ruff - Fast Python linter and formatter (replaces black, isort, flake8, pylint)
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.9
    hooks:
      # Ruff linter
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
      # Ruff formatter (replaces black)
      - id: ruff-format

  # Python type checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        additional_dependencies: [types-requests, types-python-dateutil]
        args: [--strict, --ignore-missing-imports]

  # General file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-docstring-first
      - id: debug-statements
      - id: requirements-txt-fixer

  # Docker file linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        args: [--ignore, DL3008, --ignore, DL3009]

  # YAML formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v4.0.0-alpha.8
    hooks:
      - id: prettier
        types: [yaml]
        exclude: ^(.devcontainer/|docker-compose)

  # Shell script linting
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.6
    hooks:
      - id: shellcheck
        args: [--severity=warning]

  # Security scanning with Bandit
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-c, pyproject.toml]
        additional_dependencies: ["bandit[toml]"]
        exclude: ^tests/

  # Dependency vulnerability scanning
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check
        args: [--ignore=70612]  # Ignore specific CVE if needed

  # Commit message formatting
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.13.0
    hooks:
      - id: commitizen
        stages: [commit-msg]

  # Local hooks for project-specific checks
  - repo: local
    hooks:
      # Run unit tests before commit
      - id: pytest-unit
        name: Run unit tests
        entry: python -m pytest tests/unit/ -x --ff -q
        language: system
        types: [python]
        pass_filenames: false
        stages: [pre-commit]

      # Validate API schema
      - id: api-schema-check
        name: Validate API schema
        entry: python -c "from api.v1.application import get_application; app = get_application(); print('✅ API schema valid')"
        language: system
        files: ^api/
        pass_filenames: false
