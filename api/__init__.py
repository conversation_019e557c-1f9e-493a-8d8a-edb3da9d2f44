"""
TurdParty API Package

This package provides the core API functionality for the TurdParty malware analysis platform.
It includes models, services, middleware, and route handlers for file upload, VM management,
and malware analysis workflows.

Key Components:
    - models: Data models for API entities (files, VMs, workflows)
    - services: Business logic services (VM management, file injection, metrics)
    - middleware: Request/response middleware (ECS logging, exception handling)
    - v1: API version 1 routes and application factory

The API follows RESTful principles and provides endpoints for:
    - File upload and management
    - VM lifecycle management (create, start, stop, destroy)
    - File injection into VMs
    - Workflow orchestration and monitoring
    - ECS data collection and reporting

Usage:
    This package is typically imported by the main application or service containers.
    Individual components can be imported as needed:

    from api.models import FileUpload, VMInstance
    from api.services import VMService, FileInjectionService
    from api.v1 import create_app
"""
