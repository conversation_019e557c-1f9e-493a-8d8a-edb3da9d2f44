"""
Custom API Exceptions for TurdParty

This module provides custom exception classes that map to standardized error responses.
All exceptions include proper HTTP status codes and error codes for consistent API behavior.
"""

from typing import Optional, List, Dict, Any
from fastapi import HTTPException
from api.models.error_responses import <PERSON><PERSON>r<PERSON><PERSON>, ValidationErrorDetail


class TurdPartyAPIException(HTTPException):
    """Base exception class for all TurdParty API errors."""
    
    def __init__(
        self,
        status_code: int,
        error_code: ErrorCode,
        message: str,
        details: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None
    ):
        """
        Initialize the base TurdParty API exception.

        Args:
            status_code: HTTP status code for the error
            error_code: Standardized error code from Error<PERSON>ode enum
            message: Human-readable error message
            details: Optional additional error details
            headers: Optional HTTP headers to include in response
        """
        self.error_code = error_code
        self.message = message
        self.details = details
        super().__init__(status_code=status_code, detail=message, headers=headers)


class ValidationException(TurdPartyAPIException):
    """Exception for validation errors (422)."""
    
    def __init__(
        self,
        message: str = "Validation failed",
        details: Optional[str] = None,
        validation_errors: Optional[List[ValidationErrorDetail]] = None
    ):
        """
        Initialize validation exception for request validation errors.

        Args:
            message: Error message describing the validation failure
            details: Optional additional details about the validation error
            validation_errors: List of specific field validation errors
        """
        self.validation_errors = validation_errors or []
        super().__init__(
            status_code=422,
            error_code=ErrorCode.VALIDATION_ERROR,
            message=message,
            details=details
        )


class ResourceNotFoundException(TurdPartyAPIException):
    """Exception for resource not found errors (404)."""
    
    def __init__(
        self,
        resource_type: str,
        resource_id: str,
        message: Optional[str] = None
    ):
        """
        Initialize resource not found exception.

        Args:
            resource_type: Type of resource that was not found (e.g., "VM", "File")
            resource_id: Unique identifier of the missing resource
            message: Optional custom error message (auto-generated if not provided)
        """
        if not message:
            message = f"{resource_type} with ID '{resource_id}' not found"
        
        super().__init__(
            status_code=404,
            error_code=ErrorCode.RESOURCE_NOT_FOUND,
            message=message,
            details=f"Resource type: {resource_type}, ID: {resource_id}"
        )


class ResourceConflictException(TurdPartyAPIException):
    """Exception for resource conflict errors (409)."""
    
    def __init__(
        self,
        resource_type: str,
        message: Optional[str] = None,
        details: Optional[str] = None
    ):
        """
        Initialize resource conflict exception.

        Args:
            resource_type: Type of resource causing the conflict
            message: Optional custom error message (auto-generated if not provided)
            details: Optional additional details about the conflict
        """
        if not message:
            message = f"{resource_type} already exists or conflicts with existing resource"
        
        super().__init__(
            status_code=409,
            error_code=ErrorCode.RESOURCE_CONFLICT,
            message=message,
            details=details
        )


class InvalidInputException(TurdPartyAPIException):
    """Exception for invalid input errors (400)."""
    
    def __init__(
        self,
        message: str = "Invalid input provided",
        details: Optional[str] = None
    ):
        """
        Initialize invalid input exception for malformed requests.

        Args:
            message: Error message describing the invalid input
            details: Optional additional details about what was invalid
        """
        super().__init__(
            status_code=400,
            error_code=ErrorCode.INVALID_INPUT,
            message=message,
            details=details
        )


class FileTooLargeException(TurdPartyAPIException):
    """Exception for file size limit errors (413)."""
    
    def __init__(
        self,
        file_size: int,
        max_size: int,
        message: Optional[str] = None
    ):
        """
        Initialize file too large exception for upload size violations.

        Args:
            file_size: Actual size of the uploaded file in bytes
            max_size: Maximum allowed file size in bytes
            message: Optional custom error message (auto-generated if not provided)
        """
        if not message:
            message = f"File size {file_size} bytes exceeds maximum allowed size of {max_size} bytes"
        
        super().__init__(
            status_code=413,
            error_code=ErrorCode.FILE_TOO_LARGE,
            message=message,
            details=f"File size: {file_size}, Max size: {max_size}"
        )


class AuthenticationRequiredException(TurdPartyAPIException):
    """Exception for authentication required errors (401)."""
    
    def __init__(
        self,
        message: str = "Authentication required",
        details: Optional[str] = None
    ):
        """
        Initialize authentication required exception for unauthorized access.

        Args:
            message: Error message describing the authentication requirement
            details: Optional additional details about authentication failure
        """
        super().__init__(
            status_code=401,
            error_code=ErrorCode.AUTHENTICATION_REQUIRED,
            message=message,
            details=details,
            headers={"WWW-Authenticate": "Bearer"}
        )


class AccessDeniedException(TurdPartyAPIException):
    """Exception for access denied errors (403)."""
    
    def __init__(
        self,
        message: str = "Access denied",
        details: Optional[str] = None
    ):
        """
        Initialize access denied exception for forbidden operations.

        Args:
            message: Error message describing the access denial
            details: Optional additional details about why access was denied
        """
        super().__init__(
            status_code=403,
            error_code=ErrorCode.ACCESS_DENIED,
            message=message,
            details=details
        )


class RateLimitExceededException(TurdPartyAPIException):
    """Exception for rate limit exceeded errors (429)."""
    
    def __init__(
        self,
        retry_after: int = 60,
        message: str = "Rate limit exceeded",
        details: Optional[str] = None
    ):
        """
        Initialize rate limit exceeded exception for API throttling.

        Args:
            retry_after: Number of seconds to wait before retrying
            message: Error message describing the rate limit violation
            details: Optional additional details about the rate limit
        """
        self.retry_after = retry_after
        super().__init__(
            status_code=429,
            error_code=ErrorCode.RATE_LIMIT_EXCEEDED,
            message=message,
            details=details,
            headers={"Retry-After": str(retry_after)}
        )


class ServiceUnavailableException(TurdPartyAPIException):
    """Exception for service unavailable errors (503)."""
    
    def __init__(
        self,
        service_name: str,
        message: Optional[str] = None,
        retry_after: Optional[int] = None
    ):
        """
        Initialize service unavailable exception for temporary outages.

        Args:
            service_name: Name of the unavailable service
            message: Optional custom error message (auto-generated if not provided)
            retry_after: Optional seconds to wait before retrying
        """
        if not message:
            message = f"Service '{service_name}' is temporarily unavailable"
        
        headers = {}
        if retry_after:
            headers["Retry-After"] = str(retry_after)
        
        super().__init__(
            status_code=503,
            error_code=ErrorCode.SERVICE_UNAVAILABLE,
            message=message,
            details=f"Service: {service_name}",
            headers=headers if headers else None
        )


class ExternalServiceException(TurdPartyAPIException):
    """Exception for external service errors (502)."""
    
    def __init__(
        self,
        service_name: str,
        message: Optional[str] = None,
        details: Optional[str] = None
    ):
        """
        Initialize external service exception for third-party service errors.

        Args:
            service_name: Name of the external service that failed
            message: Optional custom error message (auto-generated if not provided)
            details: Optional additional details about the service error
        """
        if not message:
            message = f"External service '{service_name}' error"
        
        super().__init__(
            status_code=502,
            error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
            message=message,
            details=details
        )


class VMCreationException(TurdPartyAPIException):
    """Exception for VM creation failures (500)."""
    
    def __init__(
        self,
        message: str = "Failed to create virtual machine",
        details: Optional[str] = None
    ):
        """
        Initialize VM creation exception for virtual machine setup failures.

        Args:
            message: Error message describing the VM creation failure
            details: Optional additional details about what went wrong
        """
        super().__init__(
            status_code=500,
            error_code=ErrorCode.VM_CREATION_FAILED,
            message=message,
            details=details
        )


class FileInjectionException(TurdPartyAPIException):
    """Exception for file injection failures (500)."""
    
    def __init__(
        self,
        message: str = "Failed to inject file into virtual machine",
        details: Optional[str] = None
    ):
        """
        Initialize file injection exception for file transfer failures.

        Args:
            message: Error message describing the file injection failure
            details: Optional additional details about the injection error
        """
        super().__init__(
            status_code=500,
            error_code=ErrorCode.FILE_INJECTION_FAILED,
            message=message,
            details=details
        )


class StorageException(TurdPartyAPIException):
    """Exception for storage-related errors (500)."""
    
    def __init__(
        self,
        message: str = "Storage operation failed",
        details: Optional[str] = None
    ):
        """
        Initialize storage exception for file storage and retrieval errors.

        Args:
            message: Error message describing the storage operation failure
            details: Optional additional details about the storage error
        """
        super().__init__(
            status_code=500,
            error_code=ErrorCode.STORAGE_ERROR,
            message=message,
            details=details
        )
