"""
Enhanced Exception Handlers for TurdParty API

This module provides comprehensive exception handling with standardized error responses,
proper logging, and correlation ID tracking for all API errors.
"""

import logging
import traceback
import uuid
from datetime import datetime
from typing import Union

from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from api.models.error_responses import (
    ErrorResponse, ErrorCode, ValidationErrorDetail, 
    create_error_response, STATUS_CODE_MAPPINGS
)
from api.exceptions.api_exceptions import TurdPartyAPIException


logger = logging.getLogger(__name__)


def get_correlation_id(request: Request) -> str:
    """Get or generate correlation ID for request tracing."""
    correlation_id = getattr(request.state, 'correlation_id', None)
    if not correlation_id:
        correlation_id = str(uuid.uuid4())
        request.state.correlation_id = correlation_id
    return correlation_id


async def turdparty_exception_handler(
    request: Request, 
    exc: TurdPartyAPIException
) -> JSONResponse:
    """Handle custom TurdParty API exceptions."""
    correlation_id = get_correlation_id(request)
    
    # Log the exception with context
    logger.warning(
        f"TurdParty API Exception: {exc.error_code.value}",
        extra={
            "correlation_id": correlation_id,
            "error_code": exc.error_code.value,
            "status_code": exc.status_code,
            "message": exc.message,
            "details": exc.details,
            "method": request.method,
            "url": str(request.url),
            "user_agent": request.headers.get("user-agent"),
        }
    )
    
    # Create standardized error response
    error_response = create_error_response(
        error_code=exc.error_code,
        message=exc.message,
        details=exc.details,
        request_id=correlation_id,
        validation_errors=getattr(exc, 'validation_errors', None),
        retry_after=getattr(exc, 'retry_after', None)
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.dict(),
        headers=exc.headers
    )


async def validation_exception_handler(
    request: Request, 
    exc: Union[RequestValidationError, ValidationError]
) -> JSONResponse:
    """Handle Pydantic validation errors."""
    correlation_id = get_correlation_id(request)
    
    # Convert Pydantic errors to our format
    validation_errors = []
    for error in exc.errors():
        field_path = " -> ".join(str(loc) for loc in error["loc"])
        validation_errors.append(
            ValidationErrorDetail(
                field=field_path,
                message=error["msg"],
                code=error["type"],
                value=error.get("input")
            )
        )
    
    # Log validation error
    logger.info(
        f"Validation error: {len(validation_errors)} field(s)",
        extra={
            "correlation_id": correlation_id,
            "validation_errors": [ve.dict() for ve in validation_errors],
            "method": request.method,
            "url": str(request.url),
        }
    )
    
    # Create error response
    error_response = create_error_response(
        error_code=ErrorCode.VALIDATION_ERROR,
        message="Request validation failed",
        details=f"{len(validation_errors)} validation error(s) found",
        request_id=correlation_id,
        validation_errors=validation_errors,
        documentation_url="/docs"
    )
    
    return JSONResponse(
        status_code=422,
        content=error_response.dict()
    )


async def http_exception_handler(
    request: Request, 
    exc: Union[HTTPException, StarletteHTTPException]
) -> JSONResponse:
    """Handle standard HTTP exceptions."""
    correlation_id = get_correlation_id(request)
    
    # Map status code to error code
    error_code = STATUS_CODE_MAPPINGS.get(exc.status_code, ErrorCode.INTERNAL_SERVER_ERROR)
    
    # Log the exception
    logger.warning(
        f"HTTP Exception: {exc.status_code}",
        extra={
            "correlation_id": correlation_id,
            "status_code": exc.status_code,
            "detail": exc.detail,
            "method": request.method,
            "url": str(request.url),
        }
    )
    
    # Create error response
    error_response = create_error_response(
        error_code=error_code,
        message=str(exc.detail),
        request_id=correlation_id
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.dict(),
        headers=getattr(exc, 'headers', None)
    )


async def general_exception_handler(
    request: Request, 
    exc: Exception
) -> JSONResponse:
    """Handle unexpected exceptions."""
    correlation_id = get_correlation_id(request)
    
    # Log the full exception with stack trace
    logger.error(
        f"Unhandled exception: {type(exc).__name__}",
        exc_info=True,
        extra={
            "correlation_id": correlation_id,
            "exception_type": type(exc).__name__,
            "exception_message": str(exc),
            "method": request.method,
            "url": str(request.url),
            "user_agent": request.headers.get("user-agent"),
            "stack_trace": traceback.format_exc()
        }
    )
    
    # Create generic error response (don't expose internal details)
    error_response = create_error_response(
        error_code=ErrorCode.INTERNAL_SERVER_ERROR,
        message="An unexpected error occurred",
        details="The error has been logged and will be investigated",
        request_id=correlation_id
    )
    
    return JSONResponse(
        status_code=500,
        content=error_response.dict()
    )


def setup_exception_handlers(app):
    """Set up all exception handlers for the FastAPI app."""
    
    # Custom TurdParty exceptions (highest priority)
    app.add_exception_handler(TurdPartyAPIException, turdparty_exception_handler)
    
    # Validation errors
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(ValidationError, validation_exception_handler)
    
    # Standard HTTP exceptions
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    
    # Catch-all for unexpected exceptions
    app.add_exception_handler(Exception, general_exception_handler)
    
    logger.info("Exception handlers configured successfully")


# Utility functions for common error scenarios
def raise_not_found(resource_type: str, resource_id: str):
    """Raise a standardized not found exception."""
    from api.exceptions.api_exceptions import ResourceNotFoundException
    raise ResourceNotFoundException(resource_type, resource_id)


def raise_validation_error(message: str, field_errors: list = None):
    """Raise a standardized validation exception."""
    from api.exceptions.api_exceptions import ValidationException
    validation_errors = []
    if field_errors:
        for field_error in field_errors:
            validation_errors.append(
                ValidationErrorDetail(
                    field=field_error.get('field', 'unknown'),
                    message=field_error.get('message', 'Invalid value'),
                    code=field_error.get('code', 'invalid'),
                    value=field_error.get('value')
                )
            )
    raise ValidationException(message, validation_errors=validation_errors)


def raise_service_unavailable(service_name: str, retry_after: int = None):
    """Raise a standardized service unavailable exception."""
    from api.exceptions.api_exceptions import ServiceUnavailableException
    raise ServiceUnavailableException(service_name, retry_after=retry_after)
