"""
TurdParty API Models Package

This package contains data models and schemas for the TurdParty API.
These models define the structure and validation rules for API entities
used throughout the malware analysis platform.

Key Models:
    - error_responses: Standardized error response models for API consistency
    - file_injection: Models for file injection operations and status tracking
    - vm_management: Models for VM lifecycle management and configuration

Model Categories:
    1. **Request/Response Models**: Pydantic models for API input/output validation
    2. **Error Models**: Standardized error response structures
    3. **Configuration Models**: VM and service configuration schemas
    4. **Status Models**: Enumeration and status tracking models

Usage:
    Import specific models as needed:

    from api.models.file_injection import FileInjectionRequest, FileInjectionResponse
    from api.models.vm_management import VMCreateRequest, VMStatusResponse
    from api.models.error_responses import ErrorResponse, ValidationErrorResponse

Integration:
    These models are used by:
    - API route handlers for request/response validation
    - Service layer for data transfer objects
    - OpenAPI documentation generation
    - Client SDK generation
"""
