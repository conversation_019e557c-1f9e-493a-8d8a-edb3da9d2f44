"""
Standardized Error Response Models for TurdParty API

This module provides consistent error response formats across all API endpoints.
All errors follow a standardized structure with proper HTTP status codes and error codes.
"""

from datetime import datetime, timezone
from enum import Enum
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class ErrorCode(str, Enum):
    """Standardized error codes for the TurdParty API."""
    
    # Validation Errors (4xx)
    VALIDATION_ERROR = "VALIDATION_ERROR"
    INVALID_INPUT = "INVALID_INPUT"
    MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD"
    INVALID_FILE_FORMAT = "INVALID_FILE_FORMAT"
    FILE_TOO_LARGE = "FILE_TOO_LARGE"
    INVALID_UUID = "INVALID_UUID"
    
    # Authentication & Authorization (4xx)
    AUTHENTICATION_REQUIRED = "AUTHENTICATION_REQUIRED"
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS"
    ACCESS_DENIED = "ACCESS_DENIED"
    INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    
    # Resource Errors (4xx)
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"
    RESOURCE_ALREADY_EXISTS = "RESOURCE_ALREADY_EXISTS"
    RESOURCE_CONFLICT = "RESOURCE_CONFLICT"
    RESOURCE_LOCKED = "RESOURCE_LOCKED"
    
    # Rate Limiting (4xx)
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    QUOTA_EXCEEDED = "QUOTA_EXCEEDED"
    
    # Service Errors (5xx)
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    DATABASE_ERROR = "DATABASE_ERROR"
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
    TIMEOUT_ERROR = "TIMEOUT_ERROR"
    
    # Business Logic Errors (4xx/5xx)
    VM_CREATION_FAILED = "VM_CREATION_FAILED"
    FILE_INJECTION_FAILED = "FILE_INJECTION_FAILED"
    STORAGE_ERROR = "STORAGE_ERROR"
    WORKFLOW_ERROR = "WORKFLOW_ERROR"


class ValidationErrorDetail(BaseModel):
    """Detailed validation error information."""
    
    field: str = Field(..., description="The field that failed validation")
    message: str = Field(..., description="Human-readable error message")
    code: str = Field(..., description="Machine-readable error code")
    value: Optional[Any] = Field(None, description="The invalid value that was provided")


class ErrorResponse(BaseModel):
    """Standardized error response format for all API endpoints."""
    
    error: bool = Field(True, description="Always true for error responses")
    error_code: ErrorCode = Field(..., description="Machine-readable error code")
    message: str = Field(..., description="Human-readable error message")
    details: Optional[str] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Error timestamp")
    request_id: Optional[str] = Field(None, description="Unique request identifier for tracing")
    
    # Optional fields for specific error types
    validation_errors: Optional[List[ValidationErrorDetail]] = Field(
        None, description="Detailed validation errors for 422 responses"
    )
    retry_after: Optional[int] = Field(
        None, description="Seconds to wait before retrying (for rate limiting)"
    )
    documentation_url: Optional[str] = Field(
        None, description="URL to relevant documentation"
    )


class SuccessResponse(BaseModel):
    """Standardized success response format."""
    
    success: bool = Field(True, description="Always true for success responses")
    message: str = Field(..., description="Human-readable success message")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Response timestamp")
    request_id: Optional[str] = Field(None, description="Unique request identifier for tracing")


# HTTP Status Code to Error Code Mappings
STATUS_CODE_MAPPINGS = {
    400: ErrorCode.INVALID_INPUT,
    401: ErrorCode.AUTHENTICATION_REQUIRED,
    403: ErrorCode.ACCESS_DENIED,
    404: ErrorCode.RESOURCE_NOT_FOUND,
    409: ErrorCode.RESOURCE_CONFLICT,
    422: ErrorCode.VALIDATION_ERROR,
    429: ErrorCode.RATE_LIMIT_EXCEEDED,
    500: ErrorCode.INTERNAL_SERVER_ERROR,
    502: ErrorCode.EXTERNAL_SERVICE_ERROR,
    503: ErrorCode.SERVICE_UNAVAILABLE,
    504: ErrorCode.TIMEOUT_ERROR,
}


def create_error_response(
    error_code: ErrorCode,
    message: str,
    details: Optional[str] = None,
    request_id: Optional[str] = None,
    validation_errors: Optional[List[ValidationErrorDetail]] = None,
    retry_after: Optional[int] = None,
    documentation_url: Optional[str] = None
) -> ErrorResponse:
    """
    Create a standardized error response.
    
    Args:
        error_code: The standardized error code
        message: Human-readable error message
        details: Additional error details
        request_id: Unique request identifier
        validation_errors: List of validation errors (for 422 responses)
        retry_after: Seconds to wait before retrying
        documentation_url: URL to relevant documentation
        
    Returns:
        ErrorResponse: Standardized error response object
    """
    return ErrorResponse(
        error_code=error_code,
        message=message,
        details=details,
        request_id=request_id,
        validation_errors=validation_errors,
        retry_after=retry_after,
        documentation_url=documentation_url
    )


def create_success_response(
    message: str,
    data: Optional[Dict[str, Any]] = None,
    request_id: Optional[str] = None
) -> SuccessResponse:
    """
    Create a standardized success response.
    
    Args:
        message: Human-readable success message
        data: Response data
        request_id: Unique request identifier
        
    Returns:
        SuccessResponse: Standardized success response object
    """
    return SuccessResponse(
        message=message,
        data=data,
        request_id=request_id
    )
