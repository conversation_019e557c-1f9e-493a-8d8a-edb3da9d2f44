"""
VM Management Data Models
"""
from pydantic import BaseModel, Field, field_validator
from typing import Optional, List, Dict, Any
from enum import Enum


class VMType(str, Enum):
    """VM backend types"""
    DOCKER = "docker"
    VAGRANT = "vagrant"


class VMStatus(str, Enum):
    """VM status states"""
    CREATING = "creating"
    RUNNING = "running"
    STOPPED = "stopped"
    SUSPENDED = "suspended"
    DESTROYED = "destroyed"
    ERROR = "error"


class VMAction(str, Enum):
    """Available VM actions"""
    START = "start"
    STOP = "stop"
    RESTART = "restart"
    DESTROY = "destroy"
    SUSPEND = "suspend"
    RESUME = "resume"


class ResourceRequirements(BaseModel):
    """VM resource requirements"""
    min_memory_mb: int = Field(..., ge=256, le=16384)
    min_cpus: int = Field(..., ge=1, le=16)
    min_disk_gb: int = Field(..., ge=5, le=500)


class VMTemplateResponse(BaseModel):
    """VM template information"""
    template_id: str
    name: str
    description: str
    vm_type: VMType
    compatibility: List[str]
    resource_requirements: ResourceRequirements


class VMCreateRequest(BaseModel):
    """Request to create a new VM"""
    name: str = Field(..., min_length=1, max_length=100)
    template: str = Field(..., min_length=1)
    vm_type: VMType
    memory_mb: int = Field(..., ge=256, le=16384)
    cpus: int = Field(..., ge=1, le=16)
    disk_gb: Optional[int] = Field(20, ge=5, le=500)
    domain: str = Field(..., pattern="^TurdParty$")
    description: Optional[str] = Field("", max_length=500)
    auto_start: Optional[bool] = True

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate VM name format"""
        if not v.replace('-', '').replace('_', '').isalnum():
            raise ValueError('Name must contain only alphanumeric characters, hyphens, and underscores')
        return v

    @field_validator('template')
    @classmethod
    def validate_template(cls, v):
        """Validate template format"""
        valid_templates = [
            "ubuntu:20.04", "ubuntu:22.04", "ubuntu:18.04",
            "windows:10", "windows:11", "windows:server2019",
            "10Baht/windows10-turdparty",  # 10Baht Packer built Windows 10
            "kali:latest", "centos:8", "debian:11"
        ]
        if v not in valid_templates:
            raise ValueError(f'Invalid template. Valid options: {valid_templates}')
        return v


class VMMetrics(BaseModel):
    """Current VM metrics"""
    cpu_percent: float = Field(..., ge=0, le=100)
    memory_percent: float = Field(..., ge=0, le=100)
    uptime_seconds: int = Field(..., ge=0)


class VMResponse(BaseModel):
    """VM information response"""
    vm_id: str
    name: str
    template: str
    vm_type: VMType
    memory_mb: int
    cpus: int
    disk_gb: Optional[int] = 20
    domain: str
    description: Optional[str] = ""
    status: VMStatus
    created_at: str
    started_at: Optional[str] = None
    stopped_at: Optional[str] = None
    runtime_minutes: float = 0.0
    is_expired: bool = False
    ip_address: Optional[str] = None
    current_metrics: Optional[VMMetrics] = None


class VMListResponse(BaseModel):
    """Paginated list of VMs"""
    vms: List[VMResponse]
    total: int
    skip: int
    limit: int


class VMActionRequest(BaseModel):
    """Request to perform action on VM"""
    action: VMAction
    force: Optional[bool] = False


class VMActionResponse(BaseModel):
    """Response from VM action"""
    vm_id: str
    action: VMAction
    status: VMStatus
    task_id: str
    message: str


class FileUploadProgress(BaseModel):
    """File upload progress information"""
    progress: float = Field(..., ge=0, le=100)
    uploaded: int = Field(..., ge=0)
    total: int = Field(..., ge=0)
    speed_mbps: float = Field(..., ge=0)


class CommandExecutionRequest(BaseModel):
    """Command execution request"""
    command: str = Field(..., min_length=1, max_length=1000)
    working_directory: Optional[str] = "/tmp"
    timeout_seconds: Optional[int] = Field(300, ge=1, le=3600)


class CommandExecutionResponse(BaseModel):
    """Command execution response"""
    type: str  # command_output, error
    stdout: Optional[str] = None
    stderr: Optional[str] = None
    exit_code: Optional[int] = None
    is_complete: bool = False
    execution_time: Optional[float] = None
    message: Optional[str] = None


class ProcessInfo(BaseModel):
    """Process information"""
    pid: int
    name: str
    cpu_percent: float = Field(..., ge=0)
    memory_mb: float = Field(..., ge=0)


class NetworkStats(BaseModel):
    """Network statistics"""
    rx_bytes: int = Field(..., ge=0)
    tx_bytes: int = Field(..., ge=0)
    rx_packets: Optional[int] = Field(0, ge=0)
    tx_packets: Optional[int] = Field(0, ge=0)


class VMMetricsDetailed(BaseModel):
    """Detailed VM metrics for streaming"""
    vm_id: str
    vm_type: VMType
    timestamp: int
    status: VMStatus
    cpu_percent: float = Field(..., ge=0, le=100)
    memory_percent: float = Field(..., ge=0, le=100)
    memory_used_bytes: int = Field(..., ge=0)
    memory_limit_bytes: int = Field(..., ge=0)
    memory_used_mb: float = Field(..., ge=0)
    disk_usage_percent: Optional[float] = Field(0, ge=0, le=100)
    network_rx_bytes: int = Field(..., ge=0)
    network_tx_bytes: int = Field(..., ge=0)
    top_processes: List[ProcessInfo] = []
    uptime_seconds: int = Field(..., ge=0)
    error: Optional[str] = None


class FileSystemEvent(BaseModel):
    """File system change event"""
    vm_id: str
    event_type: str  # created, modified, deleted, moved
    file_path: str
    timestamp: int
    file_size: Optional[int] = None
    permissions: Optional[str] = None


class VMInjectionRequest(BaseModel):
    """Malware injection request"""
    file_uuid: str = Field(..., min_length=1)
    injection_path: str = Field(..., min_length=1)
    execute: Optional[bool] = True
    monitor: Optional[bool] = True
    monitoring_duration_minutes: Optional[int] = Field(30, ge=1, le=120)


class VMInjectionResponse(BaseModel):
    """Malware injection response"""
    injection_id: str
    vm_id: str
    file_uuid: str
    injection_path: str
    status: str
    message: str
    monitoring_enabled: bool = False


class BehaviorEvent(BaseModel):
    """Malware behavior event"""
    vm_id: str
    injection_id: str
    event_type: str  # file_access, network_connection, process_creation, registry_modification
    timestamp: int
    process_name: str
    process_id: int
    event_data: Dict[str, Any]
    severity: str  # low, medium, high, critical


class NetworkPacket(BaseModel):
    """Network packet information"""
    vm_id: str
    timestamp: int
    source_ip: str
    dest_ip: str
    source_port: int
    dest_port: int
    protocol: str
    packet_size: int
    payload_preview: Optional[str] = None
    is_suspicious: bool = False


class VMHealthCheck(BaseModel):
    """VM health check result"""
    vm_id: str
    is_healthy: bool
    last_check: str
    issues: List[str] = []
    uptime_seconds: int
    resource_usage: VMMetrics
