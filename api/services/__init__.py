"""
TurdParty API Services Package

This package contains the business logic services for the TurdParty API.
Services implement the core functionality and orchestrate interactions
between different components of the malware analysis platform.

Key Services:
    - elk_logger: ECS-compliant logging service for structured event logging
    - file_injection_service: Handles file injection operations into VMs
    - vm_metrics_service: Collects and processes VM performance metrics
    - vm_service: Core VM lifecycle management and orchestration

Service Architecture:
    1. **Domain Services**: Business logic for specific domains (VM, files, metrics)
    2. **Integration Services**: External system integrations (ELK, MinIO, Celery)
    3. **Utility Services**: Common functionality (logging, validation, formatting)

Design Principles:
    - Single Responsibility: Each service handles one domain
    - Dependency Injection: Services receive dependencies via constructor
    - Async/Await: Non-blocking operations for better performance
    - Error Handling: Comprehensive exception handling and logging

Usage:
    Services are typically injected into route handlers:

    from api.services import VMService, FileInjectionService

    vm_service = VMService()
    file_service = FileInjectionService()

Integration:
    Services interact with:
    - Database models for persistence
    - Celery workers for background tasks
    - External APIs (Vagrant, Docker, ELK)
    - MinIO for file storage
"""
