"""
ELK Stack Logging Service

This module provides comprehensive logging capabilities for the TurdParty malware analysis
platform, integrating with Elasticsearch, Logstash, and Kibana (ELK) stack for centralized
log management, analysis, and monitoring.

Key Features:
    - Dual logging to both Logstash and Elasticsearch
    - Structured logging with ECS (Elastic Common Schema) compliance
    - File injection event tracking and correlation
    - Installation base information logging for forensic analysis
    - System event logging for operational monitoring
    - Advanced log search and querying capabilities
    - Automatic index management with monthly rotation

Logging Categories:
    1. **File Injection Events**: Track file upload, injection, and processing
    2. **Installation Base**: Forensic data about file installations
    3. **System Events**: Operational events and system status
    4. **Error Events**: Exception tracking and error analysis

Index Patterns:
    - turdparty-file-injections-YYYY.MM: File injection events
    - turdparty-installation-base-YYYY.MM: Installation forensics
    - turdparty-system-events-YYYY.MM: System operational events

Integration:
    - Elasticsearch for log storage and indexing
    - Logstash for log processing and enrichment
    - Kibana for visualization and dashboards
    - ECS schema for standardized log format

Usage:
    elk_logger = ELKLogger()
    await elk_logger.log_file_injection_event(
        injection_id="123",
        event_type="injection_started",
        filename="malware.exe"
    )
"""
import os
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from elasticsearch import AsyncElasticsearch
import logstash

from api.models.file_injection import ELKLogEntry

logger = logging.getLogger(__name__)

class ELKLogger:
    """
    Service for logging to ELK stack with dual Elasticsearch and Logstash integration.

    Provides comprehensive logging capabilities for the TurdParty platform including
    file injection tracking, installation base forensics, and system event monitoring.
    Automatically manages index creation and rotation for optimal performance.
    """
    
    def __init__(self):
        """
        Initialize ELK logger with Elasticsearch and Logstash connections.

        Configures dual logging to both Elasticsearch (direct indexing) and
        Logstash (processing pipeline) for comprehensive log management.
        """
        self.elasticsearch_host = os.getenv("ELASTICSEARCH_HOST", "elasticsearch")
        self.elasticsearch_port = int(os.getenv("ELASTICSEARCH_PORT", "9200"))
        self.logstash_host = os.getenv("LOGSTASH_HOST", "logstash")
        self.logstash_port = int(os.getenv("LOGSTASH_PORT", "5000"))
        
        # Initialize Elasticsearch client
        self.es_client = AsyncElasticsearch([
            f"http://{self.elasticsearch_host}:{self.elasticsearch_port}"
        ])
        
        # Initialize logstash handler
        self.logstash_handler = logstash.TCPLogstashHandler(
            self.logstash_host, 
            self.logstash_port, 
            version=1
        )
        
        # Create logger for ELK
        self.elk_logger = logging.getLogger('turdparty.elk')
        self.elk_logger.setLevel(logging.INFO)
        self.elk_logger.addHandler(self.logstash_handler)
        
    async def log_file_injection_event(
        self,
        injection_id: str,
        event_type: str,
        filename: Optional[str] = None,
        target_path: Optional[str] = None,
        status: Optional[str] = None,
        error_message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Log file injection events to ELK stack for tracking and analysis.

        Creates structured log entries for all file injection operations including
        creation, processing, completion, and error events. Logs to both Logstash
        and Elasticsearch for comprehensive tracking.

        Args:
            injection_id: Unique identifier for the injection operation
            event_type: Type of event (injection_created, injection_processed, etc.)
            filename: Name of the file being injected
            target_path: Target path in the VM for the file
            status: Current status of the injection
            error_message: Error details if injection failed
            details: Additional event-specific details
        """
        
        log_entry = ELKLogEntry(
            event_type=event_type,
            injection_id=injection_id,
            message=f"File injection {event_type}: {injection_id}",
            details={
                "filename": filename,
                "target_path": target_path,
                "status": status,
                "error_message": error_message,
                **(details or {})
            }
        )
        
        # Log to Logstash
        self.elk_logger.info(
            log_entry.message,
            extra={
                "injection_id": injection_id,
                "event_type": event_type,
                "file_name": filename,  # Renamed to avoid conflict with LogRecord.filename
                "target_path": target_path,
                "status": status,
                "error_message": error_message,
                "details": details,
                "timestamp": log_entry.timestamp.isoformat(),
                "service": log_entry.service
            }
        )
        
        # Also index directly to Elasticsearch
        try:
            await self.es_client.index(
                index=f"turdparty-file-injections-{datetime.now().strftime('%Y.%m')}",
                document=log_entry.model_dump()
            )
        except Exception as e:
            logger.error(f"Failed to index to Elasticsearch: {str(e)}")
    
    async def log_installation_base(
        self,
        injection_id: str,
        file_path: Optional[str] = None,
        target_path: Optional[str] = None,
        permissions: Optional[str] = None,
        additional_info: Optional[Dict[str, Any]] = None
    ):
        """
        Log installation base information for forensic analysis.

        Records detailed information about file installations including system
        context, file permissions, and installation metadata for security
        analysis and forensic investigation.

        Args:
            injection_id: Unique identifier for the injection operation
            file_path: Source file path
            target_path: Installation target path
            permissions: File permissions set during installation
            additional_info: Additional forensic information
        """
        
        installation_data = {
            "injection_id": injection_id,
            "file_path": file_path,
            "target_path": target_path,
            "permissions": permissions,
            "installation_timestamp": datetime.now(timezone.utc).isoformat(),
            "system_info": await self._get_system_info(),
            **(additional_info or {})
        }
        
        log_entry = ELKLogEntry(
            event_type="installation_base",
            injection_id=injection_id,
            message=f"Installation base logged for injection {injection_id}",
            details=installation_data
        )
        
        # Log to Logstash
        self.elk_logger.info(
            log_entry.message,
            extra={
                "injection_id": injection_id,
                "event_type": "installation_base",
                "installation_data": installation_data,
                "timestamp": log_entry.timestamp.isoformat(),
                "service": log_entry.service
            }
        )
        
        # Index to Elasticsearch
        try:
            await self.es_client.index(
                index=f"turdparty-installation-base-{datetime.now().strftime('%Y.%m')}",
                document=log_entry.model_dump()
            )
        except Exception as e:
            logger.error(f"Failed to index installation base to Elasticsearch: {str(e)}")
    
    async def log_system_event(
        self,
        event_type: str,
        message: str,
        level: str = "INFO",
        details: Optional[Dict[str, Any]] = None
    ):
        """Log general system events to ELK stack"""
        
        log_entry = ELKLogEntry(
            event_type=event_type,
            message=message,
            level=level,
            details=details
        )
        
        # Log to Logstash
        self.elk_logger.info(
            message,
            extra={
                "event_type": event_type,
                "level": level,
                "details": details,
                "timestamp": log_entry.timestamp.isoformat(),
                "service": log_entry.service
            }
        )
        
        # Index to Elasticsearch
        try:
            await self.es_client.index(
                index=f"turdparty-system-events-{datetime.now().strftime('%Y.%m')}",
                document=log_entry.model_dump()
            )
        except Exception as e:
            logger.error(f"Failed to index system event to Elasticsearch: {str(e)}")
    
    async def _get_system_info(self) -> Dict[str, Any]:
        """Get system information for logging"""
        import platform
        import psutil
        
        try:
            return {
                "platform": platform.platform(),
                "python_version": platform.python_version(),
                "cpu_count": psutil.cpu_count(),
                "memory_total": psutil.virtual_memory().total,
                "disk_usage": {
                    "total": psutil.disk_usage('/').total,
                    "used": psutil.disk_usage('/').used,
                    "free": psutil.disk_usage('/').free
                },
                "hostname": platform.node()
            }
        except Exception as e:
            logger.error(f"Failed to get system info: {str(e)}")
            return {"error": "Failed to retrieve system information"}
    
    async def search_logs(
        self,
        query: str,
        index_pattern: str = "turdparty-*",
        size: int = 100,
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Search logs in Elasticsearch"""
        
        search_body = {
            "query": {
                "bool": {
                    "must": [
                        {"query_string": {"query": query}}
                    ]
                }
            },
            "size": size,
            "sort": [{"timestamp": {"order": "desc"}}]
        }
        
        # Add date range filter if provided
        if from_date or to_date:
            date_range = {}
            if from_date:
                date_range["gte"] = from_date.isoformat()
            if to_date:
                date_range["lte"] = to_date.isoformat()
            
            search_body["query"]["bool"]["filter"] = [
                {"range": {"timestamp": date_range}}
            ]
        
        try:
            response = await self.es_client.search(
                index=index_pattern,
                body=search_body
            )
            return response
        except Exception as e:
            logger.error(f"Failed to search logs: {str(e)}")
            return {"error": str(e)}
    
    async def close(self):
        """Close connections"""
        await self.es_client.close()
