"""
VM Metrics Service for real-time monitoring and gRPC communication
"""
import asyncio
import logging
import time
import psutil
import docker
import subprocess
from typing import Dict, Any, Optional, AsyncGenerator, List
from datetime import datetime
import json

logger = logging.getLogger(__name__)


class VMMetricsService:
    """Service for collecting and streaming VM metrics"""
    
    def __init__(self):
        self.docker_client = None
        self.active_streams: Dict[str, bool] = {}
        
    async def initialize(self):
        """Initialize Docker client and other resources"""
        try:
            self.docker_client = docker.from_env()
            logger.info("VM Metrics Service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Docker client: {e}")
            self.docker_client = None
    
    async def stream_vm_metrics(self, vm_id: str, vm_type: str = "docker", interval: float = 1.0):
        """Stream real-time VM metrics"""
        try:
            while True:
                metrics = await self.get_vm_metrics(vm_id, vm_type)
                yield metrics
                await asyncio.sleep(interval)
        except Exception as e:
            logger.error(f"Error streaming metrics for VM {vm_id}: {e}")
            yield self._get_error_metrics(str(e))

    async def get_vm_metrics(self, vm_id: str, vm_type: str = "docker") -> Dict[str, Any]:
        """Get current VM metrics"""
        try:
            if vm_type == "docker":
                return await self._get_docker_metrics(vm_id)
            elif vm_type == "vagrant":
                return await self._get_vagrant_metrics(vm_id)
            else:
                raise ValueError(f"Unsupported VM type: {vm_type}")
        except Exception as e:
            logger.error(f"Error getting metrics for VM {vm_id}: {e}")
            return self._get_error_metrics(str(e))
    
    async def _get_docker_metrics(self, container_id: str) -> Dict[str, Any]:
        """Get Docker container metrics via API"""
        try:
            if not self.docker_client:
                await self.initialize()
            
            container = self.docker_client.containers.get(container_id)
            stats = container.stats(stream=False)
            
            # Calculate CPU percentage
            cpu_percent = self._calculate_cpu_percent(stats)
            
            # Calculate memory percentage
            memory_stats = stats.get('memory_stats', {})
            memory_usage = memory_stats.get('usage', 0)
            memory_limit = memory_stats.get('limit', 1)
            memory_percent = (memory_usage / memory_limit) * 100 if memory_limit > 0 else 0
            
            # Network stats
            networks = stats.get('networks', {})
            network_rx = sum(net.get('rx_bytes', 0) for net in networks.values())
            network_tx = sum(net.get('tx_bytes', 0) for net in networks.values())
            
            # Get process list (simplified for Docker)
            top_processes = await self._get_docker_processes(container)
            
            return {
                "vm_id": container_id,
                "vm_type": "docker",
                "timestamp": int(time.time() * 1000),
                "status": container.status,
                "cpu_percent": round(cpu_percent, 2),
                "memory_percent": round(memory_percent, 2),
                "memory_used_bytes": memory_usage,
                "memory_limit_bytes": memory_limit,
                "memory_used_mb": round(memory_usage / (1024 * 1024), 2),
                "network_rx_bytes": network_rx,
                "network_tx_bytes": network_tx,
                "top_processes": top_processes,
                "uptime_seconds": self._get_container_uptime(container)
            }
            
        except Exception as e:
            logger.error(f"Error getting Docker metrics for {container_id}: {e}")
            return self._get_error_metrics(str(e))
    
    async def _get_vagrant_metrics(self, vm_id: str) -> Dict[str, Any]:
        """Get Vagrant VM metrics via SSH"""
        try:
            # Get real metrics from Vagrant VM via SSH
            import asyncio
            import subprocess
            import json

            # First, get VM status from vagrant
            try:
                result = subprocess.run(
                    ["vagrant", "status", vm_id],
                    capture_output=True,
                    text=True,
                    timeout=10,
                    cwd="/vagrant"  # Assuming vagrant files are in /vagrant
                )

                if result.returncode != 0:
                    logger.warning(f"Vagrant status failed for {vm_id}: {result.stderr}")
                    return self._get_error_metrics(f"Vagrant status failed: {result.stderr}")

                # Parse vagrant status
                status_output = result.stdout
                if "running" not in status_output.lower():
                    return {
                        "vm_id": vm_id,
                        "vm_type": "vagrant",
                        "timestamp": int(time.time() * 1000),
                        "status": "stopped",
                        "cpu_percent": 0.0,
                        "memory_percent": 0.0,
                        "memory_used_bytes": 0,
                        "memory_limit_bytes": 0,
                        "memory_used_mb": 0.0,
                        "network_rx_bytes": 0,
                        "network_tx_bytes": 0,
                        "top_processes": [],
                        "uptime_seconds": 0
                    }

            except subprocess.TimeoutExpired:
                logger.error(f"Vagrant status timeout for {vm_id}")
                return self._get_error_metrics("Vagrant status timeout")
            except Exception as e:
                logger.error(f"Error checking vagrant status for {vm_id}: {e}")
                return self._get_error_metrics(f"Vagrant status error: {e}")

            # Get real metrics via SSH
            metrics_data = await self._get_vagrant_ssh_metrics(vm_id)
            return metrics_data

        except Exception as e:
            logger.error(f"Error getting Vagrant metrics for {vm_id}: {e}")
            return self._get_error_metrics(str(e))
    
    async def _get_vagrant_ssh_metrics(self, vm_id: str) -> Dict[str, Any]:
        """Get real metrics from Vagrant VM via SSH"""
        try:
            import subprocess
            import re

            # SSH command to get system metrics
            ssh_commands = [
                # CPU usage from /proc/stat
                "cat /proc/stat | head -1",
                # Memory info from /proc/meminfo
                "cat /proc/meminfo | head -10",
                # Network stats from /proc/net/dev
                "cat /proc/net/dev | grep -E '(eth0|enp|ens)'",
                # Top processes
                "ps aux --sort=-%cpu | head -6",
                # Uptime
                "cat /proc/uptime"
            ]

            # Execute SSH commands
            ssh_results = {}
            for i, cmd in enumerate(ssh_commands):
                try:
                    result = subprocess.run(
                        ["vagrant", "ssh", vm_id, "-c", cmd],
                        capture_output=True,
                        text=True,
                        timeout=5,
                        cwd="/vagrant"
                    )

                    if result.returncode == 0:
                        ssh_results[i] = result.stdout.strip()
                    else:
                        logger.warning(f"SSH command {i} failed for {vm_id}: {result.stderr}")
                        ssh_results[i] = ""

                except subprocess.TimeoutExpired:
                    logger.warning(f"SSH command {i} timeout for {vm_id}")
                    ssh_results[i] = ""
                except Exception as e:
                    logger.warning(f"SSH command {i} error for {vm_id}: {e}")
                    ssh_results[i] = ""

            # Parse results
            cpu_percent = self._parse_vagrant_cpu(ssh_results.get(0, ""))
            memory_stats = self._parse_vagrant_memory(ssh_results.get(1, ""))
            network_stats = self._parse_vagrant_network(ssh_results.get(2, ""))
            processes = self._parse_vagrant_processes(ssh_results.get(3, ""))
            uptime = self._parse_vagrant_uptime(ssh_results.get(4, ""))

            return {
                "vm_id": vm_id,
                "vm_type": "vagrant",
                "timestamp": int(time.time() * 1000),
                "status": "running",
                "cpu_percent": cpu_percent,
                "memory_percent": memory_stats["percent"],
                "memory_used_bytes": memory_stats["used_bytes"],
                "memory_limit_bytes": memory_stats["total_bytes"],
                "memory_used_mb": memory_stats["used_mb"],
                "network_rx_bytes": network_stats["rx_bytes"],
                "network_tx_bytes": network_stats["tx_bytes"],
                "top_processes": processes,
                "uptime_seconds": uptime
            }

        except Exception as e:
            logger.error(f"Error getting SSH metrics for {vm_id}: {e}")
            return self._get_error_metrics(f"SSH metrics error: {e}")

    def _parse_vagrant_cpu(self, cpu_stat: str) -> float:
        """Parse CPU usage from /proc/stat"""
        try:
            if not cpu_stat:
                return 0.0

            # Parse: cpu  user nice system idle iowait irq softirq steal guest guest_nice
            parts = cpu_stat.split()
            if len(parts) < 5:
                return 0.0

            user = int(parts[1])
            nice = int(parts[2])
            system = int(parts[3])
            idle = int(parts[4])

            total = user + nice + system + idle
            if total == 0:
                return 0.0

            cpu_percent = ((total - idle) / total) * 100
            return round(cpu_percent, 2)

        except Exception:
            return 0.0

    def _parse_vagrant_memory(self, meminfo: str) -> Dict[str, Any]:
        """Parse memory info from /proc/meminfo"""
        try:
            if not meminfo:
                return {"percent": 0.0, "used_bytes": 0, "total_bytes": 0, "used_mb": 0.0}

            mem_total = 0
            mem_available = 0

            for line in meminfo.split('\n'):
                if line.startswith('MemTotal:'):
                    mem_total = int(line.split()[1]) * 1024  # Convert KB to bytes
                elif line.startswith('MemAvailable:'):
                    mem_available = int(line.split()[1]) * 1024  # Convert KB to bytes

            if mem_total == 0:
                return {"percent": 0.0, "used_bytes": 0, "total_bytes": 0, "used_mb": 0.0}

            mem_used = mem_total - mem_available
            mem_percent = (mem_used / mem_total) * 100

            return {
                "percent": round(mem_percent, 2),
                "used_bytes": mem_used,
                "total_bytes": mem_total,
                "used_mb": round(mem_used / (1024 * 1024), 2)
            }

        except Exception:
            return {"percent": 0.0, "used_bytes": 0, "total_bytes": 0, "used_mb": 0.0}

    def _parse_vagrant_network(self, netdev: str) -> Dict[str, int]:
        """Parse network stats from /proc/net/dev"""
        try:
            if not netdev:
                return {"rx_bytes": 0, "tx_bytes": 0}

            rx_bytes = 0
            tx_bytes = 0

            for line in netdev.split('\n'):
                if ':' in line:
                    parts = line.split()
                    if len(parts) >= 10:
                        rx_bytes += int(parts[1])  # RX bytes
                        tx_bytes += int(parts[9])  # TX bytes

            return {"rx_bytes": rx_bytes, "tx_bytes": tx_bytes}

        except Exception:
            return {"rx_bytes": 0, "tx_bytes": 0}

    def _parse_vagrant_processes(self, ps_output: str) -> List[Dict[str, Any]]:
        """Parse process list from ps aux output"""
        try:
            if not ps_output:
                return []

            processes = []
            lines = ps_output.split('\n')[1:]  # Skip header

            for line in lines[:5]:  # Top 5 processes
                parts = line.split(None, 10)
                if len(parts) >= 11:
                    try:
                        processes.append({
                            "pid": int(parts[1]),
                            "name": parts[10].split()[0] if parts[10] else "unknown",
                            "cpu_percent": float(parts[2]) if parts[2].replace('.', '').isdigit() else 0.0,
                            "memory_mb": float(parts[3]) if parts[3].replace('.', '').isdigit() else 0.0
                        })
                    except (ValueError, IndexError):
                        continue

            return processes

        except Exception:
            return []

    def _parse_vagrant_uptime(self, uptime_output: str) -> int:
        """Parse uptime from /proc/uptime"""
        try:
            if not uptime_output:
                return 0

            uptime_seconds = float(uptime_output.split()[0])
            return int(uptime_seconds)

        except Exception:
            return 0

    def _calculate_cpu_percent(self, stats: Dict) -> float:
        """Calculate CPU percentage from Docker stats"""
        try:
            cpu_stats = stats.get('cpu_stats', {})
            precpu_stats = stats.get('precpu_stats', {})
            
            cpu_usage = cpu_stats.get('cpu_usage', {})
            precpu_usage = precpu_stats.get('cpu_usage', {})
            
            cpu_delta = cpu_usage.get('total_usage', 0) - precpu_usage.get('total_usage', 0)
            system_delta = cpu_stats.get('system_cpu_usage', 0) - precpu_stats.get('system_cpu_usage', 0)
            
            if system_delta > 0 and cpu_delta > 0:
                cpu_count = len(cpu_usage.get('percpu_usage', [1]))
                return (cpu_delta / system_delta) * cpu_count * 100.0
            return 0.0
        except Exception:
            return 0.0
    
    async def _get_docker_processes(self, container) -> List[Dict[str, Any]]:
        """Get process list from Docker container"""
        try:
            # Execute 'ps' command in container
            exec_result = container.exec_run("ps aux --no-headers", demux=True)
            if exec_result.exit_code == 0 and exec_result.output[0]:
                processes = []
                lines = exec_result.output[0].decode().strip().split('\n')
                
                for line in lines[:10]:  # Top 10 processes
                    parts = line.split(None, 10)
                    if len(parts) >= 11:
                        processes.append({
                            "pid": int(parts[1]),
                            "name": parts[10].split()[0] if parts[10] else "unknown",
                            "cpu_percent": float(parts[2]) if parts[2].replace('.', '').isdigit() else 0.0,
                            "memory_mb": float(parts[3]) if parts[3].replace('.', '').isdigit() else 0.0
                        })
                
                return processes
        except Exception as e:
            logger.error(f"Error getting Docker processes: {e}")
        
        return []
    
    def _get_container_uptime(self, container) -> int:
        """Get container uptime in seconds"""
        try:
            started_at = container.attrs['State']['StartedAt']
            start_time = datetime.fromisoformat(started_at.replace('Z', '+00:00'))
            uptime = datetime.now(start_time.tzinfo) - start_time
            return int(uptime.total_seconds())
        except Exception:
            return 0
    
    def _get_error_metrics(self, error_message: str) -> Dict[str, Any]:
        """Return error metrics when collection fails"""
        return {
            "vm_id": "unknown",
            "vm_type": "unknown",
            "timestamp": int(time.time() * 1000),
            "status": "error",
            "error": error_message,
            "cpu_percent": 0.0,
            "memory_percent": 0.0,
            "memory_used_bytes": 0,
            "memory_limit_bytes": 0,
            "memory_used_mb": 0.0,
            "network_rx_bytes": 0,
            "network_tx_bytes": 0,
            "top_processes": [],
            "uptime_seconds": 0
        }
    
    async def stream_vm_metrics(self, vm_id: str, vm_type: str = "docker", interval: float = 1.0) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream VM metrics in real-time"""
        stream_key = f"{vm_id}:{vm_type}"
        self.active_streams[stream_key] = True
        
        try:
            logger.info(f"Starting metrics stream for VM {vm_id} (type: {vm_type})")
            
            while self.active_streams.get(stream_key, False):
                metrics = await self.get_vm_metrics(vm_id, vm_type)
                yield metrics
                await asyncio.sleep(interval)
                
        except Exception as e:
            logger.error(f"Error in metrics stream for VM {vm_id}: {e}")
            yield self._get_error_metrics(str(e))
        finally:
            self.active_streams.pop(stream_key, None)
            logger.info(f"Stopped metrics stream for VM {vm_id}")
    
    def stop_stream(self, vm_id: str, vm_type: str = "docker"):
        """Stop metrics streaming for a VM"""
        stream_key = f"{vm_id}:{vm_type}"
        self.active_streams[stream_key] = False
        logger.info(f"Requested stop for metrics stream: {stream_key}")


# Global instance
vm_metrics_service = VMMetricsService()
