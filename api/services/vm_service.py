"""
VM Service for managing virtual machines with PostgreSQL persistence
"""
import logging
import asyncio
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.exc import SQLAlchemyError

from api.models.vm_management import VMCreateRequest, VMResponse, VMType, VMStatus

# Import database models and session
try:
    from services.api.src.models.vm_instance import VMInstance
    from services.api.src.services.database import get_db
    DATABASE_AVAILABLE = True
except ImportError:
    # Fallback for development environments
    DATABASE_AVAILABLE = False
    VMInstance = None
    get_db = None

logger = logging.getLogger(__name__)


class VMService:
    """Service for VM lifecycle management with PostgreSQL persistence"""

    def __init__(self):
        # Remove in-memory storage - use database instead
        self.docker_client = None
        self._db_session: Optional[AsyncSession] = None

    async def initialize(self):
        """Initialize VM service with database and Docker"""
        try:
            import docker
            self.docker_client = docker.from_env()
            logger.info("Docker client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Docker client: {e}")
            self.docker_client = None

        # Verify database availability
        if not DATABASE_AVAILABLE:
            logger.warning("Database models not available - running in degraded mode")
        else:
            logger.info("Database integration available")

    async def _get_db_session(self) -> AsyncSession:
        """Get database session for VM operations"""
        if not DATABASE_AVAILABLE or not get_db:
            raise RuntimeError("Database not available")

        # Get session from dependency injection
        async for session in get_db():
            return session
    
    async def create_vm(self, request: VMCreateRequest) -> VMResponse:
        """Create a new VM with database persistence"""
        if not DATABASE_AVAILABLE:
            raise RuntimeError("Database not available for VM creation")

        vm_instance = None
        try:
            # Create VM instance in database
            vm_instance = VMInstance(
                name=request.name,
                template=request.template,
                memory_mb=request.memory_mb,
                cpus=request.cpus,
                disk_gb=getattr(request, 'disk_gb', 20),
                status=VMStatus.CREATING,
                # Additional fields from request
                vm_id=None,  # Will be set after container/VM creation
            )

            # Save to database
            async with await self._get_db_session() as session:
                session.add(vm_instance)
                await session.commit()
                await session.refresh(vm_instance)

            vm_id = str(vm_instance.id)
            logger.info(f"Created VM record in database: {vm_id}")

            # Start VM creation process
            if request.vm_type == VMType.DOCKER:
                await self._create_docker_vm(vm_instance, request)
            elif request.vm_type == VMType.VAGRANT:
                await self._create_vagrant_vm(vm_instance, request)

            # Auto-start if requested
            if getattr(request, 'auto_start', True):
                await self._start_vm(vm_instance.id)

            # Convert to response model
            return await self._vm_instance_to_response(vm_instance)

        except Exception as e:
            logger.error(f"Error creating VM: {e}")
            if vm_instance and vm_instance.id:
                # Update status to error in database
                try:
                    async with await self._get_db_session() as session:
                        await session.execute(
                            update(VMInstance)
                            .where(VMInstance.id == vm_instance.id)
                            .values(status=VMStatus.ERROR)
                        )
                        await session.commit()
                except Exception as db_error:
                    logger.error(f"Failed to update VM status to error: {db_error}")
            raise

    async def _vm_instance_to_response(self, vm_instance: VMInstance) -> VMResponse:
        """Convert VMInstance database model to VMResponse"""
        return VMResponse(
            vm_id=str(vm_instance.id),
            name=vm_instance.name,
            template=vm_instance.template,
            vm_type=VMType.DOCKER,  # Default for now
            memory_mb=vm_instance.memory_mb,
            cpus=vm_instance.cpus,
            disk_gb=vm_instance.disk_gb,
            domain="TurdParty",  # Default domain
            description="",  # Default description
            status=vm_instance.status,
            created_at=vm_instance.created_at.isoformat() if vm_instance.created_at else datetime.now(timezone.utc).isoformat(),
            runtime_minutes=0.0,  # Calculate from timestamps if needed
            is_expired=False,  # Calculate based on runtime
            ip_address=vm_instance.ip_address,
            started_at=vm_instance.started_at.isoformat() if vm_instance.started_at else None,
        )

    async def _create_docker_vm(self, vm_instance: VMInstance, request: VMCreateRequest):
        """Create Docker container VM with database persistence"""
        try:
            if not self.docker_client:
                await self.initialize()

            # Map template to Docker image
            image_map = {
                "ubuntu:20.04": "ubuntu:20.04",
                "ubuntu:22.04": "ubuntu:22.04",
                "ubuntu:18.04": "ubuntu:18.04",
                "kali:latest": "kalilinux/kali-rolling:latest",
                "centos:8": "centos:8",
                "debian:11": "debian:11"
            }

            image = image_map.get(request.template, "ubuntu:20.04")

            # Check if turdpartycollab_net exists, otherwise use default bridge
            network_name = "bridge"  # Default to bridge network
            try:
                networks = self.docker_client.networks.list(names=["turdpartycollab_net"])
                if networks:
                    network_name = "turdpartycollab_net"
            except Exception:
                pass  # Use default bridge network

            # Create container with simplified configuration for testing environments
            container_name = f"turdparty_{request.name}_{str(vm_instance.id)[:8]}"

            # Check if container with this name already exists and remove it
            try:
                existing = self.docker_client.containers.get(container_name)
                logger.warning(f"Removing existing container: {container_name}")
                existing.remove(force=True)
            except Exception:
                pass  # Container doesn't exist, which is good

            container_config = {
                "image": image,
                "name": container_name,
                "detach": True,
                "command": "tail -f /dev/null",  # Keep container running
                "labels": {
                    "turdparty.vm_id": str(vm_instance.id),
                    "turdparty.vm_name": request.name,
                    "turdparty.domain": getattr(request, 'domain', 'TurdParty'),
                    "turdparty.test": "true"  # Mark as test container
                }
            }

            # Create container without resource limits for better compatibility
            container = self.docker_client.containers.run(**container_config)

            # Get container IP
            container.reload()
            ip_address = None
            networks = container.attrs.get('NetworkSettings', {}).get('Networks', {})
            for network_name, network_info in networks.items():
                if network_info.get('IPAddress'):
                    ip_address = network_info['IPAddress']
                    break

            # Update VM instance in database
            async with await self._get_db_session() as session:
                await session.execute(
                    update(VMInstance)
                    .where(VMInstance.id == vm_instance.id)
                    .values(
                        vm_id=container.id,
                        status=VMStatus.RUNNING,
                        started_at=datetime.now(timezone.utc),
                        ip_address=ip_address
                    )
                )
                await session.commit()

            logger.info(f"Docker VM created successfully: {vm_instance.id}")

        except Exception as e:
            logger.error(f"Error creating Docker VM {vm_instance.id}: {e}")
            # Update status to error in database
            try:
                async with await self._get_db_session() as session:
                    await session.execute(
                        update(VMInstance)
                        .where(VMInstance.id == vm_instance.id)
                        .values(status=VMStatus.ERROR)
                    )
                    await session.commit()
            except Exception as db_error:
                logger.error(f"Failed to update VM status to error: {db_error}")
            raise
    
    async def _create_vagrant_vm(self, vm_id: str, request: VMCreateRequest):
        """Create Vagrant VM using real Vagrant commands"""
        try:
            import subprocess
            import tempfile
            import os

            vm_data = self.vms[vm_id]

            # Create Vagrant directory
            vagrant_dir = f"/tmp/vagrant_{vm_id}"
            os.makedirs(vagrant_dir, exist_ok=True)

            # Create Vagrantfile
            vagrantfile_content = f"""
Vagrant.configure("2") do |config|
  config.vm.box = "{request.template or 'ubuntu/focal64'}"
  config.vm.hostname = "{vm_id}"

  config.vm.provider "virtualbox" do |vb|
    vb.memory = {getattr(request, 'memory_mb', 1024)}
    vb.cpus = {getattr(request, 'cpus', 1)}
    vb.name = "{vm_id}"
  end

  # Enable SSH agent forwarding
  config.ssh.forward_agent = true

  # Sync folder
  config.vm.synced_folder ".", "/vagrant", disabled: false
end
"""

            vagrantfile_path = os.path.join(vagrant_dir, "Vagrantfile")
            with open(vagrantfile_path, "w") as f:
                f.write(vagrantfile_content)

            # Initialize and create Vagrant VM
            result = subprocess.run(
                ["vagrant", "up"],
                cwd=vagrant_dir,
                capture_output=True,
                text=True,
                timeout=600  # 10 minute timeout
            )

            if result.returncode == 0:
                vm_data["status"] = VMStatus.STOPPED
                vm_data["vagrant_dir"] = vagrant_dir
                logger.info(f"Vagrant VM created successfully: {vm_id}")
            else:
                vm_data["status"] = VMStatus.ERROR
                logger.error(f"Vagrant VM creation failed: {result.stderr}")
                raise Exception(f"Vagrant creation failed: {result.stderr}")

        except subprocess.TimeoutExpired:
            logger.error(f"Vagrant VM creation timeout for {vm_id}")
            self.vms[vm_id]["status"] = VMStatus.ERROR
            raise Exception("Vagrant VM creation timeout")
        except Exception as e:
            logger.error(f"Error creating Vagrant VM {vm_id}: {e}")
            self.vms[vm_id]["status"] = VMStatus.ERROR
            raise
    
    async def _start_vm(self, vm_id: str):
        """Start a VM"""
        try:
            vm_data = self.vms[vm_id]
            
            if vm_data["vm_type"] == VMType.DOCKER:
                # Docker containers are started during creation
                pass
            elif vm_data["vm_type"] == VMType.VAGRANT:
                # Real Vagrant start
                vagrant_dir = vm_data.get("vagrant_dir")
                if not vagrant_dir:
                    raise Exception("Vagrant directory not found")

                import subprocess
                result = subprocess.run(
                    ["vagrant", "up"],
                    cwd=vagrant_dir,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minute timeout
                )

                if result.returncode == 0:
                    vm_data["status"] = VMStatus.RUNNING
                    vm_data["started_at"] = datetime.now(timezone.utc).isoformat()

                    # Get real IP address
                    ip_result = subprocess.run(
                        ["vagrant", "ssh-config"],
                        cwd=vagrant_dir,
                        capture_output=True,
                        text=True
                    )

                    # Parse IP from ssh-config output
                    ip_address = "*************"  # Default fallback
                    if ip_result.returncode == 0:
                        for line in ip_result.stdout.split('\n'):
                            if 'HostName' in line:
                                ip_address = line.split()[-1]
                                break

                    vm_data["ip_address"] = ip_address
                else:
                    vm_data["status"] = VMStatus.ERROR
                    raise Exception(f"Vagrant start failed: {result.stderr}")
            
            logger.info(f"VM started: {vm_id}")
            
        except Exception as e:
            logger.error(f"Error starting VM {vm_id}: {e}")
            self.vms[vm_id]["status"] = VMStatus.ERROR
            raise
    
    async def get_vm(self, vm_id: str) -> Optional[VMResponse]:
        """Get VM by ID from database"""
        if not DATABASE_AVAILABLE:
            return None

        try:
            # Convert string ID to UUID
            vm_uuid = uuid.UUID(vm_id)

            async with await self._get_db_session() as session:
                result = await session.execute(
                    select(VMInstance).where(VMInstance.id == vm_uuid)
                )
                vm_instance = result.scalar_one_or_none()

                if vm_instance:
                    return await self._vm_instance_to_response(vm_instance)
                return None

        except (ValueError, SQLAlchemyError) as e:
            logger.error(f"Error getting VM {vm_id}: {e}")
            return None

    async def list_vms(self, skip: int = 0, limit: int = 10, status_filter: Optional[str] = None) -> List[VMResponse]:
        """List VMs with pagination and filtering from database"""
        if not DATABASE_AVAILABLE:
            return []

        try:
            async with await self._get_db_session() as session:
                query = select(VMInstance)

                # Apply status filter
                if status_filter:
                    query = query.where(VMInstance.status == status_filter)

                # Apply pagination
                query = query.offset(skip).limit(limit)

                result = await session.execute(query)
                vm_instances = result.scalars().all()

                # Convert to response models
                responses = []
                for vm_instance in vm_instances:
                    responses.append(await self._vm_instance_to_response(vm_instance))

                return responses

        except SQLAlchemyError as e:
            logger.error(f"Error listing VMs: {e}")
            return []
    
    async def perform_action(self, vm_id: str, action: str, force: bool = False) -> Dict[str, Any]:
        """Perform action on VM"""
        if vm_id not in self.vms:
            raise ValueError(f"VM {vm_id} not found")
        
        vm_data = self.vms[vm_id]
        old_status = vm_data["status"]
        
        try:
            if action == "start":
                await self._start_vm(vm_id)
            elif action == "stop":
                await self._stop_vm(vm_id, force)
            elif action == "restart":
                await self._restart_vm(vm_id)
            elif action == "destroy":
                await self._destroy_vm(vm_id, force)
            elif action == "suspend":
                await self._suspend_vm(vm_id)
            elif action == "resume":
                await self._resume_vm(vm_id)
            else:
                raise ValueError(f"Invalid action: {action}")
            
            return {
                "vm_id": vm_id,
                "action": action,
                "old_status": old_status,
                "new_status": vm_data["status"],
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error performing action {action} on VM {vm_id}: {e}")
            vm_data["status"] = VMStatus.ERROR
            raise
    
    async def _stop_vm(self, vm_id: str, force: bool = False):
        """Stop a VM"""
        vm_data = self.vms[vm_id]
        
        if vm_data["vm_type"] == VMType.DOCKER:
            if "container_id" in vm_data:
                container = self.docker_client.containers.get(vm_data["container_id"])
                if force:
                    container.kill()
                else:
                    container.stop()
        
        vm_data["status"] = VMStatus.STOPPED
        vm_data["stopped_at"] = datetime.now(timezone.utc).isoformat()
    
    async def _restart_vm(self, vm_id: str):
        """Restart a VM"""
        await self._stop_vm(vm_id)
        await asyncio.sleep(1)  # Brief pause
        await self._start_vm(vm_id)
    
    async def _destroy_vm(self, vm_id: str, force: bool = False):
        """Destroy a VM"""
        vm_data = self.vms[vm_id]
        
        if vm_data["vm_type"] == VMType.DOCKER:
            if "container_id" in vm_data:
                container = self.docker_client.containers.get(vm_data["container_id"])
                if container.status == "running":
                    container.kill() if force else container.stop()
                container.remove()
        
        vm_data["status"] = VMStatus.DESTROYED
        vm_data["destroyed_at"] = datetime.now(timezone.utc).isoformat()
    
    async def _suspend_vm(self, vm_id: str):
        """Suspend a VM"""
        vm_data = self.vms[vm_id]
        
        if vm_data["vm_type"] == VMType.DOCKER:
            if "container_id" in vm_data:
                container = self.docker_client.containers.get(vm_data["container_id"])
                container.pause()
        
        vm_data["status"] = VMStatus.SUSPENDED
        vm_data["suspended_at"] = datetime.now(timezone.utc).isoformat()
    
    async def _resume_vm(self, vm_id: str):
        """Resume a VM"""
        vm_data = self.vms[vm_id]

        if vm_data["vm_type"] == VMType.DOCKER:
            if "container_id" in vm_data:
                container = self.docker_client.containers.get(vm_data["container_id"])
                container.reload()

                # Handle different container states
                if container.status == "paused":
                    container.unpause()
                elif container.status == "exited":
                    container.start()
                # If already running, do nothing

        vm_data["status"] = VMStatus.RUNNING
        vm_data["resumed_at"] = datetime.now(timezone.utc).isoformat()
    
    async def delete_vm(self, vm_id: str, force: bool = False):
        """Delete a VM"""
        if vm_id not in self.vms:
            raise ValueError(f"VM {vm_id} not found")
        
        vm_data = self.vms[vm_id]
        
        # Stop/destroy VM first if running
        if vm_data["status"] in [VMStatus.RUNNING, VMStatus.SUSPENDED]:
            if not force:
                raise ValueError("Cannot delete running VM without force=true")
            await self._destroy_vm(vm_id, force=True)
        
        # Remove from storage
        del self.vms[vm_id]
        
        logger.info(f"VM deleted: {vm_id}")


# Global instance
vm_service = VMService()
