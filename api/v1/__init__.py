"""
TurdParty API Version 1 Package

This package contains the version 1 implementation of the TurdParty API.
It provides RESTful endpoints for malware analysis workflows including
file upload, VM management, and analysis orchestration.

API Version: 1.0
Base Path: /api/v1

Key Components:
    - application: FastAPI application factory and configuration
    - routes: Organized route handlers for different API domains

API Domains:
    1. **Files**: File upload, storage, and metadata management
    2. **VMs**: Virtual machine lifecycle and configuration management
    3. **Injection**: File injection operations into analysis VMs
    4. **Templates**: VM template and configuration management
    5. **Reporting**: Analysis results and report generation

Features:
    - RESTful API design following OpenAPI 3.0 specification
    - Automatic request/response validation using Pydantic models
    - Comprehensive error handling with standardized error responses
    - ECS-compliant structured logging for all operations
    - WebSocket support for real-time status updates
    - Async/await for high-performance non-blocking operations

Usage:
    Create and configure the API application:

    from api.v1 import create_app

    app = create_app()

Versioning Strategy:
    - Semantic versioning (MAJOR.MINOR.PATCH)
    - Backward compatibility maintained within major versions
    - Deprecation notices provided before breaking changes
    - Migration guides provided for major version upgrades
"""
