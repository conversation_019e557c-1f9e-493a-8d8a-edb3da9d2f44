"""
TurdParty API v1 Routes Package

This package contains all route handlers for API version 1.
Routes are organized by domain and provide RESTful endpoints
for the TurdParty malware analysis platform.

Route Organization:
    - file_injection: File injection operations and status tracking
    - files: File upload, download, and metadata management
    - reporting: Analysis results and report generation
    - template_injection: VM template injection and configuration
    - vm_files: VM file system operations and management
    - vm_injection: VM-specific file injection operations
    - vm_management: VM lifecycle management and monitoring

Route Patterns:
    - GET /api/v1/{domain} - List resources
    - POST /api/v1/{domain} - Create new resource
    - GET /api/v1/{domain}/{id} - Get specific resource
    - PUT /api/v1/{domain}/{id} - Update resource
    - DELETE /api/v1/{domain}/{id} - Delete resource

Common Features:
    - Automatic request/response validation
    - Standardized error responses
    - ECS-compliant logging
    - OpenAPI documentation generation
    - Rate limiting and authentication (where applicable)
    - WebSocket endpoints for real-time updates

Usage:
    Routes are automatically registered with the FastAPI application:

    from api.v1.routes import file_injection, vm_management

    app.include_router(file_injection.router)
    app.include_router(vm_management.router)

Integration:
    Routes interact with:
    - Service layer for business logic
    - Database models for persistence
    - Celery workers for background tasks
    - External APIs and services
"""
