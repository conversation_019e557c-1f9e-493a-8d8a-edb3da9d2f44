"""
File Injection API Endpoints

This module provides REST API endpoints for managing file injection operations
in the TurdParty malware analysis platform. File injection allows uploading
and injecting files into analysis environments for behavioral monitoring.

Key Features:
    - File upload and injection management
    - Status tracking and monitoring
    - ELK stack integration for event logging
    - Comprehensive error handling and validation
    - RESTful API design with proper HTTP status codes

File Injection Workflow:
    1. Upload file via POST /file_injection/
    2. Monitor status via GET /file_injection/{id}/status
    3. Process injection via POST /file_injection/{id}/process
    4. Track results through ELK logging
    5. Clean up via DELETE /file_injection/{id}

ELK Integration:
    - Logs all injection events for monitoring
    - Tracks installation base information
    - Provides audit trail for security analysis
    - Enables real-time monitoring and alerting

API Endpoints:
    - GET /file_injection/ - List all injections with filtering
    - POST /file_injection/ - Create new file injection
    - GET /file_injection/{id} - Get injection details
    - GET /file_injection/{id}/status - Get injection status
    - POST /file_injection/{id}/process - Process pending injection
    - DELETE /file_injection/{id} - Delete injection record

Security Features:
    - File validation and sanitization
    - Permission management for injected files
    - Comprehensive error handling
    - Audit logging for all operations
"""
import os
import uuid
import logging
from datetime import datetime
from typing import Optional, List
from fastapi import APIRouter, HTTPException, status, UploadFile, File, Form
from pydantic import BaseModel
from api.models.file_injection import (
    FileInjectionCreate, 
    FileInjectionResponse, 
    FileInjectionStatus,
    InjectionStatus
)
from api.services.file_injection_service import FileInjectionService
from api.services.elk_logger import ELKLogger

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/file_injection",
    tags=["file_injection"],
    responses={404: {"description": "File injection not found"}},
)

# Initialize services
file_injection_service = FileInjectionService()
elk_logger = ELKLogger()

@router.get("/", response_model=List[FileInjectionResponse])
async def get_all_file_injections(
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None
):
    """
    Get all file injections with optional filtering.
    
    Parameters:
    - **skip**: Number of records to skip (for pagination)
    - **limit**: Maximum number of records to return (for pagination)
    - **status_filter**: Optional status to filter by (pending, in_progress, completed, failed)
    
    Returns:
    - List of file injection objects
    """
    try:
        injections = await file_injection_service.get_all(
            skip=skip,
            limit=limit,
            status_filter=status_filter,
            authenticated=False  # TODO: Implement proper authentication
        )
        return injections
    except Exception as e:
        logger.error(f"Error retrieving file injections: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve file injections"
        )

@router.get("/{injection_id}", response_model=FileInjectionResponse)
async def get_file_injection(injection_id: str):
    """
    Get a specific file injection by ID.
    
    Parameters:
    - **injection_id**: UUID of the file injection to retrieve
    
    Returns:
    - File injection object with all details
    
    Raises:
    - 404: If the file injection is not found
    """
    try:
        injection = await file_injection_service.get_by_id(injection_id)
        if not injection:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File injection with id {injection_id} not found"
            )
        return injection
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving file injection {injection_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve file injection"
        )

@router.post("/", response_model=FileInjectionResponse, status_code=status.HTTP_201_CREATED)
async def create_file_injection(
    file: UploadFile = File(...),
    target_path: str = Form("/app/injected_file"),
    permissions: str = Form("0755"),
    description: Optional[str] = Form(None)
):
    """
    Create a new file injection.
    
    This endpoint uploads a file and creates an injection task.
    The file will be processed and logged to ELK stack.
    
    Parameters:
    - **file**: File to inject
    - **target_path**: Target path for the injected file
    - **permissions**: File permissions (default: 0755)
    - **description**: Optional description of the injection
    
    Returns:
    - Created file injection object with status set to "pending"
    """
    try:
        # Create injection request
        injection_data = FileInjectionCreate(
            filename=file.filename,
            target_path=target_path,
            permissions=permissions,
            description=description
        )
        
        # Process the injection
        injection = await file_injection_service.create_injection(
            injection_data=injection_data,
            file_content=await file.read()
        )
        
        # Log to ELK
        await elk_logger.log_file_injection_event(
            injection_id=injection.id,
            event_type="injection_created",
            filename=injection.filename,
            target_path=injection.target_path,
            status=injection.status
        )
        
        return injection
        
    except Exception as e:
        logger.error(f"Error creating file injection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create file injection: {str(e)}"
        )

@router.get("/{injection_id}/status", response_model=FileInjectionStatus)
async def get_injection_status(injection_id: str):
    """
    Get the status of a file injection.
    
    Parameters:
    - **injection_id**: UUID of the file injection to check
    
    Returns:
    - Status object with current status and details
    """
    try:
        status_info = await file_injection_service.get_status(injection_id)
        if not status_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File injection with id {injection_id} not found"
            )
        return status_info
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting injection status {injection_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get injection status"
        )

@router.post("/{injection_id}/process", response_model=FileInjectionStatus)
async def process_injection(injection_id: str):
    """
    Process a pending file injection.
    
    This endpoint triggers the actual file injection process and logs
    the installation base information to ELK stack.
    
    Parameters:
    - **injection_id**: UUID of the file injection to process
    
    Returns:
    - Updated status object
    """
    try:
        # Process the injection
        result = await file_injection_service.process_injection(injection_id)
        
        # Log processing event to ELK
        await elk_logger.log_file_injection_event(
            injection_id=injection_id,
            event_type="injection_processed",
            status=result.status,
            details=result.details
        )
        
        # Log installation base information
        await elk_logger.log_installation_base(
            injection_id=injection_id,
            file_path=result.details.get("file_path"),
            target_path=result.details.get("target_path"),
            permissions=result.details.get("permissions")
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Error processing injection {injection_id}: {str(e)}")
        
        # Log error to ELK
        await elk_logger.log_file_injection_event(
            injection_id=injection_id,
            event_type="injection_failed",
            status="failed",
            error_message=str(e)
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process injection: {str(e)}"
        )

@router.delete("/{injection_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_file_injection(injection_id: str):
    """
    Delete a file injection record.
    
    Parameters:
    - **injection_id**: UUID of the file injection to delete
    
    Returns:
    - No content (204)
    """
    try:
        await file_injection_service.delete(injection_id)
        
        # Log deletion to ELK
        await elk_logger.log_file_injection_event(
            injection_id=injection_id,
            event_type="injection_deleted"
        )
        
    except Exception as e:
        logger.error(f"Error deleting injection {injection_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete file injection"
        )
