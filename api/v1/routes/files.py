"""
File Management API Endpoints

This module provides comprehensive REST API endpoints for file management operations
in the TurdParty malware analysis platform. It handles file upload, storage, retrieval,
and lifecycle management for analysis workflows.

Key Features:
    - File upload with validation and metadata extraction
    - File storage and retrieval operations
    - File metadata management and tracking
    - Download functionality for analysis results
    - File lifecycle management (upload, store, analyze, cleanup)
    - RESTful API design with comprehensive error handling

File Operations:
    - Upload: Secure file upload with size and type validation
    - Storage: Persistent file storage with metadata tracking
    - Retrieval: File metadata and content retrieval
    - Download: Secure file download with access control
    - Deletion: File cleanup and storage management

API Endpoints:
    - GET /files/ - List all files with pagination and filtering
    - POST /files/upload - Upload new file with metadata
    - GET /files/{id} - Get file metadata by ID
    - GET /files/{id}/download - Download file content
    - DELETE /files/{id} - Delete file and cleanup storage

Security Features:
    - File type validation and sanitization
    - Size limits and quota management
    - Access control and authentication
    - Audit logging for all file operations
    - Secure file storage with encryption

Integration:
    - MinIO object storage for file persistence
    - Database integration for metadata tracking
    - Workflow system for analysis orchestration
    - ELK stack for audit logging and monitoring

Mock Implementation:
    This module includes mock storage for development and testing.
    Production deployment uses MinIO and PostgreSQL for persistence.
"""
import os
import uuid
import logging
from datetime import datetime, timezone
from typing import Optional, List
from fastapi import APIRouter, HTTPException, status, UploadFile, File, Form, Query
from fastapi.responses import FileResponse
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/files",
    tags=["files"],
    responses={404: {"description": "File not found"}},
)

class FileResponse(BaseModel):
    """
    Response model for file operations.

    Attributes:
        file_id: Unique identifier for the file
        filename: Original filename as uploaded
        file_size: File size in bytes
        status: Current file status (uploaded, processing, completed, failed)
        created_at: ISO timestamp of file upload
        workflow_job_id: Optional workflow job ID if file is being processed
    """
    file_id: str
    filename: str
    file_size: int
    status: str
    created_at: str
    workflow_job_id: Optional[str] = None

class FileListResponse(BaseModel):
    """
    Response model for paginated file listings.

    Attributes:
        files: List of file objects
        total: Total number of files matching the query
        skip: Number of records skipped for pagination
        limit: Maximum number of records returned
    """
    files: List[FileResponse]
    total: int
    skip: int
    limit: int

# Real file storage using MinIO and database
from minio import Minio
from minio.error import S3Error
import hashlib
import tempfile
from pathlib import Path

# MinIO client configuration - use container hostname
MINIO_ENDPOINT = os.getenv("MINIO_ENDPOINT", "storage:9000")
MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY", "minioadmin")
MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY", "minioadmin")
MINIO_BUCKET = os.getenv("MINIO_BUCKET", "turdparty-files")

# Initialize MinIO client
minio_client = Minio(
    MINIO_ENDPOINT,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=False  # Set to True for HTTPS
)

# Ensure bucket exists
try:
    if not minio_client.bucket_exists(MINIO_BUCKET):
        minio_client.make_bucket(MINIO_BUCKET)
        logger.info(f"Created MinIO bucket: {MINIO_BUCKET}")
except Exception as e:
    logger.warning(f"Could not create/verify MinIO bucket: {e}")

# In-memory storage for development (will be replaced with database)
file_metadata_store = {}

@router.get("/", response_model=FileListResponse)
async def list_files(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status_filter: Optional[str] = Query(None)
):
    """
    List all uploaded files with pagination and filtering.

    Parameters:
    - **skip**: Number of records to skip (for pagination)
    - **limit**: Maximum number of records to return
    - **status_filter**: Filter by file status (stored, processing, error)

    Returns:
    - Paginated list of files
    """
    try:
        # Get files from metadata store (in production, this would be from database)
        files = list(file_metadata_store.values())

        # Apply status filter
        if status_filter:
            files = [f for f in files if f["status"] == status_filter]

        # Sort by creation date (newest first)
        files.sort(key=lambda x: x["created_at"], reverse=True)

        # Apply pagination
        total = len(files)
        files = files[skip:skip + limit]

        return FileListResponse(
            files=files,
            total=total,
            skip=skip,
            limit=limit
        )
    except Exception as e:
        logger.error(f"Error listing files: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list files"
        )

@router.get("/{file_id}", response_model=FileResponse)
async def get_file(file_id: str):
    """
    Get file metadata by ID.
    
    Parameters:
    - **file_id**: UUID of the file to retrieve
    
    Returns:
    - File metadata object
    """
    try:
        # Get file from metadata store
        file_data = file_metadata_store.get(file_id)
        if not file_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File with id {file_id} not found"
            )
        return file_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving file {file_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve file"
        )

@router.post("/upload", response_model=FileResponse, status_code=status.HTTP_201_CREATED)
async def upload_file(
    file: UploadFile = File(...),
    description: Optional[str] = Form(None)
):
    """
    Upload a new file.
    
    Parameters:
    - **file**: File to upload
    - **description**: Optional description
    
    Returns:
    - Created file object
    """
    try:
        # Generate unique file ID
        file_id = str(uuid.uuid4())

        # Read file content
        file_content = await file.read()
        file_size = len(file_content)

        # Calculate file hash
        file_hash = hashlib.sha256(file_content).hexdigest()

        # Create object name for MinIO
        object_name = f"{file_id}/{file.filename}"

        # Upload to MinIO
        try:
            with tempfile.NamedTemporaryFile() as temp_file:
                temp_file.write(file_content)
                temp_file.seek(0)

                minio_client.put_object(
                    MINIO_BUCKET,
                    object_name,
                    temp_file,
                    length=file_size,
                    content_type=file.content_type or "application/octet-stream"
                )

            logger.info(f"File uploaded to MinIO: {object_name}")

        except S3Error as e:
            logger.error(f"MinIO upload failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to store file: {str(e)}"
            )

        # Create file record
        file_record = {
            "file_id": file_id,
            "filename": file.filename,
            "file_size": file_size,
            "file_hash": file_hash,
            "content_type": file.content_type or "application/octet-stream",
            "object_name": object_name,
            "status": "stored",
            "created_at": datetime.now(timezone.utc).isoformat(),
            "workflow_job_id": None,
            "description": description
        }

        # Store metadata
        file_metadata_store[file_id] = file_record

        logger.info(f"File uploaded successfully: {file.filename} (ID: {file_id}, Size: {file_size} bytes)")
        return file_record
        
    except Exception as e:
        logger.error(f"Error uploading file: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload file: {str(e)}"
        )

@router.get("/{file_id}/download")
async def download_file(file_id: str):
    """
    Download a file by ID.
    
    Parameters:
    - **file_id**: UUID of the file to download
    
    Returns:
    - File content as download
    """
    try:
        # Get file metadata
        file_data = file_metadata_store.get(file_id)
        if not file_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File with id {file_id} not found"
            )

        # Download from MinIO
        try:
            response = minio_client.get_object(MINIO_BUCKET, file_data["object_name"])

            # Create temporary file for download
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                for data in response.stream(32*1024):
                    temp_file.write(data)
                temp_path = temp_file.name

            response.close()
            response.release_conn()

            # Return file response
            return FileResponse(
                path=temp_path,
                filename=file_data["filename"],
                media_type=file_data.get("content_type", "application/octet-stream")
            )

        except S3Error as e:
            logger.error(f"MinIO download failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to download file: {str(e)}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading file {file_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to download file"
        )

@router.delete("/{file_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_file(file_id: str):
    """
    Delete a file by ID.
    
    Parameters:
    - **file_id**: UUID of the file to delete
    
    Returns:
    - No content (204)
    """
    try:
        # Get file metadata
        file_data = file_metadata_store.get(file_id)
        if not file_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File with id {file_id} not found"
            )

        # Delete from MinIO
        try:
            minio_client.remove_object(MINIO_BUCKET, file_data["object_name"])
            logger.info(f"File deleted from MinIO: {file_data['object_name']}")
        except S3Error as e:
            logger.warning(f"Failed to delete from MinIO: {e}")
            # Continue with metadata deletion even if MinIO deletion fails

        # Remove from metadata store
        del file_metadata_store[file_id]

        logger.info(f"File deleted successfully: {file_id}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting file {file_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete file"
        )
