"""
Enhanced VM Management API with real-time WebSocket support
"""
from fastapi import APIRouter, HTTPException, status, Depends, Query, WebSocket, WebSocketDisconnect
from typing import Optional, List, Dict, Any
import uuid
import logging
import asyncio
import json
from datetime import datetime, timezone

from api.models.vm_management import (
    VMCreateRequest, VMResponse, VMListResponse, VMActionRequest, 
    VMActionResponse, VMTemplateResponse
)
from api.services.vm_service import VMService
from api.services.vm_metrics_service import vm_metrics_service, VMMetricsService
from api.middleware.ecs_logging import WebSocketECSLogger

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/vms",
    tags=["vm_management"],
    responses={404: {"description": "VM not found"}},
)

# Dependency injection functions
def get_vm_service() -> VMService:
    """Get VM service instance"""
    return VMService()

def get_vm_metrics_service() -> VMMetricsService:
    """Get VM metrics service instance"""
    return vm_metrics_service

# WebSocket connection manager
class ConnectionManager:
    """
    Manages WebSocket connections for VM real-time communication.

    Handles multiple WebSocket connections per VM, enabling real-time
    metrics streaming, command execution, and status updates. Provides
    connection lifecycle management and message broadcasting capabilities.

    Attributes:
        active_connections: Dictionary mapping VM IDs to lists of WebSocket connections
    """
    def __init__(self):
        """Initialize the connection manager with empty connections dictionary."""
        self.active_connections: Dict[str, List[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, vm_id: str):
        """
        Accept and register a new WebSocket connection for a VM.

        Args:
            websocket: The WebSocket connection to register
            vm_id: Unique identifier of the VM this connection is for
        """
        await websocket.accept()
        if vm_id not in self.active_connections:
            self.active_connections[vm_id] = []
        self.active_connections[vm_id].append(websocket)
        logger.info(f"WebSocket connected for VM {vm_id}")
    
    def disconnect(self, websocket: WebSocket, vm_id: str):
        """
        Remove a WebSocket connection from the VM's connection list.

        Args:
            websocket: The WebSocket connection to remove
            vm_id: Unique identifier of the VM this connection was for
        """
        if vm_id in self.active_connections:
            self.active_connections[vm_id].remove(websocket)
            if not self.active_connections[vm_id]:
                del self.active_connections[vm_id]
        logger.info(f"WebSocket disconnected for VM {vm_id}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """
        Send a message to a specific WebSocket connection.

        Args:
            message: Text message to send
            websocket: Target WebSocket connection
        """
        await websocket.send_text(message)
    
    async def broadcast_to_vm(self, message: str, vm_id: str):
        """
        Broadcast a message to all WebSocket connections for a specific VM.

        Args:
            message: Text message to broadcast
            vm_id: Unique identifier of the VM whose connections should receive the message

        Note:
            Failed connections are logged but don't interrupt the broadcast to other connections.
        """
        if vm_id in self.active_connections:
            for connection in self.active_connections[vm_id]:
                try:
                    await connection.send_text(message)
                except Exception as e:
                    logger.error(f"Error broadcasting to VM {vm_id}: {e}")

manager = ConnectionManager()

# VM storage is now handled by the VM service

@router.websocket("/{vm_id}/metrics/stream")
async def stream_vm_metrics(websocket: WebSocket, vm_id: str, vm_type: str = "docker"):
    """Stream real-time VM metrics via WebSocket"""
    connection_id = str(uuid.uuid4())
    client_ip = websocket.client.host if websocket.client else "unknown"
    user_agent = websocket.headers.get("user-agent", "unknown")

    # Log WebSocket connection start
    WebSocketECSLogger.log_websocket_event(
        event_type="connection_start",
        vm_id=vm_id,
        connection_id=connection_id,
        client_ip=client_ip,
        user_agent=user_agent,
        data={"vm_type": vm_type, "endpoint": "metrics_stream"}
    )

    await manager.connect(websocket, vm_id)
    
    try:
        # Initialize metrics service
        await vm_metrics_service.initialize()
        
        # Start streaming metrics
        async for metrics in vm_metrics_service.stream_vm_metrics(vm_id, vm_type, interval=1.0):
            await websocket.send_json(metrics)
            
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for VM {vm_id} metrics stream")
    except Exception as e:
        logger.error(f"Error in metrics stream for VM {vm_id}: {e}")
        await websocket.send_json({
            "error": str(e),
            "timestamp": int(datetime.now(datetime.UTC).timestamp() * 1000)
        })
    finally:
        manager.disconnect(websocket, vm_id)
        vm_metrics_service.stop_stream(vm_id, vm_type)

@router.websocket("/{vm_id}/commands/execute")
async def execute_command_stream(websocket: WebSocket, vm_id: str):
    """Execute commands with real-time output streaming"""
    await manager.connect(websocket, f"{vm_id}_commands")
    
    try:
        while True:
            # Wait for command from client
            data = await websocket.receive_text()
            command_data = json.loads(data)
            
            command = command_data.get("command", "")
            working_dir = command_data.get("working_directory", "/tmp")
            
            if not command:
                await websocket.send_json({
                    "type": "error",
                    "message": "No command provided"
                })
                continue
            
            # Execute command and stream output
            await execute_command_with_streaming(websocket, vm_id, command, working_dir)
            
    except WebSocketDisconnect:
        logger.info(f"Command execution WebSocket disconnected for VM {vm_id}")
    except Exception as e:
        logger.error(f"Error in command execution for VM {vm_id}: {e}")
        await websocket.send_json({
            "type": "error",
            "message": str(e)
        })
    finally:
        manager.disconnect(websocket, f"{vm_id}_commands")

async def execute_command_with_streaming(websocket: WebSocket, vm_id: str, command: str, working_dir: str):
    """Execute command and stream output in real-time"""
    try:
        # For Docker containers
        if vm_id.startswith("docker_"):
            await execute_docker_command_stream(websocket, vm_id, command, working_dir)
        else:
            # For Vagrant VMs (mock implementation for now)
            await execute_vagrant_command_stream(websocket, vm_id, command, working_dir)
            
    except Exception as e:
        await websocket.send_json({
            "type": "command_output",
            "stderr": f"Command execution error: {str(e)}",
            "exit_code": 1,
            "is_complete": True
        })

async def execute_docker_command_stream(websocket: WebSocket, vm_id: str, command: str, working_dir: str):
    """Execute command in Docker container with streaming"""
    try:
        import docker
        client = docker.from_env()
        container_id = vm_id.replace("docker_", "")
        container = client.containers.get(container_id)
        
        # Create exec instance
        exec_instance = container.exec_run(
            f"cd {working_dir} && {command}",
            stream=True,
            demux=True
        )
        
        # Stream output
        for chunk in exec_instance.output:
            if chunk[0]:  # stdout
                await websocket.send_json({
                    "type": "command_output",
                    "stdout": chunk[0].decode(),
                    "is_complete": False
                })
            if chunk[1]:  # stderr
                await websocket.send_json({
                    "type": "command_output",
                    "stderr": chunk[1].decode(),
                    "is_complete": False
                })
        
        # Send completion
        await websocket.send_json({
            "type": "command_output",
            "exit_code": exec_instance.exit_code,
            "is_complete": True
        })
        
    except Exception as e:
        await websocket.send_json({
            "type": "command_output",
            "stderr": f"Docker command error: {str(e)}",
            "exit_code": 1,
            "is_complete": True
        })

async def execute_vagrant_command_stream(websocket: WebSocket, vm_id: str, command: str, working_dir: str):
    """Execute command in Vagrant VM with streaming (mock implementation)"""
    try:
        # Mock streaming output for Vagrant
        await websocket.send_json({
            "type": "command_output",
            "stdout": f"$ cd {working_dir} && {command}\n",
            "is_complete": False
        })
        
        # Simulate command execution
        await asyncio.sleep(0.5)
        
        await websocket.send_json({
            "type": "command_output",
            "stdout": f"Mock output for command: {command}\n",
            "is_complete": False
        })
        
        await asyncio.sleep(0.5)
        
        await websocket.send_json({
            "type": "command_output",
            "stdout": "Command completed successfully\n",
            "exit_code": 0,
            "is_complete": True
        })
        
    except Exception as e:
        await websocket.send_json({
            "type": "command_output",
            "stderr": f"Vagrant command error: {str(e)}",
            "exit_code": 1,
            "is_complete": True
        })

# Traditional REST endpoints
@router.get("/templates", response_model=List[VMTemplateResponse])
async def get_vm_templates():
    """Get available VM templates"""
    templates = [
        {
            "template_id": "ubuntu:20.04",
            "name": "Ubuntu 20.04 LTS",
            "description": "Ubuntu 20.04 LTS with security tools",
            "vm_type": "docker",
            "compatibility": ["linux", "malware_analysis"],
            "resource_requirements": {
                "min_memory_mb": 512,
                "min_cpus": 1,
                "min_disk_gb": 10
            }
        },
        {
            "template_id": "windows:10",
            "name": "Windows 10 Enterprise",
            "description": "Windows 10 Enterprise for malware analysis",
            "vm_type": "vagrant",
            "compatibility": ["windows", "malware_analysis"],
            "resource_requirements": {
                "min_memory_mb": 2048,
                "min_cpus": 2,
                "min_disk_gb": 40
            }
        }
    ]
    return templates

@router.post("/", response_model=VMResponse, status_code=status.HTTP_201_CREATED)
async def create_vm(
    request: VMCreateRequest,
    vm_service: VMService = Depends(get_vm_service)
):
    """
    Create a new virtual machine instance.

    Creates a VM based on the provided configuration including template,
    resource allocation, and domain settings. Supports both Docker containers
    and Vagrant VMs with automatic startup for Docker instances.

    Args:
        request: VM creation request containing configuration details
        vm_service: VM service dependency

    Returns:
        VMResponse: Complete VM information including ID, status, and configuration

    Raises:
        HTTPException: If VM creation fails due to resource constraints or configuration errors

    Example:
        POST /api/v1/vms/
        {
            "name": "analysis-vm",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 2048,
            "cpus": 2,
            "domain": "malware.local"
        }
    """
    try:
        # Use the VM service to create the VM
        vm_response = await vm_service.create_vm(request)

        logger.info(f"VM created successfully: {vm_response.vm_id}")
        return vm_response

    except Exception as e:
        logger.error(f"Error creating VM: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create VM: {str(e)}"
        )

@router.get("/", response_model=VMListResponse)
async def list_vms(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    status_filter: Optional[str] = Query(None),
    vm_service: VMService = Depends(get_vm_service)
):
    """List VMs with pagination and filtering"""
    try:
        # Get VMs from the VM service
        vms_data = await vm_service.list_vms(skip=skip, limit=limit, status_filter=status_filter)

        return VMListResponse(
            vms=vms_data["vms"],
            total=vms_data["total"],
            skip=skip,
            limit=limit
        )

    except Exception as e:
        logger.error(f"Error listing VMs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list VMs"
        )

@router.get("/{vm_id}", response_model=VMResponse)
async def get_vm(
    vm_id: str,
    vm_service: VMService = Depends(get_vm_service),
    vm_metrics_service: VMMetricsService = Depends(get_vm_metrics_service)
):
    """Get VM details by ID"""
    try:
        # Get VM from the VM service
        vm_data = await vm_service.get_vm(vm_id)

        if not vm_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"VM with id {vm_id} not found"
            )

        # Add current metrics if VM is running
        if vm_data.status == "running":
            try:
                metrics = await vm_metrics_service.get_vm_metrics(vm_id, vm_data.vm_type)
                vm_data.current_metrics = {
                    "cpu_percent": metrics.get("cpu_percent", 0),
                    "memory_percent": metrics.get("memory_percent", 0),
                    "uptime_seconds": metrics.get("uptime_seconds", 0)
                }
            except Exception as e:
                logger.warning(f"Could not get metrics for VM {vm_id}: {e}")

        return vm_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting VM {vm_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get VM details"
        )

@router.post("/{vm_id}/action", response_model=VMActionResponse)
async def vm_action(
    vm_id: str,
    action_request: VMActionRequest,
    vm_service: VMService = Depends(get_vm_service)
):
    """Perform action on VM (start, stop, restart, destroy, suspend, resume)"""
    try:
        # Use the VM service to perform the action
        result = await vm_service.perform_action(
            vm_id=vm_id,
            action=action_request.action,
            force=getattr(action_request, 'force', False)
        )

        response = VMActionResponse(
            vm_id=vm_id,
            action=action_request.action,
            status=result["new_status"],
            task_id=f"task_{uuid.uuid4().hex[:8]}",
            message=f"VM {action_request.action} completed successfully"
        )

        logger.info(f"VM {vm_id} action {action_request.action} completed: {result['old_status']} -> {result['new_status']}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error performing action {action_request.action} on VM {vm_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to perform VM action: {str(e)}"
        )

@router.delete("/{vm_id}")
async def delete_vm(
    vm_id: str,
    force: bool = Query(False),
    vm_service: VMService = Depends(get_vm_service),
    vm_metrics_service: VMMetricsService = Depends(get_vm_metrics_service)
):
    """
    Delete a virtual machine and clean up resources.

    Permanently removes a VM from the system including stopping any active
    streams and cleaning up associated resources. Requires force=true to
    delete running VMs.

    Args:
        vm_id: Unique identifier of the VM to delete
        force: If True, allows deletion of running VMs
        vm_service: VM service dependency
        vm_metrics_service: VM metrics service dependency

    Returns:
        dict: Deletion confirmation with task ID for tracking

    Raises:
        HTTPException:
            - 404 if VM not found
            - 400 if trying to delete running VM without force
            - 500 if deletion operation fails

    Example:
        DELETE /api/v1/vms/{vm_id}?force=true
    """
    try:
        # Get VM to check if it exists and get its type
        vm_data = await vm_service.get_vm(vm_id)
        if not vm_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"VM with id {vm_id} not found"
            )

        # Check if VM can be deleted
        if vm_data.status == "running" and not force:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete running VM without force=true"
            )

        # Stop any active streams
        vm_metrics_service.stop_stream(vm_id, vm_data.vm_type)

        # Delete VM using the service
        await vm_service.delete_vm(vm_id, force=force)

        task_id = f"task_{uuid.uuid4().hex[:8]}"

        logger.info(f"VM {vm_id} deleted successfully")
        return {
            "vm_id": vm_id,
            "message": "VM deletion completed",
            "task_id": task_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting VM {vm_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete VM: {str(e)}"
        )
