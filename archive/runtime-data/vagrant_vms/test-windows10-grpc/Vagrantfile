
Vagrant.configure("2") do |config|
  config.vm.box = "10Baht/windows10-turdparty"
  config.vm.hostname = "test-windows10-grpc"

  config.vm.provider "virtualbox" do |vb|
    vb.memory = 4096
    vb.cpus = 2
    vb.name = "test-windows10-grpc"
    vb.gui = false
  end

  # Network configuration
  config.vm.network "private_network", type: "dhcp"
  config.vm.network "forwarded_port", guest: 3389, host: 33890, auto_correct: true
  config.vm.network "forwarded_port", guest: 22, host: 2222, auto_correct: true

  # Provision monitoring agents
  config.vm.provision "shell", inline: <<-SHELL
    # Enable PowerShell execution
    Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Force

    # Create TurdParty monitoring directory
    New-Item -ItemType Directory -Force -Path "C:\\TurdParty"
    New-Item -ItemType Directory -Force -Path "C:\\TurdParty\\logs"
    New-Item -ItemType Directory -Force -Path "C:\\TurdParty\\uploads"

    # Install monitoring agent
    $monitorScript = @'
# TurdParty VM Monitoring Agent
$logFile = "C:\\TurdParty\\logs\\monitoring.log"
$eventFile = "C:\\TurdParty\\logs\\events.json"

# File system watcher
$watcher = New-Object System.IO.FileSystemWatcher
$watcher.Path = "C:\\"
$watcher.IncludeSubdirectories = $true
$watcher.EnableRaisingEvents = $true

$action = {
    $path = $Event.SourceEventArgs.FullPath
    $changeType = $Event.SourceEventArgs.ChangeType
    $timeStamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"

    $event = @{
        "@timestamp" = $timeStamp
        "event" = @{
            "kind" = "event"
            "category" = @("file")
            "type" = @($changeType.ToString().ToLower())
            "action" = "file_$($changeType.ToString().ToLower())"
            "outcome" = "success"
        }
        "file" = @{
            "path" = $path
            "type" = "file"
        }
        "host" = @{
            "name" = "test-windows10-grpc"
            "id" = "test-windows10-grpc"
        }
        "service" = @{
            "name" = "turdparty-vm-agent"
            "type" = "monitoring"
        }
        "tags" = @("vm-monitoring", "file-system", "malware-analysis")
    }

    $eventJson = $event | ConvertTo-Json -Depth 10 -Compress
    Add-Content -Path $eventFile -Value $eventJson

    $logEntry = "$timeStamp - $changeType - $path"
    Add-Content -Path $logFile -Value $logEntry
}

Register-ObjectEvent -InputObject $watcher -EventName "Created" -Action $action
Register-ObjectEvent -InputObject $watcher -EventName "Changed" -Action $action
Register-ObjectEvent -InputObject $watcher -EventName "Deleted" -Action $action

# Process monitoring
$processWatcher = {
    while ($true) {
        $processes = Get-Process | Where-Object { $_.StartTime -gt (Get-Date).AddMinutes(-1) }
        foreach ($proc in $processes) {
            $timeStamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
            $event = @{
                "@timestamp" = $timeStamp
                "event" = @{
                    "kind" = "event"
                    "category" = @("process")
                    "type" = @("start")
                    "action" = "process_start"
                    "outcome" = "success"
                }
                "process" = @{
                    "name" = $proc.ProcessName
                    "pid" = $proc.Id
                    "command_line" = $proc.Path
                }
                "host" = @{
                    "name" = "test-windows10-grpc"
                    "id" = "test-windows10-grpc"
                }
                "service" = @{
                    "name" = "turdparty-vm-agent"
                    "type" = "monitoring"
                }
                "tags" = @("vm-monitoring", "process", "malware-analysis")
            }

            $eventJson = $event | ConvertTo-Json -Depth 10 -Compress
            Add-Content -Path $eventFile -Value $eventJson
        }
        Start-Sleep 10
    }
}

# Start monitoring in background
Start-Job -ScriptBlock $processWatcher

# Keep file watcher running
while ($true) { Start-Sleep 30 }
'@

    $monitorScript | Out-File -FilePath "C:\\TurdParty\\monitor.ps1" -Encoding UTF8

    # Start monitoring service
    Start-Process powershell -ArgumentList "-File C:\\TurdParty\\monitor.ps1" -WindowStyle Hidden
  SHELL
end
