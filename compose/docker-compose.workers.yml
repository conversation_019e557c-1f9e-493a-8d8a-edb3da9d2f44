# TurdParty Worker Services
# Phase 2: Celery workers for file processing, VM management, and injection

services:
  # File Operations Worker
  worker-file:
    container_name: turdpartycollab_worker_file
    build:
      context: ../services/workers
      dockerfile: Dockerfile.celery
    command: celery -A celery_app worker --loglevel=info --queues=file_ops --concurrency=2
    environment:
      # Database
      - DATABASE_URL=********************************************/turdparty

      # Redis
      - REDIS_HOST=cache
      - REDIS_PORT=6379
      - REDIS_DB=0

      # MinIO
      - MINIO_ENDPOINT=storage:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_SECURE=false

      # Logging
      - LOG_LEVEL=INFO
    depends_on:
      - cache
      - database
      - storage
    networks:
      - turdpartycollab_net
    volumes:
      - ../data/temp_files:/tmp/turdparty
      - /var/run/docker.sock:/var/run/docker.sock
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # VM Management Worker
  worker-vm:
    container_name: turdpartycollab_worker_vm
    build:
      context: ../services/workers
      dockerfile: Dockerfile.celery
    command: celery -A celery_app worker --loglevel=info --queues=vm_ops --concurrency=1
    environment:
      # Database
      - DATABASE_URL=********************************************/turdparty

      # Redis
      - REDIS_HOST=cache
      - REDIS_PORT=6379
      - REDIS_DB=0

      # Docker
      - DOCKER_HOST=unix:///var/run/docker.sock

      # Logging
      - LOG_LEVEL=INFO
    depends_on:
      - cache
      - database
    networks:
      - turdpartycollab_net
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ../data/temp_files:/tmp/turdparty
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # File Injection Worker
  worker-injection:
    container_name: turdpartycollab_worker_injection
    build:
      context: ../services/workers
      dockerfile: Dockerfile.celery
    command: celery -A celery_app worker --loglevel=info --queues=injection_ops --concurrency=2
    environment:
      # Database
      - DATABASE_URL=********************************************/turdparty

      # Redis
      - REDIS_HOST=cache
      - REDIS_PORT=6379
      - REDIS_DB=0

      # MinIO
      - MINIO_ENDPOINT=storage:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_SECURE=false

      # Docker
      - DOCKER_HOST=unix:///var/run/docker.sock

      # Logging
      - LOG_LEVEL=INFO
    depends_on:
      - cache
      - database
      - storage
    networks:
      - turdpartycollab_net
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ../data/temp_files:/tmp/turdparty
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # VM Pool Manager Worker
  worker-pool:
    container_name: turdpartycollab_worker_pool
    build:
      context: ../services/workers
      dockerfile: Dockerfile.celery
    command: celery -A celery_app worker --loglevel=info --queues=pool_ops --concurrency=1
    environment:
      # Database
      - DATABASE_URL=********************************************/turdparty

      # Redis
      - REDIS_HOST=cache
      - REDIS_PORT=6379
      - REDIS_DB=0

      # Docker
      - DOCKER_HOST=unix:///var/run/docker.sock

      # VM Pool Configuration
      - VM_POOL_MIN_SIZE=2
      - VM_POOL_MAX_SIZE=10

      # Logging
      - LOG_LEVEL=INFO
    depends_on:
      - cache
      - database
    networks:
      - turdpartycollab_net
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Workflow Orchestrator Worker
  worker-workflow:
    container_name: turdpartycollab_worker_workflow
    build:
      context: ../services/workers
      dockerfile: Dockerfile.celery
    command: celery -A celery_app worker --loglevel=info --queues=workflow_ops --concurrency=2
    environment:
      # Database
      - DATABASE_URL=********************************************/turdparty

      # Redis
      - REDIS_HOST=cache
      - REDIS_PORT=6379
      - REDIS_DB=0

      # Logging
      - LOG_LEVEL=INFO
    depends_on:
      - cache
      - database
    networks:
      - turdpartycollab_net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ELK Integration Worker
  worker-elk:
    container_name: turdpartycollab_worker_elk
    build:
      context: ../services/workers
      dockerfile: Dockerfile.celery
    command: celery -A celery_app worker --loglevel=info --queues=elk_ops --concurrency=3
    environment:
      # Database
      - DATABASE_URL=********************************************/turdparty

      # Redis
      - REDIS_HOST=cache
      - REDIS_PORT=6379
      - REDIS_DB=0

      # ELK Stack
      - LOGSTASH_HOST=logstash
      - LOGSTASH_HTTP_PORT=8080
      - ELASTICSEARCH_HOST=elasticsearch
      - ELASTICSEARCH_PORT=9200

      # Logging
      - LOG_LEVEL=INFO
    depends_on:
      - cache
      - database
    networks:
      - turdpartycollab_net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Beat Scheduler (for periodic tasks)
  worker-beat:
    container_name: turdpartycollab_worker_beat
    build:
      context: ../services/workers
      dockerfile: Dockerfile.celery
    command: celery -A celery_app beat --loglevel=info
    environment:
      # Database
      - DATABASE_URL=********************************************/turdparty

      # Redis
      - REDIS_HOST=cache
      - REDIS_PORT=6379
      - REDIS_DB=0

      # Logging
      - LOG_LEVEL=INFO
    depends_on:
      - cache
      - database
    networks:
      - turdpartycollab_net
    restart: unless-stopped

  # Celery Flower (Task Monitoring)
  task-monitor:
    container_name: turdpartycollab_task_monitor
    build:
      context: ../services/workers
      dockerfile: Dockerfile.celery
    command: celery -A celery_app flower --port=5555 --basic_auth=admin:turdparty123
    ports:
      - "5555:5555"
    environment:
      # Redis/Celery
      - REDIS_HOST=cache
      - REDIS_PORT=6379
      - REDIS_DB=0

      # Flower configuration
      - FLOWER_PORT=5555
      - FLOWER_BASIC_AUTH=admin:turdparty123
    depends_on:
      - cache
    networks:
      - turdpartycollab_net
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.turdparty-flower.rule=Host(`flower.turdparty.localhost`)"
      - "traefik.http.routers.turdparty-flower.entrypoints=web"
      - "traefik.http.services.turdparty-flower.loadbalancer.server.port=5555"
      - "traefik.docker.network=traefik_network"

networks:
  turdpartycollab_net:
    external: true
