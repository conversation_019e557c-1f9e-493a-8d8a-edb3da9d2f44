# Configuration Directory

This directory contains configuration files for various services and components of the TurdParty project.

## Current Structure

```
config/
├── README.md           # This file
└── logstash/
    └── logstash.conf   # Logstash pipeline configuration
```

## Configuration Files

### Logstash Configuration
- **File**: `logstash/logstash.conf`
- **Purpose**: Defines the Logstash pipeline for processing file injection events
- **Components**:
  - Input: Receives data from the API
  - Filter: Processes and enriches log data
  - Output: Sends processed data to Elasticsearch

## Planned Configuration

### API Configuration
- `api/` - API service configuration
  - `settings.py` - Application settings
  - `logging.conf` - Logging configuration
  - `security.conf` - Security settings

### Database Configuration
- `database/` - Database configuration
  - `postgres.conf` - PostgreSQL settings
  - `migrations/` - Database migration scripts

### ELK Stack Configuration
- `elasticsearch/` - Elasticsearch configuration
  - `elasticsearch.yml` - Main configuration
  - `mappings/` - Index mappings
- `kibana/` - Kibana configuration
  - `kibana.yml` - Main configuration
  - `dashboards/` - Pre-configured dashboards

### Monitoring Configuration
- `monitoring/` - Monitoring and alerting
  - `prometheus.yml` - Metrics collection
  - `grafana/` - Grafana dashboards
  - `alerts/` - Alert rules

### Security Configuration
- `security/` - Security-related configuration
  - `ssl/` - SSL certificates and keys
  - `auth/` - Authentication configuration
  - `policies/` - Security policies

## Environment-Specific Configuration

Configuration files should support multiple environments:
- `development` - Local development settings
- `staging` - Staging environment settings
- `production` - Production environment settings

Use environment variables and `.env` files to override settings per environment.

## Configuration Management

### Best Practices
1. **Sensitive Data**: Never commit secrets or sensitive data
2. **Environment Variables**: Use environment variables for environment-specific settings
3. **Validation**: Validate configuration on application startup
4. **Documentation**: Document all configuration options
5. **Defaults**: Provide sensible defaults for all settings

### File Naming Convention
- Use lowercase with hyphens: `service-name.conf`
- Include environment suffix when needed: `api-production.yml`
- Use appropriate extensions: `.yml`, `.conf`, `.json`, `.env`

## Usage

Configuration files are typically mounted into Docker containers or referenced by environment variables:

```yaml
# docker-compose.yml example
services:
  logstash:
    volumes:
      - ./config/logstash/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
```

```bash
# Environment variable example
export CONFIG_FILE=/app/config/api/settings.yml
```
