# Filebeat configuration for Docker container log collection
# Sends all Docker container logs to Logstash for processing

filebeat.inputs:
- type: container
  paths:
    - '/var/lib/docker/containers/*/*.log'
  
  # Parse Docker JSON logs
  json.keys_under_root: true
  json.add_error_key: true
  json.message_key: log
  
  # Add container metadata
  processors:
    - add_docker_metadata:
        host: "unix:///var/run/docker.sock"
        
    # Add service identification from container labels
    - script:
        lang: javascript
        id: parse_container_labels
        source: >
          function process(event) {
            var labels = event.Get("container.labels");
            if (labels) {
              // Extract service name from container name or labels
              var containerName = event.Get("container.name");
              if (containerName && containerName.startsWith("turdpartycollab_")) {
                var serviceName = containerName.replace("turdpartycollab_", "");
                event.Put("service.name", serviceName);
                event.Put("service.namespace", "turdparty");
              }
              
              // Extract component and environment from logging labels
              var loggingLabels = labels["logging"];
              if (loggingLabels) {
                var parts = loggingLabels.split(",");
                for (var i = 0; i < parts.length; i++) {
                  var part = parts[i].trim();
                  if (part.startsWith("service=")) {
                    event.Put("service.name", part.substring(8));
                  } else if (part.startsWith("component=")) {
                    event.Put("service.component", part.substring(10));
                  } else if (part.startsWith("environment=")) {
                    event.Put("service.environment", part.substring(12));
                  }
                }
              }
            }
            
            // Set event category based on service
            var serviceName = event.Get("service.name");
            if (serviceName) {
              switch(serviceName) {
                case "api":
                  event.Put("event.category", "application");
                  event.Put("event.type", "info");
                  break;
                case "elasticsearch":
                  event.Put("event.category", "database");
                  event.Put("event.type", "info");
                  break;
                case "logstash":
                  event.Put("event.category", "process");
                  event.Put("event.type", "info");
                  break;
                case "kibana":
                  event.Put("event.category", "web");
                  event.Put("event.type", "access");
                  break;
                case "frontend":
                  event.Put("event.category", "web");
                  event.Put("event.type", "access");
                  break;
                case "docs":
                  event.Put("event.category", "web");
                  event.Put("event.type", "access");
                  break;
                case "vm-monitor":
                  event.Put("event.category", "process");
                  event.Put("event.type", "info");
                  break;
                case "status":
                  event.Put("event.category", "web");
                  event.Put("event.type", "access");
                  break;
                default:
                  event.Put("event.category", "application");
                  event.Put("event.type", "info");
              }
            }
            
            return event;
          }

# Output to Logstash
output.logstash:
  hosts: ["logstash:5044"]
  
  # Load balancing
  loadbalance: true
  
  # Compression
  compression_level: 3
  
  # Retry settings
  max_retries: 3
  backoff.init: 1s
  backoff.max: 60s

# Logging configuration
logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644

# Monitoring
monitoring.enabled: true
monitoring.elasticsearch:
  hosts: ["elasticsearch:9200"]

# Performance tuning
queue.mem:
  events: 4096
  flush.min_events: 512
  flush.timeout: 1s

# Processors for log enhancement
processors:
  # Add timestamp
  - timestamp:
      field: "@timestamp"
      layouts:
        - '2006-01-02T15:04:05.000Z'
        - '2006-01-02T15:04:05Z'
      test:
        - '2023-01-01T12:00:00.000Z'
        
  # Add host information
  - add_host_metadata:
      when.not.contains.tags: forwarded
      
  # Drop empty events
  - drop_event:
      when:
        or:
          - equals:
              message: ""
          - equals:
              log: ""

# Fields to include in output
fields:
  logstash_format: "docker"
  environment: "development"
  platform: "turdparty"
fields_under_root: true
