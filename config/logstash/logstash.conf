input {
  # TCP input for direct application logs
  tcp {
    port => 5000
    codec => json_lines
    tags => ["tcp_logs"]
  }

  # Beats input for Filebeat (Docker container logs)
  beats {
    port => 5044
    tags => ["docker_logs"]
  }

  # File input for log files
  file {
    path => "/app/logs/*.log"
    start_position => "beginning"
    sincedb_path => "/dev/null"
    codec => "json"
    tags => ["file_logs"]
  }
}

filter {
  # Process Docker container logs from Filebeat
  if "docker_logs" in [tags] {
    # Parse Docker JSON log format
    if [message] {
      json {
        source => "message"
        target => "docker"
      }
    }

    # Extract container information and create ECS-compliant fields
    if [container][name] {
      mutate {
        add_field => { "service_name" => "%{[container][name]}" }
        add_field => { "container_id" => "%{[container][id]}" }
        add_field => { "container_image" => "%{[container][image][name]}" }
      }

      # Clean up service name (remove turdpartycollab_ prefix)
      mutate {
        gsub => [ "service_name", "turdpartycollab_", "" ]
      }

      # Create ECS service fields
      mutate {
        add_field => { "[service][name]" => "%{service_name}" }
        add_field => { "[service][type]" => "turdparty" }
        add_field => { "[service][version]" => "1.0.0" }
        add_field => { "[service][environment]" => "development" }
      }

      # Create ECS container fields
      mutate {
        add_field => { "[container][name]" => "%{[container][name]}" }
        add_field => { "[container][id]" => "%{container_id}" }
        add_field => { "[container][image][name]" => "%{container_image}" }
        add_field => { "[container][runtime]" => "docker" }
      }
    }

    # Use log field as main message for Docker logs
    if [log] {
      mutate {
        rename => { "log" => "message" }
      }
    }

    # Add comprehensive debugging context
    mutate {
      add_field => { "log_source" => "docker" }
      add_field => { "platform" => "turdparty" }
      add_field => { "[labels][platform]" => "turdparty" }
      add_field => { "[labels][log_source]" => "docker" }
      add_field => { "[labels][collection_method]" => "filebeat" }
    }

    # Parse structured logs from TurdParty applications
    if [message] =~ /^\{.*\}$/ {
      json {
        source => "message"
        target => "app_log"
        add_tag => ["structured_log"]
      }

      # Extract structured log fields into ECS format
      if [app_log][timestamp] {
        mutate {
          add_field => { "[@timestamp]" => "%{[app_log][timestamp]}" }
        }
      }

      if [app_log][level] {
        mutate {
          add_field => { "[log][level]" => "%{[app_log][level]}" }
        }
      }

      if [app_log][service] {
        mutate {
          add_field => { "[service][name]" => "%{[app_log][service]}" }
        }
      }

      if [app_log][logger] {
        mutate {
          add_field => { "[log][logger]" => "%{[app_log][logger]}" }
        }
      }

      if [app_log][correlation_id] {
        mutate {
          add_field => { "[trace][id]" => "%{[app_log][correlation_id]}" }
          add_field => { "[labels][correlation_id]" => "%{[app_log][correlation_id]}" }
        }
      }

      if [app_log][module] {
        mutate {
          add_field => { "[labels][module]" => "%{[app_log][module]}" }
        }
      }

      if [app_log][function] {
        mutate {
          add_field => { "[labels][function]" => "%{[app_log][function]}" }
        }
      }

      if [app_log][line] {
        mutate {
          add_field => { "[labels][line_number]" => "%{[app_log][line]}" }
        }
      }

      # Use structured message if available
      if [app_log][message] {
        mutate {
          update => { "message" => "%{[app_log][message]}" }
        }
      }
    }
  }

  # Parse timestamp with multiple formats
  if [timestamp] {
    date {
      match => [ "timestamp", "ISO8601", "yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS'Z'", "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'" ]
      target => "@timestamp"
    }
  }

  # Parse @timestamp from Filebeat
  if [@timestamp] {
    date {
      match => [ "@timestamp", "ISO8601" ]
      target => "@timestamp"
    }
  }

  # Add service identification for non-Docker logs with ECS compliance
  if ![service_name] {
    mutate {
      add_field => { "service_name" => "turdparty" }
      add_field => { "[service][name]" => "turdparty" }
      add_field => { "[service][type]" => "application" }
    }
  }

  # Add ECS event fields based on log content
  mutate {
    add_field => { "[event][dataset]" => "turdparty.%{service_name}" }
    add_field => { "[event][module]" => "turdparty" }
    add_field => { "[event][created]" => "%{@timestamp}" }
  }

  # Determine event category and type based on service and content
  if [service_name] == "api" {
    mutate {
      add_field => { "[event][category]" => ["application"] }
      add_field => { "[event][type]" => ["info"] }
    }

    # WebSocket-specific categorization
    if [message] =~ /WebSocket/ {
      mutate {
        update => { "[event][category]" => ["network", "application"] }
        update => { "[event][type]" => ["connection", "protocol"] }
        add_field => { "[labels][protocol]" => "websocket" }
      }
    }

    # Route registration categorization
    if [message] =~ /(Registering|Registered|Failed to register)/ {
      mutate {
        update => { "[event][category]" => ["configuration", "application"] }
        update => { "[event][type]" => ["change", "info"] }
        add_field => { "[labels][operation]" => "route_registration" }
      }
    }
  } else if [service_name] == "database" {
    mutate {
      add_field => { "[event][category]" => ["database"] }
      add_field => { "[event][type]" => ["info"] }
    }
  } else if [service_name] == "elasticsearch" {
    mutate {
      add_field => { "[event][category]" => ["database"] }
      add_field => { "[event][type]" => ["info"] }
    }
  } else if [service_name] =~ /^(frontend|docs|status)$/ {
    mutate {
      add_field => { "[event][category]" => ["web"] }
      add_field => { "[event][type]" => ["access"] }
    }
  } else if [service_name] =~ /^(celery-worker|celery-beat|vm-monitor)$/ {
    mutate {
      add_field => { "[event][category]" => ["process"] }
      add_field => { "[event][type]" => ["info"] }
    }
  } else {
    mutate {
      add_field => { "[event][category]" => ["application"] }
      add_field => { "[event][type]" => ["info"] }
    }
  }

  # Add host information in ECS format
  mutate {
    add_field => { "[host][name]" => "%{[beat][hostname]}" }
    add_field => { "[host][hostname]" => "%{[beat][hostname]}" }
  }

  # Add comprehensive debugging labels
  mutate {
    add_field => { "[labels][environment]" => "development" }
    add_field => { "[labels][platform]" => "turdparty" }
    add_field => { "[labels][log_processor]" => "logstash" }
    add_field => { "[labels][ecs_version]" => "8.11.0" }
  }
  
  # Clean up fields
  mutate {
    remove_field => [ "host", "path", "tags" ]
  }
}

output {
  # Output to Elasticsearch with ECS-compliant indexing
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    
    # ECS-compliant dynamic indexing with service and date
    index => "ecs-turdparty-%{[service][name]:unknown}-%{+YYYY.MM.dd}"
    
    # Disable template management for now to get basic functionality working
    manage_template => false
  }
  
  # Debug output to stdout (can be removed in production)
  stdout {
    codec => rubydebug
  }
}
