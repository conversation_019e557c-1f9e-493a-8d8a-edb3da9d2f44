# Ruff configuration for TurdParty
# Ensures PEP8, PEP257 (docstrings), and PEP484 (type hints) compliance

[tool.ruff]
# Python version
target-version = "py310"

# Line length (PEP8 recommends 79, but 88 is common with black)
line-length = 88

# Enable auto-fixing
fix = true

# Directories to include
include = ["*.py", "*.pyi"]

# Directories to exclude
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "migrations",
]

[tool.ruff.lint]
# Enable rule categories
select = [
    # Pycodestyle (PEP8)
    "E",  # Error
    "W",  # Warning
    
    # Pyflakes
    "F",  # Pyflakes
    
    # pydocstyle (PEP257 - docstring conventions)
    "D",  # pydocstyle
    
    # pyupgrade
    "UP", # pyupgrade
    
    # flake8-bugbear
    "B",  # flake8-bugbear
    
    # flake8-simplify
    "SIM", # flake8-simplify
    
    # isort
    "I",  # isort
    
    # flake8-comprehensions
    "C4", # flake8-comprehensions
    
    # flake8-pie
    "PIE", # flake8-pie
    
    # flake8-type-checking
    "TCH", # flake8-type-checking
    
    # flake8-annotations (PEP484 - type hints)
    "ANN", # flake8-annotations
    
    # flake8-async
    "ASYNC", # flake8-async
    
    # flake8-bandit (security)
    "S", # flake8-bandit
    
    # flake8-logging-format
    "G", # flake8-logging-format
    
    # Pylint
    "PL", # Pylint
    
    # Ruff-specific
    "RUF", # Ruff-specific rules
]

# Disable specific rules that may be too strict or conflict
ignore = [
    # pydocstyle
    "D100", # Missing docstring in public module
    "D104", # Missing docstring in public package
    "D107", # Missing docstring in __init__
    "D203", # 1 blank line required before class docstring (conflicts with D211)
    "D213", # Multi-line docstring summary should start at the second line (conflicts with D212)
    
    # flake8-annotations
    "ANN101", # Missing type annotation for self in method
    "ANN102", # Missing type annotation for cls in classmethod
    "ANN401", # Dynamically typed expressions (Any) are disallowed
    
    # Pylint
    "PLR0913", # Too many arguments to function call
    "PLR2004", # Magic value used in comparison
    
    # Others
    "E501", # Line too long (handled by formatter)
    "S101", # Use of assert detected (common in tests)
    "S311", # Standard pseudo-random generators are not suitable for cryptographic purposes
]

# Allow unused variables when underscore-prefixed
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

# Allow autofix for all enabled rules
fixable = ["ALL"]
unfixable = []

[tool.ruff.lint.per-file-ignores]
# Tests can use magic values, assertions, and don't need full docstrings
"tests/**/*.py" = [
    "D",      # pydocstyle
    "S101",   # assert
    "PLR2004", # Magic values
    "ANN",    # Type annotations
]

# __init__.py files don't need docstrings for imports
"__init__.py" = ["D104", "F401"]

# Scripts and utilities can be more relaxed
"scripts/**/*.py" = ["D", "ANN"]

[tool.ruff.lint.pydocstyle]
# Use Google docstring convention
convention = "google"

[tool.ruff.lint.isort]
# Import sorting configuration
known-first-party = ["api", "tests"]
force-single-line = false
force-sort-within-sections = true
single-line-exclusions = ["typing"]

[tool.ruff.lint.flake8-type-checking]
# Type checking configuration
strict = true

[tool.ruff.lint.pylint]
# Pylint configuration
max-args = 10
max-branches = 15
max-returns = 8
max-statements = 60

[tool.ruff.format]
# Formatter configuration (replaces black)
quote-style = "double"
indent-style = "space"
skip-source-first-line = false
line-ending = "auto"

# Respect magic trailing comma
docstring-code-format = true
docstring-code-line-length = 60
