# 🛠️ Development Tools and Resources

This directory contains development-specific tools, scripts, and debug resources for the TurdParty platform.

## 📁 Directory Structure

### `debug/`
Contains debugging and testing HTML/JS files:
- `debug-tabs.html` - Tab debugging interface
- `test-debug-js.js` - JavaScript debugging utilities
- `test-js-error-logging.html` - Error logging test page

### `scripts/`
Development helper scripts:
- `dev.sh` - Main development helper script with various commands

### `examples/`
Example code and configurations for development reference.

### `tools/`
Development tools and utilities.

## 🚀 Usage

### Development Script
```bash
# Run the main development helper
./development/scripts/dev.sh --help

# Common development tasks
./development/scripts/dev.sh start    # Start development environment
./development/scripts/dev.sh test     # Run tests
./development/scripts/dev.sh clean    # Clean temporary files
```

### Debug Tools
The debug files can be opened in a browser for testing and debugging:
- Open `development/debug/debug-tabs.html` for tab interface testing
- Use `development/debug/test-js-error-logging.html` for error logging tests

## 📝 Notes

These files were moved from the root directory during the project cleanup to maintain a cleaner root structure while keeping development tools easily accessible.

For main project documentation, see the `docs/` directory.
