const { test, expect } = require('@playwright/test');

test('Debug JavaScript execution', async ({ page }) => {
    // Capture console logs
    const consoleLogs = [];
    page.on('console', msg => {
        consoleLogs.push(`${msg.type()}: ${msg.text()}`);
    });

    // Navigate to the status page
    await page.goto('http://status.turdparty.localhost');
    
    // Wait for the page to load
    await page.waitForTimeout(3000);
    
    // Print all console logs
    console.log('=== Console Logs ===');
    consoleLogs.forEach(log => console.log(log));
    
    // Check if the page loaded
    await expect(page).toHaveTitle(/💩🎉 TurdParty 💩🎉 System Status/);
    
    // Check if tab buttons exist
    const tabButtons = await page.locator('.tab-button').count();
    console.log(`Found ${tabButtons} tab buttons`);
    
    // Check if tab contents exist
    const tabContents = await page.locator('.tab-content').count();
    console.log(`Found ${tabContents} tab contents`);
    
    // Try clicking a tab button
    if (tabButtons > 0) {
        console.log('Clicking Services tab...');
        await page.locator('.tab-button[data-tab="services"]').click();
        await page.waitForTimeout(1000);
        
        // Check if the tab became active
        const isActive = await page.locator('.tab-button[data-tab="services"]').getAttribute('class');
        console.log(`Services tab class after click: ${isActive}`);
    }
});
