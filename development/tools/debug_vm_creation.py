#!/usr/bin/env python3
"""
Debug VM Creation Issues

This script helps debug the VM creation problems by testing simpler configurations
and providing detailed error information.
"""

import subprocess
import tempfile
from pathlib import Path
import time


def check_vagrant_boxes():
    """Check available Vagrant boxes."""
    print("🔍 Checking available Vagrant boxes...")
    try:
        result = subprocess.run(['vagrant', 'box', 'list'], capture_output=True, text=True)
        if result.returncode == 0:
            print("Available boxes:")
            print(result.stdout)
        else:
            print("No boxes found or error:", result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"Error checking boxes: {e}")
        return False


def check_virtualbox_info():
    """Check VirtualBox system info."""
    print("\n🔍 Checking VirtualBox system info...")
    try:
        result = subprocess.run(['VBoxManage', 'list', 'systemproperties'], capture_output=True, text=True)
        if result.returncode == 0:
            print("VirtualBox system properties:")
            for line in result.stdout.split('\n'):
                if any(keyword in line.lower() for keyword in ['memory', 'cpu', 'acceleration', 'nested']):
                    print(f"  {line}")
        return True
    except Exception as e:
        print(f"Error checking VirtualBox info: {e}")
        return False


def test_simple_vagrantfile():
    """Test with a very simple Vagrantfile."""
    print("\n🧪 Testing simple Vagrantfile...")
    
    simple_vagrantfile = '''
Vagrant.configure("2") do |config|
  config.vm.box = "ubuntu/focal64"
  config.vm.hostname = "test-simple"
  
  # Minimal configuration
  config.vm.synced_folder ".", "/vagrant", disabled: true
  
  config.vm.provider "virtualbox" do |vb|
    vb.memory = 1024
    vb.cpus = 1
    vb.gui = false
    vb.name = "test-simple-vm"
    
    # Try to fix common VirtualBox issues
    vb.customize ["modifyvm", :id, "--natdnshostresolver1", "on"]
    vb.customize ["modifyvm", :id, "--natdnsproxy1", "on"]
    vb.customize ["modifyvm", :id, "--nictype1", "virtio"]
  end
end
'''
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        vagrantfile_path = temp_path / "Vagrantfile"
        
        with open(vagrantfile_path, 'w') as f:
            f.write(simple_vagrantfile)
        
        print(f"Created Vagrantfile in: {temp_path}")
        print("Vagrantfile content:")
        print(simple_vagrantfile)
        
        # Try to validate the Vagrantfile
        print("\n🔍 Validating Vagrantfile...")
        try:
            result = subprocess.run(
                ['vagrant', 'validate'],
                cwd=temp_path,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                print("✅ Vagrantfile is valid")
            else:
                print(f"❌ Vagrantfile validation failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error validating Vagrantfile: {e}")
            return False
        
        # Try to start the VM with detailed output
        print("\n🚀 Attempting to start VM...")
        try:
            result = subprocess.run(
                ['vagrant', 'up', '--debug'],
                cwd=temp_path,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes
            )
            
            if result.returncode == 0:
                print("✅ VM started successfully!")
                
                # Try to get VM status
                status_result = subprocess.run(
                    ['vagrant', 'status'],
                    cwd=temp_path,
                    capture_output=True,
                    text=True
                )
                print("VM Status:")
                print(status_result.stdout)
                
                # Cleanup
                print("🧹 Cleaning up VM...")
                subprocess.run(['vagrant', 'destroy', '-f'], cwd=temp_path)
                return True
            else:
                print(f"❌ VM failed to start:")
                print("STDOUT:", result.stdout[-2000:])  # Last 2000 chars
                print("STDERR:", result.stderr[-2000:])  # Last 2000 chars
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ VM startup timed out after 5 minutes")
            # Try to cleanup
            subprocess.run(['vagrant', 'destroy', '-f'], cwd=temp_path, capture_output=True)
            return False
        except Exception as e:
            print(f"❌ Error starting VM: {e}")
            return False


def check_host_virtualization():
    """Check if host supports virtualization."""
    print("\n🔍 Checking host virtualization support...")
    
    # Check if VT-x/AMD-V is enabled
    try:
        with open('/proc/cpuinfo', 'r') as f:
            cpuinfo = f.read()
            
        if 'vmx' in cpuinfo or 'svm' in cpuinfo:
            print("✅ Hardware virtualization supported")
        else:
            print("❌ Hardware virtualization not detected")
            
        # Check if KVM is available (might conflict with VirtualBox)
        kvm_check = subprocess.run(['ls', '/dev/kvm'], capture_output=True)
        if kvm_check.returncode == 0:
            print("⚠️ KVM detected - might conflict with VirtualBox")
        else:
            print("✅ No KVM conflict detected")
            
    except Exception as e:
        print(f"Could not check virtualization: {e}")


def download_ubuntu_box():
    """Download Ubuntu box if not available."""
    print("\n📦 Checking/downloading Ubuntu box...")
    try:
        # Check if box exists
        result = subprocess.run(['vagrant', 'box', 'list'], capture_output=True, text=True)
        if 'ubuntu/focal64' in result.stdout:
            print("✅ ubuntu/focal64 box already available")
            return True
        
        print("📥 Downloading ubuntu/focal64 box...")
        result = subprocess.run(
            ['vagrant', 'box', 'add', 'ubuntu/focal64', '--provider', 'virtualbox'],
            capture_output=True,
            text=True,
            timeout=1800  # 30 minutes
        )
        
        if result.returncode == 0:
            print("✅ Ubuntu box downloaded successfully")
            return True
        else:
            print(f"❌ Failed to download box: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Box download timed out")
        return False
    except Exception as e:
        print(f"❌ Error downloading box: {e}")
        return False


def main():
    """Run all diagnostic tests."""
    print("🔧 TurdParty VM Creation Diagnostics")
    print("=" * 50)
    
    tests = [
        ("Host Virtualization", check_host_virtualization),
        ("VirtualBox Info", check_virtualbox_info),
        ("Vagrant Boxes", check_vagrant_boxes),
        ("Download Ubuntu Box", download_ubuntu_box),
        ("Simple VM Test", test_simple_vagrantfile),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Diagnostic Results Summary")
    print("=" * 50)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All diagnostics passed! VM creation should work.")
    else:
        print("⚠️ Some diagnostics failed. Check the issues above.")


if __name__ == "__main__":
    main()
