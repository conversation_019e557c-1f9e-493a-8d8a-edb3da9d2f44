# Docker Compose configuration for productive testing infrastructure
# This replaces mocked services with real containerized services for testing

version: '3.8'

services:
  # Test Database - PostgreSQL
  test-postgres:
    image: postgres:15-alpine
    container_name: turdparty_test_postgres
    environment:
      POSTGRES_DB: turdparty_test
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_pass
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    ports:
      - "5433:5432"
    volumes:
      - test_postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d turdparty_test"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - test_network

  # Test Cache - Redis
  test-redis:
    image: redis:7-alpine
    container_name: turdparty_test_redis
    ports:
      - "6380:6379"
    volumes:
      - test_redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    networks:
      - test_network

  # Test Storage - MinIO
  test-minio:
    image: minio/minio:latest
    container_name: turdparty_test_minio
    environment:
      MINIO_ROOT_USER: test_access_key
      MINIO_ROOT_PASSWORD: test_secret_key
    ports:
      - "9001:9000"
      - "9002:9001"  # Console
    volumes:
      - test_minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - test_network

  # Test Search - Elasticsearch
  test-elasticsearch:
    image: elasticsearch:8.11.0
    container_name: turdparty_test_elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - bootstrap.memory_lock=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - "9201:9200"
      - "9301:9300"
    volumes:
      - test_elasticsearch_data:/usr/share/elasticsearch/data
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - test_network

  # Test Log Processing - Logstash
  test-logstash:
    image: logstash:8.11.0
    container_name: turdparty_test_logstash
    environment:
      - "LS_JAVA_OPTS=-Xmx256m -Xms256m"
    ports:
      - "5001:5000"
      - "9601:9600"
    volumes:
      - ./tests/config/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
    depends_on:
      test-elasticsearch:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9600 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - test_network

  # Test Message Broker - RabbitMQ (for Celery)
  test-rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: turdparty_test_rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: test_user
      RABBITMQ_DEFAULT_PASS: test_pass
    ports:
      - "5673:5672"
      - "15673:15672"  # Management UI
    volumes:
      - test_rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - test_network

  # Test Worker - Celery Worker
  test-celery-worker:
    build:
      context: .
      dockerfile: services/workers/Dockerfile.celery
    container_name: turdparty_test_celery_worker
    environment:
      - CELERY_BROKER_URL=amqp://test_user:test_pass@test-rabbitmq:5672//
      - CELERY_RESULT_BACKEND=redis://test-redis:6379/0
      - DATABASE_URL=***************************************************/turdparty_test
      - MINIO_ENDPOINT=test-minio:9000
      - MINIO_ACCESS_KEY=test_access_key
      - MINIO_SECRET_KEY=test_secret_key
      - REDIS_URL=redis://test-redis:6379/0
    depends_on:
      test-postgres:
        condition: service_healthy
      test-redis:
        condition: service_healthy
      test-rabbitmq:
        condition: service_healthy
      test-minio:
        condition: service_healthy
    volumes:
      - ./services/workers:/app
      - test_worker_data:/app/data
    command: celery -A celery_app worker --loglevel=info --concurrency=2
    networks:
      - test_network

  # Test API Service
  test-api:
    build:
      context: .
      dockerfile: services/api/Dockerfile
    container_name: turdparty_test_api
    environment:
      - DATABASE_URL=***************************************************/turdparty_test
      - REDIS_URL=redis://test-redis:6379/0
      - MINIO_ENDPOINT=test-minio:9000
      - MINIO_ACCESS_KEY=test_access_key
      - MINIO_SECRET_KEY=test_secret_key
      - ELASTICSEARCH_HOST=test-elasticsearch
      - ELASTICSEARCH_PORT=9200
      - CELERY_BROKER_URL=amqp://test_user:test_pass@test-rabbitmq:5672//
      - CELERY_RESULT_BACKEND=redis://test-redis:6379/0
    ports:
      - "8001:8000"
    depends_on:
      test-postgres:
        condition: service_healthy
      test-redis:
        condition: service_healthy
      test-minio:
        condition: service_healthy
      test-elasticsearch:
        condition: service_healthy
      test-rabbitmq:
        condition: service_healthy
    volumes:
      - ./api:/app
      - test_api_data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - test_network

  # Test Monitoring - Prometheus (optional)
  test-prometheus:
    image: prom/prometheus:latest
    container_name: turdparty_test_prometheus
    ports:
      - "9091:9090"
    volumes:
      - ./tests/config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - test_prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - test_network

volumes:
  test_postgres_data:
    driver: local
  test_redis_data:
    driver: local
  test_minio_data:
    driver: local
  test_elasticsearch_data:
    driver: local
  test_rabbitmq_data:
    driver: local
  test_worker_data:
    driver: local
  test_api_data:
    driver: local
  test_prometheus_data:
    driver: local

networks:
  test_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
