# TurdParty Sphinx Reports Platform Dockerfile
FROM python:3.10-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    pandoc \
    texlive-latex-base \
    texlive-latex-extra \
    texlive-fonts-recommended \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Create app user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app

# Copy requirements
COPY requirements.txt .

# Install Python dependencies
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir \
    sphinx \
    sphinx-rtd-theme \
    sphinxcontrib-mermaid \
    sphinx-copybutton \
    sphinx-tabs \
    sphinx-design \
    jinja2 \
    httpx \
    elasticsearch

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/docs/reports/_build /app/docs/reports/_static /app/docs/reports/_templates /app/logs && \
    chown -R appuser:appuser /app

# Switch to app user
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Start command
CMD ["python", "services/report_generator.py", "--serve", "--port", "8080"]
