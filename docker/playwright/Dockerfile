# Playwright E2E Testing Container for TurdParty
FROM mcr.microsoft.com/playwright/python:v1.50.0-jammy

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV IN_DOCKER=true
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    jq \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements-playwright.txt /app/
RUN pip install --no-cache-dir -r requirements-playwright.txt

# Install Playwright browsers (they're already in the base image, but ensure they're available)
RUN playwright install --with-deps

# Copy test files and configuration
COPY tests/ /app/tests/
COPY pytest.ini /app/
COPY config/ /app/config/
COPY utils/ /app/utils/

# Copy any necessary source files for imports
COPY services/api/src/ /app/services/api/src/
COPY api/ /app/api/

# Create necessary directories
RUN mkdir -p /app/test-results /app/logs /app/reports

# Set permissions
RUN chmod -R 755 /app/tests

# Copy health server script
COPY docker/playwright/health_server.py /app/

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Expose port for health endpoint and test reports
EXPOSE 8080

# Default command - start health server and run tests
CMD ["sh", "-c", "python health_server.py & python -m pytest tests/e2e/ -v --tb=short --html=reports/e2e-report.html --self-contained-html"]
