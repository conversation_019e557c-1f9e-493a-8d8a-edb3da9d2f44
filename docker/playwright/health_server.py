#!/usr/bin/env python3
"""
Health Server for Playwright E2E Testing Container.

Provides health endpoint and test report serving for Traefik integration.
"""

import json
import os
import time
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from pathlib import Path
from threading import Thread


class PlaywrightHealthHandler(BaseHTTPRequestHandler):
    """HTTP handler for Playwright health and test reports."""
    
    def do_GET(self):
        """Handle GET requests."""
        if self.path == "/health":
            self.send_health_response()
        elif self.path == "/":
            self.send_dashboard_response()
        elif self.path.startswith("/reports/"):
            self.serve_report_file()
        elif self.path.startswith("/test-results/"):
            self.serve_test_results()
        else:
            self.send_404()
    
    def send_health_response(self):
        """Send health check response."""
        try:
            # Check if Playwright is available
            from playwright.sync_api import sync_playwright
            
            health_data = {
                "status": "healthy",
                "service": "turdparty-playwright",
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "version": "1.0.0",
                "playwright_available": True,
                "environment": os.getenv("IN_DOCKER", "false"),
                "reports_available": Path("/app/reports").exists(),
                "test_results_available": Path("/app/test-results").exists()
            }
            
            self.send_response(200)
            self.send_header("Content-Type", "application/json")
            self.send_header("Access-Control-Allow-Origin", "*")
            self.end_headers()
            self.wfile.write(json.dumps(health_data, indent=2).encode())
            
        except Exception as e:
            error_data = {
                "status": "unhealthy",
                "service": "turdparty-playwright",
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "error": str(e),
                "playwright_available": False
            }
            
            self.send_response(503)
            self.send_header("Content-Type", "application/json")
            self.send_header("Access-Control-Allow-Origin", "*")
            self.end_headers()
            self.wfile.write(json.dumps(error_data, indent=2).encode())
    
    def send_dashboard_response(self):
        """Send dashboard HTML response."""
        html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>💩🎉TurdParty🎉💩 - Playwright E2E Testing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #1a1a1a; color: #fff; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 40px; }
        .status { background: #2d2d2d; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .healthy { border-left: 4px solid #4CAF50; }
        .links { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
        .link-card { background: #2d2d2d; padding: 20px; border-radius: 8px; text-align: center; }
        .link-card a { color: #4CAF50; text-decoration: none; font-weight: bold; }
        .link-card a:hover { color: #66BB6A; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💩🎉TurdParty🎉💩</h1>
            <h2>Playwright E2E Testing Service</h2>
        </div>
        
        <div class="status healthy">
            <h3>🎭 Service Status</h3>
            <p><strong>Status:</strong> <span style="color: #4CAF50;">Healthy</span></p>
            <p><strong>Service:</strong> Playwright E2E Testing</p>
            <p><strong>Environment:</strong> Docker Container</p>
            <p><strong>Last Check:</strong> <span id="timestamp"></span></p>
        </div>
        
        <div class="links">
            <div class="link-card">
                <h4>🏥 Health Check</h4>
                <a href="/health">View Health Status</a>
            </div>
            <div class="link-card">
                <h4>📊 Test Reports</h4>
                <a href="/reports/">Browse Reports</a>
            </div>
            <div class="link-card">
                <h4>🧪 Test Results</h4>
                <a href="/test-results/">View Results</a>
            </div>
            <div class="link-card">
                <h4>🔗 Frontend</h4>
                <a href="http://frontend.turdparty.localhost">Open Frontend</a>
            </div>
        </div>
    </div>
    
    <script>
        document.getElementById('timestamp').textContent = new Date().toISOString();
        
        // Auto-refresh health status every 30 seconds
        setInterval(() => {
            document.getElementById('timestamp').textContent = new Date().toISOString();
        }, 30000);
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header("Content-Type", "text/html")
        self.send_header("Access-Control-Allow-Origin", "*")
        self.end_headers()
        self.wfile.write(html_content.encode())
    
    def serve_report_file(self):
        """Serve report files."""
        file_path = Path("/app") / self.path.lstrip("/")
        if file_path.exists() and file_path.is_file():
            self.send_response(200)
            if file_path.suffix == ".html":
                self.send_header("Content-Type", "text/html")
            elif file_path.suffix == ".json":
                self.send_header("Content-Type", "application/json")
            else:
                self.send_header("Content-Type", "application/octet-stream")
            self.send_header("Access-Control-Allow-Origin", "*")
            self.end_headers()
            with open(file_path, "rb") as f:
                self.wfile.write(f.read())
        else:
            self.send_404()
    
    def serve_test_results(self):
        """Serve test result files."""
        self.serve_report_file()  # Same logic as reports
    
    def send_404(self):
        """Send 404 response."""
        self.send_response(404)
        self.send_header("Content-Type", "text/plain")
        self.end_headers()
        self.wfile.write(b"404 Not Found")
    
    def log_message(self, format, *args):
        """Override to reduce log noise."""
        pass


def run_health_server():
    """Run the health server."""
    server = HTTPServer(("0.0.0.0", 8080), PlaywrightHealthHandler)
    print("🎭 Playwright Health Server starting on port 8080...")
    print("🏥 Health endpoint: http://localhost:8080/health")
    print("📊 Dashboard: http://localhost:8080/")
    server.serve_forever()


if __name__ == "__main__":
    # Run server in background thread
    server_thread = Thread(target=run_health_server, daemon=True)
    server_thread.start()
    
    print("🎭 Playwright E2E Testing Service Ready!")
    print("🌐 Access via: http://playwright.turdparty.localhost")
    
    # Keep the main thread alive
    try:
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        print("🛑 Shutting down Playwright service...")
