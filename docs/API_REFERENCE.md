# 💩🎉TurdParty🎉💩 API Reference

## Overview

TurdParty provides a comprehensive REST and WebSocket API for malware analysis and VM management. The API is built with FastAPI and provides both synchronous REST endpoints and real-time WebSocket connections for live monitoring and interaction.

### Key Features
- **REST API**: Complete CRUD operations for files, VMs, and workflows
- **WebSocket API**: Real-time metrics, command execution, and file uploads
- **ECS Logging**: Comprehensive observability with Elasticsearch integration
- **Traefik Integration**: Automatic service discovery and load balancing
- **Hot Reloading**: Development environment with live code updates
- **Volume Mounts**: Live code synchronization for development

## Base URLs

### REST API
- **Development**: `http://api.turdparty.localhost`
- **Production**: `https://api.turdparty.localhost`
- **Direct Access**: `http://localhost:8000` (development only)

### WebSocket API
- **Development**: `ws://api.turdparty.localhost`
- **Production**: `wss://api.turdparty.localhost`
- **Direct Access**: `ws://localhost:8000` (development only)

## Authentication

TurdParty uses Traefik-based authentication with automatic user management:
- **Reverse Proxy Authentication**: All requests authenticated through Traefik
- **Service Discovery**: Automatic registration with `servicename.turdparty.localhost`
- **SSL Termination**: HTTPS/WSS handled by Traefik in production
- **Development Mode**: Direct access available for testing

## API Versioning

All API endpoints are versioned using URL path versioning:
- **Current Version**: `v1`
- **Base Path**: `/api/v1/`
- **WebSocket Path**: `/api/v1/vms/{vm_id}/`

## 📋 VM Management API

### Get VM Templates

Retrieve all available VM templates with descriptions and compatibility information.

**Endpoint:** `GET /api/v1/vms/templates`

**Response:**
```json
[
  {
    "value": "ubuntu:20.04",
    "name": "DOCKER_UBUNTU_2004",
    "description": "Docker Ubuntu 20.04 - Containerized Ubuntu",
    "compatible_vm_types": ["docker"],
    "recommended": true
  },
  {
    "value": "ubuntu/focal64",
    "name": "UBUNTU_2004",
    "description": "Ubuntu 20.04 LTS (Focal Fossa) - Recommended for most workloads",
    "compatible_vm_types": ["vagrant", "docker"],
    "recommended": true
  }
]
```

### Create VM

Create a new VM instance with specified configuration.

**Endpoint:** `POST /api/v1/vms/`

**Request Body:**
```json
{
  "name": "analysis-vm-001",
  "template": "ubuntu:20.04",
  "vm_type": "docker",
  "memory_mb": 1024,
  "cpus": 2,
  "disk_gb": 20,
  "domain": "TurdParty",
  "description": "Malware analysis environment",
  "auto_start": true,
  "provision_script": "#!/bin/bash\necho 'VM provisioned'"
}
```

**Response:**
```json
{
  "vm_id": "123e4567-e89b-12d3-a456-************",
  "name": "analysis-vm-001",
  "template": "ubuntu:20.04",
  "vm_type": "docker",
  "memory_mb": 1024,
  "cpus": 2,
  "disk_gb": 20,
  "status": "creating",
  "domain": "TurdParty",
  "runtime_minutes": 0.0,
  "is_expired": false,
  "created_at": "2025-01-10T10:00:00Z",
  "description": "Malware analysis environment"
}
```

**Validation Rules:**
- `name`: Must be unique, alphanumeric with hyphens
- `template`: Must be from available templates
- `vm_type`: Either "docker" or "vagrant"
- `memory_mb`: 256-8192 MB
- `cpus`: 1-8 cores
- `disk_gb`: 5-100 GB
- `domain`: Must be "TurdParty"

### List VMs

Retrieve paginated list of VMs with optional filtering.

**Endpoint:** `GET /api/v1/vms/`

**Query Parameters:**
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum records to return (default: 100, max: 1000)
- `status`: Filter by VM status (optional)

**Example:** `GET /api/v1/vms/?skip=0&limit=10&status=running`

**Response:**
```json
{
  "vms": [
    {
      "vm_id": "123e4567-e89b-12d3-a456-************",
      "name": "analysis-vm-001",
      "template": "ubuntu:20.04",
      "vm_type": "docker",
      "status": "running",
      "runtime_minutes": 15.5,
      "is_expired": false,
      "created_at": "2025-01-10T10:00:00Z"
    }
  ],
  "total": 42,
  "skip": 0,
  "limit": 10
}
```

### Get VM Details

Retrieve detailed information about a specific VM.

**Endpoint:** `GET /api/v1/vms/{vm_id}`

**Response:**
```json
{
  "vm_id": "123e4567-e89b-12d3-a456-************",
  "name": "analysis-vm-001",
  "template": "ubuntu:20.04",
  "vm_type": "docker",
  "memory_mb": 1024,
  "cpus": 2,
  "disk_gb": 20,
  "status": "running",
  "domain": "TurdParty",
  "ip_address": "**********",
  "ssh_port": null,
  "runtime_minutes": 15.5,
  "is_expired": false,
  "created_at": "2025-01-10T10:00:00Z",
  "started_at": "2025-01-10T10:01:00Z",
  "terminated_at": null,
  "description": "Malware analysis environment",
  "error_message": null
}
```

### VM Actions

Perform actions on a VM instance using the unified action endpoint.

**Endpoint:** `POST /api/v1/vms/{vm_id}/action`

**Request Body:**
```json
{
  "action": "start",
  "force": false
}
```

**Available Actions:**
- `start`: Start the VM
- `stop`: Stop the VM gracefully
- `restart`: Restart the VM
- `destroy`: Permanently destroy the VM
- `suspend`: Suspend the VM (Vagrant only)
- `resume`: Resume suspended VM (Vagrant only)

**Response:**
```json
{
  "vm_id": "123e4567-e89b-12d3-a456-************",
  "name": "analysis-vm-001",
  "action": "start",
  "status": "running",
  "task_id": "celery-task-uuid",
  "force": false,
  "message": "VM start queued"
}
```

### Delete VM

Delete a VM instance and clean up all resources.

**Endpoint:** `DELETE /api/v1/vms/{vm_id}`

**Query Parameters:**
- `force`: Force deletion even if VM is running (default: false)

**Example:** `DELETE /api/v1/vms/{vm_id}?force=true`

**Response:**
```json
{
  "vm_id": "123e4567-e89b-12d3-a456-************",
  "message": "VM deletion queued",
  "task_id": "celery-task-uuid"
}
```

## 🔌 WebSocket API

TurdParty provides real-time WebSocket connections for live VM monitoring, command execution, and file operations. All WebSocket endpoints follow the same authentication and routing patterns as REST endpoints.

### WebSocket Connection Lifecycle

1. **Connection Establishment**: Client initiates WebSocket handshake
2. **Authentication**: Traefik validates request (production)
3. **Connection Registration**: Server registers client in connection manager
4. **Message Exchange**: Bidirectional communication begins
5. **Graceful Disconnection**: Client or server closes connection

### WebSocket Endpoints Overview

| Endpoint | Purpose | Message Types |
|----------|---------|---------------|
| `/api/v1/vms/{vm_id}/metrics/stream` | Real-time VM metrics | `metrics`, `connection_established` |
| `/api/v1/vms/{vm_id}/commands/execute` | Interactive command execution | `command`, `stdout`, `stderr`, `exit` |
| `/api/v1/vms/{vm_id}/files/upload` | Progressive file upload | `upload_ready`, `chunk`, `progress`, `complete` |

### VM Metrics Stream

Real-time streaming of VM performance metrics and status updates.

**Endpoint:** `ws://localhost:8000/api/v1/vms/{vm_id}/metrics/stream`

**Query Parameters:**
- `vm_type`: VM type (docker/vagrant) for optimized metrics collection

**Connection Message:**
```json
{
  "type": "connection_established",
  "vm_id": "123e4567-e89b-12d3-a456-************",
  "connection_id": "conn-uuid",
  "timestamp": "2025-06-14T10:00:00Z"
}
```

**Metrics Message:**
```json
{
  "type": "metrics",
  "vm_id": "123e4567-e89b-12d3-a456-************",
  "timestamp": "2025-06-14T10:00:01Z",
  "cpu_percent": 15.2,
  "memory_percent": 25.6,
  "memory_usage_mb": 256,
  "memory_limit_mb": 1024,
  "network_rx_bytes": 1024000,
  "network_tx_bytes": 512000,
  "disk_usage_gb": 2.5,
  "processes": 42,
  "uptime_seconds": 930,
  "status": "running"
}
```

**Error Message:**
```json
{
  "type": "error",
  "error": "VM not found",
  "error_code": "VM_NOT_FOUND",
  "timestamp": "2025-06-14T10:00:00Z"
}
```

### Command Execution

Interactive command execution with real-time output streaming.

**Endpoint:** `ws://localhost:8000/api/v1/vms/{vm_id}/commands/execute`

**Command Message (Client → Server):**
```json
{
  "type": "command",
  "command": "ls -la /tmp",
  "timeout": 30,
  "working_directory": "/tmp"
}
```

**STDOUT Message (Server → Client):**
```json
{
  "type": "stdout",
  "command": "ls -la /tmp",
  "output": "total 8\ndrwxrwxrwt 2 root root 4096 Jun 14 10:00 .\n",
  "timestamp": "2025-06-14T10:00:01Z"
}
```

**STDERR Message (Server → Client):**
```json
{
  "type": "stderr",
  "command": "ls -la /tmp",
  "output": "ls: cannot access '/nonexistent': No such file or directory\n",
  "timestamp": "2025-06-14T10:00:01Z"
}
```

**Exit Message (Server → Client):**
```json
{
  "type": "exit",
  "command": "ls -la /tmp",
  "exit_code": 0,
  "duration_ms": 150,
  "timestamp": "2025-06-14T10:00:01Z"
}
```

### File Upload Progress

Progressive file upload with real-time progress updates.

**Endpoint:** `ws://localhost:8000/api/v1/vms/{vm_id}/files/upload`

**Upload Ready Message (Server → Client):**
```json
{
  "type": "upload_ready",
  "vm_id": "123e4567-e89b-12d3-a456-************",
  "max_chunk_size": 65536,
  "supported_formats": ["binary", "base64"],
  "timestamp": "2025-06-14T10:00:00Z"
}
```

**Chunk Message (Client → Server):**
```json
{
  "type": "chunk",
  "filename": "malware.exe",
  "chunk_index": 0,
  "total_chunks": 10,
  "data": "base64-encoded-chunk-data",
  "checksum": "blake3-hash"
}
```

**Progress Message (Server → Client):**
```json
{
  "type": "progress",
  "filename": "malware.exe",
  "bytes_received": 65536,
  "total_bytes": 655360,
  "progress_percent": 10.0,
  "chunks_received": 1,
  "total_chunks": 10,
  "timestamp": "2025-06-14T10:00:01Z"
}
```

**Complete Message (Server → Client):**
```json
{
  "type": "complete",
  "filename": "malware.exe",
  "file_path": "/tmp/malware.exe",
  "total_bytes": 655360,
  "blake3_hash": "final-file-hash",
  "upload_duration_ms": 5000,
  "timestamp": "2025-06-14T10:00:05Z"
}
```

### WebSocket Error Handling

All WebSocket endpoints implement consistent error handling:

**Connection Errors:**
- `403 Forbidden`: Authentication failed or VM access denied
- `404 Not Found`: VM not found or endpoint not available
- `500 Internal Server Error`: Server-side error during connection

**Message Errors:**
```json
{
  "type": "error",
  "error": "Invalid message format",
  "error_code": "INVALID_MESSAGE",
  "details": {
    "expected_fields": ["type", "command"],
    "received_fields": ["type"]
  },
  "timestamp": "2025-06-14T10:00:00Z"
}
```

### Connection Management

TurdParty uses a centralized connection manager for WebSocket connections:

- **Connection Pooling**: Efficient resource management
- **Automatic Cleanup**: Connections cleaned up on VM termination
- **Heartbeat Monitoring**: Automatic detection of stale connections
- **Graceful Shutdown**: Proper connection closure on service restart

## 📁 File Management API

### Upload File

Upload a file to MinIO storage for analysis.

**Endpoint:** `POST /api/v1/files/upload`

**Request:** Multipart form data
- `file`: File to upload
- `description`: Optional file description

**Response:**
```json
{
  "file_id": "uuid",
  "filename": "malware.exe",
  "size": 1024000,
  "content_type": "application/octet-stream",
  "blake3_hash": "hash_value",
  "uploaded_at": "2025-01-10T10:00:00Z"
}
```

### List Files

Retrieve paginated list of uploaded files.

**Endpoint:** `GET /api/v1/files/`

**Query Parameters:**
- `skip`: Number of records to skip
- `limit`: Maximum records to return

### Get File Details

Retrieve metadata about a specific file.

**Endpoint:** `GET /api/v1/files/{file_id}`

### Download File

Download a file from storage.

**Endpoint:** `GET /api/v1/files/{file_id}/download`

## 💉 File Injection API

### Inject File into VM

Inject a file into a running VM for analysis.

**Endpoint:** `POST /api/v1/vms/{vm_id}/inject`

**Request Body:**
```json
{
  "file_id": "uuid",
  "injection_path": "/tmp/malware.exe",
  "execute": true,
  "monitor": true
}
```

**Response:**
```json
{
  "vm_id": "uuid",
  "file_id": "uuid",
  "injection_path": "/tmp/malware.exe",
  "task_id": "celery-task-uuid",
  "message": "File injection queued"
}
```

## 📊 Monitoring API

### Get VM Metrics

Retrieve real-time metrics for a VM.

**Endpoint:** `GET /api/v1/vms/{vm_id}/metrics`

**Response:**
```json
{
  "vm_id": "uuid",
  "cpu_percent": 15.2,
  "memory_usage_mb": 256,
  "memory_limit_mb": 1024,
  "network_rx_bytes": 1024000,
  "network_tx_bytes": 512000,
  "disk_usage_gb": 2.5,
  "processes": 42,
  "uptime_seconds": 930
}
```

### Get VM Logs

Retrieve logs from a VM.

**Endpoint:** `GET /api/v1/vms/{vm_id}/logs`

**Query Parameters:**
- `lines`: Number of log lines to return (default: 100)
- `since`: Return logs since timestamp

## 🏥 Health Check API

### System Health

Check overall system health.

**Endpoint:** `GET /health/`

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-10T10:00:00Z",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "minio": "healthy",
    "docker": "healthy"
  }
}
```

### API Health

Check API service health.

**Endpoint:** `GET /api/v1/health/`

## 📈 Status Codes

### Success Codes
- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `202 Accepted`: Request accepted for processing

### Client Error Codes
- `400 Bad Request`: Invalid request data
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation error

### Server Error Codes
- `500 Internal Server Error`: Server error
- `503 Service Unavailable`: Service temporarily unavailable

## 🔧 Error Response Format

All error responses follow a consistent format:

```json
{
  "detail": "Error description",
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2025-01-10T10:00:00Z",
  "request_id": "uuid"
}
```

## 📚 Interactive Documentation

Access the interactive API documentation at:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🎯 Examples

### Complete VM Analysis Workflow with WebSocket Monitoring

```bash
#!/bin/bash
# Complete TurdParty malware analysis workflow with real-time monitoring

# 1. Get available templates
echo "🔍 Getting available VM templates..."
curl -s http://localhost:8000/api/v1/vms/templates | jq '.[].name'

# 2. Create analysis VM
echo "🚀 Creating analysis VM..."
VM_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" \
  -d '{
    "name":"malware-analysis-$(date +%s)",
    "template":"ubuntu:20.04",
    "vm_type":"docker",
    "memory_mb":1024,
    "cpus":2,
    "domain":"TurdParty",
    "description":"Automated malware analysis session"
  }' \
  http://localhost:8000/api/v1/vms/)

VM_ID=$(echo $VM_RESPONSE | jq -r '.vm_id')
echo "✅ VM created: $VM_ID"

# 3. Wait for VM to be ready with status monitoring
echo "⏳ Waiting for VM to be ready..."
while [ "$(curl -s http://localhost:8000/api/v1/vms/$VM_ID | jq -r '.status')" != "running" ]; do
  STATUS=$(curl -s http://localhost:8000/api/v1/vms/$VM_ID | jq -r '.status')
  echo "   VM Status: $STATUS"
  sleep 5
done
echo "✅ VM is running"

# 4. Upload malware sample
echo "📁 Uploading malware sample..."
FILE_RESPONSE=$(curl -s -X POST -F "file=@malware.exe" \
  -F "description=Malware sample for analysis" \
  http://localhost:8000/api/v1/files/upload)

FILE_ID=$(echo $FILE_RESPONSE | jq -r '.file_id')
FILENAME=$(echo $FILE_RESPONSE | jq -r '.filename')
echo "✅ File uploaded: $FILENAME ($FILE_ID)"

# 5. Start WebSocket monitoring in background
echo "📊 Starting real-time monitoring..."
wscat -c "ws://localhost:8000/api/v1/vms/$VM_ID/metrics/stream" > vm_metrics.log &
METRICS_PID=$!

# 6. Inject file into VM
echo "💉 Injecting file into VM..."
INJECT_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" \
  -d '{
    "file_id":"'$FILE_ID'",
    "injection_path":"/tmp/'$FILENAME'",
    "execute":true,
    "monitor":true
  }' \
  http://localhost:8000/api/v1/vms/$VM_ID/inject)

TASK_ID=$(echo $INJECT_RESPONSE | jq -r '.task_id')
echo "✅ File injection queued: $TASK_ID"

# 7. Monitor analysis for 5 minutes
echo "🔬 Monitoring analysis for 5 minutes..."
for i in {1..30}; do
  echo "   Analysis progress: $((i*100/30))%"
  sleep 10
done

# 8. Get analysis results
echo "📋 Collecting analysis results..."
curl -s http://localhost:8000/api/v1/vms/$VM_ID/logs?lines=1000 > analysis_results.log
curl -s http://localhost:8000/api/v1/vms/$VM_ID/metrics > final_metrics.json

# 9. Stop monitoring
kill $METRICS_PID 2>/dev/null

# 10. Generate analysis report
echo "📊 Analysis Summary:"
echo "   VM ID: $VM_ID"
echo "   File: $FILENAME"
echo "   Duration: 5 minutes"
echo "   Logs: $(wc -l < analysis_results.log) lines"
echo "   Metrics: $(cat vm_metrics.log | wc -l) data points"

# 11. Terminate VM
echo "🧹 Cleaning up VM..."
curl -s -X DELETE "http://localhost:8000/api/v1/vms/$VM_ID?force=true"
echo "✅ VM terminated and cleaned up"

echo "🎉 Analysis complete! Results saved to analysis_results.log"
```

### WebSocket Client Examples

#### Python WebSocket Client

```python
#!/usr/bin/env python3
"""
TurdParty WebSocket client for real-time VM monitoring
"""

import asyncio
import websockets
import json
import sys
from datetime import datetime

class TurdPartyWebSocketClient:
    def __init__(self, vm_id, base_url="ws://localhost:8000"):
        self.vm_id = vm_id
        self.base_url = base_url
        self.metrics_data = []

    async def monitor_metrics(self, duration=60):
        """Monitor VM metrics for specified duration."""
        uri = f"{self.base_url}/api/v1/vms/{self.vm_id}/metrics/stream"

        print(f"🔗 Connecting to metrics stream: {uri}")

        try:
            async with websockets.connect(uri) as websocket:
                print("✅ Connected to metrics stream")

                start_time = asyncio.get_event_loop().time()

                async for message in websocket:
                    data = json.loads(message)

                    if data.get("type") == "metrics":
                        self.process_metrics(data)

                    # Check if duration exceeded
                    if asyncio.get_event_loop().time() - start_time > duration:
                        break

        except websockets.exceptions.ConnectionClosed:
            print("❌ Connection closed")
        except Exception as e:
            print(f"❌ Error: {e}")

    def process_metrics(self, data):
        """Process incoming metrics data."""
        timestamp = data.get("timestamp", datetime.now().isoformat())
        cpu = data.get("cpu_percent", "N/A")
        memory = data.get("memory_percent", "N/A")
        status = data.get("status", "unknown")

        print(f"📊 [{timestamp}] CPU: {cpu}%, Memory: {memory}%, Status: {status}")

        self.metrics_data.append(data)

    async def execute_command(self, command):
        """Execute command and stream output."""
        uri = f"{self.base_url}/api/v1/vms/{self.vm_id}/commands/execute"

        print(f"🔗 Connecting to command execution: {uri}")

        try:
            async with websockets.connect(uri) as websocket:
                print("✅ Connected to command execution")

                # Send command
                command_msg = {
                    "type": "command",
                    "command": command,
                    "timeout": 30
                }

                await websocket.send(json.dumps(command_msg))
                print(f"📤 Sent command: {command}")

                # Receive output
                async for message in websocket:
                    data = json.loads(message)

                    if data.get("type") == "stdout":
                        print(f"📤 STDOUT: {data.get('output', '').strip()}")
                    elif data.get("type") == "stderr":
                        print(f"📤 STDERR: {data.get('output', '').strip()}")
                    elif data.get("type") == "exit":
                        exit_code = data.get("exit_code", -1)
                        duration = data.get("duration_ms", 0)
                        print(f"✅ Command completed: exit_code={exit_code}, duration={duration}ms")
                        break

        except Exception as e:
            print(f"❌ Error: {e}")

async def main():
    if len(sys.argv) != 2:
        print("Usage: python websocket_client.py <vm_id>")
        sys.exit(1)

    vm_id = sys.argv[1]
    client = TurdPartyWebSocketClient(vm_id)

    print(f"🚀 Starting TurdParty WebSocket client for VM: {vm_id}")

    # Monitor metrics for 30 seconds
    print("📊 Monitoring metrics for 30 seconds...")
    await client.monitor_metrics(duration=30)

    # Execute some commands
    commands = ["whoami", "pwd", "ls -la /tmp", "ps aux"]

    for cmd in commands:
        print(f"\n🔧 Executing command: {cmd}")
        await client.execute_command(cmd)
        await asyncio.sleep(2)

    print(f"\n📈 Collected {len(client.metrics_data)} metrics data points")
    print("🎉 WebSocket client session complete")

if __name__ == "__main__":
    asyncio.run(main())
```

#### JavaScript WebSocket Client

```javascript
/**
 * TurdParty WebSocket client for web applications
 */
class TurdPartyWebSocketManager {
    constructor(vmId, baseUrl = 'ws://localhost:8000') {
        this.vmId = vmId;
        this.baseUrl = baseUrl;
        this.connections = new Map();
        this.callbacks = new Map();
        this.reconnectAttempts = new Map();
        this.maxReconnectAttempts = 5;
    }

    /**
     * Connect to metrics stream
     */
    connectMetrics(callback) {
        const endpoint = 'metrics/stream';
        const url = `${this.baseUrl}/api/v1/vms/${this.vmId}/${endpoint}`;

        this.connect(endpoint, url, callback);
    }

    /**
     * Connect to command execution
     */
    connectCommands(callback) {
        const endpoint = 'commands/execute';
        const url = `${this.baseUrl}/api/v1/vms/${this.vmId}/${endpoint}`;

        this.connect(endpoint, url, callback);
    }

    /**
     * Connect to file upload
     */
    connectFileUpload(callback) {
        const endpoint = 'files/upload';
        const url = `${this.baseUrl}/api/v1/vms/${this.vmId}/${endpoint}`;

        this.connect(endpoint, url, callback);
    }

    /**
     * Generic connection method
     */
    connect(endpoint, url, callback) {
        console.log(`🔗 Connecting to ${endpoint}: ${url}`);

        const ws = new WebSocket(url);

        ws.onopen = (event) => {
            console.log(`✅ Connected to ${endpoint}`);
            this.reconnectAttempts.set(endpoint, 0);

            if (callback && callback.onOpen) {
                callback.onOpen(event);
            }
        };

        ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);

                if (callback && callback.onMessage) {
                    callback.onMessage(data);
                }

                // Handle specific message types
                this.handleMessage(endpoint, data);

            } catch (error) {
                console.error(`❌ Error parsing message from ${endpoint}:`, error);
            }
        };

        ws.onclose = (event) => {
            console.log(`❌ Connection to ${endpoint} closed:`, event.code, event.reason);

            if (callback && callback.onClose) {
                callback.onClose(event);
            }

            // Attempt reconnection
            this.attemptReconnection(endpoint, url, callback);
        };

        ws.onerror = (error) => {
            console.error(`❌ WebSocket error on ${endpoint}:`, error);

            if (callback && callback.onError) {
                callback.onError(error);
            }
        };

        this.connections.set(endpoint, ws);
        this.callbacks.set(endpoint, callback);
    }

    /**
     * Handle incoming messages
     */
    handleMessage(endpoint, data) {
        const timestamp = new Date().toISOString();

        switch (data.type) {
            case 'metrics':
                console.log(`📊 [${timestamp}] Metrics - CPU: ${data.cpu_percent}%, Memory: ${data.memory_percent}%`);
                break;

            case 'stdout':
                console.log(`📤 [${timestamp}] STDOUT: ${data.output}`);
                break;

            case 'stderr':
                console.log(`📤 [${timestamp}] STDERR: ${data.output}`);
                break;

            case 'exit':
                console.log(`✅ [${timestamp}] Command completed: exit_code=${data.exit_code}`);
                break;

            case 'progress':
                console.log(`📁 [${timestamp}] Upload progress: ${data.progress_percent}%`);
                break;

            case 'complete':
                console.log(`✅ [${timestamp}] Upload complete: ${data.filename}`);
                break;

            case 'error':
                console.error(`❌ [${timestamp}] Error: ${data.error}`);
                break;

            default:
                console.log(`📨 [${timestamp}] ${endpoint}: ${data.type}`, data);
        }
    }

    /**
     * Send command
     */
    sendCommand(command, timeout = 30) {
        const ws = this.connections.get('commands/execute');

        if (ws && ws.readyState === WebSocket.OPEN) {
            const message = {
                type: 'command',
                command: command,
                timeout: timeout,
                timestamp: new Date().toISOString()
            };

            ws.send(JSON.stringify(message));
            console.log(`📤 Sent command: ${command}`);
        } else {
            console.error('❌ Command connection not available');
        }
    }

    /**
     * Attempt reconnection with exponential backoff
     */
    attemptReconnection(endpoint, url, callback) {
        const attempts = this.reconnectAttempts.get(endpoint) || 0;

        if (attempts < this.maxReconnectAttempts) {
            const delay = Math.pow(2, attempts) * 1000; // Exponential backoff

            console.log(`🔄 Reconnecting to ${endpoint} in ${delay}ms (attempt ${attempts + 1}/${this.maxReconnectAttempts})`);

            setTimeout(() => {
                this.reconnectAttempts.set(endpoint, attempts + 1);
                this.connect(endpoint, url, callback);
            }, delay);
        } else {
            console.error(`❌ Max reconnection attempts reached for ${endpoint}`);
        }
    }

    /**
     * Disconnect from specific endpoint
     */
    disconnect(endpoint) {
        const ws = this.connections.get(endpoint);

        if (ws) {
            ws.close();
            this.connections.delete(endpoint);
            this.callbacks.delete(endpoint);
            this.reconnectAttempts.delete(endpoint);
            console.log(`🔌 Disconnected from ${endpoint}`);
        }
    }

    /**
     * Disconnect from all endpoints
     */
    disconnectAll() {
        for (const endpoint of this.connections.keys()) {
            this.disconnect(endpoint);
        }
        console.log('🔌 Disconnected from all endpoints');
    }
}

// Usage example
const vmId = 'your-vm-id-here';
const wsManager = new TurdPartyWebSocketManager(vmId);

// Connect to metrics
wsManager.connectMetrics({
    onMessage: (data) => {
        if (data.type === 'metrics') {
            // Update UI with metrics data
            updateMetricsDisplay(data);
        }
    }
});

// Connect to commands
wsManager.connectCommands({
    onMessage: (data) => {
        if (data.type === 'stdout' || data.type === 'stderr') {
            // Display command output
            appendToTerminal(data.output);
        }
    }
});

// Send commands
wsManager.sendCommand('ls -la /tmp');
wsManager.sendCommand('ps aux');

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    wsManager.disconnectAll();
});
```

This comprehensive API documentation provides enterprise-grade VM management capabilities for malware analysis with real-time WebSocket monitoring, interactive command execution, and progressive file operations.
