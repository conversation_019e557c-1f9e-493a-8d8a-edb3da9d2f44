# Git Hooks Documentation

This document describes the custom git hooks implemented in the TurdParty project to improve development workflow and prevent accidental pushes during business hours.

## Overview

The TurdParty project uses custom git hooks to:
- Discourage pushing to GitHub during business hours (7am-18:00 CET, Monday-Friday)
- Allow unrestricted local commits at any time
- Provide clear feedback and options for developers
- Maintain team collaboration standards

## Installation

### Automatic Setup (Recommended)

Run the setup script to install all git hooks:

```bash
./scripts/setup-git-hooks.sh
```

This script will:
- Backup any existing git hooks
- Install the custom hooks from `.githooks/`
- Make hooks executable
- Test the installation
- Display current status and usage information

### Manual Setup

If you prefer to install hooks manually:

```bash
# Copy hooks to git directory
cp .githooks/* .git/hooks/

# Make hooks executable
chmod +x .git/hooks/*
```

## Pre-Push Hook

### Purpose

The pre-push hook prevents accidental pushes to GitHub during business hours, encouraging developers to:
- Work locally during the day
- Push changes outside business hours
- Be more intentional about when code is shared

### Business Hours Definition

- **Days**: Monday through Friday
- **Hours**: 07:00 to 18:00 CET (Central European Time)
- **Weekends**: No restrictions (Saturday and Sunday)

### Behaviour

#### During Business Hours (7am-18:00 CET, Mon-Fri)

When attempting to push to a GitHub remote:

1. **Detection**: Hook detects GitHub URL in remote
2. **Warning**: Displays business hours warning with current time
3. **Prompt**: Asks for confirmation to proceed
4. **Options**:
   - **No (default)**: Cancels the push, suggests local work
   - **Yes**: Proceeds with push after confirmation
   - **Bypass**: Use `git push --no-verify` to skip hook entirely

#### Outside Business Hours

- **Evenings/Mornings**: Push proceeds normally
- **Weekends**: Push proceeds normally
- **Non-GitHub remotes**: Always allowed

#### Local Commits

- **Always allowed**: Local commits work at any time
- **No restrictions**: Hook only affects pushes, not commits

### Usage Examples

#### Normal Development Workflow

```bash
# Local work (always allowed)
git add .
git commit -m "implement new feature"
git commit -m "fix bug in validation"

# Push outside business hours (after 18:00 CET)
git push origin feature/my-feature
```

#### Business Hours Push (Interactive)

```bash
# During business hours
git push origin main

# Output:
# ⚠️  BUSINESS HOURS DETECTED!
# Current time: 14:00 CET
# 🚫 Pushing to GitHub during business hours is discouraged!
# ❓ Do you really want to push during business hours? [y/N]:

# Type 'y' to proceed or 'n' to cancel
```

#### Emergency Push (Bypass)

```bash
# For urgent fixes during business hours
git push --no-verify origin hotfix/critical-bug
```

#### Non-GitHub Remote (Always Allowed)

```bash
# Internal or other remotes work anytime
git push internal-server main
git push gitlab.company.com main
```

## Hook Files

### `.githooks/pre-push`

The main pre-push hook implementation:

- **Language**: Bash script
- **Dependencies**: Standard Unix tools (date, grep)
- **Timezone**: Handles CET calculation from UTC
- **Error Handling**: Graceful fallbacks for timezone detection

### `scripts/setup-git-hooks.sh`

Installation and setup script:

- **Backup**: Creates timestamped backups of existing hooks
- **Installation**: Copies and sets permissions for hooks
- **Testing**: Validates hook installation
- **Information**: Shows current status and usage examples

## Configuration

### Timezone Handling

The hook automatically detects CET time using multiple methods:

1. **timedatectl** (systemd systems)
2. **zoneinfo** files (`/usr/share/zoneinfo/Europe/Berlin`)
3. **UTC calculation** (fallback)

### Customisation

To modify business hours or behaviour, edit `.githooks/pre-push`:

```bash
# Change business hours (currently 7-18)
if [ $current_hour -ge 7 ] && [ $current_hour -lt 18 ]; then

# Change timezone (currently CET/Europe/Berlin)
TZ='Europe/Berlin' date '+%H'

# Modify weekend detection (currently Sat/Sun)
if [ $day -eq 6 ] || [ $day -eq 7 ]; then
```

## Troubleshooting

### Hook Not Running

```bash
# Check if hook is installed and executable
ls -la .git/hooks/pre-push

# Reinstall hooks
./scripts/setup-git-hooks.sh
```

### Timezone Issues

```bash
# Test timezone detection
TZ='Europe/Berlin' date '+%H'

# Check system timezone
timedatectl status
```

### Bypass Hook Temporarily

```bash
# Skip all hooks for one push
git push --no-verify origin main

# Or set environment variable
GIT_PUSH_OPTION_NO_VERIFY=1 git push origin main
```

### Remove Hooks

```bash
# Remove specific hook
rm .git/hooks/pre-push

# Remove all custom hooks
rm .git/hooks/*

# Restore from backup (if available)
cp .git/hooks.backup.TIMESTAMP/* .git/hooks/
```

## Best Practices

### Development Workflow

1. **Work Locally**: Make commits throughout the day
2. **Batch Pushes**: Push accumulated work outside business hours
3. **Feature Branches**: Use feature branches for ongoing work
4. **Emergency Only**: Use `--no-verify` only for critical fixes

### Team Collaboration

1. **Consistent Setup**: All team members should install hooks
2. **Documentation**: Keep this documentation updated
3. **Feedback**: Report issues or suggestions for hook improvements
4. **Respect Hours**: Follow the spirit of the policy, not just the letter

### CI/CD Integration

The hooks work alongside CI/CD pipelines:

- **Local Protection**: Hooks prevent accidental pushes
- **CI Validation**: Automated tests run on all pushes
- **Deployment**: Separate deployment schedules can be maintained

## FAQ

### Q: Can I still work during business hours?

**A**: Yes! Local commits are always allowed. Only pushes to GitHub are discouraged.

### Q: What if I have an urgent fix?

**A**: Use `git push --no-verify` to bypass the hook for critical issues.

### Q: Does this affect CI/CD?

**A**: No, hooks only run on local git operations, not on CI/CD systems.

### Q: Can I customise the business hours?

**A**: Yes, edit the `.githooks/pre-push` file to change hours or timezone.

### Q: What about different timezones?

**A**: The hook uses CET. Team members in other timezones will see CET-based restrictions.

### Q: How do I uninstall the hooks?

**A**: Delete files from `.git/hooks/` or restore from the backup created during installation.

---

*This documentation follows British English conventions and is maintained as part of the TurdParty project.*
