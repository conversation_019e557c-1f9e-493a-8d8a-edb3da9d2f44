# Makefile for Sphinx documentation

# You can set these variables from the command line, and also
# from the environment for the first two.
SPHINXOPTS    ?=
SPHINXBUILD  ?= sphinx-build
SOURCEDIR    = .
BUILDDIR     = _build

# Put it first so that "make" without argument is like "make help".
help:
	@$(SPHINXBUILD) -M help "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

.PHONY: help Makefile

# Custom targets for TurdParty documentation

# Install documentation dependencies
install:
	pip install -r requirements-docs.txt

# Clean build directory
clean:
	rm -rf $(BUILDDIR)/*
	rm -rf _static/generated/*

# Build HTML documentation
html:
	@echo "Building HTML documentation..."
	@$(SPHINXBUILD) -b html "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS) $(O)
	@echo "HTML documentation built in $(BUILDDIR)/html/"

# Build HTML documentation with live reload
livehtml:
	@echo "Starting live HTML documentation server..."
	sphinx-autobuild "$(SOURCEDIR)" "$(BUILDDIR)/html" --host 0.0.0.0 --port 8080

# Build PDF documentation
pdf:
	@echo "Building PDF documentation..."
	@$(SPHINXBUILD) -b latex "$(SOURCEDIR)" "$(BUILDDIR)/latex" $(SPHINXOPTS) $(O)
	@make -C $(BUILDDIR)/latex all-pdf
	@echo "PDF documentation built in $(BUILDDIR)/latex/"

# Build EPUB documentation
epub:
	@echo "Building EPUB documentation..."
	@$(SPHINXBUILD) -b epub "$(SOURCEDIR)" "$(BUILDDIR)/epub" $(SPHINXOPTS) $(O)
	@echo "EPUB documentation built in $(BUILDDIR)/epub/"

# Check documentation for errors
check:
	@echo "Checking documentation for errors..."
	@$(SPHINXBUILD) -b linkcheck "$(SOURCEDIR)" "$(BUILDDIR)/linkcheck" $(SPHINXOPTS) $(O)
	@$(SPHINXBUILD) -b spelling "$(SOURCEDIR)" "$(BUILDDIR)/spelling" $(SPHINXOPTS) $(O)

# Generate API documentation from source code
apidoc:
	@echo "Generating API documentation from source code..."
	sphinx-apidoc -o reference/api ../api --force --module-first
	sphinx-apidoc -o reference/services ../services --force --module-first

# Build all documentation formats
all: clean html pdf epub
	@echo "All documentation formats built successfully!"

# Deploy documentation to GitHub Pages
deploy:
	@echo "Deploying documentation to GitHub Pages..."
	@if [ ! -d "$(BUILDDIR)/html" ]; then \
		echo "HTML documentation not found. Run 'make html' first."; \
		exit 1; \
	fi
	@git checkout gh-pages || git checkout -b gh-pages
	@cp -r $(BUILDDIR)/html/* .
	@git add .
	@git commit -m "Update documentation"
	@git push origin gh-pages
	@git checkout main

# Serve documentation locally
serve:
	@echo "Serving documentation at http://localhost:8000"
	@cd $(BUILDDIR)/html && python -m http.server 8000

# Watch for changes and rebuild
watch:
	@echo "Watching for changes..."
	@while true; do \
		inotifywait -r -e modify,create,delete $(SOURCEDIR); \
		make html; \
		echo "Documentation rebuilt at $$(date)"; \
	done

# Validate documentation structure
validate:
	@echo "Validating documentation structure..."
	@python scripts/validate_docs.py

# Generate OpenAPI documentation
openapi:
	@echo "Generating OpenAPI documentation..."
	@curl -s http://localhost:8000/openapi.json > _static/openapi.json
	@echo "OpenAPI specification saved to _static/openapi.json"

# Update version information
version:
	@echo "Updating version information..."
	@python scripts/update_version.py

# Catch-all target: route all unknown targets to Sphinx using the new
# "make mode" option.  $(O) is meant as a shortcut for $(SPHINXOPTS).
%: Makefile
	@$(SPHINXBUILD) -M $@ "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)
