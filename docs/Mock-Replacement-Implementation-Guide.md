# Mock Replacement Implementation Guide

## Overview

This guide provides detailed implementation steps for replacing mock testing with productive testing in the TurdParty platform. Each section includes specific code examples, configuration changes, and validation procedures.

## Phase 1: Infrastructure Services (Weeks 1-2)

### Database Layer Migration

#### Current Mock Pattern
```python
# tests/conftest.py - Current mock approach
@pytest.fixture
def mock_database():
    with patch('api.database.get_session') as mock_session:
        mock_session.return_value = MagicMock()
        yield mock_session
```

#### Target Real Implementation
```python
# tests/conftest.py - Real database approach
import pytest
from testcontainers.postgres import PostgresContainer
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

@pytest.fixture(scope="session")
def postgres_container():
    with PostgresContainer("postgres:15") as postgres:
        yield postgres

@pytest.fixture(scope="session")
def test_database_url(postgres_container):
    return postgres_container.get_connection_url()

@pytest.fixture
def test_db_session(test_database_url):
    engine = create_engine(test_database_url)
    TestingSessionLocal = sessionmaker(bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
```

#### Migration Steps
1. **Install TestContainers**: `pip install testcontainers[postgres]`
2. **Update conftest.py**: Replace mock database fixtures
3. **Update test files**: Replace mock assertions with real database queries
4. **Add cleanup procedures**: Ensure test data isolation

#### Validation Checklist
- [ ] All database tests use real PostgreSQL
- [ ] Test data is properly isolated
- [ ] Database migrations work in tests
- [ ] Connection pooling is tested
- [ ] Performance is acceptable (<2s setup time)

### Redis Layer Migration

#### Current Mock Pattern
```python
# Current Redis mocking
@pytest.fixture
def mock_redis():
    with patch('api.cache.redis_client') as mock_redis:
        mock_redis.get.return_value = None
        mock_redis.set.return_value = True
        yield mock_redis
```

#### Target Real Implementation
```python
# Real Redis testing
from testcontainers.redis import RedisContainer
import redis

@pytest.fixture(scope="session")
def redis_container():
    with RedisContainer("redis:7-alpine") as redis_container:
        yield redis_container

@pytest.fixture
def test_redis_client(redis_container):
    client = redis.Redis(
        host=redis_container.get_container_host_ip(),
        port=redis_container.get_exposed_port(6379),
        decode_responses=True
    )
    yield client
    client.flushall()  # Cleanup after each test
```

### MinIO Storage Migration

#### Current Mock Pattern
```python
# Current MinIO mocking
@pytest.fixture
def mock_minio_client():
    mock_client = MagicMock()
    mock_client.bucket_exists.return_value = True
    mock_client.get_object.return_value = MagicMock()
    return mock_client
```

#### Target Real Implementation
```python
# Real MinIO testing
from testcontainers.minio import MinioContainer
from minio import Minio

@pytest.fixture(scope="session")
def minio_container():
    with MinioContainer() as minio:
        yield minio

@pytest.fixture
def test_minio_client(minio_container):
    client = Minio(
        f"{minio_container.get_container_host_ip()}:{minio_container.get_exposed_port(9000)}",
        access_key=minio_container.access_key,
        secret_key=minio_container.secret_key,
        secure=False
    )
    
    # Create test bucket
    bucket_name = "test-bucket"
    if not client.bucket_exists(bucket_name):
        client.make_bucket(bucket_name)
    
    yield client, bucket_name
    
    # Cleanup
    objects = client.list_objects(bucket_name, recursive=True)
    for obj in objects:
        client.remove_object(bucket_name, obj.object_name)
```

## Phase 2: Core Services (Weeks 3-4)

### VM Management Service Migration

#### Current Mock Pattern
```python
# tests/unit/test_vm_service.py - Current approach
@pytest.fixture
def mock_docker_client():
    mock_client = MagicMock()
    mock_container = MagicMock()
    mock_container.id = "test_container_123"
    mock_client.containers.run.return_value = mock_container
    return mock_client
```

#### Target Real Implementation
```python
# Real Docker testing
import docker
from testcontainers.core.container import DockerContainer

@pytest.fixture(scope="session")
def docker_client():
    return docker.from_env()

@pytest.fixture
def test_vm_service(docker_client):
    # Use real Docker client for testing
    from api.services.vm_service import VMService
    service = VMService()
    service.docker_client = docker_client
    
    yield service
    
    # Cleanup: Remove test containers
    containers = docker_client.containers.list(
        filters={"label": "turdparty.test=true"}
    )
    for container in containers:
        container.remove(force=True)

@pytest.mark.asyncio
async def test_create_docker_vm_real(test_vm_service):
    """Test real Docker VM creation"""
    request = VMCreateRequest(
        name="test-vm",
        template="ubuntu:20.04",
        vm_type=VMType.DOCKER,
        memory_mb=256,
        cpus=1
    )
    
    vm = await test_vm_service.create_vm(request)
    
    # Verify real container was created
    assert vm.vm_id is not None
    assert vm.status == VMStatus.RUNNING
    
    # Verify container exists in Docker
    container = test_vm_service.docker_client.containers.get(
        f"turdparty_{request.name}_{vm.vm_id[:8]}"
    )
    assert container.status == "running"
```

### File Injection Service Migration

#### Current Mock Pattern
```python
# Current file injection mocking
@pytest.fixture
def mock_file_injection_service():
    mock_service = MagicMock()
    mock_service.create_injection = AsyncMock()
    return mock_service
```

#### Target Real Implementation
```python
# Real file injection testing
import tempfile
import shutil
from pathlib import Path

@pytest.fixture
def test_file_injection_service(test_minio_client, temp_dir):
    from api.services.file_injection_service import FileInjectionService
    
    # Configure service with real MinIO and temp directory
    service = FileInjectionService()
    service.minio_client = test_minio_client[0]
    service.bucket_name = test_minio_client[1]
    service.upload_dir = temp_dir
    
    yield service
    
    # Cleanup temp files
    if temp_dir.exists():
        shutil.rmtree(temp_dir)

@pytest.mark.asyncio
async def test_file_injection_real_workflow(test_file_injection_service):
    """Test complete file injection workflow with real services"""
    # Create test file
    test_content = b"#!/bin/bash\necho 'Hello World'\n"
    injection_data = FileInjectionCreate(
        filename="test_script.sh",
        target_path="/tmp/test_script.sh",
        permissions="0755",
        description="Test script"
    )
    
    # Test real file injection
    injection = await test_file_injection_service.create_injection(
        injection_data, test_content
    )
    
    # Verify file was actually saved
    assert injection.id is not None
    assert injection.file_size == len(test_content)
    
    # Verify file exists in storage
    file_path = Path(injection.file_path)
    assert file_path.exists()
    assert file_path.read_bytes() == test_content
    
    # Test processing
    result = await test_file_injection_service.process_injection(injection.id)
    assert result.status == InjectionStatus.COMPLETED
```

## Phase 3: Worker Services (Weeks 5-6)

### Celery Worker Migration

#### Current Mock Pattern
```python
# Current Celery mocking
with patch.dict('sys.modules', {
    'celery': MagicMock(),
    'services.workers.celery_app': MagicMock(),
}):
    from services.workers.tasks.file_operations import download_file_from_minio
```

#### Target Real Implementation
```python
# Real Celery testing
from celery import Celery
from testcontainers.redis import RedisContainer
import pytest

@pytest.fixture(scope="session")
def celery_app(redis_container):
    """Create real Celery app for testing"""
    broker_url = f"redis://{redis_container.get_container_host_ip()}:{redis_container.get_exposed_port(6379)}/0"
    
    app = Celery('test_app')
    app.conf.update(
        broker_url=broker_url,
        result_backend=broker_url,
        task_always_eager=True,  # Execute tasks synchronously for testing
        task_eager_propagates=True,
    )
    
    return app

@pytest.fixture
def test_worker_tasks(celery_app, test_minio_client):
    """Configure worker tasks with real dependencies"""
    from services.workers.tasks import file_operations
    
    # Configure tasks with real services
    file_operations.minio_client = test_minio_client[0]
    file_operations.bucket_name = test_minio_client[1]
    
    yield file_operations

def test_real_file_download_task(test_worker_tasks, test_minio_client):
    """Test real file download from MinIO"""
    client, bucket_name = test_minio_client
    
    # Upload test file
    test_content = b"test file content"
    object_name = "test/file.txt"
    client.put_object(
        bucket_name, object_name, 
        io.BytesIO(test_content), len(test_content)
    )
    
    # Test real download task
    result = test_worker_tasks.download_file_from_minio.delay(
        bucket_name, object_name
    )
    
    assert result.successful()
    downloaded_content = result.result
    assert downloaded_content == test_content
```

## Phase 4: Integration Services (Weeks 7-8)

### ELK Stack Migration

#### Current Mock Pattern
```python
# Current ELK mocking
@pytest.fixture
def mock_elasticsearch_client():
    mock_es = MagicMock()
    mock_es.index = AsyncMock()
    mock_es.search = AsyncMock()
    return mock_es
```

#### Target Real Implementation
```python
# Real ELK testing
from testcontainers.elasticsearch import ElasticsearchContainer
from elasticsearch import AsyncElasticsearch

@pytest.fixture(scope="session")
def elasticsearch_container():
    with ElasticsearchContainer("elasticsearch:8.11.0") as es:
        yield es

@pytest.fixture
async def test_elasticsearch_client(elasticsearch_container):
    client = AsyncElasticsearch([
        f"http://{elasticsearch_container.get_container_host_ip()}:{elasticsearch_container.get_exposed_port(9200)}"
    ])
    
    yield client
    
    # Cleanup indices
    await client.indices.delete(index="test-*", ignore=[404])
    await client.close()

@pytest.mark.asyncio
async def test_real_elk_logging(test_elasticsearch_client):
    """Test real ELK logging functionality"""
    from api.services.elk_logger import ELKLogger
    
    logger = ELKLogger()
    logger.es_client = test_elasticsearch_client
    
    # Test real log indexing
    await logger.log_file_injection_event(
        injection_id="test-123",
        event_type="created",
        filename="test.txt",
        target_path="/tmp/test.txt",
        status="pending"
    )
    
    # Verify log was actually indexed
    await test_elasticsearch_client.indices.refresh(index="turdparty-*")
    
    response = await test_elasticsearch_client.search(
        index="turdparty-*",
        body={"query": {"match": {"injection_id": "test-123"}}}
    )
    
    assert response["hits"]["total"]["value"] > 0
    hit = response["hits"]["hits"][0]
    assert hit["_source"]["injection_id"] == "test-123"
    assert hit["_source"]["event_type"] == "created"
```

## Test Configuration Updates

### pytest Configuration
```ini
# pytest.ini updates
[tool:pytest]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = 
    --strict-markers
    --disable-warnings
    --tb=short
    --cov=api
    --cov=services
    --cov-report=html
    --cov-report=term-missing
    --maxfail=5
markers =
    unit: Unit tests with real dependencies
    integration: Integration tests with real services
    performance: Performance tests with real load
    security: Security tests with real vulnerabilities
    slow: Slow running tests (>10s)
    requires_docker: Tests requiring Docker
    requires_network: Tests requiring network access
```

### Docker Compose for Testing
```yaml
# docker-compose.test.yml
version: '3.8'
services:
  test-postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: turdparty_test
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_pass
    ports:
      - "5433:5432"
    tmpfs:
      - /var/lib/postgresql/data
  
  test-redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    tmpfs:
      - /data
  
  test-minio:
    image: minio/minio:latest
    environment:
      MINIO_ROOT_USER: test_access
      MINIO_ROOT_PASSWORD: test_secret
    ports:
      - "9001:9000"
    command: server /data
    tmpfs:
      - /data
  
  test-elasticsearch:
    image: elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9201:9200"
    tmpfs:
      - /usr/share/elasticsearch/data
```

## Validation and Quality Gates

### Automated Validation Script
```python
# scripts/validate_mock_replacement.py
#!/usr/bin/env python3
"""
Validation script to ensure mock replacement is successful
"""

import ast
import os
from pathlib import Path

def find_mock_usage(directory):
    """Find remaining mock usage in test files"""
    mock_patterns = [
        'MagicMock', 'AsyncMock', 'Mock', 'patch', 'mock_open',
        '@patch', 'unittest.mock', 'pytest-mock'
    ]
    
    mock_files = []
    for file_path in Path(directory).rglob("test_*.py"):
        with open(file_path, 'r') as f:
            content = f.read()
            for pattern in mock_patterns:
                if pattern in content:
                    mock_files.append((file_path, pattern))
    
    return mock_files

def validate_test_infrastructure():
    """Validate test infrastructure is properly configured"""
    required_packages = [
        'testcontainers', 'docker', 'pytest-asyncio',
        'pytest-docker', 'pytest-postgresql'
    ]
    
    # Check if packages are installed
    import pkg_resources
    missing_packages = []
    for package in required_packages:
        try:
            pkg_resources.get_distribution(package)
        except pkg_resources.DistributionNotFound:
            missing_packages.append(package)
    
    return missing_packages

if __name__ == "__main__":
    print("🔍 Validating mock replacement...")
    
    # Check for remaining mocks
    mock_usage = find_mock_usage("tests/")
    if mock_usage:
        print(f"⚠️  Found {len(mock_usage)} files still using mocks:")
        for file_path, pattern in mock_usage:
            print(f"   {file_path}: {pattern}")
    else:
        print("✅ No mock usage found in test files")
    
    # Check test infrastructure
    missing_packages = validate_test_infrastructure()
    if missing_packages:
        print(f"❌ Missing required packages: {missing_packages}")
    else:
        print("✅ Test infrastructure properly configured")
    
    print("\n📊 Mock replacement validation complete")
```

## Performance Optimization

### Test Execution Optimization
```python
# conftest.py - Optimized test setup
import pytest
from testcontainers.compose import DockerCompose

@pytest.fixture(scope="session")
def test_infrastructure():
    """Start all test infrastructure once per session"""
    with DockerCompose(".", compose_file_name="docker-compose.test.yml") as compose:
        # Wait for services to be ready
        compose.wait_for("http://localhost:5433")  # PostgreSQL
        compose.wait_for("http://localhost:6380")  # Redis
        compose.wait_for("http://localhost:9001")  # MinIO
        compose.wait_for("http://localhost:9201")  # Elasticsearch
        
        yield compose

@pytest.fixture(autouse=True)
def cleanup_test_data(test_infrastructure):
    """Cleanup test data after each test"""
    yield
    
    # Cleanup database
    # Cleanup Redis
    # Cleanup MinIO
    # Cleanup Elasticsearch
```

## Migration Checklist

### Pre-Migration Checklist
- [ ] Test infrastructure containers configured
- [ ] TestContainers library installed and configured
- [ ] Docker environment available for testing
- [ ] Test data cleanup procedures implemented
- [ ] Performance benchmarks established

### Post-Migration Validation
- [ ] All tests pass with real services
- [ ] Test execution time within acceptable limits
- [ ] No mock imports remaining in test files
- [ ] Test isolation verified
- [ ] CI/CD pipeline updated for new test requirements

### Rollback Plan
- [ ] Keep mock implementations as backup
- [ ] Feature flags for test mode selection
- [ ] Gradual migration with parallel test execution
- [ ] Performance monitoring during transition
- [ ] Quick rollback procedure documented

---

*This implementation guide should be used in conjunction with the main PRD document.*
