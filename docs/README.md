# TurdParty API Documentation

This directory contains the comprehensive documentation for the TurdParty VM WebSocket API.

## Quick Access

- **Interactive API Documentation**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc
- **HTML Documentation**: Open `_build/index.html` in your browser
- **Kibana Dashboard**: http://kibana.turdparty.localhost

## Building Documentation

To build the full Sphinx documentation:

```bash
# Install documentation dependencies
pip install sphinx sphinx-rtd-theme myst-parser

# Build HTML documentation
make html

# Serve documentation locally
make serve
```

## Documentation Structure

- `api/` - API reference documentation
- `websocket/` - WebSocket endpoint documentation
- `vm/` - Virtual machine management guides
- `monitoring/` - Monitoring and logging documentation
- `installation.rst` - Installation and setup guide
- `_static/` - Static assets (CSS, images)
- `_build/` - Generated documentation output

## Features Documented

- ✅ REST API endpoints with examples
- ✅ WebSocket real-time communication
- ✅ VM management and lifecycle
- ✅ ECS logging and monitoring
- ✅ Installation and configuration
- ✅ Performance optimization
- ✅ Security considerations
- ✅ Troubleshooting guides

## Contributing

When adding new documentation:

1. Follow the existing structure and style
2. Include code examples and use cases
3. Update the table of contents in `index.rst`
4. Test documentation builds locally
5. Ensure all links work correctly

For more information, see the main project README.
