# Documentation Rebuild Guide

## 📚 When to Rebuild Documentation

### **Automatic Rebuild Triggers**

The documentation should be rebuilt automatically in the following scenarios:

#### **🔄 Continuous (Real-time)**
- **API Schema Changes**: When OpenAPI spec changes (every API restart)
- **Development Mode**: When working on documentation (watch mode)
- **Live Updates**: Status pages and dynamic content (every 30 seconds)

#### **📅 Regular Intervals**

| Frequency | Trigger | Reason |
|-----------|---------|---------|
| **Every API Restart** | Service deployment | OpenAPI spec extraction |
| **Every 5 minutes** | Development mode | File change detection |
| **Every 30 minutes** | Production mode | Status updates |
| **Daily** | Scheduled job | Full rebuild with latest data |
| **On Git Push** | CI/CD pipeline | Version updates |

### **Manual Rebuild Scenarios**

#### **🚨 Immediate Rebuild Required**
- New API endpoints added
- Major documentation updates
- Configuration changes
- New features documented
- Bug fixes in documentation

#### **⚡ Quick Rebuild (< 30 seconds)**
```bash
# Build documentation once
./scripts/build-docs-auto.sh build

# Extract latest API docs
curl -s http://localhost:8000/openapi.json > docs/_build/html/api/openapi.json
```

#### **🔍 Full Rebuild (< 2 minutes)**
```bash
# Clean and rebuild everything
rm -rf docs/_build/html/*
./scripts/build-docs-auto.sh build
```

## 🛠️ Rebuild Methods

### **Method 1: Automatic Watch Mode (Recommended for Development)**

```bash
# Start automatic rebuilding on file changes
WATCH_MODE=true ./scripts/build-docs-auto.sh watch
```

**Features:**
- Monitors `docs/` directory for changes
- Rebuilds within 5 seconds of file modification
- Extracts API docs every 30 seconds
- Runs continuously in background

### **Method 2: Manual Build**

```bash
# Single build
./scripts/build-docs-auto.sh build

# With API extraction
./scripts/build-docs-auto.sh build && curl -s http://localhost:8000/openapi.json > docs/_build/html/api/openapi.json
```

### **Method 3: Docker Compose Integration**

```bash
# Restart frontend to pick up new docs
docker-compose restart frontend

# Full rebuild and restart
./scripts/build-docs-auto.sh build && docker-compose restart frontend
```

### **Method 4: Scheduled Automation**

```bash
# Add to crontab for automatic rebuilds
# Rebuild every 30 minutes during work hours
*/30 9-17 * * 1-5 cd /path/to/turdparty && ./scripts/build-docs-auto.sh build

# Daily full rebuild at 2 AM
0 2 * * * cd /path/to/turdparty && rm -rf docs/_build/html/* && ./scripts/build-docs-auto.sh build
```

## 📊 Rebuild Performance

### **Build Times**

| Build Type | Duration | Description |
|------------|----------|-------------|
| **Simple HTML** | 2-5 seconds | Basic HTML generation |
| **Sphinx Build** | 10-30 seconds | Full Sphinx with extensions |
| **API Extraction** | 1-2 seconds | OpenAPI spec download |
| **Full Clean Build** | 30-60 seconds | Complete rebuild from scratch |

### **Resource Usage**

| Resource | Light Build | Full Build |
|----------|-------------|------------|
| **CPU** | < 10% | 20-40% |
| **Memory** | < 100MB | 200-500MB |
| **Disk I/O** | Minimal | Moderate |
| **Network** | API calls only | Image downloads |

## 🔧 Configuration Options

### **Environment Variables**

```bash
# Watch mode settings
export WATCH_MODE=true              # Enable file watching
export AUTO_RELOAD=true             # Auto-extract API docs
export BUILD_INTERVAL=30            # Check interval (seconds)

# Build options
export DOCS_FORMAT=html             # Output format
export INCLUDE_API_DOCS=true        # Extract API documentation
export CLEAN_BUILD=false            # Clean before build
```

### **Development vs Production**

#### **Development Settings**
```bash
# Fast rebuilds, frequent updates
WATCH_MODE=true
AUTO_RELOAD=true
BUILD_INTERVAL=5
CLEAN_BUILD=false
```

#### **Production Settings**
```bash
# Stable builds, less frequent updates
WATCH_MODE=false
AUTO_RELOAD=false
BUILD_INTERVAL=1800  # 30 minutes
CLEAN_BUILD=true
```

## 🚀 Integration with Services

### **Frontend Integration**

The documentation is mounted as a volume in the frontend container:

```yaml
volumes:
  - ./docs/_build/html:/usr/share/nginx/html/docs:ro
```

**Access URLs:**
- Main docs: `http://frontend.turdparty.localhost/docs/`
- API docs: `http://frontend.turdparty.localhost/docs/api/`

### **CI/CD Integration**

```yaml
# GitHub Actions example
- name: Build Documentation
  run: |
    ./scripts/build-docs-auto.sh build
    
- name: Deploy Documentation
  run: |
    docker-compose restart frontend
```

### **Monitoring Integration**

```bash
# Check if docs are up to date
if [ docs/_build/html/index.html -ot docs/index.rst ]; then
    echo "Documentation needs rebuild"
    ./scripts/build-docs-auto.sh build
fi
```

## 📈 Best Practices

### **🎯 Optimal Rebuild Strategy**

1. **Development**: Use watch mode for immediate feedback
2. **Testing**: Rebuild before running tests
3. **Staging**: Rebuild on deployment
4. **Production**: Scheduled rebuilds + manual triggers

### **⚡ Performance Optimization**

1. **Incremental Builds**: Only rebuild changed sections
2. **Caching**: Cache API responses and static content
3. **Parallel Processing**: Build multiple formats simultaneously
4. **Resource Limits**: Limit CPU/memory usage during builds

### **🔍 Quality Assurance**

```bash
# Validate documentation after rebuild
./scripts/build-docs-auto.sh build

# Check for broken links
find docs/_build/html -name "*.html" -exec grep -l "404\|broken" {} \;

# Verify API docs are current
curl -s http://localhost:8000/openapi.json | jq '.info.version'
```

## 🚨 Troubleshooting

### **Common Issues**

#### **Build Failures**
```bash
# Check Sphinx installation
python3 -c "import sphinx; print(sphinx.__version__)"

# Check file permissions
ls -la docs/_build/html/

# Check disk space
df -h
```

#### **Stale Documentation**
```bash
# Force clean rebuild
rm -rf docs/_build/html/*
./scripts/build-docs-auto.sh build

# Check file timestamps
stat docs/_build/html/index.html
```

#### **API Docs Not Updating**
```bash
# Check API connectivity
curl -s http://localhost:8000/health

# Manual API docs extraction
curl -s http://localhost:8000/openapi.json > docs/_build/html/api/openapi.json
```

### **Debug Mode**

```bash
# Enable verbose output
DEBUG=true ./scripts/build-docs-auto.sh build

# Check build logs
tail -f /tmp/turdparty-docs.log
```

## 📋 Checklist

### **Before Deployment**
- [ ] Documentation builds without errors
- [ ] All links work correctly
- [ ] API documentation is current
- [ ] Images and assets load properly
- [ ] Mobile responsiveness verified

### **After Deployment**
- [ ] Frontend serves docs at `/docs/`
- [ ] API docs accessible at `/docs/api/`
- [ ] Search functionality works
- [ ] Performance is acceptable
- [ ] Auto-rebuild is functioning

## 🔗 Quick Commands

```bash
# Essential commands for daily use

# Quick rebuild
./scripts/build-docs-auto.sh build

# Start watch mode
./scripts/build-docs-auto.sh watch

# Check documentation status
curl -s http://frontend.turdparty.localhost/docs/ | grep -q "TurdParty" && echo "✅ Docs OK" || echo "❌ Docs Error"

# Restart frontend to pick up changes
docker-compose restart frontend

# Full rebuild pipeline
rm -rf docs/_build/html/* && ./scripts/build-docs-auto.sh build && docker-compose restart frontend
```
