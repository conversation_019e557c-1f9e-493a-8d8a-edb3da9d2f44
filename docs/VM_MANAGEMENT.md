# VM Management Documentation

This document provides comprehensive guidance for the TurdParty VM management system, including Docker containers and Vagrant VMs for malware analysis.

## 🎯 Overview

TurdParty's VM management system provides isolated environments for malware analysis using:
- **Docker Containers** - Lightweight, fast-starting Linux environments
- **Vagrant VMs** - Full virtual machines with complete OS isolation
- **Resource Management** - Configurable memory, CPU, and storage limits
- **Auto-Termination** - 30-minute runtime limits for security
- **File Injection** - Automated malware sample deployment

## 🏗️ Architecture

### VM Management Components

```mermaid
graph TB
    A[API Gateway] --> B[VM Management API]
    B --> C[Celery Workers]
    C --> D[Docker Engine]
    C --> E[Vagrant/VirtualBox]
    B --> F[PostgreSQL Database]
    C --> G[MinIO Storage]
    H[ELK Stack] --> I[Runtime Monitoring]
    D --> I
    E --> I
```

### Service Integration
- **FastAPI** - REST API for VM operations
- **Celery** - Async task processing for VM lifecycle
- **PostgreSQL** - VM state and metadata storage
- **Docker** - Container-based VM backend
- **Vagrant/VirtualBox** - Full VM backend
- **MinIO** - File storage for malware samples
- **ELK Stack** - Runtime monitoring and logging

## 🚀 Quick Start

### Prerequisites
```bash
# Check required services
docker --version && docker info
vagrant --version
VBoxManage --version
curl http://localhost:8000/health/
```

### Create Your First VM
```bash
# Create Docker VM via API
curl -X POST -H "Content-Type: application/json" \
  -d '{
    "name": "analysis-vm-001",
    "template": "ubuntu:20.04",
    "vm_type": "docker",
    "memory_mb": 1024,
    "cpus": 2,
    "domain": "TurdParty",
    "description": "Malware analysis environment"
  }' \
  http://localhost:8000/api/v1/vms/

# Response includes VM ID for tracking
{
  "vm_id": "123e4567-e89b-12d3-a456-426614174000",
  "name": "analysis-vm-001",
  "status": "creating",
  "template": "ubuntu:20.04",
  "vm_type": "docker",
  "domain": "TurdParty",
  "runtime_minutes": 0.0,
  "is_expired": false
}
```

## 📋 VM Templates

### Available Templates

#### Docker Templates
| Template | Description | Use Case | Memory | Boot Time |
|----------|-------------|----------|---------|-----------|
| `ubuntu:20.04` | Ubuntu 20.04 LTS | General malware analysis | 512MB+ | ~10s |
| `ubuntu:22.04` | Ubuntu 22.04 LTS | Modern Linux analysis | 512MB+ | ~10s |
| `alpine:latest` | Alpine Linux | Minimal footprint analysis | 256MB+ | ~5s |
| `centos:7` | CentOS 7 | Enterprise Linux analysis | 1GB+ | ~15s |
| `debian:bullseye` | Debian 11 | Stable Linux analysis | 512MB+ | ~10s |

#### Vagrant Templates
| Template | Description | Use Case | Memory | Boot Time |
|----------|-------------|----------|---------|-----------|
| `ubuntu/focal64` | Ubuntu 20.04 VM | Full OS isolation | 1GB+ | ~2min |
| `ubuntu/jammy64` | Ubuntu 22.04 VM | Modern Linux VM | 1GB+ | ~2min |
| `centos/7` | CentOS 7 VM | Enterprise analysis | 1GB+ | ~3min |
| `gusztavvargadr/windows-10` | Windows 10 VM | Windows malware | 2GB+ | ~5min |

### Template Selection Guide
- **Quick Analysis** → Alpine Docker (`alpine:latest`)
- **Standard Linux** → Ubuntu Docker (`ubuntu:20.04`)
- **Full Isolation** → Ubuntu Vagrant (`ubuntu/focal64`)
- **Windows Malware** → Windows Vagrant (`gusztavvargadr/windows-10`)
- **Enterprise Testing** → CentOS (`centos:7`)

## 🔧 API Reference

### VM Management Endpoints

#### Get VM Templates
```bash
GET /api/v1/vms/templates

# Response: Template catalog with descriptions
[
  {
    "value": "ubuntu:20.04",
    "name": "DOCKER_UBUNTU_2004",
    "description": "Docker Ubuntu 20.04 - Containerized Ubuntu",
    "compatible_vm_types": ["docker"],
    "recommended": true
  }
]
```

#### Create VM
```bash
POST /api/v1/vms/

# Request body:
{
  "name": "string",           # Unique VM name
  "template": "string",       # Template from catalog
  "vm_type": "docker|vagrant", # VM backend type
  "memory_mb": 1024,          # Memory allocation (256-8192)
  "cpus": 2,                  # CPU cores (1-8)
  "disk_gb": 20,              # Disk size (5-100)
  "domain": "TurdParty",      # Must be "TurdParty"
  "description": "string",    # Optional description
  "auto_start": true          # Auto-start after creation
}
```

#### List VMs
```bash
GET /api/v1/vms/?skip=0&limit=10&status=running

# Response: Paginated VM list
{
  "vms": [...],
  "total": 42,
  "skip": 0,
  "limit": 10
}
```

#### Get VM Details
```bash
GET /api/v1/vms/{vm_id}

# Response: Complete VM information
{
  "vm_id": "uuid",
  "name": "string",
  "status": "running",
  "template": "ubuntu:20.04",
  "vm_type": "docker",
  "memory_mb": 1024,
  "cpus": 2,
  "runtime_minutes": 15.5,
  "is_expired": false,
  "ip_address": "**********",
  "created_at": "2025-01-10T10:00:00Z",
  "started_at": "2025-01-10T10:01:00Z"
}
```

#### VM Actions
```bash
POST /api/v1/vms/{vm_id}/action

# Request body:
{
  "action": "start|stop|restart|destroy|suspend|resume",
  "force": false  # Force action if needed
}

# Response: Action confirmation
{
  "vm_id": "uuid",
  "action": "start",
  "status": "running",
  "task_id": "celery-task-id",
  "message": "VM start queued"
}
```

#### Delete VM
```bash
DELETE /api/v1/vms/{vm_id}?force=true

# Response: Deletion confirmation
{
  "vm_id": "uuid",
  "message": "VM deletion queued",
  "task_id": "celery-task-id"
}
```

## 🔄 VM Lifecycle

### VM States
```mermaid
stateDiagram-v2
    [*] --> creating: Create VM
    creating --> running: Start successful
    creating --> failed: Start failed
    running --> terminating: Stop/Destroy
    running --> failed: Runtime error
    terminating --> terminated: Stop complete
    terminated --> [*]
    failed --> [*]
```

### State Descriptions
- **creating** - VM is being provisioned
- **running** - VM is active and accessible
- **terminating** - VM is being stopped/destroyed
- **terminated** - VM has been stopped and cleaned up
- **failed** - VM encountered an error

### Automatic Termination
All VMs are automatically terminated after **30 minutes** of runtime to:
- Prevent resource exhaustion
- Ensure security isolation
- Manage infrastructure costs
- Force analysis completion

## 💉 File Injection

### Injection Workflow
1. **Upload File** → MinIO storage with UUID
2. **Create VM** → Provision analysis environment
3. **Inject File** → Transfer file to VM filesystem
4. **Monitor Execution** → Capture runtime behavior
5. **Collect Results** → Stream data to ELK stack
6. **Terminate VM** → Clean up after analysis

### Injection API
```bash
# Inject file into running VM
POST /api/v1/vms/{vm_id}/inject

# Request body:
{
  "file_uuid": "uuid",        # File UUID from MinIO
  "injection_path": "/tmp/malware.exe",  # Target path in VM
  "execute": true,            # Auto-execute after injection
  "monitor": true             # Enable runtime monitoring
}
```

### Supported File Types
- **PE Executables** (.exe, .dll, .scr)
- **Scripts** (.bat, .ps1, .sh, .py)
- **Documents** (.pdf, .doc, .xls, .ppt)
- **Archives** (.zip, .rar, .7z)
- **Any Binary** (custom analysis)

## 📊 Monitoring & Metrics

### Runtime Monitoring
- **CPU Usage** - Real-time processor utilization
- **Memory Usage** - RAM consumption tracking
- **Network Activity** - Connection monitoring
- **File System** - File creation/modification tracking
- **Process Activity** - Process spawning and behavior

### ELK Integration
```bash
# VM metrics are automatically streamed to:
# - Elasticsearch: Runtime data storage
# - Logstash: Data processing and enrichment  
# - Kibana: Visualization and analysis dashboards
```

### Monitoring APIs
```bash
# Get VM metrics
GET /api/v1/vms/{vm_id}/metrics

# Get VM logs
GET /api/v1/vms/{vm_id}/logs?lines=100

# Get network activity
GET /api/v1/vms/{vm_id}/network
```

## 🔒 Security & Isolation

### Security Features
- **Network Isolation** - VMs run in isolated networks
- **Resource Limits** - Prevent resource exhaustion
- **Auto-Termination** - Forced cleanup after 30 minutes
- **Domain Enforcement** - All VMs must use TurdParty domain
- **File Sandboxing** - Isolated file system access
- **Process Monitoring** - Track all process activity

### Best Practices
1. **Always use TurdParty domain** for VM organization
2. **Set appropriate resource limits** based on analysis needs
3. **Monitor VM runtime** to prevent overruns
4. **Clean up VMs promptly** after analysis completion
5. **Use minimal templates** when possible for faster startup
6. **Enable monitoring** for comprehensive analysis data

## 🛠️ Troubleshooting

### Common Issues

#### VM Creation Fails
```bash
# Check Docker/Vagrant status
docker info
vagrant global-status

# Check API logs
docker logs turdpartycollab_api

# Check worker logs
docker logs turdpartycollab_worker_vm
```

#### VM Stuck in Creating State
```bash
# Check Celery task status
curl http://localhost:5555/api/tasks

# Restart workers
docker-compose restart worker_vm

# Check resource availability
docker system df
```

#### File Injection Fails
```bash
# Verify VM is running
curl http://localhost:8000/api/v1/vms/{vm_id}

# Check MinIO file exists
mc ls minio/turdparty-uploads/{file_uuid}

# Check injection logs
docker logs turdpartycollab_worker_injection
```

### Debug Commands
```bash
# List all TurdParty containers
docker ps --filter "label=turdparty.vm=true"

# Check VM resource usage
docker stats $(docker ps --filter "label=turdparty.vm=true" -q)

# View VM filesystem
docker exec {container_id} ls -la /tmp/

# Check VM network
docker exec {container_id} ip addr show
```

## 📚 Related Documentation

- [API Documentation](http://localhost:8000/docs) - Interactive API reference
- [Testing Guide](./TESTING.md) - VM testing procedures
- [Development Setup](./DEVELOPMENT.md) - Development environment
- [Security Guidelines](./SECURITY.md) - Security best practices
- [Performance Tuning](./PERFORMANCE.md) - Optimization guide

## 🎯 Examples

### Complete Analysis Workflow
```bash
# 1. Create analysis VM
VM_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" \
  -d '{"name":"malware-analysis","template":"ubuntu:20.04","vm_type":"docker","memory_mb":1024,"cpus":2,"domain":"TurdParty"}' \
  http://localhost:8000/api/v1/vms/)

VM_ID=$(echo $VM_RESPONSE | jq -r '.vm_id')

# 2. Wait for VM to be ready
while [ "$(curl -s http://localhost:8000/api/v1/vms/$VM_ID | jq -r '.status')" != "running" ]; do
  echo "Waiting for VM..."
  sleep 5
done

# 3. Inject malware sample
curl -X POST -H "Content-Type: application/json" \
  -d '{"file_uuid":"sample-uuid","injection_path":"/tmp/malware.exe","execute":true,"monitor":true}' \
  http://localhost:8000/api/v1/vms/$VM_ID/inject

# 4. Monitor for 5 minutes
sleep 300

# 5. Collect results
curl http://localhost:8000/api/v1/vms/$VM_ID/logs > analysis_results.log

# 6. Terminate VM
curl -X DELETE http://localhost:8000/api/v1/vms/$VM_ID?force=true
```

This comprehensive VM management system provides enterprise-grade malware analysis capabilities with robust isolation, monitoring, and automation features.
