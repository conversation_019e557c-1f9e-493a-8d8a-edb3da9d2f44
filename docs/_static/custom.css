/* TurdParty Documentation - Minimal Custom Styling for Furo Theme */

/* TurdParty Brand Colors and RAG Status Indicators */
:root {
    /* TurdParty Brand Colors */
    --turdparty-primary: #3498DB;
    --turdparty-secondary: #2ECC71;
    --turdparty-accent: #E74C3C;
    --turdparty-warning: #F39C12;
    --turdparty-success: #27AE60;
    --turdparty-info: #17A2B8;

    /* RAG Status Colors for Mermaid Diagrams */
    --status-red: #dc3545;
    --status-amber: #ffc107;
    --status-green: #28a745;
    --status-blue: #007bff;
    --status-purple: #6f42c1;
    --status-orange: #fd7e14;
}

/* API Endpoint Styling for Documentation */
.api-endpoint {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    font-weight: 600;
    font-size: 0.9em;
}

.websocket-endpoint {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    font-weight: 600;
    font-size: 0.9em;
}

/* HTTP Method Badges */
.http-method {
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    font-weight: bold;
    font-size: 0.8em;
    text-transform: uppercase;
    margin-right: 8px;
}

.http-method.get { background: var(--turdparty-success); color: white; }
.http-method.post { background: var(--turdparty-primary); color: white; }
.http-method.put { background: var(--turdparty-warning); color: white; }
.http-method.delete { background: var(--turdparty-accent); color: white; }
.http-method.patch { background: var(--turdparty-info); color: white; }

/* Mermaid Diagram Integration with RAG Status */
.mermaid {
    text-align: center;
    margin: 20px 0;
}

/* RAG Status Indicators for Service Nodes */
.rag-status-operational { fill: var(--status-green) !important; }
.rag-status-degraded { fill: var(--status-amber) !important; }
.rag-status-outage { fill: var(--status-red) !important; }
.rag-status-maintenance { fill: var(--status-blue) !important; }
.rag-status-unknown { fill: var(--status-purple) !important; }



