/**
 * RAG Status Integration for TurdParty Documentation
 * Integrates real-time service status into Mermaid diagrams
 */

class TurdPartyStatusIntegration {
    constructor() {
        this.statusEndpoint = '/api/v1/status';
        this.statusColors = {
            operational: '#28a745',    // Green
            degraded: '#ffc107',       // Amber  
            outage: '#dc3545',         // Red
            maintenance: '#007bff',    // Blue
            unknown: '#6c757d',        // Gray
            starting: '#17a2b8',       // Cyan
            stopping: '#fd7e14'        // Orange
        };
        
        this.serviceMapping = {
            // Map API service names to Mermaid node IDs
            'api': ['API', 'FastAPI', 'MainApplication'],
            'elasticsearch': ['ES', 'Elasticsearch', 'Search'],
            'logstash': ['LS', 'Logstash', 'LogProcessing'],
            'kibana': ['KB', 'Kibana', 'Visualization'],
            'docker': ['Docker', 'ContainerRuntime'],
            'vagrant': ['Vagrant', 'VMManager'],
            'traefik': ['Traefik', 'ReverseProxy'],
            'frontend': ['Frontend', 'ReactApp', 'WebUI'],
            'docs': ['Docs', 'Documentation']
        };
        
        this.init();
    }
    
    init() {
        this.setupStatusPolling();
        this.setupMermaidIntegration();
        
        // Listen for theme changes to update colors
        window.addEventListener('themeChanged', () => {
            this.updateAllDiagrams();
        });
    }
    
    async fetchSystemStatus() {
        try {
            const response = await fetch(this.statusEndpoint);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.warn('Could not fetch system status:', error);
            return this.getMockStatus(); // Fallback to mock data
        }
    }
    
    getMockStatus() {
        // Mock status data based on your status dashboard
        return {
            services: {
                api: { status: 'operational', response_time_ms: 45 },
                elasticsearch: { status: 'operational', response_time_ms: 12 },
                logstash: { status: 'operational', response_time_ms: 8 },
                kibana: { status: 'operational', response_time_ms: 156 },
                docker: { status: 'operational', response_time_ms: 5 },
                vagrant: { status: 'degraded', response_time_ms: 2000 },
                traefik: { status: 'operational', response_time_ms: 3 },
                frontend: { status: 'operational', response_time_ms: 25 },
                docs: { status: 'operational', response_time_ms: 15 }
            },
            system: {
                cpu_percent: 25.3,
                memory_percent: 67.8,
                disk_percent: 45.2
            },
            vms: {
                total: 6,
                running: 6,
                stopped: 0,
                error: 0
            }
        };
    }
    
    setupStatusPolling() {
        // Initial status fetch
        this.updateStatus();
        
        // Poll every 30 seconds
        setInterval(() => {
            this.updateStatus();
        }, 30000);
    }
    
    async updateStatus() {
        const status = await this.fetchSystemStatus();
        this.currentStatus = status;
        this.updateAllDiagrams();
        
        // Dispatch event for other components
        window.dispatchEvent(new CustomEvent('statusUpdated', { 
            detail: status 
        }));
    }
    
    setupMermaidIntegration() {
        // Wait for Mermaid to be available
        const checkMermaid = () => {
            if (typeof mermaid !== 'undefined') {
                this.enhanceMermaidDiagrams();
            } else {
                setTimeout(checkMermaid, 500);
            }
        };
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', checkMermaid);
        } else {
            checkMermaid();
        }
    }
    
    enhanceMermaidDiagrams() {
        // Find all mermaid diagrams
        const diagrams = document.querySelectorAll('.mermaid');
        
        diagrams.forEach((diagram, index) => {
            // Add unique ID if not present
            if (!diagram.id) {
                diagram.id = `mermaid-diagram-${index}`;
            }
            
            // Store original content
            if (!diagram.getAttribute('data-original-content')) {
                diagram.setAttribute('data-original-content', diagram.textContent);
            }
            
            // Add status integration
            this.addStatusToDiagram(diagram);
        });
    }
    
    addStatusToDiagram(diagram) {
        const originalContent = diagram.getAttribute('data-original-content');
        if (!originalContent) return;
        
        // Parse the diagram content and add status indicators
        let enhancedContent = this.enhanceDiagramContent(originalContent);
        
        // Update diagram content
        diagram.textContent = enhancedContent;
        diagram.removeAttribute('data-processed');
        
        // Re-render with Mermaid
        if (typeof mermaid !== 'undefined') {
            mermaid.init(undefined, diagram);
        }
        
        // Add click handlers for status details
        setTimeout(() => {
            this.addStatusClickHandlers(diagram);
        }, 1000);
    }
    
    enhanceDiagramContent(content) {
        if (!this.currentStatus) return content;
        
        let enhanced = content;
        
        // Add status colors to service nodes
        Object.entries(this.serviceMapping).forEach(([service, nodeIds]) => {
            const serviceStatus = this.currentStatus.services?.[service];
            if (!serviceStatus) return;
            
            const statusColor = this.statusColors[serviceStatus.status] || this.statusColors.unknown;
            
            nodeIds.forEach(nodeId => {
                // Add status styling to nodes
                const nodePattern = new RegExp(`(${nodeId}\\[.*?\\])`, 'g');
                enhanced = enhanced.replace(nodePattern, (match) => {
                    return `${match}:::status-${serviceStatus.status}`;
                });
                
                // Add classDef for status colors
                if (!enhanced.includes(`classDef status-${serviceStatus.status}`)) {
                    enhanced += `\n    classDef status-${serviceStatus.status} fill:${statusColor},stroke:#333,stroke-width:2px,color:#fff`;
                }
            });
        });
        
        return enhanced;
    }
    
    addStatusClickHandlers(diagram) {
        // Add click handlers to show detailed status
        const nodes = diagram.querySelectorAll('g.node');
        
        nodes.forEach(node => {
            const nodeText = node.textContent;
            const service = this.findServiceByNodeText(nodeText);
            
            if (service && this.currentStatus?.services?.[service]) {
                node.style.cursor = 'pointer';
                node.setAttribute('title', this.getStatusTooltip(service));
                
                node.addEventListener('click', () => {
                    this.showStatusDetails(service, node);
                });
            }
        });
    }
    
    findServiceByNodeText(nodeText) {
        for (const [service, nodeIds] of Object.entries(this.serviceMapping)) {
            if (nodeIds.some(nodeId => nodeText.includes(nodeId))) {
                return service;
            }
        }
        return null;
    }
    
    getStatusTooltip(service) {
        const serviceStatus = this.currentStatus?.services?.[service];
        if (!serviceStatus) return 'Status unknown';
        
        const status = serviceStatus.status;
        const responseTime = serviceStatus.response_time_ms;
        
        return `${service}: ${status}${responseTime ? ` (${responseTime}ms)` : ''}`;
    }
    
    showStatusDetails(service, node) {
        const serviceStatus = this.currentStatus?.services?.[service];
        if (!serviceStatus) return;
        
        // Create status popup
        const popup = document.createElement('div');
        popup.className = 'status-popup';
        popup.innerHTML = `
            <div class="status-popup-header">
                <h4>${service.charAt(0).toUpperCase() + service.slice(1)} Status</h4>
                <button class="status-popup-close">&times;</button>
            </div>
            <div class="status-popup-content">
                <div class="status-item">
                    <span class="status-label">Status:</span>
                    <span class="status-value status-${serviceStatus.status}">${serviceStatus.status}</span>
                </div>
                ${serviceStatus.response_time_ms ? `
                <div class="status-item">
                    <span class="status-label">Response Time:</span>
                    <span class="status-value">${serviceStatus.response_time_ms}ms</span>
                </div>
                ` : ''}
                ${serviceStatus.last_check ? `
                <div class="status-item">
                    <span class="status-label">Last Check:</span>
                    <span class="status-value">${new Date(serviceStatus.last_check).toLocaleTimeString()}</span>
                </div>
                ` : ''}
                ${serviceStatus.error_message ? `
                <div class="status-item">
                    <span class="status-label">Error:</span>
                    <span class="status-value status-error">${serviceStatus.error_message}</span>
                </div>
                ` : ''}
            </div>
        `;
        
        // Position popup near the clicked node
        const rect = node.getBoundingClientRect();
        popup.style.position = 'fixed';
        popup.style.left = `${rect.right + 10}px`;
        popup.style.top = `${rect.top}px`;
        popup.style.zIndex = '1000';
        
        document.body.appendChild(popup);
        
        // Close popup handlers
        const closeBtn = popup.querySelector('.status-popup-close');
        const closePopup = () => {
            document.body.removeChild(popup);
        };
        
        closeBtn.addEventListener('click', closePopup);
        
        // Close on outside click
        setTimeout(() => {
            document.addEventListener('click', function outsideClick(e) {
                if (!popup.contains(e.target)) {
                    closePopup();
                    document.removeEventListener('click', outsideClick);
                }
            });
        }, 100);
        
        // Auto-close after 10 seconds
        setTimeout(closePopup, 10000);
    }
    
    updateAllDiagrams() {
        const diagrams = document.querySelectorAll('.mermaid[data-original-content]');
        diagrams.forEach(diagram => {
            this.addStatusToDiagram(diagram);
        });
    }
    
    // Public method to manually update a specific diagram
    updateDiagram(diagramId) {
        const diagram = document.getElementById(diagramId);
        if (diagram) {
            this.addStatusToDiagram(diagram);
        }
    }
}

// CSS for status popups and indicators
const statusCSS = `
.status-popup {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    min-width: 250px;
    max-width: 400px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.status-popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-tertiary);
    border-radius: 8px 8px 0 0;
}

.status-popup-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.status-popup-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 18px;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.status-popup-close:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.status-popup-content {
    padding: 12px 16px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.status-value {
    color: var(--text-primary);
    font-weight: 600;
}

.status-value.status-operational {
    color: var(--status-green);
}

.status-value.status-degraded {
    color: var(--status-amber);
}

.status-value.status-outage {
    color: var(--status-red);
}

.status-value.status-maintenance {
    color: var(--status-blue);
}

.status-value.status-error {
    color: var(--status-red);
    font-size: 12px;
    max-width: 200px;
    word-wrap: break-word;
}

/* Mermaid node hover effects */
.mermaid g.node[title] {
    transition: all 0.2s ease;
}

.mermaid g.node[title]:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
}
`;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Inject CSS
    const style = document.createElement('style');
    style.textContent = statusCSS;
    document.head.appendChild(style);
    
    // Initialize status integration
    window.turdPartyStatus = new TurdPartyStatusIntegration();
});

// Export for external use
window.TurdPartyStatusIntegration = TurdPartyStatusIntegration;
