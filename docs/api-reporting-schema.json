{"binary_report": {"metadata": {"report_id": "rpt_148f183a-38f6-4d09-9433-30e14db058ce", "file_uuid": "148f183a-38f6-4d09-9433-30e14db058ce", "generated_at": "2025-06-12T09:30:00Z", "report_version": "1.0", "data_sources": ["elasticsearch", "minio"], "analysis_duration_seconds": 1.234}, "file_info": {"filename": "VSCodeSetup-x64.exe", "original_filename": "VSCodeSetup-x64.exe", "file_size_bytes": 108253352, "file_type": "PE32+ executable", "mime_type": "application/x-dosexec", "hashes": {"blake3": "d33e5108c34b1167a576343c44bdb577d4d9e774a1886d4d8359384f59a2ab58", "sha256": "a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890", "md5": "12345678901234567890123456789012"}, "upload_timestamp": "2025-06-12T09:10:23Z", "minio_location": "turdparty-uploads/148f183a-38f6-4d09-9433-30e14db058ce/VSCodeSetup-x64.exe"}, "execution_summary": {"total_executions": 1, "successful_executions": 1, "failed_executions": 0, "last_execution": "2025-06-12T09:15:30Z", "total_runtime_seconds": 45.7, "vm_environments": ["windows-10-enterprise"], "execution_status": "completed"}, "installation_footprint": {"filesystem_changes": {"files_created": 156, "files_modified": 23, "files_deleted": 2, "directories_created": 12, "total_disk_usage_mb": 245.8, "installation_paths": ["C:\\Program Files\\Microsoft VS Code\\", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\"], "critical_files": [{"path": "C:\\Program Files\\Microsoft VS Code\\Code.exe", "size_bytes": 156789012, "created_at": "2025-06-12T09:15:35Z", "file_type": "executable"}]}, "registry_changes": {"keys_created": 45, "keys_modified": 12, "keys_deleted": 0, "registry_paths": ["HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{EA457B21-F73E-494C-ACAB-524FDE069978}_is1", "HKEY_CURRENT_USER\\SOFTWARE\\Classes\\Applications\\Code.exe"], "startup_entries": [{"key": "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", "value": "VSCode", "data": "C:\\Program Files\\Microsoft VS Code\\Code.exe --startup"}]}, "services_installed": [{"service_name": "VSCodeUpdater", "display_name": "Visual Studio Code Updater", "start_type": "automatic", "status": "running", "executable_path": "C:\\Program Files\\Microsoft VS Code\\updater.exe"}], "network_configuration": {"firewall_rules_added": 2, "ports_opened": [], "outbound_connections_attempted": 5}}, "runtime_behavior": {"process_execution": {"main_process": {"process_name": "VSCodeSetup-x64.exe", "pid": 1234, "parent_pid": 567, "command_line": "VSCodeSetup-x64.exe /VERYSILENT /NORESTART /MERGETASKS=!runcode", "start_time": "2025-06-12T09:15:30Z", "end_time": "2025-06-12T09:16:15Z", "exit_code": 0, "cpu_usage_percent": 15.2, "memory_usage_mb": 89.5}, "child_processes": [{"process_name": "msiexec.exe", "pid": 1567, "command_line": "msiexec.exe /i VSCode.msi /quiet", "duration_seconds": 30.2, "exit_code": 0}], "total_processes_spawned": 3}, "network_activity": {"connections_established": 5, "data_transmitted_bytes": 1024000, "data_received_bytes": 2048000, "dns_queries": ["update.code.visualstudio.com", "vscode.download.prss.microsoft.com"], "external_ips_contacted": ["************", "*************"]}, "resource_usage": {"peak_cpu_percent": 25.8, "peak_memory_mb": 156.7, "disk_io_read_mb": 45.2, "disk_io_write_mb": 245.8, "execution_duration_seconds": 45.7}}, "security_analysis": {"threat_indicators": {"suspicious_behavior_score": 2, "risk_level": "low", "indicators": [{"type": "network_connection", "description": "Connects to Microsoft update servers", "severity": "info", "count": 5}]}, "behavioral_patterns": {"installation_behavior": "standard_installer", "persistence_mechanisms": ["startup_registry"], "privilege_escalation": false, "code_injection": false, "anti_analysis": false}, "file_reputation": {"known_good": true, "digital_signature": {"signed": true, "signer": "Microsoft Corporation", "valid": true, "timestamp": "2024-11-15T10:30:00Z"}, "virus_total_score": "0/70", "reputation_sources": ["microsoft", "virus_total"]}}, "vm_environment": {"vm_id": "vm_d5a163ce", "vm_template": "gusztavvargadr/windows-10", "vm_configuration": {"memory_mb": 4096, "cpus": 2, "disk_gb": 40, "os_version": "Windows 10 Enterprise 21H2"}, "execution_environment": {"user_context": "Administrator", "working_directory": "C:\\temp", "environment_variables": {"TEMP": "C:\\temp", "PATH": "C:\\Windows\\system32;C:\\Windows"}}, "vm_lifecycle": {"created_at": "2025-06-12T09:14:00Z", "execution_started": "2025-06-12T09:15:30Z", "execution_completed": "2025-06-12T09:16:15Z", "destroyed_at": "2025-06-12T09:17:00Z", "total_vm_runtime_seconds": 180}}, "ecs_data_summary": {"total_log_entries": 1247, "log_sources": ["vm-manager", "file-monitor", "process-monitor", "network-monitor"], "event_categories": {"file": 156, "process": 45, "network": 23, "registry": 57, "authentication": 12}, "data_collection_period": {"start": "2025-06-12T09:15:30Z", "end": "2025-06-12T09:16:15Z", "duration_seconds": 45}}, "artifacts": {"screenshots": [{"timestamp": "2025-06-12T09:15:45Z", "description": "Installation progress dialog", "url": "http://minio.turdparty.localhost/turdparty-artifacts/148f183a-38f6-4d09-9433-30e14db058ce/screenshot_001.png"}], "memory_dumps": [{"timestamp": "2025-06-12T09:16:00Z", "process": "VSCodeSetup-x64.exe", "size_mb": 89.5, "url": "http://minio.turdparty.localhost/turdparty-artifacts/148f183a-38f6-4d09-9433-30e14db058ce/memory_dump.dmp"}], "log_files": [{"name": "installation.log", "size_bytes": 15678, "url": "http://minio.turdparty.localhost/turdparty-artifacts/148f183a-38f6-4d09-9433-30e14db058ce/installation.log"}]}}}