File Processing API
===================

The File Processing API provides endpoints for uploading, managing, and tracking files through the TurdParty malware analysis pipeline.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

The file processing system handles:

- **File Upload**: Secure file upload with UUID generation
- **File Validation**: Integrity checking and format validation
- **File Storage**: Secure storage in MinIO with metadata tracking
- **File Retrieval**: Download processed files and results
- **File Lifecycle**: Automatic cleanup and retention management

File Upload
-----------

Upload File for Analysis
~~~~~~~~~~~~~~~~~~~~~~~~

**Endpoint**: ``POST /api/v1/files/upload``

Upload a file for malware analysis.

**Request**:

.. code-block:: http

   POST /api/v1/files/upload HTTP/1.1
   Content-Type: multipart/form-data
   
   --boundary
   Content-Disposition: form-data; name="file"; filename="malware.exe"
   Content-Type: application/octet-stream
   
   [binary file data]
   --boundary
   Content-Disposition: form-data; name="filename"
   
   malware.exe
   --boundary--

**Parameters**:

.. list-table:: Upload Parameters
   :widths: 20 20 60
   :header-rows: 1

   * - Parameter
     - Type
     - Description
   * - ``file``
     - File
     - Binary file data (required)
   * - ``filename``
     - String
     - Original filename (optional, extracted from file if not provided)
   * - ``description``
     - String
     - File description or notes (optional)
   * - ``tags``
     - Array[String]
     - Classification tags (optional)

**Response**:

.. code-block:: json

   {
     "file_id": "550e8400-e29b-41d4-a716-************",
     "filename": "malware.exe",
     "size": 1048576,
     "content_type": "application/octet-stream",
     "blake3_hash": "af1349b9f5f9a1a6a0404dea36dcc9499bcb25c9adc112b7cc9a93cae41f3262",
     "upload_timestamp": "2024-01-15T10:30:00Z",
     "status": "uploaded",
     "storage_path": "files/550e8400-e29b-41d4-a716-************"
   }

**Example Usage**:

.. code-block:: python

   import httpx
   
   async def upload_file(file_path: str):
       async with httpx.AsyncClient() as client:
           with open(file_path, 'rb') as f:
               files = {'file': f}
               data = {'filename': 'malware.exe'}
               
               response = await client.post(
                   'http://localhost:8000/api/v1/files/upload',
                   files=files,
                   data=data
               )
               
               return response.json()

.. code-block:: bash

   # cURL example
   curl -X POST \
     -F "file=@malware.exe" \
     -F "filename=malware.exe" \
     http://localhost:8000/api/v1/files/upload

**Error Responses**:

.. list-table:: Upload Error Codes
   :widths: 20 80
   :header-rows: 1

   * - Status Code
     - Description
   * - ``400 Bad Request``
     - Invalid file format, missing file, or file too large
   * - ``413 Payload Too Large``
     - File exceeds maximum size limit
   * - ``415 Unsupported Media Type``
     - File type not allowed
   * - ``500 Internal Server Error``
     - Storage or processing error

File Management
---------------

Get File Information
~~~~~~~~~~~~~~~~~~~~

**Endpoint**: ``GET /api/v1/files/{file_id}``

Retrieve detailed information about an uploaded file.

**Parameters**:

.. list-table:: Path Parameters
   :widths: 20 20 60
   :header-rows: 1

   * - Parameter
     - Type
     - Description
   * - ``file_id``
     - UUID
     - Unique file identifier

**Response**:

.. code-block:: json

   {
     "file_id": "550e8400-e29b-41d4-a716-************",
     "filename": "malware.exe",
     "original_filename": "suspicious_file.exe",
     "size": 1048576,
     "content_type": "application/octet-stream",
     "blake3_hash": "af1349b9f5f9a1a6a0404dea36dcc9499bcb25c9adc112b7cc9a93cae41f3262",
     "md5_hash": "5d41402abc4b2a76b9719d911017c592",
     "sha1_hash": "aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d",
     "sha256_hash": "2c26b46b68ffc68ff99b453c1d30413413422d706483bfa0f98a5e886266e7ae",
     "upload_timestamp": "2024-01-15T10:30:00Z",
     "last_accessed": "2024-01-15T10:35:00Z",
     "status": "processed",
     "analysis_count": 3,
     "tags": ["malware", "trojan"],
     "metadata": {
       "file_type": "PE32 executable",
       "architecture": "x86",
       "compiler": "Microsoft Visual C++",
       "entropy": 7.8
     }
   }

List Files
~~~~~~~~~~

**Endpoint**: ``GET /api/v1/files/``

List uploaded files with filtering and pagination.

**Query Parameters**:

.. list-table:: Query Parameters
   :widths: 20 20 60
   :header-rows: 1

   * - Parameter
     - Type
     - Description
   * - ``limit``
     - Integer
     - Maximum number of results (default: 50, max: 1000)
   * - ``offset``
     - Integer
     - Number of results to skip (default: 0)
   * - ``status``
     - String
     - Filter by status (uploaded, processing, processed, failed)
   * - ``filename``
     - String
     - Filter by filename (partial match)
   * - ``hash``
     - String
     - Filter by any hash value
   * - ``tags``
     - String
     - Filter by tags (comma-separated)
   * - ``uploaded_after``
     - DateTime
     - Filter files uploaded after this date
   * - ``uploaded_before``
     - DateTime
     - Filter files uploaded before this date

**Response**:

.. code-block:: json

   {
     "files": [
       {
         "file_id": "550e8400-e29b-41d4-a716-************",
         "filename": "malware.exe",
         "size": 1048576,
         "blake3_hash": "af1349b9f5f9a1a6a0404dea36dcc9499bcb25c9adc112b7cc9a93cae41f3262",
         "upload_timestamp": "2024-01-15T10:30:00Z",
         "status": "processed",
         "analysis_count": 3
       }
     ],
     "total": 1,
     "limit": 50,
     "offset": 0,
     "has_more": false
   }

File Download
-------------

Download Original File
~~~~~~~~~~~~~~~~~~~~~~

**Endpoint**: ``GET /api/v1/files/{file_id}/download``

Download the original uploaded file.

**Parameters**:

.. list-table:: Path Parameters
   :widths: 20 20 60
   :header-rows: 1

   * - Parameter
     - Type
     - Description
   * - ``file_id``
     - UUID
     - Unique file identifier

**Response**:

Returns the original file as binary data with appropriate headers.

**Headers**:

.. code-block:: http

   Content-Type: application/octet-stream
   Content-Disposition: attachment; filename="malware.exe"
   Content-Length: 1048576
   X-File-Hash: af1349b9f5f9a1a6a0404dea36dcc9499bcb25c9adc112b7cc9a93cae41f3262

Download Analysis Results
~~~~~~~~~~~~~~~~~~~~~~~~~

**Endpoint**: ``GET /api/v1/files/{file_id}/results``

Download analysis results and reports.

**Query Parameters**:

.. list-table:: Query Parameters
   :widths: 20 20 60
   :header-rows: 1

   * - Parameter
     - Type
     - Description
   * - ``format``
     - String
     - Result format (json, xml, pdf, html) - default: json
   * - ``include_logs``
     - Boolean
     - Include analysis logs - default: false
   * - ``include_artifacts``
     - Boolean
     - Include extracted artifacts - default: false

**Response** (JSON format):

.. code-block:: json

   {
     "file_id": "550e8400-e29b-41d4-a716-************",
     "analysis_id": "analysis-123",
     "analysis_timestamp": "2024-01-15T10:35:00Z",
     "analysis_duration": 1800,
     "threat_level": "high",
     "confidence_score": 85,
     "indicators": [
       {
         "type": "file_hash",
         "value": "af1349b9f5f9a1a6a0404dea36dcc9499bcb25c9adc112b7cc9a93cae41f3262",
         "confidence": 100
       },
       {
         "type": "network_connection",
         "value": "*************:443",
         "confidence": 75
       }
     ],
     "behavior": {
       "file_operations": 15,
       "network_connections": 3,
       "process_creations": 2,
       "registry_modifications": 8
     },
     "vm_metrics": {
       "max_cpu_usage": 45.2,
       "max_memory_usage": 67.8,
       "network_bytes_sent": 1024,
       "network_bytes_received": 2048
     }
   }

File Operations
---------------

Update File Metadata
~~~~~~~~~~~~~~~~~~~~

**Endpoint**: ``PATCH /api/v1/files/{file_id}``

Update file metadata and tags.

**Request**:

.. code-block:: json

   {
     "filename": "updated_malware.exe",
     "description": "Updated description",
     "tags": ["malware", "trojan", "updated"]
   }

**Response**:

.. code-block:: json

   {
     "file_id": "550e8400-e29b-41d4-a716-************",
     "filename": "updated_malware.exe",
     "description": "Updated description",
     "tags": ["malware", "trojan", "updated"],
     "updated_timestamp": "2024-01-15T11:00:00Z"
   }

Delete File
~~~~~~~~~~~

**Endpoint**: ``DELETE /api/v1/files/{file_id}``

Delete a file and all associated data.

**Parameters**:

.. list-table:: Query Parameters
   :widths: 20 20 60
   :header-rows: 1

   * - Parameter
     - Type
     - Description
   * - ``force``
     - Boolean
     - Force deletion even if analysis is running - default: false

**Response**:

.. code-block:: json

   {
     "file_id": "550e8400-e29b-41d4-a716-************",
     "deleted": true,
     "deleted_timestamp": "2024-01-15T11:30:00Z"
   }

File Validation
---------------

Validate File Hash
~~~~~~~~~~~~~~~~~~

**Endpoint**: ``POST /api/v1/files/validate``

Validate file integrity using hash comparison.

**Request**:

.. code-block:: json

   {
     "file_id": "550e8400-e29b-41d4-a716-************",
     "expected_hash": "af1349b9f5f9a1a6a0404dea36dcc9499bcb25c9adc112b7cc9a93cae41f3262",
     "hash_type": "blake3"
   }

**Response**:

.. code-block:: json

   {
     "file_id": "550e8400-e29b-41d4-a716-************",
     "valid": true,
     "expected_hash": "af1349b9f5f9a1a6a0404dea36dcc9499bcb25c9adc112b7cc9a93cae41f3262",
     "actual_hash": "af1349b9f5f9a1a6a0404dea36dcc9499bcb25c9adc112b7cc9a93cae41f3262",
     "hash_type": "blake3",
     "validation_timestamp": "2024-01-15T11:45:00Z"
   }

Bulk Operations
---------------

Bulk File Upload
~~~~~~~~~~~~~~~~

**Endpoint**: ``POST /api/v1/files/bulk-upload``

Upload multiple files in a single request.

**Request**:

.. code-block:: http

   POST /api/v1/files/bulk-upload HTTP/1.1
   Content-Type: multipart/form-data
   
   --boundary
   Content-Disposition: form-data; name="files"; filename="file1.exe"
   Content-Type: application/octet-stream
   
   [binary file data 1]
   --boundary
   Content-Disposition: form-data; name="files"; filename="file2.exe"
   Content-Type: application/octet-stream
   
   [binary file data 2]
   --boundary--

**Response**:

.. code-block:: json

   {
     "uploaded_files": [
       {
         "file_id": "550e8400-e29b-41d4-a716-************",
         "filename": "file1.exe",
         "status": "uploaded"
       },
       {
         "file_id": "550e8400-e29b-41d4-a716-446655440001",
         "filename": "file2.exe",
         "status": "uploaded"
       }
     ],
     "failed_files": [],
     "total_uploaded": 2,
     "total_failed": 0
   }

Bulk File Operations
~~~~~~~~~~~~~~~~~~~

**Endpoint**: ``POST /api/v1/files/bulk-operation``

Perform bulk operations on multiple files.

**Request**:

.. code-block:: json

   {
     "operation": "delete",
     "file_ids": [
       "550e8400-e29b-41d4-a716-************",
       "550e8400-e29b-41d4-a716-446655440001"
     ],
     "options": {
       "force": false
     }
   }

**Response**:

.. code-block:: json

   {
     "operation": "delete",
     "successful": [
       "550e8400-e29b-41d4-a716-************"
     ],
     "failed": [
       {
         "file_id": "550e8400-e29b-41d4-a716-446655440001",
         "error": "File is currently being analyzed"
       }
     ],
     "total_successful": 1,
     "total_failed": 1
   }

Error Handling
--------------

Common Error Responses
~~~~~~~~~~~~~~~~~~~~~~

**File Not Found**:

.. code-block:: json

   {
     "error": "file_not_found",
     "message": "File with ID 550e8400-e29b-41d4-a716-************ not found",
     "file_id": "550e8400-e29b-41d4-a716-************"
   }

**File Too Large**:

.. code-block:: json

   {
     "error": "file_too_large",
     "message": "File size exceeds maximum limit of 1GB",
     "max_size": 1073741824,
     "actual_size": 2147483648
   }

**Invalid File Type**:

.. code-block:: json

   {
     "error": "invalid_file_type",
     "message": "File type not allowed",
     "allowed_types": ["*"],
     "detected_type": "text/plain"
   }

Rate Limiting
-------------

The File Processing API implements rate limiting to prevent abuse:

- **Upload Rate**: 10 files per minute per IP
- **Download Rate**: 50 requests per minute per IP
- **API Rate**: 100 requests per minute per IP

Rate limit headers are included in responses:

.. code-block:: http

   X-RateLimit-Limit: 10
   X-RateLimit-Remaining: 7
   X-RateLimit-Reset: 1642248000

Best Practices
--------------

File Upload Guidelines
~~~~~~~~~~~~~~~~~~~~~~

1. **Validate Files Locally**: Check file size and type before upload
2. **Use Meaningful Filenames**: Provide descriptive filenames
3. **Add Metadata**: Include tags and descriptions for better organization
4. **Monitor Upload Progress**: Implement progress tracking for large files
5. **Handle Errors Gracefully**: Implement retry logic for failed uploads

Security Considerations
~~~~~~~~~~~~~~~~~~~~~~~

1. **File Scanning**: All uploaded files are scanned for malware
2. **Sandboxed Analysis**: Files are analyzed in isolated environments
3. **Access Control**: Implement proper authentication and authorization
4. **Audit Logging**: All file operations are logged for security auditing
5. **Data Retention**: Configure appropriate data retention policies

For more information on workflow management, see :doc:`workflow_management`.
For VM management details, see :doc:`vm_management`.
For troubleshooting file processing issues, see :doc:`../reference/troubleshooting`.
