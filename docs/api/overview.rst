API Overview
============

The TurdParty VM WebSocket API provides a comprehensive interface for managing virtual machines, executing commands, monitoring performance, and handling file operations in a malware analysis environment.

Base URL
--------

.. code-block:: text

   http://localhost:8000

All API endpoints are prefixed with ``/api/v1/`` for versioning.

Authentication
--------------

Currently, the API operates without authentication for development purposes. In production deployments, authentication is handled by <PERSON><PERSON><PERSON><PERSON> at the reverse proxy level.

.. warning::
   Ensure proper authentication and authorisation are configured before deploying to production environments.

Content Types
-------------

The API accepts and returns JSON data:

- **Request Content-Type**: ``application/json``
- **Response Content-Type**: ``application/json``
- **WebSocket Protocol**: ``ws://`` or ``wss://`` for secure connections

Rate Limiting
-------------

The API implements intelligent rate limiting to ensure system stability:

- **REST Endpoints**: 100 requests per minute per IP
- **WebSocket Connections**: 10 concurrent connections per IP
- **File Uploads**: 5 concurrent uploads per VM

Error Handling
--------------

All API responses follow a consistent error format:

.. code-block:: json

   {
       "error": {
           "code": "VM_NOT_FOUND",
           "message": "Virtual machine with ID 'vm-123' not found",
           "details": {
               "vm_id": "vm-123",
               "timestamp": "2025-06-11T07:30:00Z",
               "request_id": "req-456"
           }
       }
   }

HTTP Status Codes
-----------------

The API uses standard HTTP status codes:

.. list-table::
   :widths: 10 20 70
   :header-rows: 1

   * - Code
     - Status
     - Description
   * - 200
     - OK
     - Request successful
   * - 201
     - Created
     - Resource created successfully
   * - 400
     - Bad Request
     - Invalid request parameters
   * - 404
     - Not Found
     - Resource not found
   * - 409
     - Conflict
     - Resource already exists
   * - 422
     - Unprocessable Entity
     - Validation error
   * - 500
     - Internal Server Error
     - Server error occurred

Request/Response Format
-----------------------

**Request Headers**

.. code-block:: http

   POST /api/v1/vms/ HTTP/1.1
   Host: localhost:8000
   Content-Type: application/json
   Accept: application/json
   User-Agent: TurdParty-Client/1.0

**Response Headers**

.. code-block:: http

   HTTP/1.1 201 Created
   Content-Type: application/json
   X-Request-ID: req-789
   X-Response-Time: 0.045s

Pagination
----------

List endpoints support pagination using query parameters:

.. code-block:: http

   GET /api/v1/vms/?page=1&limit=20&sort=created_at&order=desc

**Pagination Response**

.. code-block:: json

   {
       "data": [...],
       "pagination": {
           "page": 1,
           "limit": 20,
           "total": 150,
           "pages": 8,
           "has_next": true,
           "has_prev": false
       }
   }

Filtering and Sorting
---------------------

Most list endpoints support filtering and sorting:

**Filtering**

.. code-block:: http

   GET /api/v1/vms/?status=running&vm_type=docker&created_after=2025-06-01

**Sorting**

.. code-block:: http

   GET /api/v1/vms/?sort=memory_mb&order=desc

**Available Filters**

.. list-table::
   :widths: 20 20 60
   :header-rows: 1

   * - Parameter
     - Type
     - Description
   * - ``status``
     - string
     - Filter by VM status (running, stopped, error)
   * - ``vm_type``
     - string
     - Filter by VM type (docker, vagrant)
   * - ``template``
     - string
     - Filter by VM template
   * - ``created_after``
     - datetime
     - Filter VMs created after date
   * - ``created_before``
     - datetime
     - Filter VMs created before date

WebSocket Protocol
------------------

WebSocket connections follow a structured message format:

**Connection**

.. code-block:: javascript

   const ws = new WebSocket('ws://localhost:8000/api/v1/vms/vm-id/metrics/stream');

**Message Format**

.. code-block:: json

   {
       "type": "metrics_data",
       "timestamp": "2025-06-11T07:30:00Z",
       "vm_id": "vm-123",
       "data": {
           "cpu_percent": 45.2,
           "memory_percent": 67.8,
           "disk_io": {
               "read_bytes": 1024000,
               "write_bytes": 512000
           }
       }
   }

**Connection States**

.. list-table::
   :widths: 20 80
   :header-rows: 1

   * - State
     - Description
   * - ``connecting``
     - WebSocket connection being established
   * - ``connected``
     - Connection established, ready for data
   * - ``streaming``
     - Actively streaming data
   * - ``paused``
     - Streaming paused (client request)
   * - ``error``
     - Connection error occurred
   * - ``closed``
     - Connection closed gracefully

API Versioning
--------------

The API uses URL-based versioning:

- **Current Version**: ``v1``
- **Base Path**: ``/api/v1/``
- **Deprecation Policy**: 6 months notice for breaking changes

**Version Headers**

.. code-block:: http

   X-API-Version: v1
   X-API-Deprecated: false
   X-API-Sunset: 2026-01-01

Performance Considerations
--------------------------

**Request Optimisation**

- Use HTTP/2 for improved performance
- Implement client-side caching for static data
- Batch multiple operations when possible
- Use WebSocket connections for real-time data

**Response Compression**

The API supports gzip compression:

.. code-block:: http

   Accept-Encoding: gzip, deflate

**Connection Pooling**

Recommended client configuration:

.. code-block:: python

   import httpx

   # Recommended client setup
   client = httpx.AsyncClient(
       timeout=30.0,
       limits=httpx.Limits(
           max_keepalive_connections=20,
           max_connections=100
       )
   )

Monitoring and Observability
----------------------------

**Request Tracing**

Every request includes a unique request ID:

.. code-block:: http

   X-Request-ID: req-abc123

**Metrics Endpoints**

.. code-block:: http

   GET /metrics          # Prometheus metrics
   GET /health           # Health check
   GET /api/v1/status    # Detailed system status

**Logging**

All API requests are logged in ECS format for analysis:

.. code-block:: json

   {
       "@timestamp": "2025-06-11T07:30:00Z",
       "http.request.method": "POST",
       "url.path": "/api/v1/vms/",
       "http.response.status_code": 201,
       "performance.response_time_ms": 45.2,
       "labels.request_id": "req-abc123"
   }

SDK and Client Libraries
------------------------

**Python SDK**

.. code-block:: bash

   pip install turdparty-sdk

.. code-block:: python

   from turdparty import TurdPartyClient

   client = TurdPartyClient(base_url="http://localhost:8000")
   vm = await client.vms.create(name="test-vm", template="ubuntu:20.04")

**JavaScript SDK**

.. code-block:: bash

   npm install @turdparty/sdk

.. code-block:: javascript

   import { TurdPartyClient } from '@turdparty/sdk';

   const client = new TurdPartyClient('http://localhost:8000');
   const vm = await client.vms.create({
       name: 'test-vm',
       template: 'ubuntu:20.04'
   });

Interactive Documentation
-------------------------

The API provides interactive documentation:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI Spec**: http://localhost:8000/openapi.json

These interfaces allow you to:

- Explore all available endpoints
- Test API calls directly in the browser
- View request/response schemas
- Download OpenAPI specifications
