REST API Endpoints
==================

This section provides detailed documentation for all REST API endpoints in the TurdParty VM WebSocket API.

Health Check
------------

.. http:get:: /health

   Check the health status of the API server.

   **Example Request:**

   .. code-block:: http

      GET /health HTTP/1.1
      Host: localhost:8000

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
          "status": "healthy",
          "timestamp": "2025-06-11T07:30:00Z",
          "version": "1.0.0",
          "uptime": 3600,
          "services": {
              "database": "healthy",
              "docker": "healthy",
              "vagrant": "healthy"
          }
      }

   :statuscode 200: Service is healthy
   :statuscode 503: Service is unhealthy

Virtual Machine Management
--------------------------

List Virtual Machines
~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/vms/

   Retrieve a list of all virtual machines.

   **Query Parameters:**

   .. list-table::
      :widths: 20 20 60
      :header-rows: 1

      * - Parameter
        - Type
        - Description
      * - ``page``
        - integer
        - Page number (default: 1)
      * - ``limit``
        - integer
        - Items per page (default: 20, max: 100)
      * - ``status``
        - string
        - Filter by status (running, stopped, error)
      * - ``vm_type``
        - string
        - Filter by type (docker, vagrant)
      * - ``sort``
        - string
        - Sort field (name, created_at, memory_mb)
      * - ``order``
        - string
        - Sort order (asc, desc)

   **Example Request:**

   .. code-block:: http

      GET /api/v1/vms/?page=1&limit=10&status=running HTTP/1.1
      Host: localhost:8000

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
          "data": [
              {
                  "vm_id": "vm-abc123",
                  "name": "malware-analysis-1",
                  "status": "running",
                  "vm_type": "docker",
                  "template": "ubuntu:20.04",
                  "memory_mb": 1024,
                  "cpus": 2,
                  "created_at": "2025-06-11T07:00:00Z",
                  "updated_at": "2025-06-11T07:30:00Z",
                  "domain": "TurdParty",
                  "metrics": {
                      "cpu_percent": 25.5,
                      "memory_percent": 45.2
                  }
              }
          ],
          "pagination": {
              "page": 1,
              "limit": 10,
              "total": 25,
              "pages": 3,
              "has_next": true,
              "has_prev": false
          }
      }

   :statuscode 200: Success
   :statuscode 400: Invalid query parameters

Create Virtual Machine
~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/vms/

   Create a new virtual machine.

   **Request Body:**

   .. code-block:: json

      {
          "name": "malware-analysis-vm",
          "template": "ubuntu:20.04",
          "vm_type": "docker",
          "memory_mb": 1024,
          "cpus": 2,
          "domain": "TurdParty",
          "environment": {
              "ANALYSIS_MODE": "dynamic",
              "TIMEOUT": "300"
          },
          "volumes": [
              {
                  "host_path": "/tmp/samples",
                  "container_path": "/samples",
                  "mode": "ro"
              }
          ]
      }

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 201 Created
      Content-Type: application/json
      Location: /api/v1/vms/vm-abc123

      {
          "vm_id": "vm-abc123",
          "name": "malware-analysis-vm",
          "status": "creating",
          "vm_type": "docker",
          "template": "ubuntu:20.04",
          "memory_mb": 1024,
          "cpus": 2,
          "created_at": "2025-06-11T07:30:00Z",
          "domain": "TurdParty",
          "container_id": "docker-container-123",
          "network_info": {
              "ip_address": "**********",
              "ports": {
                  "22": 32768,
                  "80": 32769
              }
          }
      }

   :statuscode 201: VM created successfully
   :statuscode 400: Invalid request data
   :statuscode 422: Validation error

Get Virtual Machine Details
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/vms/(vm_id)

   Retrieve detailed information about a specific virtual machine.

   **Path Parameters:**

   .. list-table::
      :widths: 20 20 60
      :header-rows: 1

      * - Parameter
        - Type
        - Description
      * - ``vm_id``
        - string
        - Unique VM identifier

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
          "vm_id": "vm-abc123",
          "name": "malware-analysis-vm",
          "status": "running",
          "vm_type": "docker",
          "template": "ubuntu:20.04",
          "memory_mb": 1024,
          "cpus": 2,
          "created_at": "2025-06-11T07:00:00Z",
          "updated_at": "2025-06-11T07:30:00Z",
          "domain": "TurdParty",
          "container_id": "docker-container-123",
          "network_info": {
              "ip_address": "**********",
              "ports": {
                  "22": 32768,
                  "80": 32769
              }
          },
          "metrics": {
              "cpu_percent": 25.5,
              "memory_percent": 45.2,
              "disk_usage": {
                  "total_gb": 20,
                  "used_gb": 5.2,
                  "available_gb": 14.8
              },
              "network": {
                  "bytes_sent": 1024000,
                  "bytes_received": 2048000
              }
          },
          "processes": [
              {
                  "pid": 1,
                  "name": "systemd",
                  "cpu_percent": 0.1,
                  "memory_mb": 12.5
              }
          ]
      }

   :statuscode 200: Success
   :statuscode 404: VM not found

Update Virtual Machine
~~~~~~~~~~~~~~~~~~~~~~~

.. http:patch:: /api/v1/vms/(vm_id)

   Update virtual machine configuration.

   **Request Body:**

   .. code-block:: json

      {
          "name": "updated-vm-name",
          "memory_mb": 2048,
          "cpus": 4
      }

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
          "vm_id": "vm-abc123",
          "name": "updated-vm-name",
          "status": "running",
          "memory_mb": 2048,
          "cpus": 4,
          "updated_at": "2025-06-11T07:35:00Z"
      }

   :statuscode 200: VM updated successfully
   :statuscode 400: Invalid request data
   :statuscode 404: VM not found
   :statuscode 409: VM cannot be updated in current state

Delete Virtual Machine
~~~~~~~~~~~~~~~~~~~~~~~

.. http:delete:: /api/v1/vms/(vm_id)

   Delete a virtual machine and all associated resources.

   **Query Parameters:**

   .. list-table::
      :widths: 20 20 60
      :header-rows: 1

      * - Parameter
        - Type
        - Description
      * - ``force``
        - boolean
        - Force deletion even if VM is running
      * - ``cleanup``
        - boolean
        - Remove associated volumes and networks

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 204 No Content

   :statuscode 204: VM deleted successfully
   :statuscode 404: VM not found
   :statuscode 409: VM cannot be deleted in current state

VM Templates
------------

List Available Templates
~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/vms/templates

   Retrieve a list of available VM templates.

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
          "templates": [
              {
                  "name": "ubuntu:20.04",
                  "type": "docker",
                  "description": "Ubuntu 20.04 LTS with analysis tools",
                  "size_mb": 512,
                  "tags": ["linux", "ubuntu", "analysis"],
                  "tools": [
                      "volatility",
                      "yara",
                      "clamav",
                      "wireshark"
                  ],
                  "default_config": {
                      "memory_mb": 1024,
                      "cpus": 2,
                      "disk_gb": 20
                  }
              },
              {
                  "name": "windows:10",
                  "type": "vagrant",
                  "description": "Windows 10 with Fibratus monitoring",
                  "size_mb": 4096,
                  "tags": ["windows", "fibratus", "monitoring"],
                  "tools": [
                      "fibratus",
                      "procmon",
                      "wireshark"
                  ],
                  "default_config": {
                      "memory_mb": 4096,
                      "cpus": 4,
                      "disk_gb": 60
                  }
              }
          ]
      }

   :statuscode 200: Success

VM Actions
----------

Start Virtual Machine
~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/vms/(vm_id)/start

   Start a stopped virtual machine.

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
          "vm_id": "vm-abc123",
          "status": "starting",
          "message": "VM start initiated"
      }

   :statuscode 200: Start initiated
   :statuscode 404: VM not found
   :statuscode 409: VM already running

Stop Virtual Machine
~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/vms/(vm_id)/stop

   Stop a running virtual machine.

   **Request Body (Optional):**

   .. code-block:: json

      {
          "force": false,
          "timeout": 30
      }

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
          "vm_id": "vm-abc123",
          "status": "stopping",
          "message": "VM stop initiated"
      }

   :statuscode 200: Stop initiated
   :statuscode 404: VM not found
   :statuscode 409: VM already stopped

Restart Virtual Machine
~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/vms/(vm_id)/restart

   Restart a virtual machine.

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
          "vm_id": "vm-abc123",
          "status": "restarting",
          "message": "VM restart initiated"
      }

   :statuscode 200: Restart initiated
   :statuscode 404: VM not found

System Information
------------------

Get System Status
~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/status

   Retrieve detailed system status and resource usage.

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
          "system": {
              "cpu_count": 8,
              "memory_total_gb": 32,
              "memory_available_gb": 18.5,
              "disk_total_gb": 500,
              "disk_available_gb": 350
          },
          "vms": {
              "total": 15,
              "running": 8,
              "stopped": 5,
              "error": 2
          },
          "docker": {
              "version": "20.10.21",
              "containers_running": 12,
              "images_count": 25
          },
          "vagrant": {
              "version": "2.3.4",
              "vms_running": 3,
              "providers": ["virtualbox", "vmware"]
          }
      }

   :statuscode 200: Success
