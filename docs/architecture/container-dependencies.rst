💩🎉TurdParty🎉💩 Container Dependencies
==========================================

This document provides a comprehensive analysis of container dependencies within the TurdParty platform, essential for understanding startup sequences, troubleshooting connectivity issues, and optimising deployment strategies.

.. contents::
   :local:
   :depth: 2

Overview
--------

The TurdParty platform consists of multiple containerised services with varying dependency requirements. Understanding these dependencies is crucial for:

- **Startup Sequencing**: Ensuring services start in the correct order
- **Troubleshooting**: Identifying why services fail to start or connect
- **Performance Optimisation**: Minimising startup time through parallel initialisation
- **Resilience Planning**: Building fault-tolerant service communication

Dependency Categories
--------------------

Services are categorised by their dependency complexity:

❌ **No Dependencies (Core Infrastructure)**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

These services have no external dependencies and should start first:

- **Database (PostgreSQL)** - Core data store
- **Redis** - Cache and message broker
- **MinIO Storage** - Object storage
- **Elasticsearch** - Search engine
- **Frontend** - Static React application (Nginx)
- **Documentation** - Static Sphinx documentation (Nginx)

**Characteristics:**
- Start immediately upon container creation
- Provide foundational services for other containers
- Should be monitored for health before dependent services start

✅ **Light Dependencies (Secondary Services)**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

These services depend on 1-2 core infrastructure services:

- **Logstash** → Elasticsearch
- **Kibana** → Elasticsearch
- **Celery Beat** → Redis + Elasticsearch
- **Celery Flower** → Redis + Celery Worker

**Characteristics:**
- Start after their dependencies are healthy
- Generally have fast startup times
- Provide enhanced functionality but aren't critical for basic operation

🚨 **Heavy Dependencies (Application Layer)**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

These services have complex dependency chains and should start last:

- **API Service** → Database + Redis + Elasticsearch + Logstash + MinIO Storage
- **Celery Worker** → Redis + Elasticsearch + MinIO Storage
- **VM Monitor** → Elasticsearch + API Service
- **Status Dashboard** → API Service + Elasticsearch + Kibana

**Characteristics:**
- Require multiple services to be operational
- Longest startup times due to dependency checking
- Critical for platform functionality
- Most likely to experience startup failures

Detailed Dependency Mapping
---------------------------

API Service Dependencies
~~~~~~~~~~~~~~~~~~~~~~~

The API Service has the most complex dependency chain:

.. code-block:: text

   API Service
   ├── Database (PostgreSQL) - User data, file metadata
   ├── Redis - Session storage, caching
   ├── Elasticsearch - Log storage, search functionality
   ├── Logstash - Log processing pipeline
   ├── MinIO Storage - File storage backend
   └── Celery Worker (optional) - Background task processing

**Startup Sequence:**
1. Check Database connectivity
2. Check Redis connectivity  
3. Check Elasticsearch connectivity
4. Check Logstash connectivity (may cause delays)
5. Initialise MinIO client
6. Start FastAPI server

Worker Service Dependencies
~~~~~~~~~~~~~~~~~~~~~~~~~~

Celery services require coordination:

.. code-block:: text

   Celery Worker
   ├── Redis - Task queue and results backend
   ├── Elasticsearch - Logging and monitoring
   └── MinIO Storage - File processing operations

   Celery Beat
   ├── Redis - Scheduler state storage
   └── Elasticsearch - Scheduled task logging

   Celery Flower
   ├── Redis - Worker monitoring
   └── Celery Worker - Worker statistics

Monitoring Service Dependencies
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Monitoring services depend on the application layer:

.. code-block:: text

   VM Monitor
   ├── Elasticsearch - VM state logging
   └── API Service - VM management endpoints

   Status Dashboard
   ├── API Service - Health check endpoints
   ├── Elasticsearch - System metrics
   └── Kibana - Visualisation data

Common Startup Issues
--------------------

API Service Stuck on Dependencies
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Symptoms:**
- API container shows "Waiting for [service]..." messages
- HTTP requests to localhost:8000 return "Connection reset by peer"
- Container appears running but no FastAPI server starts

**Root Causes:**
1. **Logstash Timing Issue** - Logstash may be starting but not ready for connections
2. **Network Connectivity** - Container networking issues between services
3. **Health Check Failures** - Dependencies report unhealthy status
4. **Resource Constraints** - Insufficient memory/CPU causing slow startup

**Troubleshooting Steps:**

.. code-block:: bash

   # Check container status
   docker-compose ps
   
   # Check API startup logs
   docker-compose logs --tail=20 api
   
   # Verify dependency health
   docker-compose ps database redis elasticsearch logstash storage
   
   # Test internal connectivity
   docker exec turdpartycollab_api ping redis
   docker exec turdpartycollab_api ping database

**Solutions:**
1. **Restart API Service**: ``docker-compose restart api``
2. **Restart All Services**: ``./scripts/turdparty stop && ./scripts/turdparty start``
3. **Check Resource Usage**: ``docker stats``
4. **Verify Network Configuration**: ``docker network ls | grep turdparty``

Circular Dependencies
~~~~~~~~~~~~~~~~~~~~

**Potential Issues:**
- VM Monitor depends on API, but API may try to initialise VM monitoring
- Status Dashboard depends on API, but API may try to register with dashboard

**Prevention:**
- Use lazy initialisation for non-critical dependencies
- Implement retry logic with exponential backoff
- Design services to function with degraded capabilities

Best Practices
--------------

Startup Sequence Optimisation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Start Core Infrastructure First**:
   
   .. code-block:: bash
   
      # Start foundational services
      docker-compose up -d database redis elasticsearch storage
      
      # Wait for health checks
      sleep 10
      
      # Start secondary services
      docker-compose up -d logstash kibana
      
      # Start application layer
      docker-compose up -d api celery-worker

2. **Use Health Checks**: Ensure all services have proper health check endpoints

3. **Implement Graceful Degradation**: Services should start even if non-critical dependencies are unavailable

Dependency Management
~~~~~~~~~~~~~~~~~~~~

1. **Lazy Loading**: Initialise connections only when needed
2. **Retry Logic**: Implement exponential backoff for failed connections
3. **Circuit Breakers**: Prevent cascading failures
4. **Monitoring**: Track dependency health continuously

Testing Dependencies
~~~~~~~~~~~~~~~~~~~

Use the integration test suite to verify dependency chains:

.. code-block:: bash

   # Test basic connectivity
   python tests/integration/run_basic_test.py
   
   # Test service health endpoints
   python -m pytest tests/integration/test_traefik_routing.py -v
   
   # Test full dependency chain
   ./scripts/run-parallel-tests.sh

Troubleshooting Checklist
-------------------------

When services fail to start:

☐ **Check Container Status**: ``docker-compose ps``
☐ **Review Startup Logs**: ``docker-compose logs [service]``
☐ **Verify Network Connectivity**: ``docker network inspect turdpartycollab_net``
☐ **Test Dependency Health**: Individual service health checks
☐ **Check Resource Usage**: ``docker stats``
☐ **Restart in Sequence**: Core → Secondary → Application
☐ **Verify Configuration**: Environment variables and service URLs
☐ **Test Integration**: Run basic integration tests

Related Documentation
---------------------

- :doc:`../deployment/docker-setup` - Container deployment guide
- :doc:`../monitoring/health-checks` - Service health monitoring
- :doc:`../troubleshooting/startup-issues` - Common startup problems
- :doc:`service-urls` - Central URL management system

.. note::
   This dependency analysis was generated from actual troubleshooting of API startup issues where the service was stuck waiting for Logstash connectivity, preventing the FastAPI server from starting and causing "Connection reset by peer" errors in integration tests.
