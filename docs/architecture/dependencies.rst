💩🎉TurdParty🎉💩 Service Dependencies & Health Monitoring
===========================================================

This document provides comprehensive documentation of the TurdParty platform's service dependencies, health monitoring system, and infrastructure architecture.

.. contents::
   :local:
   :depth: 3

Overview
--------

TurdParty consists of 15 interconnected services that work together to provide a complete malware analysis platform. Our dependency-aware health monitoring system ensures all services are operational and properly connected.

**Current Status: 15/15 Services Healthy** ✅

Service Architecture
-------------------

Core Infrastructure Services
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

These services provide the foundational infrastructure for the platform:

* **database** (PostgreSQL) - Primary data storage
* **redis** - Caching and message broker
* **storage** (MinIO) - Object storage for files and artifacts
* **elasticsearch** - Search and analytics engine

Logging & Monitoring Stack
~~~~~~~~~~~~~~~~~~~~~~~~~~

Complete ELK stack with Beats for comprehensive logging:

* **elasticsearch** - Log storage and search
* **logstash** - Log processing and transformation
* **kibana** - Log visualization and dashboards
* **filebeat** - Log collection from Docker containers

Application Services
~~~~~~~~~~~~~~~~~~~~

Core application components:

* **api** - FastAPI backend service
* **frontend** - React-based user interface
* **status** - System status dashboard
* **docs** - Sphinx documentation server

Worker Services
~~~~~~~~~~~~~~~

Background processing and task management:

* **celery_worker** - Background task execution
* **celery_beat** - Scheduled task management
* **celery_flower** - Celery monitoring interface

Monitoring Services
~~~~~~~~~~~~~~~~~~~

System and VM monitoring:

* **vm_monitor** - Virtual machine monitoring and management

Service Dependency Graph
------------------------

Complete Service Dependencies
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       %% Foundation Layer (No Dependencies)
       DB[(💾 database<br/>PostgreSQL)]
       REDIS[(🔄 redis<br/>Cache/Queue)]
       STORAGE[(📦 storage<br/>MinIO S3)]
       ES[(🔍 elasticsearch<br/>Search Engine)]
       STATUS[📊 status<br/>Dashboard]
       DOCS[📚 docs<br/>Sphinx]

       %% Level 1 Dependencies
       LOGSTASH[📝 logstash<br/>Log Processing]
       KIBANA[📈 kibana<br/>Log Visualization]
       API[🚀 api<br/>FastAPI Backend]
       VM_MON[🖥️ vm_monitor<br/>VM Management]
       CELERY_WORKER[⚙️ celery_worker<br/>Background Tasks]
       CELERY_BEAT[⏰ celery_beat<br/>Scheduler]

       %% Level 2 Dependencies
       FILEBEAT[📋 filebeat<br/>Log Collection]
       CELERY_FLOWER[🌸 celery_flower<br/>Worker Monitor]
       FRONTEND[🌐 frontend<br/>React UI]

       %% Foundation connections
       ES --> LOGSTASH
       ES --> KIBANA
       ES --> VM_MON

       DB --> API
       REDIS --> API
       STORAGE --> API

       DB --> CELERY_WORKER
       REDIS --> CELERY_WORKER

       DB --> CELERY_BEAT
       REDIS --> CELERY_BEAT

       %% Level 2 connections
       ES --> FILEBEAT
       LOGSTASH --> FILEBEAT

       REDIS --> CELERY_FLOWER
       CELERY_WORKER --> CELERY_FLOWER

       API --> FRONTEND

       %% Styling with TurdParty theme
       classDef foundation fill:#e1f5fe,stroke:#01579b,stroke-width:3px
       classDef level1 fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
       classDef level2 fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
       classDef web fill:#fff3e0,stroke:#e65100,stroke-width:2px

       class DB,REDIS,STORAGE,ES,STATUS,DOCS foundation
       class LOGSTASH,KIBANA,API,VM_MON,CELERY_WORKER,CELERY_BEAT level1
       class FILEBEAT,CELERY_FLOWER,FRONTEND level2

Dependency Layers
~~~~~~~~~~~~~~~~

The services are organized into dependency layers for optimal startup ordering:

**Foundation Layer (0 dependencies):**
   * database, redis, storage, elasticsearch
   * status, docs (standalone web services)

**Level 1 (depends on foundation):**
   * logstash → elasticsearch
   * kibana → elasticsearch  
   * api → database, redis, storage
   * vm_monitor → elasticsearch
   * celery_worker → database, redis
   * celery_beat → database, redis

**Level 2 (depends on level 1):**
   * filebeat → elasticsearch, logstash
   * celery_flower → redis, celery_worker
   * frontend → api

Network Architecture
-------------------

Traefik Routing
~~~~~~~~~~~~~~

.. mermaid::

   graph LR
       USER[User] --> TRAEFIK[Traefik Proxy]
       
       TRAEFIK --> |api.turdparty.localhost| API[api]
       TRAEFIK --> |frontend.turdparty.localhost| FRONTEND[frontend]
       TRAEFIK --> |kibana.turdparty.localhost| KIBANA[kibana]
       TRAEFIK --> |elasticsearch.turdparty.localhost| ES[elasticsearch]
       TRAEFIK --> |storage.turdparty.localhost| STORAGE[storage]
       TRAEFIK --> |flower.turdparty.localhost| FLOWER[celery_flower]
       TRAEFIK --> |status.turdparty.localhost| STATUS[status]
       TRAEFIK --> |docs.turdparty.localhost| DOCS[docs]
       
       %% Internal services (no Traefik routing)
       INTERNAL[Internal Network]
       INTERNAL -.-> DATABASE[(database)]
       INTERNAL -.-> REDIS[(redis)]
       INTERNAL -.-> LOGSTASH[logstash]
       INTERNAL -.-> FILEBEAT[filebeat]
       INTERNAL -.-> CELERY_WORKER[celery_worker]
       INTERNAL -.-> CELERY_BEAT[celery_beat]
       INTERNAL -.-> VM_MON[vm_monitor]
       
       classDef web fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
       classDef internal fill:#f1f8e9,stroke:#388e3c,stroke-width:2px
       classDef proxy fill:#fff3e0,stroke:#f57c00,stroke-width:3px
       
       class API,FRONTEND,KIBANA,ES,STORAGE,FLOWER,STATUS,DOCS web
       class DATABASE,REDIS,LOGSTASH,FILEBEAT,CELERY_WORKER,CELERY_BEAT,VM_MON internal
       class TRAEFIK proxy

Network Configuration
~~~~~~~~~~~~~~~~~~~~~

**Networks:**
   * ``turdpartycollab_net`` - Internal service communication
   * ``traefik_network`` - Traefik proxy network

**Web-Accessible Services (via Traefik):**
   * api, frontend, kibana, elasticsearch, storage
   * celery_flower, status, docs

**Internal-Only Services:**
   * database, redis, logstash, filebeat
   * celery_worker, celery_beat, vm_monitor

Health Monitoring System
------------------------

Health Check Architecture
~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       HCM[Health Check Manager]
       
       HCM --> |Docker Inspect| DOCKER[Docker Health Status]
       HCM --> |Dependency Check| DEP_GRAPH[Dependency Graph]
       HCM --> |Circular Detection| CIRCULAR[Circular Dependency Detection]
       
       DOCKER --> |healthy/unhealthy/starting| STATUS[Service Status]
       DEP_GRAPH --> |dependency validation| DEPS[Dependency Validation]
       CIRCULAR --> |graph analysis| SAFE[Safe Startup Order]
       
       STATUS --> REPORT[Health Report]
       DEPS --> REPORT
       SAFE --> REPORT
       
       REPORT --> CLI[CLI Integration]
       REPORT --> JSON[JSON Output]
       REPORT --> TESTS[Test Suite]
       
       classDef manager fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
       classDef check fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
       classDef output fill:#e0f2f1,stroke:#4caf50,stroke-width:2px
       
       class HCM manager
       class DOCKER,DEP_GRAPH,CIRCULAR check
       class REPORT,CLI,JSON,TESTS output

Health Check Types
~~~~~~~~~~~~~~~~~

Each service has a tailored health check based on its function:

**Database Services:**
   * PostgreSQL: Connection + query test
   * Redis: Ping + replication status
   * Elasticsearch: Cluster health API

**Application Services:**
   * API: HTTP endpoint health check
   * Frontend: HTTP availability check
   * Status/Docs: Nginx HTTP response

**Processing Services:**
   * Logstash: Java process detection
   * Filebeat: Process running check
   * Celery Worker: Celery ping command
   * Celery Beat: Process detection

**Storage Services:**
   * MinIO: Live + ready endpoint checks

Dependency-Aware Health Checking
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The health check system implements dependency awareness:

1. **Dependency Validation**: Services are only checked when their dependencies are healthy
2. **Circular Detection**: Prevents deadlocks in dependency chains
3. **Startup Ordering**: Ensures services start in correct dependency order
4. **Failure Propagation**: Dependency failures are clearly reported

Testing Framework
----------------

Comprehensive Test Suite
~~~~~~~~~~~~~~~~~~~~~~~

The health check system includes a complete test suite with 6 test categories:

.. mermaid::

   graph LR
       TESTS[Test Suite] --> DEP_GRAPH[Dependency Graph Validation]
       TESTS --> JSON_OUT[Health Check Manager JSON]
       TESTS --> DOCKER_HC[Docker Compose Health Checks]
       TESTS --> CLI_INT[CLI Integration]
       TESTS --> SVC_DEPS[Service Dependency Logic]
       TESTS --> HC_TIMING[Health Check Timing]
       
       DEP_GRAPH --> |✅ No circular deps| PASS1[PASS]
       JSON_OUT --> |✅ Valid JSON format| PASS2[PASS]
       DOCKER_HC --> |✅ All 15 services| PASS3[PASS]
       CLI_INT --> |✅ CLI validation| PASS4[PASS]
       SVC_DEPS --> |✅ Correct dependencies| PASS5[PASS]
       HC_TIMING --> |✅ Reasonable timing| PASS6[PASS]
       
       classDef test fill:#fff3e0,stroke:#ff8f00,stroke-width:2px
       classDef pass fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
       
       class TESTS,DEP_GRAPH,JSON_OUT,DOCKER_HC,CLI_INT,SVC_DEPS,HC_TIMING test
       class PASS1,PASS2,PASS3,PASS4,PASS5,PASS6 pass

Test Commands
~~~~~~~~~~~~

**Run Health Check Tests:**

.. code-block:: bash

   # Quick test suite
   python3 scripts/test-health-checks.py --quick
   
   # Live service tests (requires running services)
   python3 scripts/test-health-checks.py --live
   
   # Comprehensive dependency check
   bash scripts/check-comprehensive-dependencies.sh

**Manual Health Checks:**

.. code-block:: bash

   # Check all service health
   python3 scripts/health-check-manager.py
   
   # JSON output for monitoring
   python3 scripts/health-check-manager.py --json
   
   # Validate dependency graph
   python3 scripts/health-check-manager.py --validate

CLI Integration
--------------

The health check system is fully integrated with the TurdParty CLI:

**Health Check Commands:**

.. code-block:: bash

   # Basic health checks
   turdparty check
   
   # Comprehensive dependency validation
   turdparty check --validate-deps
   
   # Comprehensive checks (Traefik + Health)
   turdparty check --comprehensive

**Startup Integration:**

The CLI automatically runs comprehensive dependency checks during startup to ensure all services are operational before proceeding.

Monitoring & Alerting
--------------------

Real-time Monitoring
~~~~~~~~~~~~~~~~~~~

The platform provides multiple monitoring interfaces:

* **CLI Dashboard**: Real-time service status with progress indicators
* **Status Service**: Web-based status dashboard at ``status.turdparty.localhost``
* **Kibana**: Comprehensive log analysis at ``kibana.turdparty.localhost``
* **Celery Flower**: Worker monitoring at ``flower.turdparty.localhost``

Health Check Metrics
~~~~~~~~~~~~~~~~~~~

Key metrics tracked by the health monitoring system:

* **Service Availability**: 15/15 services healthy
* **Dependency Health**: All dependency chains validated
* **Response Times**: Health check execution timing
* **Failure Rates**: Service restart and failure tracking

Troubleshooting
--------------

Common Issues
~~~~~~~~~~~~

**Service Shows as UNKNOWN:**
   * Check container name matches health check manager configuration
   * Verify Docker health check is properly configured
   * Ensure service is running and accessible

**Dependency Failed:**
   * Check that all dependencies are healthy first
   * Verify network connectivity between services
   * Review service logs for connection errors

**Health Check Timing Out:**
   * Increase health check timeout values
   * Check service startup time requirements
   * Verify resource allocation (CPU/memory)

Debug Commands
~~~~~~~~~~~~~

.. code-block:: bash

   # Check specific service health
   docker inspect turdpartycollab_<service> --format '{{.State.Health.Status}}'
   
   # View health check logs
   docker inspect turdpartycollab_<service> --format '{{range .State.Health.Log}}{{.Output}}{{end}}'
   
   # Test service connectivity
   docker exec turdpartycollab_<service> <connectivity-test>

Performance Considerations
-------------------------

Health Check Optimization
~~~~~~~~~~~~~~~~~~~~~~~~~

The health check system is optimized for:

* **Minimal Overhead**: Lightweight checks with reasonable intervals
* **Fast Startup**: Dependency-aware ordering reduces startup time
* **Resource Efficiency**: Checks only run when dependencies are satisfied
* **Scalability**: System scales with additional services

Recommended Settings
~~~~~~~~~~~~~~~~~~~

* **Check Interval**: 30s for most services
* **Timeout**: 10s for network checks, 5s for process checks
* **Retries**: 3-5 retries before marking unhealthy
* **Start Period**: 60s for complex services, 20s for simple services

Conclusion
----------

The TurdParty health monitoring system provides:

✅ **Complete Coverage**: All 15 services monitored
✅ **Dependency Awareness**: Smart dependency checking
✅ **Zero Circular Dependencies**: Safe startup ordering
✅ **Comprehensive Testing**: 6-category test suite
✅ **CLI Integration**: Seamless operational experience
✅ **Production Ready**: Robust monitoring and alerting

This system ensures TurdParty operates reliably with full visibility into service health and dependencies.
