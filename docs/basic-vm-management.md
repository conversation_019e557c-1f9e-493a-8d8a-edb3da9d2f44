# Basic VM Management for TurdParty

## Overview

This document describes the basic VM management implementation that provides immediate VM allocation from pre-available pools, addressing the current issue where VMs are queued but never actually allocated.

## Problem Solved

**Current Issue**: The test results show VMs are being queued (`"success": false`, `"status": "queued"`) but never allocated because there are no pre-available VMs in the pools.

**Solution**: Maintain pools of ready-to-use VMs for immediate allocation, with automatic replenishment.

## Architecture

### Components

1. **BasicVMAvailabilityManager** - Core pool management
2. **Basic VM Allocation API** - Enhanced API endpoints
3. **Pool Maintenance Tasks** - Automatic pool replenishment
4. **Initialization Scripts** - Setup and health monitoring

### VM Templates Supported

- `ubuntu:20.04` - 3 min ready, 10 max total (Docker)
- `ubuntu:22.04` - 2 min ready, 8 max total (Docker)  
- `alpine:latest` - 2 min ready, 6 max total (Docker)
- `10Baht/windows10-turdparty` - 1 min ready, 3 max total (Vagrant)

## Quick Start

### 1. Initialize VM Pools

```bash
# Initialize all pools
python scripts/initialize_vm_pools.py

# Check pool health
python scripts/initialize_vm_pools.py --check-health

# Initialize specific templates only
python scripts/initialize_vm_pools.py --templates ubuntu:20.04 alpine:latest

# Dry run to see what would be created
python scripts/initialize_vm_pools.py --dry-run
```

### 2. API Usage

#### Allocate VM Immediately

```bash
curl -X POST "http://api.turdparty.localhost/api/v1/vm-allocation/allocate" \
  -H "Content-Type: application/json" \
  -d '{
    "template": "ubuntu:20.04",
    "requester_id": "user123",
    "purpose": "malware_analysis",
    "duration_minutes": 30
  }'
```

**Successful Response:**
```json
{
  "success": true,
  "vm_id": "12345678-1234-1234-1234-123456789012",
  "vm_details": {
    "vm_id": "12345678-1234-1234-1234-123456789012",
    "name": "pool_ubuntu_20_04_abc12345",
    "template": "ubuntu:20.04",
    "memory_mb": 1024,
    "cpus": 1,
    "ip_address": "***********",
    "ssh_port": 22,
    "status": "running"
  },
  "status": "allocated",
  "message": "VM allocated immediately from ubuntu:20.04 pool",
  "allocated_at": "2025-06-17T10:30:00Z",
  "request_id": "req-abc123"
}
```

**Provisioning Response (no ready VMs):**
```json
{
  "success": false,
  "status": "provisioning",
  "message": "No ready VMs available for ubuntu:20.04, provisioning new VM",
  "estimated_wait_minutes": 3,
  "request_id": "req-def456"
}
```

#### Check Pool Status

```bash
curl "http://api.turdparty.localhost/api/v1/vm-allocation/pools/status"
```

**Response:**
```json
{
  "ubuntu:20.04": {
    "template": "ubuntu:20.04",
    "ready_vms": 3,
    "total_vms": 5,
    "creating_vms": 2,
    "running_vms": 0,
    "min_ready": 3,
    "max_total": 10,
    "needs_provisioning": false,
    "can_provision": true,
    "health_status": "healthy"
  },
  "alpine:latest": {
    "template": "alpine:latest",
    "ready_vms": 1,
    "total_vms": 2,
    "creating_vms": 1,
    "running_vms": 0,
    "min_ready": 2,
    "max_total": 6,
    "needs_provisioning": true,
    "can_provision": true,
    "health_status": "degraded"
  }
}
```

#### Trigger Pool Maintenance

```bash
curl -X POST "http://api.turdparty.localhost/api/v1/vm-allocation/pools/maintain"
```

## Integration with Current API

### Enhanced Allocation Endpoint

The new `/api/v1/vm-allocation/allocate` endpoint provides:

- **Immediate allocation** from ready pools
- **Fallback provisioning** if no VMs available
- **Comprehensive logging** to Elasticsearch
- **Automatic termination** scheduling
- **Error handling** and validation

### Backward Compatibility

The existing endpoints continue to work:
- `/api/v1/pools/{template}/allocate` - Enhanced pool allocation
- `/api/v1/vms/` - Direct VM creation
- `/api/v1/workflow/start` - Complete workflow

## Monitoring and Logging

### Elasticsearch Events

All operations are logged to Elasticsearch with event types:

- `vm_allocation_request` - Allocation requests
- `vm_allocation_success` - Successful allocations  
- `vm_allocation_provisioning` - Provisioning status
- `vm_allocation_error` - Allocation errors
- `pool_status_request` - Status requests
- `pool_maintenance_triggered` - Maintenance events

### Health Monitoring

Pool health is categorized as:
- **Healthy** - Ready VMs ≥ minimum required
- **Degraded** - Some ready VMs but below minimum
- **Critical** - No ready VMs available

## Configuration

### Pool Configuration

Each template has configurable limits:

```python
PoolConfig(
    template="ubuntu:20.04",
    min_ready=3,        # Minimum ready VMs to maintain
    max_total=10,       # Maximum total VMs allowed
    vm_type="docker",   # VM type (docker/vagrant)
    memory_mb=1024,     # Memory allocation
    cpus=1             # CPU allocation
)
```

### Celery Tasks

New Celery tasks for pool management:

- `maintain_vm_pools` - Maintain minimum ready VMs
- `allocate_vm_immediately` - Immediate allocation
- `get_pool_status` - Pool status retrieval

## Testing

### Run Integration Tests

```bash
# Run basic VM availability tests
python -m pytest tests/integration/test_basic_vm_availability.py -v

# Run with coverage
python -m pytest tests/integration/test_basic_vm_availability.py --cov=services.workers.tasks.basic_vm_availability_manager
```

### Manual Testing

```bash
# Check pool health
python scripts/initialize_vm_pools.py --check-health

# Test allocation
curl -X POST "http://api.turdparty.localhost/api/v1/vm-allocation/allocate" \
  -H "Content-Type: application/json" \
  -d '{"template": "alpine:latest", "requester_id": "test"}'
```

## Deployment

### Step-by-Step Deployment Guide

#### 1. Pre-Deployment Verification

```bash
# Verify current system state
python scripts/test_basic_vm_integration.py --comprehensive

# Check current test results (should show VMs stuck in queue)
cat test_results_test-*.json | jq '.test_results.allocation_result'
```

#### 2. Database Migration

```bash
# Run database migration (dry run first)
python scripts/migrate_vm_pool_schema.py --dry-run

# Execute migration
python scripts/migrate_vm_pool_schema.py

# Verify migration
python scripts/migrate_vm_pool_schema.py --verify
```

#### 3. Deploy New Components

```bash
# The following files are already created and ready:
# - services/workers/tasks/basic_vm_availability_manager.py
# - services/api/src/routes/v1/basic_vm_allocation.py
# - scripts/initialize_vm_pools.py
# - scripts/test_basic_vm_integration.py

# Restart API server to load new endpoints
sudo systemctl restart turdparty-api

# Restart Celery workers to load new tasks
sudo systemctl restart turdparty-celery-worker
```

#### 4. Configure Celery Integration

Add to your existing Celery configuration:

```python
# In services/workers/celery_app.py
CELERY_IMPORTS = [
    'services.workers.tasks.basic_vm_availability_manager',
    'services.workers.tasks.vm_management',  # existing
    # ... other existing imports
]

# Add periodic task for pool maintenance
CELERYBEAT_SCHEDULE = {
    'maintain-vm-pools': {
        'task': 'services.workers.tasks.basic_vm_availability_manager.maintain_vm_pools',
        'schedule': 300.0,  # Every 5 minutes
    },
    # ... other existing scheduled tasks
}
```

#### 5. Initialize VM Pools

```bash
# Initialize all VM pools
python scripts/initialize_vm_pools.py

# Check pool health
python scripts/initialize_vm_pools.py --check-health

# Expected output:
# Pool Health Report (4 templates):
# ubuntu:20.04              |  3/ 3 ready |  3 total |  0 creating | ✅ HEALTHY
# ubuntu:22.04              |  2/ 2 ready |  2 total |  0 creating | ✅ HEALTHY
# alpine:latest             |  2/ 2 ready |  2 total |  0 creating | ✅ HEALTHY
# 10Baht/windows10-turdparty|  1/ 1 ready |  1 total |  0 creating | ✅ HEALTHY
```

#### 6. Verify Integration

```bash
# Test the new allocation endpoint
curl -X POST "http://api.turdparty.localhost/api/v1/vm-allocation/allocate" \
  -H "Content-Type: application/json" \
  -d '{
    "template": "alpine:latest",
    "requester_id": "deployment_test",
    "purpose": "deployment_verification",
    "duration_minutes": 5
  }'

# Expected response (immediate allocation):
# {
#   "success": true,
#   "vm_id": "12345678-1234-1234-1234-123456789012",
#   "vm_details": {...},
#   "status": "allocated",
#   "message": "VM allocated immediately from alpine:latest pool",
#   "allocated_at": "2025-06-17T...",
#   "request_id": "..."
# }
```

#### 7. Run Comprehensive Tests

```bash
# Run integration tests
python scripts/test_basic_vm_integration.py --comprehensive --save-results

# Expected output:
# ✅ pool_status: PASSED
# ✅ vm_allocation: PASSED
# ✅ pool_maintenance: PASSED
# ✅ api_integration: PASSED
# Overall Success: ✅ PASS
```

#### 8. Monitor Pool Status

```bash
# Check pool status via API
curl "http://api.turdparty.localhost/api/v1/vm-allocation/pools/status" | jq

# Monitor Elasticsearch logs
curl "http://elasticsearch.turdparty.localhost:9200/turdparty-queue-actions/_search" \
  -H "Content-Type: application/json" \
  -d '{"query": {"match": {"event_type": "vm_allocation_success"}}}'
```

### Rollback Procedure (if needed)

```bash
# Rollback database changes
python scripts/migrate_vm_pool_schema.py --rollback --dry-run
python scripts/migrate_vm_pool_schema.py --rollback

# Restart services with previous configuration
sudo systemctl restart turdparty-api
sudo systemctl restart turdparty-celery-worker

# Verify rollback
python scripts/migrate_vm_pool_schema.py --verify
```

## Benefits

1. **Immediate Allocation** - VMs available instantly from ready pools
2. **Automatic Replenishment** - Pools maintained automatically
3. **Better Resource Utilization** - Pre-created VMs reduce waste
4. **Improved User Experience** - No waiting for VM creation
5. **Comprehensive Monitoring** - Full visibility into pool health
6. **Elasticsearch Integration** - All operations logged for analysis

## Next Steps

1. **Deploy and Test** - Deploy the basic VM management system
2. **Monitor Performance** - Watch pool utilization and allocation times
3. **Tune Configuration** - Adjust pool sizes based on usage patterns
4. **Enhance Features** - Add more sophisticated scheduling and optimization
5. **Scale Up** - Implement the full 3-phase plan for enterprise scale
