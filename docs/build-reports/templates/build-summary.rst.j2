💩🎉TurdParty🎉💩 Build Summary: {{ summary_period }}
{{ "=" * (35 + summary_period|length) }}

.. meta::
   :description: Summary of TurdParty VM builds for {{ summary_period }}
   :keywords: TurdParty, VM, builds, summary, statistics

.. contents:: Table of Contents
   :depth: 2
   :local:

Overview
========

.. admonition:: Build Statistics for {{ summary_period }}
   :class: note

   **Total Builds:** {{ stats.total_builds }}
   
   **Successful Builds:** {{ stats.successful_builds }} ({{ stats.success_rate }}%)
   
   **Failed Builds:** {{ stats.failed_builds }} ({{ stats.failure_rate }}%)
   
   **Average Build Time:** {{ stats.avg_build_time }}

Quick Stats
-----------

.. list-table:: Build Performance Summary
   :widths: 40 60
   :header-rows: 1

   * - Metric
     - Value
   * - Total Build Time
     - {{ stats.total_build_time }}
   * - Fastest Build
     - {{ stats.fastest_build.duration }} ({{ stats.fastest_build.vm_name }})
   * - Slowest Build
     - {{ stats.slowest_build.duration }} ({{ stats.slowest_build.vm_name }})
   * - Most Common OS
     - {{ stats.most_common_os }}
   * - Total VM Storage
     - {{ stats.total_vm_storage }}

Recent Builds
=============

{% for build in recent_builds %}
{{ loop.index }}. **{{ build.vm_name }}** - {{ "✅" if build.success else "❌" }} {{ build.status }}
   {{ "-" * (build.vm_name|length + 15) }}

   * **Build ID:** ``{{ build.build_id }}``
   * **Date:** {{ build.date }}
   * **Duration:** {{ build.duration }}
   * **VM Size:** {{ build.vm_size }}
   * **OS:** {{ build.os_type }} {{ build.os_version }}
   
   {% if build.success %}
   * **Report:** :doc:`{{ build.report_link }}`
   {% else %}
   * **Error:** {{ build.error_summary }}
   {% endif %}

{% endfor %}

Build Trends
============

.. figure:: {{ charts.trend_chart_path }}
   :alt: Build Trends Chart
   :align: center
   :width: 100%

   Build success rate and performance trends over time

Success Rate Analysis
---------------------

{% if trends.success_rate_improving %}
.. note::
   **Improving Trend:** Build success rate has improved by {{ trends.success_rate_change }}% over the last {{ trends.analysis_period }}.
{% elif trends.success_rate_declining %}
.. warning::
   **Declining Trend:** Build success rate has decreased by {{ trends.success_rate_change }}% over the last {{ trends.analysis_period }}.
{% else %}
.. admonition:: Stable Performance
   :class: tip
   
   Build success rate has remained stable at {{ trends.current_success_rate }}% over the last {{ trends.analysis_period }}.
{% endif %}

Performance Trends
------------------

.. list-table:: Performance Metrics Trend
   :widths: 30 25 25 20
   :header-rows: 1

   * - Metric
     - Current Period
     - Previous Period
     - Change
   * - Average Build Time
     - {{ trends.current_avg_time }}
     - {{ trends.previous_avg_time }}
     - {{ trends.time_change }}
   * - Average VM Size
     - {{ trends.current_avg_size }}
     - {{ trends.previous_avg_size }}
     - {{ trends.size_change }}
   * - Resource Usage
     - {{ trends.current_resource_usage }}%
     - {{ trends.previous_resource_usage }}%
     - {{ trends.resource_change }}

Common Issues
=============

{% if common_issues %}
Top Build Issues
----------------

{% for issue in common_issues %}
{{ loop.index }}. **{{ issue.category }}** ({{ issue.frequency }} occurrences)
   {{ "-" * (issue.category|length + 20) }}

   **Description:** {{ issue.description }}
   
   **Common Causes:**
   
   {% for cause in issue.causes %}
   * {{ cause }}
   {% endfor %}
   
   **Recommended Solutions:**
   
   {% for solution in issue.solutions %}
   * {{ solution }}
   {% endfor %}

{% endfor %}
{% else %}
.. note::
   No common issues identified in recent builds. Excellent work! 🎉
{% endif %}

Resource Usage
==============

.. figure:: {{ charts.resource_chart_path }}
   :alt: Resource Usage Chart
   :align: center
   :width: 100%

   System resource utilization across all builds

Infrastructure Health
---------------------

.. list-table:: Build Infrastructure Status
   :widths: 30 20 50
   :header-rows: 1

   * - Component
     - Status
     - Notes
   * - KVM/QEMU
     - {{ infrastructure.kvm_status }}
     - {{ infrastructure.kvm_notes }}
   * - Elasticsearch
     - {{ infrastructure.elasticsearch_status }}
     - {{ infrastructure.elasticsearch_notes }}
   * - Storage
     - {{ infrastructure.storage_status }}
     - {{ infrastructure.storage_notes }}
   * - Network
     - {{ infrastructure.network_status }}
     - {{ infrastructure.network_notes }}

VM Portfolio
============

Active VMs
----------

.. list-table:: Current VM Inventory
   :widths: 25 15 15 15 30
   :header-rows: 1

   * - VM Name
     - OS Type
     - Size
     - Last Built
     - Purpose
   {% for vm in vm_portfolio.active_vms %}
   * - {{ vm.name }}
     - {{ vm.os_type }}
     - {{ vm.size }}
     - {{ vm.last_built }}
     - {{ vm.purpose }}
   {% endfor %}

Build Templates
---------------

.. list-table:: Available Build Templates
   :widths: 30 20 50
   :header-rows: 1

   * - Template
     - OS Version
     - Description
   {% for template in vm_portfolio.templates %}
   * - {{ template.name }}
     - {{ template.os_version }}
     - {{ template.description }}
   {% endfor %}

Recommendations
===============

Performance Optimization
-------------------------

{% if recommendations.performance %}
{% for rec in recommendations.performance %}
* **{{ rec.category }}:** {{ rec.description }}
  
  *Impact:* {{ rec.impact }}
  
  *Implementation:* {{ rec.implementation }}
{% endfor %}
{% else %}
.. note::
   Current build performance is optimal. No immediate optimizations needed.
{% endif %}

Infrastructure Improvements
----------------------------

{% if recommendations.infrastructure %}
{% for rec in recommendations.infrastructure %}
* **{{ rec.category }}:** {{ rec.description }}
  
  *Priority:* {{ rec.priority }}
  
  *Estimated Effort:* {{ rec.effort }}
{% endfor %}
{% else %}
.. note::
   Infrastructure is performing well. Continue monitoring for future improvements.
{% endif %}

Security Enhancements
---------------------

{% if recommendations.security %}
{% for rec in recommendations.security %}
* **{{ rec.category }}:** {{ rec.description }}
  
  *Risk Level:* {{ rec.risk_level }}
  
  *Action Required:* {{ rec.action }}
{% endfor %}
{% else %}
.. note::
   Security configurations are up to date. Regular reviews recommended.
{% endif %}

Upcoming Builds
===============

Scheduled Builds
----------------

{% if upcoming_builds.scheduled %}
.. list-table:: Planned VM Builds
   :widths: 30 20 20 30
   :header-rows: 1

   * - VM Name
     - OS Type
     - Scheduled Date
     - Purpose
   {% for build in upcoming_builds.scheduled %}
   * - {{ build.name }}
     - {{ build.os_type }}
     - {{ build.scheduled_date }}
     - {{ build.purpose }}
   {% endfor %}
{% else %}
.. note::
   No builds currently scheduled. Use the build system to create new VMs as needed.
{% endif %}

Template Updates
----------------

{% if upcoming_builds.template_updates %}
.. admonition:: Template Updates Available
   :class: tip

   The following build templates have updates available:

   {% for update in upcoming_builds.template_updates %}
   * **{{ update.template }}:** {{ update.description }}
     
     *Version:* {{ update.current_version }} → {{ update.new_version }}
   {% endfor %}
{% endif %}

Historical Data
===============

Build History Archive
---------------------

For detailed historical analysis, see the complete build archive:

* :doc:`All Build Reports <generated/index>`
* :download:`Build Data Export (CSV) <{{ exports.csv_path }}>`
* :download:`Build Metrics (JSON) <{{ exports.json_path }}>`

Monthly Summaries
-----------------

{% for month in historical_data.monthly_summaries %}
* :doc:`{{ month.name }} {{ month.year }} <summaries/{{ month.filename }}>` - {{ month.total_builds }} builds
{% endfor %}

---

*Summary generated on {{ metadata.generation_time }} by 💩🎉TurdParty🎉💩 Build Report System v{{ metadata.version }}*

.. note::
   This summary is automatically updated after each build. For real-time build status, check the `Elasticsearch Dashboard <{{ metadata.elasticsearch_dashboard_url }}>`_.
