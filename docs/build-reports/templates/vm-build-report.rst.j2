💩🎉TurdParty🎉💩 VM Build Report: {{ build_config.vm_name }}
{{ "=" * (50 + build_config.vm_name|length) }}

.. meta::
   :description: Comprehensive build report for {{ build_config.vm_name }} VM
   :keywords: TurdParty, VM, build, {{ build_config.os_type }}, security, analysis

.. contents:: Table of Contents
   :depth: 3
   :local:

Executive Summary
=================

.. admonition:: Build Status: {{ "✅ SUCCESS" if build_status.success else "❌ FAILED" }}
   :class: {{ "note" if build_status.success else "warning" }}

   **Build ID:** ``{{ build_metadata.build_id }}``
   
   **Duration:** {{ build_metrics.total_duration_formatted }}
   
   **Completed:** {{ build_metadata.completion_time }}
   
   **VM Type:** {{ build_config.os_type }} {{ build_config.os_version }}

Key Metrics
-----------

.. list-table:: Build Performance
   :widths: 30 70
   :header-rows: 1

   * - Metric
     - Value
   * - Total Build Time
     - {{ build_metrics.total_duration_formatted }}
   * - ISO Processing Time
     - {{ build_metrics.iso_processing_time }}
   * - VM Creation Time
     - {{ build_metrics.vm_creation_time }}
   * - Final VM Size
     - {{ build_artifacts.vm_size_formatted }}
   * - Success Rate
     - {{ build_metrics.success_rate }}%

Quick Links
-----------

* :download:`VM Box File <{{ build_artifacts.vm_box_path }}>`
* :download:`Build Logs <{{ build_artifacts.build_log_path }}>`
* :download:`Packer Configuration <{{ build_artifacts.packer_config_path }}>`
* `Elasticsearch Logs <{{ build_metadata.elasticsearch_url }}>`_

Build Configuration
===================

ISO Information
---------------

.. code-block:: yaml

   iso_file: {{ build_config.iso_file }}
   iso_size: {{ build_config.iso_size_formatted }}
   iso_sha256: {{ build_config.iso_sha256 }}
   iso_source: {{ build_config.iso_source | default("Manual download") }}

VM Specifications
-----------------

.. list-table:: Virtual Machine Configuration
   :widths: 25 75
   :header-rows: 1

   * - Component
     - Configuration
   * - CPU Cores
     - {{ build_config.cpu_cores }}
   * - Memory (RAM)
     - {{ build_config.memory_mb }}MB ({{ build_config.memory_gb }}GB)
   * - Disk Size
     - {{ build_config.disk_size_formatted }}
   * - Disk Interface
     - {{ build_config.disk_interface }}
   * - Network Device
     - {{ build_config.network_device }}
   * - Machine Type
     - {{ build_config.machine_type }}
   * - Acceleration
     - {{ build_config.acceleration }}

Packer Configuration
--------------------

.. code-block:: json

   {
     "builders": [
       {
         "type": "{{ build_config.builder_type }}",
         "accelerator": "{{ build_config.acceleration }}",
         "machine_type": "{{ build_config.machine_type }}",
         "memory": "{{ build_config.memory_mb }}M",
         "cpus": "{{ build_config.cpu_cores }}",
         "disk_size": "{{ build_config.disk_size }}",
         "disk_interface": "{{ build_config.disk_interface }}",
         "net_device": "{{ build_config.network_device }}"
       }
     ]
   }

TurdParty Customizations
------------------------

.. admonition:: Overlay System Applied
   :class: tip

   The following TurdParty-specific customizations were applied:

   * **Fibratus Integration:** Real-time malware behavior monitoring
   * **ELK Logging:** Comprehensive event streaming to Elasticsearch
   * **Security Tools:** Pre-installed analysis and monitoring tools
   * **gRPC Communication:** Port 40000 for workflow management
   * **Custom Provisioning:** TurdParty-specific VM configuration

Applied Overlays:

{% for overlay in build_config.applied_overlays %}
* ``{{ overlay.name }}``: {{ overlay.description }}
{% endfor %}

Build Timeline
==============

.. figure:: {{ build_artifacts.timeline_chart_path }}
   :alt: Build Timeline Chart
   :align: center
   :width: 100%

   Build stage progression and timing analysis

Stage Details
-------------

{% for stage in build_timeline.stages %}
{{ loop.index }}. **{{ stage.name|title }}** ({{ stage.duration_formatted }})
   {{ "-" * (stage.name|length + 20) }}

   * **Started:** {{ stage.start_time }}
   * **Completed:** {{ stage.end_time }}
   * **Duration:** {{ stage.duration_formatted }}
   * **Status:** {{ "✅ Success" if stage.success else "❌ Failed" }}
   
   {% if stage.details %}
   **Details:**
   
   .. code-block:: text
   
      {{ stage.details | indent(6) }}
   {% endif %}

   {% if stage.errors %}
   .. warning::
      **Errors encountered:**
      
      {% for error in stage.errors %}
      * {{ error }}
      {% endfor %}
   {% endif %}

{% endfor %}

Performance Analysis
====================

Resource Usage
--------------

.. figure:: {{ build_artifacts.resource_chart_path }}
   :alt: Resource Usage Chart
   :align: center
   :width: 100%

   CPU, Memory, and Disk usage during build process

.. list-table:: Peak Resource Usage
   :widths: 30 35 35
   :header-rows: 1

   * - Resource
     - Peak Usage
     - Average Usage
   * - CPU Usage
     - {{ build_metrics.peak_cpu_usage }}%
     - {{ build_metrics.avg_cpu_usage }}%
   * - Memory Usage
     - {{ build_metrics.peak_memory_usage }}MB
     - {{ build_metrics.avg_memory_usage }}MB
   * - Disk I/O
     - {{ build_metrics.peak_disk_io }}MB/s
     - {{ build_metrics.avg_disk_io }}MB/s

Bottleneck Analysis
-------------------

{% if build_metrics.bottlenecks %}
.. warning::
   **Performance Bottlenecks Identified:**

   {% for bottleneck in build_metrics.bottlenecks %}
   * **{{ bottleneck.stage }}:** {{ bottleneck.description }}
     
     *Recommendation:* {{ bottleneck.recommendation }}
   {% endfor %}
{% else %}
.. note::
   No significant performance bottlenecks detected during this build.
{% endif %}

Issues and Resolutions
======================

{% if build_issues.errors %}
Errors Encountered
------------------

{% for error in build_issues.errors %}
{{ loop.index }}. **{{ error.stage }}** - {{ error.title }}
   {{ "-" * (error.stage|length + error.title|length + 5) }}

   **Error Message:**
   
   .. code-block:: text
   
      {{ error.message | indent(6) }}

   **Resolution:**
   
   {{ error.resolution }}

   **Prevention:**
   
   {{ error.prevention_advice }}

{% endfor %}
{% endif %}

{% if build_issues.warnings %}
Warnings and Recommendations
-----------------------------

{% for warning in build_issues.warnings %}
* **{{ warning.category }}:** {{ warning.message }}
  
  *Recommendation:* {{ warning.recommendation }}
{% endfor %}
{% endif %}

Build Artifacts
===============

Generated Files
---------------

.. list-table:: Build Outputs
   :widths: 40 30 30
   :header-rows: 1

   * - File
     - Size
     - Description
   {% for artifact in build_artifacts.files %}
   * - :download:`{{ artifact.name }} <{{ artifact.path }}>`
     - {{ artifact.size_formatted }}
     - {{ artifact.description }}
   {% endfor %}

Log Files
---------

.. list-table:: Debug and Log Files
   :widths: 40 60
   :header-rows: 1

   * - Log File
     - Description
   {% for log in build_artifacts.logs %}
   * - :download:`{{ log.name }} <{{ log.path }}>`
     - {{ log.description }}
   {% endfor %}

Quality Metrics
===============

Build Reproducibility
----------------------

.. admonition:: Reproducibility Score: {{ build_quality.reproducibility_score }}/100
   :class: {{ "note" if build_quality.reproducibility_score >= 80 else "warning" }}

   This build can be reproduced with {{ build_quality.reproducibility_score }}% confidence.

   **Factors affecting reproducibility:**
   
   {% for factor in build_quality.reproducibility_factors %}
   * {{ factor }}
   {% endfor %}

Security Configuration
----------------------

.. list-table:: Security Validation
   :widths: 40 20 40
   :header-rows: 1

   * - Security Check
     - Status
     - Notes
   {% for check in build_quality.security_checks %}
   * - {{ check.name }}
     - {{ "✅ Pass" if check.passed else "❌ Fail" }}
     - {{ check.notes }}
   {% endfor %}

Comparison with Previous Builds
===============================

{% if build_comparison.previous_builds %}
.. figure:: {{ build_artifacts.comparison_chart_path }}
   :alt: Build Comparison Chart
   :align: center
   :width: 100%

   Performance comparison with previous builds

.. list-table:: Build Trend Analysis
   :widths: 25 25 25 25
   :header-rows: 1

   * - Build Date
     - Duration
     - VM Size
     - Success Rate
   {% for prev_build in build_comparison.previous_builds %}
   * - {{ prev_build.date }}
     - {{ prev_build.duration }}
     - {{ prev_build.vm_size }}
     - {{ prev_build.success_rate }}%
   {% endfor %}
{% else %}
.. note::
   This is the first build of this VM type. Future builds will include comparison data.
{% endif %}

Technical Appendix
==================

Environment Information
-----------------------

.. code-block:: yaml

   host_system:
     hostname: {{ build_environment.hostname }}
     os: {{ build_environment.os }}
     kernel: {{ build_environment.kernel }}
     cpu: {{ build_environment.cpu }}
     memory: {{ build_environment.total_memory }}
     
   virtualization:
     kvm_available: {{ build_environment.kvm_available }}
     qemu_version: {{ build_environment.qemu_version }}
     packer_version: {{ build_environment.packer_version }}

Build Command
-------------

.. code-block:: bash

   {{ build_metadata.build_command }}

Raw Build Data
--------------

For advanced analysis, the raw build data is available in JSON format:

:download:`Raw Build Data (JSON) <{{ build_artifacts.raw_data_path }}>`

---

*Report generated on {{ build_metadata.report_generation_time }} by 💩🎉TurdParty🎉💩 Build Report System v{{ build_metadata.report_version }}*
