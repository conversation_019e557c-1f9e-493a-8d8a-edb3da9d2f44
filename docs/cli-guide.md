# 💩🎉TurdParty🎉💩 CLI Guide

A beautiful, modern command-line interface for managing TurdParty malware analysis services, built with Click and Rich libraries for a BTOP-like aesthetic.

## 🚀 Quick Start

### Prerequisites

The CLI uses <PERSON> for dependency management. All required packages are included in the Nix shell:

```bash
# Enter the Nix development environment
nix-shell

# The CLI is now available
./turdparty --help
```

### Dependencies Included in Nix

- `python312Packages.click` - Command-line interface framework
- `python312Packages.rich` - Beautiful terminal output
- `python312Packages.requests` - HTTP client for API calls

## 📋 Available Commands

### 🏠 Main Dashboard

```bash
# Show the main service dashboard (default command)
./turdparty
./turdparty status
```

### 🚀 Service Management

```bash
# Start all services with dependency validation
./turdparty start

# Start services and show logs
./turdparty start --logs

# Start services with interactive monitoring (NEW!)
./turdparty start --interactive
./turdparty start -i

# Start with comprehensive options (NEW!)
./turdparty start -i --monitor-interval 3 --rebuild-docs --no-animation
./turdparty start --parallel --skip-traefik-check --timeout 600

# Stop all services
./turdparty stop

# Restart a specific service
./turdparty restart api
./turdparty restart database
```

### 📊 Monitoring & Status

```bash
# Real-time monitoring dashboard (like BTOP)
./turdparty monitor

# Monitor with custom refresh interval (5 seconds)
./turdparty monitor -i 5

# Advanced monitoring with filtering (NEW!)
./turdparty monitor --filter-service api --show-metrics
./turdparty monitor --filter-status healthy --compact
./turdparty monitor --output-format json --export-file monitoring.json

# Show detailed service status
./turdparty status
```

### 🎮 Interactive Startup Mode

The new interactive startup mode combines service startup with real-time monitoring:

```bash
# Start services and enter interactive monitoring
./turdparty start --interactive

# Interactive mode with custom refresh interval
./turdparty start -i --monitor-interval 3
```

**Interactive Mode Features:**
- **Real-time Dashboard**: Live service status updates after startup
- **Hotkey Controls**: Press `B` to background, `Q` to quit, or `Ctrl+C` to exit
- **Background Mode**: Services continue running when backgrounded
- **Seamless Transition**: From startup progress to live monitoring

**Hotkey Reference:**
- `B` - Background the monitoring process (services keep running)
- `Q` - Quit monitoring (services keep running)
- `Ctrl+C` - Exit monitoring (services keep running)

### 📚 Documentation Management

Comprehensive documentation building and management:

```bash
# Build documentation
./turdparty docs --build

# Build with API extraction and serve locally
./turdparty docs --build --api-docs --serve --open-browser

# Watch mode for development
./turdparty docs --watch

# Clean rebuild
./turdparty docs --clean --build --format html

# Serve existing documentation
./turdparty docs --serve --port 8080
```

**Documentation Features:**
- **API Integration**: Automatic OpenAPI spec extraction
- **Live Serving**: Built-in HTTP server with browser opening
- **Watch Mode**: Automatic rebuilds on file changes
- **Multiple Formats**: HTML, PDF, and EPUB support
- **Clean Builds**: Option to clean before rebuilding

### 🔧 Administrative Operations

System administration and service management:

```bash
# Show admin dashboard
./turdparty admin

# Service-specific operations
./turdparty admin --service api
./turdparty admin --service database --backup

# System operations with safety features
./turdparty admin --service all --dry-run
./turdparty admin --service redis --force
```

**Admin Features:**
- **Service Management**: Individual service operations
- **Safety Checks**: Dry-run mode and confirmation prompts
- **Health Monitoring**: Real-time service status checks
- **Backup Operations**: Data protection for destructive operations

### 📋 Enhanced Logging

Advanced log viewing with filtering and formatting:

```bash
# Basic log viewing
./turdparty logs api

# Advanced filtering (NEW!)
./turdparty logs api --level ERROR --since "2023-01-01T00:00:00Z"
./turdparty logs database --grep "connection" --json-format
./turdparty logs frontend --follow --output-file frontend.log

# Time-based filtering
./turdparty logs api --since "1 hour ago" --until "now"
```

**Logging Features:**
- **Level Filtering**: Filter by DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Pattern Matching**: Grep-style pattern filtering
- **Time Ranges**: Since/until timestamp filtering
- **Output Formats**: Plain text or structured JSON
- **File Export**: Save logs to files for analysis

### 🔍 Health Checks

```bash
# Run comprehensive dependency checks
./turdparty check

# This validates:
# - Traefik container status
# - Traefik API accessibility
# - Network configuration
# - Service discovery
```

### 📋 Logs

```bash
# Show logs for a specific service
./turdparty logs api

# Follow logs in real-time
./turdparty logs api -f

# Show last 100 lines
./turdparty logs api -n 100

# Follow logs for multiple services
./turdparty logs api -f
./turdparty logs database -f
```

### 🏗️ Advanced Operations

```bash
# Rebuild all services from scratch
./turdparty rebuild

# Run the parallel test suite
./turdparty test
```

## 🎨 CLI Features

### Beautiful Interface

- **Rich Terminal Output**: Colorful, formatted output using the Rich library
- **Progress Bars**: Visual progress indicators for long-running operations
- **Status Tables**: Beautiful service status tables with emojis and colors
- **Real-time Updates**: Live monitoring dashboard with automatic refresh

### BTOP-like Aesthetics

- **Color-coded Status**: 🟢 Green for healthy, 🟡 Yellow for warnings, 🔴 Red for errors
- **Box Layouts**: Organized panels and sections
- **Live Monitoring**: Real-time service status updates
- **Interactive Elements**: Confirmation prompts and user input

### Smart Dependency Management

- **Nix Integration**: Automatic dependency detection in Nix shell
- **Fallback Support**: Works with system Python if Nix unavailable
- **Clear Error Messages**: Helpful guidance when dependencies missing

## 🔧 Technical Details

### Architecture

```
scripts/turdparty              # Main wrapper script (Bash)
├── Dependency checking        # Nix-aware dependency validation
├── Environment setup          # Project directory validation
└── CLI execution             # Python CLI with Rich interface

scripts/turdparty-cli.py       # Core CLI implementation (Python)
├── Click commands            # Command-line interface framework
├── Rich components           # Beautiful terminal output
├── Service management        # Docker Compose integration
└── Real-time monitoring      # Live dashboard updates
```

### Integration with Existing Scripts

The CLI leverages existing TurdParty scripts:

- `scripts/check-traefik-dependency.sh` - Dependency validation
- `scripts/start-turdparty.sh` - Service startup
- `scripts/rebuild-services.sh` - Service rebuilding
- `scripts/run-parallel-tests.sh` - Test execution

### Error Handling

- **Graceful Failures**: Clear error messages with suggested solutions
- **Dependency Validation**: Immediate feedback on missing requirements
- **Service Status**: Real-time health monitoring with detailed diagnostics

## 🎯 Usage Examples

### Daily Workflow

```bash
# 1. Enter development environment
nix-shell

# 2. Check system health
./turdparty check

# 3. Start services with interactive monitoring (recommended)
./turdparty start --interactive

# Alternative: Start services and show logs
./turdparty start --logs

# 4. Monitor in real-time (if not using interactive mode)
./turdparty monitor

# 5. View specific service logs
./turdparty logs api -f
```

### Troubleshooting

```bash
# Check if Traefik is running
./turdparty check

# Restart problematic service
./turdparty restart api

# Rebuild everything if needed
./turdparty rebuild

# Run tests to verify functionality
./turdparty test
```

### Development Workflow

```bash
# Start services for development with interactive monitoring
./turdparty start --interactive --monitor-interval 2

# Alternative: Start services and monitor separately
./turdparty start
./turdparty monitor -i 2

# Restart API after code changes
./turdparty restart api

# Run tests after changes
./turdparty test
```

## 🎉 Benefits

### For Developers

- **Faster Workflow**: Single command for complex operations
- **Visual Feedback**: Beautiful, informative output
- **Error Prevention**: Dependency validation before operations
- **Real-time Monitoring**: Live service status updates

### For Operations

- **Consistent Interface**: Standardized commands across environments
- **Comprehensive Checks**: Built-in health validation
- **Clear Status**: Visual service health indicators
- **Easy Troubleshooting**: Integrated log viewing and service management

### For Users

- **Modern UX**: BTOP-like interface with rich colors and layouts
- **Intuitive Commands**: Self-documenting command structure
- **Helpful Output**: Clear success/error messages with guidance
- **Interactive Elements**: Confirmation prompts for destructive operations

The 💩🎉TurdParty🎉💩 CLI provides a modern, beautiful interface for managing the malware analysis platform, making complex operations simple and visually appealing!
