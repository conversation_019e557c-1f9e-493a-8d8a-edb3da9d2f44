Configuration Reference
=======================

This document provides comprehensive configuration options for the TurdParty malware analysis platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Environment Configuration
-------------------------

Core Platform Settings
~~~~~~~~~~~~~~~~~~~~~~~

**Project Configuration**

.. code-block:: bash

   # Project namespace and domain
   COMPOSE_PROJECT_NAME=turdpartycollab
   TURDPARTY_DOMAIN=turdparty.localhost
   
   # Environment type
   ENVIRONMENT=development  # development, staging, production
   DEBUG=true              # Enable debug mode
   LOG_LEVEL=INFO          # DEBUG, INFO, WARNING, ERROR, CRITICAL

**API Server Configuration**

.. code-block:: bash

   # API server settings
   API_HOST=0.0.0.0
   API_PORT=8000
   API_WORKERS=4
   API_TIMEOUT=300
   
   # CORS settings
   ENABLE_CORS=true
   ALLOWED_ORIGINS=*
   ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
   ALLOWED_HEADERS=*

Database Configuration
~~~~~~~~~~~~~~~~~~~~~~

**PostgreSQL Settings**

.. code-block:: bash

   # Database connection
   POSTGRES_HOST=postgres
   POSTGRES_PORT=5432
   POSTGRES_DB=turdparty
   POSTGRES_USER=turdparty
   POSTGRES_PASSWORD=your_secure_password
   
   # Connection pool settings
   DB_POOL_SIZE=20
   DB_MAX_OVERFLOW=30
   DB_POOL_TIMEOUT=30
   DB_POOL_RECYCLE=3600

**Redis Configuration**

.. code-block:: bash

   # Redis connection
   REDIS_HOST=redis
   REDIS_PORT=6379
   REDIS_PASSWORD=your_secure_password
   REDIS_DB=0
   
   # Connection settings
   REDIS_SOCKET_TIMEOUT=5
   REDIS_SOCKET_CONNECT_TIMEOUT=5
   REDIS_RETRY_ON_TIMEOUT=true
   REDIS_MAX_CONNECTIONS=50

Storage Configuration
~~~~~~~~~~~~~~~~~~~~~

**MinIO Object Storage**

.. code-block:: bash

   # MinIO connection
   MINIO_ENDPOINT=minio:9000
   MINIO_ROOT_USER=turdparty
   MINIO_ROOT_PASSWORD=your_secure_password
   MINIO_SECURE=false
   
   # Bucket configuration
   MINIO_BUCKET_FILES=turdparty-files
   MINIO_BUCKET_RESULTS=turdparty-results
   MINIO_BUCKET_LOGS=turdparty-logs
   
   # Upload settings
   MAX_FILE_SIZE=1073741824  # 1GB in bytes
   ALLOWED_FILE_TYPES=*      # * for all, or comma-separated list

Worker Configuration
--------------------

Celery Settings
~~~~~~~~~~~~~~~

**Task Queue Configuration**

.. code-block:: bash

   # Celery broker settings
   CELERY_BROKER_URL=redis://redis:6379/0
   CELERY_RESULT_BACKEND=redis://redis:6379/0
   
   # Task settings
   CELERY_TASK_SERIALIZER=json
   CELERY_RESULT_SERIALIZER=json
   CELERY_ACCEPT_CONTENT=json
   CELERY_TIMEZONE=UTC
   CELERY_ENABLE_UTC=true
   
   # Task routing
   CELERY_TASK_ROUTES={
       "tasks.file_operations.*": {"queue": "file_ops"},
       "tasks.vm_management.*": {"queue": "vm_ops"},
       "tasks.injection_tasks.*": {"queue": "injection_ops"},
       "tasks.vm_pool_manager.*": {"queue": "pool_ops"},
       "tasks.workflow_orchestrator.*": {"queue": "workflow_ops"},
       "tasks.elk_integration.*": {"queue": "elk_ops"}
   }

**Worker Concurrency Settings**

.. code-block:: bash

   # Worker concurrency per type
   WORKER_CONCURRENCY_FILE_OPS=2
   WORKER_CONCURRENCY_VM_OPS=1
   WORKER_CONCURRENCY_INJECTION_OPS=2
   WORKER_CONCURRENCY_POOL_OPS=1
   WORKER_CONCURRENCY_WORKFLOW_OPS=2
   WORKER_CONCURRENCY_ELK_OPS=3
   
   # Worker resource limits
   WORKER_MAX_MEMORY_PER_CHILD=1000000  # KB
   WORKER_MAX_TASKS_PER_CHILD=1000
   WORKER_PREFETCH_MULTIPLIER=1

VM Management Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**VM Pool Settings**

.. code-block:: bash

   # Pool size management
   VM_POOL_MIN_SIZE=2
   VM_POOL_MAX_SIZE=10
   VM_POOL_TARGET_SIZE=5
   
   # Pool maintenance
   VM_POOL_MAINTENANCE_INTERVAL=300  # seconds
   VM_POOL_CLEANUP_INTERVAL=600      # seconds
   VM_POOL_HEALTH_CHECK_INTERVAL=60  # seconds

**VM Lifecycle Settings**

.. code-block:: bash

   # VM execution limits
   VM_MAX_EXECUTION_TIME=1800        # 30 minutes
   VM_STARTUP_TIMEOUT=300            # 5 minutes
   VM_SHUTDOWN_TIMEOUT=60            # 1 minute
   
   # VM resource limits
   VM_DEFAULT_MEMORY_MB=1024
   VM_DEFAULT_CPUS=1
   VM_DEFAULT_DISK_GB=20
   
   # VM templates
   VM_DEFAULT_TEMPLATE=ubuntu:20.04
   VM_AVAILABLE_TEMPLATES=ubuntu:20.04,ubuntu:22.04,alpine:latest

ELK Stack Configuration
-----------------------

Elasticsearch Settings
~~~~~~~~~~~~~~~~~~~~~~~

**Connection Configuration**

.. code-block:: bash

   # Elasticsearch connection
   ELASTICSEARCH_HOST=elasticsearch
   ELASTICSEARCH_PORT=9200
   ELASTICSEARCH_SCHEME=http
   ELASTICSEARCH_USERNAME=elastic
   ELASTICSEARCH_PASSWORD=your_secure_password
   
   # Index settings
   ELASTICSEARCH_INDEX_PREFIX=turdparty
   ELASTICSEARCH_INDEX_ROTATION=daily
   ELASTICSEARCH_INDEX_REPLICAS=0
   ELASTICSEARCH_INDEX_SHARDS=1

**Data Retention**

.. code-block:: bash

   # Retention policies (days)
   ELASTICSEARCH_RETENTION_WORKFLOW=30
   ELASTICSEARCH_RETENTION_VM_MONITORING=7
   ELASTICSEARCH_RETENTION_RUNTIME=14
   ELASTICSEARCH_RETENTION_INSTALL=7

Logstash Configuration
~~~~~~~~~~~~~~~~~~~~~~

**Pipeline Settings**

.. code-block:: bash

   # Logstash connection
   LOGSTASH_HOST=logstash
   LOGSTASH_PORT=8080
   LOGSTASH_PROTOCOL=http
   
   # Pipeline configuration
   LOGSTASH_BATCH_SIZE=125
   LOGSTASH_BATCH_DELAY=50
   LOGSTASH_PIPELINE_WORKERS=2

Kibana Settings
~~~~~~~~~~~~~~~

**Dashboard Configuration**

.. code-block:: bash

   # Kibana connection
   KIBANA_HOST=kibana
   KIBANA_PORT=5601
   KIBANA_USERNAME=kibana_system
   KIBANA_PASSWORD=your_secure_password
   
   # Dashboard settings
   KIBANA_DEFAULT_INDEX=turdparty-*
   KIBANA_REFRESH_INTERVAL=30s
   KIBANA_TIME_FILTER=24h

Monitoring Configuration
------------------------

VM Agent Settings
~~~~~~~~~~~~~~~~~

**Agent Configuration**

.. code-block:: bash

   # VM agent settings
   VM_AGENT_COLLECTION_INTERVAL=5    # seconds
   VM_AGENT_BATCH_SIZE=10
   VM_AGENT_BUFFER_SIZE=100
   VM_AGENT_TIMEOUT=30
   
   # Monitoring paths
   VM_AGENT_MONITOR_PATHS=/tmp,/home,/var/log
   VM_AGENT_EXCLUDE_PATHS=/proc,/sys,/dev
   
   # Suspicious activity detection
   VM_AGENT_ENABLE_THREAT_DETECTION=true
   VM_AGENT_CPU_THRESHOLD=80
   VM_AGENT_MEMORY_THRESHOLD=90
   VM_AGENT_DISK_THRESHOLD=95

**Data Streaming**

.. code-block:: bash

   # ELK streaming settings
   VM_AGENT_ELK_ENDPOINT=http://logstash:8080
   VM_AGENT_STREAM_BUFFER_SIZE=50
   VM_AGENT_STREAM_TIMEOUT=10
   VM_AGENT_RETRY_ATTEMPTS=3
   VM_AGENT_RETRY_DELAY=5

Security Configuration
----------------------

Authentication Settings
~~~~~~~~~~~~~~~~~~~~~~~

**API Authentication**

.. code-block:: bash

   # Authentication settings
   ENABLE_AUTHENTICATION=false      # Enable for production
   JWT_SECRET_KEY=your_jwt_secret
   JWT_ALGORITHM=HS256
   JWT_EXPIRATION_HOURS=24
   
   # API key settings
   API_KEY_HEADER=X-API-Key
   API_KEY_LENGTH=32
   API_KEY_EXPIRATION_DAYS=365

**Session Management**

.. code-block:: bash

   # Session configuration
   SESSION_SECRET_KEY=your_session_secret
   SESSION_TIMEOUT=3600             # seconds
   SESSION_SECURE=false             # true for HTTPS
   SESSION_HTTPONLY=true
   SESSION_SAMESITE=lax

Network Security
~~~~~~~~~~~~~~~~

**SSL/TLS Configuration**

.. code-block:: bash

   # SSL settings
   ENABLE_SSL=false                 # Enable for production
   SSL_CERT_PATH=/certs/cert.pem
   SSL_KEY_PATH=/certs/key.pem
   SSL_CA_PATH=/certs/ca.pem
   
   # Security headers
   ENABLE_SECURITY_HEADERS=true
   HSTS_MAX_AGE=31536000
   CONTENT_SECURITY_POLICY=default-src 'self'

**Network Isolation**

.. code-block:: bash

   # Network settings
   DOCKER_NETWORK=turdpartycollab_net
   TRAEFIK_NETWORK=traefik_network
   
   # VM network isolation
   VM_NETWORK_ISOLATION=true
   VM_INTERNET_ACCESS=limited       # none, limited, full
   VM_DNS_SERVERS=8.8.8.8,8.8.4.4

Performance Configuration
-------------------------

Resource Limits
~~~~~~~~~~~~~~~

**Memory Management**

.. code-block:: bash

   # Memory limits (MB)
   API_MEMORY_LIMIT=2048
   WORKER_MEMORY_LIMIT=1024
   VM_MEMORY_LIMIT=1024
   ELK_MEMORY_LIMIT=4096
   
   # Memory optimization
   ENABLE_MEMORY_PROFILING=false
   MEMORY_CLEANUP_INTERVAL=3600     # seconds

**CPU Management**

.. code-block:: bash

   # CPU limits
   API_CPU_LIMIT=2.0
   WORKER_CPU_LIMIT=1.0
   VM_CPU_LIMIT=1.0
   ELK_CPU_LIMIT=4.0
   
   # CPU optimization
   ENABLE_CPU_PROFILING=false
   CPU_AFFINITY_ENABLED=false

Caching Configuration
~~~~~~~~~~~~~~~~~~~~~

**Application Caching**

.. code-block:: bash

   # Cache settings
   ENABLE_CACHING=true
   CACHE_TTL=3600                   # seconds
   CACHE_MAX_SIZE=1000              # entries
   
   # Specific cache TTLs
   CACHE_TTL_VM_STATUS=60
   CACHE_TTL_POOL_STATUS=300
   CACHE_TTL_HEALTH_CHECK=30

Development Configuration
-------------------------

Debug Settings
~~~~~~~~~~~~~~

**Development Mode**

.. code-block:: bash

   # Development settings
   DEVELOPMENT_MODE=true
   ENABLE_DEBUG_TOOLBAR=true
   ENABLE_PROFILING=true
   
   # Hot reloading
   ENABLE_AUTO_RELOAD=true
   WATCH_FILES=true
   RELOAD_DELAY=1

**Testing Configuration**

.. code-block:: bash

   # Test settings
   TEST_DATABASE_URL=postgresql://test:test@localhost/test_turdparty
   TEST_REDIS_URL=redis://localhost:6379/1
   TEST_ELASTICSEARCH_URL=http://localhost:9200
   
   # Test data
   ENABLE_TEST_DATA=false
   TEST_DATA_PATH=tests/fixtures

Logging Configuration
~~~~~~~~~~~~~~~~~~~~~

**Log Settings**

.. code-block:: bash

   # Logging configuration
   LOG_FORMAT=json                  # json, text
   LOG_LEVEL=INFO
   LOG_FILE_PATH=/var/log/turdparty
   LOG_MAX_SIZE=100MB
   LOG_BACKUP_COUNT=5
   
   # ECS logging
   ENABLE_ECS_LOGGING=true
   ECS_VERSION=8.11.0

Production Configuration
------------------------

Production Optimizations
~~~~~~~~~~~~~~~~~~~~~~~~

**Performance Settings**

.. code-block:: bash

   # Production optimizations
   ENABLE_GZIP=true
   ENABLE_ETAG=true
   ENABLE_STATIC_CACHE=true
   STATIC_CACHE_MAX_AGE=86400
   
   # Database optimizations
   DB_STATEMENT_TIMEOUT=30000       # milliseconds
   DB_LOCK_TIMEOUT=10000           # milliseconds
   DB_IDLE_IN_TRANSACTION_TIMEOUT=60000

**Security Hardening**

.. code-block:: bash

   # Production security
   ENABLE_RATE_LIMITING=true
   RATE_LIMIT_PER_MINUTE=60
   RATE_LIMIT_BURST=10
   
   # Security monitoring
   ENABLE_SECURITY_LOGGING=true
   ENABLE_INTRUSION_DETECTION=true
   SECURITY_LOG_LEVEL=WARNING

Configuration Validation
-------------------------

Environment Validation
~~~~~~~~~~~~~~~~~~~~~~

The platform includes built-in configuration validation:

.. code-block:: bash

   # Validate configuration
   ./scripts/validate-config.sh
   
   # Check specific components
   ./scripts/validate-config.sh --component=database
   ./scripts/validate-config.sh --component=elk
   ./scripts/validate-config.sh --component=workers

**Common Validation Errors**:

- Missing required environment variables
- Invalid database connection parameters
- Insufficient memory allocation
- Network connectivity issues
- SSL certificate problems

Configuration Templates
-----------------------

Development Template
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # .env.development
   ENVIRONMENT=development
   DEBUG=true
   LOG_LEVEL=DEBUG
   ENABLE_AUTHENTICATION=false
   VM_POOL_MIN_SIZE=1
   VM_POOL_MAX_SIZE=3
   ELASTICSEARCH_RETENTION_WORKFLOW=7

Production Template
~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # .env.production
   ENVIRONMENT=production
   DEBUG=false
   LOG_LEVEL=INFO
   ENABLE_AUTHENTICATION=true
   ENABLE_SSL=true
   ENABLE_RATE_LIMITING=true
   VM_POOL_MIN_SIZE=5
   VM_POOL_MAX_SIZE=20
   ELASTICSEARCH_RETENTION_WORKFLOW=90

Configuration Management
------------------------

Best Practices
~~~~~~~~~~~~~~

1. **Use Environment-Specific Files**: Separate configurations for dev/staging/prod
2. **Secure Secrets**: Use secret management tools for sensitive data
3. **Validate Configuration**: Always validate before deployment
4. **Document Changes**: Keep configuration changes documented
5. **Backup Configuration**: Maintain configuration backups

**Configuration Hierarchy**:

1. Environment variables (highest priority)
2. .env file
3. Default values in code (lowest priority)

For troubleshooting configuration issues, see :doc:`reference/troubleshooting`.
For deployment-specific configuration, see :doc:`deployment/production`.
