VM Management Deployment
========================

This guide covers the deployment and configuration of the 💩🎉 TurdParty VM management system, including database integration, pool management, and Elasticsearch logging.

Overview
--------

The VM management system provides:

- **Immediate VM Allocation**: Pre-provisioned VMs for instant use
- **Automatic Pool Maintenance**: Background tasks maintain optimal pool sizes
- **Database Integration**: PostgreSQL for persistent VM state tracking
- **Elasticsearch Logging**: Comprehensive operation logging and monitoring
- **WebSocket Support**: Real-time VM metrics and command execution

Prerequisites
-------------

**Infrastructure Requirements:**

- PostgreSQL database with VM schema
- Redis for Celery task queue
- Elasticsearch for logging (optional but recommended)
- Docker or Vagrant for VM provisioning
- Traefik for service routing

**Environment Variables:**

.. code-block:: bash

   # Database Configuration
   DATABASE_URL=********************************************/turdparty
   
   # Redis Configuration
   REDIS_URL=redis://redis:6379/0
   
   # Elasticsearch Configuration (optional)
   ELASTICSEARCH_URL=http://elasticsearch:9200
   ELASTICSEARCH_LOGGING_ENABLED=true
   
   # Service URLs
   API_BASE_URL=http://api.turdparty.localhost
   TRAEFIK_ENABLED=true

Deployment Scripts
------------------

**Pre-flight Check:**

.. code-block:: bash

   # Run comprehensive pre-flight check
   python scripts/preflight_check.py
   
   # Check specific components
   python scripts/preflight_check.py --check-database
   python scripts/preflight_check.py --check-elasticsearch
   python scripts/preflight_check.py --check-traefik

**VM Management Deployment:**

.. code-block:: bash

   # Deploy VM management system
   python scripts/deploy_vm_management.py
   
   # Deploy with specific configuration
   python scripts/deploy_vm_management.py --environment production
   python scripts/deploy_vm_management.py --dry-run

**Database Migration:**

.. code-block:: bash

   # Run database migrations
   python scripts/migrate_vm_pool_schema.py
   
   # Run in container
   python scripts/run_migration_in_container.py

**Infrastructure Testing:**

.. code-block:: bash

   # Test infrastructure readiness
   python scripts/test_infrastructure_readiness.py
   
   # Test VM management core
   python scripts/test_vm_management_core.py
   
   # Test basic integration
   python scripts/test_basic_vm_integration.py

Database Setup
--------------

**Schema Creation:**

.. code-block:: sql

   -- Create VM instances table
   CREATE TABLE vm_instances (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       name VARCHAR(255) NOT NULL,
       template VARCHAR(255) NOT NULL,
       status VARCHAR(50) NOT NULL,
       memory_mb INTEGER,
       cpus INTEGER,
       disk_gb INTEGER,
       ip_address INET,
       ssh_port INTEGER,
       vm_type VARCHAR(50) DEFAULT 'docker',
       domain VARCHAR(255) DEFAULT 'TurdParty',
       created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
       allocated_at TIMESTAMP WITH TIME ZONE,
       started_at TIMESTAMP WITH TIME ZONE,
       terminated_at TIMESTAMP WITH TIME ZONE,
       runtime_minutes DECIMAL(10,2) DEFAULT 0.0,
       description TEXT
   );

   -- Create indexes for performance
   CREATE INDEX idx_vm_instances_status ON vm_instances(status);
   CREATE INDEX idx_vm_instances_template ON vm_instances(template);
   CREATE INDEX idx_vm_instances_created_at ON vm_instances(created_at);

**Data Validation:**

.. code-block:: bash

   # Validate existing data
   python -c "
   from services.workers.tasks.basic_vm_availability_manager import availability_manager
   status = availability_manager.get_pool_status()
   print(f'Pool status: {status}')
   "

Celery Configuration
--------------------

**Worker Configuration:**

.. code-block:: python

   # celery_app.py
   from celery import Celery
   from celery.schedules import crontab
   
   app = Celery('turdparty')
   
   # Scheduled tasks
   app.conf.beat_schedule = {
       'maintain-vm-pools': {
           'task': 'services.workers.tasks.basic_vm_availability_manager.maintain_vm_pools',
           'schedule': crontab(minute='*/5'),  # Every 5 minutes
       },
   }

**Task Registration:**

.. code-block:: bash

   # Start Celery worker
   celery -A services.workers.celery_app worker --loglevel=info
   
   # Start Celery beat scheduler
   celery -A services.workers.celery_app beat --loglevel=info
   
   # Monitor tasks
   celery -A services.workers.celery_app flower

API Deployment
--------------

**FastAPI Configuration:**

.. code-block:: python

   # Include VM management routes
   from services.api.src.routes.v1.basic_vm_allocation import router as vm_allocation_router
   
   app.include_router(
       vm_allocation_router,
       prefix="/api/v1/vm-allocation",
       tags=["vm_allocation"]
   )

**Health Checks:**

.. code-block:: bash

   # Test API endpoints
   curl http://api.turdparty.localhost/api/v1/vm-allocation/pools/status
   curl -X POST http://api.turdparty.localhost/api/v1/vm-allocation/allocate \
        -H "Content-Type: application/json" \
        -d '{"template": "ubuntu:20.04"}'

WebSocket Configuration
-----------------------

**Traefik Integration:**

.. code-block:: yaml

   # docker-compose.yml
   services:
     api:
       labels:
         - "traefik.http.routers.api.rule=Host(`api.turdparty.localhost`)"
         - "traefik.http.services.api.loadbalancer.server.port=8000"
         # WebSocket support
         - "traefik.http.routers.api.middlewares=websocket-headers"
         - "traefik.http.middlewares.websocket-headers.headers.customrequestheaders.Connection=Upgrade"
         - "traefik.http.middlewares.websocket-headers.headers.customrequestheaders.Upgrade=websocket"

**WebSocket Testing:**

.. code-block:: bash

   # Test WebSocket connectivity
   python -c "
   import asyncio
   import websockets
   from utils.service_urls import ServiceURLManager
   
   async def test():
       manager = ServiceURLManager('development')
       api_url = manager.get_service_url('api')
       ws_url = api_url.replace('http://', 'ws://')
       
       async with websockets.connect(f'{ws_url}/test-ws') as ws:
           message = await ws.recv()
           print(f'WebSocket test: {message}')
   
   asyncio.run(test())
   "

Elasticsearch Configuration
----------------------------

**Index Templates:**

.. code-block:: json

   {
     "index_patterns": ["vm-operations-*"],
     "template": {
       "mappings": {
         "properties": {
           "@timestamp": {"type": "date"},
           "event_type": {"type": "keyword"},
           "vm_id": {"type": "keyword"},
           "template": {"type": "keyword"},
           "correlation_id": {"type": "keyword"},
           "duration_ms": {"type": "integer"},
           "success": {"type": "boolean"}
         }
       }
     }
   }

**Kibana Dashboards:**

.. code-block:: bash

   # Import VM management dashboards
   curl -X POST "kibana.turdparty.localhost/api/saved_objects/_import" \
        -H "kbn-xsrf: true" \
        -F file=@vm_management_dashboard.ndjson

Monitoring and Alerts
----------------------

**Key Metrics to Monitor:**

- VM pool sizes and availability
- Allocation success rates and response times
- Database connection health
- Elasticsearch logging status
- WebSocket connection counts

**Health Check Endpoints:**

.. code-block:: bash

   # System health
   curl http://api.turdparty.localhost/health
   
   # VM pool status
   curl http://api.turdparty.localhost/api/v1/vm-allocation/pools/status
   
   # Database connectivity
   curl http://api.turdparty.localhost/api/v1/health/database

Production Considerations
-------------------------

**Security:**

- Use secure database connections (SSL)
- Implement proper authentication for API endpoints
- Secure WebSocket connections with WSS
- Network isolation for VM environments

**Performance:**

- Monitor database query performance
- Optimize pool sizes based on usage patterns
- Implement connection pooling for database
- Use Redis clustering for high availability

**Scaling:**

- Horizontal scaling of API instances
- Database read replicas for query performance
- Elasticsearch cluster for logging resilience
- Load balancing for WebSocket connections

**Backup and Recovery:**

- Regular database backups
- VM template versioning
- Configuration backup
- Disaster recovery procedures

Troubleshooting
---------------

**Common Issues:**

1. **Database Connection Failures**
   - Check DATABASE_URL configuration
   - Verify PostgreSQL service status
   - Test connection manually

2. **VM Allocation Failures**
   - Check Docker/Vagrant service status
   - Verify template availability
   - Review resource constraints

3. **WebSocket Connection Issues**
   - Verify Traefik configuration
   - Check ServiceURLManager settings
   - Test basic WebSocket connectivity

4. **Elasticsearch Logging Failures**
   - Check ELASTICSEARCH_URL configuration
   - Verify Elasticsearch cluster health
   - Review index templates and mappings

**Debug Commands:**

.. code-block:: bash

   # Check service status
   docker ps | grep turdparty
   
   # View logs
   docker logs turdpartycollab_api
   docker logs turdpartycollab_workers
   
   # Test database connection
   python -c "
   from services.workers.tasks.basic_vm_availability_manager import SessionLocal
   with SessionLocal() as db:
       print('Database connection successful')
   "

The VM management deployment provides a robust, scalable foundation for the 💩🎉 TurdParty malware analysis platform with comprehensive monitoring and operational capabilities.
