💩🎉TurdParty🎉💩 Contributing Guide
===================================

Overview
--------

Welcome to TurdParty development! This guide provides everything you need to contribute effectively to the malware analysis platform. We use modern development practices with <PERSON> for reproducible environments, comprehensive testing, and automated quality checks.

Getting Started
---------------

Prerequisites
~~~~~~~~~~~~~

1. **Nix Package Manager** - For reproducible development environment
2. **Git** - Version control and collaboration
3. **GitHub Account** - For pull requests and issue tracking
4. **Docker** (optional) - For full service stack testing

**Quick Setup:**

.. code-block:: bash

   # Clone repository
   <NAME_EMAIL>:tenbahtsecurity/turdparty-collab.git
   cd turdparty-collab
   
   # Enter development environment
   nix-shell
   💩🎉 user@host:~/turdparty-collab (master) $
   
   # Verify setup
   tp-status

Development Workflow
--------------------

Branch Strategy
~~~~~~~~~~~~~~~

<PERSON><PERSON><PERSON><PERSON><PERSON> uses a specific git workflow:

.. list-table::
   :header-rows: 1
   :widths: 25 25 50

   * - Branch
     - Purpose
     - Push Destination
   * - **master**
     - Production releases
     - tenbahtsecurity/turdparty-collab
   * - **develop/local**
     - Development work
     - forkrul/turdparty-collab-develop_local
   * - **feature/***
     - Feature development
     - tenbahtsecurity/turdparty-collab
   * - **bugfix/***
     - Bug fixes
     - tenbahtsecurity/turdparty-collab

**For Core Contributors:**

.. code-block:: bash

   # Work on develop/local branch (pushes to forkrul)
   git checkout develop/local
   # ... make changes ...
   git add . && git commit -m "feat: new feature"
   git push  # Goes to forkrul, no time restrictions

**For External Contributors:**

.. code-block:: bash

   # Create feature branch
   git checkout -b feature/my-feature
   # ... make changes ...
   git add . && git commit -m "feat: new feature"
   git push origin feature/my-feature
   # Create pull request

Commit Guidelines
~~~~~~~~~~~~~~~~~

**Conventional Commits:**

Use the conventional commit format for clear history:

.. code-block:: bash

   # Format: type(scope): description
   
   feat(api): add WebSocket metrics streaming
   fix(vm): resolve container cleanup issue
   docs(nix): update development environment guide
   test(websocket): add integration tests for file upload
   refactor(database): optimize query performance
   style(frontend): fix linting issues
   chore(deps): update Python dependencies

**Commit Types:**

- ``feat`` - New features
- ``fix`` - Bug fixes
- ``docs`` - Documentation changes
- ``test`` - Test additions/modifications
- ``refactor`` - Code refactoring
- ``style`` - Code style changes
- ``chore`` - Maintenance tasks
- ``perf`` - Performance improvements
- ``ci`` - CI/CD changes

**Commit Message Guidelines:**

1. **Use imperative mood** - "Add feature" not "Added feature"
2. **Keep first line under 50 characters**
3. **Add detailed description if needed**
4. **Reference issues** - "Fixes #123" or "Closes #456"
5. **Include breaking changes** - Use "BREAKING CHANGE:" footer

Code Quality Standards
----------------------

Automated Quality Checks
~~~~~~~~~~~~~~~~~~~~~~~~

All code must pass automated quality checks:

.. code-block:: bash

   # Run all quality checks
   pre-commit run --all-files
   
   # Individual checks
   ruff check .          # Linting
   ruff format .         # Code formatting
   mypy .               # Type checking
   bandit -r .          # Security scanning

**Pre-commit Hooks:**

The repository includes pre-commit hooks that run automatically:

- **Ruff** - Fast Python linting and formatting
- **MyPy** - Static type checking
- **Bandit** - Security vulnerability scanning
- **Conventional Commits** - Commit message validation
- **File Checks** - Trailing whitespace, file endings

**Quality Standards:**

.. list-table::
   :header-rows: 1
   :widths: 30 70

   * - Standard
     - Requirement
   * - **PEP 8 Compliance**
     - All Python code must follow PEP 8
   * - **Type Hints**
     - All functions must have type annotations
   * - **Docstrings**
     - All public functions must have docstrings
   * - **Test Coverage**
     - Minimum 80% code coverage
   * - **Security**
     - No security vulnerabilities (Bandit)
   * - **Performance**
     - No performance regressions

Code Style Guidelines
~~~~~~~~~~~~~~~~~~~~

**Python Code Style:**

.. code-block:: python

   """Module docstring describing purpose."""
   
   from typing import Dict, List, Optional
   import logging
   
   logger = logging.getLogger(__name__)
   
   
   class VMService:
       """Service for managing virtual machines.
       
       This service provides methods for creating, managing, and monitoring
       virtual machines used in malware analysis.
       """
       
       def __init__(self, config: Dict[str, Any]) -> None:
           """Initialize VM service with configuration.
           
           Args:
               config: Service configuration dictionary
           """
           self.config = config
           self._vms: Dict[str, VM] = {}
       
       async def create_vm(
           self, 
           vm_config: Dict[str, Any]
       ) -> Dict[str, str]:
           """Create a new virtual machine.
           
           Args:
               vm_config: VM configuration parameters
               
           Returns:
               Dictionary containing VM ID and status
               
           Raises:
               VMCreationError: If VM creation fails
           """
           try:
               # Implementation here
               logger.info(f"Creating VM with config: {vm_config}")
               return {"vm_id": "vm-123", "status": "created"}
           except Exception as e:
               logger.error(f"Failed to create VM: {e}")
               raise VMCreationError(f"VM creation failed: {e}")

**Documentation Style:**

.. code-block:: python

   def process_file(
       file_path: str, 
       analysis_type: str = "static",
       timeout: int = 300
   ) -> AnalysisResult:
       """Process a file for malware analysis.
       
       Performs static or dynamic analysis on the provided file using
       the specified analysis type and timeout configuration.
       
       Args:
           file_path: Path to the file to analyze
           analysis_type: Type of analysis ("static" or "dynamic")
           timeout: Analysis timeout in seconds
           
       Returns:
           AnalysisResult object containing analysis findings
           
       Raises:
           FileNotFoundError: If the specified file doesn't exist
           AnalysisTimeoutError: If analysis exceeds timeout
           AnalysisError: If analysis fails for other reasons
           
       Example:
           >>> result = process_file("/tmp/malware.exe", "dynamic", 600)
           >>> print(result.threat_level)
           "HIGH"
       """

Testing Requirements
--------------------

Test Coverage Standards
~~~~~~~~~~~~~~~~~~~~~~~

**Minimum Requirements:**

- **80% overall coverage** - Enforced by CI/CD
- **90% coverage for critical paths** - VM management, file processing
- **100% coverage for security functions** - Authentication, authorization
- **All public APIs tested** - REST endpoints, WebSocket handlers

**Test Types Required:**

.. code-block:: bash

   # Unit tests (required for all new code)
   python -m pytest tests/unit/ -v
   
   # Integration tests (required for API changes)
   python -m pytest tests/integration/ -v
   
   # Property tests (recommended for complex logic)
   python -m pytest tests/property/ -v
   
   # Performance tests (required for performance-critical code)
   python -m pytest tests/performance/ --benchmark-only

**Writing Tests:**

.. code-block:: python

   import pytest
   from unittest.mock import Mock, patch
   from api.services.vm_service import VMService
   
   
   class TestVMService:
       """Test suite for VM service functionality."""
       
       @pytest.fixture
       def vm_service(self):
           """Create VM service instance for testing."""
           config = {"docker_url": "unix://var/run/docker.sock"}
           return VMService(config)
       
       @pytest.fixture
       def sample_vm_config(self):
           """Sample VM configuration for testing."""
           return {
               "name": "test-vm",
               "template": "ubuntu:20.04",
               "memory_mb": 1024,
               "cpus": 2
           }
       
       def test_create_vm_success(self, vm_service, sample_vm_config):
           """Test successful VM creation."""
           with patch('docker.from_env') as mock_docker:
               mock_container = Mock()
               mock_container.id = "test-container-id"
               mock_docker.return_value.containers.run.return_value = mock_container
               
               result = vm_service.create_vm(sample_vm_config)
               
               assert result["status"] == "created"
               assert result["vm_id"] == "test-container-id"
       
       def test_create_vm_invalid_config(self, vm_service):
           """Test VM creation with invalid configuration."""
           invalid_config = {"name": ""}  # Invalid empty name
           
           with pytest.raises(ValidationError):
               vm_service.create_vm(invalid_config)
       
       @pytest.mark.asyncio
       async def test_vm_metrics_collection(self, vm_service):
           """Test VM metrics collection."""
           vm_id = "test-vm-id"
           
           with patch.object(vm_service, '_collect_metrics') as mock_collect:
               mock_collect.return_value = {
                   "cpu_percent": 25.5,
                   "memory_percent": 60.2
               }
               
               metrics = await vm_service.get_metrics(vm_id)
               
               assert metrics["cpu_percent"] == 25.5
               assert metrics["memory_percent"] == 60.2

Documentation Requirements
--------------------------

Documentation Standards
~~~~~~~~~~~~~~~~~~~~~~~

**Required Documentation:**

1. **API Documentation** - All endpoints documented with examples
2. **Code Documentation** - Docstrings for all public functions
3. **Architecture Documentation** - System design and component interaction
4. **User Documentation** - Installation, configuration, usage guides
5. **Developer Documentation** - Setup, testing, contributing guides

**Documentation Format:**

.. code-block:: rst

   💩🎉TurdParty🎉💩 Feature Name
   ==============================
   
   Overview
   --------
   
   Brief description of the feature and its purpose.
   
   Quick Start
   -----------
   
   .. code-block:: bash
   
      # Example usage
      tp-command --option value
   
   Detailed Usage
   --------------
   
   Comprehensive explanation with examples.
   
   API Reference
   -------------
   
   .. list-table::
      :header-rows: 1
      :widths: 25 25 50
   
      * - Endpoint
        - Method
        - Description
      * - ``/api/v1/feature``
        - POST
        - Create new feature

**Code Documentation:**

.. code-block:: python

   class FeatureService:
       """Service for managing feature functionality.
       
       This service provides comprehensive feature management including
       creation, configuration, monitoring, and cleanup operations.
       
       Attributes:
           config: Service configuration dictionary
           active_features: Dictionary of currently active features
           
       Example:
           >>> service = FeatureService(config)
           >>> feature = service.create_feature({"name": "test"})
           >>> print(feature.status)
           "active"
       """

Pull Request Process
--------------------

Creating Pull Requests
~~~~~~~~~~~~~~~~~~~~~~

**Before Creating PR:**

.. code-block:: bash

   # 1. Ensure all tests pass
   tp-test
   
   # 2. Run quality checks
   pre-commit run --all-files
   
   # 3. Update documentation if needed
   # 4. Add/update tests for new functionality
   # 5. Ensure commit messages follow conventions

**PR Template:**

.. code-block:: markdown

   ## Description
   
   Brief description of changes and motivation.
   
   ## Type of Change
   
   - [ ] Bug fix (non-breaking change which fixes an issue)
   - [ ] New feature (non-breaking change which adds functionality)
   - [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
   - [ ] Documentation update
   
   ## Testing
   
   - [ ] Unit tests pass
   - [ ] Integration tests pass
   - [ ] Manual testing completed
   - [ ] Performance impact assessed
   
   ## Checklist
   
   - [ ] Code follows style guidelines
   - [ ] Self-review completed
   - [ ] Documentation updated
   - [ ] Tests added/updated
   - [ ] No breaking changes (or documented)

**Review Process:**

1. **Automated Checks** - CI/CD runs tests and quality checks
2. **Code Review** - At least one reviewer required
3. **Testing** - Reviewer tests functionality
4. **Documentation Review** - Ensure docs are updated
5. **Approval** - Reviewer approves changes
6. **Merge** - Maintainer merges to target branch

Issue Reporting
---------------

Bug Reports
~~~~~~~~~~~

**Bug Report Template:**

.. code-block:: markdown

   ## Bug Description
   
   Clear description of the bug and expected behavior.
   
   ## Steps to Reproduce
   
   1. Step one
   2. Step two
   3. Step three
   
   ## Environment
   
   - OS: [e.g., Ubuntu 22.04]
   - Python Version: [e.g., 3.12.8]
   - TurdParty Version: [e.g., commit hash]
   - Docker Version: [e.g., 24.0.0]
   
   ## Logs
   
   ```
   Relevant log output
   ```
   
   ## Additional Context
   
   Any additional information that might help.

Feature Requests
~~~~~~~~~~~~~~~~

**Feature Request Template:**

.. code-block:: markdown

   ## Feature Description
   
   Clear description of the proposed feature.
   
   ## Use Case
   
   Explain the problem this feature would solve.
   
   ## Proposed Solution
   
   Describe your proposed implementation approach.
   
   ## Alternatives Considered
   
   Other approaches you've considered.
   
   ## Additional Context
   
   Any additional information or mockups.

Development Environment
-----------------------

IDE Configuration
~~~~~~~~~~~~~~~~

**VS Code Settings:**

.. code-block:: json

   {
       "python.defaultInterpreterPath": "./venv/bin/python",
       "python.linting.enabled": true,
       "python.linting.ruffEnabled": true,
       "python.formatting.provider": "black",
       "editor.formatOnSave": true,
       "editor.codeActionsOnSave": {
           "source.organizeImports": true
       },
       "files.exclude": {
           "**/__pycache__": true,
           "**/.pytest_cache": true,
           "**/htmlcov": true
       }
   }

**Debugging Configuration:**

.. code-block:: json

   {
       "version": "0.2.0",
       "configurations": [
           {
               "name": "TurdParty API",
               "type": "python",
               "request": "launch",
               "program": "api/main.py",
               "env": {
                   "DEVELOPMENT": "true",
                   "DEBUG": "true"
               },
               "console": "integratedTerminal"
           },
           {
               "name": "Run Tests",
               "type": "python",
               "request": "launch",
               "module": "pytest",
               "args": ["tests/", "-v"],
               "console": "integratedTerminal"
           }
       ]
   }

Useful Commands
~~~~~~~~~~~~~~

**Development Commands:**

.. code-block:: bash

   # Service management
   tp-status                    # Check service status
   tp-start                     # Start all services
   tp-monitor                   # Real-time monitoring
   tp-logs                      # View service logs
   
   # Testing
   tp-test                      # Full test suite
   python -m pytest tests/unit/ -v  # Unit tests only
   python -m pytest --cov=. --cov-report=html  # Coverage report
   
   # Code quality
   ruff check .                 # Linting
   ruff format .                # Formatting
   mypy .                       # Type checking
   pre-commit run --all-files   # All quality checks
   
   # Git workflow
   gs                          # git status
   ga .                        # git add .
   gc -m "message"             # git commit
   gp                          # git push

**Docker Commands:**

.. code-block:: bash

   # Container management
   dps                         # docker ps
   di                          # docker images
   dcu                         # docker-compose up -d
   dcd                         # docker-compose down
   dcl                         # docker-compose logs -f
   
   # Cleanup
   docker system prune -a      # Clean up unused containers/images

Getting Help
------------

**Resources:**

- **Documentation** - :doc:`../index`
- **API Reference** - :doc:`../api/overview`
- **Architecture** - :doc:`../platform/architecture`
- **Testing Guide** - :doc:`testing`

**Communication:**

- **GitHub Issues** - Bug reports and feature requests
- **GitHub Discussions** - General questions and discussions
- **Code Reviews** - Pull request feedback and collaboration

**Best Practices:**

1. **Read existing code** - Understand patterns and conventions
2. **Start small** - Begin with minor improvements or bug fixes
3. **Ask questions** - Use GitHub discussions for clarification
4. **Follow conventions** - Maintain consistency with existing code
5. **Test thoroughly** - Ensure changes don't break existing functionality
6. **Document changes** - Update relevant documentation

Welcome to the TurdParty development community! We appreciate your contributions to making malware analysis more effective and accessible.

For additional development resources, see:

- :doc:`setup` - Complete development environment setup
- :doc:`nix-environment` - Nix shell configuration and usage
- :doc:`testing` - Comprehensive testing framework guide
- :doc:`architecture` - Development architecture overview
