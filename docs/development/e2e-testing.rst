💩🎉TurdParty🎉💩 End-to-End Testing with Playwright
======================================================

Overview
--------

TurdParty includes a comprehensive End-to-End (E2E) testing infrastructure using Microsoft Playwright, providing automated browser-based testing of the complete user workflow. The E2E testing system is fully containerized and integrated with the TurdParty service ecosystem.

.. note::
   E2E tests validate the complete user journey from file upload through VM processing to ELK analysis, ensuring all components work together seamlessly.

Architecture
------------

Playwright Docker Container
~~~~~~~~~~~~~~~~~~~~~~~~~~~

The E2E testing infrastructure runs in a dedicated Docker container with:

- **Microsoft Playwright Python Image**: Official image with pre-installed browsers
- **Traefik Integration**: Accessible via ``playwright.turdparty.localhost``
- **Health Monitoring**: Built-in health server with dashboard
- **Test Report Serving**: Web-based test results and reports
- **Service Discovery**: Integrated with central service management

.. mermaid::

   graph TB
       subgraph "E2E Testing Infrastructure"
           PW[Playwright Container]
           HEALTH[Health Server :8080]
           DASH[Web Dashboard]
           REPORTS[Test Reports]
       end

       subgraph "TurdParty Services"
           API[API Service]
           FRONTEND[React Frontend]
           DB[Database]
           CACHE[Redis]
       end

       subgraph "Browser Automation"
           CHROME[Chromium Browser]
           FIREFOX[Firefox Browser]
           WEBKIT[WebKit Browser]
       end

       PW --> HEALTH
       PW --> DASH
       PW --> REPORTS
       PW --> CHROME
       PW --> FIREFOX
       PW --> WEBKIT

       PW --> API
       PW --> FRONTEND
       FRONTEND --> API
       API --> DB
       API --> CACHE

Test Categories
~~~~~~~~~~~~~~~

.. list-table::
   :header-rows: 1
   :widths: 30 20 50

   * - Test Category
     - Test Count
     - Description
   * - **Complete Workflow**
     - 8 tests
     - End-to-end user journey testing
   * - **File Upload**
     - 2 tests
     - File upload and validation workflows
   * - **VM Management**
     - 2 tests
     - Virtual machine lifecycle testing
   * - **UI Interaction**
     - 2 tests
     - User interface responsiveness and accessibility
   * - **Error Handling**
     - 1 test
     - Error scenarios and edge cases
   * - **Performance**
     - 1 test
     - Load time and performance validation

Quick Start
-----------

Building and Running
~~~~~~~~~~~~~~~~~~~

**Build Playwright Container:**

.. code-block:: bash

   # Build and start Playwright service
   ./scripts/build-playwright.sh

   # Or manually with Docker Compose
   docker-compose -f compose/docker-compose.yml --profile testing up -d playwright

**Access E2E Testing Dashboard:**

.. code-block:: bash

   # Open web dashboard
   open http://playwright.turdparty.localhost

   # Check service health
   curl http://playwright.turdparty.localhost/health

**Run E2E Tests:**

.. code-block:: bash

   # Run all E2E tests in container
   docker exec turdpartycollab_playwright python -m pytest tests/e2e/ -v

   # Run specific test category
   docker exec turdpartycollab_playwright python -m pytest tests/e2e/test_complete_workflow.py -v

   # Generate HTML test report
   docker exec turdpartycollab_playwright python -m pytest tests/e2e/ --html=reports/e2e-report.html

Test Configuration
------------------

Environment Variables
~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Container Environment
   IN_DOCKER=true
   PYTHONPATH=/app
   
   # Service URLs (automatically configured)
   APP_URL=http://frontend.turdparty.localhost
   API_URL=http://api.turdparty.localhost
   
   # Playwright Configuration
   PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
   PORT=8080
   HOST=0.0.0.0

Docker Compose Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: yaml

   playwright:
     container_name: turdpartycollab_playwright
     build:
       context: ..
       dockerfile: docker/playwright/Dockerfile
     ports:
       - "8080:8080"
     environment:
       - IN_DOCKER=true
       - APP_URL=http://frontend.turdparty.localhost
       - API_URL=http://api.turdparty.localhost
     volumes:
       - ../test-results:/app/test-results
       - ../reports:/app/reports
     networks:
       - turdpartycollab_network
       - traefik_network
     depends_on:
       api:
         condition: service_healthy
       frontend:
         condition: service_healthy
     profiles:
       - testing
       - e2e
     labels:
       - "traefik.enable=true"
       - "traefik.http.routers.turdparty-playwright.rule=Host(`playwright.turdparty.localhost`)"

Test Implementation
-------------------

Complete Workflow Tests
~~~~~~~~~~~~~~~~~~~~~~

**File Upload Workflow:**

.. code-block:: python

   async def test_file_upload_workflow(self, page: Page, app_url: str, test_file: Path):
       """Test the complete file upload workflow through the UI."""
       # Navigate to the application
       await page.goto(app_url)

       # Wait for the page to load
       await page.wait_for_selector('[data-testid="file-upload-form"]', timeout=10000)

       # Upload a file
       file_input = page.locator('input[type="file"]')
       await file_input.set_input_files(str(test_file))

       # Set target path and permissions
       target_path_input = page.locator('[data-testid="target-path-input"]')
       await target_path_input.fill("/app/scripts/e2e_test_script.sh")

       permissions_input = page.locator('[data-testid="permissions-input"]')
       await permissions_input.fill("0755")

       # Submit the form
       submit_button = page.locator('[data-testid="upload-submit-button"]')
       await submit_button.click()

       # Verify success
       success_message = page.locator('[data-testid="upload-success-message"]')
       await success_message.wait_for(timeout=30000)

**VM Management Testing:**

.. code-block:: python

   async def test_injection_status_monitoring(self, page: Page, app_url: str):
       """Test monitoring injection status through the UI."""
       # Navigate to injections list
       injections_link = page.locator('[data-testid="injections-nav-link"]')
       await injections_link.click()

       # Wait for injections list to load
       await page.wait_for_selector('[data-testid="injections-list"]', timeout=10000)

       # Find and click on injection
       first_injection = page.locator('[data-testid="injection-item"]').first
       await first_injection.click()

       # Verify injection details
       status_element = page.locator('[data-testid="injection-status"]')
       status_text = await status_element.text_content()
       assert status_text in ["pending", "in_progress", "completed", "failed"]

Accessibility Testing
~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def test_accessibility_compliance(self, page: Page, app_url: str):
       """Test basic accessibility compliance."""
       await page.goto(app_url)
       await page.wait_for_load_state("networkidle")

       # Check for proper heading structure
       h1_elements = page.locator("h1")
       h1_count = await h1_elements.count()
       assert h1_count >= 1

       # Check for alt text on images
       images = page.locator("img")
       image_count = await images.count()

       for i in range(image_count):
           img = images.nth(i)
           alt_text = await img.get_attribute("alt")
           role = await img.get_attribute("role")
           assert alt_text is not None or role == "presentation"

Performance Testing
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def test_performance_metrics(self, page: Page, app_url: str):
       """Test basic performance metrics."""
       start_time = asyncio.get_event_loop().time()
       await page.goto(app_url)
       await page.wait_for_load_state("networkidle")
       end_time = asyncio.get_event_loop().time()

       load_time = end_time - start_time
       assert load_time < 10.0, f"Page load time {load_time:.2f}s exceeds threshold"

       # Check for console errors
       console_errors = []
       page.on("console", lambda msg: console_errors.append(msg.text) if msg.type == "error" else None)

       await page.reload()
       await page.wait_for_load_state("networkidle")
       assert len(console_errors) == 0, f"Console errors found: {console_errors}"

Test Fixtures and Utilities
---------------------------

Browser Context Management
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @pytest.fixture
   async def browser_context(self) -> BrowserContext:
       """Create a browser context for testing."""
       async with async_playwright() as p:
           browser = await p.chromium.launch(headless=True)
           context = await browser.new_context()
           yield context
           await context.close()
           await browser.close()

   @pytest.fixture
   async def page(self, browser_context: BrowserContext) -> Page:
       """Create a page for testing."""
       page = await browser_context.new_page()
       yield page
       await page.close()

Test Data Management
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @pytest.fixture
   def test_file(self) -> Path:
       """Create a test file for upload."""
       content = '''#!/bin/bash
   echo "E2E test script"
   echo "Timestamp: $(date)"
   echo "Hostname: $(hostname)"
   exit 0
   '''
       temp_file = Path(tempfile.gettempdir()) / "e2e_test_script.sh"
       temp_file.write_text(content)
       yield temp_file
       temp_file.unlink(missing_ok=True)

   @pytest.fixture
   def app_url(self) -> str:
       """Get the application URL for testing."""
       return os.getenv("APP_URL", "http://frontend.turdparty.localhost")

Service Integration
------------------

Health Monitoring
~~~~~~~~~~~~~~~~

The Playwright container includes a built-in health server that provides:

**Health Endpoint:**

.. code-block:: json

   {
     "status": "healthy",
     "service": "turdparty-playwright",
     "timestamp": "2024-01-15T10:30:00Z",
     "version": "1.0.0",
     "playwright_available": true,
     "environment": "true",
     "reports_available": true,
     "test_results_available": true
   }

**Web Dashboard Features:**

- Service status monitoring
- Test report browsing
- Test result viewing
- Direct links to TurdParty services

Traefik Integration
~~~~~~~~~~~~~~~~~~

The Playwright service is fully integrated with Traefik:

.. code-block:: yaml

   labels:
     - "traefik.enable=true"
     - "traefik.http.routers.turdparty-playwright.rule=Host(`playwright.turdparty.localhost`)"
     - "traefik.http.routers.turdparty-playwright.entrypoints=web"
     - "traefik.http.services.turdparty-playwright.loadbalancer.server.port=8080"

**Service URLs:**

- **Dashboard**: ``http://playwright.turdparty.localhost``
- **Health Check**: ``http://playwright.turdparty.localhost/health``
- **Test Reports**: ``http://playwright.turdparty.localhost/reports/``
- **Test Results**: ``http://playwright.turdparty.localhost/test-results/``

Central Service Management
~~~~~~~~~~~~~~~~~~~~~~~~~

The Playwright service is registered in ``config/service-urls.json``:

.. code-block:: json

   {
     "environments": {
       "development": {
         "services": {
           "playwright": {
             "subdomain": "playwright",
             "port": null,
             "path": "",
             "health_endpoint": "/health"
           }
         }
       }
     }
   }

Best Practices
--------------

Test Organization
~~~~~~~~~~~~~~~~

**File Structure:**

.. code-block:: text

   tests/e2e/
   ├── test_complete_workflow.py      # Main E2E workflow tests
   ├── test_api_workflow_e2e.py       # API-based E2E tests
   ├── test_documentation_e2e.py      # Documentation testing
   ├── playwright/                    # Playwright-specific configs
   │   ├── frontend-test.spec.js      # JavaScript E2E tests
   │   └── playwright.config.js       # Playwright configuration
   └── fixtures/                      # Test data and fixtures

**Test Naming Convention:**

- ``test_*_workflow`` - Complete user workflow tests
- ``test_*_integration`` - Service integration tests
- ``test_*_ui`` - User interface specific tests
- ``test_*_performance`` - Performance and load tests

Error Handling
~~~~~~~~~~~~~

.. code-block:: python

   async def test_error_handling_invalid_file(self, page: Page, app_url: str):
       """Test error handling for invalid file uploads."""
       await page.goto(app_url)

       # Create invalid file
       invalid_file = Path(tempfile.gettempdir()) / "invalid_file.exe"
       invalid_file.write_bytes(b"MZ" + b"x" * 10000000)  # Large fake executable

       try:
           # Attempt upload
           file_input = page.locator('input[type="file"]')
           await file_input.set_input_files(str(invalid_file))

           submit_button = page.locator('[data-testid="upload-submit-button"]')
           await submit_button.click()

           # Verify error handling
           error_message = page.locator('[data-testid="upload-error-message"]')
           await error_message.wait_for(timeout=10000)

           error_text = await error_message.text_content()
           assert "error" in error_text.lower() or "invalid" in error_text.lower()

       finally:
           invalid_file.unlink(missing_ok=True)

Responsive Design Testing
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def test_responsive_design(self, browser_context: BrowserContext, app_url: str):
       """Test responsive design on different screen sizes."""
       # Test mobile viewport
       mobile_page = await browser_context.new_page()
       await mobile_page.set_viewport_size({"width": 375, "height": 667})
       await mobile_page.goto(app_url)

       # Verify mobile navigation
       mobile_menu = mobile_page.locator('[data-testid="mobile-menu-button"]')
       if await mobile_menu.is_visible():
           await mobile_menu.click()
           nav_items = mobile_page.locator('[data-testid="mobile-nav-item"]')
           nav_count = await nav_items.count()
           assert nav_count > 0

       await mobile_page.close()

Troubleshooting
--------------

Common Issues
~~~~~~~~~~~~

**Browser Dependencies Missing:**

.. code-block:: bash

   # Rebuild container with dependencies
   docker-compose -f compose/docker-compose.yml build playwright --no-cache

**Service Not Accessible:**

.. code-block:: bash

   # Check Traefik routing
   curl -H "Host: playwright.turdparty.localhost" http://localhost:8080/health

   # Check container logs
   docker logs turdpartycollab_playwright

**Tests Failing Due to Timing:**

.. code-block:: python

   # Increase timeout values
   await page.wait_for_selector('[data-testid="element"]', timeout=30000)

   # Use wait_for_load_state
   await page.wait_for_load_state("networkidle")

**Container Build Issues:**

.. code-block:: bash

   # Clean Docker cache
   docker system prune -f

   # Rebuild from scratch
   ./scripts/build-playwright.sh

Debugging Tests
~~~~~~~~~~~~~~

**Enable Debug Mode:**

.. code-block:: bash

   # Run with debug output
   docker exec turdpartycollab_playwright python -m pytest tests/e2e/ -v -s --tb=long

**Screenshot on Failure:**

.. code-block:: python

   @pytest.fixture(autouse=True)
   async def screenshot_on_failure(self, page: Page, request):
       """Take screenshot on test failure."""
       yield
       if request.node.rep_call.failed:
           screenshot_path = f"test-results/failure-{request.node.name}.png"
           await page.screenshot(path=screenshot_path)

**Video Recording:**

.. code-block:: python

   async def browser_context(self) -> BrowserContext:
       """Create browser context with video recording."""
       async with async_playwright() as p:
           browser = await p.chromium.launch(headless=True)
           context = await browser.new_context(
               record_video_dir="test-results/videos/"
           )
           yield context
           await context.close()
           await browser.close()

Integration with CI/CD
---------------------

GitHub Actions
~~~~~~~~~~~~~

.. code-block:: yaml

   name: E2E Tests
   on: [push, pull_request]

   jobs:
     e2e-tests:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3

         - name: Start TurdParty Services
           run: |
             docker-compose -f compose/docker-compose.yml up -d

         - name: Run E2E Tests
           run: |
             ./scripts/build-playwright.sh
             docker exec turdpartycollab_playwright python -m pytest tests/e2e/ --html=reports/e2e-report.html

         - name: Upload Test Results
           uses: actions/upload-artifact@v3
           if: always()
           with:
             name: e2e-test-results
             path: |
               test-results/
               reports/

Performance Monitoring
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Monitor test execution time
   docker exec turdpartycollab_playwright python -m pytest tests/e2e/ --durations=10

   # Generate performance report
   docker exec turdpartycollab_playwright python -m pytest tests/e2e/ --benchmark-only --benchmark-json=reports/benchmark.json

Future Enhancements
------------------

Planned Features
~~~~~~~~~~~~~~~

- **Visual Regression Testing**: Screenshot comparison for UI changes
- **Cross-Browser Testing**: Automated testing across Chrome, Firefox, and Safari
- **Mobile Testing**: Comprehensive mobile device simulation
- **API Integration**: Enhanced API-based E2E testing
- **Load Testing**: User journey load testing with multiple concurrent users
- **Accessibility Auditing**: Automated WCAG compliance checking

Contributing
-----------

Adding New E2E Tests
~~~~~~~~~~~~~~~~~~~

1. **Create Test File**: Add new test file in ``tests/e2e/``
2. **Follow Naming Convention**: Use descriptive test names
3. **Add Test Fixtures**: Create reusable fixtures for test data
4. **Update Documentation**: Document new test scenarios
5. **Test Locally**: Verify tests pass in container environment

**Example New Test:**

.. code-block:: python

   class TestNewFeature:
       """E2E tests for new feature."""

       async def test_new_feature_workflow(self, page: Page, app_url: str):
           """Test new feature complete workflow."""
           # Implementation here
           pass
