💩🎉TurdParty🎉💩 Nix Development Environment
==============================================

Overview
--------

TurdParty uses <PERSON> for reproducible development environments, ensuring all developers have identical toolchains and dependencies. The Nix configuration provides a comprehensive development shell with enhanced zsh, git integration, and all necessary tools for TurdParty development.

Quick Start
-----------

**Enter Development Environment:**

.. code-block:: bash

   # Clone the repository
   <NAME_EMAIL>:tenbahtsecurity/turdparty-collab.git
   cd turdparty-collab
   
   # Enter Nix development environment (auto-starts enhanced zsh)
   nix-shell
   
   # You'll see the enhanced prompt with git branch:
   💩🎉 user@host:~/path/turdparty-collab (develop/local) $

**Immediate Benefits:**

- ✅ **Auto-start enhanced zsh** with git branch display
- ✅ **All development tools** pre-installed and configured
- ✅ **TurdParty-specific aliases** ready to use
- ✅ **Consistent environment** across all development machines

Nix Shell Features
------------------

Enhanced Shell Environment
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Custom Zsh Configuration:**

.. code-block:: bash

   # Git branch always visible in prompt
   💩🎉 cvr@ganymede:~/turdparty-collab (develop/local) $
   
   # Auto-completion and history
   # - 10,000 command history
   # - Shared history across sessions
   # - Intelligent duplicate removal

**TurdParty Development Aliases:**

.. code-block:: bash

   # TurdParty Commands
   tp-status          # ./scripts/turdparty status
   tp-monitor         # ./scripts/turdparty monitor  
   tp-start           # ./scripts/turdparty start
   tp-test            # ./scripts/run-parallel-tests.sh
   tp-logs            # docker-compose logs -f
   
   # Docker Shortcuts
   dc                 # docker-compose
   dcu                # docker-compose up -d
   dcd                # docker-compose down
   dcl                # docker-compose logs -f
   dps                # docker ps
   di                 # docker images
   
   # Git Shortcuts
   gs                 # git status
   ga                 # git add
   gc                 # git commit
   gp                 # git push
   gl                 # git log --oneline
   gb                 # git branch
   gco                # git checkout
   
   # Modern Tools
   ll                 # eza -la --git (enhanced ls)
   la                 # eza -la
   ls                 # eza
   cat                # bat (syntax highlighting)
   grep               # rg (ripgrep)
   find               # fd (fast find)

Development Tools Included
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Core Development Stack:**

.. list-table::
   :header-rows: 1
   :widths: 25 25 50

   * - Category
     - Tools
     - Purpose
   * - **Shell & Terminal**
     - zsh, starship
     - Enhanced shell with git integration
   * - **Version Control**
     - git, git-lfs, gh
     - Complete git workflow with GitHub CLI
   * - **Python Development**
     - python312, pip, poetry
     - Python 3.12 with package management
   * - **Node.js Development**
     - nodejs_20, yarn
     - Frontend development tools
   * - **Databases**
     - postgresql_15, redis
     - Database development and testing
   * - **Containers**
     - docker, docker-compose
     - Container orchestration
   * - **Code Quality**
     - ruff, mypy, bandit, pre-commit
     - Linting, type checking, security

**Testing & Quality Tools:**

.. code-block:: bash

   # Testing Framework
   pytest              # Unit testing
   pytest-asyncio      # Async testing
   pytest-cov         # Coverage reporting
   pytest-benchmark   # Performance testing
   hypothesis          # Property-based testing
   
   # Code Quality
   ruff check .        # Fast Python linting
   ruff format .       # Code formatting
   mypy .              # Type checking
   bandit -r .         # Security scanning
   pre-commit run --all-files  # Pre-commit hooks

**Performance & Monitoring:**

.. code-block:: bash

   # System Monitoring
   htop               # Process monitoring
   iotop              # I/O monitoring
   nethogs            # Network monitoring
   
   # File Operations
   jq                 # JSON processing
   yq-go              # YAML processing
   ripgrep            # Fast text search
   fd                 # Fast file finding
   bat                # Enhanced cat with syntax highlighting
   eza                # Modern ls replacement

Configuration Details
---------------------

Shell Hook Configuration
~~~~~~~~~~~~~~~~~~~~~~~~

The Nix shell automatically configures the development environment:

.. code-block:: bash

   # Environment Variables
   DEVELOPMENT=true
   DEBUG=true
   LOG_LEVEL=DEBUG
   TURDPARTY_ENV=development
   COMPOSE_PROJECT_NAME=turdpartycollab
   DOCKER_BUILDKIT=1
   COVERAGE_THRESHOLD=80
   PARALLEL_WORKERS=4
   
   # Python Configuration
   PYTHONPATH="$PWD:$PYTHONPATH"
   PYTHONDONTWRITEBYTECODE=1
   PYTHONUNBUFFERED=1
   
   # Development Tools
   EDITOR=vim
   PAGER=bat
   GIT_EDITOR=vim

Zsh Configuration
~~~~~~~~~~~~~~~~~

**Custom Prompt with Git Integration:**

.. code-block:: bash

   # Enable git status in prompt
   autoload -Uz vcs_info
   precmd() { vcs_info }
   zstyle ':vcs_info:git:*' formats ' (%b)'
   setopt PROMPT_SUBST
   
   # Custom prompt format
   PROMPT='💩🎉 %F{cyan}%n@%m%f:%F{yellow}%~%f%F{green}${vcs_info_msg_0_}%f %F{magenta}$%f '

**History Configuration:**

.. code-block:: bash

   HISTFILE="$ZDOTDIR/.zsh_history"
   HISTSIZE=10000
   SAVEHIST=10000
   setopt SHARE_HISTORY
   setopt HIST_IGNORE_DUPS
   setopt HIST_IGNORE_ALL_DUPS

**Useful Zsh Features:**

.. code-block:: bash

   setopt AUTO_CD          # cd by typing directory name
   setopt CORRECT          # Command correction suggestions
   setopt EXTENDED_GLOB    # Extended globbing patterns

Git Configuration
-----------------

Branch Routing Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The development environment is configured with proper git branch routing:

.. code-block:: bash

   # Branch Configuration
   develop/local → forkrul account (development)
   master → tenbahtsecurity account (production)
   other branches → tenbahtsecurity account (production)

**Pre-Push Hook Behavior:**

.. list-table::
   :header-rows: 1
   :widths: 30 30 40

   * - Repository
     - Time Restrictions
     - Behavior
   * - **forkrul** (develop/local)
     - ❌ None
     - Always allowed
   * - **tenbahtsecurity** (master, others)
     - ✅ 7am-18:00 CET
     - Prompts during business hours
   * - **Weekend pushes**
     - ❌ None
     - Always allowed

**Git Configuration Commands:**

.. code-block:: bash

   # Check branch configuration
   git branch -vv
   git config --get-regexp "branch\.(master|develop/local)\.(remote|merge)"
   
   # Verify push destinations
   git remote -v

Directory Structure
-------------------

**Auto-Created Directories:**

.. code-block:: bash

   logs/                    # Application logs
   data/uploads/            # File uploads
   data/temp_files/         # Temporary files
   test-results/            # Test output
   coverage-archive/        # Coverage reports
   reports/                 # Analysis reports

**Nix-Specific Directories:**

.. code-block:: bash

   .nix-zsh/               # Custom zsh configuration
   ├── .zshrc              # Zsh configuration (tracked)
   ├── .zsh_history        # Command history (ignored)
   ├── .zcompdump*         # Completion cache (ignored)
   └── .zsh_sessions/      # Session data (ignored)

Development Workflow
--------------------

Daily Development Routine
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # 1. Enter development environment
   nix-shell
   💩🎉 user@host:~/turdparty-collab (develop/local) $
   
   # 2. Check service status
   tp-status
   
   # 3. Start services if needed
   tp-start --logs
   
   # 4. Run tests
   tp-test
   
   # 5. Development work...
   
   # 6. Check code quality
   ruff check . && ruff format .
   pre-commit run --all-files
   
   # 7. Commit and push (to forkrul, no time restrictions)
   ga . && gc -m "feat: new feature" && gp

Testing Commands
~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Unit tests
   python -m pytest tests/unit/ -v
   
   # Property-based tests  
   python -m pytest tests/property/ -v
   
   # Performance benchmarks
   python -m pytest tests/performance/ --benchmark-only
   
   # Full parallel test suite
   tp-test
   
   # Coverage reporting
   python -m pytest --cov=. --cov-report=html

Service Management
~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Service status dashboard
   tp-status
   
   # Real-time monitoring
   tp-monitor
   
   # Start all services
   tp-start
   
   # View logs
   tp-logs
   
   # Docker operations
   dcu                     # Start containers
   dcd                     # Stop containers
   dps                     # List containers
   dcl                     # View logs

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Nix Shell Not Starting:**

.. code-block:: bash

   # Ensure Nix is installed
   nix --version
   
   # Clear Nix cache if needed
   nix-collect-garbage
   
   # Rebuild shell environment
   nix-shell --pure

**Zsh Not Auto-Starting:**

.. code-block:: bash

   # Check if in interactive terminal
   echo $- | grep i
   
   # Manually start zsh if needed
   zsh
   
   # Check zsh configuration
   cat .nix-zsh/.zshrc

**Git Branch Not Showing:**

.. code-block:: bash

   # Ensure in git repository
   git status
   
   # Check vcs_info configuration
   zsh -c 'autoload -Uz vcs_info; vcs_info; echo $vcs_info_msg_0_'
   
   # Reload zsh configuration
   source .nix-zsh/.zshrc

**Permission Issues:**

.. code-block:: bash

   # Fix directory permissions
   chmod -R 755 .nix-zsh/
   
   # Recreate zsh configuration
   rm -rf .nix-zsh/
   nix-shell

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~

**Faster Shell Startup:**

.. code-block:: bash

   # Disable unnecessary plugins
   # Edit .nix-zsh/.zshrc to remove unused features
   
   # Use minimal prompt for scripts
   export PROMPT='$ '

**Reduced Memory Usage:**

.. code-block:: bash

   # Limit history size
   export HISTSIZE=1000
   export SAVEHIST=1000
   
   # Disable completion caching
   unsetopt COMPLETE_ALIASES

Advanced Configuration
----------------------

Custom Shell Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~

You can extend the zsh configuration by editing `.nix-zsh/.zshrc`:

.. code-block:: bash

   # Add custom aliases
   alias myalias='my command'
   
   # Add custom functions
   function myfunction() {
       echo "Custom function"
   }
   
   # Add custom environment variables
   export MY_VAR="value"

Nix Shell Customization
~~~~~~~~~~~~~~~~~~~~~~~

To modify the Nix environment, edit `shell.nix`:

.. code-block:: nix

   # Add new packages
   buildInputs = with pkgs; [
       # existing packages...
       newpackage
   ];
   
   # Add custom shell hook commands
   shellHook = ''
       # existing commands...
       echo "Custom setup"
   '';

Integration with IDEs
~~~~~~~~~~~~~~~~~~~~

**VS Code Integration:**

.. code-block:: json

   {
       "terminal.integrated.shell.linux": "/nix/store/.../bin/zsh",
       "terminal.integrated.shellArgs.linux": ["-l"],
       "python.defaultInterpreterPath": "./venv/bin/python"
   }

**IntelliJ/PyCharm Integration:**

1. Set terminal shell to zsh from nix-shell
2. Configure Python interpreter from nix environment
3. Set project SDK to nix Python installation

Best Practices
--------------

Development Guidelines
~~~~~~~~~~~~~~~~~~~~~

1. **Always use nix-shell** for development work
2. **Commit frequently** to develop/local branch
3. **Run tests before pushing** using tp-test
4. **Use pre-commit hooks** for code quality
5. **Monitor services** with tp-status and tp-monitor

Git Workflow
~~~~~~~~~~~~

1. **develop/local** for all development work (pushes to forkrul)
2. **master** for production releases (pushes to tenbahtsecurity)
3. **Feature branches** for specific features (pushes to tenbahtsecurity)
4. **Use descriptive commit messages** following conventional commits

Code Quality
~~~~~~~~~~~~

1. **Run ruff** for linting and formatting
2. **Use mypy** for type checking
3. **Run bandit** for security scanning
4. **Maintain test coverage** above 80%
5. **Use pre-commit hooks** to enforce standards

The Nix development environment provides a complete, reproducible setup for TurdParty development with enhanced productivity features and consistent tooling across all development machines.
