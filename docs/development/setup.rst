💩🎉TurdParty🎉💩 Development Setup Guide
==========================================

Overview
--------

This guide provides step-by-step instructions for setting up a complete TurdParty development environment using <PERSON> for reproducible builds and enhanced developer experience.

Prerequisites
-------------

System Requirements
~~~~~~~~~~~~~~~~~~~

.. list-table::
   :header-rows: 1
   :widths: 20 30 50

   * - Component
     - Requirement
     - Notes
   * - **Operating System**
     - Linux, macOS, WSL2
     - Windows requires WSL2
   * - **Memory**
     - 8GB+ RAM
     - 16GB recommended for full stack
   * - **Storage**
     - 20GB+ free space
     - SSD recommended
   * - **Network**
     - Stable internet
     - For downloading dependencies

Required Software
~~~~~~~~~~~~~~~~~

**1. Nix Package Manager:**

.. code-block:: bash

   # Install Nix (single-user installation)
   curl -L https://nixos.org/nix/install | sh
   
   # Reload shell
   source ~/.bashrc
   
   # Verify installation
   nix --version

**2. Git:**

.. code-block:: bash

   # Most systems have git pre-installed
   git --version
   
   # If not installed:
   # Ubuntu/Debian: sudo apt install git
   # macOS: xcode-select --install
   # Or use Nix: nix-env -iA nixpkgs.git

**3. Docker (Optional but Recommended):**

.. code-block:: bash

   # Install Docker Engine
   # Follow official Docker installation guide for your OS
   # https://docs.docker.com/engine/install/
   
   # Verify installation
   docker --version
   docker-compose --version

Quick Setup
-----------

**1. Clone Repository:**

.. code-block:: bash

   # Clone from tenbahtsecurity (main repository)
   <NAME_EMAIL>:tenbahtsecurity/turdparty-collab.git
   cd turdparty-collab
   
   # Or clone via HTTPS
   git clone https://github.com/tenbahtsecurity/turdparty-collab.git
   cd turdparty-collab

**2. Configure Git (First Time Only):**

.. code-block:: bash

   # Set your git identity
   git config --global user.name "Your Name"
   git config --global user.email "<EMAIL>"
   
   # Configure commit signing (recommended)
   git config --global commit.gpgsign true
   git config --global user.signingkey YOUR_GPG_KEY_ID

**3. Enter Development Environment:**

.. code-block:: bash

   # Enter Nix shell (downloads and configures everything)
   nix-shell
   
   # First run may take 5-10 minutes to download dependencies
   # Subsequent runs are instant
   
   # You'll see the enhanced prompt:
   💩🎉 user@host:~/turdparty-collab (master) $

**4. Verify Setup:**

.. code-block:: bash

   # Check Python version
   python --version  # Should show Python 3.12.x
   
   # Check development tools
   ruff --version
   pytest --version
   docker --version
   
   # Check TurdParty aliases
   tp-status  # Should show service status

Detailed Setup
--------------

Git Configuration
~~~~~~~~~~~~~~~~~

**Branch Configuration:**

The repository uses a specific git workflow:

.. code-block:: bash

   # Check current branch configuration
   git branch -vv
   git remote -v
   
   # Expected remotes:
   # origin: **************:tenbahtsecurity/turdparty-collab.git
   # develop-upstream: https://github.com/forkrul/turdparty-collab-develop_local.git

**For Core Contributors (with forkrul access):**

.. code-block:: bash

   # Add forkrul remote for development
   git remote add develop-upstream https://github.com/forkrul/turdparty-collab-develop_local.git
   
   # Create and configure develop/local branch
   git checkout -b develop/local
   git branch --set-upstream-to=develop-upstream/develop/local develop/local
   
   # Configure branch routing
   git config branch.develop/local.remote develop-upstream
   git config branch.develop/local.merge refs/heads/develop/local

**Pre-commit Hooks:**

.. code-block:: bash

   # Install pre-commit hooks (done automatically in nix-shell)
   pre-commit install
   
   # Test pre-commit hooks
   pre-commit run --all-files

Development Environment
~~~~~~~~~~~~~~~~~~~~~~~

**Directory Structure:**

.. code-block:: bash

   turdparty-collab/
   ├── .nix-zsh/              # Custom zsh configuration
   ├── api/                   # FastAPI application
   ├── docs/                  # Sphinx documentation
   ├── frontend/              # React frontend
   ├── scripts/               # Development scripts
   ├── tests/                 # Test suites
   ├── workers/               # Background workers
   ├── docker-compose.yml     # Service orchestration
   ├── shell.nix              # Nix development environment
   └── README.md              # Project overview

**Environment Variables:**

The Nix shell automatically sets up development environment variables:

.. code-block:: bash

   # Development Configuration
   DEVELOPMENT=true
   DEBUG=true
   LOG_LEVEL=DEBUG
   TURDPARTY_ENV=development
   
   # Docker Configuration
   COMPOSE_PROJECT_NAME=turdpartycollab
   DOCKER_BUILDKIT=1
   
   # Testing Configuration
   COVERAGE_THRESHOLD=80
   PARALLEL_WORKERS=4
   
   # Python Configuration
   PYTHONPATH="$PWD:$PYTHONPATH"
   PYTHONDONTWRITEBYTECODE=1
   PYTHONUNBUFFERED=1

Service Setup
~~~~~~~~~~~~~

**1. Start Core Services:**

.. code-block:: bash

   # Check service status
   tp-status
   
   # Start all services
   tp-start
   
   # Or start with logs
   tp-start --logs

**2. Verify Services:**

.. code-block:: bash

   # Check running containers
   dps  # alias for docker ps
   
   # Check service health
   curl http://localhost:8000/health
   curl http://api.turdparty.localhost/health  # via Traefik

**3. Access Services:**

.. list-table::
   :header-rows: 1
   :widths: 30 40 30

   * - Service
     - URL
     - Purpose
   * - **API**
     - http://api.turdparty.localhost
     - REST API and WebSocket
   * - **Frontend**
     - http://frontend.turdparty.localhost
     - Web interface
   * - **Docs**
     - http://frontend.turdparty.localhost/docs
     - Documentation
   * - **Kibana**
     - http://kibana.turdparty.localhost
     - Log analysis
   * - **MinIO**
     - http://minio.turdparty.localhost
     - Object storage

Testing Setup
~~~~~~~~~~~~~

**1. Run Test Suite:**

.. code-block:: bash

   # Full parallel test suite
   tp-test
   
   # Individual test categories
   python -m pytest tests/unit/ -v
   python -m pytest tests/integration/ -v
   python -m pytest tests/performance/ --benchmark-only

**2. Coverage Reporting:**

.. code-block:: bash

   # Generate coverage report
   python -m pytest --cov=. --cov-report=html
   
   # View coverage report
   open htmlcov/index.html  # macOS
   xdg-open htmlcov/index.html  # Linux

**3. Code Quality:**

.. code-block:: bash

   # Linting and formatting
   ruff check .
   ruff format .
   
   # Type checking
   mypy .
   
   # Security scanning
   bandit -r .
   
   # All quality checks
   pre-commit run --all-files

IDE Integration
---------------

VS Code Setup
~~~~~~~~~~~~~

**1. Install Extensions:**

.. code-block:: json

   {
       "recommendations": [
           "ms-python.python",
           "ms-python.black-formatter",
           "charliermarsh.ruff",
           "ms-python.mypy-type-checker",
           "ms-vscode.vscode-json",
           "redhat.vscode-yaml",
           "ms-vscode.docker",
           "ms-vscode-remote.remote-containers"
       ]
   }

**2. Configure Settings:**

.. code-block:: json

   {
       "python.defaultInterpreterPath": "./venv/bin/python",
       "python.linting.enabled": true,
       "python.linting.ruffEnabled": true,
       "python.formatting.provider": "black",
       "terminal.integrated.shell.linux": "/nix/store/.../bin/zsh",
       "files.exclude": {
           "**/__pycache__": true,
           "**/.pytest_cache": true,
           "**/.coverage": true,
           "**/htmlcov": true
       }
   }

**3. Debugging Configuration:**

.. code-block:: json

   {
       "version": "0.2.0",
       "configurations": [
           {
               "name": "TurdParty API",
               "type": "python",
               "request": "launch",
               "program": "api/main.py",
               "env": {
                   "DEVELOPMENT": "true",
                   "DEBUG": "true"
               },
               "console": "integratedTerminal"
           }
       ]
   }

PyCharm/IntelliJ Setup
~~~~~~~~~~~~~~~~~~~~~~

**1. Project Configuration:**

- Set Python interpreter to Nix Python installation
- Configure project structure with source roots
- Set up run configurations for API and tests

**2. Code Style:**

- Import ruff configuration
- Set up automatic formatting on save
- Configure type checking with mypy

**3. Testing Integration:**

- Configure pytest as test runner
- Set up coverage reporting
- Configure debugging for tests

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Nix Installation Issues:**

.. code-block:: bash

   # Permission issues
   sudo chown -R $(whoami) /nix
   
   # Path issues
   echo 'source ~/.nix-profile/etc/profile.d/nix.sh' >> ~/.bashrc
   source ~/.bashrc
   
   # Clear cache
   nix-collect-garbage

**Git Configuration Issues:**

.. code-block:: bash

   # Check git configuration
   git config --list
   
   # Fix remote URLs
   git remote set-<NAME_EMAIL>:tenbahtsecurity/turdparty-collab.git
   
   # Fix branch tracking
   git branch --set-upstream-to=origin/master master

**Docker Issues:**

.. code-block:: bash

   # Permission issues (Linux)
   sudo usermod -aG docker $USER
   newgrp docker
   
   # Service not running
   sudo systemctl start docker
   sudo systemctl enable docker
   
   # Clean up
   docker system prune -a

**Service Startup Issues:**

.. code-block:: bash

   # Check port conflicts
   netstat -tulpn | grep :8000
   
   # Reset services
   dcd && dcu
   
   # Check logs
   dcl

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~

**Faster Nix Builds:**

.. code-block:: bash

   # Use binary cache
   echo "substituters = https://cache.nixos.org/" >> ~/.config/nix/nix.conf
   
   # Parallel builds
   echo "max-jobs = auto" >> ~/.config/nix/nix.conf

**Faster Docker Builds:**

.. code-block:: bash

   # Enable BuildKit
   export DOCKER_BUILDKIT=1
   
   # Use layer caching
   docker-compose build --parallel

**Faster Tests:**

.. code-block:: bash

   # Parallel test execution
   python -m pytest -n auto
   
   # Skip slow tests during development
   python -m pytest -m "not slow"

Next Steps
----------

After completing the setup:

1. **Read the documentation** - :doc:`../index`
2. **Explore the API** - :doc:`../api/overview`
3. **Run the test suite** - :doc:`testing`
4. **Check the architecture** - :doc:`../platform/architecture`
5. **Start contributing** - :doc:`contributing`

**Development Workflow:**

.. code-block:: bash

   # Daily routine
   nix-shell                    # Enter development environment
   tp-status                    # Check service status
   tp-start                     # Start services if needed
   # ... development work ...
   tp-test                      # Run tests
   ruff check . && ruff format . # Code quality
   git add . && git commit      # Commit changes
   git push                     # Push to appropriate remote

The development environment is now ready for TurdParty development with all tools, services, and configurations properly set up!
