Kibana Dashboards
==================

Overview
--------

TurdParty provides four comprehensive Kibana dashboards that offer complete visibility into the malware analysis platform. These dashboards provide real-time monitoring, threat detection, and performance analytics through interactive visualizations.

Dashboard Collection
---------------------

1. **TurdParty Workflow Overview**
   - Complete workflow lifecycle monitoring
   - Success/failure rate tracking
   - Performance metrics and trends
   - VM pool status and utilization

2. **TurdParty VM Runtime Monitoring**
   - Real-time VM performance metrics
   - Process activity and resource usage
   - File system operations tracking
   - Suspicious activity detection

3. **TurdParty Threat Detection & Analysis**
   - Advanced threat hunting capabilities
   - IOC tracking and classification
   - Malware behavior analysis
   - Network activity monitoring

4. **TurdParty System Performance**
   - Platform health monitoring
   - Resource utilization tracking
   - Error rate analysis
   - ELK stack performance

Dashboard Details
-----------------

TurdParty Workflow Overview
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Purpose**: Monitor the complete workflow lifecycle and system performance

**Key Visualizations**:

.. list-table:: Workflow Overview Panels
   :widths: 30 70
   :header-rows: 1

   * - Panel
     - Description
   * - Workflow Status Distribution
     - Pie chart showing success/failure rates across all workflows
   * - Workflow Events Timeline
     - Time-series view of workflow activities and completion rates
   * - Recent Workflow Events
     - Table of latest workflow executions with status and timing
   * - VM Pool Status
     - Real-time view of VM pool health and capacity
   * - File Analysis Metrics
     - Success/failure trends and performance indicators

**Key Metrics**:
   - Total workflows processed
   - Success rate percentage
   - Average processing time
   - Current VM pool utilization
   - Error rate trends

**Filters Available**:
   - Time range selection
   - Workflow status filtering
   - VM template filtering
   - File type filtering

**Refresh Rate**: 30 seconds

TurdParty VM Runtime Monitoring
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Purpose**: Real-time monitoring of VM behavior during malware analysis

**Key Visualizations**:

.. list-table:: VM Monitoring Panels
   :widths: 30 70
   :header-rows: 1

   * - Panel
     - Description
   * - CPU Usage
     - Real-time CPU utilization across all active VMs
   * - Memory Usage
     - Memory consumption trends and patterns
   * - Disk Usage
     - Disk space utilization and I/O activity
   * - Network Activity
     - Network traffic volume and connection patterns
   * - Process Activity
     - Running process counts and resource consumption
   * - File Operations
     - File system events (create, modify, delete, move)
   * - Suspicious Activity Alerts
     - Automated detection of potentially malicious behavior

**Key Metrics**:
   - CPU usage percentage
   - Memory utilization (MB and %)
   - Disk I/O rates
   - Network bytes transferred
   - Active process count
   - File operation frequency
   - Suspicious activity count

**Alert Thresholds**:
   - CPU usage > 80%
   - Memory usage > 90%
   - Disk usage > 95%
   - Suspicious process detection
   - Unusual network activity

**Refresh Rate**: 10 seconds

TurdParty Threat Detection & Analysis
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Purpose**: Advanced threat hunting and malware behavior analysis

**Key Visualizations**:

.. list-table:: Threat Detection Panels
   :widths: 30 70
   :header-rows: 1

   * - Panel
     - Description
   * - Threat Level Distribution
     - Classification of analyzed files by risk level (low/medium/high/critical)
   * - Malware Behavior Timeline
     - Chronological view of malicious activities and events
   * - Network Connections Map
     - Geographic and protocol analysis of external connections
   * - File Hash Analysis
     - Tracking of file signatures, variants, and families
   * - Process Execution Tree
     - Parent-child process relationships and execution chains
   * - IOC Indicators Table
     - Indicators of Compromise with confidence scores and types

**Key Metrics**:
   - Threat level distribution
   - IOC confidence scores
   - Network connection counts
   - Process execution patterns
   - File hash uniqueness
   - Behavioral indicators

**Threat Categories**:
   - **Critical** (90-100% confidence): Confirmed malware
   - **High** (70-89% confidence): Likely malicious
   - **Medium** (50-69% confidence): Suspicious activity
   - **Low** (0-49% confidence): Potentially unwanted

**IOC Types Tracked**:
   - File hashes (MD5, SHA1, SHA256, Blake3)
   - IP addresses and domains
   - Registry keys and values
   - File paths and names
   - Process names and command lines
   - Network protocols and ports

**Refresh Rate**: 15 seconds

TurdParty System Performance
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Purpose**: Platform health monitoring and resource optimization

**Key Visualizations**:

.. list-table:: System Performance Panels
   :widths: 30 70
   :header-rows: 1

   * - Panel
     - Description
   * - ELK Stack Health
     - Elasticsearch, Logstash, and Kibana service status
   * - Worker Queue Status
     - Real-time view of task processing across all worker types
   * - VM Pool Metrics
     - Pool utilization trends and provisioning patterns
   * - Data Ingestion Rate
     - Volume of data flowing through the ELK pipeline
   * - Workflow Performance
     - Processing times and throughput metrics
   * - Error Rate Monitoring
     - System reliability and failure tracking

**Key Metrics**:
   - Service uptime percentage
   - Queue depth and processing rate
   - VM pool utilization
   - Data ingestion volume (MB/hour)
   - Average workflow duration
   - Error rate percentage

**Performance Indicators**:
   - Green: All systems operational
   - Yellow: Performance degradation detected
   - Red: Critical issues requiring attention

**Refresh Rate**: 30 seconds

Dashboard Usage Guide
---------------------

Accessing Dashboards
~~~~~~~~~~~~~~~~~~~~~

**URL Access**:
   - Primary: ``http://kibana.turdparty.localhost``
   - Direct dashboard links available in React UI
   - Embedded dashboard views in monitoring sections

**Navigation**:
   - Dashboard list: ``/app/dashboards``
   - Discover: ``/app/discover`` (raw data exploration)
   - Visualize: ``/app/visualize`` (custom visualizations)

**Authentication**:
   - Single sign-on integration (if configured)
   - Basic authentication (development)
   - API key access for programmatic use

Interactive Features
~~~~~~~~~~~~~~~~~~~~

**Time Range Selection**:
   - Quick ranges: Last 15 minutes, 1 hour, 24 hours, 7 days
   - Custom range picker
   - Auto-refresh intervals (10s, 30s, 1m, 5m)
   - Time zone adjustment

**Filtering and Drill-down**:
   - Click on visualizations to filter data
   - Multi-level drill-down capabilities
   - Cross-dashboard filtering
   - Saved filter sets

**Export and Sharing**:
   - PDF export for reports
   - PNG export for presentations
   - Dashboard sharing via URLs
   - Embedded dashboard integration

Advanced Analytics
~~~~~~~~~~~~~~~~~~

**Custom Queries**:
   - Lucene query syntax support
   - KQL (Kibana Query Language) for complex searches
   - Saved search functionality
   - Query history and favorites

**Aggregations and Calculations**:
   - Statistical aggregations (avg, sum, min, max)
   - Percentile calculations
   - Rate calculations and derivatives
   - Custom metric calculations

**Alerting and Notifications**:
   - Threshold-based alerts
   - Anomaly detection alerts
   - Email and webhook notifications
   - Alert history and management

Dashboard Customization
-----------------------

Creating Custom Dashboards
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Dashboard Builder**:
   - Drag-and-drop panel creation
   - Visualization type selection
   - Data source configuration
   - Layout and sizing options

**Visualization Types**:
   - Line charts for time-series data
   - Bar charts for categorical data
   - Pie charts for distribution analysis
   - Tables for detailed data views
   - Maps for geographic data
   - Gauges for single-value metrics

**Panel Configuration**:
   - Data source selection
   - Field mapping and aggregation
   - Color schemes and styling
   - Axis configuration and scaling
   - Legend and label customization

Modifying Existing Dashboards
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Edit Mode**:
   - Panel resizing and repositioning
   - Visualization modification
   - Filter and query updates
   - Time range adjustments

**Version Control**:
   - Dashboard versioning
   - Change tracking
   - Rollback capabilities
   - Export/import functionality

**Collaboration**:
   - Shared dashboard editing
   - Comment and annotation system
   - User permission management
   - Team workspace organization

Data Sources and Index Patterns
--------------------------------

Index Pattern Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Primary Index Patterns**:
   - ``turdparty-workflow-events-*``: Workflow lifecycle data
   - ``turdparty-vm-monitoring-*``: VM runtime metrics
   - ``turdparty-runtime-ecs-*``: Analysis runtime data
   - ``turdparty-install-ecs-*``: Installation events

**Field Mappings**:
   - Timestamp fields: ``@timestamp``
   - Keyword fields: For exact matching and aggregations
   - Text fields: For full-text search
   - Numeric fields: For mathematical operations
   - Geo fields: For geographic visualizations

**Index Management**:
   - Automatic index rotation (daily)
   - Retention policy enforcement
   - Index optimization and compression
   - Backup and recovery procedures

Query Optimization
~~~~~~~~~~~~~~~~~~~

**Performance Best Practices**:
   - Use specific time ranges
   - Limit result set sizes
   - Optimize aggregation queries
   - Use cached queries when possible

**Common Query Patterns**:

.. code-block:: lucene

   # Find failed workflows
   event.outcome:failure AND turdparty.phase:workflow
   
   # High CPU usage VMs
   host.cpu.usage:>80 AND event.action:vm_metrics
   
   # Suspicious processes
   process.suspicious_reasons:* AND event.action:process_monitoring
   
   # Network connections to specific IPs
   destination.ip:************* AND network.protocol:tcp

Troubleshooting and Maintenance
--------------------------------

Common Issues
~~~~~~~~~~~~~

**Dashboard Loading Problems**:
   - Check Elasticsearch connectivity
   - Verify index pattern existence
   - Validate time range selection
   - Review browser console errors

**Performance Issues**:
   - Optimize query time ranges
   - Reduce visualization complexity
   - Check Elasticsearch cluster health
   - Monitor resource utilization

**Data Visualization Problems**:
   - Verify field mappings
   - Check data availability
   - Validate aggregation settings
   - Review filter configurations

Maintenance Tasks
~~~~~~~~~~~~~~~~~

**Regular Maintenance**:
   - Index pattern updates
   - Dashboard performance optimization
   - User access review
   - Backup verification

**Monitoring**:
   - Dashboard usage analytics
   - Query performance monitoring
   - Resource utilization tracking
   - Error rate analysis

**Updates and Upgrades**:
   - Kibana version management
   - Dashboard migration procedures
   - Feature update integration
   - Security patch application

Integration with TurdParty Platform
------------------------------------

React UI Integration
~~~~~~~~~~~~~~~~~~~~

**Embedded Dashboards**:
   - Dashboard iframe embedding
   - Single sign-on integration
   - Context-aware filtering
   - Real-time data synchronization

**Navigation Integration**:
   - Direct links from workflow pages
   - VM-specific dashboard views
   - File analysis result integration
   - Alert notification system

API Integration
~~~~~~~~~~~~~~~

**Programmatic Access**:
   - Kibana API for dashboard management
   - Elasticsearch API for data queries
   - Webhook integration for alerts
   - Custom visualization development

**Automation**:
   - Automated dashboard deployment
   - Scheduled report generation
   - Alert rule management
   - Data export automation
