ELK Stack Integration Overview
==============================

Introduction
------------

TurdParty integrates a complete ELK (Elasticsearch, Logstash, Kibana) stack to provide comprehensive data collection, analysis, and visualization capabilities for malware analysis workflows. The ELK integration enables real-time monitoring, threat detection, and advanced analytics through a standardized data pipeline.

Architecture Overview
---------------------

.. mermaid::

   graph TB
       subgraph "Data Sources"
           VM1[VM Agent 1]
           VM2[VM Agent 2]
           VM3[VM Agent 3]
           WF[Workflow Events]
           SYS[System Metrics]
       end
       
       subgraph "Data Pipeline"
           LS[Logstash]
           ES[Elasticsearch]
           KB[Kibana]
       end
       
       subgraph "Visualization"
           D1[Workflow Overview]
           D2[VM Monitoring]
           D3[Threat Detection]
           D4[System Performance]
       end
       
       VM1 --> LS
       VM2 --> LS
       VM3 --> LS
       WF --> LS
       SYS --> LS
       
       LS --> ES
       ES --> KB
       
       KB --> D1
       KB --> D2
       KB --> D3
       KB --> D4

Core Components
---------------

Elasticsearch
~~~~~~~~~~~~~

**Purpose**: Time-series data storage and search engine

**Configuration**:
   - Cluster name: ``turdparty-cluster``
   - Node roles: master, data, ingest
   - Memory allocation: 2GB heap (configurable)
   - Index patterns: ``turdparty-*``

**Index Structure**:
   - ``turdparty-workflow-events-*``: Workflow lifecycle events
   - ``turdparty-vm-monitoring-*``: VM runtime metrics and monitoring
   - ``turdparty-runtime-ecs-*``: Runtime analysis data
   - ``turdparty-install-ecs-*``: Installation and setup events

**Features**:
   - Automatic index rotation (daily)
   - ECS-compliant field mappings
   - Optimized for time-series data
   - Configurable retention policies

Logstash
~~~~~~~~

**Purpose**: Data ingestion, transformation, and routing

**Input Sources**:
   - HTTP input (port 8080) for VM agents and workers
   - Beats input for system metrics
   - File input for log aggregation
   - TCP/UDP inputs for network data

**Processing Pipeline**:
   - ECS field standardization
   - Data enrichment and transformation
   - Geoip lookup for network connections
   - Threat intelligence integration
   - Data validation and filtering

**Output Routing**:
   - Route to appropriate Elasticsearch indices
   - Conditional routing based on data type
   - Error handling and dead letter queues
   - Performance optimization

Kibana
~~~~~~

**Purpose**: Data visualization and dashboard management

**Dashboards**:
   - **Workflow Overview**: Complete workflow lifecycle monitoring
   - **VM Runtime Monitoring**: Real-time VM performance and behavior
   - **Threat Detection & Analysis**: Advanced threat hunting and IOC tracking
   - **System Performance**: Platform health and resource utilization

**Features**:
   - Real-time data visualization
   - Interactive filtering and drill-down
   - Alerting and notification system
   - Custom dashboard creation
   - Export and sharing capabilities

Data Flow Architecture
----------------------

VM Agent Data Collection
~~~~~~~~~~~~~~~~~~~~~~~~~

**System Metrics** (5-second intervals):
   - CPU usage and load averages
   - Memory utilization and swap usage
   - Disk usage and I/O statistics
   - Network traffic and connections

**Process Monitoring**:
   - Running process inventory
   - Resource consumption per process
   - Process creation and termination
   - Command line arguments and paths

**File System Events**:
   - File creation, modification, deletion
   - Directory changes and permissions
   - File access patterns
   - Suspicious file operations

**Network Activity**:
   - Active network connections
   - Traffic volume and patterns
   - External communication attempts
   - Protocol analysis

Workflow Event Streaming
~~~~~~~~~~~~~~~~~~~~~~~~~

**Lifecycle Events**:
   - Workflow initiation and completion
   - VM allocation and termination
   - File processing stages
   - Error conditions and failures

**Performance Metrics**:
   - Task execution times
   - Resource utilization
   - Queue depths and processing rates
   - System health indicators

**Security Events**:
   - Suspicious activity detection
   - IOC identification and scoring
   - Threat level classification
   - Security alert generation

ECS Compliance
--------------

Elastic Common Schema (ECS) Standardization
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Field Mapping**:
   - ``@timestamp``: Event timestamp (ISO 8601)
   - ``event.*``: Event metadata (action, category, type, outcome)
   - ``host.*``: Host information (name, IP, OS, metrics)
   - ``process.*``: Process details (PID, name, command line)
   - ``file.*``: File information (path, hash, size, operation)
   - ``network.*``: Network data (protocol, bytes, connections)
   - ``turdparty.*``: Custom TurdParty-specific fields

**Custom Fields**:
   - ``turdparty.workflow_id``: Unique workflow identifier
   - ``turdparty.vm_id``: Virtual machine identifier
   - ``turdparty.phase``: Analysis phase (install, runtime, cleanup)
   - ``turdparty.threat_level``: Threat classification (low, medium, high, critical)
   - ``turdparty.indicators``: IOC array with type, value, and confidence

**Benefits**:
   - Standardized data structure across all sources
   - Improved search and correlation capabilities
   - Better integration with security tools
   - Enhanced visualization and reporting

Data Pipeline Configuration
---------------------------

Logstash Pipeline
~~~~~~~~~~~~~~~~~

**Input Configuration**:

.. code-block:: ruby

   input {
     http {
       port => 8080
       codec => json
     }
     
     beats {
       port => 5044
     }
   }

**Filter Configuration**:

.. code-block:: ruby

   filter {
     # ECS field standardization
     if [turdparty] {
       mutate {
         add_field => { "[@metadata][index_prefix]" => "turdparty" }
       }
     }
     
     # Geoip enrichment for network data
     if [destination][ip] {
       geoip {
         source => "[destination][ip]"
         target => "[destination][geo]"
       }
     }
     
     # Threat level classification
     if [turdparty][indicators] {
       ruby {
         code => "
           indicators = event.get('[turdparty][indicators]')
           if indicators && indicators.length > 0
             max_confidence = indicators.map { |i| i['confidence'] || 0 }.max
             if max_confidence >= 90
               event.set('[turdparty][threat_level]', 'critical')
             elsif max_confidence >= 70
               event.set('[turdparty][threat_level]', 'high')
             elsif max_confidence >= 50
               event.set('[turdparty][threat_level]', 'medium')
             else
               event.set('[turdparty][threat_level]', 'low')
             end
           end
         "
       }
     }
   }

**Output Configuration**:

.. code-block:: ruby

   output {
     if [event][dataset] == "turdparty.workflow" {
       elasticsearch {
         hosts => ["elasticsearch:9200"]
         index => "turdparty-workflow-events-%{+YYYY.MM.dd}"
         template_name => "turdparty-workflow-events"
         template_pattern => "turdparty-workflow-events-*"
       }
     }
     
     if [event][dataset] == "turdparty.vm_metrics" {
       elasticsearch {
         hosts => ["elasticsearch:9200"]
         index => "turdparty-vm-monitoring-%{+YYYY.MM.dd}"
         template_name => "turdparty-vm-monitoring"
         template_pattern => "turdparty-vm-monitoring-*"
       }
     }
   }

Elasticsearch Templates
~~~~~~~~~~~~~~~~~~~~~~~

**VM Monitoring Template** (``turdparty-vm-monitoring-template.json``):
   - Optimized field mappings for VM metrics
   - Time-series data optimization
   - Proper data types for numeric fields
   - Keyword fields for aggregations

**Workflow Events Template**:
   - Event lifecycle tracking
   - Status and outcome fields
   - Performance metrics storage
   - Error message indexing

Performance Optimization
-------------------------

Indexing Performance
~~~~~~~~~~~~~~~~~~~~

**Bulk Indexing**:
   - Batch size optimization (100-1000 documents)
   - Concurrent indexing threads
   - Refresh interval tuning
   - Memory allocation optimization

**Index Management**:
   - Daily index rotation
   - Automatic index lifecycle management
   - Compression and optimization
   - Retention policy enforcement

**Query Performance**:
   - Optimized field mappings
   - Proper index patterns
   - Query caching
   - Aggregation optimization

Resource Management
~~~~~~~~~~~~~~~~~~~

**Memory Allocation**:
   - Elasticsearch heap sizing (50% of available RAM)
   - Logstash JVM tuning
   - Kibana memory optimization
   - OS-level caching

**Storage Optimization**:
   - SSD storage for hot data
   - Compression for cold data
   - Index sharding strategy
   - Backup and archival

Monitoring and Alerting
-----------------------

System Health Monitoring
~~~~~~~~~~~~~~~~~~~~~~~~~

**ELK Stack Health**:
   - Cluster health monitoring
   - Node availability tracking
   - Index health and status
   - Performance metrics collection

**Data Pipeline Health**:
   - Ingestion rate monitoring
   - Processing latency tracking
   - Error rate alerting
   - Queue depth monitoring

**Alert Configuration**:
   - Critical system failures
   - Performance degradation
   - Data pipeline issues
   - Security event notifications

Security Monitoring
~~~~~~~~~~~~~~~~~~~

**Threat Detection**:
   - Automated IOC detection
   - Behavioral analysis alerts
   - Anomaly detection
   - Threat level escalation

**Security Alerts**:
   - High-confidence threat indicators
   - Suspicious process activity
   - Network communication anomalies
   - File system security events

Integration Points
------------------

Worker Integration
~~~~~~~~~~~~~~~~~~

**ELK Integration Worker** (``services/workers/tasks/elk_integration.py``):
   - Workflow event streaming
   - VM metrics collection
   - Health monitoring
   - Error handling and retry logic

**VM Agent Integration** (``services/monitoring/vm-agent/agent.py``):
   - Real-time data streaming
   - ECS-compliant formatting
   - Buffered transmission
   - Connection management

API Integration
~~~~~~~~~~~~~~~

**Health Endpoints**:
   - ``/api/v1/health/elk``: ELK stack connectivity
   - ``/api/v1/health/workers``: Worker queue status
   - ``/health``: Overall system health

**Data Access**:
   - Elasticsearch query API
   - Kibana dashboard embedding
   - Real-time data streaming
   - Historical data analysis

Deployment and Configuration
----------------------------

Docker Compose Setup
~~~~~~~~~~~~~~~~~~~~~

**ELK Services** (``compose/docker-compose.elk.yml``):
   - Elasticsearch cluster configuration
   - Logstash pipeline setup
   - Kibana dashboard deployment
   - Network and volume configuration

**Environment Variables**:
   - ``ELASTICSEARCH_HOST``: Elasticsearch hostname
   - ``LOGSTASH_HOST``: Logstash hostname
   - ``KIBANA_HOST``: Kibana hostname
   - ``ELK_MEMORY``: Memory allocation settings

**Volume Mounts**:
   - Elasticsearch data persistence
   - Logstash configuration files
   - Kibana dashboard definitions
   - Log file aggregation

Production Considerations
~~~~~~~~~~~~~~~~~~~~~~~~~

**Scalability**:
   - Multi-node Elasticsearch cluster
   - Logstash pipeline parallelization
   - Load balancing and failover
   - Horizontal scaling strategies

**Security**:
   - Authentication and authorization
   - SSL/TLS encryption
   - Network security
   - Access control and auditing

**Backup and Recovery**:
   - Elasticsearch snapshot management
   - Configuration backup
   - Disaster recovery procedures
   - Data retention policies
