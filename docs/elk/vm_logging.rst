VM Elasticsearch Logging
========================

The 💩🎉 TurdParty platform includes comprehensive Elasticsearch logging for all VM operations, providing detailed audit trails, performance metrics, and operational insights for the VM management system.

Overview
--------

The VM logging system captures:

- **VM Lifecycle Events**: Creation, allocation, termination
- **Pool Management Operations**: Maintenance tasks and status changes
- **Performance Metrics**: Allocation times, provisioning durations
- **Error Tracking**: Failed operations with detailed context
- **Correlation Tracking**: Request tracing across distributed operations

Architecture
------------

.. mermaid::

   graph TB
       subgraph "VM Operations"
           VM[VM Manager]
           POOL[Pool Manager]
           ALLOC[Allocation Service]
       end

       subgraph "Logging Layer"
           VMLOG[VM Logger]
           FALLBACK[Fallback Logger]
       end

       subgraph "ELK Stack"
           ES[(Elasticsearch)]
           LS[Logstash]
           KB[Kibana]
       end

       VM --> VMLOG
       POOL --> VMLOG
       ALLOC --> VMLOG
       
       VMLOG --> ES
       VMLOG -.-> FALLBACK
       
       ES --> LS
       LS --> KB

Logger Implementation
---------------------

The VM logging system includes robust fallback mechanisms:

**Primary Logger (Elasticsearch Available):**

.. code-block:: python

   from utils.elasticsearch_logger import VMLogger, vm_logger
   
   # Log VM allocation
   vm_logger.log_vm_allocation(
       vm_id="19ae1a52-06d3-4f66-baf1-0ffdcfd15490",
       template="ubuntu:20.04",
       success=True,
       duration_ms=45,
       metadata={
           "allocation_method": "immediate_pool",
           "requester_id": "api_user",
           "correlation_id": "550e8400-e29b-41d4-a716-************"
       }
   )

**Fallback Logger (Elasticsearch Unavailable):**

.. code-block:: python

   class VMLogger:
       """Fallback VM logger when Elasticsearch is not available"""
       def __init__(self):
           self.correlation_id = str(uuid.uuid4())
           
       def log_vm_allocation(self, *args, **kwargs):
           # Logs to standard Python logging as fallback
           pass

Event Types
-----------

**VM Allocation Events**

Logged when VMs are allocated from pools or provisioned on-demand:

.. code-block:: json

   {
     "@timestamp": "2025-06-17T22:04:34.596Z",
     "event_type": "vm_allocation",
     "vm_id": "19ae1a52-06d3-4f66-baf1-0ffdcfd15490",
     "template": "ubuntu:20.04",
     "allocation_method": "immediate_pool",
     "success": true,
     "duration_ms": 45,
     "requester_id": "api_user",
     "correlation_id": "550e8400-e29b-41d4-a716-************",
     "metadata": {
       "vm_name": "pool_ubuntu_20.04_e18fed06",
       "pool_maintenance_triggered": true
     }
   }

**VM Provisioning Events**

Logged when new VMs are created for pools:

.. code-block:: json

   {
     "@timestamp": "2025-06-17T22:05:15.123Z",
     "event_type": "vm_provisioning",
     "vm_id": "7da82037-c7fc-4707-acb1-8510b1535349",
     "template": "ubuntu:20.04",
     "success": true,
     "duration_ms": 2340,
     "trigger": "scheduled_maintenance",
     "correlation_id": "550e8400-e29b-41d4-a716-446655440001",
     "metadata": {
       "vm_name": "pool_ubuntu_20.04_b1df597d",
       "memory_mb": 1024,
       "cpus": 1
     }
   }

**Pool Maintenance Events**

Logged during scheduled pool maintenance operations:

.. code-block:: json

   {
     "@timestamp": "2025-06-17T22:05:00.000Z",
     "event_type": "pool_maintenance",
     "actions_taken": [
       {
         "action": "provision",
         "template": "ubuntu:20.04",
         "vm_id": "7da82037-c7fc-4707-acb1-8510b1535349",
         "duration_ms": 2340
       }
     ],
     "pool_status": {
       "ubuntu:20.04": {
         "ready": 1,
         "total": 3,
         "needs_provisioning": true
       }
     },
     "duration_ms": 2450,
     "correlation_id": "550e8400-e29b-41d4-a716-446655440001"
   }

**Pool Status Events**

Logged periodically to track pool health:

.. code-block:: json

   {
     "@timestamp": "2025-06-17T22:04:35.000Z",
     "event_type": "pool_status",
     "template": "ubuntu:20.04",
     "ready_count": 2,
     "total_count": 3,
     "creating_count": 1,
     "running_count": 0,
     "terminated_count": 0,
     "needs_provisioning": false,
     "can_provision": true,
     "min_ready": 1,
     "max_total": 3
   }

**Performance Metrics**

Logged to track system performance:

.. code-block:: json

   {
     "@timestamp": "2025-06-17T22:04:34.641Z",
     "event_type": "performance_metric",
     "metric_name": "pool.status.query_time",
     "value": 45,
     "unit": "ms",
     "metadata": {
       "templates_checked": 4,
       "operation": "get_pool_status"
     }
   }

**Error Events**

Logged when operations fail:

.. code-block:: json

   {
     "@timestamp": "2025-06-17T22:06:00.000Z",
     "event_type": "error",
     "error_message": "Failed to provision VM: Docker daemon not responding",
     "operation": "provision_vm",
     "template": "ubuntu:20.04",
     "vm_id": "failed-vm-id",
     "correlation_id": "550e8400-e29b-41d4-a716-446655440002",
     "stack_trace": "...",
     "metadata": {
       "retry_count": 3,
       "last_error": "Connection timeout"
     }
   }

Correlation Tracking
--------------------

All related operations share correlation IDs for distributed tracing:

.. code-block:: python

   # Generate correlation ID for request
   correlation_id = vm_logger.new_correlation_id()
   
   # Log request initiation
   vm_logger.log_request("vm_allocation", {
       "template": "ubuntu:20.04",
       "requester_id": "api_user",
       "correlation_id": correlation_id
   })
   
   # All subsequent operations use the same correlation_id
   vm_logger.log_vm_allocation(vm_id, template, True, duration_ms, {
       "correlation_id": correlation_id,
       "allocation_method": "immediate_pool"
   })

Kibana Dashboards
-----------------

**VM Operations Dashboard**

Visualizes VM allocation patterns, success rates, and performance metrics:

- VM allocation timeline
- Success/failure rates by template
- Average allocation times
- Pool status overview

**Pool Management Dashboard**

Monitors pool health and maintenance operations:

- Pool size trends over time
- Maintenance frequency and duration
- Provisioning success rates
- Resource utilization by template

**Performance Monitoring Dashboard**

Tracks system performance metrics:

- Response time distributions
- Throughput metrics
- Error rate trends
- Correlation analysis

Query Examples
--------------

**Find All VM Allocations for a Template:**

.. code-block:: json

   {
     "query": {
       "bool": {
         "must": [
           {"term": {"event_type": "vm_allocation"}},
           {"term": {"template": "ubuntu:20.04"}},
           {"range": {"@timestamp": {"gte": "now-1h"}}}
         ]
       }
     }
   }

**Track Operations by Correlation ID:**

.. code-block:: json

   {
     "query": {
       "term": {"correlation_id": "550e8400-e29b-41d4-a716-************"}
     },
     "sort": [{"@timestamp": {"order": "asc"}}]
   }

**Monitor Pool Health:**

.. code-block:: json

   {
     "query": {
       "bool": {
         "must": [
           {"term": {"event_type": "pool_status"}},
           {"term": {"needs_provisioning": true}}
         ]
       }
     }
   }

Configuration
-------------

**Environment Variables:**

.. code-block:: bash

   # Elasticsearch connection
   ELASTICSEARCH_URL=http://elasticsearch:9200
   
   # Enable/disable logging
   ELASTICSEARCH_LOGGING_ENABLED=true
   
   # Log level for fallback logger
   LOG_LEVEL=INFO

**Python Configuration:**

.. code-block:: python

   # Check if Elasticsearch logging is available
   try:
       from utils.elasticsearch_logger import VMLogger, vm_logger
       ELASTICSEARCH_AVAILABLE = True
   except ImportError:
       ELASTICSEARCH_AVAILABLE = False
       # Use fallback logger
       vm_logger = VMLogger()

Integration Testing
-------------------

The logging system includes comprehensive tests:

.. code-block:: python

   def test_vm_allocation_logging():
       """Test VM allocation event logging"""
       vm_logger.log_vm_allocation(
           vm_id="test-vm",
           template="ubuntu:20.04",
           success=True,
           duration_ms=100
       )
       
       # Verify event was logged to Elasticsearch
       # Check fallback behavior when ES unavailable

Troubleshooting
---------------

**Common Issues:**

1. **Elasticsearch Connection Failures**
   - Check ELASTICSEARCH_URL configuration
   - Verify network connectivity
   - Fallback logger will activate automatically

2. **Missing Correlation IDs**
   - Ensure correlation_id is passed through all operations
   - Check request initiation logging

3. **Performance Impact**
   - Logging is asynchronous and non-blocking
   - Failed logging attempts don't affect VM operations
   - Monitor Elasticsearch cluster health

**Debug Logging:**

.. code-block:: python

   import logging
   logging.getLogger('utils.elasticsearch_logger').setLevel(logging.DEBUG)

The VM Elasticsearch logging system provides comprehensive observability for the VM management platform while maintaining high availability through robust fallback mechanisms.
