====================
📡 API Reference
====================

Complete reference for the TurdParty REST API and WebSocket endpoints.

.. contents:: Table of Contents
   :local:
   :depth: 2

🌐 API Overview
===============

Base Configuration
-----------------

.. code-block:: text

   Base URL: http://localhost:8000/api/v1
   Content-Type: application/json
   Authentication: Handled by Traefik (external)
   WebSocket URL: ws://localhost:8000/ws

**Core Features**:
- RESTful API with OpenAPI 3.0 specification
- Real-time WebSocket connections for monitoring
- Comprehensive error handling and validation
- ELK stack integration for all API calls
- Automatic documentation generation

📁 File Management
==================

Upload Files for Analysis
-------------------------

.. code-block:: http

   POST /api/v1/files/upload

Upload malware samples with metadata tracking and Blake3 hashing.

**Response Example**:

.. code-block:: json

   {
     "file_id": "uuid-string",
     "filename": "sample.exe",
     "blake3_hash": "hash_value",
     "status": "uploaded",
     "minio_path": "files/uuid/sample.exe"
   }

🖥️ VM Management
================

Create Virtual Machine
----------------------

.. code-block:: http

   POST /api/v1/vms

Create and configure VMs for malware analysis with Docker or Vagrant backends.

**Request Example**:

.. code-block:: json

   {
     "name": "analysis-vm-001",
     "template": "ubuntu:20.04",
     "vm_type": "docker",
     "memory_mb": 2048,
     "cpus": 2,
     "domain": "analysis"
   }

**Response Example**:

.. code-block:: json

   {
     "vm_id": "vm-uuid",
     "name": "analysis-vm-001",
     "status": "running",
     "ip_address": "***********",
     "created_at": "2024-01-01T12:00:00Z"
   }

VM Actions
----------

.. code-block:: http

   POST /api/v1/vms/{vm_id}/actions

Perform actions on VMs: start, stop, restart, destroy, suspend, resume.

**WebSocket Monitoring**:

.. code-block:: javascript

   const ws = new WebSocket('ws://localhost:8000/ws/vms/{vm_id}');
   ws.onmessage = (event) => {
     const metrics = JSON.parse(event.data);
     console.log('VM Metrics:', metrics);
   };
   WebSocket URL: ws://localhost:8000/ws

**Key Features**:
- RESTful API design with OpenAPI 3.0 specification
- Real-time WebSocket connections for live monitoring
- Comprehensive error handling with detailed responses
- Request validation using Pydantic models
- Automatic API documentation generation
- ELK stack integration for all API calls

📁 File Management API
======================

File Upload Endpoint
-------------------

Upload files for malware analysis with comprehensive metadata tracking.

.. code-block:: http

   POST /api/v1/files/upload
   Content-Type: multipart/form-data

**Request Parameters**:

.. code-block:: json

   {
     "file": "binary_file_data",
     "description": "Optional file description",
     "tags": ["malware", "analysis"],
     "priority": "high"
   }

**Response**:

.. code-block:: json

   {
     "file_id": "uuid-string",
     "filename": "malware_sample.exe",
     "size": 1048576,
     "blake3_hash": "hash_string",
     "upload_timestamp": "2024-01-01T12:00:00Z",
     "status": "uploaded",
     "minio_path": "files/uuid/malware_sample.exe"
   }

Interactive Documentation
-------------------------

- **Swagger UI**: `http://localhost:8000/docs <http://localhost:8000/docs>`_
- **ReDoc**: `http://localhost:8000/redoc <http://localhost:8000/redoc>`_

📁 File Management Endpoints
============================

Upload Malware File
-------------------

.. code-block:: http

   POST /api/v1/file_injection/
   Content-Type: multipart/form-data

**Request:**

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/file_injection/" \
        -F "file=@malware.exe" \
        -F "filename=malware.exe"

**Response:**

.. code-block:: json

   {
     "file_uuid": "550e8400-e29b-41d4-a716-************",
     "filename": "malware.exe",
     "size_bytes": 1024000,
     "blake3_hash": "abc123...",
     "upload_timestamp": "2023-12-01T10:00:00Z",
     "status": "uploaded"
   }

List Files
----------

.. code-block:: http

   GET /api/v1/files/

**Response:**

.. code-block:: json

   {
     "files": [
       {
         "uuid": "550e8400-e29b-41d4-a716-************",
         "filename": "malware.exe",
         "size_bytes": 1024000,
         "upload_timestamp": "2023-12-01T10:00:00Z",
         "analysis_status": "completed"
       }
     ],
     "total": 1,
     "page": 1,
     "per_page": 50
   }

Get File Details
---------------

.. code-block:: http

   GET /api/v1/files/{file_uuid}

**Response:**

.. code-block:: json

   {
     "uuid": "550e8400-e29b-41d4-a716-************",
     "filename": "malware.exe",
     "size_bytes": 1024000,
     "blake3_hash": "abc123...",
     "mime_type": "application/x-executable",
     "upload_timestamp": "2023-12-01T10:00:00Z",
     "metadata": {
       "file_type": "PE32 executable",
       "architecture": "x86_64",
       "compiler": "Microsoft Visual C++"
     }
   }

🖥️ VM Management Endpoints
==========================

Create VM
---------

.. code-block:: http

   POST /api/v1/vms/

**Request:**

.. code-block:: json

   {
     "vm_type": "docker",
     "template": "ubuntu-analysis",
     "resources": {
       "memory_mb": 2048,
       "cpu_cores": 2
     },
     "network_isolation": true
   }

**Response:**

.. code-block:: json

   {
     "vm_id": "vm-550e8400-e29b-41d4-a716-************",
     "status": "provisioning",
     "vm_type": "docker",
     "template": "ubuntu-analysis",
     "created_at": "2023-12-01T10:00:00Z",
     "ip_address": "*************",
     "ssh_port": 2222
   }

List VMs
-------

.. code-block:: http

   GET /api/v1/vms/

**Response:**

.. code-block:: json

   {
     "vms": [
       {
         "vm_id": "vm-550e8400-e29b-41d4-a716-************",
         "status": "running",
         "vm_type": "docker",
         "template": "ubuntu-analysis",
         "created_at": "2023-12-01T10:00:00Z",
         "uptime_seconds": 1800
       }
     ],
     "total": 1
   }

Get VM Status
------------

.. code-block:: http

   GET /api/v1/vms/{vm_id}

**Response:**

.. code-block:: json

   {
     "vm_id": "vm-550e8400-e29b-41d4-a716-************",
     "status": "running",
     "vm_type": "docker",
     "template": "ubuntu-analysis",
     "created_at": "2023-12-01T10:00:00Z",
     "ip_address": "*************",
     "resources": {
       "cpu_usage_percent": 25.5,
       "memory_usage_mb": 1024,
       "disk_usage_mb": 512
     },
     "network": {
       "bytes_sent": 1048576,
       "bytes_received": 2097152,
       "connections_active": 3
     }
   }

Destroy VM
---------

.. code-block:: http

   DELETE /api/v1/vms/{vm_id}

**Response:**

.. code-block:: json

   {
     "vm_id": "vm-550e8400-e29b-41d4-a716-************",
     "status": "destroying",
     "message": "VM destruction initiated"
   }

🔄 VM Operations Endpoints
=========================

Inject File into VM
-------------------

.. code-block:: http

   POST /api/v1/vm_injection/

**Request:**

.. code-block:: json

   {
     "vm_id": "vm-550e8400-e29b-41d4-a716-************",
     "file_uuid": "550e8400-e29b-41d4-a716-************",
     "target_path": "/tmp/malware.exe",
     "execute": true,
     "execution_args": ["--verbose", "--output=/tmp/results"]
   }

**Response:**

.. code-block:: json

   {
     "injection_id": "inj-550e8400-e29b-41d4-a716-************",
     "vm_id": "vm-550e8400-e29b-41d4-a716-************",
     "file_uuid": "550e8400-e29b-41d4-a716-************",
     "status": "injecting",
     "target_path": "/tmp/malware.exe",
     "started_at": "2023-12-01T10:00:00Z"
   }

List VM Files
------------

.. code-block:: http

   GET /api/v1/vm_files/{vm_id}

**Response:**

.. code-block:: json

   {
     "vm_id": "vm-550e8400-e29b-41d4-a716-************",
     "files": [
       {
         "path": "/tmp/malware.exe",
         "size_bytes": 1024000,
         "permissions": "755",
         "modified_at": "2023-12-01T10:00:00Z",
         "file_type": "executable"
       }
     ],
     "total_files": 1
   }

Apply Template
-------------

.. code-block:: http

   POST /api/v1/template_injection/

**Request:**

.. code-block:: json

   {
     "vm_id": "vm-550e8400-e29b-41d4-a716-************",
     "template_name": "malware-analysis-tools",
     "parameters": {
       "install_volatility": true,
       "install_yara": true,
       "custom_scripts": ["/scripts/setup-analysis.sh"]
     }
   }

**Response:**

.. code-block:: json

   {
     "template_id": "tpl-550e8400-e29b-41d4-a716-************",
     "vm_id": "vm-550e8400-e29b-41d4-a716-************",
     "template_name": "malware-analysis-tools",
     "status": "applying",
     "started_at": "2023-12-01T10:00:00Z"
   }

🔌 WebSocket Endpoints
=====================

Real-time VM Metrics
--------------------

.. code-block:: javascript

   // Connect to VM metrics stream
   const ws = new WebSocket('ws://localhost:8000/api/v1/vms/{vm_id}/metrics/stream');
   
   ws.onmessage = function(event) {
     const metrics = JSON.parse(event.data);
     console.log('VM Metrics:', metrics);
   };

**Message Format:**

.. code-block:: json

   {
     "timestamp": "2023-12-01T10:00:00Z",
     "vm_id": "vm-550e8400-e29b-41d4-a716-************",
     "metrics": {
       "cpu_usage_percent": 25.5,
       "memory_usage_mb": 1024,
       "disk_io_read_mb": 10.5,
       "disk_io_write_mb": 5.2,
       "network_bytes_sent": 1048576,
       "network_bytes_received": 2097152
     }
   }

Command Execution Stream
-----------------------

.. code-block:: javascript

   // Connect to command execution stream
   const ws = new WebSocket('ws://localhost:8000/api/v1/vms/{vm_id}/commands/stream');
   
   // Send command
   ws.send(JSON.stringify({
     "command": "ls -la /tmp",
     "timeout_seconds": 30
   }));
   
   ws.onmessage = function(event) {
     const result = JSON.parse(event.data);
     console.log('Command Output:', result);
   };

**Response Format:**

.. code-block:: json

   {
     "command_id": "cmd-550e8400-e29b-41d4-a716-************",
     "command": "ls -la /tmp",
     "status": "completed",
     "exit_code": 0,
     "stdout": "total 8\ndrwxrwxrwt  2 root root 4096 Dec  1 10:00 .\n",
     "stderr": "",
     "execution_time_ms": 150
   }

File Upload Stream
-----------------

.. code-block:: javascript

   // Connect to file upload stream
   const ws = new WebSocket('ws://localhost:8000/api/v1/vms/{vm_id}/files/upload');
   
   // Upload file in chunks
   const fileChunk = new Uint8Array(buffer);
   ws.send(JSON.stringify({
     "chunk_id": 1,
     "total_chunks": 10,
     "filename": "analysis-tool.py",
     "target_path": "/tmp/analysis-tool.py",
     "data": btoa(String.fromCharCode.apply(null, fileChunk))
   }));

🛡️ System Endpoints
===================

Health Check
-----------

.. code-block:: http

   GET /health

**Response:**

.. code-block:: json

   {
     "status": "healthy",
     "timestamp": "2023-12-01T10:00:00Z",
     "version": "1.0.0",
     "services": {
       "database": "healthy",
       "redis": "healthy",
       "minio": "healthy",
       "elasticsearch": "healthy"
     }
   }

System Information
-----------------

.. code-block:: http

   GET /api/v1/system/info

**Response:**

.. code-block:: json

   {
     "version": "1.0.0",
     "build_date": "2023-12-01",
     "api_version": "v1",
     "supported_vm_types": ["docker", "vagrant"],
     "max_concurrent_vms": 10,
     "available_templates": [
       "ubuntu-analysis",
       "windows-10",
       "alpine-minimal"
     ]
   }

📊 Error Responses
=================

Standard Error Format
---------------------

.. code-block:: json

   {
     "error": {
       "code": "VALIDATION_ERROR",
       "message": "Invalid file format",
       "details": {
         "field": "file",
         "reason": "File type not supported"
       },
       "timestamp": "2023-12-01T10:00:00Z",
       "request_id": "req-550e8400-e29b-41d4-a716-************"
     }
   }

Common Error Codes
-----------------

.. list-table:: HTTP Error Codes
   :header-rows: 1
   :widths: 15 25 60

   * - Code
     - Error Type
     - Description
   * - **400**
     - Bad Request
     - Invalid request format or parameters
   * - **404**
     - Not Found
     - Resource (VM, file, etc.) not found
   * - **409**
     - Conflict
     - Resource already exists or state conflict
   * - **422**
     - Validation Error
     - Request validation failed
   * - **429**
     - Rate Limited
     - Too many requests
   * - **500**
     - Internal Error
     - Server-side error
   * - **503**
     - Service Unavailable
     - Service temporarily unavailable

🚀 Next Steps
=============

- **Component Overview**: :doc:`components/api-layer` - Understand the API architecture
- **Installation Guide**: :doc:`quickstart/installation` - Set up your environment
- **Operations Guide**: :doc:`operations/logging-operations` - Monitor and troubleshoot

📚 External Resources
====================

- **FastAPI Documentation**: `https://fastapi.tiangolo.com/ <https://fastapi.tiangolo.com/>`_
- **WebSocket Guide**: `https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API <https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API>`_
- **REST API Best Practices**: `https://restfulapi.net/ <https://restfulapi.net/>`_
