====================
🌐 API Layer Overview
====================

The TurdParty API layer provides the core interface for malware analysis operations, built on modern Python technologies.

.. contents:: Table of Contents
   :local:
   :depth: 2

🏗️ Architecture Overview
========================

API Stack Components
--------------------

.. mermaid::

   graph TB
       subgraph "Client Layer"
           REACT[React Frontend]
           CLI[CLI Tools]
           EXTERNAL[External Integrations]
       end
       
       subgraph "Reverse Proxy"
           TRAEFIK[Traefik<br/>Load Balancer & SSL]
       end
       
       subgraph "API Application"
           FASTAPI[FastAPI Application<br/>Port 8000]
           MIDDLEWARE[Middleware Stack]
           ROUTES[Route Handlers]
       end
       
       subgraph "Background Processing"
           CELERY[Celery Workers]
           REDIS_QUEUE[Redis Task Queue]
       end
       
       subgraph "Data Layer"
           POSTGRES[PostgreSQL<br/>Metadata]
           MINIO[MinIO<br/>File Storage]
           REDIS_CACHE[Redis Cache]
       end
       
       REACT --> TRAEFIK
       CLI --> TRAEFIK
       EXTERNAL --> TRAEFIK
       
       TRAEFIK --> FASTAPI
       FASTAPI --> MIDDLEWARE
       MIDDLEWARE --> ROUTES
       
       ROUTES --> CELERY
       ROUTES --> POSTGRES
       ROUTES --> MINIO
       ROUTES --> REDIS_CACHE
       
       CELERY --> REDIS_QUEUE

🔧 FastAPI Application Structure
===============================

Core Components
---------------

.. mermaid::

   graph LR
       subgraph "FastAPI App"
           APP[Application Factory]
           MIDDLEWARE[Middleware Stack]
           ROUTES[Route Modules]
           MODELS[Pydantic Models]
       end
       
       subgraph "Middleware"
           CORS[CORS Middleware]
           ECS[ECS Logging]
           AUTH[Authentication]
           RATE[Rate Limiting]
       end
       
       subgraph "Route Modules"
           FILE_INJ[file_injection]
           VM_MGMT[vm_management]
           VM_FILES[vm_files]
           TEMPLATES[template_injection]
           FILES[files]
       end
       
       APP --> MIDDLEWARE
       MIDDLEWARE --> CORS
       MIDDLEWARE --> ECS
       MIDDLEWARE --> AUTH
       MIDDLEWARE --> RATE
       
       APP --> ROUTES
       ROUTES --> FILE_INJ
       ROUTES --> VM_MGMT
       ROUTES --> VM_FILES
       ROUTES --> TEMPLATES
       ROUTES --> FILES
       
       ROUTES --> MODELS

📡 API Endpoints Overview
========================

Core Endpoint Categories
------------------------

.. mermaid::

   graph TB
       subgraph "File Management"
           UPLOAD[POST /api/v1/file_injection/<br/>Upload malware files]
           LIST_FILES[GET /api/v1/files/<br/>List uploaded files]
           GET_FILE[GET /api/v1/files/{uuid}<br/>Retrieve file metadata]
       end
       
       subgraph "VM Management"
           CREATE_VM[POST /api/v1/vms/<br/>Create new VM]
           LIST_VMS[GET /api/v1/vms/<br/>List active VMs]
           VM_STATUS[GET /api/v1/vms/{vm_id}<br/>Get VM status]
           DELETE_VM[DELETE /api/v1/vms/{vm_id}<br/>Destroy VM]
       end
       
       subgraph "VM Operations"
           INJECT[POST /api/v1/vm_injection/<br/>Inject file into VM]
           VM_FILES[GET /api/v1/vm_files/{vm_id}<br/>List VM files]
           TEMPLATES[POST /api/v1/template_injection/<br/>Apply templates]
       end
       
       subgraph "WebSocket Streams"
           METRICS[WS /api/v1/vms/{vm_id}/metrics/stream<br/>Real-time metrics]
           COMMANDS[WS /api/v1/vms/{vm_id}/commands/stream<br/>Command execution]
           FILE_OPS[WS /api/v1/vms/{vm_id}/files/upload<br/>File operations]
       end
       
       subgraph "System"
           HEALTH[GET /health<br/>Health check]
           DOCS[GET /docs<br/>Swagger UI]
           REDOC[GET /redoc<br/>ReDoc documentation]
       end

🔄 Request/Response Flow
=======================

Typical Analysis Workflow
-------------------------

.. mermaid::

   sequenceDiagram
       participant Client
       participant FastAPI
       participant Celery
       participant MinIO
       participant VM
       participant ELK
       
       Note over Client,ELK: File Upload & Analysis Flow
       
       Client->>FastAPI: POST /api/v1/file_injection/
       FastAPI->>MinIO: Store file with UUID
       FastAPI->>Celery: Queue analysis task
       FastAPI->>Client: Return task_id & file_uuid
       
       Note over Celery,VM: Background Processing
       
       Celery->>MinIO: Download file
       Celery->>VM: Provision VM instance
       Celery->>VM: Inject file into VM
       
       Note over VM,ELK: Runtime Monitoring
       
       VM->>ELK: Stream execution logs
       VM->>ELK: Stream system metrics
       VM->>ELK: Stream network activity
       
       Note over Client,ELK: Status Monitoring
       
       Client->>FastAPI: GET /api/v1/vms/{vm_id}
       FastAPI->>Client: Return VM status
       
       Client->>FastAPI: WS /api/v1/vms/{vm_id}/metrics/stream
       FastAPI->>Client: Real-time metrics stream
       
       Note over Celery,VM: Cleanup
       
       Celery->>VM: Destroy VM (30min timeout)
       Celery->>FastAPI: Update task status

🛡️ Security & Authentication
============================

Security Layers
---------------

.. mermaid::

   graph TB
       subgraph "External Security"
           TRAEFIK_AUTH[Traefik Authentication<br/>JWT/OAuth2]
           SSL[SSL/TLS Termination]
       end
       
       subgraph "Application Security"
           CORS_POLICY[CORS Policy]
           RATE_LIMIT[Rate Limiting]
           INPUT_VAL[Input Validation]
           PYDANTIC[Pydantic Models]
       end
       
       subgraph "Data Security"
           FILE_SCAN[File Type Validation]
           UUID_AUTH[UUID-based Access]
           ISOLATION[VM Isolation]
       end
       
       TRAEFIK_AUTH --> CORS_POLICY
       SSL --> CORS_POLICY
       CORS_POLICY --> RATE_LIMIT
       RATE_LIMIT --> INPUT_VAL
       INPUT_VAL --> PYDANTIC
       
       PYDANTIC --> FILE_SCAN
       FILE_SCAN --> UUID_AUTH
       UUID_AUTH --> ISOLATION

📊 Monitoring & Logging
=======================

ECS Logging Integration
----------------------

.. mermaid::

   graph LR
       subgraph "API Events"
           REQUEST[HTTP Requests]
           RESPONSE[HTTP Responses]
           ERRORS[Error Events]
           METRICS[Performance Metrics]
       end
       
       subgraph "ECS Middleware"
           LOGGER[ECS Logger]
           FORMATTER[ECS Formatter]
           ENRICHER[Context Enricher]
       end
       
       subgraph "ELK Stack"
           LOGSTASH[Logstash]
           ELASTIC[Elasticsearch]
           KIBANA[Kibana]
       end
       
       REQUEST --> LOGGER
       RESPONSE --> LOGGER
       ERRORS --> LOGGER
       METRICS --> LOGGER
       
       LOGGER --> FORMATTER
       FORMATTER --> ENRICHER
       ENRICHER --> LOGSTASH
       
       LOGSTASH --> ELASTIC
       ELASTIC --> KIBANA

🔗 Related Documentation
========================

- **API Reference**: :doc:`../../api/overview`
- **WebSocket Features**: :doc:`../../websocket/overview`
- **VM Management**: :doc:`../../vm/overview`
- **Testing Guide**: :doc:`../development/testing-guide`

🚀 Next Steps
=============

Now that you understand the API layer, explore:

1. **Storage Systems**: :doc:`storage-systems` - Learn about MinIO, PostgreSQL, and Redis
2. **VM Management**: :doc:`vm-management` - Understand VM provisioning and lifecycle
3. **Quick Start**: :doc:`../quickstart/installation` - Get hands-on with the platform

📚 External Resources
====================

- `FastAPI Documentation <https://fastapi.tiangolo.com/>`_
- `Pydantic Models <https://docs.pydantic.dev/>`_
- `Celery Documentation <https://docs.celeryproject.org/>`_
- `Traefik Proxy <https://doc.traefik.io/traefik/>`_
