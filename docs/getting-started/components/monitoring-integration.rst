=======================================
📊 Unified Monitoring & Status Dashboard
=======================================

💩🎉 TurdParty provides a comprehensive monitoring solution that combines real-time status monitoring with automated maintenance tasks through a unified dashboard approach.

.. contents:: Table of Contents
   :local:
   :depth: 3

🎯 Overview
===========

The monitoring system integrates two key components:

1. **Status Dashboard** - Real-time visual monitoring of all system components
2. **Celery Beat Automation** - Scheduled maintenance tasks and health monitoring

This unified approach eliminates redundancy while providing comprehensive observability and automated system maintenance.

🌐 Status Dashboard
==================

Access Points
-------------

**Primary Access (Traefik)**:
- **http://status.turdparty.localhost** - Main status dashboard

**Direct Access**:
- **http://localhost:8090** - Direct port access

Dashboard Features
------------------

**System Components Monitored**:
- ✅ **API Service** - FastAPI health and response times
- ✅ **Database** - PostgreSQL connection and performance
- ✅ **Cache** - Redis availability and memory usage
- ✅ **Storage** - MinIO object storage health
- ✅ **ELK Stack** - Elasticsearch, Logstash, Kibana status
- ✅ **Celery Workers** - **NEW** Worker availability and task metrics
- ✅ **Scheduled Tasks** - **NEW** Task success rates and execution status

**Real-time Metrics**:
- 📈 **Success Rate** - Celery task success percentage (color-coded)
- 📊 **Total Tasks** - 24-hour execution count
- ❌ **Failed Tasks** - Failure tracking and alerting
- 👷 **Active Workers** - Worker availability status

**Visual Indicators**:
- 🟢 **Operational** - Service running normally
- 🟡 **Degraded** - Service experiencing issues
- 🔴 **Outage** - Service unavailable

Architecture Diagram
--------------------

.. mermaid::

   graph TB
       subgraph "Status Dashboard"
           DASH[Visual Dashboard<br/>status.turdparty.localhost]
           HEALTH[Health Checks<br/>Every 30s]
           VISUAL[Status Indicators]
       end
       
       subgraph "System Services"
           API[FastAPI]
           DB[PostgreSQL]
           REDIS[Redis]
           MINIO[MinIO]
           ELK[ELK Stack]
       end
       
       subgraph "Celery Integration"
           WORKERS[Celery Workers]
           TASKS[Scheduled Tasks]
           METRICS[Task Metrics]
       end
       
       HEALTH --> API
       HEALTH --> DB
       HEALTH --> REDIS
       HEALTH --> MINIO
       HEALTH --> ELK
       
       METRICS --> WORKERS
       METRICS --> TASKS
       METRICS --> DASH
       
       DASH --> VISUAL

🤖 Automated Maintenance Tasks
==============================

Celery Beat Schedule
-------------------

**Maintenance Tasks (Queue: maintenance)**:

.. list-table:: Scheduled Maintenance Tasks
   :header-rows: 1
   :widths: 30 20 50

   * - Task
     - Frequency
     - Description
   * - **VM Pool Health Check**
     - Every 5 minutes
     - Monitor VM health, trigger corrective actions
   * - **Resource Cleanup**
     - Every hour
     - Clean orphaned VMs, expired files, stale workflows
   * - **ELK Index Management**
     - Daily at 2 AM UTC
     - Rotate, optimize, and cleanup Elasticsearch indices
   * - **Cachet Metrics Update**
     - Every 10 minutes
     - Feed Celery metrics to status dashboard

**Monitoring Tasks (Queue: monitoring)**:

.. list-table:: Monitoring Tasks
   :header-rows: 1
   :widths: 30 20 50

   * - Task
     - Frequency
     - Description
   * - **Celery Worker Monitoring**
     - Every 15 minutes
     - Monitor worker health, generate alerts
   * - **Task Metrics Collection**
     - Every 10 minutes
     - Collect execution statistics and performance data

Task Features
-------------

**Error Handling**:
- ✅ **Automatic Retries** - Exponential backoff for failed tasks
- ✅ **Structured Logging** - JSON logs with correlation IDs
- ✅ **Alert Generation** - Intelligent alerting for failures
- ✅ **Performance Tracking** - Duration and success rate monitoring

**Self-Healing Capabilities**:
- 🔧 **VM Health Issues** - Automatic VM restart/replacement
- 🧹 **Resource Cleanup** - Automated orphaned resource removal
- 📊 **ELK Optimization** - Index rotation and performance tuning
- ⚠️ **Worker Failures** - Alert generation for worker issues

🔧 Configuration
================

Environment Variables
---------------------

**Status Dashboard**:

.. code-block:: bash

   # Status service configuration
   STATUS_PORT=8090
   STATUS_HOST=status.turdparty.localhost
   
   # Health check intervals
   HEALTH_CHECK_INTERVAL=30s
   CRITICAL_CHECK_INTERVAL=10s

**Celery Beat Configuration**:

.. code-block:: bash

   # Celery Beat settings
   CELERY_BEAT_SCHEDULE_FILENAME=celerybeat-schedule
   CELERY_BEAT_MAX_LOOP_INTERVAL=300
   
   # Task configuration
   CELERY_TASK_TIME_LIMIT=1800  # 30 minutes
   CELERY_TASK_SOFT_TIME_LIMIT=1500  # 25 minutes
   
   # Queue configuration
   CELERY_TASK_ROUTES_MAINTENANCE=maintenance
   CELERY_TASK_ROUTES_MONITORING=monitoring

Docker Compose Integration
--------------------------

**Status Service**:

.. code-block:: yaml

   status:
     container_name: turdpartycollab_status
     build:
       context: ../services/status
     ports:
       - "8090:80"
     networks:
       - turdpartycollab_network
       - traefik_network
     labels:
       - "traefik.enable=true"
       - "traefik.http.routers.turdparty-status.rule=Host(`status.turdparty.localhost`)"

**Celery Beat Service**:

.. code-block:: yaml

   celery-beat:
     build: ./services/workers
     command: celery -A celery_app beat --loglevel=info
     environment:
       - REDIS_HOST=cache
       - LOG_LEVEL=INFO
     depends_on:
       - cache
       - api

📈 Monitoring APIs
==================

Health Endpoints
---------------

**Celery Worker Status**:

.. code-block:: http

   GET /api/v1/health/celery
   
   Response:
   {
     "status": "operational",
     "active_workers": 3,
     "total_workers": 3,
     "active_tasks": 5,
     "queues": {...},
     "last_check": "2024-01-15T10:30:00Z"
   }

**Task Metrics**:

.. code-block:: http

   GET /api/v1/health/tasks
   
   Response:
   {
     "status": "operational",
     "success_rate": 95.2,
     "total_executions": 1247,
     "successful_executions": 1187,
     "failed_executions": 60,
     "average_duration": 28.5,
     "last_updated": "2024-01-15T10:30:00Z"
   }

**Detailed Status**:

.. code-block:: http

   GET /api/v1/health/celery/detailed
   
   Response:
   {
     "timestamp": "2024-01-15T10:30:00Z",
     "workers": {...},
     "queues": {...},
     "tasks": {...},
     "system": {
       "healthy": true,
       "worker_count": 3,
       "active_task_count": 5,
       "status": "operational"
     }
   }

🚀 Getting Started
==================

Quick Setup
-----------

1. **Start the Status Dashboard**:

   .. code-block:: bash

      # Start status service
      nix-shell --run "docker compose -f compose/docker-compose.yml up -d status"
      
      # Access dashboard
      open http://status.turdparty.localhost

2. **Start Celery Beat**:

   .. code-block:: bash

      # Start Celery Beat scheduler
      nix-shell --run "docker compose -f compose/docker-compose.yml up -d celery-beat"
      
      # Start Celery workers
      nix-shell --run "docker compose -f compose/docker-compose.yml up -d celery-worker"

3. **Verify Integration**:

   .. code-block:: bash

      # Check Celery worker status
      curl http://localhost:8000/api/v1/health/celery
      
      # Check task metrics
      curl http://localhost:8000/api/v1/health/tasks

Monitoring Commands
------------------

.. code-block:: bash

   # Check beat scheduler status
   celery -A celery_app inspect scheduled
   
   # Monitor task execution
   celery -A celery_app events
   
   # View worker statistics
   celery -A celery_app inspect stats
   
   # Check active tasks
   celery -A celery_app inspect active

🎯 Benefits
===========

**Operational Excellence**:
- ✅ **Unified Monitoring** - Single dashboard for all system components
- ✅ **Automated Maintenance** - Reduces manual intervention by 80%
- ✅ **Proactive Monitoring** - Issues detected before user impact
- ✅ **Self-Healing** - Automatic corrective actions for common problems

**Enhanced Observability**:
- 📊 **Real-time Metrics** - Live system performance visibility
- 🔍 **Structured Logging** - JSON logs with correlation IDs
- 📈 **Performance Tracking** - Task execution and success rate monitoring
- 🚨 **Intelligent Alerting** - Context-aware alert generation

**Reduced Complexity**:
- 🎯 **Single Source of Truth** - Unified status dashboard
- 🔄 **Eliminated Redundancy** - No duplicate monitoring systems
- 🛠️ **Simplified Operations** - Clear separation of concerns
- 📋 **Consistent Interface** - Unified monitoring experience

This monitoring integration provides 💩🎉 TurdParty with enterprise-grade observability and automated maintenance capabilities while maintaining operational simplicity.
