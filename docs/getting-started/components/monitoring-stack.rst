===============================
📊 Monitoring & Logging Stack
===============================

TurdParty implements a comprehensive ELK (Elasticsearch, Logstash, Kibana) stack with Filebeat for centralized logging, monitoring, and observability across all Docker services.

.. contents:: Table of Contents
   :local:
   :depth: 2

🏗️ ELK Stack Architecture
=========================

Complete Logging Pipeline
-------------------------

.. mermaid::

   graph TB
       subgraph "Docker Containers"
           API[API Service<br/>FastAPI Backend]
           FRONTEND[Frontend Service<br/>React Application]
           DOCS[Documentation Service<br/>Nginx Static Files]
           STATUS[Status Service<br/>System Dashboard]
           VM[VM Monitor Service<br/>Resource Tracking]
       end
       
       subgraph "Log Collection Layer"
           FILEBEAT[Filebeat<br/>Container Log Collector]
           DOCKER_LOGS[Docker JSON Logs<br/>/var/lib/docker/containers]
           FILE_LOGS[Application Log Files<br/>/app/logs/*.log]
       end
       
       subgraph "Log Processing Layer"
           LOGSTASH[Logstash<br/>Log Processor & Enricher]
           TCP_INPUT[TCP Input :5000<br/>Direct App Logs]
           BEATS_INPUT[Beats Input :5044<br/>Container Logs]
           FILE_INPUT[File Input<br/>Log File Monitoring]
       end
       
       subgraph "Storage & Visualization"
           ELASTICSEARCH[Elasticsearch<br/>Log Storage & Search]
           KIBANA[Kibana Dashboard<br/>kibana.turdparty.localhost]
           INDICES[Dynamic Indices<br/>Service-based Organization]
       end
       
       API --> DOCKER_LOGS
       FRONTEND --> DOCKER_LOGS
       DOCS --> DOCKER_LOGS
       STATUS --> DOCKER_LOGS
       VM --> DOCKER_LOGS
       
       DOCKER_LOGS --> FILEBEAT
       FILE_LOGS --> FILE_INPUT
       
       FILEBEAT --> BEATS_INPUT
       TCP_INPUT --> LOGSTASH
       BEATS_INPUT --> LOGSTASH
       FILE_INPUT --> LOGSTASH
       
       LOGSTASH --> ELASTICSEARCH
       ELASTICSEARCH --> INDICES
       ELASTICSEARCH --> KIBANA

📋 Service Logging Configuration
================================

Docker Container Logging
------------------------

Every Docker service is configured with structured logging:

.. code-block:: yaml

   # Example service logging configuration
   api:
     container_name: turdpartycollab_api
     # ... other configuration
     logging:
       driver: "json-file"
       options:
         max-size: "10m"
         max-file: "3"
         labels: "service=api,component=backend,environment=development"

Service Logging Matrix
---------------------

.. list-table:: Service Logging Configuration
   :header-rows: 1
   :widths: 20 20 25 35

   * - Service
     - Component
     - Index Pattern
     - Description
   * - **API**
     - `backend`
     - `turdparty-docker-api-*`
     - FastAPI application logs
   * - **Frontend**
     - `ui`
     - `turdparty-docker-frontend-*`
     - React application and nginx logs
   * - **Elasticsearch**
     - `storage`
     - `turdparty-docker-elasticsearch-*`
     - Database operation logs
   * - **Logstash**
     - `logging`
     - `turdparty-docker-logstash-*`
     - Log processing pipeline logs
   * - **Kibana**
     - `visualization`
     - `turdparty-docker-kibana-*`
     - Dashboard and query logs
   * - **VM Monitor**
     - `monitoring`
     - `turdparty-docker-vm-monitor-*`
     - Resource monitoring logs
   * - **Documentation**
     - `documentation`
     - `turdparty-docker-docs-*`
     - Nginx documentation server logs
   * - **Status**
     - `dashboard`
     - `turdparty-docker-status-*`
     - System status dashboard logs
   * - **Filebeat**
     - `log-collection`
     - `turdparty-docker-filebeat-*`
     - Log collection agent logs

🔄 Log Processing Pipeline
=========================

Filebeat Collection
-------------------

Filebeat automatically collects logs from all Docker containers:

.. code-block:: yaml

   # Filebeat input configuration
   filebeat.inputs:
   - type: container
     paths:
       - '/var/lib/docker/containers/*/*.log'
     
     # Parse Docker JSON logs
     json.keys_under_root: true
     json.add_error_key: true
     json.message_key: log
     
     # Add container metadata
     processors:
       - add_docker_metadata:
           host: "unix:///var/run/docker.sock"

Logstash Processing
------------------

Logstash enriches and processes logs with service metadata:

.. code-block:: ruby

   # Logstash filter for Docker logs
   filter {
     # Process Docker container logs from Filebeat
     if "docker_logs" in [tags] {
       # Parse Docker JSON log format
       if [message] {
         json {
           source => "message"
           target => "docker"
         }
       }
       
       # Extract container information
       if [container][name] {
         mutate {
           add_field => { "service_name" => "%{[container][name]}" }
           add_field => { "container_id" => "%{[container][id]}" }
           add_field => { "container_image" => "%{[container][image][name]}" }
         }
         
         # Clean up service name (remove turdpartycollab_ prefix)
         mutate {
           gsub => [ "service_name", "turdpartycollab_", "" ]
         }
       }
     }
   }

Elasticsearch Indexing
----------------------

Dynamic index creation based on service and date:

.. code-block:: ruby

   # Elasticsearch output with dynamic indexing
   output {
     elasticsearch {
       hosts => ["elasticsearch:9200"]
       
       # Dynamic index based on log source and service
       index => "turdparty-%{[service_name]:unknown}-%{+YYYY.MM.dd}"
       
       # Use different index for Docker logs
       if "docker_logs" in [tags] {
         index => "turdparty-docker-%{[service_name]:unknown}-%{+YYYY.MM.dd}"
       }
     }
   }

📈 Index Strategy & Organization
===============================

Index Patterns
--------------

.. mermaid::

   graph TB
       subgraph "Index Categories"
           DOCKER[Docker Container Logs<br/>turdparty-docker-*]
           APP[Application Logs<br/>turdparty-*]
           INJECTION[File Injection Logs<br/>turdparty-file_injection-*]
           INSTALL[Installation Logs<br/>turdparty-installation-*]
       end
       
       subgraph "Service-Specific Indices"
           API_IDX[turdparty-docker-api-YYYY.MM.dd]
           FRONTEND_IDX[turdparty-docker-frontend-YYYY.MM.dd]
           DOCS_IDX[turdparty-docker-docs-YYYY.MM.dd]
           VM_IDX[turdparty-docker-vm-monitor-YYYY.MM.dd]
       end
       
       subgraph "Time-Based Organization"
           DAILY[Daily Rotation<br/>YYYY.MM.dd]
           RETENTION[Retention Policy<br/>Configurable]
           CLEANUP[Automatic Cleanup<br/>Based on Age/Size]
       end
       
       DOCKER --> API_IDX
       DOCKER --> FRONTEND_IDX
       DOCKER --> DOCS_IDX
       DOCKER --> VM_IDX
       
       API_IDX --> DAILY
       FRONTEND_IDX --> DAILY
       DOCS_IDX --> DAILY
       VM_IDX --> DAILY
       
       DAILY --> RETENTION
       RETENTION --> CLEANUP

Index Field Mapping
-------------------

.. code-block:: json

   {
     "mappings": {
       "properties": {
         "@timestamp": { "type": "date" },
         "service_name": { "type": "keyword" },
         "container_id": { "type": "keyword" },
         "container_image": { "type": "keyword" },
         "log_source": { "type": "keyword" },
         "platform": { "type": "keyword" },
         "message": { "type": "text" },
         "level": { "type": "keyword" },
         "event_type": { "type": "keyword" },
         "event_category": { "type": "keyword" },
         "container": {
           "properties": {
             "name": { "type": "keyword" },
             "id": { "type": "keyword" },
             "image": {
               "properties": {
                 "name": { "type": "keyword" },
                 "tag": { "type": "keyword" }
               }
             }
           }
         }
       }
     }
   }

🔍 Kibana Dashboard Access
==========================

Dashboard Configuration
-----------------------

.. mermaid::

   graph TB
       subgraph "Kibana Interface"
           DISCOVER[Discover<br/>Log Search & Filter]
           VISUALIZE[Visualizations<br/>Charts & Graphs]
           DASHBOARD[Dashboards<br/>Service Overview]
           ALERTS[Alerts<br/>Monitoring Rules]
       end
       
       subgraph "Index Patterns"
           PATTERN1[turdparty-*<br/>All Logs]
           PATTERN2[turdparty-docker-*<br/>Container Logs]
           PATTERN3[turdparty-docker-api-*<br/>API Logs Only]
           PATTERN4[turdparty-file_injection-*<br/>Injection Logs]
       end
       
       subgraph "Common Queries"
           ERROR_LOGS[Error Log Analysis]
           SERVICE_HEALTH[Service Health Monitoring]
           PERFORMANCE[Performance Metrics]
           SECURITY[Security Event Tracking]
       end
       
       DISCOVER --> PATTERN1
       DISCOVER --> PATTERN2
       VISUALIZE --> PATTERN3
       DASHBOARD --> PATTERN4
       
       DISCOVER --> ERROR_LOGS
       VISUALIZE --> SERVICE_HEALTH
       DASHBOARD --> PERFORMANCE
       ALERTS --> SECURITY

Access URLs
----------

- **Kibana Dashboard**: `http://kibana.turdparty.localhost`
- **Elasticsearch API**: `http://localhost:9200`
- **Index Overview**: `http://localhost:9200/_cat/indices/turdparty-*?v`
- **Cluster Health**: `http://localhost:9200/_cluster/health`

🛠️ Management & Operations
==========================

Service Management
-----------------

.. code-block:: bash

   # Restart all services with logging
   ./scripts/restart-with-logging.sh
   
   # Check service status
   docker-compose ps
   
   # View specific service logs
   docker-compose logs api
   docker-compose logs filebeat
   docker-compose logs logstash

Log Monitoring Commands
----------------------

.. code-block:: bash

   # Check Elasticsearch indices
   curl "http://localhost:9200/_cat/indices/turdparty-*?v"
   
   # View index statistics
   curl "http://localhost:9200/turdparty-docker-api-*/_stats"
   
   # Search recent logs
   curl -X GET "localhost:9200/turdparty-*/_search" -H 'Content-Type: application/json' -d'
   {
     "query": {
       "range": {
         "@timestamp": {
           "gte": "now-1h"
         }
       }
     },
     "sort": [
       {
         "@timestamp": {
           "order": "desc"
         }
       }
     ],
     "size": 10
   }'

Performance Optimization
-----------------------

.. list-table:: Performance Settings
   :header-rows: 1
   :widths: 25 25 50

   * - Component
     - Setting
     - Purpose
   * - **Docker Logging**
     - `max-size: 10m, max-file: 3`
     - Prevent disk space exhaustion
   * - **Filebeat**
     - `queue.mem.events: 4096`
     - Buffer events for batch processing
   * - **Logstash**
     - `pipeline.batch.size: 125`
     - Optimize throughput
   * - **Elasticsearch**
     - `refresh_interval: 5s`
     - Balance search vs indexing performance

🚀 Next Steps
=============

Explore related components:

1. **API Layer**: :doc:`api-layer` - Learn about application logging
2. **VM Management**: :doc:`vm-management` - Understand VM monitoring
3. **Storage Systems**: :doc:`storage-systems` - Database and file storage

📚 External Resources
====================

- `Elasticsearch Documentation <https://www.elastic.co/guide/en/elasticsearch/reference/current/index.html>`_
- `Logstash Configuration <https://www.elastic.co/guide/en/logstash/current/configuration.html>`_
- `Kibana User Guide <https://www.elastic.co/guide/en/kibana/current/index.html>`_
- `Filebeat Reference <https://www.elastic.co/guide/en/beats/filebeat/current/index.html>`_
