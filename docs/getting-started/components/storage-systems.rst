========================
💾 Storage Systems Overview
========================

TurdParty uses a multi-tier storage architecture designed for scalability, performance, and data integrity in malware analysis workflows.

.. contents:: Table of Contents
   :local:
   :depth: 2

🏗️ Storage Architecture
=======================

Multi-Tier Storage Design
-------------------------

.. mermaid::

   graph TB
       subgraph "Application Layer"
           API[FastAPI Application]
           WORKER[Celery Workers]
           VM[VM Instances]
       end
       
       subgraph "Object Storage"
           MINIO[MinIO S3-Compatible<br/>File Repository]
           BUCKETS[Storage Buckets]
       end
       
       subgraph "Relational Database"
           POSTGRES[PostgreSQL<br/>Metadata & State]
           TABLES[Database Tables]
       end
       
       subgraph "Cache & Queue"
           REDIS[Redis<br/>Cache & Task Queue]
           CACHE[Application Cache]
           QUEUE[Celery Task Queue]
       end
       
       subgraph "Log Storage"
           ELASTIC[Elasticsearch<br/>Log & Analytics Data]
           INDICES[ECS Indices]
       end
       
       API --> MINIO
       API --> POSTGRES
       API --> REDIS
       API --> ELASTIC
       
       WORKER --> MINIO
       WORKER --> POSTGRES
       WORKER --> REDIS
       
       VM --> ELASTIC
       
       MINIO --> BUCKETS
       POSTGRES --> TABLES
       REDIS --> CACHE
       REDIS --> QUEUE
       ELASTIC --> INDICES

🗄️ MinIO Object Storage
=======================

S3-Compatible File Repository
-----------------------------

.. mermaid::

   graph LR
       subgraph "MinIO Buckets"
           MALWARE[malware-samples<br/>Original Files]
           RESULTS[analysis-results<br/>VM Outputs]
           TEMPLATES[vm-templates<br/>VM Images]
           LOGS[vm-logs<br/>Execution Logs]
       end
       
       subgraph "File Operations"
           UPLOAD[File Upload<br/>Blake3 Hashing]
           DOWNLOAD[File Download<br/>UUID Access]
           METADATA[Metadata Extraction<br/>File Analysis]
           CLEANUP[Automated Cleanup<br/>Retention Policy]
       end
       
       subgraph "Access Control"
           UUID_AUTH[UUID-based Access]
           PRESIGNED[Presigned URLs]
           BUCKET_POLICY[Bucket Policies]
       end
       
       UPLOAD --> MALWARE
       DOWNLOAD --> MALWARE
       METADATA --> MALWARE
       CLEANUP --> MALWARE
       
       UPLOAD --> RESULTS
       DOWNLOAD --> RESULTS
       
       UUID_AUTH --> PRESIGNED
       PRESIGNED --> BUCKET_POLICY

MinIO Configuration
------------------

.. code-block:: yaml

   # docker-compose.yml MinIO service
   minio:
     image: minio/minio:latest
     environment:
       MINIO_ROOT_USER: turdparty
       MINIO_ROOT_PASSWORD: ${MINIO_PASSWORD}
     volumes:
       - minio_data:/data
     command: server /data --console-address ":9001"
     networks:
       - turdpartycollab_net

Bucket Structure
---------------

.. mermaid::

   graph TB
       subgraph "malware-samples"
           MS_STRUCTURE["{uuid}/original<br/>{uuid}/metadata.json<br/>{uuid}/analysis.json"]
       end
       
       subgraph "analysis-results"
           AR_STRUCTURE["{uuid}/{vm_id}/screenshots/<br/>{uuid}/{vm_id}/network/<br/>{uuid}/{vm_id}/filesystem/"]
       end
       
       subgraph "vm-templates"
           VT_STRUCTURE["windows-10/{version}/<br/>ubuntu-20.04/{version}/<br/>custom-templates/"]
       end
       
       subgraph "vm-logs"
           VL_STRUCTURE["{vm_id}/system.log<br/>{vm_id}/application.log<br/>{vm_id}/security.log"]
       end

🐘 PostgreSQL Database
======================

Relational Data Management
--------------------------

.. mermaid::

   graph TB
       subgraph "Core Tables"
           FILES[files<br/>File metadata & hashes]
           VMS[vms<br/>VM instances & state]
           TASKS[tasks<br/>Analysis task tracking]
           TEMPLATES[templates<br/>VM template definitions]
       end
       
       subgraph "Relationship Tables"
           FILE_VM[file_vm_associations<br/>File-to-VM mappings]
           TASK_FILES[task_file_relations<br/>Task-to-File mappings]
           VM_METRICS[vm_metrics<br/>Performance snapshots]
       end
       
       subgraph "Audit Tables"
           AUDIT_LOG[audit_log<br/>System events]
           USER_ACTIONS[user_actions<br/>API call tracking]
           SYSTEM_EVENTS[system_events<br/>Infrastructure events]
       end
       
       FILES --> FILE_VM
       VMS --> FILE_VM
       TASKS --> TASK_FILES
       FILES --> TASK_FILES
       VMS --> VM_METRICS
       
       FILES --> AUDIT_LOG
       VMS --> AUDIT_LOG
       TASKS --> AUDIT_LOG

Database Schema Overview
-----------------------

.. mermaid::

   erDiagram
       FILES {
           uuid id PK
           string filename
           string blake3_hash
           bigint size_bytes
           string mime_type
           timestamp uploaded_at
           json metadata
       }
       
       VMS {
           uuid id PK
           string name
           string status
           string vm_type
           json configuration
           timestamp created_at
           timestamp destroyed_at
       }
       
       TASKS {
           uuid id PK
           uuid file_id FK
           uuid vm_id FK
           string status
           json parameters
           timestamp started_at
           timestamp completed_at
       }
       
       TEMPLATES {
           uuid id PK
           string name
           string os_type
           string version
           json configuration
           boolean active
       }
       
       FILES ||--o{ TASKS : "analyzed_in"
       VMS ||--o{ TASKS : "executes"
       TEMPLATES ||--o{ VMS : "instantiated_from"

⚡ Redis Cache & Queue
=====================

High-Performance Caching
------------------------

.. mermaid::

   graph TB
       subgraph "Redis Databases"
           DB0[Database 0<br/>Application Cache]
           DB1[Database 1<br/>Celery Task Queue]
           DB2[Database 2<br/>Session Storage]
           DB3[Database 3<br/>Rate Limiting]
       end
       
       subgraph "Cache Patterns"
           LRU[LRU Eviction<br/>Automatic Cleanup]
           TTL[TTL Expiration<br/>Time-based Cleanup]
           PIPELINE[Pipeline Operations<br/>Batch Processing]
       end
       
       subgraph "Queue Operations"
           PRODUCER[Task Producers<br/>API Endpoints]
           CONSUMER[Task Consumers<br/>Celery Workers]
           MONITOR[Queue Monitoring<br/>Flower Dashboard]
       end
       
       DB0 --> LRU
       DB0 --> TTL
       DB1 --> PRODUCER
       DB1 --> CONSUMER
       DB1 --> MONITOR
       DB2 --> TTL
       DB3 --> PIPELINE

Cache Strategy
-------------

.. mermaid::

   sequenceDiagram
       participant API
       participant Redis
       participant Database
       participant MinIO
       
       Note over API,MinIO: Cache-First Strategy
       
       API->>Redis: Check cache for data
       alt Cache Hit
           Redis->>API: Return cached data
       else Cache Miss
           API->>Database: Query database
           Database->>API: Return data
           API->>Redis: Store in cache (TTL)
           Redis->>API: Confirm storage
       end
       
       Note over API,MinIO: File Metadata Caching
       
       API->>Redis: Check file metadata
       alt Cache Miss
           API->>MinIO: Get file metadata
           MinIO->>API: Return metadata
           API->>Redis: Cache metadata (1h TTL)
       end

🔍 Elasticsearch Analytics
==========================

Log & Analytics Storage
----------------------

.. mermaid::

   graph TB
       subgraph "ECS Indices"
           INSTALL[install-time-logs<br/>VM Setup Events]
           RUNTIME[runtime-logs<br/>Execution Events]
           API_LOGS[api-logs<br/>HTTP Requests]
           METRICS[vm-metrics<br/>Performance Data]
       end
       
       subgraph "Data Pipeline"
           LOGSTASH[Logstash<br/>Log Processing]
           BEATS[Filebeat<br/>Log Shipping]
           ENRICHMENT[Data Enrichment<br/>GeoIP, User-Agent]
       end
       
       subgraph "Analytics"
           KIBANA[Kibana<br/>Visualisation]
           DASHBOARDS[Custom Dashboards<br/>Analysis Views]
           ALERTS[Alerting<br/>Anomaly Detection]
       end
       
       BEATS --> LOGSTASH
       LOGSTASH --> INSTALL
       LOGSTASH --> RUNTIME
       LOGSTASH --> API_LOGS
       LOGSTASH --> METRICS
       
       INSTALL --> KIBANA
       RUNTIME --> KIBANA
       API_LOGS --> KIBANA
       METRICS --> KIBANA
       
       KIBANA --> DASHBOARDS
       KIBANA --> ALERTS

Index Mapping Strategy
---------------------

.. code-block:: json

   {
     "install-time-logs": {
       "mappings": {
         "properties": {
           "@timestamp": {"type": "date"},
           "vm.id": {"type": "keyword"},
           "file.uuid": {"type": "keyword"},
           "event.action": {"type": "keyword"},
           "process.name": {"type": "keyword"},
           "network.bytes": {"type": "long"}
         }
       }
     },
     "runtime-logs": {
       "mappings": {
         "properties": {
           "@timestamp": {"type": "date"},
           "vm.id": {"type": "keyword"},
           "file.uuid": {"type": "keyword"},
           "malware.family": {"type": "keyword"},
           "threat.indicator": {"type": "keyword"}
         }
       }
     }
   }

🔄 Data Flow Patterns
====================

Complete Data Lifecycle
-----------------------

.. mermaid::

   sequenceDiagram
       participant User
       participant API
       participant MinIO
       participant PostgreSQL
       participant Redis
       participant Elasticsearch
       
       Note over User,Elasticsearch: File Upload Flow
       
       User->>API: Upload malware file
       API->>MinIO: Store file with UUID
       API->>PostgreSQL: Insert file metadata
       API->>Redis: Cache file info
       API->>Elasticsearch: Log upload event
       
       Note over User,Elasticsearch: Analysis Flow
       
       API->>Redis: Queue analysis task
       API->>PostgreSQL: Create task record
       
       Note over User,Elasticsearch: VM Execution
       
       API->>PostgreSQL: Create VM record
       API->>MinIO: Download file to VM
       API->>Elasticsearch: Stream VM logs
       API->>Redis: Update task status
       
       Note over User,Elasticsearch: Cleanup Flow
       
       API->>PostgreSQL: Mark VM destroyed
       API->>Redis: Clear cache entries
       API->>Elasticsearch: Log completion

🚀 Next Steps
=============

Continue your journey:

1. **VM Management**: :doc:`vm-management` - Learn about VM provisioning and lifecycle
2. **Monitoring Stack**: :doc:`monitoring-stack` - Understand logging and analytics
3. **Installation Guide**: :doc:`../quickstart/installation` - Set up your environment

📚 External Resources
====================

- `MinIO Documentation <https://docs.min.io/>`_
- `PostgreSQL Documentation <https://www.postgresql.org/docs/>`_
- `Redis Documentation <https://redis.io/documentation>`_
- `Elasticsearch Guide <https://www.elastic.co/guide/>`_
