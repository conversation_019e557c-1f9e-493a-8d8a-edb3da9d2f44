==========================
🖥️ VM Management Overview
==========================

TurdParty's VM management system provides secure, isolated environments for malware analysis using both Vagrant and Docker-based virtual machines.

.. contents:: Table of Contents
   :local:
   :depth: 2

🏗️ VM Architecture
==================

Dual VM Strategy
---------------

.. mermaid::

   graph TB
       subgraph "VM Management Layer"
           GRPC[gRPC Service<br/>Port 40000]
           POOL[VM Pool Manager<br/>Resource Allocation]
           LIFECYCLE[VM Lifecycle<br/>Create/Destroy/Monitor]
       end
       
       subgraph "Vagrant VMs"
           VAGRANT_LINUX[Ubuntu 20.04<br/>Full Linux Analysis]
           VAGRANT_WIN[Windows 10<br/>Full Windows Analysis]
           VAGRANT_CUSTOM[Custom Templates<br/>Specialised Environments]
       end
       
       subgraph "Docker VMs"
           DOCKER_LINUX[Alpine Linux<br/>Lightweight Analysis]
           DOCKER_UBUNTU[Ubuntu Container<br/>Quick Testing]
           DOCKER_WINE[Wine Container<br/>Windows Emulation]
       end
       
       subgraph "Shared Resources"
           NETWORK[Isolated Networks<br/>Traffic Monitoring]
           STORAGE[Shared Volumes<br/>File Exchange]
           MONITORING[Real-time Monitoring<br/>Metrics Collection]
       end
       
       GRPC --> POOL
       POOL --> LIFECYCLE
       
       LIFECYCLE --> VAGRANT_LINUX
       LIFECYCLE --> VAGRANT_WIN
       LIFECYCLE --> VAGRANT_CUSTOM
       
       LIFECYCLE --> DOCKER_LINUX
       LIFECYCLE --> DOCKER_UBUNTU
       LIFECYCLE --> DOCKER_WINE
       
       VAGRANT_LINUX --> NETWORK
       VAGRANT_WIN --> NETWORK
       DOCKER_LINUX --> NETWORK
       
       VAGRANT_LINUX --> STORAGE
       DOCKER_LINUX --> STORAGE
       
       VAGRANT_LINUX --> MONITORING
       VAGRANT_WIN --> MONITORING
       DOCKER_LINUX --> MONITORING

🔄 VM Lifecycle Management
==========================

Complete VM Workflow
--------------------

.. mermaid::

   stateDiagram-v2
       [*] --> Requested: API Request
       Requested --> Provisioning: Resource Available
       Requested --> Queued: Resource Busy
       
       Queued --> Provisioning: Resource Available
       
       Provisioning --> Starting: VM Created
       Starting --> Ready: Boot Complete
       
       Ready --> FileInjection: Inject Malware
       FileInjection --> Executing: Start Analysis
       
       Executing --> Monitoring: Real-time Tracking
       Monitoring --> Executing: Continue Analysis
       Monitoring --> Completing: Timeout/Manual Stop
       
       Completing --> Cleanup: Collect Results
       Cleanup --> Destroyed: VM Terminated
       
       Destroyed --> [*]: Resources Released
       
       Ready --> Error: Boot Failed
       FileInjection --> Error: Injection Failed
       Executing --> Error: VM Crashed
       Error --> Cleanup: Force Cleanup

VM State Transitions
-------------------

.. mermaid::

   sequenceDiagram
       participant API
       participant Pool
       participant VM
       participant Monitor
       participant Storage
       
       Note over API,Storage: VM Creation Flow
       
       API->>Pool: Request VM (type, template)
       Pool->>VM: Provision new instance
       VM->>Pool: Report ready state
       Pool->>API: Return VM details
       
       Note over API,Storage: File Injection
       
       API->>Storage: Download malware file
       API->>VM: Inject file into VM
       VM->>Monitor: Start monitoring
       Monitor->>API: Stream metrics
       
       Note over API,Storage: Analysis Execution
       
       VM->>VM: Execute malware
       VM->>Monitor: Log system events
       VM->>Storage: Save screenshots
       VM->>Storage: Export network logs
       
       Note over API,Storage: Cleanup Process
       
       API->>VM: Signal termination
       VM->>Storage: Final data export
       VM->>Pool: Release resources
       Pool->>API: Confirm destruction

🐧 Vagrant VM Management
========================

Full Virtualisation Platform
----------------------------

.. mermaid::

   graph TB
       subgraph "Vagrant Infrastructure"
           VAGRANTFILE[Vagrantfile<br/>VM Definitions]
           PROVIDER[VirtualBox Provider<br/>Hypervisor]
           NETWORK[Private Networks<br/>Isolated Subnets]
       end
       
       subgraph "Linux VMs"
           UBUNTU[Ubuntu 20.04<br/>- Python analysis<br/>- ELF binaries<br/>- Shell scripts]
           KALI[Kali Linux<br/>- Security tools<br/>- Network analysis<br/>- Forensics]
       end
       
       subgraph "Windows VMs"
           WIN10[Windows 10<br/>- PE executables<br/>- Office macros<br/>- PowerShell]
           WIN_SERVER[Windows Server<br/>- Enterprise malware<br/>- AD environments]
       end
       
       subgraph "VM Features"
           SNAPSHOTS[VM Snapshots<br/>Quick Reset]
           SHARED_FOLDERS[Shared Folders<br/>File Exchange]
           PORT_FORWARD[Port Forwarding<br/>Service Access]
       end
       
       VAGRANTFILE --> PROVIDER
       PROVIDER --> UBUNTU
       PROVIDER --> KALI
       PROVIDER --> WIN10
       PROVIDER --> WIN_SERVER
       
       UBUNTU --> SNAPSHOTS
       WIN10 --> SNAPSHOTS
       UBUNTU --> SHARED_FOLDERS
       WIN10 --> SHARED_FOLDERS
       UBUNTU --> PORT_FORWARD
       WIN10 --> PORT_FORWARD

Vagrant Configuration
--------------------

.. code-block:: ruby

   # Vagrantfile example
   Vagrant.configure("2") do |config|
     # Ubuntu analysis VM
     config.vm.define "ubuntu-analysis" do |ubuntu|
       ubuntu.vm.box = "ubuntu/focal64"
       ubuntu.vm.network "private_network", ip: "*************"
       ubuntu.vm.provider "virtualbox" do |vb|
         vb.memory = "2048"
         vb.cpus = 2
         vb.gui = false
       end
       
       # Provisioning script
       ubuntu.vm.provision "shell", inline: <<-SHELL
         apt-get update
         apt-get install -y python3 python3-pip
         pip3 install volatility3 yara-python
       SHELL
     end
     
     # Windows analysis VM
     config.vm.define "windows-analysis" do |windows|
       windows.vm.box = "gusztavvargadr/windows-10"
       windows.vm.network "private_network", ip: "*************"
       windows.vm.provider "virtualbox" do |vb|
         vb.memory = "4096"
         vb.cpus = 2
         vb.gui = true
       end
     end
   end

🐳 Docker VM Management
=======================

Lightweight Containerisation
----------------------------

.. mermaid::

   graph TB
       subgraph "Docker Infrastructure"
           COMPOSE[Docker Compose<br/>Service Orchestration]
           NETWORK[Docker Networks<br/>Isolated Communication]
           VOLUMES[Docker Volumes<br/>Persistent Storage]
       end
       
       subgraph "Analysis Containers"
           ALPINE[Alpine Linux<br/>- Minimal footprint<br/>- Quick startup<br/>- Basic analysis]
           UBUNTU_DOCKER[Ubuntu Container<br/>- Full Linux tools<br/>- Python environment<br/>- Development tools]
       end
       
       subgraph "Specialised Containers"
           WINE[Wine Container<br/>- Windows emulation<br/>- PE analysis<br/>- Cross-platform]
           SANDBOX[Sandbox Container<br/>- Restricted environment<br/>- Network isolation<br/>- Resource limits]
       end
       
       subgraph "Container Features"
           EPHEMERAL[Ephemeral Storage<br/>Auto-cleanup]
           RESOURCE_LIMITS[Resource Limits<br/>CPU/Memory Control]
           SECURITY[Security Profiles<br/>AppArmor/SELinux]
       end
       
       COMPOSE --> ALPINE
       COMPOSE --> UBUNTU_DOCKER
       COMPOSE --> WINE
       COMPOSE --> SANDBOX
       
       NETWORK --> ALPINE
       NETWORK --> UBUNTU_DOCKER
       VOLUMES --> UBUNTU_DOCKER
       
       ALPINE --> EPHEMERAL
       UBUNTU_DOCKER --> RESOURCE_LIMITS
       SANDBOX --> SECURITY

Docker Configuration
-------------------

.. code-block:: yaml

   # docker-compose.vm.yml
   version: '3.8'
   
   services:
     alpine-analysis:
       image: alpine:latest
       networks:
         - analysis_network
       volumes:
         - malware_samples:/samples:ro
         - analysis_results:/results:rw
       environment:
         - ANALYSIS_TIMEOUT=1800
       command: |
         sh -c "
           apk add --no-cache python3 py3-pip file
           pip3 install yara-python
           /analysis/run_analysis.sh
         "
       deploy:
         resources:
           limits:
             memory: 512M
             cpus: '0.5'
     
     ubuntu-analysis:
       image: ubuntu:20.04
       networks:
         - analysis_network
       volumes:
         - malware_samples:/samples:ro
         - analysis_results:/results:rw
       environment:
         - DEBIAN_FRONTEND=noninteractive
       command: |
         bash -c "
           apt-get update && apt-get install -y python3 python3-pip
           pip3 install volatility3 yara-python requests
           /analysis/run_analysis.sh
         "
       deploy:
         resources:
           limits:
             memory: 2G
             cpus: '1.0'

🌐 Network Isolation
====================

Secure Network Architecture
---------------------------

.. mermaid::

   graph TB
       subgraph "Host Network"
           HOST[Host Machine<br/>***********/24]
           INTERNET[Internet Access<br/>Controlled Egress]
       end
       
       subgraph "VM Networks"
           ANALYSIS_NET[Analysis Network<br/>************/24]
           ISOLATED_NET[Isolated Network<br/>10.0.0.0/24]
           MONITORING_NET[Monitoring Network<br/>**********/24]
       end
       
       subgraph "Network Controls"
           FIREWALL[iptables Rules<br/>Traffic Filtering]
           PROXY[HTTP Proxy<br/>Request Logging]
           DNS[DNS Sinkhole<br/>Malicious Domain Blocking]
       end
       
       subgraph "Traffic Analysis"
           PCAP[Packet Capture<br/>Network Forensics]
           IDS[Intrusion Detection<br/>Suricata/Snort]
           FLOW[Flow Analysis<br/>Connection Tracking]
       end
       
       HOST --> ANALYSIS_NET
       ANALYSIS_NET --> ISOLATED_NET
       MONITORING_NET --> ANALYSIS_NET
       
       ANALYSIS_NET --> FIREWALL
       FIREWALL --> PROXY
       PROXY --> DNS
       
       ANALYSIS_NET --> PCAP
       PCAP --> IDS
       IDS --> FLOW

📊 Resource Management
=====================

VM Pool Strategy
---------------

.. mermaid::

   graph TB
       subgraph "Resource Pool"
           AVAILABLE[Available VMs<br/>Ready for Analysis]
           ACTIVE[Active VMs<br/>Running Analysis]
           CLEANUP[Cleanup Queue<br/>Pending Destruction]
       end
       
       subgraph "Pool Management"
           SCHEDULER[VM Scheduler<br/>Resource Allocation]
           MONITOR[Resource Monitor<br/>Usage Tracking]
           SCALER[Auto Scaler<br/>Dynamic Provisioning]
       end
       
       subgraph "Resource Limits"
           CPU_LIMIT[CPU Limits<br/>Per-VM Allocation]
           MEMORY_LIMIT[Memory Limits<br/>RAM Management]
           STORAGE_LIMIT[Storage Limits<br/>Disk Quotas]
       end
       
       AVAILABLE --> SCHEDULER
       SCHEDULER --> ACTIVE
       ACTIVE --> CLEANUP
       CLEANUP --> AVAILABLE
       
       SCHEDULER --> MONITOR
       MONITOR --> SCALER
       SCALER --> SCHEDULER
       
       ACTIVE --> CPU_LIMIT
       ACTIVE --> MEMORY_LIMIT
       ACTIVE --> STORAGE_LIMIT

Resource Allocation Strategy
---------------------------

.. mermaid::

   sequenceDiagram
       participant Request
       participant Scheduler
       participant Pool
       participant VM
       participant Monitor
       
       Request->>Scheduler: Request VM (type, resources)
       Scheduler->>Pool: Check available resources
       
       alt Resources Available
           Pool->>VM: Provision new VM
           VM->>Pool: Report ready
           Pool->>Scheduler: VM allocated
           Scheduler->>Request: Return VM details
       else Resources Exhausted
           Scheduler->>Pool: Queue request
           Pool->>Monitor: Check cleanup candidates
           Monitor->>VM: Terminate idle VMs
           VM->>Pool: Resources released
           Pool->>Scheduler: Retry allocation
       end
       
       Note over Request,Monitor: Continuous Monitoring
       
       Monitor->>VM: Check resource usage
       VM->>Monitor: Report metrics
       Monitor->>Scheduler: Update availability

🚀 Next Steps
=============

Explore more components:

1. **Monitoring Stack**: :doc:`monitoring-stack` - Learn about logging and analytics
2. **Networking**: :doc:`networking` - Understand Traefik and service discovery
3. **Quick Start**: :doc:`../quickstart/installation` - Set up your first VM

📚 External Resources
====================

- `Vagrant Documentation <https://www.vagrantup.com/docs>`_
- `Docker Documentation <https://docs.docker.com/>`_
- `VirtualBox Manual <https://www.virtualbox.org/manual/>`_
- `gRPC Documentation <https://grpc.io/docs/>`_
