============================
🔍 Logging Operations Guide
============================

Comprehensive guide for managing, monitoring, and troubleshooting the TurdParty ELK logging stack.

.. contents:: Table of Contents
   :local:
   :depth: 2

🚀 Quick Start Operations
========================

Essential Commands
-----------------

.. code-block:: bash

   # Start all services with logging
   ./scripts/restart-with-logging.sh
   
   # Check service health
   docker-compose ps
   
   # View real-time logs
   docker-compose logs -f api
   docker-compose logs -f filebeat
   
   # Check Elasticsearch health
   curl http://localhost:9200/_cluster/health
   
   # List all log indices
   curl "http://localhost:9200/_cat/indices/turdparty-*?v"

Service Status Verification
---------------------------

.. code-block:: bash

   # Verify all logging components
   echo "=== Service Status ==="
   docker-compose ps elasticsearch logstash kibana filebeat
   
   echo "=== Elasticsearch Health ==="
   curl -s http://localhost:9200/_cluster/health | jq
   
   echo "=== Available Indices ==="
   curl -s "http://localhost:9200/_cat/indices/turdparty-*?v"
   
   echo "=== Recent Log Count ==="
   curl -s "http://localhost:9200/turdparty-*/_count" | jq

📊 Log Analysis & Search
=======================

Kibana Dashboard Operations
--------------------------

**Access Kibana**: `http://kibana.turdparty.localhost`

**Initial Setup Steps:**

1. **Create Index Patterns**:
   - Go to Management → Stack Management → Index Patterns
   - Create pattern: `turdparty-*` (all logs)
   - Create pattern: `turdparty-docker-*` (container logs only)
   - Set `@timestamp` as time field

2. **Common Searches**:

.. code-block:: text

   # Search for errors across all services
   level:ERROR OR level:error OR message:*error*
   
   # Filter by specific service
   service_name:api AND level:INFO
   
   # Search for container-specific logs
   container.name:turdpartycollab_api
   
   # Time-based filtering (last hour)
   @timestamp:[now-1h TO now]
   
   # Search for specific log messages
   message:"Failed to connect" OR message:"Connection refused"

Elasticsearch Query Examples
----------------------------

.. code-block:: bash

   # Search recent error logs
   curl -X GET "localhost:9200/turdparty-*/_search" -H 'Content-Type: application/json' -d'
   {
     "query": {
       "bool": {
         "must": [
           {
             "range": {
               "@timestamp": {
                 "gte": "now-1h"
               }
             }
           },
           {
             "terms": {
               "level": ["ERROR", "error", "Error"]
             }
           }
         ]
       }
     },
     "sort": [{"@timestamp": {"order": "desc"}}],
     "size": 20
   }'
   
   # Get log statistics by service
   curl -X GET "localhost:9200/turdparty-docker-*/_search" -H 'Content-Type: application/json' -d'
   {
     "size": 0,
     "aggs": {
       "services": {
         "terms": {
           "field": "service_name",
           "size": 10
         },
         "aggs": {
           "log_levels": {
             "terms": {
               "field": "level"
             }
           }
         }
       }
     }
   }'

🔧 Troubleshooting Common Issues
===============================

Filebeat Connection Issues
--------------------------

**Problem**: Filebeat cannot connect to Logstash

.. code-block:: bash

   # Check Filebeat logs
   docker-compose logs filebeat | grep -i error
   
   # Verify Logstash is listening
   docker-compose exec logstash netstat -tlnp | grep 5044
   
   # Test network connectivity
   docker-compose exec filebeat ping logstash

**Solution**:

.. code-block:: bash

   # Restart Logstash first, then Filebeat
   docker-compose restart logstash
   sleep 30
   docker-compose restart filebeat

Logstash Configuration Errors
-----------------------------

**Problem**: Logstash fails to start due to configuration syntax errors

.. code-block:: bash

   # Check Logstash logs for syntax errors
   docker-compose logs logstash | grep -i "configuration error"
   
   # Validate configuration syntax
   docker-compose exec logstash /usr/share/logstash/bin/logstash --config.test_and_exit

**Solution**:

.. code-block:: bash

   # Fix configuration and restart
   # Edit config/logstash/logstash.conf
   docker-compose restart logstash

Elasticsearch Storage Issues
---------------------------

**Problem**: Elasticsearch running out of disk space

.. code-block:: bash

   # Check disk usage
   curl "http://localhost:9200/_cat/allocation?v"
   
   # Check index sizes
   curl "http://localhost:9200/_cat/indices/turdparty-*?v&s=store.size:desc"
   
   # Check cluster health
   curl "http://localhost:9200/_cluster/health?pretty"

**Solution**:

.. code-block:: bash

   # Delete old indices (older than 7 days)
   curl -X DELETE "localhost:9200/turdparty-*-$(date -d '7 days ago' +%Y.%m.%d)"
   
   # Set up index lifecycle management
   curl -X PUT "localhost:9200/_ilm/policy/turdparty-policy" -H 'Content-Type: application/json' -d'
   {
     "policy": {
       "phases": {
         "hot": {
           "actions": {
             "rollover": {
               "max_size": "1GB",
               "max_age": "1d"
             }
           }
         },
         "delete": {
           "min_age": "7d",
           "actions": {
             "delete": {}
           }
         }
       }
     }
   }'

📈 Performance Monitoring
========================

Resource Usage Monitoring
-------------------------

.. code-block:: bash

   # Monitor container resource usage
   docker stats turdpartycollab_elasticsearch turdpartycollab_logstash turdpartycollab_kibana turdpartycollab_filebeat
   
   # Check Elasticsearch performance
   curl "http://localhost:9200/_nodes/stats/jvm,process,fs?pretty"
   
   # Monitor index performance
   curl "http://localhost:9200/_stats/indexing,search?pretty"

Log Volume Analysis
------------------

.. code-block:: bash

   # Check daily log volume
   curl -X GET "localhost:9200/turdparty-*/_search" -H 'Content-Type: application/json' -d'
   {
     "size": 0,
     "aggs": {
       "daily_logs": {
         "date_histogram": {
           "field": "@timestamp",
           "calendar_interval": "day"
         },
         "aggs": {
           "services": {
             "terms": {
               "field": "service_name"
             }
           }
         }
       }
     }
   }'
   
   # Check log ingestion rate
   curl "http://localhost:9200/_cat/indices/turdparty-*?v&h=index,docs.count,store.size&s=docs.count:desc"

🔄 Maintenance Operations
========================

Index Management
---------------

.. code-block:: bash

   # Create index template for consistent mapping
   curl -X PUT "localhost:9200/_index_template/turdparty-template" -H 'Content-Type: application/json' -d'
   {
     "index_patterns": ["turdparty-*"],
     "template": {
       "settings": {
         "number_of_shards": 1,
         "number_of_replicas": 0,
         "refresh_interval": "5s"
       },
       "mappings": {
         "properties": {
           "@timestamp": {"type": "date"},
           "service_name": {"type": "keyword"},
           "level": {"type": "keyword"},
           "message": {"type": "text"},
           "container_id": {"type": "keyword"}
         }
       }
     }
   }'
   
   # Force merge old indices
   curl -X POST "localhost:9200/turdparty-*-$(date -d '1 day ago' +%Y.%m.%d)/_forcemerge?max_num_segments=1"

Log Rotation & Cleanup
----------------------

.. code-block:: bash

   #!/bin/bash
   # Automated log cleanup script
   
   # Delete indices older than 30 days
   CUTOFF_DATE=$(date -d '30 days ago' +%Y.%m.%d)
   
   # Get list of old indices
   OLD_INDICES=$(curl -s "http://localhost:9200/_cat/indices/turdparty-*?h=index" | grep -E "turdparty-.*-[0-9]{4}\.[0-9]{2}\.[0-9]{2}" | while read index; do
     INDEX_DATE=$(echo $index | grep -oE '[0-9]{4}\.[0-9]{2}\.[0-9]{2}$')
     if [[ "$INDEX_DATE" < "$CUTOFF_DATE" ]]; then
       echo $index
     fi
   done)
   
   # Delete old indices
   for index in $OLD_INDICES; do
     echo "Deleting old index: $index"
     curl -X DELETE "localhost:9200/$index"
   done

Configuration Backup
--------------------

.. code-block:: bash

   # Backup Elasticsearch configuration
   mkdir -p backups/elasticsearch
   curl "http://localhost:9200/_cluster/settings?pretty" > backups/elasticsearch/cluster_settings.json
   curl "http://localhost:9200/_template?pretty" > backups/elasticsearch/templates.json
   
   # Backup Logstash configuration
   cp -r config/logstash/ backups/
   
   # Backup Filebeat configuration
   cp -r config/filebeat/ backups/

🚨 Alerting & Monitoring
========================

Health Check Automation
-----------------------

.. code-block:: bash

   #!/bin/bash
   # Health check script for logging stack
   
   check_elasticsearch() {
     local health=$(curl -s "http://localhost:9200/_cluster/health" | jq -r '.status')
     if [[ "$health" != "green" && "$health" != "yellow" ]]; then
       echo "ALERT: Elasticsearch cluster health is $health"
       return 1
     fi
     return 0
   }
   
   check_log_ingestion() {
     local recent_logs=$(curl -s "http://localhost:9200/turdparty-*/_count?q=@timestamp:[now-5m TO now]" | jq '.count')
     if [[ "$recent_logs" -eq 0 ]]; then
       echo "ALERT: No logs ingested in the last 5 minutes"
       return 1
     fi
     return 0
   }
   
   check_disk_space() {
     local disk_usage=$(curl -s "http://localhost:9200/_cat/allocation?h=disk.percent" | head -1)
     if [[ "${disk_usage%\%}" -gt 85 ]]; then
       echo "ALERT: Elasticsearch disk usage is ${disk_usage}"
       return 1
     fi
     return 0
   }

Error Rate Monitoring
--------------------

.. code-block:: bash

   # Monitor error rates across services
   curl -X GET "localhost:9200/turdparty-*/_search" -H 'Content-Type: application/json' -d'
   {
     "size": 0,
     "query": {
       "range": {
         "@timestamp": {
           "gte": "now-1h"
         }
       }
     },
     "aggs": {
       "error_rate": {
         "filters": {
           "filters": {
             "errors": {
               "terms": {
                 "level": ["ERROR", "error", "Error"]
               }
             },
             "total": {
               "match_all": {}
             }
           }
         }
       },
       "services": {
         "terms": {
           "field": "service_name"
         },
         "aggs": {
           "error_count": {
             "filter": {
               "terms": {
                 "level": ["ERROR", "error", "Error"]
               }
             }
           }
         }
       }
     }
   }'

🚀 Next Steps
=============

Advanced Operations:

1. **API Development**: :doc:`../development/api-development` - Application logging integration
2. **Testing Guide**: :doc:`../development/testing-guide` - Log validation in tests
3. **Production Deployment**: :doc:`../../deployment/production` - Production logging setup

📚 Additional Resources
======================

- **Elasticsearch Operations**: `Cluster Management Guide <https://www.elastic.co/guide/en/elasticsearch/reference/current/cluster.html>`_
- **Logstash Troubleshooting**: `Pipeline Debugging <https://www.elastic.co/guide/en/logstash/current/debugging.html>`_
- **Kibana Best Practices**: `Dashboard Optimization <https://www.elastic.co/guide/en/kibana/current/dashboard.html>`_
