====================
📋 Prerequisites
====================

Before setting up TurdParty, ensure your system meets the requirements and has the necessary tools installed.

.. contents:: Table of Contents
   :local:
   :depth: 2

💻 System Requirements
=====================

Minimum Hardware Specifications
------------------------------

.. mermaid::

   graph TB
       subgraph "Host System"
           CPU[CPU: 8+ cores<br/>Intel/AMD x64]
           RAM[RAM: 16GB minimum<br/>32GB recommended]
           STORAGE[Storage: 500GB SSD<br/>1TB+ recommended]
           NETWORK[Network: Gigabit Ethernet<br/>Stable internet connection]
       end
       
       subgraph "VM Resources"
           VM_CPU[VM CPU: 2-4 cores per VM<br/>Multiple concurrent VMs]
           VM_RAM[VM RAM: 2-8GB per VM<br/>Depends on analysis type]
           VM_STORAGE[VM Storage: 50-100GB per VM<br/>Temporary allocation]
       end
       
       subgraph "Container Resources"
           DOCKER_CPU[Docker CPU: 0.5-2 cores<br/>Lightweight analysis]
           DOCKER_RAM[Docker RAM: 512MB-2GB<br/>Per container]
           DOCKER_STORAGE[Docker Storage: 10-50GB<br/>Image and volume storage]
       end
       
       CPU --> VM_CPU
       RAM --> VM_RAM
       STORAGE --> VM_STORAGE
       
       CPU --> DOCKER_CPU
       RAM --> DOCKER_RAM
       STORAGE --> DOCKER_STORAGE

Supported Operating Systems
---------------------------

.. list-table:: OS Compatibility Matrix
   :header-rows: 1
   :widths: 25 25 25 25

   * - Operating System
     - Docker Support
     - Vagrant Support
     - Recommended
   * - **Ubuntu 20.04/22.04**
     - ✅ Native
     - ✅ Full
     - ⭐ **Primary**
   * - **Debian 11/12**
     - ✅ Native
     - ✅ Full
     - ✅ Supported
   * - **CentOS 8/RHEL 8**
     - ✅ Native
     - ✅ Full
     - ✅ Supported
   * - **macOS 12+**
     - ✅ Docker Desktop
     - ⚠️ Limited
     - ⚠️ Development only
   * - **Windows 10/11**
     - ✅ Docker Desktop
     - ⚠️ WSL2 required
     - ⚠️ Development only

🛠️ Required Software
====================

Core Dependencies
----------------

.. mermaid::

   graph TB
       subgraph "Container Platform"
           DOCKER[Docker Engine 24.0+<br/>Container runtime]
           COMPOSE[Docker Compose 2.0+<br/>Multi-container orchestration]
       end
       
       subgraph "Virtualisation"
           VAGRANT[Vagrant 2.3+<br/>VM management]
           VBOX[VirtualBox 7.0+<br/>Hypervisor]
       end
       
       subgraph "Development Tools"
           GIT[Git 2.30+<br/>Version control]
           NIX[Nix Package Manager<br/>Dependency management]
           PYTHON[Python 3.9+<br/>API development]
       end
       
       subgraph "Optional Tools"
           VSCODE[VS Code<br/>Development environment]
           DEVCONTAINER[Dev Containers<br/>Consistent development]
       end
       
       DOCKER --> COMPOSE
       VAGRANT --> VBOX
       GIT --> NIX
       NIX --> PYTHON
       VSCODE --> DEVCONTAINER

Installation Commands
--------------------

**Ubuntu/Debian:**

.. code-block:: bash

   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   sudo usermod -aG docker $USER
   
   # Install Docker Compose
   sudo apt install docker-compose-plugin
   
   # Install Vagrant and VirtualBox
   sudo apt install vagrant virtualbox virtualbox-ext-pack
   
   # Install Git and development tools
   sudo apt install git curl wget build-essential
   
   # Install Nix (single-user installation)
   sh <(curl -L https://nixos.org/nix/install) --no-daemon

   # Reload shell environment
   source ~/.nix-profile/etc/profile.d/nix.sh

   # Verify Nix installation
   nix --version

**CentOS/RHEL:**

.. code-block:: bash

   # Install Docker
   sudo dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
   sudo dnf install docker-ce docker-ce-cli containerd.io docker-compose-plugin
   sudo systemctl enable --now docker
   sudo usermod -aG docker $USER
   
   # Install Vagrant and VirtualBox
   sudo dnf install vagrant VirtualBox
   
   # Install development tools
   sudo dnf groupinstall "Development Tools"
   sudo dnf install git curl wget

   # Install Nix
   sh <(curl -L https://nixos.org/nix/install) --no-daemon
   source ~/.nix-profile/etc/profile.d/nix.sh

🖥️ VM Management Configuration
===============================

Vagrant Setup for Full VM Testing
----------------------------------

For complete VM testing capabilities, Vagrant requires specific configuration:

**Vagrant Host Binding**

.. code-block:: bash

   # Start Vagrant with host binding for API access
   vagrant serve --host 0.0.0.0

   # This enables WebSocket and gRPC connectivity from containers to VMs

**Vagrantfile Configuration**

Add these port forwards to your ``Vagrantfile`` for proper connectivity:

.. code-block:: ruby

   # Vagrantfile
   Vagrant.configure("2") do |config|
     # gRPC communication ports
     config.vm.network "forwarded_port", guest: 40000, host: 40000
     config.vm.network "forwarded_port", guest: 50051, host: 50051

     # SSH access
     config.vm.network "forwarded_port", guest: 22, host: 2222

     # Windows RDP (if using Windows VMs)
     config.vm.network "forwarded_port", guest: 3389, host: 3389

     # Windows WinRM (if using Windows VMs)
     config.vm.network "forwarded_port", guest: 5985, host: 5985
     config.vm.network "forwarded_port", guest: 5986, host: 5986
   end

**Development Environment with Nix**

The recommended way to work with TurdParty is using the Nix development environment:

.. code-block:: bash

   # Enter the development environment (provides all dependencies)
   nix-shell

   # Verify Python and testing tools are available
   python --version    # Should show Python 3.12+
   pytest --version    # Should show pytest with all plugins

   # Run the test suite
   ./scripts/run-parallel-tests.sh

🌐 Network Configuration
========================

Domain Setup
-----------

TurdParty uses local domain routing for service access. Configure your system:

.. code-block:: bash

   # Add to /etc/hosts (Linux/macOS) or C:\Windows\System32\drivers\etc\hosts (Windows)
   127.0.0.1 frontend.turdparty.localhost
   127.0.0.1 docs.turdparty.localhost
   127.0.0.1 kibana.turdparty.localhost
   127.0.0.1 status.turdparty.localhost
   127.0.0.1 minio.turdparty.localhost

Network Architecture
-------------------

.. mermaid::

   graph TB
       subgraph "External Access"
           BROWSER[Web Browser]
           API_CLIENT[API Clients]
       end
       
       subgraph "Traefik Proxy"
           TRAEFIK[Traefik<br/>Port 80/443]
           ROUTER[Request Router<br/>Domain-based routing]
       end
       
       subgraph "Internal Services"
           FRONTEND[React Frontend<br/>frontend.turdparty.localhost]
           DOCS[Documentation<br/>docs.turdparty.localhost]
           API[FastAPI<br/>localhost:8000]
           KIBANA[Kibana<br/>kibana.turdparty.localhost]
           STATUS[Status Page<br/>status.turdparty.localhost]
       end
       
       subgraph "Docker Networks"
           TURDPARTY_NET[turdpartycollab_net<br/>Internal communication]
           TRAEFIK_NET[traefik_network<br/>Proxy communication]
       end
       
       BROWSER --> TRAEFIK
       API_CLIENT --> TRAEFIK
       TRAEFIK --> ROUTER
       
       ROUTER --> FRONTEND
       ROUTER --> DOCS
       ROUTER --> API
       ROUTER --> KIBANA
       ROUTER --> STATUS
       
       FRONTEND --> TURDPARTY_NET
       API --> TURDPARTY_NET
       TRAEFIK --> TRAEFIK_NET

Port Requirements
----------------

.. list-table:: Port Allocation
   :header-rows: 1
   :widths: 15 20 35 30

   * - Port
     - Service
     - Purpose
     - Access Level
   * - **80**
     - Traefik HTTP
     - Web interface access
     - External
   * - **443**
     - Traefik HTTPS
     - Secure web access
     - External
   * - **8000**
     - FastAPI
     - REST API endpoints
     - Internal/External
   * - **9200**
     - Elasticsearch
     - Search and analytics
     - Internal
   * - **5601**
     - Kibana
     - Log visualisation
     - Internal
   * - **9000**
     - MinIO API
     - Object storage
     - Internal
   * - **9001**
     - MinIO Console
     - Storage management
     - Internal
   * - **40000**
     - gRPC Service
     - VM communication
     - Internal
   * - **6379**
     - Redis
     - Cache and queue
     - Internal
   * - **5432**
     - PostgreSQL
     - Database
     - Internal

🔐 Security Considerations
=========================

Development vs Production
-------------------------

.. mermaid::

   graph TB
       subgraph "Development Environment"
           DEV_AUTH[No Authentication<br/>Local access only]
           DEV_SSL[HTTP Only<br/>No SSL certificates]
           DEV_SECRETS[Default Secrets<br/>Hardcoded passwords]
           DEV_NETWORK[Open Networks<br/>No firewall rules]
       end
       
       subgraph "Production Environment"
           PROD_AUTH[OAuth2/JWT<br/>User authentication]
           PROD_SSL[HTTPS/TLS<br/>Valid certificates]
           PROD_SECRETS[Vault Secrets<br/>Encrypted storage]
           PROD_NETWORK[Restricted Networks<br/>Firewall protection]
       end
       
       subgraph "Security Measures"
           ISOLATION[VM Isolation<br/>Network segmentation]
           MONITORING[Security Monitoring<br/>Intrusion detection]
           BACKUP[Encrypted Backups<br/>Data protection]
           AUDIT[Audit Logging<br/>Compliance tracking]
       end
       
       DEV_AUTH -.-> PROD_AUTH
       DEV_SSL -.-> PROD_SSL
       DEV_SECRETS -.-> PROD_SECRETS
       DEV_NETWORK -.-> PROD_NETWORK
       
       PROD_AUTH --> ISOLATION
       PROD_SSL --> MONITORING
       PROD_SECRETS --> BACKUP
       PROD_NETWORK --> AUDIT

Security Checklist
------------------

.. admonition:: Development Security
   :class: warning

   ⚠️ **Development Environment Only**
   
   The default configuration is designed for development and testing:
   
   - Default passwords are used
   - No authentication is required
   - Services are accessible without encryption
   - Network isolation is minimal
   
   **Never use development configuration in production!**

.. admonition:: Production Hardening
   :class: important

   🔒 **Production Deployment Requirements**
   
   Before production deployment:
   
   - [ ] Change all default passwords
   - [ ] Enable authentication (OAuth2/JWT)
   - [ ] Configure SSL/TLS certificates
   - [ ] Implement network firewalls
   - [ ] Enable audit logging
   - [ ] Set up monitoring and alerting
   - [ ] Configure backup and recovery
   - [ ] Perform security assessment

🧪 Verification Steps
====================

System Readiness Check
----------------------

.. code-block:: bash

   #!/bin/bash
   # TurdParty Prerequisites Check Script
   
   echo "🔍 TurdParty Prerequisites Check"
   echo "================================"
   
   # Check Docker
   if command -v docker &> /dev/null; then
       echo "✅ Docker: $(docker --version)"
   else
       echo "❌ Docker: Not installed"
   fi
   
   # Check Docker Compose
   if docker compose version &> /dev/null; then
       echo "✅ Docker Compose: $(docker compose version)"
   else
       echo "❌ Docker Compose: Not available"
   fi
   
   # Check Vagrant
   if command -v vagrant &> /dev/null; then
       echo "✅ Vagrant: $(vagrant --version)"
   else
       echo "❌ Vagrant: Not installed"
   fi
   
   # Check VirtualBox
   if command -v vboxmanage &> /dev/null; then
       echo "✅ VirtualBox: $(vboxmanage --version)"
   else
       echo "❌ VirtualBox: Not installed"
   fi
   
   # Check system resources
   echo ""
   echo "💻 System Resources:"
   echo "CPU Cores: $(nproc)"
   echo "Memory: $(free -h | awk '/^Mem:/ {print $2}')"
   echo "Disk Space: $(df -h / | awk 'NR==2 {print $4}')"

🚀 Next Steps
=============

Once prerequisites are met:

1. **Installation**: :doc:`installation` - Set up the TurdParty platform
2. **First Analysis**: :doc:`first-analysis` - Run your first malware analysis
3. **System Components**: :doc:`../components/api-layer` - Understand the architecture

📚 Troubleshooting
==================

Common issues and solutions:

- **Docker permission denied**: Add user to docker group and restart session
- **VirtualBox conflicts**: Disable Hyper-V on Windows, check kernel modules on Linux
- **Network connectivity**: Verify firewall settings and DNS resolution
- **Resource constraints**: Monitor system resources during VM operations

For detailed troubleshooting, see the :doc:`../../TESTING` documentation.
