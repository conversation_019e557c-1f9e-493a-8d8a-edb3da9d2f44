========
Glossary
========

This glossary defines key terms and concepts used throughout the TurdParty malware analysis platform.

.. glossary::
   :sorted:

   Analysis Pipeline
      The complete workflow from file upload through VM execution to result generation. Includes file validation, VM provisioning, malware injection, monitoring, and cleanup phases.

   Analysis Report
      Comprehensive document containing binary execution results, installation footprint analysis, runtime behaviour monitoring, and threat intelligence correlation. Generated from ECS data aggregation.

   Auto-Termination
      Security mechanism that automatically destroys VMs after 30 minutes of runtime to prevent resource exhaustion and limit malware persistence.

   API Client
      External applications or services that interact with TurdParty through REST or WebSocket APIs. Includes SDKs for Python and JavaScript.

   Behaviour Event
      A recorded action or activity detected during malware execution, such as file access, network connections, process creation, or registry modifications. Events are classified by severity (low, medium, high, critical).

   Binary Analysis
      The process of examining executable files to understand their structure, behaviour, and potential threats. Includes both static analysis (without execution) and dynamic analysis (runtime monitoring).

   Blake3 Hash
      A cryptographic hash function used for file integrity verification and deduplication. Provides faster performance than SHA-256 while maintaining security properties.

   BDD Testing
      Behaviour-Driven Development testing using BEHAVE framework. Validates business scenarios through Given-When-Then specifications covering file injection workflows, VM management, and ELK logging pipelines.

   Celery Worker
      Background task processors that handle asynchronous operations like file processing, VM management, and workflow orchestration. Workers are organised by function (file operations, VM management, workflow orchestration).

   Container Isolation
      Security mechanism using Docker containers to isolate malware execution from the host system. Provides process, network, and filesystem isolation with configurable resource limits.

   Content Type Detection
      Automatic identification of file formats and MIME types during upload validation. Supports executable files, documents, archives, and scripts with security filtering.

   Docker VM
      Lightweight containerised analysis environment using Docker. Provides fast startup times (< 5 seconds) and low resource overhead, suitable for high-throughput Linux malware analysis.

   ECS Logging
      Structured logging format following Elastic Common Schema standards. Enables consistent log parsing, searching, and analysis across the ELK stack.

   ELK Stack
      Elasticsearch, Logstash, and Kibana components providing log aggregation, search, and visualisation capabilities. Stores all system events, analysis results, and performance metrics.

   Event Streaming
      Real-time data flow from VM execution environments to ELK stack. Captures filesystem operations, network activity, process creation, and system calls for behaviour analysis.

   FastAPI Server
      High-performance Python web framework providing REST API endpoints, automatic OpenAPI documentation, and WebSocket support. Runs on port 8000 with async request handling.

   File Injection
      The process of deploying malware samples into isolated VM environments for analysis. Includes file transfer, permission setting, and execution triggering.

   File Upload
      The initial stage where users submit files for analysis. Files are validated, hashed, and stored in MinIO before entering the analysis pipeline.

   File Validation
      Security checks performed on uploaded files including size limits, format verification, hash calculation, and malicious content detection before storage.

   Installation Footprint
      The complete record of filesystem and registry changes made by malware during execution. Includes created files, modified registry keys, and system configuration changes.

   Job Queue
      Celery-based task distribution system with separate queues for file operations, VM management, workflow orchestration, and ELK integration. Enables horizontal scaling and fault tolerance.

   Load Balancing
      Traffic distribution across multiple service instances using Traefik proxy. Provides high availability, automatic failover, and service discovery for API endpoints.

   Malware Sample
      A potentially malicious file submitted for analysis. Can be executables, scripts, documents, or any file type that may contain threats.

   MinIO Storage
      S3-compatible object storage system used for file persistence. Stores uploaded malware samples, analysis results, and temporary files with versioning and encryption.

   Network Packet
      Captured network traffic data during malware execution. Includes source/destination IPs, ports, protocols, payload previews, and suspicious activity flags.

   Object Storage
      MinIO-based S3-compatible storage providing file persistence, versioning, and encryption. Handles malware samples, analysis artifacts, and temporary files with automatic cleanup.

   PostgreSQL Database
      Primary data store for metadata, workflow state, VM configurations, and analysis results. Provides ACID compliance, JSON support, and horizontal scaling capabilities.

   Real-time Monitoring
      Live observation of VM execution through WebSocket connections. Provides streaming metrics, command execution, and file operations monitoring.

   Redis Cache
      In-memory data structure store providing caching, session management, and message queuing. Supports Celery task distribution and real-time data sharing between services.

   Resource Management
      Dynamic allocation and monitoring of CPU, memory, storage, and network resources for VM instances. Includes configurable limits, usage tracking, and automatic cleanup.

   Runtime Behaviour
      The actions and activities performed by malware during execution. Includes process creation, network communications, file operations, and system modifications.

   Security Validation
      Comprehensive security testing including input validation, container escape prevention, privilege escalation detection, and data protection verification.

   Service Discovery
      Automatic detection and registration of microservices using Traefik proxy. Enables dynamic routing, health checking, and load balancing across service instances.

   Static Analysis
      Examination of malware files without execution to identify structure, embedded strings, imports, and potential capabilities. Complements dynamic analysis for comprehensive assessment.

   Threat Assessment
      Automated evaluation of malware risk based on behaviour analysis, known signatures, and threat intelligence correlation. Produces confidence scores and threat classifications.

   Traefik Proxy
      Reverse proxy and load balancer providing service discovery, SSL termination, and request routing. Handles authentication and authorisation for API access.

   Vagrant VM
      Full virtual machine environments using Vagrant and VirtualBox/VMware. Provides complete OS isolation for Windows malware analysis with configurable resources.

   VM Instance
      A running virtual machine (Docker or Vagrant) allocated for malware analysis. Has defined lifecycle states: creating, running, suspended, destroyed, error.

   VM Lifecycle
      The complete lifespan of a virtual machine from creation to destruction. Includes provisioning, configuration, execution, monitoring, and cleanup phases with 30-minute maximum runtime.

   VM Pool
      Pre-provisioned virtual machines maintained in ready state for immediate allocation. Typically maintains 2-10 ready VMs to reduce analysis startup time.

   VM Template
      Predefined virtual machine configurations specifying OS type, version, resources, and software packages. Templates enable consistent and reproducible analysis environments.

   VM Type
      Classification of virtual machine backend technology. Supported types include Docker containers for lightweight Linux analysis and Vagrant VMs for full Windows environments.

   WebSocket API
      Real-time bidirectional communication protocol enabling live monitoring, command execution, and file operations. Provides streaming updates for VM metrics and analysis progress.

   Workflow Job
      A complete end-to-end analysis task tracking file processing from upload through VM execution to result generation. Includes status tracking, progress monitoring, and error handling.

   Workflow Orchestrator
      Central coordinator managing the complete analysis pipeline. Handles task dependencies, error recovery, resource allocation, and ensures 30-minute VM lifecycle enforcement.

.. note::
   This glossary covers core concepts used throughout TurdParty documentation, APIs, and user interfaces. For detailed technical specifications, refer to the :doc:`api/overview` and :doc:`architecture/system-overview` sections.

.. seealso::
   * :doc:`getting-started/quickstart` - Getting started with TurdParty
   * :doc:`api/overview` - Complete API reference
   * :doc:`vm/overview` - Virtual machine management
   * :doc:`security/overview` - Security architecture and practices
