Docker Images Licenses
======================

This document lists all Docker images used in TurdParty and their respective licenses.

Base Images and Services
------------------------


docker.elastic.co/beats/filebeat:8.11.0
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**License**: Unknown

**Description**: Docker image: docker.elastic.co/beats/filebeat:8.11.0


docker.elastic.co/elasticsearch/elasticsearch:8.11.0
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**License**: Unknown

**Description**: Docker image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0


docker.elastic.co/kibana/kibana:8.11.0
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**License**: Unknown

**Description**: Docker image: docker.elastic.co/kibana/kibana:8.11.0


docker.elastic.co/logstash/logstash:8.11.0
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**License**: Unknown

**Description**: Docker image: docker.elastic.co/logstash/logstash:8.11.0


elasticsearch:8.11.0
~~~~~~~~~~~~~~~~~~~~

**License**: Elastic License 2.0

**License URL**: https://www.elastic.co/licensing/elastic-license

**Description**: Elasticsearch search engine


kibana:8.11.0
~~~~~~~~~~~~~

**License**: Elastic License 2.0

**License URL**: https://www.elastic.co/licensing/elastic-license

**Description**: Kibana visualization platform


logstash:8.11.0
~~~~~~~~~~~~~~~

**License**: Elastic License 2.0

**License URL**: https://www.elastic.co/licensing/elastic-license

**Description**: Logstash data processing pipeline


minio/minio
~~~~~~~~~~~

**License**: GNU AGPL v3.0

**License URL**: https://github.com/minio/minio/blob/master/LICENSE

**Description**: MinIO object storage server


minio/minio:latest
~~~~~~~~~~~~~~~~~~

**License**: Unknown

**Description**: Docker image: minio/minio:latest


nginx:alpine
~~~~~~~~~~~~

**License**: Unknown

**Description**: Docker image: nginx:alpine


nginx:alpine
~~~~~~~~~~~~

**License**: Unknown

**Description**: Docker image: nginx:alpine


postgres:15
~~~~~~~~~~~

**License**: PostgreSQL License (MIT-style)

**License URL**: https://www.postgresql.org/about/licence/

**Description**: PostgreSQL database server


postgres:15-alpine
~~~~~~~~~~~~~~~~~~

**License**: Unknown

**Description**: Docker image: postgres:15-alpine


python:3.12
~~~~~~~~~~~

**License**: Python Software Foundation License

**License URL**: https://docs.python.org/3/license.html

**Description**: Official Python runtime


redis:7
~~~~~~~

**License**: BSD 3-Clause License

**License URL**: https://redis.io/docs/about/license/

**Description**: Redis in-memory data store


redis:7-alpine
~~~~~~~~~~~~~~

**License**: Unknown

**Description**: Docker image: redis:7-alpine


traefik:v3.0
~~~~~~~~~~~~

**License**: MIT License

**License URL**: https://github.com/traefik/traefik/blob/master/LICENSE.md

**Description**: Traefik reverse proxy


ubuntu:20.04
~~~~~~~~~~~~

**License**: Ubuntu License (GPL-compatible)

**License URL**: https://ubuntu.com/legal/intellectual-property-policy

**Description**: Ubuntu base operating system


Summary
-------

**Total Images**: 18

*Generated on 2025-06-14 15:55:21*
