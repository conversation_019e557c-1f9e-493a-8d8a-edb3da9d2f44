💩🎉TurdParty🎉💩 Software Licenses
=====================================

This document provides a comprehensive overview of all software licenses used in the TurdParty malware analysis platform, including both Docker container dependencies and Python library dependencies.

.. toctree::
   :maxdepth: 2
   :caption: License Categories:

   python-libraries
   docker-images
   system-tools
   license-compliance

Overview
--------

TurdParty uses a variety of open-source software components, each with their own licensing terms. This documentation ensures compliance with all license requirements and provides transparency about the software stack.

**License Summary:**

- **Python Libraries**: 50+ packages with various licenses (MIT, Apache 2.0, BSD, etc.)
- **Docker Base Images**: Ubuntu, Alpine, and official language images
- **System Tools**: Standard Unix utilities and development tools
- **Development Tools**: Nix packages and build tools

Quick Reference
---------------

**Most Common Licenses:**

- **MIT License**: Permissive license allowing commercial use
- **Apache License 2.0**: Permissive license with patent protection
- **BSD Licenses**: Family of permissive licenses
- **GNU GPL**: Copyleft licenses requiring source distribution

**Compliance Requirements:**

1. **Attribution**: All licenses require proper attribution
2. **License Inclusion**: License texts must be included in distributions
3. **Source Availability**: Some licenses require source code availability
4. **Patent Grants**: Some licenses include patent protection clauses

License Categories
------------------

Python Libraries
~~~~~~~~~~~~~~~~

The TurdParty platform uses numerous Python packages for core functionality:

- **Web Framework**: FastAPI (MIT), Uvicorn (BSD)
- **Data Processing**: Pydantic (MIT), SQLAlchemy (MIT)
- **Async Operations**: asyncio (Python Software Foundation), aiofiles (Apache 2.0)
- **Testing**: pytest (MIT), Hypothesis (Mozilla Public License 2.0)
- **Security**: cryptography (Apache 2.0/BSD), hashlib (Python Software Foundation)

Docker Images
~~~~~~~~~~~~~

Container base images and official images:

- **Ubuntu**: Ubuntu License (GPL-compatible)
- **Python Official**: Python Software Foundation License
- **PostgreSQL**: PostgreSQL License (MIT-style)
- **Redis**: BSD 3-Clause License
- **Elasticsearch**: Elastic License 2.0

System Tools
~~~~~~~~~~~~

Development and runtime tools:

- **Nix**: LGPL 2.1
- **Docker**: Apache License 2.0
- **Git**: GPL 2.0
- **GNU Tools**: Various GPL licenses

Compliance Statement
--------------------

TurdParty is committed to open-source license compliance. All dependencies have been reviewed for license compatibility, and this documentation serves as our compliance record.

**Key Compliance Measures:**

1. **License Tracking**: Automated scanning of all dependencies
2. **Attribution**: Proper credit given to all open-source projects
3. **License Inclusion**: All required license texts are preserved
4. **Regular Audits**: Periodic review of license obligations

For questions about licensing or compliance, please contact the TurdParty development team.

Last Updated
------------

This license documentation was last updated on |today|.

.. note::
   This documentation is automatically generated from dependency analysis.
   For the most current license information, please refer to the individual
   project repositories and package metadata.
