License Compliance
==================

This document outlines <PERSON><PERSON><PERSON><PERSON>y's approach to open-source license compliance.

Compliance Framework
--------------------

TurdParty follows industry best practices for open-source license compliance:

1. **Automated Scanning**: Regular automated scans of all dependencies
2. **License Tracking**: Comprehensive tracking of all software licenses
3. **Documentation**: Detailed documentation of all license obligations
4. **Regular Audits**: Periodic manual review of license compliance

License Categories
------------------

Permissive Licenses
~~~~~~~~~~~~~~~~~~~

**Examples**: MIT, Apache 2.0, BSD

**Obligations**:
- Include license text in distributions
- Provide attribution to original authors
- No requirement to share source code modifications

**Compliance**: ✅ Fully compliant

Copyleft Licenses
~~~~~~~~~~~~~~~~~

**Examples**: GPL 2.0, GPL 3.0, LGPL 2.1

**Obligations**:
- Include license text in distributions
- Provide source code for modifications
- Ensure derivative works use compatible licenses

**Compliance**: ✅ Compliant (primarily development tools)

Proprietary/Commercial Licenses
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Examples**: Elastic License 2.0

**Obligations**:
- Review specific license terms
- Ensure compliance with usage restrictions
- May require commercial licensing for certain use cases

**Compliance**: ⚠️ Under review

Compliance Checklist
---------------------

- ✅ All dependencies identified and catalogued
- ✅ License texts preserved and accessible
- ✅ Attribution provided in documentation
- ✅ Source code availability ensured for GPL components
- ⚠️ Commercial license review in progress

Risk Assessment
---------------

**Low Risk**: MIT, Apache 2.0, BSD licensed components
**Medium Risk**: GPL licensed development tools (contained usage)
**High Risk**: None identified

Contact Information
-------------------

For license compliance questions or concerns:

- **Development Team**: TurdParty Core Team
- **Legal Review**: Consult legal counsel for commercial deployments
- **License Questions**: Review individual project documentation

Last Updated
------------

This compliance documentation was last updated on 2025-06-14

.. warning::
   This documentation is for informational purposes only and does not
   constitute legal advice. Consult qualified legal counsel for specific
   license compliance questions.
