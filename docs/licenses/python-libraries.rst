Python Libraries Licenses
=========================

This document lists all Python packages used in TurdParty and their respective licenses.

.. note::
   This documentation is automatically generated from the current Python environment.
   License information is extracted from package metadata.

Package Licenses
----------------





**Packages**:

- **annotated-types** (v0.7.0): Reusable constraint types to use with typing.Annotated
- **argon2-cffi** (v23.1.0): Argon2 for Python
- **attrs** (v24.2.0): Classes Without Boilerplate
- **ddt** (v1.7.2): Data-Driven/Decorated Tests
  
  *Homepage*: https://github.com/datadriventests/ddt
- **docker** (v7.1.0): A Python library for the Docker Engine API.
- **elastic-transport** (v8.15.0): Transport classes and utilities shared among Python Elastic client libraries
  
  *Homepage*: https://github.com/elastic/elastic-transport-python
- **elasticsearch** (v8.15.1): Python client for Elasticsearch
- **execnet** (v2.1.1): execnet: rapid multi-Python deployment
- **fastapi** (v0.115.3): FastAPI framework, high performance, easy to learn, fast to code, ready for production
- **filelock** (v3.16.1): A platform independent file lock.
- **httpcore** (v1.0.6): A minimal low-level HTTP client.
- **httpx** (v0.27.2): The next generation HTTP client.
- **idna** (v3.10): Internationalized Domain Names in Applications (IDNA)
- **importlib_metadata** (v8.5.0): Read metadata from Python packages
- **iniconfig** (v2.0.0): brain-dead simple config-ini parsing
- **Jinja2** (v3.1.5): A very fast and expressive template engine.
- **markdown-it-py** (v3.0.0): Python port of markdown-it. Markdown parsing, done right!
- **mdurl** (v0.1.2): Markdown URL utilities
- **packaging** (v24.1): Core utilities for Python packages
- **platformdirs** (v4.3.6): A small Python package for determining appropriate platform-specific dirs, e.g. a `user data dir`.
- **prompt_toolkit** (v3.0.48): Library for building powerful interactive command lines in Python
  
  *Homepage*: https://github.com/prompt-toolkit/python-prompt-toolkit
- **pydantic** (v2.9.2): Data validation using Python type hints
- **pytest-html** (v4.1.1): pytest plugin for generating HTML reports
- **pytest-metadata** (v3.1.1): pytest plugin for test session metadata
- **python-multipart** (v0.0.12): A streaming multipart parser for Python
- **setuptools** (v75.1.1.post0): Easily download, build, install, upgrade, and uninstall Python packages
- **starlette** (v0.40.0): The little ASGI library that shines.
- **stevedore** (v5.3.0): Manage dynamic plugins for Python applications
  
  *Homepage*: https://docs.openstack.org/stevedore/latest/
- **structlog** (v24.4.0): Structured Logging for Python
- **tomli** (v2.0.1): A lil' TOML parser
- **typer** (v0.12.5): Typer, build great CLIs. Easy to code. Based on Python type hints.
- **typing_extensions** (v4.12.2): Backported and Experimental Type Hints for Python 3.8+
- **urllib3** (v2.2.3): HTTP library with thread-safe connection pooling, file post, and more.
- **uvicorn** (v0.32.0): The lightning-fast ASGI server.
- **virtualenv** (v20.26.6): Virtual Python Environment builder
- **zipp** (v3.20.2): Backport of pathlib-compatible object wrapper for zip files


Apache 2
~~~~~~~~

**Packages**:

- **async-timeout** (v4.0.3): Timeout context manager for asyncio programs
  
  *Homepage*: https://github.com/aio-libs/async-timeout
- **multidict** (v6.1.0): multidict implementation
  
  *Homepage*: https://github.com/aio-libs/multidict


Apache 2.0
~~~~~~~~~~

**Packages**:

- **pytest-asyncio** (v0.23.8): Pytest support for asyncio
  
  *Homepage*: https://github.com/pytest-dev/pytest-asyncio
- **requests-toolbelt** (v1.0.0): A utility belt for advanced users of python-requests
  
  *Homepage*: https://toolbelt.readthedocs.io/
- **sortedcontainers** (v2.4.0): Sorted Containers -- Sorted List, Sorted Dict, Sorted Set
  
  *Homepage*: http://www.grantjenks.com/docs/sortedcontainers/


Apache License 2.0
~~~~~~~~~~~~~~~~~~

**License URL**: https://opensource.org/licenses/Apache-2.0

**Packages**:

- **grpcio** (v1.67.0): HTTP/2-based RPC framework
  
  *Homepage*: https://grpc.io
- **grpcio-tools** (v1.67.0): Protobuf code generator for gRPC
  
  *Homepage*: https://grpc.io


Apache License, Version 2.0
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Packages**:

- **asyncpg** (v0.29.0): An asyncio PostgreSQL driver


Apache-2.0
~~~~~~~~~~

**License URL**: https://opensource.org/licenses/Apache-2.0

**Packages**:

- **aiofiles** (v24.1.0): File support for asyncio.
- **coverage** (v7.6.1): Code coverage measurement for Python
  
  *Homepage*: https://github.com/nedbat/coveragepy
- **minio** (v7.2.12): MinIO Python SDK for Amazon S3 Compatible Cloud Storage
  
  *Homepage*: https://github.com/minio/minio-py
- **requests** (v2.32.3): Python HTTP for Humans.
  
  *Homepage*: https://requests.readthedocs.io
- **tzdata** (v2024.2): Provider of IANA time zone data
  
  *Homepage*: https://github.com/python/tzdata


Apache-2.0 license
~~~~~~~~~~~~~~~~~~

**Packages**:

- **bandit** (v1.7.10): Security oriented static analyser for python code.
  
  *Homepage*: https://bandit.readthedocs.io/


Artistic License
~~~~~~~~~~~~~~~~

**Packages**:

- **text-unidecode** (v1.3): The most basic Text::Unidecode port
  
  *Homepage*: https://github.com/kmike/text-unidecode/


BSD
~~~

**Packages**:

- **amqp** (v5.2.0): Low-level AMQP client for Python (fork of amqplib).
  
  *Homepage*: http://github.com/celery/py-amqp
- **billiard** (v4.2.1): Python multiprocessing fork with improvements and bugfixes
  
  *Homepage*: https://github.com/celery/billiard
- **httpie** (v3.2.4): HTTPie: modern, user-friendly command-line HTTP client for the API era.
  
  *Homepage*: https://httpie.io/
- **PySocks** (v1.7.1): A Python SOCKS client module. See https://github.com/Anorov/PySocks for more information.
  
  *Homepage*: https://github.com/Anorov/PySocks
- **smmap** (v6.0.0): A pure Python implementation of a sliding window memory map manager
  
  *Homepage*: https://github.com/gitpython-developers/smmap
- **vine** (v5.1.0): Python promises.
  
  *Homepage*: https://github.com/celery/vine
- **wrapt** (v1.16.0): Module for decorators, wrappers and monkey patching.
  
  *Homepage*: https://github.com/GrahamDumpleton/wrapt


BSD License
~~~~~~~~~~~

**Packages**:

- **gitdb** (v4.0.11): Git Object Database
  
  *Homepage*: https://github.com/gitpython-developers/gitdb


BSD, Public Domain
~~~~~~~~~~~~~~~~~~

**Packages**:

- **pycryptodome** (v3.20.0): Cryptographic library for Python
  
  *Homepage*: https://www.pycryptodome.org


BSD-2-Clause
~~~~~~~~~~~~

**License URL**: https://opensource.org/licenses/BSD-2-Clause

**Packages**:

- **Pygments** (v2.18.0): Pygments is a syntax highlighting package written in Python.
- **pytest-benchmark** (v4.0.0): A ``pytest`` fixture for benchmarking code. It will group the tests into rounds that are calibrated ...
  
  *Homepage*: https://github.com/ionelmc/pytest-benchmark


BSD-3-Clause
~~~~~~~~~~~~

**License URL**: https://opensource.org/licenses/BSD-3-Clause

**Packages**:

- **celery** (v5.4.0): Distributed Task Queue.
  
  *Homepage*: https://docs.celeryq.dev/
- **click** (v8.1.7): Composable command line interface toolkit
  
  *Homepage*: https://palletsprojects.com/p/click/
- **GitPython** (v3.1.43): GitPython is a Python library used to interact with Git repositories
  
  *Homepage*: https://github.com/gitpython-developers/GitPython
- **kombu** (v5.4.2): Messaging library for Python.
  
  *Homepage*: https://kombu.readthedocs.io
- **protobuf** (v5.28.3): Protocol Buffers
  
  *Homepage*: https://developers.google.com/protocol-buffers/
- **psutil** (v6.0.0): Cross-platform lib for process and system monitoring in Python.
  
  *Homepage*: https://github.com/giampaolo/psutil
- **pycparser** (v2.22): C parser in Python
  
  *Homepage*: https://github.com/eliben/pycparser
- **python-dotenv** (v1.0.1): Read key-value pairs from a .env file and set them as environment variables
  
  *Homepage*: https://github.com/theskumar/python-dotenv
- **websockets** (v13.1): An implementation of the WebSocket Protocol (RFC 6455 & 7692)


CC0-1.0 OR Apache-2.0
~~~~~~~~~~~~~~~~~~~~~

**Packages**:

- **blake3** (v0.4.1): Python bindings for the Rust blake3 crate
  
  *Homepage*: https://github.com/oconnor663/blake3-py


Copyright 2010 Pallets
~~~~~~~~~~~~~~~~~~~~~~

**Packages**:

- **MarkupSafe** (v3.0.2): Safely add untrusted strings to HTML/XML markup.


Dual License
~~~~~~~~~~~~

**Packages**:

- **python-dateutil** (v2.9.0.post0): Extensions to the standard Python datetime module
  
  *Homepage*: https://github.com/dateutil/dateutil


GPL
~~~

**Packages**:

- **iotop** (v0.6): Per process I/O bandwidth monitor
  
  *Homepage*: http://guichaz.free.fr/iotop


ISC License
~~~~~~~~~~~

**Packages**:

- **shellingham** (v1.5.4): Tool to Detect Surrounding Shell
  
  *Homepage*: https://github.com/sarugaku/shellingham


LGPL with exceptions
~~~~~~~~~~~~~~~~~~~~

**Packages**:

- **psycopg2** (v2.9.9): psycopg2 - Python-PostgreSQL Database Adapter
  
  *Homepage*: https://psycopg.org/


MIT
~~~

**License URL**: https://opensource.org/licenses/MIT

**Packages**:

- **alembic** (v1.13.3): A database migration tool for SQLAlchemy.
  
  *Homepage*: https://alembic.sqlalchemy.org
- **anyio** (v4.6.2): High level compatibility layer for multiple asynchronous event loop implementations
- **argon2-cffi-bindings** (v21.2.0): Low-level CFFI bindings for Argon2
  
  *Homepage*: https://github.com/hynek/argon2-cffi-bindings
- **brotlicffi** (v1.1.0.0): Python CFFI bindings to the Brotli library
  
  *Homepage*: https://github.com/python-hyper/brotlicffi
- **cffi** (v1.17.1): Foreign Function Interface for Python calling C code.
  
  *Homepage*: http://cffi.readthedocs.org
- **charset-normalizer** (v3.3.2): The Real First Universal Charset Detector. Open, modern and actively maintained alternative to Chard...
  
  *Homepage*: https://github.com/Ousret/charset_normalizer
- **click-didyoumean** (v0.3.1): Enables git-like *did-you-mean* feature in click
  
  *Homepage*: https://github.com/click-contrib/click-didyoumean
- **click-repl** (v0.3.0): REPL plugin for Click
  
  *Homepage*: https://github.com/untitaker/click-repl
- **Deprecated** (v1.2.14): Python @deprecated decorator to deprecate old python classes, functions or methods.
  
  *Homepage*: https://github.com/tantale/deprecated
- **factory_boy** (v3.3.1): A versatile test fixtures replacement based on thoughtbot's factory_bot for Ruby.
  
  *Homepage*: https://github.com/FactoryBoy/factory_boy
- **fastapi-cli** (v0.0.5): Run and manage FastAPI apps from the command line with FastAPI CLI. 🚀
- **h11** (v0.14.0): A pure-Python, bring-your-own-I/O implementation of HTTP/1.1
  
  *Homepage*: https://github.com/python-hyper/h11
- **httptools** (v0.6.1): A collection of framework independent HTTP protocol utils.
  
  *Homepage*: https://github.com/MagicStack/httptools
- **Mako** (v1.3.5): A super-fast templating language that borrows the best ideas from the existing templating languages.
  
  *Homepage*: https://www.makotemplates.org/
- **mypy** (v1.11.2): Optional static typing for Python
  
  *Homepage*: https://www.mypy-lang.org/
- **pip** (v24.0): The PyPA recommended tool for installing Python packages.
- **pluggy** (v1.5.0): plugin and hook calling mechanisms for python
  
  *Homepage*: https://github.com/pytest-dev/pluggy
- **poetry** (v1.8.4): Python dependency management and packaging made easy.
  
  *Homepage*: https://python-poetry.org/
- **pre_commit** (v4.0.1): A framework for managing and maintaining multi-language pre-commit hooks.
  
  *Homepage*: https://github.com/pre-commit/pre-commit
- **py-cpuinfo** (v9.0.0): Get CPU info with pure Python
  
  *Homepage*: https://github.com/workhorsy/py-cpuinfo
- **pydantic_core** (v2.23.4): Core functionality for Pydantic validation and serialization
  
  *Homepage*: https://github.com/pydantic/pydantic-core
- **pytest** (v8.3.3): pytest: simple powerful testing with Python
- **pytest-cov** (v5.0.0): Pytest plugin for measuring coverage.
  
  *Homepage*: https://github.com/pytest-dev/pytest-cov
- **pytest-mock** (v3.14.0): Thin-wrapper around the mock package for easier use with pytest
- **pytest-timeout** (v2.3.1): pytest plugin to abort hanging tests
  
  *Homepage*: https://github.com/pytest-dev/pytest-timeout
- **python-logstash** (v0.4.8): Python logging handler for Logstash.
  
  *Homepage*: https://github.com/vklochan/python-logstash
- **PyYAML** (v6.0.2): YAML parser and emitter for Python
  
  *Homepage*: https://pyyaml.org/
- **redis** (v5.1.1): Python client for Redis database and key-value store
  
  *Homepage*: https://github.com/redis/redis-py
- **rich** (v13.8.1): Render rich text, tables, progress bars, syntax highlighting, markdown and more to the terminal
  
  *Homepage*: https://github.com/Textualize/rich
- **ruff** (v0.7.3): An extremely fast Python linter and code formatter, written in Rust.
  
  *Homepage*: https://docs.astral.sh/ruff
- **six** (v1.16.0): Python 2 and 3 compatibility utilities
  
  *Homepage*: https://github.com/benjaminp/six
- **SQLAlchemy** (v2.0.34): Database Abstraction Library
  
  *Homepage*: https://www.sqlalchemy.org
- **toml** (v0.10.2): Python Library for Tom's Obvious, Minimal Language
  
  *Homepage*: https://github.com/uiri/toml
- **watchfiles** (v0.22.0): Simple, modern and high performance file watching and code reload in python.
  
  *Homepage*: https://github.com/samuelcolvin/watchfiles
- **wcwidth** (v0.2.13): Measures the displayed width of unicode strings in a terminal
  
  *Homepage*: https://github.com/jquast/wcwidth


MIT License
~~~~~~~~~~~

**Packages**:

- **Faker** (v25.9.2): Faker is a Python package that generates fake data for you.
  
  *Homepage*: https://github.com/joke2k/faker
- **greenlet** (v3.1.1): Lightweight in-process concurrent programming
  
  *Homepage*: https://greenlet.readthedocs.io/
- **mypy_extensions** (v1.0.0): Type system extensions for programs checked with the mypy type checker.
  
  *Homepage*: https://github.com/python/mypy_extensions
- **pytest-xdist** (v3.6.1): pytest xdist plugin for distributed testing, most importantly across multiple CPUs
- **uvloop** (v0.21.0): Fast implementation of asyncio event loop on top of libuv


MIT OR Apache-2.0
~~~~~~~~~~~~~~~~~

**Packages**:

- **sniffio** (v1.3.1): Sniff out which async library your code is running under


MPL-2.0
~~~~~~~

**License URL**: https://opensource.org/licenses/MPL-2.0

**Packages**:

- **certifi** (v2024.8.30): Python package for providing Mozilla's CA Bundle.
  
  *Homepage*: https://github.com/certifi/python-certifi
- **hypothesis** (v6.112.2): A library for property-based testing
  
  *Homepage*: https://hypothesis.works


New BSD
~~~~~~~

**Packages**:

- **click-plugins** (v1.1.1): An extension module for click to enable registering CLI commands via setuptools entry-points.
  
  *Homepage*: https://github.com/click-contrib/click-plugins


PSF-2.0
~~~~~~~

**Packages**:

- **distlib** (v0.3.8): Distribution utilities
  
  *Homepage*: https://github.com/pypa/distlib


PSFL
~~~~

**Packages**:

- **defusedxml** (v0.8.0rc2): XML bomb protection for Python stdlib modules
  
  *Homepage*: https://github.com/tiran/defusedxml


Summary
-------

**Total Packages**: 123

**License Distribution**:

- ****: 36 packages
- **MIT**: 35 packages
- **BSD-3-Clause**: 9 packages
- **BSD**: 7 packages
- **Apache-2.0**: 5 packages
- **MIT License**: 5 packages
- **Apache 2.0**: 3 packages
- **Apache 2**: 2 packages
- **MPL-2.0**: 2 packages
- **Apache License 2.0**: 2 packages
- **BSD-2-Clause**: 2 packages
- **Apache License, Version 2.0**: 1 packages
- **Apache-2.0 license**: 1 packages
- **CC0-1.0 OR Apache-2.0**: 1 packages
- **New BSD**: 1 packages
- **PSFL**: 1 packages
- **PSF-2.0**: 1 packages
- **BSD License**: 1 packages
- **GPL**: 1 packages
- **Copyright 2010 Pallets**: 1 packages
- **LGPL with exceptions**: 1 packages
- **BSD, Public Domain**: 1 packages
- **Dual License**: 1 packages
- **ISC License**: 1 packages
- **MIT OR Apache-2.0**: 1 packages
- **Artistic License**: 1 packages

*Generated on 2025-06-14 15:55:21*
