System Tools Licenses
=====================

This document lists all system tools and development dependencies used in TurdParty.

Development Tools
-----------------


Bash
~~~~

**License**: GPL 3.0

**License URL**: https://www.gnu.org/licenses/gpl-3.0.html

**Description**: Bash shell


curl
~~~~

**License**: MIT/X derivate license

**License URL**: https://curl.se/docs/copyright.html

**Description**: Command line HTTP client


Docker
~~~~~~

**License**: Apache License 2.0

**License URL**: https://github.com/docker/docker-ce/blob/master/LICENSE

**Description**: Docker containerization platform


Git
~~~

**License**: GPL 2.0

**License URL**: https://git-scm.com/about/free-and-open-source

**Description**: Git version control system


GNU Coreutils
~~~~~~~~~~~~~

**License**: GPL 3.0

**License URL**: https://www.gnu.org/licenses/gpl-3.0.html

**Description**: GNU core utilities (ls, cp, mv, etc.)


jq
~~

**License**: MIT License

**License URL**: https://github.com/stedolan/jq/blob/master/COPYING

**Description**: JSON processor


Nix Package Manager
~~~~~~~~~~~~~~~~~~~

**License**: LGPL 2.1

**License URL**: https://github.com/NixOS/nix/blob/master/COPYING

**Description**: Nix package manager and build system


Summary
-------

**Total Tools**: 7

*Generated on 2025-06-14 15:55:21*
