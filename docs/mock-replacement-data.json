{
  "metrics": {
    "total_test_files": 47,
    "total_mock_usage": 252,
    "high_priority_mocks": 231,
    "components_analyzed": 9,
    "avg_mocks_per_file": 5.361702127659575,
    "completion_percentage": 0,
<<<<<<< HEAD
    "last_analysis": "2025-06-14T15:05:17.860035"
=======
    "last_analysis": "2025-06-14T14:52:32.037284"
>>>>>>> 3f43360 (feat: Implement real storage layer testing with MinIO containers)
  },
  "components": {
    "Database Layer": {
      "files": [
        {
          "path": "tests/unit/test_models_validation.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 467,
          "last_modified": "2025-06-14T14:36:49.531613"
        }
      ],
      "total_mocks": 0,
      "total_files": 1,
      "priority": "High",
      "target_state": "TestContainers PostgreSQL"
    },
    "Storage Layer": {
      "files": [
        {
          "path": "tests/unit/test_file_injection_service.py",
          "mock_count": 1,
          "mocks": [
            {
              "pattern": "from unittest\\.mock import",
              "line": 10,
              "context": "from unittest.mock import patch"
            }
          ],
          "file_size": 296,
          "last_modified": "2025-06-14T14:36:49.531613"
        },
        {
          "path": "tests/unit/test_file_injection_service_enhanced.py",
          "mock_count": 56,
          "mocks": [
            {
              "pattern": "from unittest\\.mock import",
              "line": 9,
              "context": "from unittest.mock import MagicMock, mock_open, patch"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 51,
              "context": "patch(\"uuid.uuid4\", return_value=MagicMock(hex=\"test-uuid-123\")):"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 136,
              "context": "mock_injection = MagicMock()"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 173,
              "context": "MagicMock(id=\"1\", status=InjectionStatus.PENDING),"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 174,
              "context": "MagicMock(id=\"2\", status=InjectionStatus.COMPLETED),"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 175,
              "context": "MagicMock(id=\"3\", status=InjectionStatus.FAILED),"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 196,
              "context": "mock_injections = [MagicMock(id=str(i)) for i in range(20)]"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 217,
              "context": "mock_injection = MagicMock()"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 244,
              "context": "mock_injection = MagicMock()"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 290,
              "context": "mock_injection = MagicMock()"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 317,
              "context": "mock_injection = MagicMock()"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 332,
              "context": "mock_injection = MagicMock()"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 444,
              "context": "mock_injection = MagicMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 51,
              "context": "patch(\"uuid.uuid4\", return_value=MagicMock(hex=\"test-uuid-123\")):"
            },
            {
              "pattern": "Mock\\(",
              "line": 136,
              "context": "mock_injection = MagicMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 173,
              "context": "MagicMock(id=\"1\", status=InjectionStatus.PENDING),"
            },
            {
              "pattern": "Mock\\(",
              "line": 174,
              "context": "MagicMock(id=\"2\", status=InjectionStatus.COMPLETED),"
            },
            {
              "pattern": "Mock\\(",
              "line": 175,
              "context": "MagicMock(id=\"3\", status=InjectionStatus.FAILED),"
            },
            {
              "pattern": "Mock\\(",
              "line": 196,
              "context": "mock_injections = [MagicMock(id=str(i)) for i in range(20)]"
            },
            {
              "pattern": "Mock\\(",
              "line": 217,
              "context": "mock_injection = MagicMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 244,
              "context": "mock_injection = MagicMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 290,
              "context": "mock_injection = MagicMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 317,
              "context": "mock_injection = MagicMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 332,
              "context": "mock_injection = MagicMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 444,
              "context": "mock_injection = MagicMock()"
            },
            {
              "pattern": "mock_open\\(",
              "line": 48,
              "context": "with patch(\"builtins.open\", mock_open(read_data=sample_file_content)), \\"
            },
            {
              "pattern": "mock_open\\(",
              "line": 448,
              "context": "patch(\"builtins.open\", mock_open()) as mock_file:"
            },
            {
              "pattern": "mock_open\\(",
              "line": 471,
              "context": "patch(\"builtins.open\", mock_open()), \\"
            },
            {
              "pattern": "patch\\(",
              "line": 48,
              "context": "with patch(\"builtins.open\", mock_open(read_data=sample_file_content)), \\"
            },
            {
              "pattern": "patch\\(",
              "line": 49,
              "context": "patch(\"os.path.exists\", return_value=True), \\"
            },
            {
              "pattern": "patch\\(",
              "line": 50,
              "context": "patch(\"os.path.getsize\", return_value=len(sample_file_content)), \\"
            },
            {
              "pattern": "patch\\(",
              "line": 51,
              "context": "patch(\"uuid.uuid4\", return_value=MagicMock(hex=\"test-uuid-123\")):"
            },
            {
              "pattern": "patch\\(",
              "line": 447,
              "context": "with patch(\"json.dump\") as mock_dump, \\"
            },
            {
              "pattern": "patch\\(",
              "line": 448,
              "context": "patch(\"builtins.open\", mock_open()) as mock_file:"
            },
            {
              "pattern": "patch\\(",
              "line": 470,
              "context": "with patch(\"json.load\", return_value=mock_data), \\"
            },
            {
              "pattern": "patch\\(",
              "line": 471,
              "context": "patch(\"builtins.open\", mock_open()), \\"
            },
            {
              "pattern": "patch\\(",
              "line": 472,
              "context": "patch(\"os.path.exists\", return_value=True):"
            },
            {
              "pattern": "patch\\(",
              "line": 489,
              "context": "with patch(\"os.path.exists\", return_value=False):"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 141,
              "context": "with patch.object(service, \"_get_from_storage\", return_value=mock_injection):"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 159,
              "context": "with patch.object(service, \"_get_from_storage\", return_value=None):"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 178,
              "context": "with patch.object(service, \"_get_all_from_storage\", return_value=mock_injections):"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 198,
              "context": "with patch.object(service, \"_get_all_from_storage\", return_value=mock_injections):"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 221,
              "context": "with patch.object(service, \"_get_from_storage\", return_value=mock_injection), \\"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 222,
              "context": "patch.object(service, \"_save_to_storage\") as mock_save:"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 247,
              "context": "with patch.object(service, \"_get_from_storage\", return_value=mock_injection), \\"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 248,
              "context": "patch.object(service, \"_delete_from_storage\") as mock_delete, \\"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 249,
              "context": "patch.object(service, \"_cleanup_files\") as mock_cleanup:"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 267,
              "context": "with patch.object(service, \"_get_from_storage\", return_value=None):"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 296,
              "context": "with patch.object(service, \"_get_from_storage\", return_value=mock_injection), \\"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 297,
              "context": "patch.object(service, \"_copy_file_to_target\") as mock_copy, \\"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 298,
              "context": "patch.object(service, \"_set_file_permissions\") as mock_perms, \\"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 299,
              "context": "patch.object(service, \"update_status\") as mock_update:"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 321,
              "context": "with patch.object(service, \"_get_from_storage\", return_value=mock_injection), \\"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 336,
              "context": "with patch.object(service, \"_get_from_storage\", return_value=mock_injection), \\"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 337,
              "context": "patch.object(service, \"_copy_file_to_target\", side_effect=OSError(\"Copy failed\")), \\"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 338,
              "context": "patch.object(service, \"update_status\") as mock_update:"
            }
          ],
          "file_size": 494,
          "last_modified": "2025-06-14T14:36:49.531613"
        },
        {
          "path": "tests/unit/test_storage_layer_real.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 404,
          "last_modified": "2025-06-14T14:56:20.494919"
        },
        {
          "path": "tests/unit/test_file_upload_routes_real.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 368,
          "last_modified": "2025-06-14T14:57:11.553222"
        },
        {
          "path": "tests/integration/test_file_injection.py",
          "mock_count": 4,
          "mocks": [
            {
              "pattern": "from unittest\\.mock import",
              "line": 6,
              "context": "from unittest.mock import patch"
            },
            {
              "pattern": "patch\\(",
              "line": 36,
              "context": "with patch(\"tasks.vm_tasks.requests.post\") as mock_post, \\"
            },
            {
              "pattern": "patch\\(",
              "line": 37,
              "context": "patch(\"tasks.vm_tasks.requests.get\") as mock_get:"
            },
            {
              "pattern": "patch\\(",
              "line": 144,
              "context": "with patch(\"tasks.vm_tasks.inject_file_to_vm\") as mock_inject:"
            }
          ],
          "file_size": 170,
          "last_modified": "2025-06-14T14:36:49.529613"
        },
        {
          "path": "tests/integration/test_file_injection_api.py",
          "mock_count": 4,
          "mocks": [
            {
              "pattern": "from unittest\\.mock import",
              "line": 8,
              "context": "from unittest.mock import AsyncMock, patch"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 47,
              "context": "mock_instance = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 47,
              "context": "mock_instance = AsyncMock()"
            },
            {
              "pattern": "patch\\(",
              "line": 46,
              "context": "with patch(\"api.v1.routes.file_injection.ELKLogger\") as mock_class:"
            }
          ],
          "file_size": 319,
          "last_modified": "2025-06-14T14:36:49.529613"
        },
        {
          "path": "tests/integration/test_minio_integration.py",
          "mock_count": 2,
          "mocks": [
            {
              "pattern": "from unittest\\.mock import",
              "line": 11,
              "context": "from unittest.mock import patch"
            },
            {
              "pattern": "patch\\(",
              "line": 41,
              "context": "with patch(\"api.services.storage_service.Minio\", return_value=minio_client):"
            }
          ],
          "file_size": 370,
          "last_modified": "2025-06-14T14:36:49.529613"
        }
      ],
      "total_mocks": 67,
      "total_files": 7,
      "priority": "High",
      "target_state": "TestContainers MinIO"
    },
    "Caching Layer": {
      "files": [],
      "total_mocks": 0,
      "total_files": 0,
      "priority": "High",
      "target_state": "TestContainers Redis"
    },
    "VM Management": {
      "files": [
        {
          "path": "tests/unit/test_vm_injection_service.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 120,
          "last_modified": "2025-06-14T14:36:49.532613"
        },
        {
          "path": "tests/unit/test_vm_management_enhanced.py",
          "mock_count": 16,
          "mocks": [
            {
              "pattern": "from unittest\\.mock import",
              "line": 8,
              "context": "from unittest.mock import Mock, patch, AsyncMock"
            },
            {
              "pattern": "@patch\\(",
              "line": 289,
              "context": "@patch('services.api.src.routes.v1.vms.get_celery_app')"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 240,
              "context": "session.execute = AsyncMock()"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 242,
              "context": "session.commit = AsyncMock()"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 243,
              "context": "session.refresh = AsyncMock()"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 244,
              "context": "session.rollback = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 239,
              "context": "session = Mock(spec=AsyncSession)"
            },
            {
              "pattern": "Mock\\(",
              "line": 240,
              "context": "session.execute = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 241,
              "context": "session.add = Mock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 242,
              "context": "session.commit = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 243,
              "context": "session.refresh = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 244,
              "context": "session.rollback = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 250,
              "context": "celery_app = Mock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 251,
              "context": "task = Mock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 295,
              "context": "mock_result = Mock()"
            },
            {
              "pattern": "patch\\(",
              "line": 289,
              "context": "@patch('services.api.src.routes.v1.vms.get_celery_app')"
            }
          ],
          "file_size": 383,
          "last_modified": "2025-06-14T14:36:49.532613"
        },
        {
          "path": "tests/unit/test_vm_management_routes.py",
          "mock_count": 7,
          "mocks": [
            {
              "pattern": "from unittest\\.mock import",
              "line": 6,
              "context": "from unittest.mock import Mock, AsyncMock, patch"
            },
            {
              "pattern": "@patch\\(",
              "line": 276,
              "context": "@patch('api.v1.routes.vm_management.vm_metrics_service')"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 280,
              "context": "mock_service.stream_vm_metrics = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 280,
              "context": "mock_service.stream_vm_metrics = AsyncMock()"
            },
            {
              "pattern": "patch\\(",
              "line": 276,
              "context": "@patch('api.v1.routes.vm_management.vm_metrics_service')"
            },
            {
              "pattern": "patch\\(",
              "line": 402,
              "context": "with patch('api.v1.routes.vm_management.mock_vms', side_effect=Exception(\"Database error\")):"
            },
            {
              "pattern": "patch\\(",
              "line": 409,
              "context": "with patch('api.services.vm_metrics_service.vm_metrics_service.stream_vm_metrics',"
            }
          ],
          "file_size": 451,
          "last_modified": "2025-06-14T14:36:49.532613"
        },
        {
          "path": "tests/unit/test_vm_metrics_service.py",
          "mock_count": 13,
          "mocks": [
            {
              "pattern": "from unittest\\.mock import",
              "line": 7,
              "context": "from unittest.mock import Mock, AsyncMock, patch, MagicMock"
            },
            {
              "pattern": "Mock\\(",
              "line": 25,
              "context": "mock_client = Mock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 26,
              "context": "mock_container = Mock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 58,
              "context": "mock_container.exec_run.return_value = Mock("
            },
            {
              "pattern": "Mock\\(",
              "line": 70,
              "context": "mock_docker.return_value = Mock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 256,
              "context": "mock_container = Mock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 257,
              "context": "mock_container.exec_run.return_value = Mock("
            },
            {
              "pattern": "Mock\\(",
              "line": 278,
              "context": "mock_container = Mock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 279,
              "context": "mock_container.exec_run.return_value = Mock("
            },
            {
              "pattern": "Mock\\(",
              "line": 290,
              "context": "mock_container = Mock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 305,
              "context": "mock_container = Mock()"
            },
            {
              "pattern": "patch\\(",
              "line": 69,
              "context": "with patch('docker.from_env') as mock_docker:"
            },
            {
              "pattern": "patch\\(",
              "line": 80,
              "context": "with patch('docker.from_env') as mock_docker:"
            }
          ],
          "file_size": 454,
          "last_modified": "2025-06-14T14:47:38.763680"
        },
        {
          "path": "tests/unit/test_vm_management_real.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 349,
          "last_modified": "2025-06-14T14:46:09.215451"
        },
        {
          "path": "tests/unit/test_vm_management_routes_real.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 400,
          "last_modified": "2025-06-14T14:47:06.602560"
        },
        {
          "path": "tests/integration/test_real_vagrant_vms.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 486,
          "last_modified": "2025-06-14T14:36:49.530613"
        },
        {
          "path": "tests/integration/test_real_vm_operations.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 470,
          "last_modified": "2025-06-14T14:36:49.530613"
        },
        {
          "path": "tests/integration/test_real_vm_workflow.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 448,
          "last_modified": "2025-06-14T14:36:49.530613"
        },
        {
          "path": "tests/integration/test_vm_injection.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 599,
          "last_modified": "2025-06-14T14:36:49.530613"
        },
        {
          "path": "tests/integration/test_vm_service_integration.py",
          "mock_count": 49,
          "mocks": [
            {
              "pattern": "from unittest\\.mock import",
              "line": 10,
              "context": "from unittest.mock import AsyncMock, MagicMock, patch"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 24,
              "context": "mock_service.create_vm = AsyncMock(return_value={"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 33,
              "context": "mock_service.get_vm_status = AsyncMock(return_value={"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 41,
              "context": "mock_service.start_vm = AsyncMock(return_value={\"status\": \"starting\"})"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 42,
              "context": "mock_service.stop_vm = AsyncMock(return_value={\"status\": \"stopping\"})"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 43,
              "context": "mock_service.delete_vm = AsyncMock(return_value={\"status\": \"deleted\"})"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 46,
              "context": "mock_service.list_vms = AsyncMock(return_value={"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 128,
              "context": "mock_service.get_vm_metrics = AsyncMock(return_value={"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 152,
              "context": "mock_service.stream_vm_metrics = AsyncMock(return_value=mock_stream())"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 195,
              "context": "mock_service.upload_file = AsyncMock(return_value={"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 203,
              "context": "mock_service.download_file = AsyncMock(return_value={"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 210,
              "context": "mock_service.list_files = AsyncMock(return_value={"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 278,
              "context": "mock_service.execute_command = AsyncMock(return_value={"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 288,
              "context": "mock_service.get_command_status = AsyncMock(return_value={"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 341,
              "context": "mock_vm_service.create_vm = AsyncMock(return_value={\"vm_id\": \"workflow-vm-123\"})"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 342,
              "context": "mock_vm_service.start_vm = AsyncMock(return_value={\"status\": \"running\"})"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 343,
              "context": "mock_file_service.upload_file = AsyncMock(return_value={\"file_id\": \"file-123\"})"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 344,
              "context": "mock_command_service.execute_command = AsyncMock(return_value={\"exit_code\": 0})"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 345,
              "context": "mock_metrics_service.get_vm_metrics = AsyncMock(return_value={\"cpu_percent\": 25.0})"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 346,
              "context": "mock_vm_service.stop_vm = AsyncMock(return_value={\"status\": \"stopped\"})"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 347,
              "context": "mock_vm_service.delete_vm = AsyncMock(return_value={\"status\": \"deleted\"})"
            },
            {
              "pattern": "Mock\\(",
              "line": 24,
              "context": "mock_service.create_vm = AsyncMock(return_value={"
            },
            {
              "pattern": "Mock\\(",
              "line": 33,
              "context": "mock_service.get_vm_status = AsyncMock(return_value={"
            },
            {
              "pattern": "Mock\\(",
              "line": 41,
              "context": "mock_service.start_vm = AsyncMock(return_value={\"status\": \"starting\"})"
            },
            {
              "pattern": "Mock\\(",
              "line": 42,
              "context": "mock_service.stop_vm = AsyncMock(return_value={\"status\": \"stopping\"})"
            },
            {
              "pattern": "Mock\\(",
              "line": 43,
              "context": "mock_service.delete_vm = AsyncMock(return_value={\"status\": \"deleted\"})"
            },
            {
              "pattern": "Mock\\(",
              "line": 46,
              "context": "mock_service.list_vms = AsyncMock(return_value={"
            },
            {
              "pattern": "Mock\\(",
              "line": 128,
              "context": "mock_service.get_vm_metrics = AsyncMock(return_value={"
            },
            {
              "pattern": "Mock\\(",
              "line": 152,
              "context": "mock_service.stream_vm_metrics = AsyncMock(return_value=mock_stream())"
            },
            {
              "pattern": "Mock\\(",
              "line": 195,
              "context": "mock_service.upload_file = AsyncMock(return_value={"
            },
            {
              "pattern": "Mock\\(",
              "line": 203,
              "context": "mock_service.download_file = AsyncMock(return_value={"
            },
            {
              "pattern": "Mock\\(",
              "line": 210,
              "context": "mock_service.list_files = AsyncMock(return_value={"
            },
            {
              "pattern": "Mock\\(",
              "line": 278,
              "context": "mock_service.execute_command = AsyncMock(return_value={"
            },
            {
              "pattern": "Mock\\(",
              "line": 288,
              "context": "mock_service.get_command_status = AsyncMock(return_value={"
            },
            {
              "pattern": "Mock\\(",
              "line": 341,
              "context": "mock_vm_service.create_vm = AsyncMock(return_value={\"vm_id\": \"workflow-vm-123\"})"
            },
            {
              "pattern": "Mock\\(",
              "line": 342,
              "context": "mock_vm_service.start_vm = AsyncMock(return_value={\"status\": \"running\"})"
            },
            {
              "pattern": "Mock\\(",
              "line": 343,
              "context": "mock_file_service.upload_file = AsyncMock(return_value={\"file_id\": \"file-123\"})"
            },
            {
              "pattern": "Mock\\(",
              "line": 344,
              "context": "mock_command_service.execute_command = AsyncMock(return_value={\"exit_code\": 0})"
            },
            {
              "pattern": "Mock\\(",
              "line": 345,
              "context": "mock_metrics_service.get_vm_metrics = AsyncMock(return_value={\"cpu_percent\": 25.0})"
            },
            {
              "pattern": "Mock\\(",
              "line": 346,
              "context": "mock_vm_service.stop_vm = AsyncMock(return_value={\"status\": \"stopped\"})"
            },
            {
              "pattern": "Mock\\(",
              "line": 347,
              "context": "mock_vm_service.delete_vm = AsyncMock(return_value={\"status\": \"deleted\"})"
            },
            {
              "pattern": "patch\\(",
              "line": 22,
              "context": "with patch('api.services.vm_service.VMService') as mock_service:"
            },
            {
              "pattern": "patch\\(",
              "line": 126,
              "context": "with patch('api.services.vm_metrics_service.VMMetricsService') as mock_service:"
            },
            {
              "pattern": "patch\\(",
              "line": 193,
              "context": "with patch('api.services.vm_file_service.VMFileService') as mock_service:"
            },
            {
              "pattern": "patch\\(",
              "line": 276,
              "context": "with patch('api.services.vm_command_service.VMCommandService') as mock_service:"
            },
            {
              "pattern": "patch\\(",
              "line": 335,
              "context": "with patch('api.services.vm_service.VMService') as mock_vm_service, \\"
            },
            {
              "pattern": "patch\\(",
              "line": 336,
              "context": "patch('api.services.vm_file_service.VMFileService') as mock_file_service, \\"
            },
            {
              "pattern": "patch\\(",
              "line": 337,
              "context": "patch('api.services.vm_command_service.VMCommandService') as mock_command_service, \\"
            },
            {
              "pattern": "patch\\(",
              "line": 338,
              "context": "patch('api.services.vm_metrics_service.VMMetricsService') as mock_metrics_service:"
            }
          ],
          "file_size": 387,
          "last_modified": "2025-06-14T14:36:49.530613"
        }
      ],
      "total_mocks": 85,
      "total_files": 11,
      "priority": "High",
      "target_state": "Real Docker API"
    },
    "File Injection": {
      "files": [
        {
          "path": "tests/unit/test_upload.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 21,
          "last_modified": "2025-06-14T14:36:49.532613"
        },
        {
          "path": "tests/integration/test_fibratus_real_injection.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 734,
          "last_modified": "2025-06-14T14:36:49.529613"
        }
      ],
      "total_mocks": 0,
      "total_files": 2,
      "priority": "High",
      "target_state": "Real file operations"
    },
    "Worker Services": {
      "files": [
        {
          "path": "tests/unit/test_worker_services.py",
          "mock_count": 33,
          "mocks": [
            {
              "pattern": "from unittest\\.mock import",
              "line": 11,
              "context": "from unittest.mock import MagicMock, patch"
            },
            {
              "pattern": "@patch\\(",
              "line": 61,
              "context": "@patch('services.workers.tasks.file_operations.Minio')"
            },
            {
              "pattern": "@patch\\(",
              "line": 91,
              "context": "@patch('services.workers.tasks.file_operations.Minio')"
            },
            {
              "pattern": "@patch\\(",
              "line": 223,
              "context": "@patch('services.workers.tasks.vm_management.subprocess.run')"
            },
            {
              "pattern": "@patch\\(",
              "line": 243,
              "context": "@patch('services.workers.tasks.vm_management.subprocess.run')"
            },
            {
              "pattern": "@patch\\(",
              "line": 262,
              "context": "@patch('services.workers.tasks.vm_management.subprocess.run')"
            },
            {
              "pattern": "@patch\\(",
              "line": 281,
              "context": "@patch('services.workers.tasks.vm_management.subprocess.run')"
            },
            {
              "pattern": "@patch\\(",
              "line": 299,
              "context": "@patch('services.workers.tasks.vm_management.subprocess.run')"
            },
            {
              "pattern": "@patch\\(",
              "line": 332,
              "context": "@patch('services.workers.tasks.injection_tasks.subprocess.run')"
            },
            {
              "pattern": "@patch\\(",
              "line": 351,
              "context": "@patch('services.workers.tasks.injection_tasks.subprocess.run')"
            },
            {
              "pattern": "@patch\\(",
              "line": 370,
              "context": "@patch('services.workers.tasks.injection_tasks.time.sleep')"
            },
            {
              "pattern": "@patch\\(",
              "line": 371,
              "context": "@patch('services.workers.tasks.injection_tasks.subprocess.run')"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 17,
              "context": "'celery': MagicMock(),"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 18,
              "context": "'services.workers.celery_app': MagicMock(),"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 44,
              "context": "mock_client = MagicMock()"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 45,
              "context": "mock_client.get_object.return_value = MagicMock()"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 71,
              "context": "mock_response = MagicMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 17,
              "context": "'celery': MagicMock(),"
            },
            {
              "pattern": "Mock\\(",
              "line": 18,
              "context": "'services.workers.celery_app': MagicMock(),"
            },
            {
              "pattern": "Mock\\(",
              "line": 44,
              "context": "mock_client = MagicMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 45,
              "context": "mock_client.get_object.return_value = MagicMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 71,
              "context": "mock_response = MagicMock()"
            },
            {
              "pattern": "patch\\(",
              "line": 61,
              "context": "@patch('services.workers.tasks.file_operations.Minio')"
            },
            {
              "pattern": "patch\\(",
              "line": 91,
              "context": "@patch('services.workers.tasks.file_operations.Minio')"
            },
            {
              "pattern": "patch\\(",
              "line": 223,
              "context": "@patch('services.workers.tasks.vm_management.subprocess.run')"
            },
            {
              "pattern": "patch\\(",
              "line": 243,
              "context": "@patch('services.workers.tasks.vm_management.subprocess.run')"
            },
            {
              "pattern": "patch\\(",
              "line": 262,
              "context": "@patch('services.workers.tasks.vm_management.subprocess.run')"
            },
            {
              "pattern": "patch\\(",
              "line": 281,
              "context": "@patch('services.workers.tasks.vm_management.subprocess.run')"
            },
            {
              "pattern": "patch\\(",
              "line": 299,
              "context": "@patch('services.workers.tasks.vm_management.subprocess.run')"
            },
            {
              "pattern": "patch\\(",
              "line": 332,
              "context": "@patch('services.workers.tasks.injection_tasks.subprocess.run')"
            },
            {
              "pattern": "patch\\(",
              "line": 351,
              "context": "@patch('services.workers.tasks.injection_tasks.subprocess.run')"
            },
            {
              "pattern": "patch\\(",
              "line": 370,
              "context": "@patch('services.workers.tasks.injection_tasks.time.sleep')"
            },
            {
              "pattern": "patch\\(",
              "line": 371,
              "context": "@patch('services.workers.tasks.injection_tasks.subprocess.run')"
            }
          ],
          "file_size": 406,
          "last_modified": "2025-06-14T14:36:49.532613"
        }
      ],
      "total_mocks": 33,
      "total_files": 1,
      "priority": "High",
      "target_state": "Real Celery workers"
    },
    "Logging & Monitoring": {
      "files": [
        {
          "path": "tests/unit/test_elk_logger.py",
          "mock_count": 21,
          "mocks": [
            {
              "pattern": "from unittest\\.mock import",
              "line": 8,
              "context": "from unittest.mock import AsyncMock, MagicMock, patch"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 21,
              "context": "mock_es = MagicMock()"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 30,
              "context": "return MagicMock()"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 22,
              "context": "mock_es.index = AsyncMock()"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 23,
              "context": "mock_es.search = AsyncMock()"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 24,
              "context": "mock_es.close = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 21,
              "context": "mock_es = MagicMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 22,
              "context": "mock_es.index = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 23,
              "context": "mock_es.search = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 24,
              "context": "mock_es.close = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 30,
              "context": "return MagicMock()"
            },
            {
              "pattern": "patch\\(",
              "line": 35,
              "context": "with patch(\"api.services.elk_logger.AsyncElasticsearch\") as mock_es_class, \\"
            },
            {
              "pattern": "patch\\(",
              "line": 36,
              "context": "patch(\"api.services.elk_logger.logstash.TCPLogstashHandler\") as mock_handler_class:"
            },
            {
              "pattern": "patch\\(",
              "line": 168,
              "context": "with patch(\"platform.platform\", return_value=\"Linux-5.4.0\"), \\"
            },
            {
              "pattern": "patch\\(",
              "line": 169,
              "context": "patch(\"platform.python_version\", return_value=\"3.10.0\"), \\"
            },
            {
              "pattern": "patch\\(",
              "line": 170,
              "context": "patch(\"platform.node\", return_value=\"test-host\"), \\"
            },
            {
              "pattern": "patch\\(",
              "line": 171,
              "context": "patch(\"psutil.cpu_count\", return_value=4), \\"
            },
            {
              "pattern": "patch\\(",
              "line": 172,
              "context": "patch(\"psutil.virtual_memory\") as mock_memory, \\"
            },
            {
              "pattern": "patch\\(",
              "line": 173,
              "context": "patch(\"psutil.disk_usage\") as mock_disk:"
            },
            {
              "pattern": "patch\\(",
              "line": 192,
              "context": "with patch(\"platform.platform\", side_effect=Exception(\"Platform error\")):"
            },
            {
              "pattern": "patch\\.object\\(",
              "line": 115,
              "context": "with patch.object(elk_logger, \"_get_system_info\", return_value={\"platform\": \"Linux\"}):"
            }
          ],
          "file_size": 303,
          "last_modified": "2025-06-14T14:36:49.531613"
        },
        {
          "path": "tests/integration/test_ecs_logging.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 372,
          "last_modified": "2025-06-14T14:36:49.529613"
        },
        {
          "path": "tests/integration/test_elk_integration.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 359,
          "last_modified": "2025-06-14T14:36:49.529613"
        }
      ],
      "total_mocks": 21,
      "total_files": 3,
      "priority": "Medium",
      "target_state": "TestContainers ELK"
    },
    "API Layer": {
      "files": [
        {
          "path": "tests/unit/test_api_main.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 136,
          "last_modified": "2025-06-14T14:36:49.531613"
        },
        {
          "path": "tests/unit/test_route_registration.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 260,
          "last_modified": "2025-06-14T14:36:49.532613"
        },
        {
          "path": "tests/unit/test_routes.py",
          "mock_count": 45,
          "mocks": [
            {
              "pattern": "from unittest\\.mock import",
              "line": 9,
              "context": "from unittest.mock import AsyncMock, MagicMock, patch"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 48,
              "context": "mock_service = MagicMock()"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 60,
              "context": "mock_logger = MagicMock()"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 75,
              "context": "mock_file_injection_service.create_injection.return_value = MagicMock(**injection_response_data)"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 122,
              "context": "mock_file_injection_service.get_by_id.return_value = MagicMock(**injection_response_data)"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 159,
              "context": "mock_file_injection_service.get_all.return_value = [MagicMock(**injection_response_data)]"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 197,
              "context": "mock_file_injection_service.get_status.return_value = MagicMock(**injection_status_data)"
            },
            {
              "pattern": "MagicMock\\(",
              "line": 219,
              "context": "mock_file_injection_service.process_injection.return_value = MagicMock(**injection_status_data)"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 49,
              "context": "mock_service.create_injection = AsyncMock()"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 50,
              "context": "mock_service.get_by_id = AsyncMock()"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 51,
              "context": "mock_service.get_all = AsyncMock()"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 52,
              "context": "mock_service.get_status = AsyncMock()"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 53,
              "context": "mock_service.process_injection = AsyncMock()"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 54,
              "context": "mock_service.delete = AsyncMock()"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 61,
              "context": "mock_logger.log_file_injection_event = AsyncMock()"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 62,
              "context": "mock_logger.log_installation_base = AsyncMock()"
            },
            {
              "pattern": "AsyncMock\\(",
              "line": 63,
              "context": "mock_logger.log_system_event = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 48,
              "context": "mock_service = MagicMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 49,
              "context": "mock_service.create_injection = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 50,
              "context": "mock_service.get_by_id = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 51,
              "context": "mock_service.get_all = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 52,
              "context": "mock_service.get_status = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 53,
              "context": "mock_service.process_injection = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 54,
              "context": "mock_service.delete = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 60,
              "context": "mock_logger = MagicMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 61,
              "context": "mock_logger.log_file_injection_event = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 62,
              "context": "mock_logger.log_installation_base = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 63,
              "context": "mock_logger.log_system_event = AsyncMock()"
            },
            {
              "pattern": "Mock\\(",
              "line": 75,
              "context": "mock_file_injection_service.create_injection.return_value = MagicMock(**injection_response_data)"
            },
            {
              "pattern": "Mock\\(",
              "line": 122,
              "context": "mock_file_injection_service.get_by_id.return_value = MagicMock(**injection_response_data)"
            },
            {
              "pattern": "Mock\\(",
              "line": 159,
              "context": "mock_file_injection_service.get_all.return_value = [MagicMock(**injection_response_data)]"
            },
            {
              "pattern": "Mock\\(",
              "line": 197,
              "context": "mock_file_injection_service.get_status.return_value = MagicMock(**injection_status_data)"
            },
            {
              "pattern": "Mock\\(",
              "line": 219,
              "context": "mock_file_injection_service.process_injection.return_value = MagicMock(**injection_status_data)"
            },
            {
              "pattern": "patch\\(",
              "line": 77,
              "context": "with patch(\"api.v1.routes.file_injection.file_injection_service\", mock_file_injection_service), \\"
            },
            {
              "pattern": "patch\\(",
              "line": 78,
              "context": "patch(\"api.v1.routes.file_injection.elk_logger\", mock_elk_logger):"
            },
            {
              "pattern": "patch\\(",
              "line": 124,
              "context": "with patch(\"api.v1.routes.file_injection.file_injection_service\", mock_file_injection_service):"
            },
            {
              "pattern": "patch\\(",
              "line": 144,
              "context": "with patch(\"api.v1.routes.file_injection.file_injection_service\", mock_file_injection_service):"
            },
            {
              "pattern": "patch\\(",
              "line": 161,
              "context": "with patch(\"api.v1.routes.file_injection.file_injection_service\", mock_file_injection_service):"
            },
            {
              "pattern": "patch\\(",
              "line": 180,
              "context": "with patch(\"api.v1.routes.file_injection.file_injection_service\", mock_file_injection_service):"
            },
            {
              "pattern": "patch\\(",
              "line": 199,
              "context": "with patch(\"api.v1.routes.file_injection.file_injection_service\", mock_file_injection_service):"
            },
            {
              "pattern": "patch\\(",
              "line": 221,
              "context": "with patch(\"api.v1.routes.file_injection.file_injection_service\", mock_file_injection_service), \\"
            },
            {
              "pattern": "patch\\(",
              "line": 222,
              "context": "patch(\"api.v1.routes.file_injection.elk_logger\", mock_elk_logger):"
            },
            {
              "pattern": "patch\\(",
              "line": 244,
              "context": "with patch(\"api.v1.routes.file_injection.file_injection_service\", mock_file_injection_service), \\"
            },
            {
              "pattern": "patch\\(",
              "line": 245,
              "context": "patch(\"api.v1.routes.file_injection.elk_logger\", mock_elk_logger):"
            },
            {
              "pattern": "patch\\(",
              "line": 270,
              "context": "response = client.patch(\"/health\")"
            }
          ],
          "file_size": 284,
          "last_modified": "2025-06-14T14:36:49.532613"
        },
        {
          "path": "tests/unit/test_api_layer_real.py",
          "mock_count": 1,
          "mocks": [
            {
              "pattern": "patch\\(",
              "line": 178,
              "context": "response = await api_client.patch(\"/health\")"
            }
          ],
          "file_size": 381,
          "last_modified": "2025-06-14T15:02:57.067568"
        },
        {
          "path": "tests/unit/test_api_routes_real.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 354,
          "last_modified": "2025-06-14T15:03:45.186917"
        },
        {
          "path": "tests/integration/test_documentation_endpoints.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 353,
          "last_modified": "2025-06-14T14:36:49.529613"
        }
      ],
      "total_mocks": 46,
      "total_files": 6,
      "priority": "High",
      "target_state": "Real service calls"
    },
    "Other": {
      "files": [
        {
          "path": "tests/unit/test_basic.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 285,
          "last_modified": "2025-06-14T14:36:49.531613"
        },
        {
          "path": "tests/unit/test_performance_edge_cases.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 416,
          "last_modified": "2025-06-14T14:36:49.531613"
        },
        {
          "path": "tests/integration/test_app_downloader.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 328,
          "last_modified": "2025-06-14T14:36:49.529613"
        },
        {
          "path": "tests/integration/test_benchmark_persistence.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 447,
          "last_modified": "2025-06-14T14:36:49.529613"
        },
        {
          "path": "tests/integration/test_complete_workflow.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 772,
          "last_modified": "2025-06-14T14:36:49.529613"
        },
        {
          "path": "tests/integration/test_comprehensive_suite.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 586,
          "last_modified": "2025-06-14T14:36:49.529613"
        },
        {
          "path": "tests/integration/test_infrastructure_ready.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 114,
          "last_modified": "2025-06-14T14:36:49.529613"
        },
        {
          "path": "tests/integration/test_parallel_analysis.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 541,
          "last_modified": "2025-06-14T14:36:49.529613"
        },
        {
          "path": "tests/integration/test_performance_load.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 613,
          "last_modified": "2025-06-14T14:36:49.530613"
        },
        {
          "path": "tests/integration/test_security_edge_cases.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 607,
          "last_modified": "2025-06-14T14:36:49.530613"
        },
        {
          "path": "tests/integration/test_setup_verification.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 300,
          "last_modified": "2025-06-14T14:36:49.530613"
        },
        {
          "path": "tests/integration/test_traefik_routing.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 367,
          "last_modified": "2025-06-14T14:36:49.530613"
        },
        {
          "path": "tests/integration/test_websocket_integration.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 416,
          "last_modified": "2025-06-14T14:36:49.530613"
        },
        {
          "path": "tests/integration/run_basic_test.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 229,
          "last_modified": "2025-06-14T14:36:49.528613"
        },
        {
          "path": "tests/e2e/test_complete_workflow.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 384,
          "last_modified": "2025-06-14T14:36:49.528613"
        },
        {
          "path": "tests/e2e/test_documentation_e2e.py",
          "mock_count": 0,
          "mocks": [],
          "file_size": 329,
          "last_modified": "2025-06-14T14:36:49.528613"
        }
      ],
      "total_mocks": 0,
      "total_files": 16,
      "priority": "Low",
      "target_state": "TBD"
    }
  },
<<<<<<< HEAD
  "generated_at": "2025-06-14T15:05:17.860618"
=======
  "generated_at": "2025-06-14T14:52:32.037547"
>>>>>>> 3f43360 (feat: Implement real storage layer testing with MinIO containers)
}