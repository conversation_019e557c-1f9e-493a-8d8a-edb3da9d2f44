server {
    listen 80;
    server_name localhost;
    
    # Main documentation root
    root /usr/share/nginx/html;
    index index.html index.htm;
    
    # Enable gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https:; img-src 'self' data: https:; font-src 'self' data: https:;" always;
    
    # Main documentation
    location / {
        try_files $uri $uri/ $uri/index.html =404;
        
        # Set proper MIME types
        location ~* \.html$ {
            add_header Content-Type "text/html; charset=utf-8";
            add_header Cache-Control "public, max-age=300"; # 5 minutes
        }
        
        location ~* \.css$ {
            add_header Content-Type "text/css; charset=utf-8";
            add_header Cache-Control "public, max-age=3600"; # 1 hour
        }
        
        location ~* \.js$ {
            add_header Content-Type "application/javascript; charset=utf-8";
            add_header Cache-Control "public, max-age=3600"; # 1 hour
        }
        
        location ~* \.json$ {
            add_header Content-Type "application/json; charset=utf-8";
            add_header Cache-Control "public, max-age=300"; # 5 minutes
        }
        
        # Cache static assets longer
        location ~* \.(png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            add_header Cache-Control "public, max-age=86400"; # 24 hours
        }
    }
    
    # API documentation (Swagger UI)
    location /api/ {
        try_files $uri $uri/ $uri/index.html =404;
        
        # Allow iframe for Swagger UI
        add_header X-Frame-Options "SAMEORIGIN";
        
        # CORS headers for API docs
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range";
    }
    
    # Static assets directory
    location /_static/ {
        try_files $uri =404;
        
        # Cache static assets
        add_header Cache-Control "public, max-age=3600"; # 1 hour
        
        # CORS for static assets
        add_header Access-Control-Allow-Origin "*";
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Status endpoint (simple response)
    location /status {
        return 200 "Documentation service operational\n";
        add_header Content-Type text/plain;
    }
    
    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root /usr/share/nginx/html;
        internal;
    }
    
    location = /50x.html {
        root /usr/share/nginx/html;
        internal;
    }
    
    # Deny access to hidden files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Deny access to backup files
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Logging
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
}
