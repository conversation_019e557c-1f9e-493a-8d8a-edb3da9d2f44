TurdParty Platform Architecture
================================

Overview
--------

TurdParty is a comprehensive malware analysis platform built on a modern microservices architecture. The platform provides automated file processing, real-time virtual machine monitoring, and advanced threat detection capabilities through a complete end-to-end workflow.

.. mermaid::

   graph TB
       subgraph "Phase 1: Core Infrastructure"
           API[FastAPI Server]
           DB[PostgreSQL Database]
           REDIS[Redis Queue]
           MINIO[MinIO Storage]
       end
       
       subgraph "Phase 2: Processing Pipeline"
           WF[Workflow Orchestrator]
           FILE[File Operations Worker]
           VM[VM Management Worker]
           INJ[File Injection Worker]
           POOL[VM Pool Manager]
       end
       
       subgraph "Phase 3: Data Pipeline"
           ELK[ELK Integration Worker]
           AGENT[VM Monitoring Agent]
           ES[Elasticsearch]
           LS[Logstash]
           KB[Kibana]
       end
       
       API --> WF
       WF --> FILE
       WF --> VM
       WF --> INJ
       WF --> POOL
       WF --> ELK
       
       FILE --> MINIO
       VM --> POOL
       INJ --> AGENT
       ELK --> LS
       LS --> ES
       ES --> KB

Core Components
---------------

API Layer
~~~~~~~~~~

**FastAPI Server** (``services/api/``)
   - RESTful API endpoints for file upload and workflow management
   - Real-time WebSocket connections for monitoring
   - Authentication and authorization
   - Request validation and error handling
   - OpenAPI documentation generation

**React Frontend** (``services/frontend/``)
   - Modern web interface for file upload and analysis
   - Real-time workflow status monitoring
   - Integration with Kibana dashboards
   - Responsive design with dark mode support

Worker Services
~~~~~~~~~~~~~~~

**Workflow Orchestrator** (``services/workers/tasks/workflow_orchestrator.py``)
   - Coordinates complete file processing pipeline
   - Manages task dependencies and error handling
   - Implements 30-minute VM lifecycle enforcement
   - Streams workflow events to ELK stack

**File Operations Worker** (``services/workers/tasks/file_operations.py``)
   - Downloads files from MinIO storage
   - Validates file integrity and format
   - Handles file metadata and hashing (Blake3)
   - Manages temporary file storage

**VM Management Worker** (``services/workers/tasks/vm_management.py``)
   - Creates and manages Docker and Vagrant VMs
   - Handles VM lifecycle (create, monitor, terminate)
   - Integrates with VM pool management
   - Supports multiple VM templates

**File Injection Worker** (``services/workers/tasks/injection_tasks.py``)
   - Injects files into running VMs
   - Supports Docker exec and SSH-based injection
   - Manages file permissions and paths
   - Tracks injection status and errors

**VM Pool Manager** (``services/workers/tasks/vm_pool_manager.py``)
   - Maintains pool of 2-10 ready VMs
   - Automatic VM provisioning and cleanup
   - Template rotation and load balancing
   - Pool health monitoring and metrics

**ELK Integration Worker** (``services/workers/tasks/elk_integration.py``)
   - Streams workflow events to ELK stack
   - Handles VM metrics and monitoring data
   - Manages ECS-compliant data formatting
   - Provides ELK stack health monitoring

Testing Infrastructure
~~~~~~~~~~~~~~~~~~~~~~

**Playwright E2E Testing** (``docker/playwright/``)
   - Containerized browser-based testing environment
   - Automated end-to-end workflow validation
   - Multi-browser testing (Chromium, Firefox, WebKit)
   - Integrated with Traefik service discovery
   - Web dashboard for test management and reporting

Data Storage
~~~~~~~~~~~~

**PostgreSQL Database**
   - Workflow job tracking and status
   - File upload metadata and hashes
   - VM instance management
   - User authentication and sessions

**MinIO Object Storage**
   - Secure file storage with UUID-based naming
   - File versioning and metadata
   - Integration with file operations workers
   - S3-compatible API for scalability

**Redis Task Queue**
   - Celery task queue management
   - Worker coordination and load balancing
   - Task result caching
   - Real-time status updates

VM Runtime Environment
~~~~~~~~~~~~~~~~~~~~~~

**VM Monitoring Agent** (``services/monitoring/vm-agent/``)
   - Lightweight Python agent deployed in each VM
   - Real-time system metrics collection (CPU, memory, disk, network)
   - Process monitoring with suspicious activity detection
   - File system event tracking
   - Direct ELK stack integration

**VM Templates**
   - Ubuntu 20.04/22.04 containers
   - Alpine Linux for lightweight analysis
   - Windows VMs (via Vagrant)
   - Custom templates with pre-installed tools

ELK Stack Integration
~~~~~~~~~~~~~~~~~~~~~

**Elasticsearch**
   - Time-series data storage for VM metrics
   - Workflow event indexing and search
   - ECS-compliant schema for standardization
   - Automatic index rotation and retention

**Logstash**
   - Data ingestion from VM agents and workers
   - Real-time data transformation and enrichment
   - Multiple input sources (HTTP, beats, files)
   - Output routing to appropriate indices

**Kibana**
   - Four comprehensive dashboards:
     - Workflow Overview
     - VM Runtime Monitoring
     - Threat Detection & Analysis
     - System Performance
   - Real-time visualization and alerting
   - Interactive filtering and drill-down

Workflow Architecture
---------------------

Complete File Processing Pipeline
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant User
       participant API
       participant WF as Workflow Orchestrator
       participant POOL as VM Pool
       participant VM as Virtual Machine
       participant AGENT as VM Agent
       participant ELK as ELK Stack
       
       User->>API: Upload File
       API->>WF: Start Workflow
       WF->>POOL: Get Ready VM
       POOL->>VM: Allocate VM
       WF->>VM: Download File
       WF->>VM: Inject File
       WF->>AGENT: Deploy Agent
       AGENT->>ELK: Stream Metrics
       VM->>ELK: Runtime Data
       WF->>VM: Monitor (30min)
       WF->>POOL: Terminate & Replace
       WF->>User: Results

Data Flow Architecture
~~~~~~~~~~~~~~~~~~~~~~

1. **File Upload**: User uploads file via React UI or API
2. **Workflow Initiation**: Workflow orchestrator coordinates processing
3. **VM Allocation**: Pool manager provides ready VM
4. **File Processing**: File downloaded and injected into VM
5. **Agent Deployment**: Monitoring agent automatically deployed
6. **Real-time Monitoring**: Agent streams metrics to ELK stack
7. **Analysis**: 30-minute analysis window with continuous monitoring
8. **Results**: Analysis results and IOCs extracted
9. **Cleanup**: VM terminated and replaced in pool

Scalability Design
------------------

Horizontal Scaling
~~~~~~~~~~~~~~~~~~

**Worker Scaling**
   - Independent worker containers for each task type
   - Configurable concurrency per worker type
   - Queue-based load distribution
   - Auto-scaling based on queue depth

**VM Pool Scaling**
   - Dynamic pool size adjustment (2-10 VMs)
   - Template-based VM provisioning
   - Load balancing across available VMs
   - Automatic replacement of terminated VMs

**ELK Stack Scaling**
   - Elasticsearch cluster expansion
   - Logstash pipeline parallelization
   - Kibana dashboard optimization
   - Index sharding and replication

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~~

**Resource Management**
   - Efficient VM resource allocation
   - Memory and CPU optimization
   - Disk space management and cleanup
   - Network bandwidth optimization

**Data Pipeline Efficiency**
   - Batch processing for metrics
   - Compressed data transmission
   - Optimized Elasticsearch mappings
   - Intelligent index rotation

Security Architecture
---------------------

Isolation and Containment
~~~~~~~~~~~~~~~~~~~~~~~~~~

**VM Isolation**
   - Docker container isolation
   - Network segmentation
   - Resource limits and quotas
   - Automatic termination after analysis

**Data Security**
   - Encrypted file storage in MinIO
   - Secure inter-service communication
   - Authentication and authorization
   - Audit logging for all operations

**Network Security**
   - Traefik reverse proxy
   - Internal network isolation
   - Rate limiting and DDoS protection
   - SSL/TLS encryption

Monitoring and Alerting
~~~~~~~~~~~~~~~~~~~~~~~

**System Health Monitoring**
   - Real-time service health checks
   - Resource utilization monitoring
   - Error rate tracking and alerting
   - Performance metrics collection

**Security Monitoring**
   - Suspicious activity detection
   - IOC extraction and tracking
   - Threat level classification
   - Real-time security alerts

Deployment Architecture
-----------------------

Container Orchestration
~~~~~~~~~~~~~~~~~~~~~~~~

**Docker Compose Services**
   - Core services (API, database, storage)
   - Worker services (5 specialized workers)
   - ELK stack (Elasticsearch, Logstash, Kibana)
   - Testing services (Playwright E2E testing)
   - Monitoring services (Flower, health checks)

**Network Configuration**
   - Internal service communication
   - Traefik reverse proxy integration
   - External access control
   - Service discovery and load balancing

**Volume Management**
   - Persistent data storage
   - Temporary file handling
   - Log aggregation
   - Configuration management

Production Considerations
~~~~~~~~~~~~~~~~~~~~~~~~~

**High Availability**
   - Service redundancy and failover
   - Database replication and backup
   - Load balancing and health checks
   - Disaster recovery procedures

**Monitoring and Observability**
   - Comprehensive logging and metrics
   - Real-time alerting and notifications
   - Performance monitoring and optimization
   - Capacity planning and scaling

**Maintenance and Updates**
   - Rolling updates and zero-downtime deployment
   - Database migration procedures
   - Configuration management
   - Security patch management
