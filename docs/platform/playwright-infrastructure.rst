💩🎉TurdParty🎉💩 Playwright Infrastructure
==========================================

Overview
--------

The TurdParty Playwright infrastructure provides comprehensive End-to-End (E2E) testing capabilities through a fully containerized testing environment. This infrastructure enables automated browser-based testing of the complete TurdParty malware analysis workflow, from file upload through VM processing to ELK analytics.

.. note::
   The Playwright infrastructure is designed to validate the complete user journey and ensure all TurdParty components work together seamlessly in a production-like environment.

Architecture Components
-----------------------

Docker Container Stack
~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Playwright Testing Infrastructure"
           PC[Playwright Container<br/>turdpartycollab_playwright]
           HS[Health Server :8080]
           WD[Web Dashboard]
           TR[Test Reports]
           BR[Browser Runtime]
       end

       subgraph "Browser Engines"
           CH[Chromium Engine]
           FF[Firefox Engine]
           WK[WebKit Engine]
       end

       subgraph "TurdParty Services"
           API[API Service<br/>api.turdparty.localhost]
           FE[Frontend Service<br/>frontend.turdparty.localhost]
           ELK[ELK Stack]
           DB[(Database)]
           REDIS[(Redis)]
       end

       subgraph "Infrastructure"
           TR_NET[Traefik Network]
           TP_NET[TurdParty Network]
           VOL[Shared Volumes]
       end

       PC --> HS
       PC --> WD
       PC --> TR
       PC --> BR
       BR --> CH
       BR --> FF
       BR --> WK

       PC --> API
       PC --> FE
       FE --> API
       API --> ELK
       API --> DB
       API --> REDIS

       PC --> TR_NET
       PC --> TP_NET
       PC --> VOL

Service Integration
~~~~~~~~~~~~~~~~~~

**Traefik Integration:**

.. code-block:: yaml

   labels:
     - "traefik.enable=true"
     - "traefik.http.routers.turdparty-playwright.rule=Host(`playwright.turdparty.localhost`)"
     - "traefik.http.routers.turdparty-playwright.entrypoints=web"
     - "traefik.http.services.turdparty-playwright.loadbalancer.server.port=8080"
     - "traefik.docker.network=traefik_network"

**Service Discovery:**

The Playwright service is registered in the central service management system:

.. code-block:: json

   {
     "environments": {
       "development": {
         "services": {
           "playwright": {
             "subdomain": "playwright",
             "port": null,
             "path": "",
             "health_endpoint": "/health"
           }
         }
       }
     }
   }

Container Configuration
----------------------

Dockerfile Structure
~~~~~~~~~~~~~~~~~~~

**Base Image:**

.. code-block:: dockerfile

   FROM mcr.microsoft.com/playwright/python:v1.50.0-jammy

**Key Features:**

- **Pre-installed Browsers**: Chromium, Firefox, WebKit with all dependencies
- **Python Environment**: Python 3.11+ with Playwright and testing libraries
- **System Dependencies**: All required system libraries for browser automation
- **Health Server**: Built-in HTTP server for monitoring and dashboard

**Environment Variables:**

.. code-block:: bash

   # Container Configuration
   PYTHONPATH=/app
   PYTHONDONTWRITEBYTECODE=1
   PYTHONUNBUFFERED=1
   IN_DOCKER=true
   
   # Playwright Configuration
   PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
   
   # Service URLs
   APP_URL=http://frontend.turdparty.localhost
   API_URL=http://api.turdparty.localhost
   
   # Health Server
   PORT=8080
   HOST=0.0.0.0

Volume Mounts
~~~~~~~~~~~~

.. code-block:: yaml

   volumes:
     - ../test-results:/app/test-results    # Test execution results
     - ../reports:/app/reports              # HTML test reports
     - ../logs:/app/logs                    # Container logs

**Directory Structure:**

.. code-block:: text

   /app/
   ├── tests/                    # Test suite
   │   └── e2e/                 # E2E test files
   ├── test-results/            # Test execution results
   ├── reports/                 # HTML reports and artifacts
   ├── logs/                    # Application logs
   ├── config/                  # Configuration files
   ├── utils/                   # Utility modules
   └── health_server.py         # Health monitoring server

Health Monitoring
----------------

Health Server Features
~~~~~~~~~~~~~~~~~~~~~

The integrated health server provides comprehensive monitoring:

**Health Endpoint Response:**

.. code-block:: json

   {
     "status": "healthy",
     "service": "turdparty-playwright",
     "timestamp": "2024-01-15T10:30:00Z",
     "version": "1.0.0",
     "playwright_available": true,
     "environment": "true",
     "reports_available": true,
     "test_results_available": true
   }

**Web Dashboard:**

- **Service Status**: Real-time health monitoring
- **Test Reports**: Browse and download test reports
- **Test Results**: View detailed test execution results
- **Service Links**: Direct access to TurdParty services

Dashboard Interface
~~~~~~~~~~~~~~~~~~

The web dashboard provides a comprehensive interface for test management:

**Features:**

- **Service Status Display**: Visual health indicators
- **Test Report Browser**: Navigate and view test reports
- **Result Viewer**: Detailed test execution results
- **Performance Metrics**: Test execution timing and statistics
- **Error Reporting**: Detailed error logs and stack traces

**Access URLs:**

- **Main Dashboard**: ``http://playwright.turdparty.localhost``
- **Health Check**: ``http://playwright.turdparty.localhost/health``
- **Test Reports**: ``http://playwright.turdparty.localhost/reports/``
- **Test Results**: ``http://playwright.turdparty.localhost/test-results/``

Deployment and Operations
------------------------

Build and Deployment
~~~~~~~~~~~~~~~~~~~

**Automated Build Script:**

.. code-block:: bash

   # Build and deploy Playwright infrastructure
   ./scripts/build-playwright.sh

**Manual Deployment:**

.. code-block:: bash

   # Build container
   docker build -t turdpartycollab_playwright:latest -f docker/playwright/Dockerfile .

   # Start with testing profile
   docker-compose -f compose/docker-compose.yml --profile testing up -d playwright

**Service Dependencies:**

The Playwright service depends on:

- **API Service**: Must be healthy before starting
- **Frontend Service**: Must be healthy before starting
- **Traefik**: For routing and service discovery
- **TurdParty Network**: For internal communication

Operational Commands
~~~~~~~~~~~~~~~~~~~

**Service Management:**

.. code-block:: bash

   # Start Playwright service
   docker-compose -f compose/docker-compose.yml --profile testing up -d playwright

   # Stop Playwright service
   docker-compose -f compose/docker-compose.yml stop playwright

   # View logs
   docker logs -f turdpartycollab_playwright

   # Execute tests
   docker exec turdpartycollab_playwright python -m pytest tests/e2e/ -v

**Health Monitoring:**

.. code-block:: bash

   # Check service health
   curl http://playwright.turdparty.localhost/health

   # Monitor container status
   docker ps | grep playwright

   # Check resource usage
   docker stats turdpartycollab_playwright

Performance and Scaling
-----------------------

Resource Requirements
~~~~~~~~~~~~~~~~~~~~

**Minimum Requirements:**

- **CPU**: 2 cores
- **Memory**: 4GB RAM
- **Storage**: 2GB for browsers and test artifacts
- **Network**: Access to TurdParty services

**Recommended Configuration:**

- **CPU**: 4+ cores for parallel test execution
- **Memory**: 8GB+ RAM for multiple browser instances
- **Storage**: 5GB+ for test results and reports
- **Network**: Low-latency connection to services

Optimization Strategies
~~~~~~~~~~~~~~~~~~~~~~

**Browser Configuration:**

.. code-block:: python

   # Optimized browser launch
   browser = await p.chromium.launch(
       headless=True,
       args=[
           '--no-sandbox',
           '--disable-dev-shm-usage',
           '--disable-gpu',
           '--disable-web-security'
       ]
   )

**Parallel Test Execution:**

.. code-block:: bash

   # Run tests in parallel
   docker exec turdpartycollab_playwright python -m pytest tests/e2e/ -n auto

**Resource Monitoring:**

.. code-block:: bash

   # Monitor resource usage during tests
   docker exec turdpartycollab_playwright top
   docker exec turdpartycollab_playwright free -h

Security Considerations
----------------------

Container Security
~~~~~~~~~~~~~~~~~

**Security Features:**

- **Non-root User**: Container runs with restricted privileges
- **Network Isolation**: Isolated network access to required services only
- **Volume Restrictions**: Limited volume mounts for test artifacts only
- **Resource Limits**: CPU and memory limits to prevent resource exhaustion

**Security Configuration:**

.. code-block:: yaml

   security_opt:
     - no-new-privileges:true
   read_only: false  # Required for test artifact generation
   tmpfs:
     - /tmp:noexec,nosuid,size=100m

Browser Security
~~~~~~~~~~~~~~~

**Browser Isolation:**

- **Sandboxed Execution**: Browsers run in isolated contexts
- **No Persistent Storage**: Browser data cleared between test runs
- **Network Restrictions**: Limited network access to test targets only
- **Content Security**: CSP headers enforced during testing

Troubleshooting
--------------

Common Issues
~~~~~~~~~~~~

**Container Build Failures:**

.. code-block:: bash

   # Clean build cache
   docker system prune -f
   docker build --no-cache -t turdpartycollab_playwright:latest -f docker/playwright/Dockerfile .

**Browser Launch Failures:**

.. code-block:: bash

   # Check browser dependencies
   docker exec turdpartycollab_playwright playwright install --with-deps

   # Verify browser availability
   docker exec turdpartycollab_playwright python -c "from playwright.sync_api import sync_playwright; print('OK')"

**Service Connectivity Issues:**

.. code-block:: bash

   # Test service connectivity
   docker exec turdpartycollab_playwright curl -f http://api.turdparty.localhost/health
   docker exec turdpartycollab_playwright curl -f http://frontend.turdparty.localhost

**Test Execution Failures:**

.. code-block:: bash

   # Run with verbose output
   docker exec turdpartycollab_playwright python -m pytest tests/e2e/ -v -s --tb=long

   # Generate debug screenshots
   docker exec turdpartycollab_playwright python -m pytest tests/e2e/ --screenshot=on

Monitoring and Alerting
~~~~~~~~~~~~~~~~~~~~~~~

**Health Check Monitoring:**

.. code-block:: bash

   # Automated health monitoring
   while true; do
     curl -f http://playwright.turdparty.localhost/health || echo "Health check failed"
     sleep 30
   done

**Log Monitoring:**

.. code-block:: bash

   # Monitor container logs
   docker logs -f turdpartycollab_playwright | grep -E "(ERROR|FAILED|Exception)"

**Performance Monitoring:**

.. code-block:: bash

   # Monitor test execution performance
   docker exec turdpartycollab_playwright python -m pytest tests/e2e/ --durations=10

Future Enhancements
------------------

Planned Improvements
~~~~~~~~~~~~~~~~~~~

- **Multi-browser Testing**: Parallel execution across different browsers
- **Visual Regression Testing**: Screenshot comparison for UI changes
- **Mobile Device Testing**: Comprehensive mobile device simulation
- **Load Testing Integration**: User journey load testing capabilities
- **CI/CD Integration**: Enhanced GitHub Actions integration
- **Accessibility Testing**: Automated WCAG compliance validation

Integration Roadmap
~~~~~~~~~~~~~~~~~~

- **ELK Integration**: Test result logging to Elasticsearch
- **Metrics Collection**: Prometheus metrics for test execution
- **Alert Integration**: Slack/email notifications for test failures
- **Report Enhancement**: Advanced test reporting with trends and analytics
