💩🎉TurdParty🎉💩 WebSocket Infrastructure Overview
===================================================

System Architecture
--------------------

TurdParty's WebSocket infrastructure is designed as a distributed, scalable system that provides real-time communication capabilities across the entire malware analysis platform.

High-Level Infrastructure View
------------------------------

.. mermaid::

   graph TB
       subgraph "External Clients"
           WEB[Web Dashboard]
           CLI[CLI Tools]
           API_CLIENT[API Clients]
           MOBILE[Mobile Apps]
       end
       
       subgraph "Load Balancer & Proxy"
           TRAEFIK[Traefik Proxy<br/>- SSL Termination<br/>- Load Balancing<br/>- Service Discovery]
       end
       
       subgraph "API Gateway"
           FASTAPI[FastAPI Application<br/>- WebSocket Endpoints<br/>- REST API<br/>- Authentication]
       end
       
       subgraph "WebSocket Services"
           WS_METRICS[Metrics Service<br/>- Real-time monitoring<br/>- Performance data<br/>- Alert generation]
           WS_COMMANDS[Command Service<br/>- Interactive execution<br/>- Output streaming<br/>- Session management]
           WS_FILES[File Service<br/>- Progressive uploads<br/>- Transfer monitoring<br/>- Validation]
           WS_EVENTS[Event Service<br/>- VM state changes<br/>- System notifications<br/>- Audit logging]
       end
       
       subgraph "Connection Management"
           CONN_MGR[Connection Manager<br/>- Pool management<br/>- Message routing<br/>- Cleanup handling]
           SESSION_STORE[Session Store<br/>- Connection metadata<br/>- Client state<br/>- Heartbeat tracking]
       end
       
       subgraph "VM Infrastructure"
           DOCKER[Docker Engine<br/>- Container VMs<br/>- Resource isolation<br/>- Network management]
           VAGRANT[Vagrant/VirtualBox<br/>- Full VMs<br/>- OS-level isolation<br/>- Snapshot support]
           VM_MONITOR[VM Monitor<br/>- Health checking<br/>- Resource tracking<br/>- Lifecycle management]
       end
       
       subgraph "Storage Layer"
           MINIO[MinIO Object Storage<br/>- File storage<br/>- Chunk management<br/>- Metadata tracking]
           POSTGRES[PostgreSQL<br/>- VM metadata<br/>- Session data<br/>- Audit logs]
           REDIS[Redis Cache<br/>- Session cache<br/>- Message queuing<br/>- Rate limiting]
       end
       
       subgraph "Observability Stack"
           ELASTICSEARCH[Elasticsearch<br/>- Log aggregation<br/>- Search indexing<br/>- Analytics]
           LOGSTASH[Logstash<br/>- Log processing<br/>- ECS formatting<br/>- Data enrichment]
           KIBANA[Kibana<br/>- Visualization<br/>- Dashboards<br/>- Alerting]
           FILEBEAT[Filebeat<br/>- Log collection<br/>- Container monitoring<br/>- Metric shipping]
       end
       
       subgraph "Message Queue"
           CELERY[Celery Workers<br/>- Async processing<br/>- Task scheduling<br/>- Background jobs]
           CELERY_BEAT[Celery Beat<br/>- Periodic tasks<br/>- Cleanup jobs<br/>- Health checks]
       end
       
       %% Client connections
       WEB --> TRAEFIK
       CLI --> TRAEFIK
       API_CLIENT --> TRAEFIK
       MOBILE --> TRAEFIK
       
       %% Proxy to API
       TRAEFIK --> FASTAPI
       
       %% API to WebSocket services
       FASTAPI --> WS_METRICS
       FASTAPI --> WS_COMMANDS
       FASTAPI --> WS_FILES
       FASTAPI --> WS_EVENTS
       
       %% WebSocket services to connection management
       WS_METRICS --> CONN_MGR
       WS_COMMANDS --> CONN_MGR
       WS_FILES --> CONN_MGR
       WS_EVENTS --> CONN_MGR
       
       CONN_MGR --> SESSION_STORE
       
       %% Services to VM infrastructure
       WS_METRICS --> VM_MONITOR
       WS_COMMANDS --> DOCKER
       WS_COMMANDS --> VAGRANT
       WS_FILES --> DOCKER
       WS_FILES --> VAGRANT
       
       VM_MONITOR --> DOCKER
       VM_MONITOR --> VAGRANT
       
       %% Storage connections
       WS_FILES --> MINIO
       CONN_MGR --> POSTGRES
       SESSION_STORE --> REDIS
       
       %% Observability connections
       FASTAPI --> LOGSTASH
       WS_METRICS --> LOGSTASH
       WS_COMMANDS --> LOGSTASH
       WS_FILES --> LOGSTASH
       WS_EVENTS --> LOGSTASH
       
       FILEBEAT --> LOGSTASH
       LOGSTASH --> ELASTICSEARCH
       ELASTICSEARCH --> KIBANA
       
       %% Background processing
       FASTAPI --> CELERY
       VM_MONITOR --> CELERY
       CELERY --> CELERY_BEAT

Network Architecture
--------------------

.. mermaid::

   graph LR
       subgraph "External Network"
           INTERNET[Internet]
       end
       
       subgraph "DMZ Network (172.18.0.0/16)"
           TRAEFIK_DMZ[Traefik Proxy<br/>172.18.0.10]
       end
       
       subgraph "Application Network (172.19.0.0/16)"
           API[FastAPI<br/>172.19.0.20]
           WS_SERVICES[WebSocket Services<br/>172.19.0.21-25]
       end
       
       subgraph "Data Network (172.20.0.0/16)"
           POSTGRES_NET[PostgreSQL<br/>172.20.0.30]
           REDIS_NET[Redis<br/>172.20.0.31]
           MINIO_NET[MinIO<br/>172.20.0.32]
       end
       
       subgraph "VM Network (172.21.0.0/16)"
           DOCKER_NET[Docker VMs<br/>172.21.0.40-99]
           VAGRANT_NET[Vagrant VMs<br/>172.21.0.100-199]
       end
       
       subgraph "Monitoring Network (172.22.0.0/16)"
           ELK_STACK[ELK Stack<br/>172.22.0.50-52]
           FILEBEAT_NET[Filebeat<br/>172.22.0.53]
       end
       
       INTERNET --> TRAEFIK_DMZ
       TRAEFIK_DMZ --> API
       API --> WS_SERVICES
       WS_SERVICES --> POSTGRES_NET
       WS_SERVICES --> REDIS_NET
       WS_SERVICES --> MINIO_NET
       WS_SERVICES --> DOCKER_NET
       WS_SERVICES --> VAGRANT_NET
       
       API --> ELK_STACK
       WS_SERVICES --> ELK_STACK
       FILEBEAT_NET --> ELK_STACK

WebSocket Service Integration
-----------------------------

.. mermaid::

   graph TB
       subgraph "WebSocket Endpoint Layer"
           EP1[/api/v1/vms/{vm_id}/metrics/stream]
           EP2[/api/v1/vms/{vm_id}/commands/execute]
           EP3[/api/v1/vms/{vm_id}/files/upload]
           EP4[/test-ws]
           EP5[/ws/test]
           EP6[/minimal-ws]
           EP7[/test-with-param/{test_id}]
           EP8[/test-with-manager/{test_id}]
       end
       
       subgraph "Service Orchestration"
           ROUTER[Message Router<br/>- Type-based routing<br/>- Validation<br/>- Error handling]
           MIDDLEWARE[WebSocket Middleware<br/>- Authentication<br/>- Rate limiting<br/>- Logging]
       end
       
       subgraph "Core Services"
           METRICS_SVC[Metrics Service<br/>- VM monitoring<br/>- Data aggregation<br/>- Real-time streaming]
           COMMAND_SVC[Command Service<br/>- Shell execution<br/>- Output buffering<br/>- Session management]
           FILE_SVC[File Service<br/>- Upload handling<br/>- Progress tracking<br/>- Validation]
           EVENT_SVC[Event Service<br/>- State changes<br/>- Notifications<br/>- Audit trail]
       end
       
       subgraph "Integration Layer"
           VM_API[VM Management API<br/>- Lifecycle control<br/>- Resource allocation<br/>- Health monitoring]
           STORAGE_API[Storage API<br/>- File operations<br/>- Metadata management<br/>- Access control]
           MONITOR_API[Monitoring API<br/>- Metrics collection<br/>- Alert generation<br/>- Dashboard data]
       end
       
       EP1 --> ROUTER
       EP2 --> ROUTER
       EP3 --> ROUTER
       EP4 --> ROUTER
       EP5 --> ROUTER
       EP6 --> ROUTER
       EP7 --> ROUTER
       EP8 --> ROUTER
       
       ROUTER --> MIDDLEWARE
       
       MIDDLEWARE --> METRICS_SVC
       MIDDLEWARE --> COMMAND_SVC
       MIDDLEWARE --> FILE_SVC
       MIDDLEWARE --> EVENT_SVC
       
       METRICS_SVC --> VM_API
       COMMAND_SVC --> VM_API
       FILE_SVC --> STORAGE_API
       EVENT_SVC --> MONITOR_API

Data Flow Architecture
----------------------

.. mermaid::

   flowchart TD
       subgraph "Client Layer"
           CLIENT[WebSocket Client]
       end
       
       subgraph "Proxy Layer"
           PROXY[Traefik Proxy<br/>WSS Termination]
       end
       
       subgraph "Application Layer"
           FASTAPI[FastAPI App<br/>WebSocket Handler]
           CONN_MGR[Connection Manager<br/>Pool & Routing]
       end
       
       subgraph "Service Layer"
           METRICS[Metrics Collector]
           COMMANDS[Command Executor]
           FILES[File Handler]
       end
       
       subgraph "Infrastructure Layer"
           VMS[Virtual Machines]
           STORAGE[Object Storage]
           CACHE[Redis Cache]
       end
       
       subgraph "Observability Layer"
           LOGS[Structured Logging]
           METRICS_DB[Metrics Database]
           ALERTS[Alert Manager]
       end
       
       CLIENT -.->|WSS Connection| PROXY
       PROXY -.->|WS Upgrade| FASTAPI
       FASTAPI -.->|Register| CONN_MGR
       
       CONN_MGR -->|Route Messages| METRICS
       CONN_MGR -->|Route Messages| COMMANDS
       CONN_MGR -->|Route Messages| FILES
       
       METRICS -->|Collect Data| VMS
       COMMANDS -->|Execute| VMS
       FILES -->|Store/Retrieve| STORAGE
       
       METRICS -->|Cache| CACHE
       COMMANDS -->|Session State| CACHE
       FILES -->|Progress| CACHE
       
       FASTAPI -->|ECS Format| LOGS
       METRICS -->|Performance Data| METRICS_DB
       COMMANDS -->|Execution Logs| LOGS
       FILES -->|Transfer Logs| LOGS
       
       METRICS_DB -->|Threshold Breach| ALERTS
       LOGS -->|Error Detection| ALERTS

Security Architecture
---------------------

.. mermaid::

   graph TB
       subgraph "Security Layers"
           subgraph "Network Security"
               FIREWALL[Firewall Rules<br/>- Port restrictions<br/>- IP whitelisting<br/>- DDoS protection]
               TLS[TLS/SSL<br/>- Certificate management<br/>- Cipher suites<br/>- HSTS headers]
           end
           
           subgraph "Application Security"
               AUTH[Authentication<br/>- Traefik integration<br/>- JWT validation<br/>- Session management]
               AUTHZ[Authorization<br/>- Role-based access<br/>- Resource permissions<br/>- VM ownership]
               VALIDATION[Input Validation<br/>- Message schemas<br/>- Data sanitization<br/>- Rate limiting]
           end
           
           subgraph "Infrastructure Security"
               ISOLATION[Container Isolation<br/>- Network segmentation<br/>- Resource limits<br/>- Capability dropping]
               SECRETS[Secret Management<br/>- Environment variables<br/>- Encrypted storage<br/>- Rotation policies]
               AUDIT[Audit Logging<br/>- Access logs<br/>- Change tracking<br/>- Compliance reporting]
           end
       end
       
       subgraph "WebSocket Security Flow"
           CLIENT_SEC[Client Request]
           PROXY_SEC[Proxy Validation]
           APP_SEC[Application Validation]
           SERVICE_SEC[Service Authorization]
           RESOURCE_SEC[Resource Access]
       end
       
       CLIENT_SEC --> FIREWALL
       FIREWALL --> TLS
       TLS --> PROXY_SEC
       PROXY_SEC --> AUTH
       AUTH --> AUTHZ
       AUTHZ --> APP_SEC
       APP_SEC --> VALIDATION
       VALIDATION --> SERVICE_SEC
       SERVICE_SEC --> ISOLATION
       ISOLATION --> RESOURCE_SEC
       
       AUDIT -.-> CLIENT_SEC
       AUDIT -.-> PROXY_SEC
       AUDIT -.-> APP_SEC
       AUDIT -.-> SERVICE_SEC
       AUDIT -.-> RESOURCE_SEC

Deployment Architecture
-----------------------

.. mermaid::

   graph TB
       subgraph "Production Environment"
           subgraph "Load Balancer Tier"
               LB1[Traefik Instance 1]
               LB2[Traefik Instance 2]
               LB3[Traefik Instance 3]
           end
           
           subgraph "Application Tier"
               API1[FastAPI Instance 1<br/>WebSocket Endpoints]
               API2[FastAPI Instance 2<br/>WebSocket Endpoints]
               API3[FastAPI Instance 3<br/>WebSocket Endpoints]
           end
           
           subgraph "Service Tier"
               WS1[WebSocket Services 1]
               WS2[WebSocket Services 2]
               WS3[WebSocket Services 3]
           end
           
           subgraph "Data Tier"
               DB_PRIMARY[PostgreSQL Primary]
               DB_REPLICA[PostgreSQL Replica]
               REDIS_CLUSTER[Redis Cluster]
               MINIO_CLUSTER[MinIO Cluster]
           end
           
           subgraph "VM Tier"
               VM_NODE1[VM Node 1<br/>Docker + Vagrant]
               VM_NODE2[VM Node 2<br/>Docker + Vagrant]
               VM_NODE3[VM Node 3<br/>Docker + Vagrant]
           end
       end
       
       LB1 --> API1
       LB2 --> API2
       LB3 --> API3
       
       API1 --> WS1
       API2 --> WS2
       API3 --> WS3
       
       WS1 --> DB_PRIMARY
       WS2 --> DB_PRIMARY
       WS3 --> DB_PRIMARY
       
       DB_PRIMARY --> DB_REPLICA
       
       WS1 --> REDIS_CLUSTER
       WS2 --> REDIS_CLUSTER
       WS3 --> REDIS_CLUSTER
       
       WS1 --> MINIO_CLUSTER
       WS2 --> MINIO_CLUSTER
       WS3 --> MINIO_CLUSTER
       
       WS1 --> VM_NODE1
       WS2 --> VM_NODE2
       WS3 --> VM_NODE3

Monitoring & Observability
--------------------------

**Key Metrics Tracked:**

.. list-table::
   :header-rows: 1
   :widths: 25 25 25 25

   * - Category
     - Metric
     - Threshold
     - Action
   * - Connection
     - Active connections
     - > 1000
     - Scale up
   * - Performance
     - Message latency
     - > 100ms
     - Alert
   * - Reliability
     - Connection drops
     - > 5%
     - Investigate
   * - Resource
     - Memory usage
     - > 80%
     - Scale up

**Alerting Rules:**

- WebSocket connection failures > 5% in 5 minutes
- Message processing latency > 100ms for 2 minutes
- VM command execution timeout > 30 seconds
- File upload failure rate > 10% in 10 minutes

**Dashboard Components:**

- Real-time connection count and distribution
- Message throughput and latency histograms
- Error rate and type breakdown
- Resource utilization across services
- VM performance and health status
