TurdParty Workflow Management
=============================

Overview
--------

TurdParty implements a sophisticated workflow orchestration system that automates the complete malware analysis process from file upload to results delivery. The workflow system coordinates multiple specialized workers to provide a seamless, scalable, and reliable analysis pipeline.

Complete Workflow Pipeline
---------------------------

.. mermaid::

   graph LR
       A[File Upload] --> B[Workflow Start]
       B --> C[VM Allocation]
       C --> D[File Download]
       D --> E[File Injection]
       E --> F[Agent Deployment]
       F --> G[Real-time Monitoring]
       G --> H[Analysis Results]
       H --> I[VM Termination]
       I --> J[Pool Replenishment]

Workflow Phases
---------------

Phase 1: File Processing
~~~~~~~~~~~~~~~~~~~~~~~~~

**File Upload** (``/api/v1/files/upload``)
   - User uploads file via React UI or API
   - File stored in MinIO with UUID generation
   - Blake3 hash calculated for integrity
   - File metadata stored in PostgreSQL

**Workflow Initiation** (``/api/v1/workflow/start``)
   - Workflow job created with unique ID
   - VM configuration specified (template, resources)
   - Initial workflow event streamed to ELK
   - Task chain initiated via Celery

Phase 2: VM Management
~~~~~~~~~~~~~~~~~~~~~~

**VM Allocation**
   - Pool manager provides ready VM from pool
   - VM marked as allocated and removed from available pool
   - VM details (IP, SSH port, container ID) retrieved
   - Fallback VM creation if pool empty

**VM Configuration**
   - VM prepared for file analysis
   - Network isolation configured
   - Resource limits applied
   - Monitoring endpoints established

Phase 3: File Injection
~~~~~~~~~~~~~~~~~~~~~~~~

**File Download**
   - File retrieved from MinIO storage
   - Integrity verification via Blake3 hash
   - Temporary storage on worker filesystem
   - Download progress tracked and logged

**File Injection**
   - File transferred to VM via Docker exec or SSH
   - Appropriate permissions set
   - Injection path configured based on file type
   - Injection status verified and logged

Phase 4: Monitoring Setup
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Agent Deployment**
   - VM monitoring agent package created
   - Agent customized with VM-specific configuration
   - Agent deployed via Docker exec or SSH
   - Agent service started and health verified

**Monitoring Initialization**
   - Real-time metrics collection started
   - File system monitoring activated
   - Process monitoring enabled
   - Network activity tracking initiated

Phase 5: Analysis Execution
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Real-time Analysis** (30-minute window)
   - Continuous VM metrics collection
   - Process activity monitoring
   - File system change detection
   - Network connection tracking
   - Suspicious activity detection

**Data Streaming**
   - All metrics streamed to ELK stack
   - Real-time visualization in Kibana
   - Automated threat detection
   - IOC extraction and classification

Phase 6: Results and Cleanup
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Analysis Completion**
   - Final analysis results compiled
   - IOCs extracted and scored
   - Threat level classification assigned
   - Results stored and made available

**VM Termination**
   - VM automatically terminated after 30 minutes
   - All VM resources cleaned up
   - VM removed from active tracking
   - Termination event logged

**Pool Replenishment**
   - Pool manager triggered to maintain minimum VMs
   - New VM provisioned to replace terminated VM
   - Pool status updated and monitored
   - Ready for next analysis request

Workflow Orchestration
-----------------------

Enhanced Workflow Orchestrator
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The workflow orchestrator (``services/workers/tasks/workflow_orchestrator.py``) provides:

**Task Coordination**
   - Celery chain-based task dependencies
   - Error handling and retry logic
   - Status tracking and progress updates
   - Event streaming to ELK stack

**Lifecycle Management**
   - 30-minute automatic termination
   - Resource cleanup and deallocation
   - Pool maintenance triggering
   - Comprehensive logging

**Error Handling**
   - Graceful failure recovery
   - Partial workflow completion
   - Error event streaming
   - Debugging information collection

Worker Specialization
~~~~~~~~~~~~~~~~~~~~~

**File Operations Worker** (``file_ops`` queue)
   - File download and validation
   - Integrity checking and metadata extraction
   - Temporary file management
   - Concurrency: 2 workers

**VM Management Worker** (``vm_ops`` queue)
   - VM creation and termination
   - VM pool integration
   - Resource management
   - Concurrency: 1 worker (serialized operations)

**File Injection Worker** (``injection_ops`` queue)
   - File transfer to VMs
   - Permission and path management
   - Injection verification
   - Concurrency: 2 workers

**VM Pool Worker** (``pool_ops`` queue)
   - Pool maintenance and provisioning
   - VM health monitoring
   - Automatic scaling
   - Concurrency: 1 worker (centralized management)

**Workflow Orchestrator Worker** (``workflow_ops`` queue)
   - Task coordination and chaining
   - Status management
   - Event streaming
   - Concurrency: 2 workers

**ELK Integration Worker** (``elk_ops`` queue)
   - Data streaming to ELK stack
   - Event formatting and routing
   - Health monitoring
   - Concurrency: 3 workers

Workflow Configuration
----------------------

VM Configuration Options
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   vm_config = {
       "template": "ubuntu:20.04",  # VM template
       "memory_mb": 1024,           # Memory allocation
       "cpus": 1,                   # CPU cores
       "disk_gb": 20,               # Disk space
       "network_isolation": True,   # Network isolation
       "monitoring": True           # Enable monitoring
   }

Workflow Parameters
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   workflow_params = {
       "file_id": "uuid-string",           # File UUID
       "vm_config": vm_config,             # VM configuration
       "analysis_timeout": 1800,           # 30 minutes
       "monitoring_interval": 5,           # 5 seconds
       "threat_detection": True,           # Enable threat detection
       "ioc_extraction": True              # Extract IOCs
   }

Monitoring and Observability
-----------------------------

Real-time Status Tracking
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Workflow Status Updates**
   - ``PENDING``: Workflow queued for processing
   - ``VM_ALLOCATING``: Allocating VM from pool
   - ``FILE_DOWNLOADING``: Downloading file from storage
   - ``FILE_INJECTING``: Injecting file into VM
   - ``VM_EXECUTING``: Analysis in progress
   - ``COMPLETED``: Analysis completed successfully
   - ``FAILED``: Workflow failed with error
   - ``TERMINATED``: Workflow terminated (timeout)

**Progress Tracking**
   - Real-time progress updates via WebSocket
   - Detailed step-by-step status
   - Error messages and debugging information
   - Performance metrics and timing

ELK Integration
~~~~~~~~~~~~~~~

**Workflow Events**
   - All workflow steps logged to ELK
   - Real-time event streaming
   - Searchable workflow history
   - Performance analytics

**Kibana Dashboards**
   - Workflow Overview dashboard
   - Real-time status monitoring
   - Performance metrics visualization
   - Error rate tracking

API Integration
---------------

Starting a Workflow
~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import httpx
   
   async def start_analysis(file_id: str):
       async with httpx.AsyncClient() as client:
           response = await client.post(
               "http://localhost:8000/api/v1/workflow/start",
               json={
                   "file_id": file_id,
                   "vm_config": {
                       "template": "ubuntu:20.04",
                       "memory_mb": 1024,
                       "cpus": 1
                   }
               }
           )
           return response.json()

Monitoring Workflow Progress
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def monitor_workflow(workflow_id: str):
       async with httpx.AsyncClient() as client:
           response = await client.get(
               f"http://localhost:8000/api/v1/workflow/{workflow_id}"
           )
           return response.json()

Getting Analysis Results
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def get_results(workflow_id: str):
       async with httpx.AsyncClient() as client:
           response = await client.get(
               f"http://localhost:8000/api/v1/workflow/{workflow_id}/results"
           )
           return response.json()

Performance Characteristics
----------------------------

Throughput and Latency
~~~~~~~~~~~~~~~~~~~~~~~

**Typical Performance**
   - File upload: < 5 seconds for files up to 100MB
   - VM allocation: < 30 seconds from ready pool
   - File injection: < 10 seconds for typical files
   - Agent deployment: < 15 seconds
   - Total startup time: < 60 seconds

**Concurrent Processing**
   - Multiple workflows can run simultaneously
   - Limited by VM pool size (2-10 VMs)
   - Queue-based load balancing
   - Automatic scaling based on demand

Resource Utilization
~~~~~~~~~~~~~~~~~~~~~

**Memory Usage**
   - API server: ~200MB base + request overhead
   - Workers: ~100MB per worker process
   - VMs: Configurable (default 1GB per VM)
   - ELK stack: ~2GB for small deployments

**CPU Usage**
   - Low CPU usage during idle periods
   - Moderate CPU during file processing
   - VM-dependent CPU usage during analysis
   - ELK indexing CPU overhead

Error Handling and Recovery
---------------------------

Failure Scenarios
~~~~~~~~~~~~~~~~~

**VM Allocation Failures**
   - Automatic fallback to VM creation
   - Pool replenishment triggered
   - Error logged and user notified

**File Processing Errors**
   - Retry logic with exponential backoff
   - Alternative processing methods
   - Detailed error logging

**Network Failures**
   - Connection retry mechanisms
   - Graceful degradation
   - Offline capability where possible

**Resource Exhaustion**
   - Queue management and throttling
   - Resource cleanup and recovery
   - Automatic scaling triggers

Recovery Mechanisms
~~~~~~~~~~~~~~~~~~~

**Automatic Recovery**
   - Failed tasks automatically retried
   - VM pool automatically replenished
   - Service health monitoring and restart
   - Data consistency checks

**Manual Recovery**
   - Administrative tools for workflow management
   - Manual VM pool management
   - Database consistency repair
   - Log analysis and debugging tools

Best Practices
--------------

Workflow Design
~~~~~~~~~~~~~~~

**File Preparation**
   - Verify file integrity before upload
   - Use appropriate file naming conventions
   - Consider file size limitations
   - Prepare VM configuration in advance

**Resource Management**
   - Monitor VM pool status
   - Plan for peak usage periods
   - Configure appropriate timeouts
   - Implement proper cleanup procedures

**Monitoring and Alerting**
   - Set up Kibana dashboard monitoring
   - Configure alerts for failures
   - Monitor resource utilization
   - Track performance metrics

Troubleshooting
~~~~~~~~~~~~~~~

**Common Issues**
   - VM pool exhaustion: Increase pool size or reduce analysis time
   - File injection failures: Check file permissions and VM connectivity
   - ELK integration issues: Verify Logstash connectivity and configuration
   - Performance degradation: Monitor resource usage and scale accordingly

**Debugging Tools**
   - Flower dashboard for task monitoring
   - Kibana dashboards for workflow analysis
   - Docker logs for service debugging
   - Database queries for state inspection
