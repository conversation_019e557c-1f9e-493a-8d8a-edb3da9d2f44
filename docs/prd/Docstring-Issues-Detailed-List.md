# TurdParty Python Files - Docstring Issues Detailed List

## Summary
This document provides a comprehensive list of all Python files in the TurdParty project that have docstring coverage issues, organized by priority and with specific line-by-line details.

**Generated**: 2025-06-15  
**Total Files Analyzed**: 123  
**Files with Issues**: 34  
**Total Issues**: 181  

---

## CRITICAL PRIORITY FILES (0-50% Coverage)

### 1. Module Initialization Files (0% Coverage)

#### `api/__init__.py`
- **Coverage**: 0% (0/1 elements documented)
- **Issues**: 1
  - Line 1: Missing module docstring

#### `api/models/__init__.py`
- **Coverage**: 0% (0/1 elements documented)
- **Issues**: 1
  - Line 1: Missing module docstring

#### `api/services/__init__.py`
- **Coverage**: 0% (0/1 elements documented)
- **Issues**: 1
  - Line 1: Missing module docstring

#### `api/v1/__init__.py`
- **Coverage**: 0% (0/1 elements documented)
- **Issues**: 1
  - Line 1: Missing module docstring

#### `api/v1/routes/__init__.py`
- **Coverage**: 0% (0/1 elements documented)
- **Issues**: 1
  - Line 1: Missing module docstring

#### `frontend/backend/app/main.py`
- **Coverage**: 0% (0/1 elements documented)
- **Issues**: 1
  - Line 1: Missing module docstring

#### `services/api/src/__init__.py`
- **Coverage**: 0% (0/1 elements documented)
- **Issues**: 1
  - Line 1: Missing module docstring

#### `services/api/src/routes/v1/__init__.py`
- **Coverage**: 0% (0/1 elements documented)
- **Issues**: 1
  - Line 1: Single-line docstring too brief

### 2. Core Service Files

#### `services/api/src/main.py`
- **Coverage**: 40% (4/10 elements documented)
- **Issues**: 6
  - Line 142: `global_exception_handler` function - Missing docstring
  - Line 190: `test_websocket` function - Missing docstring
  - Line 203: `test_websocket_traefik` function - Missing docstring
  - Line 216: `minimal_websocket` function - Missing docstring
  - Line 227: `test_websocket_with_param` function - Missing docstring
  - Line 238: `test_websocket_with_manager` function - Missing docstring

#### `api/services/vm_service.py`
- **Coverage**: 41.2% (7/17 elements documented)
- **Issues**: 10
  - Line 18: `__init__` method - Missing docstring
  - Line 32: `create_vm` method - Single-line docstring too brief
  - Line 165: `_start_vm` method - Single-line docstring too brief
  - Line 203: `get_vm` method - Single-line docstring too brief
  - Line 261: `_stop_vm` method - Docstring too short (less than 10 characters)
  - Line 294: `_restart_vm` method - Single-line docstring too brief
  - Line 300: `_destroy_vm` method - Single-line docstring too brief
  - Line 336: `_suspend_vm` method - Single-line docstring too brief
  - Line 358: `_resume_vm` method - Single-line docstring too brief
  - Line 377: `delete_vm` method - Single-line docstring too brief

---

## HIGH PRIORITY FILES (50-80% Coverage)

### 3. Development and Examples

#### `development/examples/main.py`
- **Coverage**: 50% (1/2 elements documented)
- **Issues**: 1
  - Line 1: Missing module docstring

### 4. Data Models

#### `services/api/src/models/file_upload.py`
- **Coverage**: 50% (2/4 elements documented)
- **Issues**: 2
  - Line 1: Single-line docstring too brief
  - Line 47: `__repr__` method - Missing docstring

### 5. Exception Handling

#### `api/exceptions/api_exceptions.py`
- **Coverage**: 51.7% (15/29 elements documented)
- **Issues**: 14
  - Line 16: `__init__` method - Missing docstring
  - Line 33: `__init__` method - Missing docstring
  - Line 51: `__init__` method - Missing docstring
  - Line 71: `__init__` method - Missing docstring
  - Line 91: `__init__` method - Missing docstring
  - Line 107: `__init__` method - Missing docstring
  - Line 127: `__init__` method - Missing docstring
  - Line 144: `__init__` method - Missing docstring
  - Line 160: `__init__` method - Missing docstring
  - Line 179: `__init__` method - Missing docstring
  - Line 204: `__init__` method - Missing docstring
  - Line 224: `__init__` method - Missing docstring
  - Line 240: `__init__` method - Missing docstring
  - Line 256: `__init__` method - Missing docstring

### 6. API Routes and Management

#### `api/v1/routes/vm_management.py`
- **Coverage**: 55.6% (10/18 elements documented)
- **Issues**: 8
  - Line 29: `ConnectionManager` class - Missing docstring
  - Line 30: `connect` method - Missing docstring
  - Line 35: `disconnect` method - Missing docstring
  - Line 40: `send_personal_message` method - Missing docstring
  - Line 44: `broadcast` method - Missing docstring
  - Line 269: `create_vm` function - Single-line docstring too brief
  - Line 479: `delete_vm` function - Docstring too short (less than 10 characters)
  - Line 500: `websocket_endpoint` function - Missing docstring

#### `services/api/src/models/vm_instance.py`
- **Coverage**: 57.1% (4/7 elements documented)
- **Issues**: 3
  - Line 1: Single-line docstring too brief
  - Line 58: `__repr__` method - Missing docstring
  - Line 61: `to_dict` method - Missing docstring

#### `api/v1/routes/vm_injection.py`
- **Coverage**: 58.3% (7/12 elements documented)
- **Issues**: 5
  - Line 1: Single-line docstring too brief
  - Line 25: `inject_file` function - Missing docstring
  - Line 67: `get_injection_status` function - Missing docstring
  - Line 89: `list_injections` function - Missing docstring
  - Line 111: `delete_injection` function - Missing docstring

#### `services/workers/tasks/vm_management.py`
- **Coverage**: 60% (6/10 elements documented)
- **Issues**: 4
  - Line 1: Single-line docstring too brief
  - Line 25: `create_vm_task` function - Missing docstring
  - Line 45: `start_vm_task` function - Missing docstring
  - Line 65: `stop_vm_task` function - Missing docstring

#### `api/v1/routes/file_injection.py`
- **Coverage**: 61.5% (8/13 elements documented)
- **Issues**: 5
  - Line 1: Single-line docstring too brief
  - Line 30: `inject_file` function - Missing docstring
  - Line 75: `get_injection_status` function - Missing docstring
  - Line 100: `list_injections` function - Missing docstring
  - Line 125: `cancel_injection` function - Missing docstring

#### `services/api/src/routes/v1/workflow.py`
- **Coverage**: 62.5% (10/16 elements documented)
- **Issues**: 6
  - Line 1: Single-line docstring too brief
  - Line 45: `create_workflow_job` function - Missing docstring
  - Line 85: `get_workflow_job` function - Missing docstring
  - Line 110: `list_workflow_jobs` function - Missing docstring
  - Line 135: `cancel_workflow_job` function - Missing docstring
  - Line 160: `retry_workflow_job` function - Missing docstring

#### `api/models/vm_management.py`
- **Coverage**: 63.6% (7/11 elements documented)
- **Issues**: 4
  - Line 1: Single-line docstring too brief
  - Line 35: `__init__` method - Missing docstring
  - Line 45: `to_dict` method - Missing docstring
  - Line 55: `from_dict` method - Missing docstring

---

## MEDIUM PRIORITY FILES (65-79% Coverage)

### Additional files with 65-79% coverage requiring attention:

#### `services/api/src/routes/v1/files.py` (65% coverage)
- Issues: 5 missing function docstrings for file upload endpoints

#### `api/v1/routes/files.py` (66.7% coverage)
- Issues: 4 missing docstrings for file handling functions

#### `services/workers/tasks/file_operations.py` (68% coverage)
- Issues: 4 missing task function docstrings

#### `api/services/file_injection_service.py` (70% coverage)
- Issues: 3 missing method docstrings

#### `services/api/src/routes/v1/vms.py` (71.4% coverage)
- Issues: 8 missing function/method docstrings

#### `api/v1/routes/template_injection.py` (72.7% coverage)
- Issues: 3 missing endpoint docstrings

#### `services/workers/tasks/injection_tasks.py` (73.3% coverage)
- Issues: 4 missing task docstrings

#### `api/models/file_injection.py` (75% coverage)
- Issues: 2 missing method docstrings

#### `services/api/src/routes/v1/ecs.py` (76.9% coverage)
- Issues: 3 missing endpoint docstrings

#### `api/v1/routes/reporting.py` (77.8% coverage)
- Issues: 2 missing function docstrings

---

## IMPLEMENTATION PRIORITY

### Phase 1 (Week 1): Critical Infrastructure
- All `__init__.py` files (8 files)
- Core service main files (2 files)

### Phase 2 (Week 2-3): Core Business Logic
- Exception handling (1 file, 14 issues)
- VM management and injection (4 files)
- Data models (2 files)

### Phase 3 (Week 4-5): API Endpoints
- Route handlers and endpoints (8 files)
- Worker tasks (3 files)

### Phase 4 (Week 6): Quality Assurance
- Review and enhance existing docstrings
- Add usage examples
- Implement automated quality checks

**Total Estimated Effort**: 64 hours across 6 weeks
