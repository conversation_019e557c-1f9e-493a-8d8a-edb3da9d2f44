💩🎉TurdParty🎉💩 Troubleshooting Guide
==========================================

This guide provides solutions to common issues encountered when running the TurdParty platform.

.. contents::
   :local:
   :depth: 2

Container Startup Issues
-----------------------

API Service Won't Start
~~~~~~~~~~~~~~~~~~~~~~~

**Symptoms:**
- ``curl http://localhost:8000/health`` returns "Connection reset by peer"
- API container shows "Waiting for [service]..." messages
- Container appears running but no FastAPI server starts

**Root Cause:**
The API service has heavy dependencies and may get stuck waiting for services to be ready. See :doc:`../architecture/container-dependencies` for detailed dependency analysis.

**Quick Fix:**

.. code-block:: bash

   # Restart API service
   docker-compose restart api

   # If that doesn't work, restart all services in sequence
   ./scripts/turdparty stop
   ./scripts/turdparty start

**Detailed Troubleshooting:**

.. code-block:: bash

   # Check which dependency is causing the issue
   docker-compose logs --tail=20 api

   # Verify all dependencies are healthy
   docker-compose ps database redis elasticsearch logstash storage

   # Test internal connectivity
   docker exec turdpartycollab_api ping redis
   docker exec turdpartycollab_api ping database

MinIO Integration Test Failures
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Symptoms:**
- Integration tests show "MinIO connection failed"
- API health endpoint ``/api/v1/health/minio`` returns errors

**Root Cause:**
MinIO connectivity issues or Traefik routing problems.

**Solution:**

.. code-block:: bash

   # Test MinIO via API (recommended approach)
   curl http://localhost:8000/api/v1/health/minio

   # Check MinIO container status
   docker-compose ps storage

   # Verify Traefik routing (if using external access)
   curl -I http://storage-api.turdparty.localhost

Troubleshooting Guide=

This guide provides solutions for common issues encountered when deploying and operating the TurdParty malware analysis platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Quick Diagnostics
-----------------

System Health Check
~~~~~~~~~~~~~~~~~~~

Run the comprehensive system health check to identify issues:

.. code-block:: bash

   # Run complete system validation
   ./scripts/test-phase3-elk-integration.sh
   
   # Check individual components
   curl http://localhost:8000/health                    # API health
   curl http://flower.turdparty.localhost/api/workers   # Worker status
   curl http://elasticsearch.turdparty.localhost/_cluster/health  # ELK health

Service Status Check
~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Check all Docker services
   docker-compose -f compose/docker-compose.yml ps
   docker-compose -f compose/docker-compose.workers.yml ps
   docker-compose -f compose/docker-compose.elk.yml ps
   
   # Check service logs
   docker-compose -f compose/docker-compose.yml logs api
   docker-compose -f compose/docker-compose.workers.yml logs worker_workflow
   docker-compose -f compose/docker-compose.elk.yml logs elasticsearch

Installation Issues
-------------------

Docker and Docker Compose Problems
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Issue**: Docker daemon not running

.. code-block:: bash

   # Check Docker status
   sudo systemctl status docker
   
   # Start Docker if stopped
   sudo systemctl start docker
   sudo systemctl enable docker
   
   # Add user to docker group
   sudo usermod -aG docker $USER
   newgrp docker

**Issue**: Docker Compose version incompatibility

.. code-block:: bash

   # Check Docker Compose version
   docker-compose --version
   
   # Update Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose

**Issue**: Insufficient disk space

.. code-block:: bash

   # Check disk usage
   df -h
   docker system df
   
   # Clean up Docker resources
   docker system prune -f
   docker volume prune -f
   docker image prune -a -f

Network Configuration Issues
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Issue**: Services not accessible via domain names

.. code-block:: bash

   # Check /etc/hosts configuration
   cat /etc/hosts | grep turdparty
   
   # Add missing entries
   echo "127.0.0.1 frontend.turdparty.localhost" | sudo tee -a /etc/hosts
   echo "127.0.0.1 kibana.turdparty.localhost" | sudo tee -a /etc/hosts
   echo "127.0.0.1 flower.turdparty.localhost" | sudo tee -a /etc/hosts

**Issue**: Docker networks not created

.. code-block:: bash

   # Check existing networks
   docker network ls | grep turdparty
   
   # Create missing networks
   docker network create turdpartycollab_net
   docker network create traefik_network

**Issue**: Port conflicts

.. code-block:: bash

   # Check port usage
   sudo netstat -tulpn | grep :8000
   sudo netstat -tulpn | grep :5432
   
   # Stop conflicting services
   sudo systemctl stop postgresql  # If PostgreSQL running on host
   sudo systemctl stop redis-server  # If Redis running on host

Service Startup Issues
----------------------

API Server Problems
~~~~~~~~~~~~~~~~~~~

**Issue**: API server fails to start

.. code-block:: bash

   # Check API logs
   docker-compose -f compose/docker-compose.yml logs api
   
   # Common solutions:
   # 1. Database connection issues
   docker-compose -f compose/docker-compose.yml restart postgres
   
   # 2. Redis connection issues
   docker-compose -f compose/docker-compose.yml restart redis
   
   # 3. Environment variable issues
   cat .env | grep -E "(POSTGRES|REDIS|MINIO)"

**Issue**: Database migration errors

.. code-block:: bash

   # Check database status
   docker-compose -f compose/docker-compose.yml exec postgres psql -U turdparty -d turdparty -c "\dt"
   
   # Run migrations manually
   docker-compose -f compose/docker-compose.yml exec api python -m alembic upgrade head
   
   # Reset database if needed (WARNING: destroys data)
   docker-compose -f compose/docker-compose.yml down -v
   docker-compose -f compose/docker-compose.yml up -d postgres
   sleep 10
   docker-compose -f compose/docker-compose.yml up -d api

Worker Service Problems
~~~~~~~~~~~~~~~~~~~~~~~

**Issue**: Workers not starting

.. code-block:: bash

   # Check worker logs
   docker-compose -f compose/docker-compose.workers.yml logs worker_workflow
   docker-compose -f compose/docker-compose.workers.yml logs worker_file
   
   # Check Redis connectivity
   docker-compose -f compose/docker-compose.yml exec redis redis-cli ping
   
   # Restart workers
   docker-compose -f compose/docker-compose.workers.yml restart

**Issue**: Workers not processing tasks

.. code-block:: bash

   # Check worker status in Flower
   curl http://flower.turdparty.localhost/api/workers
   
   # Check queue status
   docker-compose -f compose/docker-compose.workers.yml exec worker_workflow celery -A celery_app inspect active
   
   # Purge stuck queues (WARNING: loses pending tasks)
   docker-compose -f compose/docker-compose.workers.yml exec worker_workflow celery -A celery_app purge

**Issue**: High worker memory usage

.. code-block:: bash

   # Check worker resource usage
   docker stats
   
   # Restart workers to clear memory
   docker-compose -f compose/docker-compose.workers.yml restart
   
   # Adjust worker concurrency in .env
   # WORKER_CONCURRENCY_WORKFLOW_OPS=1  # Reduce from 2

ELK Stack Issues
----------------

Elasticsearch Problems
~~~~~~~~~~~~~~~~~~~~~~

**Issue**: Elasticsearch fails to start

.. code-block:: bash

   # Check Elasticsearch logs
   docker-compose -f compose/docker-compose.elk.yml logs elasticsearch
   
   # Common issues:
   # 1. Insufficient memory
   # Increase vm.max_map_count
   sudo sysctl -w vm.max_map_count=262144
   echo 'vm.max_map_count=262144' | sudo tee -a /etc/sysctl.conf
   
   # 2. Disk space issues
   df -h
   docker system prune -f

**Issue**: Elasticsearch cluster health is red

.. code-block:: bash

   # Check cluster health
   curl http://elasticsearch.turdparty.localhost/_cluster/health?pretty
   
   # Check node status
   curl http://elasticsearch.turdparty.localhost/_cat/nodes?v
   
   # Check indices status
   curl http://elasticsearch.turdparty.localhost/_cat/indices?v
   
   # Delete problematic indices (WARNING: loses data)
   curl -X DELETE http://elasticsearch.turdparty.localhost/problematic-index-*

**Issue**: Elasticsearch out of disk space

.. code-block:: bash

   # Check disk usage
   curl http://elasticsearch.turdparty.localhost/_cat/allocation?v
   
   # Delete old indices
   curl -X DELETE http://elasticsearch.turdparty.localhost/turdparty-*-2024.01.01
   
   # Configure index lifecycle management
   curl -X PUT http://elasticsearch.turdparty.localhost/_ilm/policy/turdparty-policy \
     -H "Content-Type: application/json" \
     -d '{"policy":{"phases":{"delete":{"min_age":"7d"}}}}'

Logstash Problems
~~~~~~~~~~~~~~~~~

**Issue**: Logstash not receiving data

.. code-block:: bash

   # Check Logstash logs
   docker-compose -f compose/docker-compose.elk.yml logs logstash
   
   # Test Logstash HTTP input
   curl -X POST http://localhost:8080 \
     -H "Content-Type: application/json" \
     -d '{"test": "message", "@timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"}'
   
   # Check Logstash pipeline status
   curl http://localhost:9600/_node/stats/pipelines?pretty

**Issue**: Logstash pipeline errors

.. code-block:: bash

   # Check pipeline configuration
   docker-compose -f compose/docker-compose.elk.yml exec logstash cat /usr/share/logstash/pipeline/logstash.conf
   
   # Validate configuration
   docker-compose -f compose/docker-compose.elk.yml exec logstash /usr/share/logstash/bin/logstash --config.test_and_exit

Kibana Problems
~~~~~~~~~~~~~~~

**Issue**: Kibana not accessible

.. code-block:: bash

   # Check Kibana logs
   docker-compose -f compose/docker-compose.elk.yml logs kibana
   
   # Check Kibana status
   curl http://kibana.turdparty.localhost/api/status
   
   # Restart Kibana
   docker-compose -f compose/docker-compose.elk.yml restart kibana

**Issue**: Kibana dashboards not loading

.. code-block:: bash

   # Re-import dashboards
   ./services/monitoring/elk/kibana/import-dashboards.sh
   
   # Check index patterns
   curl http://kibana.turdparty.localhost/api/saved_objects/_find?type=index-pattern
   
   # Create missing index patterns manually
   curl -X POST http://kibana.turdparty.localhost/api/saved_objects/index-pattern/turdparty-* \
     -H "Content-Type: application/json" \
     -d '{"attributes":{"title":"turdparty-*","timeFieldName":"@timestamp"}}'

VM Management Issues
--------------------

VM Pool Problems
~~~~~~~~~~~~~~~~

**Issue**: No VMs available in pool

.. code-block:: bash

   # Check VM pool status
   curl http://localhost:8000/api/v1/vms/pool/status
   
   # Check pool manager logs
   docker-compose -f compose/docker-compose.workers.yml logs worker_pool
   
   # Manually trigger pool maintenance
   docker-compose -f compose/docker-compose.workers.yml exec worker_pool celery -A celery_app call tasks.vm_pool_manager.maintain_pool

**Issue**: VMs failing to start

.. code-block:: bash

   # Check Docker daemon
   docker info
   
   # Check available resources
   docker system df
   free -h
   
   # Check VM creation logs
   docker-compose -f compose/docker-compose.workers.yml logs worker_vm

**Issue**: VM agent not deploying

.. code-block:: bash

   # Check injection worker logs
   docker-compose -f compose/docker-compose.workers.yml logs worker_injection
   
   # Manually test VM connectivity
   docker exec vm-container-name ls -la /tmp/
   
   # Check agent files
   ls -la services/monitoring/vm-agent/

File Processing Issues
----------------------

Upload Problems
~~~~~~~~~~~~~~~

**Issue**: File upload fails

.. code-block:: bash

   # Check API logs
   docker-compose -f compose/docker-compose.yml logs api
   
   # Check MinIO connectivity
   curl http://minio.turdparty.localhost:9000/minio/health/live
   
   # Check MinIO logs
   docker-compose -f compose/docker-compose.yml logs minio
   
   # Test MinIO access
   docker-compose -f compose/docker-compose.yml exec minio mc ls local/

**Issue**: File processing stuck

.. code-block:: bash

   # Check file worker status
   curl http://flower.turdparty.localhost/api/workers
   
   # Check file worker logs
   docker-compose -f compose/docker-compose.workers.yml logs worker_file
   
   # Check pending tasks
   docker-compose -f compose/docker-compose.workers.yml exec worker_file celery -A celery_app inspect reserved

Workflow Problems
~~~~~~~~~~~~~~~~~

**Issue**: Workflows not starting

.. code-block:: bash

   # Check workflow orchestrator logs
   docker-compose -f compose/docker-compose.workers.yml logs worker_workflow
   
   # Check queue status
   curl http://flower.turdparty.localhost/api/queues
   
   # Check database connectivity
   docker-compose -f compose/docker-compose.yml exec api python -c "from database import engine; print(engine.execute('SELECT 1').scalar())"

**Issue**: Workflows timing out

.. code-block:: bash

   # Check VM execution logs
   docker logs vm-container-name
   
   # Check workflow timeout settings
   grep -r "VM_MAX_EXECUTION_TIME" .env
   
   # Manually terminate stuck VMs
   docker stop vm-container-name
   docker rm vm-container-name

Performance Issues
------------------

High Resource Usage
~~~~~~~~~~~~~~~~~~~

**Issue**: High CPU usage

.. code-block:: bash

   # Check resource usage
   docker stats
   top
   
   # Identify resource-intensive containers
   docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
   
   # Reduce worker concurrency
   # Edit .env file and restart workers
   docker-compose -f compose/docker-compose.workers.yml restart

**Issue**: High memory usage

.. code-block:: bash

   # Check memory usage
   free -h
   docker stats --format "table {{.Container}}\t{{.MemUsage}}\t{{.MemPerc}}"
   
   # Clean up unused resources
   docker system prune -f
   
   # Restart memory-intensive services
   docker-compose -f compose/docker-compose.elk.yml restart elasticsearch

**Issue**: Slow response times

.. code-block:: bash

   # Check database performance
   docker-compose -f compose/docker-compose.yml exec postgres psql -U turdparty -d turdparty -c "SELECT * FROM pg_stat_activity;"
   
   # Check Redis performance
   docker-compose -f compose/docker-compose.yml exec redis redis-cli --latency
   
   # Check ELK performance
   curl http://elasticsearch.turdparty.localhost/_cat/thread_pool?v

Data Issues
-----------

Missing Data
~~~~~~~~~~~~

**Issue**: Data not appearing in Kibana

.. code-block:: bash

   # Check if data is reaching Elasticsearch
   curl "http://elasticsearch.turdparty.localhost/_cat/indices/turdparty-*?v"
   
   # Check recent documents
   curl "http://elasticsearch.turdparty.localhost/turdparty-*/_search?size=1&sort=@timestamp:desc&pretty"
   
   # Check Logstash processing
   curl http://localhost:9600/_node/stats/pipelines?pretty

**Issue**: Incomplete workflow data

.. code-block:: bash

   # Check ELK integration worker
   docker-compose -f compose/docker-compose.workers.yml logs worker_elk
   
   # Check VM agent connectivity
   docker exec vm-container-name curl -X POST http://logstash:8080 -d '{"test":"message"}'
   
   # Manually trigger data streaming
   docker-compose -f compose/docker-compose.workers.yml exec worker_elk celery -A celery_app call tasks.elk_integration.stream_workflow_event

Recovery Procedures
-------------------

Complete System Reset
~~~~~~~~~~~~~~~~~~~~~

**WARNING**: This will destroy all data

.. code-block:: bash

   # Stop all services
   docker-compose -f compose/docker-compose.yml down -v
   docker-compose -f compose/docker-compose.workers.yml down -v
   docker-compose -f compose/docker-compose.elk.yml down -v
   
   # Remove all containers and volumes
   docker system prune -a -f
   docker volume prune -f
   
   # Recreate networks
   docker network create turdpartycollab_net
   docker network create traefik_network
   
   # Start fresh
   docker-compose -f compose/docker-compose.yml up -d
   docker-compose -f compose/docker-compose.workers.yml up -d
   docker-compose -f compose/docker-compose.elk.yml up -d

Partial Recovery
~~~~~~~~~~~~~~~~

**Database Reset** (preserves files):

.. code-block:: bash

   # Stop API and workers
   docker-compose -f compose/docker-compose.yml stop api
   docker-compose -f compose/docker-compose.workers.yml down
   
   # Reset database
   docker-compose -f compose/docker-compose.yml stop postgres
   docker volume rm turdpartycollab_postgres_data
   docker-compose -f compose/docker-compose.yml up -d postgres
   
   # Wait and restart services
   sleep 30
   docker-compose -f compose/docker-compose.yml up -d api
   docker-compose -f compose/docker-compose.workers.yml up -d

**ELK Reset** (preserves workflows):

.. code-block:: bash

   # Stop ELK stack
   docker-compose -f compose/docker-compose.elk.yml down -v
   
   # Start fresh ELK
   docker-compose -f compose/docker-compose.elk.yml up -d
   
   # Wait for initialization
   sleep 60
   
   # Re-import dashboards
   ./services/monitoring/elk/kibana/import-dashboards.sh

Getting Help
------------

Log Collection
~~~~~~~~~~~~~~

When reporting issues, collect relevant logs:

.. code-block:: bash

   # Create log collection script
   mkdir -p /tmp/turdparty-logs
   
   # Collect service logs
   docker-compose -f compose/docker-compose.yml logs > /tmp/turdparty-logs/core-services.log
   docker-compose -f compose/docker-compose.workers.yml logs > /tmp/turdparty-logs/workers.log
   docker-compose -f compose/docker-compose.elk.yml logs > /tmp/turdparty-logs/elk.log
   
   # Collect system info
   docker info > /tmp/turdparty-logs/docker-info.txt
   docker-compose -f compose/docker-compose.yml ps > /tmp/turdparty-logs/service-status.txt
   
   # Create archive
   tar -czf turdparty-logs-$(date +%Y%m%d-%H%M%S).tar.gz -C /tmp turdparty-logs

Support Resources
~~~~~~~~~~~~~~~~~

1. **Documentation**: Check relevant documentation sections
2. **GitHub Issues**: Search existing issues or create new ones
3. **System Logs**: Always include relevant logs when reporting issues
4. **Configuration**: Verify configuration against documentation
5. **Community**: Engage with the community for support

For configuration details, see :doc:`../configuration`.
For installation help, see :doc:`../installation`.
