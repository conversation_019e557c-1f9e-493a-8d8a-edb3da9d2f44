// TurdParty Reports Platform JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard
    initializeDashboard();
    
    // Initialize search functionality
    initializeSearch();
    
    // Initialize report interactions
    initializeReportFeatures();
    
    // Initialize dark mode detection
    initializeDarkMode();
});

function initializeDashboard() {
    // Animate dashboard metrics on load
    const metricValues = document.querySelectorAll('.metric-value');
    metricValues.forEach(metric => {
        const finalValue = parseInt(metric.textContent.replace(/,/g, ''));
        animateCounter(metric, 0, finalValue, 2000);
    });
    
    // Update dashboard data periodically
    setInterval(updateDashboardData, 300000); // 5 minutes
}

function animateCounter(element, start, end, duration) {
    const startTime = performance.now();
    
    function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.floor(start + (end - start) * progress);
        element.textContent = current.toLocaleString();
        
        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        }
    }
    
    requestAnimationFrame(updateCounter);
}

function updateDashboardData() {
    // Fetch latest dashboard metrics from API
    fetch('/api/v1/reports/dashboard/metrics')
        .then(response => response.json())
        .then(data => {
            document.getElementById('total-reports').textContent = data.total_reports.toLocaleString();
            document.getElementById('binaries-analyzed').textContent = data.binaries_analyzed.toLocaleString();
            document.getElementById('threats-detected').textContent = data.threats_detected.toLocaleString();
            document.getElementById('vm-executions').textContent = data.vm_executions.toLocaleString();
        })
        .catch(error => console.log('Dashboard update failed:', error));
}

function initializeSearch() {
    const searchForm = document.getElementById('report-search');
    if (!searchForm) return;
    
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        performSearch();
    });
    
    // Auto-complete for filename search
    const filenameInput = document.getElementById('filename');
    if (filenameInput) {
        filenameInput.addEventListener('input', debounce(suggestFilenames, 300));
    }
}

function performSearch() {
    const formData = new FormData(document.getElementById('report-search'));
    const searchParams = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        if (value.trim()) {
            searchParams.append(key, value);
        }
    }
    
    // Show loading indicator
    showSearchLoading();
    
    // Perform search via API
    fetch(`/api/v1/reports/search?${searchParams}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data);
        })
        .catch(error => {
            console.error('Search failed:', error);
            showSearchError();
        })
        .finally(() => {
            hideSearchLoading();
        });
}

function suggestFilenames(event) {
    const query = event.target.value;
    if (query.length < 2) return;
    
    fetch(`/api/v1/reports/suggest/filenames?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(suggestions => {
            showFilenameSuggestions(suggestions);
        })
        .catch(error => console.log('Suggestion failed:', error));
}

function showFilenameSuggestions(suggestions) {
    // Create or update suggestion dropdown
    let dropdown = document.getElementById('filename-suggestions');
    if (!dropdown) {
        dropdown = document.createElement('div');
        dropdown.id = 'filename-suggestions';
        dropdown.className = 'suggestions-dropdown';
        document.getElementById('filename').parentNode.appendChild(dropdown);
    }
    
    dropdown.innerHTML = '';
    suggestions.forEach(suggestion => {
        const item = document.createElement('div');
        item.className = 'suggestion-item';
        item.textContent = suggestion;
        item.addEventListener('click', () => {
            document.getElementById('filename').value = suggestion;
            dropdown.style.display = 'none';
        });
        dropdown.appendChild(item);
    });
    
    dropdown.style.display = suggestions.length > 0 ? 'block' : 'none';
}

function clearSearch() {
    document.getElementById('report-search').reset();
    const resultsContainer = document.getElementById('search-results');
    if (resultsContainer) {
        resultsContainer.innerHTML = '';
    }
}

function displaySearchResults(data) {
    let resultsContainer = document.getElementById('search-results');
    if (!resultsContainer) {
        resultsContainer = document.createElement('div');
        resultsContainer.id = 'search-results';
        document.getElementById('report-search').parentNode.appendChild(resultsContainer);
    }
    
    if (data.reports.length === 0) {
        resultsContainer.innerHTML = '<p class="no-results">No reports found matching your criteria.</p>';
        return;
    }
    
    let html = `<h3>Search Results (${data.total} found)</h3><div class="results-grid">`;
    
    data.reports.forEach(report => {
        html += `
            <div class="result-card">
                <h4><a href="reports/${report.filename.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}-analysis.html">${report.filename}</a></h4>
                <div class="result-meta">
                    <span class="risk-badge risk-${report.risk_level}">${report.risk_level.toUpperCase()}</span>
                    <span class="date">${new Date(report.last_execution).toLocaleDateString()}</span>
                </div>
                <p class="result-summary">
                    ${report.total_executions} execution(s) | 
                    Size: ${formatFileSize(report.file_size_bytes)}
                </p>
            </div>
        `;
    });
    
    html += '</div>';
    resultsContainer.innerHTML = html;
}

function initializeReportFeatures() {
    // Initialize copy-to-clipboard for code blocks
    initializeCodeCopy();
    
    // Initialize expandable sections
    initializeExpandableSections();
    
    // Initialize report export functionality
    initializeReportExport();
    
    // Initialize comparison features
    initializeComparisonFeatures();
}

function initializeCodeCopy() {
    const codeBlocks = document.querySelectorAll('.highlight');
    codeBlocks.forEach(block => {
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-code-btn';
        copyButton.innerHTML = '📋 Copy';
        copyButton.addEventListener('click', () => {
            const code = block.querySelector('pre').textContent;
            navigator.clipboard.writeText(code).then(() => {
                copyButton.innerHTML = '✅ Copied!';
                setTimeout(() => {
                    copyButton.innerHTML = '📋 Copy';
                }, 2000);
            });
        });
        
        block.style.position = 'relative';
        block.appendChild(copyButton);
    });
}

function initializeExpandableSections() {
    const expandableHeaders = document.querySelectorAll('.expandable-header');
    expandableHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const content = this.nextElementSibling;
            const isExpanded = content.style.display !== 'none';
            
            content.style.display = isExpanded ? 'none' : 'block';
            this.classList.toggle('expanded', !isExpanded);
        });
    });
}

function initializeReportExport() {
    const exportButtons = document.querySelectorAll('.export-btn');
    exportButtons.forEach(button => {
        button.addEventListener('click', function() {
            const format = this.dataset.format;
            const reportId = this.dataset.reportId;
            exportReport(reportId, format);
        });
    });
}

function exportReport(reportId, format) {
    const url = `/api/v1/reports/binary/${reportId}/export?format=${format}`;
    
    // Create temporary download link
    const link = document.createElement('a');
    link.href = url;
    link.download = `report-${reportId}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function initializeComparisonFeatures() {
    const compareCheckboxes = document.querySelectorAll('.compare-checkbox');
    const compareButton = document.getElementById('compare-selected');
    
    if (!compareButton) return;
    
    compareCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateCompareButton);
    });
    
    compareButton.addEventListener('click', performComparison);
}

function updateCompareButton() {
    const selected = document.querySelectorAll('.compare-checkbox:checked');
    const compareButton = document.getElementById('compare-selected');
    
    compareButton.disabled = selected.length < 2;
    compareButton.textContent = `Compare Selected (${selected.length})`;
}

function performComparison() {
    const selected = Array.from(document.querySelectorAll('.compare-checkbox:checked'))
        .map(cb => cb.value);
    
    if (selected.length < 2) return;
    
    // Navigate to comparison page
    const params = new URLSearchParams();
    selected.forEach(id => params.append('reports', id));
    window.location.href = `/reports/compare?${params}`;
}

function initializeDarkMode() {
    // Detect system dark mode preference
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)');
    
    function updateTheme(e) {
        document.body.classList.toggle('dark-mode', e.matches);
    }
    
    // Initial check
    updateTheme(prefersDark);
    
    // Listen for changes
    prefersDark.addEventListener('change', updateTheme);
    
    // Manual dark mode toggle
    const darkModeToggle = document.getElementById('dark-mode-toggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });
    }
    
    // Restore saved preference
    const savedDarkMode = localStorage.getItem('darkMode');
    if (savedDarkMode === 'true') {
        document.body.classList.add('dark-mode');
    }
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function formatFileSize(bytes) {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

function showSearchLoading() {
    const button = document.querySelector('.search-btn');
    button.disabled = true;
    button.innerHTML = '⏳ Searching...';
}

function hideSearchLoading() {
    const button = document.querySelector('.search-btn');
    button.disabled = false;
    button.innerHTML = '🔍 Search Reports';
}

function showSearchError() {
    const resultsContainer = document.getElementById('search-results');
    if (resultsContainer) {
        resultsContainer.innerHTML = '<p class="search-error">Search failed. Please try again.</p>';
    }
}

// Real-time updates via WebSocket (if available)
function initializeRealTimeUpdates() {
    if (typeof WebSocket === 'undefined') return;
    
    const ws = new WebSocket('ws://api.turdparty.localhost/ws/reports');
    
    ws.onmessage = function(event) {
        const data = JSON.parse(event.data);
        
        switch (data.type) {
            case 'new_report':
                showNewReportNotification(data.report);
                break;
            case 'analysis_complete':
                updateAnalysisStatus(data.report_id, 'complete');
                break;
            case 'analysis_failed':
                updateAnalysisStatus(data.report_id, 'failed');
                break;
        }
    };
    
    ws.onerror = function(error) {
        console.log('WebSocket error:', error);
    };
}

function showNewReportNotification(report) {
    const notification = document.createElement('div');
    notification.className = 'notification new-report';
    notification.innerHTML = `
        <div class="notification-content">
            <h4>📊 New Report Available</h4>
            <p>${report.filename} analysis complete</p>
            <a href="reports/${report.id}" class="notification-link">View Report</a>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

function updateAnalysisStatus(reportId, status) {
    const statusElements = document.querySelectorAll(`[data-report-id="${reportId}"] .status`);
    statusElements.forEach(element => {
        element.className = `status ${status}`;
        element.textContent = status === 'complete' ? '✅ Complete' : '❌ Failed';
    });
}

// Initialize real-time updates if supported
document.addEventListener('DOMContentLoaded', initializeRealTimeUpdates);
