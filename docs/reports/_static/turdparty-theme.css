/* TurdParty Reports Platform Custom Theme */

:root {
    --turdparty-primary: #3498db;
    --turdparty-secondary: #2c3e50;
    --turdparty-success: #27ae60;
    --turdparty-warning: #f39c12;
    --turdparty-danger: #e74c3c;
    --turdparty-info: #17a2b8;
    --turdparty-dark: #1a1a1a;
    --turdparty-light: #ecf0f1;
    --turdparty-bg: #2c3e50;
    --turdparty-text: #ecf0f1;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .wy-nav-side {
        background: var(--turdparty-dark) !important;
    }
    
    .wy-side-nav-search {
        background: var(--turdparty-secondary) !important;
    }
    
    .wy-menu-vertical a {
        color: var(--turdparty-light) !important;
    }
}

/* Report header styling */
.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: linear-gradient(135deg, var(--turdparty-secondary), var(--turdparty-primary));
    border-radius: 8px;
    color: white;
}

.report-classification {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 0.8rem;
}

.report-classification.public { background: var(--turdparty-success); }
.report-classification.internal { background: var(--turdparty-warning); }
.report-classification.confidential { background: var(--turdparty-danger); }
.report-classification.restricted { background: #8e44ad; }

.report-metadata {
    text-align: right;
    font-size: 0.9rem;
}

.report-id {
    display: block;
    font-family: monospace;
    font-weight: bold;
}

.report-date {
    display: block;
    opacity: 0.8;
}

/* Badge styling */
.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: bold;
    border-radius: 0.25rem;
    text-transform: uppercase;
}

.badge-success { background: var(--turdparty-success); color: white; }
.badge-warning { background: var(--turdparty-warning); color: white; }
.badge-danger { background: var(--turdparty-danger); color: white; }
.badge-info { background: var(--turdparty-info); color: white; }

/* Dashboard styling */
.dashboard-container {
    margin: 2rem 0;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 1rem 0;
}

.dashboard-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.dashboard-card h3 {
    margin: 0 0 1rem 0;
    color: var(--turdparty-secondary);
    font-size: 1rem;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--turdparty-primary);
    margin: 0.5rem 0;
}

.metric-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Search form styling */
.search-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 2rem 0;
}

.search-form {
    display: grid;
    gap: 1rem;
}

.search-row {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-row label {
    min-width: 100px;
    font-weight: bold;
    color: var(--turdparty-secondary);
}

.search-row input,
.search-row select {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
}

.search-btn,
.clear-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s ease;
}

.search-btn {
    background: var(--turdparty-primary);
    color: white;
}

.search-btn:hover {
    background: #2980b9;
}

.clear-btn {
    background: #6c757d;
    color: white;
}

.clear-btn:hover {
    background: #5a6268;
}

/* Resource charts */
.resource-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin: 2rem 0;
}

.chart-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
}

.chart-container h4 {
    margin: 0 0 1rem 0;
    color: var(--turdparty-secondary);
}

.chart-placeholder {
    display: flex;
    align-items: end;
    height: 150px;
    gap: 0.5rem;
    margin: 1rem 0;
}

.chart-bar {
    flex: 1;
    background: linear-gradient(to top, var(--turdparty-primary), #5dade2);
    border-radius: 4px 4px 0 0;
    min-height: 10px;
    display: flex;
    align-items: end;
    justify-content: center;
    color: white;
    font-size: 0.7rem;
    font-weight: bold;
    padding: 0.25rem;
}

/* Event distribution */
.event-distribution {
    margin: 2rem 0;
}

.event-category {
    margin: 1rem 0;
}

.event-category h4 {
    margin: 0 0 0.5rem 0;
    color: var(--turdparty-secondary);
}

.event-bar {
    height: 30px;
    background: linear-gradient(to right, var(--turdparty-primary), #5dade2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    transition: width 0.3s ease;
}

/* Grid cards */
.grid-item-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    transition: transform 0.2s ease;
    text-decoration: none;
    color: inherit;
}

.grid-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    text-decoration: none;
}

.impact-card { border-left: 4px solid var(--turdparty-success); }
.runtime-card { border-left: 4px solid var(--turdparty-primary); }
.security-card { border-left: 4px solid var(--turdparty-warning); }
.behavior-card { border-left: 4px solid var(--turdparty-info); }

/* Code blocks with syntax highlighting */
.highlight {
    border-radius: 8px;
    overflow: hidden;
}

.highlight pre {
    margin: 0;
    padding: 1rem;
    background: var(--turdparty-dark);
    color: var(--turdparty-light);
}

/* Registry code blocks */
.highlight-registry {
    background: #2d3748;
    color: #e2e8f0;
}

.highlight-registry .k { color: #63b3ed; } /* Keywords */
.highlight-registry .s { color: #68d391; } /* Strings */
.highlight-registry .c { color: #a0aec0; } /* Comments */

/* Responsive design */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .resource-charts {
        grid-template-columns: 1fr;
    }
    
    .search-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-row label {
        min-width: auto;
    }
    
    .report-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

/* Report footer */
.report-footer {
    margin-top: 3rem;
    padding: 2rem;
    background: linear-gradient(135deg, var(--turdparty-secondary), var(--turdparty-primary));
    color: white;
    text-align: center;
    border-radius: 8px;
}

.report-footer p {
    margin: 0.5rem 0;
}

/* Admonition styling */
.admonition {
    border-radius: 8px;
    overflow: hidden;
}

.admonition.tip {
    border-left: 4px solid var(--turdparty-success);
}

.admonition.note {
    border-left: 4px solid var(--turdparty-info);
}

.admonition.warning {
    border-left: 4px solid var(--turdparty-warning);
}

.admonition.danger {
    border-left: 4px solid var(--turdparty-danger);
}

/* Table styling */
.wy-table-responsive table {
    border-radius: 8px;
    overflow: hidden;
}

.wy-table-responsive table th {
    background: var(--turdparty-secondary);
    color: white;
}

.wy-table-responsive table tr:nth-child(even) {
    background: #f8f9fa;
}

/* Mermaid diagram styling */
.mermaid {
    text-align: center;
    margin: 2rem 0;
}

/* Print styles */
@media print {
    .wy-nav-side,
    .wy-nav-content-wrap .wy-nav-content .search-container,
    .dashboard-container {
        display: none;
    }
    
    .wy-nav-content {
        margin-left: 0;
    }
    
    .report-header {
        background: #f8f9fa !important;
        color: black !important;
        border: 1px solid #dee2e6;
    }
}
