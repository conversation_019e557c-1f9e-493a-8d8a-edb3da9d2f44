API Overview
============

The TurdParty Reports Platform provides comprehensive REST API endpoints for accessing binary analysis reports, generating custom reports, and integrating with external systems.

Base URL
--------

All API endpoints are available at:

.. code-block:: text

   http://api.turdparty.localhost/api/v1/

Authentication
--------------

Currently, the API operates in development mode without authentication. In production environments, API keys or OAuth2 authentication will be required.

Rate Limiting
-------------

API requests are rate-limited to prevent abuse:

* **Development**: 1000 requests per hour per IP
* **Production**: 100 requests per hour per API key

Core Endpoints
--------------

Report Generation
~~~~~~~~~~~~~~~~~

.. http:get:: /reports/binary/(uuid:file_uuid)

   Get comprehensive binary analysis report

   :param file_uuid: UUID of the analyzed binary
   :type file_uuid: string
   :statuscode 200: Report found and returned
   :statuscode 404: Report not found
   :statuscode 500: Internal server error

   **Example Request:**

   .. code-block:: bash

      curl "http://api.turdparty.localhost/api/v1/reports/binary/d5cc1f03-3041-46f5-8ad9-7af2b270ddbb"

   **Example Response:**

   .. code-block:: json

      {
        "success": true,
        "data": {
          "file_info": {
            "filename": "npp.8.5.8.Installer.x64.exe",
            "file_size_bytes": 4796432,
            "file_type": "PE32+ executable"
          },
          "security_analysis": {
            "threat_indicators": {
              "risk_level": "low",
              "suspicious_behavior_score": 0
            }
          }
        }
      }

.. http:get:: /reports/binary/(uuid:file_uuid)/summary

   Get executive summary of binary analysis

   :param file_uuid: UUID of the analyzed binary
   :statuscode 200: Summary generated successfully

.. http:get:: /reports/binary/(uuid:file_uuid)/footprint

   Get installation footprint analysis

   :param file_uuid: UUID of the analyzed binary
   :statuscode 200: Footprint analysis returned

.. http:get:: /reports/binary/(uuid:file_uuid)/runtime

   Get runtime behavior analysis

   :param file_uuid: UUID of the analyzed binary
   :statuscode 200: Runtime analysis returned

Search and Discovery
~~~~~~~~~~~~~~~~~~~~

.. http:get:: /reports/search

   Search reports by various criteria

   :query filename: Filter by filename (partial match)
   :query risk_level: Filter by risk level (low, medium, high, critical)
   :query date_from: Start date for date range filter (YYYY-MM-DD)
   :query date_to: End date for date range filter (YYYY-MM-DD)
   :query limit: Maximum number of results (default: 50, max: 500)
   :query offset: Pagination offset (default: 0)

   **Example Request:**

   .. code-block:: bash

      curl "http://api.turdparty.localhost/api/v1/reports/search?filename=notepad&risk_level=low&limit=10"

.. http:get:: /reports/suggest/filenames

   Get filename suggestions for autocomplete

   :query q: Query string for suggestions (minimum 2 characters)
   :statuscode 200: Suggestions returned

Dashboard and Metrics
~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /reports/dashboard/metrics

   Get dashboard metrics and statistics

   **Example Response:**

   .. code-block:: json

      {
        "total_reports": 1247,
        "binaries_analyzed": 892,
        "threats_detected": 23,
        "vm_executions": 1156,
        "last_updated": "2025-06-12T09:57:22Z"
      }

.. http:get:: /reports/dashboard/recent

   Get recent analysis activity

   :query limit: Number of recent activities (default: 10, max: 100)

Export and Integration
~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /reports/binary/(uuid:file_uuid)/export

   Export report in various formats

   :query format: Export format (json, pdf, xml, csv)
   :statuscode 200: Export successful
   :statuscode 400: Invalid format specified

   **Supported Formats:**

   * **JSON**: Complete structured data
   * **PDF**: Formatted report document
   * **XML**: STIX/TAXII compatible format
   * **CSV**: Tabular data for analysis

.. http:post:: /reports/compare

   Compare multiple binary reports

   **Request Body:**

   .. code-block:: json

      {
        "report_ids": [
          "d5cc1f03-3041-46f5-8ad9-7af2b270ddbb",
          "a1b2c3d4-e5f6-7890-1234-************"
        ],
        "comparison_type": "side_by_side"
      }

WebSocket Endpoints
-------------------

Real-time Updates
~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   // Connect to real-time updates
   const ws = new WebSocket('ws://api.turdparty.localhost/ws/reports');
   
   ws.onmessage = function(event) {
       const data = JSON.parse(event.data);
       console.log('Report update:', data);
   };

**Message Types:**

* ``new_report``: New analysis report available
* ``analysis_complete``: Binary analysis completed
* ``analysis_failed``: Binary analysis failed
* ``metrics_update``: Dashboard metrics updated

Error Handling
--------------

All API responses follow a consistent error format:

.. code-block:: json

   {
     "success": false,
     "error": {
       "code": "HTTP_404",
       "message": "Report not found",
       "status_code": 404,
       "details": {
         "file_uuid": "invalid-uuid-format",
         "suggestion": "Check UUID format and try again"
       }
     },
     "timestamp": "2025-06-12T09:57:22Z",
     "request_id": "req_123456789"
   }

**Common Error Codes:**

* ``HTTP_400``: Bad Request - Invalid parameters
* ``HTTP_404``: Not Found - Resource not found
* ``HTTP_429``: Too Many Requests - Rate limit exceeded
* ``HTTP_500``: Internal Server Error - Server error

SDK Libraries
-------------

Python SDK
~~~~~~~~~~

.. code-block:: bash

   pip install turdparty-reports-sdk

.. code-block:: python

   from turdparty_reports import ReportsClient
   
   client = ReportsClient(base_url="http://api.turdparty.localhost/api/v1")
   
   # Get report
   report = client.get_report("d5cc1f03-3041-46f5-8ad9-7af2b270ddbb")
   
   # Search reports
   results = client.search_reports(filename="notepad", risk_level="low")
   
   # Export report
   pdf_data = client.export_report("uuid", format="pdf")

JavaScript SDK
~~~~~~~~~~~~~~

.. code-block:: bash

   npm install @turdparty/reports-sdk

.. code-block:: javascript

   import { ReportsClient } from '@turdparty/reports-sdk';
   
   const client = new ReportsClient({
     baseUrl: 'http://api.turdparty.localhost/api/v1'
   });
   
   // Get report
   const report = await client.getReport('d5cc1f03-3041-46f5-8ad9-7af2b270ddbb');
   
   // Search reports
   const results = await client.searchReports({
     filename: 'notepad',
     riskLevel: 'low'
   });

Integration Examples
--------------------

SIEM Integration
~~~~~~~~~~~~~~~~

.. code-block:: python

   # Splunk integration example
   import requests
   import json
   
   def send_to_splunk(report_data):
       splunk_url = "https://splunk.company.com:8088/services/collector"
       headers = {
           "Authorization": "Splunk your-token-here",
           "Content-Type": "application/json"
       }
       
       event = {
           "time": report_data["timestamp"],
           "source": "turdparty",
           "sourcetype": "binary_analysis",
           "event": report_data
       }
       
       response = requests.post(splunk_url, headers=headers, data=json.dumps(event))
       return response.status_code == 200

Threat Intelligence Platform
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # MISP integration example
   from pymisp import PyMISP
   
   def create_misp_event(report_data):
       misp = PyMISP('https://misp.company.com', 'your-api-key')
       
       event = misp.new_event(
           distribution=1,
           threat_level_id=4,
           analysis=1,
           info=f"TurdParty Analysis: {report_data['file_info']['filename']}"
       )
       
       # Add file hash attributes
       misp.add_hashes(event, 
           md5=report_data['file_info']['hashes']['md5'],
           sha256=report_data['file_info']['hashes']['sha256']
       )
       
       return misp.add_event(event)

Best Practices
--------------

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~

* Use pagination for large result sets
* Cache frequently accessed reports
* Use WebSocket connections for real-time updates
* Implement client-side rate limiting

Security Considerations
~~~~~~~~~~~~~~~~~~~~~~

* Validate all input parameters
* Use HTTPS in production environments
* Implement proper authentication and authorization
* Log all API access for audit purposes

Monitoring and Alerting
~~~~~~~~~~~~~~~~~~~~~~

* Monitor API response times and error rates
* Set up alerts for high-risk binary detections
* Track API usage patterns and quotas
* Implement health checks for all endpoints
