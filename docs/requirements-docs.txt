# Documentation build requirements for TurdParty

# Core Sphinx
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0
sphinx-autobuild>=2021.3.14

# Sphinx Extensions
sphinxcontrib-openapi>=0.8.1
sphinxcontrib-httpdomain>=1.8.1
sphinxcontrib-websupport>=1.2.4
sphinx-copybutton>=0.5.2
sphinx-tabs>=3.4.1
sphinx-design>=0.5.0

# Markdown Support
myst-parser>=2.0.0
myst-nb>=0.17.2

# API Documentation
sphinx-autoapi>=2.1.1
sphinx-autodoc-typehints>=1.24.0

# Diagrams and Charts
sphinxcontrib-mermaid>=0.9.2
sphinxcontrib-plantuml>=0.25
sphinx-diagrams>=0.3.0

# Code Quality
doc8>=1.1.1
rstcheck>=6.1.3
sphinx-lint>=0.6.7

# Live Reload and Development
livereload>=2.6.3
watchdog>=3.0.0

# PDF Generation
rst2pdf>=0.101

# Additional Utilities
sphinx-notfound-page>=0.8.3
sphinx-sitemap>=2.5.1
sphinx-external-toc>=0.3.1

# Theme Customization
sphinx-book-theme>=1.0.1
furo>=2023.7.26

# Search Enhancement
sphinx-search>=0.2.4

# Internationalization
sphinx-intl>=2.1.0

# Performance
sphinx-external-toc-strict>=0.1.0

# Code Highlighting
pygments>=2.15.1
pygments-github-lexers>=0.0.5

# Documentation Testing
pytest-sphinx>=0.5.0
sphinx-pytest>=0.1.1

# Version Management
sphinx-version-warning>=1.1.2
sphinx-last-updated-by-git>=0.3.4

# Analytics and Tracking
sphinx-analytics>=0.2.1

# Social Media Integration
sphinx-social-cards>=0.1.0

# Advanced Features
sphinx-togglebutton>=0.3.2
sphinx-thebe>=0.2.1
sphinx-gallery>=0.13.0

# Quality Assurance
linkchecker>=10.2.1
html5validator>=0.4.2

# Build Tools
wheel>=0.41.0
setuptools>=68.0.0

# Development Dependencies
pre-commit>=3.3.3
black>=23.7.0
isort>=5.12.0
flake8>=6.0.0
