VM Monitoring Agent
====================

Overview
--------

The TurdParty VM Monitoring Agent is a lightweight Python application that runs inside virtual machines during malware analysis to collect comprehensive runtime data. The agent provides real-time monitoring of system metrics, process activity, file operations, and network behavior, streaming all data directly to the ELK stack for analysis and visualization.

Architecture
------------

.. mermaid::

   graph TB
       subgraph "VM Environment"
           AGENT[VM Monitoring Agent]
           SYS[System Metrics Collector]
           PROC[Process Monitor]
           FILE[File System Monitor]
           NET[Network Monitor]
       end
       
       subgraph "Data Collection"
           CPU[CPU Usage]
           MEM[Memory Usage]
           DISK[Disk I/O]
           PROCS[Process List]
           FILES[File Events]
           CONNS[Network Connections]
       end
       
       subgraph "ELK Stack"
           LS[Logstash]
           ES[Elasticsearch]
           KB[Kibana]
       end
       
       AGENT --> SYS
       AGENT --> PROC
       AGENT --> FILE
       AGENT --> NET
       
       SYS --> CPU
       SYS --> MEM
       SYS --> DISK
       PROC --> PROCS
       FILE --> FILES
       NET --> CONNS
       
       AGENT --> LS
       LS --> ES
       ES --> KB

Core Components
---------------

System Metrics Collector
~~~~~~~~~~~~~~~~~~~~~~~~~

**Purpose**: Collect real-time system performance metrics

**Metrics Collected**:
   - **CPU Usage**: Percentage utilization, load averages, core count
   - **Memory Usage**: Total, used, available, swap usage
   - **Disk Usage**: Space utilization, I/O rates, read/write operations
   - **Network Usage**: Bytes sent/received, packet counts, connection statistics

**Collection Frequency**: 5-second intervals (configurable)

**Implementation**:

.. code-block:: python

   def collect_cpu_metrics(self) -> Dict[str, Any]:
       cpu_percent = psutil.cpu_percent(interval=1)
       cpu_count = psutil.cpu_count()
       load_avg = os.getloadavg()
       
       return {
           "usage": cpu_percent,
           "cores": cpu_count,
           "load_avg_1m": load_avg[0],
           "load_avg_5m": load_avg[1],
           "load_avg_15m": load_avg[2]
       }

Process Monitor
~~~~~~~~~~~~~~~

**Purpose**: Track running processes and detect suspicious activity

**Data Collected**:
   - Process ID (PID) and parent process ID (PPID)
   - Process name and command line arguments
   - CPU and memory usage per process
   - Process creation and termination times
   - Working directory and executable path

**Suspicious Activity Detection**:
   - High resource usage processes (CPU > 80%, Memory > 50%)
   - Network tools (wget, curl, netcat, nmap)
   - Scripting interpreters with suspicious arguments
   - Processes with unusual names or paths

**Implementation**:

.. code-block:: python

   def get_suspicious_processes(self, processes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
       suspicious = []
       suspicious_names = ['nc', 'netcat', 'wget', 'curl', 'python', 'perl']
       
       for proc in processes:
           is_suspicious = False
           reasons = []
           
           if any(name in proc['name'].lower() for name in suspicious_names):
               is_suspicious = True
               reasons.append("suspicious_name")
           
           if proc['cpu_percent'] > 80.0:
               is_suspicious = True
               reasons.append("high_cpu")
           
           if is_suspicious:
               proc['suspicious_reasons'] = reasons
               suspicious.append(proc)
       
       return suspicious

File System Monitor
~~~~~~~~~~~~~~~~~~~

**Purpose**: Track file system operations and changes

**Events Monitored**:
   - File creation, modification, deletion
   - Directory creation and removal
   - File moves and renames
   - Permission changes

**Monitored Paths** (configurable):
   - ``/tmp`` - Temporary files
   - ``/home`` - User directories
   - ``/var/log`` - System logs
   - Custom paths based on analysis requirements

**Implementation**:

.. code-block:: python

   class FileSystemMonitor(FileSystemEventHandler):
       def on_any_event(self, event):
           if event.is_directory:
               return
           
           event_data = {
               "event_type": event.event_type,
               "src_path": event.src_path,
               "timestamp": datetime.utcnow().isoformat()
           }
           
           self.elk_streamer.send_file_events([event_data])

Network Monitor
~~~~~~~~~~~~~~~

**Purpose**: Monitor network activity and connections

**Data Collected**:
   - Active network connections (TCP/UDP)
   - Connection states and protocols
   - Local and remote IP addresses and ports
   - Network traffic volume (bytes/packets)
   - Connection establishment and termination

**Security Monitoring**:
   - External connections to suspicious IPs
   - Unusual port usage patterns
   - High-volume data transfers
   - Protocol anomalies

ELK Integration
---------------

Data Streaming
~~~~~~~~~~~~~~~

**ECS Compliance**: All data formatted according to Elastic Common Schema

**Stream Configuration**:
   - **Endpoint**: Logstash HTTP input (port 8080)
   - **Format**: JSON with ECS field structure
   - **Buffering**: Batch transmission for efficiency
   - **Retry Logic**: Automatic retry with exponential backoff

**Data Structure**:

.. code-block:: json

   {
     "@timestamp": "2024-01-15T10:30:45.123Z",
     "ecs": {"version": "8.11.0"},
     "event": {
       "kind": "metric",
       "category": ["host"],
       "type": ["info"],
       "action": "vm_metrics",
       "dataset": "turdparty.vm_monitoring"
     },
     "service": {
       "name": "turdparty-vm-agent",
       "type": "monitoring",
       "version": "1.0.0"
     },
     "host": {
       "name": "vm-ubuntu-001",
       "cpu": {"usage": 45.2},
       "memory": {"usage": 67.8, "total": **********}
     },
     "turdparty": {
       "vm_id": "uuid-string",
       "vm_name": "vm-ubuntu-001",
       "workflow_id": "workflow-uuid",
       "phase": "runtime"
     }
   }

Error Handling
~~~~~~~~~~~~~~

**Connection Management**:
   - Automatic reconnection on network failures
   - Graceful degradation when ELK unavailable
   - Local buffering during connectivity issues
   - Health check monitoring

**Data Validation**:
   - Schema validation before transmission
   - Data sanitization and filtering
   - Error logging and debugging
   - Performance monitoring

Agent Deployment
-----------------

Automated Injection
~~~~~~~~~~~~~~~~~~~~

**Deployment Process**:
   1. Agent package creation with VM-specific configuration
   2. Package transfer to VM via Docker exec or SSH
   3. Automatic installation and service setup
   4. Health verification and monitoring start

**Package Contents**:
   - ``agent.py``: Main agent application
   - ``requirements.txt``: Python dependencies
   - ``config/agent.yml``: Configuration file
   - ``install.sh``: Installation script

**Installation Script**:

.. code-block:: bash

   #!/bin/bash
   # Install Python dependencies
   pip3 install -r requirements.txt
   
   # Set environment variables
   export TURDPARTY_VM_ID="vm-uuid"
   export TURDPARTY_VM_NAME="vm-name"
   export TURDPARTY_WORKFLOW_ID="workflow-uuid"
   
   # Create systemd service
   cat > /etc/systemd/system/turdparty-agent.service << EOF
   [Unit]
   Description=TurdParty VM Monitoring Agent
   After=network.target
   
   [Service]
   Type=simple
   User=root
   WorkingDirectory=$(pwd)
   ExecStart=/usr/bin/python3 agent.py
   Restart=always
   RestartSec=10
   
   [Install]
   WantedBy=multi-user.target
   EOF
   
   # Start service
   systemctl daemon-reload
   systemctl enable turdparty-agent
   systemctl start turdparty-agent

Configuration Management
~~~~~~~~~~~~~~~~~~~~~~~~

**Environment Variables**:
   - ``TURDPARTY_VM_ID``: Unique VM identifier
   - ``TURDPARTY_VM_NAME``: Human-readable VM name
   - ``TURDPARTY_WORKFLOW_ID``: Associated workflow ID
   - ``LOGSTASH_HOST``: Logstash hostname (default: host.docker.internal)
   - ``COLLECTION_INTERVAL``: Metrics collection interval (default: 5 seconds)
   - ``MONITORED_PATHS``: Comma-separated list of paths to monitor

**Configuration File** (``config/agent.yml``):

.. code-block:: yaml

   # ELK Stack Configuration
   elk:
     logstash:
       host: "logstash"
       port: 8080
       timeout: 5
   
   # Collection Settings
   collection:
     interval: 5  # seconds
     batch_size: 10  # events per batch
   
   # Monitoring Configuration
   monitoring:
     system:
       enabled: true
       cpu: true
       memory: true
       disk: true
       network: true
     
     processes:
       enabled: true
       suspicious_detection: true
       max_processes: 100
     
     filesystem:
       enabled: true
       paths:
         - "/tmp"
         - "/home"
         - "/var/log"
       recursive: true

Performance Characteristics
---------------------------

Resource Usage
~~~~~~~~~~~~~~

**Memory Footprint**:
   - Base usage: ~50MB
   - Peak usage: ~100MB (during high activity)
   - Configurable buffer sizes
   - Automatic garbage collection

**CPU Usage**:
   - Idle: <1% CPU usage
   - Active monitoring: 2-5% CPU usage
   - Burst activity: Up to 10% CPU usage
   - Optimized collection algorithms

**Network Usage**:
   - Typical: 1-5 KB/s data transmission
   - Peak: 10-50 KB/s during high activity
   - Compressed data transmission
   - Batch processing for efficiency

Data Volume
~~~~~~~~~~~

**Metrics per Minute**:
   - System metrics: 12 data points (5-second intervals)
   - Process data: Variable based on process count
   - File events: Variable based on file activity
   - Network data: Continuous connection monitoring

**Daily Data Volume** (per VM):
   - System metrics: ~50MB
   - Process monitoring: ~100MB
   - File events: ~20MB (typical)
   - Network monitoring: ~30MB
   - **Total**: ~200MB per VM per day

Security Considerations
-----------------------

Agent Security
~~~~~~~~~~~~~~~

**Isolation**:
   - Runs in VM environment (already isolated)
   - Minimal system privileges required
   - Read-only access to most system resources
   - Secure communication with ELK stack

**Data Protection**:
   - No sensitive data collection
   - Anonymized process information
   - Secure transmission protocols
   - Data encryption in transit

**Access Control**:
   - Limited file system access
   - Network communication restrictions
   - Process monitoring boundaries
   - Audit logging of agent activities

Monitoring Security
~~~~~~~~~~~~~~~~~~~

**Threat Detection**:
   - Automated suspicious activity detection
   - Behavioral analysis and anomaly detection
   - IOC extraction and classification
   - Real-time alerting capabilities

**Data Integrity**:
   - Checksum validation for transmitted data
   - Timestamp verification
   - Data consistency checks
   - Error detection and correction

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Agent Not Starting**:
   - Check Python dependencies installation
   - Verify network connectivity to Logstash
   - Review system permissions
   - Check log files for error messages

**Data Not Appearing in Kibana**:
   - Verify Logstash connectivity
   - Check Elasticsearch index creation
   - Validate data format and ECS compliance
   - Review Logstash pipeline configuration

**High Resource Usage**:
   - Adjust collection intervals
   - Reduce monitored paths
   - Optimize buffer sizes
   - Check for memory leaks

Debugging Tools
~~~~~~~~~~~~~~~

**Log Analysis**:
   - Agent logs: ``/var/log/turdparty-agent.log``
   - System logs: ``journalctl -u turdparty-agent``
   - ELK logs: Logstash and Elasticsearch logs

**Health Checks**:
   - Agent status: ``systemctl status turdparty-agent``
   - Network connectivity: ``curl http://logstash:8080``
   - Resource usage: ``top``, ``htop``, ``ps aux``

**Data Validation**:
   - Manual data transmission testing
   - ECS schema validation
   - Elasticsearch query testing
   - Kibana visualization verification

Customization and Extension
---------------------------

Custom Collectors
~~~~~~~~~~~~~~~~~

**Adding New Metrics**:

.. code-block:: python

   class CustomMetricsCollector:
       def collect_custom_metrics(self) -> Dict[str, Any]:
           # Implement custom metric collection
           return {
               "custom_metric": self.get_custom_value(),
               "timestamp": datetime.utcnow().isoformat()
           }

**Integration Points**:
   - Plugin architecture for custom collectors
   - Configuration-driven metric selection
   - Dynamic collector loading
   - Custom data formatting

Advanced Configuration
~~~~~~~~~~~~~~~~~~~~~~

**Conditional Monitoring**:
   - File type-based monitoring rules
   - Process-specific collection settings
   - Network protocol filtering
   - Time-based collection schedules

**Performance Tuning**:
   - Collection interval optimization
   - Buffer size adjustment
   - Network timeout configuration
   - Resource limit enforcement

Future Enhancements
-------------------

Planned Features
~~~~~~~~~~~~~~~~

**Enhanced Detection**:
   - Machine learning-based anomaly detection
   - Behavioral pattern recognition
   - Advanced IOC correlation
   - Threat intelligence integration

**Extended Monitoring**:
   - Registry monitoring (Windows)
   - API call tracing
   - Memory dump analysis
   - Kernel-level monitoring

**Improved Performance**:
   - Optimized data compression
   - Intelligent sampling algorithms
   - Predictive resource management
   - Advanced caching strategies
