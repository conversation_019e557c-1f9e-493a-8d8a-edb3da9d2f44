Virtual Machine Management
===========================

The TurdParty platform provides comprehensive virtual machine management capabilities for malware analysis, supporting both Docker containers and Vagrant VMs with real-time monitoring and control.

VM Types
--------

Docker Containers
~~~~~~~~~~~~~~~~~

Docker containers provide lightweight, fast-starting analysis environments:

**Advantages:**
- Fast startup times (< 5 seconds)
- Low resource overhead
- Easy scaling and orchestration
- Extensive image ecosystem

**Use Cases:**
- Quick malware triage
- Automated analysis pipelines
- High-throughput processing
- Linux-based analysis

**Example Configuration:**

.. code-block:: json

   {
       "name": "docker-analysis-vm",
       "vm_type": "docker",
       "template": "ubuntu:20.04",
       "memory_mb": 1024,
       "cpus": 2,
       "environment": {
           "ANALYSIS_TIMEOUT": "300",
           "ENABLE_NETWORKING": "false"
       },
       "volumes": [
           {
               "host_path": "/samples",
               "container_path": "/analysis/samples",
               "mode": "ro"
           }
       ]
   }

Vagrant VMs
~~~~~~~~~~~

Vagrant VMs provide full virtualisation with complete OS isolation:

**Advantages:**
- Complete OS isolation
- Support for Windows analysis
- Hardware-level isolation
- Snapshot capabilities

**Use Cases:**
- Windows malware analysis
- Advanced persistent threats
- Kernel-level analysis
- Long-running investigations

**Example Configuration:**

.. code-block:: json

   {
       "name": "vagrant-windows-vm",
       "vm_type": "vagrant",
       "template": "windows:10",
       "memory_mb": 4096,
       "cpus": 4,
       "provider": "virtualbox",
       "network": {
           "type": "private_network",
           "ip": "*************"
       },
       "provisioning": [
           {
               "type": "shell",
               "script": "install-analysis-tools.ps1"
           }
       ]
   }

VM Lifecycle
------------

Creation
~~~~~~~~

VMs go through several stages during creation:

1. **Validation** - Request parameters are validated
2. **Resource Allocation** - System resources are reserved
3. **Image Preparation** - Base image is prepared/downloaded
4. **Container/VM Creation** - Actual VM is created
5. **Network Setup** - Network interfaces are configured
6. **Service Registration** - VM is registered with monitoring services

.. mermaid::

   graph TD
       A[Create Request] --> B[Validate Parameters]
       B --> C[Check Resources]
       C --> D[Prepare Image]
       D --> E[Create VM]
       E --> F[Configure Network]
       F --> G[Start Services]
       G --> H[VM Ready]

**Status Progression:**

.. code-block:: text

   creating → starting → running
            ↓
           error

Running State
~~~~~~~~~~~~~

Once running, VMs provide:

- Real-time performance monitoring
- Command execution capabilities
- File system access
- Network traffic monitoring
- Process monitoring

**Available Operations:**

- Execute commands via WebSocket
- Upload/download files
- Monitor system metrics
- Capture network traffic
- Take snapshots (Vagrant VMs)

Termination
~~~~~~~~~~~

VM termination includes:

1. **Graceful Shutdown** - Services are stopped cleanly
2. **Data Collection** - Analysis results are gathered
3. **Resource Cleanup** - System resources are freed
4. **Log Archival** - Logs are archived for later analysis

VM Templates
------------

Available Templates
~~~~~~~~~~~~~~~~~~~

.. list-table::
   :widths: 20 15 15 50
   :header-rows: 1

   * - Template
     - Type
     - Size
     - Description
   * - ``ubuntu:20.04``
     - Docker
     - 512MB
     - Ubuntu 20.04 with analysis tools
   * - ``ubuntu:22.04``
     - Docker
     - 512MB
     - Ubuntu 22.04 with modern toolchain
   * - ``kali:latest``
     - Docker
     - 1GB
     - Kali Linux with penetration testing tools
   * - ``windows:10``
     - Vagrant
     - 4GB
     - Windows 10 with Fibratus monitoring
   * - ``windows:server2019``
     - Vagrant
     - 4GB
     - Windows Server 2019 for enterprise analysis
   * - ``macos:monterey``
     - Vagrant
     - 8GB
     - macOS Monterey (requires special licensing)

Template Customisation
~~~~~~~~~~~~~~~~~~~~~~

Templates can be customised with additional tools and configurations:

.. code-block:: json

   {
       "base_template": "ubuntu:20.04",
       "customisations": {
           "packages": [
               "volatility3",
               "yara",
               "clamav-daemon",
               "wireshark-common"
           ],
           "scripts": [
               "setup-analysis-environment.sh",
               "configure-monitoring.sh"
           ],
           "environment": {
               "ANALYSIS_MODE": "dynamic",
               "LOG_LEVEL": "DEBUG"
           }
       }
   }

Resource Management
-------------------

Resource Allocation
~~~~~~~~~~~~~~~~~~~

VMs are allocated resources based on:

- Template requirements
- User specifications
- System availability
- Analysis complexity

**Default Allocations:**

.. list-table::
   :widths: 25 25 25 25
   :header-rows: 1

   * - VM Type
     - Memory
     - CPU
     - Disk
   * - Docker (Light)
     - 512MB
     - 1 CPU
     - 10GB
   * - Docker (Standard)
     - 1GB
     - 2 CPU
     - 20GB
   * - Vagrant (Windows)
     - 4GB
     - 4 CPU
     - 60GB
   * - Vagrant (Linux)
     - 2GB
     - 2 CPU
     - 40GB

Resource Monitoring
~~~~~~~~~~~~~~~~~~~

Real-time monitoring includes:

**CPU Metrics:**
- CPU utilisation percentage
- CPU time per process
- Load average
- Context switches

**Memory Metrics:**
- Memory utilisation
- Swap usage
- Memory per process
- Cache and buffer usage

**Disk Metrics:**
- Disk I/O operations
- Read/write throughput
- Disk space usage
- File system statistics

**Network Metrics:**
- Bytes sent/received
- Packet counts
- Connection statistics
- Network interface status

Networking
----------

Network Isolation
~~~~~~~~~~~~~~~~~

VMs are isolated using different network configurations:

**Docker Networks:**

.. code-block:: json

   {
       "network_mode": "isolated",
       "networks": {
           "analysis_network": {
               "driver": "bridge",
               "internal": true,
               "ipam": {
                   "subnet": "**********/16"
               }
           }
       }
   }

**Vagrant Networks:**

.. code-block:: ruby

   Vagrant.configure("2") do |config|
     config.vm.network "private_network", 
                       ip: "*************",
                       virtualbox__intnet: "analysis_network"
   end

Internet Access Control
~~~~~~~~~~~~~~~~~~~~~~~

Internet access can be controlled per VM:

- **Isolated** - No external network access
- **Monitored** - External access with traffic logging
- **Proxy** - External access through analysis proxy
- **Full** - Unrestricted external access (not recommended)

Security Features
-----------------

Isolation Mechanisms
~~~~~~~~~~~~~~~~~~~~

**Process Isolation:**
- Separate process namespaces
- Resource limits (cgroups)
- Capability restrictions
- Seccomp filtering

**File System Isolation:**
- Read-only base images
- Temporary file systems
- Volume mount restrictions
- File system quotas

**Network Isolation:**
- Separate network namespaces
- Firewall rules
- Traffic monitoring
- DNS filtering

Monitoring and Logging
~~~~~~~~~~~~~~~~~~~~~~

**Security Monitoring:**
- Process execution tracking
- File system access monitoring
- Network connection logging
- System call tracing

**Log Collection:**
- System logs (syslog, journald)
- Application logs
- Security events
- Performance metrics

**Integration with ELK Stack:**
- Real-time log ingestion
- Structured logging (ECS format)
- Alerting and dashboards
- Long-term log retention

VM Operations
-------------

Snapshot Management
~~~~~~~~~~~~~~~~~~~

For Vagrant VMs, snapshots provide point-in-time recovery:

.. code-block:: bash

   # Create snapshot
   POST /api/v1/vms/vm-123/snapshots
   {
       "name": "pre-analysis",
       "description": "Clean state before malware execution"
   }
   
   # Restore snapshot
   POST /api/v1/vms/vm-123/snapshots/pre-analysis/restore

File Operations
~~~~~~~~~~~~~~~

**File Upload:**

.. code-block:: python

   import asyncio
   import websockets
   import json
   import base64

   async def upload_file(vm_id, file_path, content):
       uri = f"ws://localhost:8000/api/v1/vms/{vm_id}/files/upload"
       
       async with websockets.connect(uri) as websocket:
           # Send file metadata
           await websocket.send(json.dumps({
               "type": "file_start",
               "filename": file_path,
               "size": len(content)
           }))
           
           # Send file content in chunks
           chunk_size = 8192
           for i in range(0, len(content), chunk_size):
               chunk = content[i:i+chunk_size]
               await websocket.send(json.dumps({
                   "type": "file_chunk",
                   "data": base64.b64encode(chunk).decode()
               }))
           
           # Signal completion
           await websocket.send(json.dumps({
               "type": "file_complete"
           }))

Command Execution
~~~~~~~~~~~~~~~~~

**Interactive Commands:**

.. code-block:: javascript

   const ws = new WebSocket('ws://localhost:8000/api/v1/vms/vm-123/commands/execute');
   
   ws.onopen = function() {
       // Execute command
       ws.send(JSON.stringify({
           command: 'python3 /analysis/malware_scanner.py',
           working_directory: '/analysis',
           environment: {
               'PYTHONPATH': '/analysis/lib'
           }
       }));
   };
   
   ws.onmessage = function(event) {
       const data = JSON.parse(event.data);
       
       if (data.type === 'command_output') {
           console.log('Output:', data.output);
       } else if (data.type === 'command_complete') {
           console.log('Exit code:', data.exit_code);
       }
   };

Performance Optimisation
------------------------

Resource Optimisation
~~~~~~~~~~~~~~~~~~~~~

**Memory Management:**
- Automatic memory ballooning
- Swap optimisation
- Memory deduplication
- Cache management

**CPU Optimisation:**
- CPU affinity settings
- Process prioritisation
- Load balancing
- Thermal management

**Storage Optimisation:**
- SSD caching
- Compression
- Deduplication
- I/O scheduling

Scaling Strategies
~~~~~~~~~~~~~~~~~~

**Horizontal Scaling:**
- Multiple VM instances
- Load distribution
- Auto-scaling based on demand
- Resource pooling

**Vertical Scaling:**
- Dynamic resource allocation
- Memory hot-add
- CPU hot-plug
- Storage expansion

Best Practices
--------------

VM Configuration
~~~~~~~~~~~~~~~~

1. **Right-size Resources** - Allocate appropriate CPU and memory
2. **Use Appropriate Templates** - Choose templates matching analysis needs
3. **Configure Networking** - Set up proper network isolation
4. **Enable Monitoring** - Ensure comprehensive logging and monitoring
5. **Plan for Cleanup** - Implement proper VM lifecycle management

Security Considerations
~~~~~~~~~~~~~~~~~~~~~~~

1. **Principle of Least Privilege** - Minimal required permissions
2. **Network Segmentation** - Isolate analysis networks
3. **Regular Updates** - Keep templates and tools updated
4. **Audit Logging** - Comprehensive audit trails
5. **Incident Response** - Procedures for security incidents

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**VM Creation Failures:**
- Insufficient system resources
- Image download failures
- Network configuration errors
- Permission issues

**Performance Issues:**
- Resource contention
- I/O bottlenecks
- Network latency
- Memory pressure

**Connectivity Issues:**
- WebSocket connection failures
- Network isolation problems
- Firewall blocking
- DNS resolution issues

Diagnostic Tools
~~~~~~~~~~~~~~~~

**System Diagnostics:**

.. code-block:: bash

   # Check system resources
   GET /api/v1/status
   
   # VM-specific diagnostics
   GET /api/v1/vms/vm-123/diagnostics
   
   # Network connectivity test
   POST /api/v1/vms/vm-123/network/test

**Log Analysis:**

.. code-block:: bash

   # View VM logs
   GET /api/v1/vms/vm-123/logs?lines=100
   
   # Search logs in Kibana
   # Navigate to http://kibana.turdparty.localhost
