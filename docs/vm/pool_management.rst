VM Pool Management
==================

The 💩🎉 TurdParty platform includes an advanced VM pool management system that maintains pre-provisioned virtual machines for immediate allocation, ensuring rapid response times for malware analysis workflows.

Overview
--------

The VM pool management system provides:

- **Immediate VM Allocation**: Pre-provisioned VMs ready for instant use
- **Automatic Pool Maintenance**: Background tasks maintain optimal pool sizes
- **Multi-Template Support**: Support for Ubuntu, Alpine, and Windows 10 VMs
- **Elasticsearch Integration**: Comprehensive logging and monitoring
- **Database Integration**: Persistent VM state tracking with PostgreSQL

Architecture
------------

.. mermaid::

   graph TB
       subgraph "API Layer"
           API[VM Allocation API]
           WS[WebSocket Endpoints]
       end

       subgraph "Pool Management"
           PM[Pool Manager]
           MT[Maintenance Tasks]
           AS[Allocation Service]
       end

       subgraph "VM Templates"
           U20[Ubuntu 20.04]
           U22[Ubuntu 22.04]
           ALP[Alpine Latest]
           W10[Windows 10]
       end

       subgraph "Infrastructure"
           DB[(PostgreSQL)]
           REDIS[(Redis Queue)]
           ES[(Elasticsearch)]
       end

       API --> AS
       AS --> PM
       PM --> MT
       MT --> U20
       MT --> U22
       MT --> ALP
       MT --> W10

       PM --> DB
       MT --> REDIS
       AS --> ES

Supported VM Templates
----------------------

The system supports four VM templates with different configurations:

.. list-table:: **VM Template Configurations**
   :widths: 25 15 15 15 15 15
   :header-rows: 1

   * - Template
     - Min Ready
     - Max Total
     - Memory (MB)
     - CPUs
     - VM Type
   * - ``ubuntu:20.04``
     - 1
     - 3
     - 1024
     - 1
     - Docker
   * - ``ubuntu:22.04``
     - 1
     - 3
     - 1024
     - 1
     - Docker
   * - ``alpine:latest``
     - 1
     - 3
     - 512
     - 1
     - Docker
   * - ``10Baht/windows10-turdparty``
     - 2
     - 5
     - 4096
     - 2
     - Vagrant

VM Lifecycle States
-------------------

VMs progress through the following states:

.. list-table:: **VM States**
   :widths: 20 80
   :header-rows: 1

   * - State
     - Description
   * - ``CREATING``
     - VM is being provisioned and configured
   * - ``READY``
     - VM is available in the pool for allocation
   * - ``ALLOCATED``
     - VM has been assigned but not yet running
   * - ``RUNNING``
     - VM is actively processing workloads
   * - ``TERMINATING``
     - VM is being shut down and cleaned up
   * - ``TERMINATED``
     - VM has been destroyed and resources freed
   * - ``FAILED``
     - VM creation or operation failed

API Endpoints
-------------

**Immediate VM Allocation**

.. code-block:: http

   POST /api/v1/vm-allocation/allocate
   Content-Type: application/json

   {
     "template": "ubuntu:20.04",
     "requester_id": "api_user"
   }

**Response:**

.. code-block:: json

   {
     "success": true,
     "vm_id": "19ae1a52-06d3-4f66-baf1-0ffdcfd15490",
     "vm": {
       "vm_id": "19ae1a52-06d3-4f66-baf1-0ffdcfd15490",
       "name": "pool_ubuntu_20.04_e18fed06",
       "template": "ubuntu:20.04",
       "status": "running",
       "memory_mb": 1024,
       "cpus": 1,
       "ip_address": "**********",
       "ssh_port": 22
     },
     "allocated_at": "2025-06-17T22:04:34.596901Z",
     "message": "VM allocated immediately from ubuntu:20.04 pool",
     "duration_ms": 45,
     "correlation_id": "550e8400-e29b-41d4-a716-446655440000"
   }

**Pool Status Check**

.. code-block:: http

   GET /api/v1/vm-allocation/pools/status

**Response:**

.. code-block:: json

   {
     "ubuntu:20.04": {
       "ready": 2,
       "total": 3,
       "creating": 1,
       "running": 0,
       "terminated": 0,
       "min_ready": 1,
       "max_total": 3,
       "needs_provisioning": false,
       "can_provision": true
     },
     "10Baht/windows10-turdparty": {
       "ready": 1,
       "total": 2,
       "creating": 1,
       "running": 0,
       "terminated": 0,
       "min_ready": 2,
       "max_total": 5,
       "needs_provisioning": true,
       "can_provision": true
     }
   }

**Manual Pool Maintenance**

.. code-block:: http

   POST /api/v1/vm-allocation/pools/maintain

Background Tasks
----------------

The system includes three Celery background tasks:

**1. Pool Maintenance (Scheduled)**

.. code-block:: python

   @shared_task
   def maintain_vm_pools():
       """Maintain VM pools by ensuring minimum ready VMs."""
       # Runs every 5 minutes via Celery Beat
       # Provisions new VMs when pools fall below minimum

**2. Immediate VM Allocation**

.. code-block:: python

   @shared_task
   def allocate_vm_immediately(template: str, requester_id: str):
       """Allocate a VM immediately from the available pool."""
       # Called by API for instant VM allocation
       # Triggers pool maintenance after allocation

**3. Pool Status Monitoring**

.. code-block:: python

   @shared_task
   def get_pool_status():
       """Get current pool status for all templates."""
       # Provides real-time pool metrics
       # Used by monitoring and health checks

Elasticsearch Integration
-------------------------

All VM operations are logged to Elasticsearch with comprehensive context:

**VM Allocation Events:**

.. code-block:: json

   {
     "@timestamp": "2025-06-17T22:04:34.596Z",
     "event_type": "vm_allocation",
     "vm_id": "19ae1a52-06d3-4f66-baf1-0ffdcfd15490",
     "template": "ubuntu:20.04",
     "allocation_method": "immediate_pool",
     "duration_ms": 45,
     "success": true,
     "correlation_id": "550e8400-e29b-41d4-a716-446655440000"
   }

**Pool Maintenance Events:**

.. code-block:: json

   {
     "@timestamp": "2025-06-17T22:05:00.000Z",
     "event_type": "pool_maintenance",
     "actions_taken": 2,
     "duration_ms": 1250,
     "correlation_id": "550e8400-e29b-41d4-a716-446655440001"
   }

WebSocket Integration
---------------------

Real-time VM monitoring is available via WebSocket connections:

**VM Metrics Streaming:**

.. code-block:: javascript

   const ws = new WebSocket('ws://api.turdparty.localhost/api/v1/vms/vm-id/metrics/stream');
   
   ws.onmessage = function(event) {
       const metrics = JSON.parse(event.data);
       console.log(`CPU: ${metrics.cpu_percent}%, Memory: ${metrics.memory_percent}%`);
   };

**Command Execution:**

.. code-block:: javascript

   const ws = new WebSocket('ws://api.turdparty.localhost/api/v1/vms/vm-id/commands/execute');
   
   ws.onopen = function() {
       ws.send(JSON.stringify({
           command: 'ps aux',
           working_directory: '/tmp'
       }));
   };

Configuration
-------------

Pool configurations are defined in the ``BasicVMAvailabilityManager`` class:

.. code-block:: python

   pool_configs = {
       "ubuntu:20.04": PoolConfig(
           template="ubuntu:20.04",
           min_ready=1,
           max_total=3,
           vm_type="docker",
           memory_mb=1024,
           cpus=1
       ),
       "10Baht/windows10-turdparty": PoolConfig(
           template="10Baht/windows10-turdparty",
           min_ready=2,
           max_total=5,
           vm_type="vagrant",
           memory_mb=4096,
           cpus=2
       )
   }

Database Schema
---------------

VM instances are stored in PostgreSQL with the following key fields:

.. code-block:: sql

   CREATE TABLE vm_instances (
       id UUID PRIMARY KEY,
       name VARCHAR(255) NOT NULL,
       template VARCHAR(255) NOT NULL,
       status VARCHAR(50) NOT NULL,
       memory_mb INTEGER,
       cpus INTEGER,
       ip_address INET,
       ssh_port INTEGER,
       created_at TIMESTAMP WITH TIME ZONE,
       allocated_at TIMESTAMP WITH TIME ZONE,
       terminated_at TIMESTAMP WITH TIME ZONE
   );

Performance Metrics
-------------------

The system tracks comprehensive performance metrics:

- **Allocation Time**: Time to allocate VM from pool (typically <100ms)
- **Provisioning Time**: Time to create new VM (varies by template)
- **Pool Maintenance Duration**: Time to complete scheduled maintenance
- **Success Rates**: Allocation and provisioning success percentages

Monitoring and Alerts
---------------------

Key metrics to monitor:

- Pool sizes falling below minimum thresholds
- High allocation failure rates
- Long provisioning times
- Database connectivity issues
- Elasticsearch logging failures

The system provides comprehensive logging and can integrate with external monitoring systems via the Elasticsearch data pipeline.
