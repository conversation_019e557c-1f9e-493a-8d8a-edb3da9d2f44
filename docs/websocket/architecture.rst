💩🎉TurdParty🎉💩 WebSocket Architecture & Infrastructure
=========================================================

Overview
--------

This document provides comprehensive documentation of TurdParty's WebSocket infrastructure, including sequence flows, component interactions, and infrastructure architecture.

Infrastructure Components
--------------------------

The WebSocket infrastructure consists of several interconnected components:

.. mermaid::

   graph TB
       subgraph "Client Layer"
           C1[Web Browser]
           C2[Python Client]
           C3[CLI Tools]
       end
       
       subgraph "Proxy Layer"
           T[Traefik Proxy]
       end
       
       subgraph "API Layer"
           API[FastAPI Application]
           WS[WebSocket Endpoints]
           CM[Connection Manager]
       end
       
       subgraph "Service Layer"
           VM[VM Service]
           FS[File Service]
           MS[Metrics Service]
       end
       
       subgraph "Infrastructure Layer"
           D[Docker Engine]
           V[Vagrant/VirtualBox]
           M[MinIO Storage]
       end
       
       subgraph "Observability Layer"
           ES[Elasticsearch]
           LS[Logstash]
           K[Kibana]
       end
       
       C1 --> T
       C2 --> T
       C3 --> T
       T --> API
       API --> WS
       WS --> CM
       CM --> VM
       CM --> FS
       CM --> MS
       VM --> D
       VM --> V
       FS --> M
       API --> LS
       LS --> ES
       ES --> K

Component Responsibilities
--------------------------

**Traefik Proxy**
   - SSL termination for WSS connections
   - Load balancing across API instances
   - Service discovery and routing
   - Authentication and authorization

**FastAPI Application**
   - WebSocket endpoint registration
   - Route validation and middleware
   - Request/response handling
   - ECS logging integration

**Connection Manager**
   - WebSocket connection pooling
   - Client session management
   - Message routing and broadcasting
   - Graceful connection cleanup

**VM Service**
   - Virtual machine lifecycle management
   - Metrics collection and aggregation
   - Command execution coordination
   - Resource monitoring

**File Service**
   - Progressive file upload handling
   - Chunk validation and assembly
   - MinIO storage integration
   - Transfer progress tracking

**Metrics Service**
   - Real-time performance monitoring
   - Data aggregation and filtering
   - Alert generation and notification
   - Historical data retention

WebSocket Endpoint Architecture
-------------------------------

.. mermaid::

   graph LR
       subgraph "WebSocket Endpoints"
           E1[/api/v1/vms/{vm_id}/metrics/stream]
           E2[/api/v1/vms/{vm_id}/commands/execute]
           E3[/api/v1/vms/{vm_id}/files/upload]
           E4[/test-ws]
           E5[/ws/test]
           E6[/minimal-ws]
           E7[/test-with-param/{test_id}]
           E8[/test-with-manager/{test_id}]
       end
       
       subgraph "Connection Manager"
           CM[ConnectionManager]
           CP[Connection Pool]
           MR[Message Router]
       end
       
       subgraph "VM Operations"
           VMM[VM Manager]
           CMD[Command Executor]
           MET[Metrics Collector]
           FUP[File Uploader]
       end
       
       E1 --> CM
       E2 --> CM
       E3 --> CM
       E4 --> CM
       E5 --> CM
       E6 --> CM
       E7 --> CM
       E8 --> CM
       
       CM --> CP
       CM --> MR
       
       MR --> VMM
       MR --> CMD
       MR --> MET
       MR --> FUP

Connection Lifecycle Sequence
-----------------------------

.. mermaid::

   sequenceDiagram
       participant C as Client
       participant T as Traefik
       participant A as API
       participant CM as Connection Manager
       participant VM as VM Service
       participant ES as Elasticsearch
       
       C->>T: WebSocket Upgrade Request
       T->>T: Validate Authentication
       T->>A: Forward WebSocket Request
       A->>A: Route to WebSocket Endpoint
       A->>CM: Register Connection
       CM->>CM: Add to Connection Pool
       CM->>C: Connection Established
       
       Note over C,ES: Connection Active Phase
       
       C->>CM: Send Message
       CM->>VM: Route Message
       VM->>VM: Process Request
       VM->>CM: Send Response
       CM->>C: Forward Response
       CM->>ES: Log WebSocket Event
       
       Note over C,ES: Connection Termination
       
       C->>CM: Close Connection
       CM->>CM: Remove from Pool
       CM->>VM: Cleanup Resources
       CM->>ES: Log Disconnection

Metrics Stream Sequence
-----------------------

.. mermaid::

   sequenceDiagram
       participant C as Client
       participant WS as WebSocket Endpoint
       participant CM as Connection Manager
       participant MS as Metrics Service
       participant VM as VM Instance
       participant ES as Elasticsearch
       
       C->>WS: Connect to /metrics/stream
       WS->>CM: Register Metrics Connection
       CM->>MS: Subscribe to VM Metrics
       MS->>VM: Start Metrics Collection
       
       loop Every 1 Second
           VM->>MS: Collect Metrics Data
           MS->>MS: Aggregate & Format
           MS->>CM: Send Metrics Message
           CM->>WS: Route to Client
           WS->>C: Stream Metrics Data
           CM->>ES: Log Metrics Event
       end
       
       C->>WS: Disconnect
       WS->>CM: Unregister Connection
       CM->>MS: Unsubscribe from Metrics
       MS->>VM: Stop Metrics Collection

Command Execution Sequence
---------------------------

.. mermaid::

   sequenceDiagram
       participant C as Client
       participant WS as WebSocket Endpoint
       participant CM as Connection Manager
       participant CE as Command Executor
       participant VM as VM Instance
       participant ES as Elasticsearch
       
       C->>WS: Connect to /commands/execute
       WS->>CM: Register Command Connection
       
       C->>WS: Send Command Message
       WS->>CM: Route Command
       CM->>CE: Execute Command
       CE->>VM: Run Command in VM
       
       VM->>CE: STDOUT Data
       CE->>CM: Stream STDOUT
       CM->>WS: Forward STDOUT
       WS->>C: Send STDOUT Message
       
       VM->>CE: STDERR Data
       CE->>CM: Stream STDERR
       CM->>WS: Forward STDERR
       WS->>C: Send STDERR Message
       
       VM->>CE: Command Exit
       CE->>CM: Send Exit Code
       CM->>WS: Forward Exit
       WS->>C: Send Exit Message
       
       CM->>ES: Log Command Execution

File Upload Sequence
--------------------

.. mermaid::

   sequenceDiagram
       participant C as Client
       participant WS as WebSocket Endpoint
       participant CM as Connection Manager
       participant FU as File Uploader
       participant VM as VM Instance
       participant M as MinIO
       participant ES as Elasticsearch
       
       C->>WS: Connect to /files/upload
       WS->>CM: Register Upload Connection
       CM->>FU: Initialize Upload Session
       FU->>C: Send Upload Ready
       
       loop For Each Chunk
           C->>WS: Send File Chunk
           WS->>CM: Route Chunk
           CM->>FU: Process Chunk
           FU->>FU: Validate Chunk
           FU->>M: Store Chunk
           FU->>CM: Send Progress
           CM->>WS: Forward Progress
           WS->>C: Send Progress Message
       end
       
       FU->>FU: Assemble Complete File
       FU->>VM: Transfer to VM
       FU->>CM: Send Complete
       CM->>WS: Forward Complete
       WS->>C: Send Complete Message
       
       CM->>ES: Log File Upload

Error Handling Sequence
-----------------------

.. mermaid::

   sequenceDiagram
       participant C as Client
       participant WS as WebSocket Endpoint
       participant CM as Connection Manager
       participant S as Service Layer
       participant ES as Elasticsearch
       
       C->>WS: Send Invalid Message
       WS->>WS: Validate Message
       WS->>CM: Invalid Message Error
       CM->>CM: Generate Error Response
       CM->>WS: Send Error Message
       WS->>C: Forward Error
       CM->>ES: Log Error Event
       
       Note over S: Service Error Scenario
       
       C->>WS: Send Valid Message
       WS->>CM: Route Message
       CM->>S: Forward to Service
       S->>S: Process Fails
       S->>CM: Return Error
       CM->>WS: Send Error Message
       WS->>C: Forward Error
       CM->>ES: Log Service Error

Connection Management
---------------------

**Connection Pool Management**

.. code-block:: python

   class ConnectionManager:
       def __init__(self):
           self.active_connections: Dict[str, WebSocket] = {}
           self.vm_connections: Dict[str, Set[str]] = {}
           self.connection_metadata: Dict[str, Dict] = {}
       
       async def connect(self, websocket: WebSocket, vm_id: str):
           await websocket.accept()
           connection_id = str(uuid.uuid4())
           self.active_connections[connection_id] = websocket
           
           if vm_id not in self.vm_connections:
               self.vm_connections[vm_id] = set()
           self.vm_connections[vm_id].add(connection_id)
           
           self.connection_metadata[connection_id] = {
               "vm_id": vm_id,
               "connected_at": datetime.utcnow(),
               "last_activity": datetime.utcnow()
           }
           
           return connection_id
       
       def disconnect(self, websocket: WebSocket, connection_id: str):
           if connection_id in self.active_connections:
               del self.active_connections[connection_id]
               
           # Clean up VM connections
           for vm_id, connections in self.vm_connections.items():
               connections.discard(connection_id)
               
           if connection_id in self.connection_metadata:
               del self.connection_metadata[connection_id]

**Message Broadcasting**

.. code-block:: python

   async def broadcast_to_vm(self, vm_id: str, message: dict):
       """Broadcast message to all connections for a specific VM."""
       if vm_id in self.vm_connections:
           connections = self.vm_connections[vm_id].copy()
           for connection_id in connections:
               if connection_id in self.active_connections:
                   websocket = self.active_connections[connection_id]
                   try:
                       await websocket.send_text(json.dumps(message))
                       self.connection_metadata[connection_id]["last_activity"] = datetime.utcnow()
                   except ConnectionClosedError:
                       await self.cleanup_connection(connection_id)

Performance Characteristics
---------------------------

**Throughput Metrics**

.. list-table::
   :header-rows: 1
   :widths: 30 20 25 25

   * - Endpoint
     - Max Connections
     - Messages/Second
     - Latency (p95)
   * - Metrics Stream
     - 100 per VM
     - 1,000
     - < 10ms
   * - Command Execute
     - 10 per VM
     - 100
     - < 50ms
   * - File Upload
     - 5 per VM
     - 50
     - < 100ms

**Resource Usage**

- Memory: ~1MB per active WebSocket connection
- CPU: ~0.1% per 100 active connections
- Network: Varies by endpoint (1KB/s to 1MB/s per connection)

**Scaling Considerations**

- Horizontal scaling via multiple API instances
- Connection affinity through Traefik sticky sessions
- Redis-based connection state sharing (future enhancement)
- Database connection pooling for metadata storage
