💩🎉TurdParty🎉💩 WebSocket Sequence Flows
===========================================

Overview
--------

This document provides detailed sequence diagrams for all WebSocket interactions in TurdParty, showing the complete flow from client connection to resource cleanup.

Complete WebSocket Lifecycle
-----------------------------

.. mermaid::

   sequenceDiagram
       participant Client as Web Client
       participant Traefik as Traefik Proxy
       participant FastAPI as FastAPI App
       participant Router as Message Router
       participant ConnMgr as Connection Manager
       participant Service as Service Layer
       participant VM as Virtual Machine
       participant ELK as ELK Stack
       
       Note over Client,ELK: Connection Establishment Phase
       
       Client->>Traefik: WebSocket Upgrade Request
       Note right of Client: ws://api.turdparty.localhost/api/v1/vms/{vm_id}/metrics/stream
       
       Traefik->>Traefik: Validate SSL/Authentication
       Traefik->>FastAPI: Forward WebSocket Request
       
       FastAPI->>FastAPI: Route to WebSocket Endpoint
       FastAPI->>Router: Initialize Message Router
       Router->>ConnMgr: Register New Connection
       
       ConnMgr->>ConnMgr: Add to Connection Pool
       ConnMgr->>Service: Initialize Service Handler
       Service->>VM: Validate VM Access
       
       VM-->>Service: VM Status Confirmed
       Service-->>ConnMgr: Service Ready
       ConnMgr-->>Router: Connection Registered
       Router-->>FastAPI: WebSocket Ready
       FastAPI-->>Client: 101 Switching Protocols
       
       ConnMgr->>ELK: Log Connection Established
       
       Note over Client,ELK: Active Communication Phase
       
       loop Message Exchange
           Client->>FastAPI: Send WebSocket Message
           FastAPI->>Router: Route Message by Type
           Router->>Service: Process Message
           Service->>VM: Execute Operation
           
           VM-->>Service: Operation Result
           Service-->>Router: Format Response
           Router-->>FastAPI: Send Response
           FastAPI-->>Client: WebSocket Message
           
           Router->>ELK: Log Message Exchange
       end
       
       Note over Client,ELK: Connection Termination Phase
       
       Client->>FastAPI: Close WebSocket
       FastAPI->>Router: Handle Disconnection
       Router->>ConnMgr: Unregister Connection
       ConnMgr->>Service: Cleanup Service Resources
       Service->>VM: Release VM Resources
       
       ConnMgr->>ELK: Log Connection Closed
       
       Note over Client,ELK: Cleanup Complete

VM Metrics Stream Flow
----------------------

.. mermaid::

   sequenceDiagram
       participant Client as Monitoring Client
       participant WS as WebSocket Endpoint
       participant ConnMgr as Connection Manager
       participant MetricsSvc as Metrics Service
       participant VMMonitor as VM Monitor
       participant Docker as Docker Engine
       participant ELK as ELK Stack
       
       Client->>WS: Connect to /api/v1/vms/{vm_id}/metrics/stream?vm_type=docker
       WS->>ConnMgr: Register Metrics Connection
       ConnMgr->>MetricsSvc: Subscribe to VM Metrics
       MetricsSvc->>VMMonitor: Start Monitoring VM
       VMMonitor->>Docker: Begin Resource Collection
       
       WS-->>Client: Connection Established Message
       Note right of Client: {"type": "connection_established", "vm_id": "...", "connection_id": "..."}
       
       loop Every 1 Second
           Docker-->>VMMonitor: Container Stats
           Note right of Docker: CPU, Memory, Network, Disk usage
           
           VMMonitor->>VMMonitor: Aggregate & Calculate Percentages
           VMMonitor->>MetricsSvc: Send Metrics Data
           MetricsSvc->>ConnMgr: Route Metrics Message
           ConnMgr->>WS: Forward to Client
           WS-->>Client: Metrics Message
           Note right of Client: {"type": "metrics", "cpu_percent": 15.2, "memory_percent": 25.6, ...}
           
           MetricsSvc->>ELK: Log Metrics Event
           Note right of ELK: ECS format with correlation ID
       end
       
       alt VM Becomes Unavailable
           Docker-->>VMMonitor: Container Not Found
           VMMonitor->>MetricsSvc: VM Unavailable Error
           MetricsSvc->>ConnMgr: Send Error Message
           ConnMgr->>WS: Forward Error
           WS-->>Client: Error Message
           Note right of Client: {"type": "error", "error": "VM not found", "error_code": "VM_NOT_FOUND"}
       end
       
       Client->>WS: Disconnect
       WS->>ConnMgr: Unregister Connection
       ConnMgr->>MetricsSvc: Unsubscribe from Metrics
       MetricsSvc->>VMMonitor: Stop Monitoring
       VMMonitor->>Docker: Release Resources

Interactive Command Execution Flow
-----------------------------------

.. mermaid::

   sequenceDiagram
       participant Client as Terminal Client
       participant WS as WebSocket Endpoint
       participant ConnMgr as Connection Manager
       participant CmdSvc as Command Service
       participant Executor as Command Executor
       participant VM as Virtual Machine
       participant ELK as ELK Stack
       
       Client->>WS: Connect to /api/v1/vms/{vm_id}/commands/execute
       WS->>ConnMgr: Register Command Connection
       ConnMgr->>CmdSvc: Initialize Command Session
       CmdSvc->>Executor: Create Execution Context
       Executor->>VM: Validate Shell Access
       
       WS-->>Client: Connection Ready
       
       Client->>WS: Send Command Message
       Note right of Client: {"type": "command", "command": "ls -la /tmp", "timeout": 30}
       
       WS->>ConnMgr: Route Command
       ConnMgr->>CmdSvc: Process Command
       CmdSvc->>Executor: Execute in VM
       Executor->>VM: Run Command
       
       CmdSvc->>ELK: Log Command Execution Start
       
       loop Command Output Streaming
           VM-->>Executor: STDOUT Data
           Executor->>CmdSvc: Stream STDOUT
           CmdSvc->>ConnMgr: Send STDOUT Message
           ConnMgr->>WS: Forward STDOUT
           WS-->>Client: STDOUT Message
           Note right of Client: {"type": "stdout", "output": "total 8\ndrwxrwxrwt 2 root root 4096..."}
           
           alt STDERR Available
               VM-->>Executor: STDERR Data
               Executor->>CmdSvc: Stream STDERR
               CmdSvc->>ConnMgr: Send STDERR Message
               ConnMgr->>WS: Forward STDERR
               WS-->>Client: STDERR Message
               Note right of Client: {"type": "stderr", "output": "ls: cannot access..."}
           end
       end
       
       VM-->>Executor: Command Exit
       Executor->>CmdSvc: Command Complete
       CmdSvc->>ConnMgr: Send Exit Message
       ConnMgr->>WS: Forward Exit
       WS-->>Client: Exit Message
       Note right of Client: {"type": "exit", "exit_code": 0, "duration_ms": 150}
       
       CmdSvc->>ELK: Log Command Execution Complete
       
       alt Command Timeout
           Executor->>Executor: Timeout Reached
           Executor->>CmdSvc: Kill Command Process
           CmdSvc->>ConnMgr: Send Timeout Error
           ConnMgr->>WS: Forward Error
           WS-->>Client: Timeout Error
           Note right of Client: {"type": "error", "error": "Command timeout", "error_code": "TIMEOUT"}
       end

Progressive File Upload Flow
----------------------------

.. mermaid::

   sequenceDiagram
       participant Client as Upload Client
       participant WS as WebSocket Endpoint
       participant ConnMgr as Connection Manager
       participant FileSvc as File Service
       participant Uploader as File Uploader
       participant VM as Virtual Machine
       participant MinIO as MinIO Storage
       participant ELK as ELK Stack
       
       Client->>WS: Connect to /api/v1/vms/{vm_id}/files/upload
       WS->>ConnMgr: Register Upload Connection
       ConnMgr->>FileSvc: Initialize Upload Session
       FileSvc->>Uploader: Create Upload Context
       Uploader->>MinIO: Prepare Storage
       
       Uploader-->>FileSvc: Upload Ready
       FileSvc-->>ConnMgr: Send Ready Message
       ConnMgr-->>WS: Forward Ready
       WS-->>Client: Upload Ready Message
       Note right of Client: {"type": "upload_ready", "max_chunk_size": 65536, "supported_formats": ["binary", "base64"]}
       
       FileSvc->>ELK: Log Upload Session Start
       
       loop For Each File Chunk
           Client->>WS: Send Chunk Message
           Note right of Client: {"type": "chunk", "filename": "malware.exe", "chunk_index": 0, "data": "base64-data", "checksum": "blake3-hash"}
           
           WS->>ConnMgr: Route Chunk
           ConnMgr->>FileSvc: Process Chunk
           FileSvc->>Uploader: Validate & Store Chunk
           
           Uploader->>Uploader: Verify Checksum
           Uploader->>MinIO: Store Chunk
           MinIO-->>Uploader: Chunk Stored
           
           Uploader->>FileSvc: Chunk Processed
           FileSvc->>ConnMgr: Send Progress Message
           ConnMgr->>WS: Forward Progress
           WS-->>Client: Progress Message
           Note right of Client: {"type": "progress", "bytes_received": 65536, "progress_percent": 10.0}
           
           FileSvc->>ELK: Log Chunk Progress
       end
       
       Uploader->>Uploader: Assemble Complete File
       Uploader->>Uploader: Validate Final Checksum
       Uploader->>VM: Transfer File to VM
       VM-->>Uploader: File Transfer Complete
       
       Uploader->>FileSvc: Upload Complete
       FileSvc->>ConnMgr: Send Complete Message
       ConnMgr->>WS: Forward Complete
       WS-->>Client: Complete Message
       Note right of Client: {"type": "complete", "filename": "malware.exe", "file_path": "/tmp/malware.exe", "blake3_hash": "final-hash"}
       
       FileSvc->>ELK: Log Upload Complete
       
       alt Upload Error
           Uploader->>Uploader: Checksum Mismatch
           Uploader->>FileSvc: Upload Error
           FileSvc->>ConnMgr: Send Error Message
           ConnMgr->>WS: Forward Error
           WS-->>Client: Error Message
           Note right of Client: {"type": "error", "error": "Checksum validation failed", "error_code": "CHECKSUM_MISMATCH"}
           
           FileSvc->>ELK: Log Upload Error
       end

Error Handling and Recovery Flow
--------------------------------

.. mermaid::

   sequenceDiagram
       participant Client as WebSocket Client
       participant WS as WebSocket Endpoint
       participant ConnMgr as Connection Manager
       participant Service as Service Layer
       participant VM as Virtual Machine
       participant ELK as ELK Stack
       
       Note over Client,ELK: Normal Operation
       
       Client->>WS: Send Message
       WS->>ConnMgr: Route Message
       ConnMgr->>Service: Process Message
       
       alt Service Error
           Service->>Service: Processing Fails
           Service-->>ConnMgr: Return Error
           ConnMgr->>WS: Send Error Message
           WS-->>Client: Error Response
           Note right of Client: {"type": "error", "error": "Service unavailable", "error_code": "SERVICE_ERROR"}
           
           ConnMgr->>ELK: Log Service Error
       end
       
       alt VM Unavailable
           Service->>VM: Attempt Operation
           VM-->>Service: VM Not Responding
           Service-->>ConnMgr: VM Error
           ConnMgr->>WS: Send VM Error
           WS-->>Client: VM Error Response
           Note right of Client: {"type": "error", "error": "VM not responding", "error_code": "VM_UNAVAILABLE"}
           
           ConnMgr->>ELK: Log VM Error
       end
       
       alt Connection Lost
           Client->>WS: Send Message
           Note over WS: Connection Broken
           WS->>ConnMgr: Connection Lost Event
           ConnMgr->>Service: Cleanup Resources
           Service->>VM: Release VM Resources
           
           ConnMgr->>ELK: Log Connection Lost
           
           Note over Client: Client Reconnection Logic
           Client->>WS: Reconnect Attempt
           WS->>ConnMgr: New Connection
           ConnMgr->>Service: Restore Session
           Service->>VM: Re-establish VM Connection
           
           WS-->>Client: Reconnection Successful
           ConnMgr->>ELK: Log Reconnection
       end
       
       alt Rate Limiting
           Client->>WS: Rapid Messages
           WS->>WS: Rate Limit Exceeded
           WS-->>Client: Rate Limit Error
           Note right of Client: {"type": "error", "error": "Rate limit exceeded", "error_code": "RATE_LIMIT"}
           
           WS->>ELK: Log Rate Limit Event
           
           Note over Client: Client Backoff
           Client->>Client: Wait Before Retry
           Client->>WS: Retry Message
           WS->>ConnMgr: Process Message
           ConnMgr-->>WS: Success
           WS-->>Client: Success Response
       end

Connection Management Flow
--------------------------

.. mermaid::

   sequenceDiagram
       participant Client1 as Client 1
       participant Client2 as Client 2
       participant ConnMgr as Connection Manager
       participant Pool as Connection Pool
       participant Cleanup as Cleanup Service
       participant ELK as ELK Stack
       
       Note over Client1,ELK: Multiple Client Connections
       
       Client1->>ConnMgr: Connect to VM
       ConnMgr->>Pool: Add Connection
       Pool->>Pool: Update VM Connection Count
       ConnMgr->>ELK: Log New Connection
       
       Client2->>ConnMgr: Connect to Same VM
       ConnMgr->>Pool: Add Connection
       Pool->>Pool: Update VM Connection Count
       ConnMgr->>ELK: Log New Connection
       
       Note over Client1,ELK: Heartbeat Monitoring
       
       loop Every 30 Seconds
           ConnMgr->>Client1: Send Heartbeat
           ConnMgr->>Client2: Send Heartbeat
           
           Client1-->>ConnMgr: Heartbeat Response
           Client2-->>ConnMgr: Heartbeat Response
           
           ConnMgr->>Pool: Update Last Activity
       end
       
       Note over Client1,ELK: Connection Cleanup
       
       Cleanup->>Pool: Check Stale Connections
       Pool->>Pool: Identify Inactive Connections
       
       alt Stale Connection Found
           Pool->>ConnMgr: Stale Connection Detected
           ConnMgr->>ConnMgr: Force Close Connection
           ConnMgr->>Pool: Remove from Pool
           ConnMgr->>ELK: Log Cleanup Event
       end
       
       Note over Client1,ELK: VM Termination Cleanup
       
       ConnMgr->>ConnMgr: VM Termination Event
       ConnMgr->>Pool: Get All VM Connections
       Pool-->>ConnMgr: Connection List
       
       loop For Each Connection
           ConnMgr->>Client1: Send VM Terminated Message
           ConnMgr->>Client2: Send VM Terminated Message
           ConnMgr->>ConnMgr: Close Connection
           ConnMgr->>Pool: Remove Connection
       end
       
       ConnMgr->>ELK: Log VM Cleanup Complete
