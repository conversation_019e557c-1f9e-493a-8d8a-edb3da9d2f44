{
  description = "TurdParty Development Environment";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
    flake-utils.url = "github:numtide/flake-utils";
  };

  outputs = { self, nixpkgs, flake-utils }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        pkgs = nixpkgs.legacyPackages.${system};
        
        # Python environment with basic packages
        pythonEnv = pkgs.python311.withPackages (ps: with ps; [
          # Core dependencies
          pip
          setuptools
          wheel

          # Testing dependencies
          pytest
          pytest-asyncio
          pytest-cov
          pytest-html
          pytest-xdist
          pytest-mock
          pytest-timeout

          # API testing
          requests

          # Code quality
          black

          # Utilities
          python-dotenv
          click

          # Development tools
          ipython
        ]);

        # Node.js environment
        nodeEnv = pkgs.buildEnv {
          name = "turdparty-node-env";
          paths = with pkgs; [
            nodejs_20
            yarn
            nodePackages.npm
            nodePackages.typescript
            nodePackages.eslint
            nodePackages.prettier
          ];
        };

        # Development scripts
        devScripts = pkgs.writeScriptBin "tp-dev" ''
          #!${pkgs.bash}/bin/bash
          case "$1" in
            "test")
              ./scripts/run_tests.sh "''${@:2}"
              ;;
            "test-unit")
              ./scripts/run_tests.sh --unit-only "''${@:2}"
              ;;
            "test-fast")
              ./scripts/run_tests.sh --unit-only --no-linting "''${@:2}"
              ;;
            "coverage")
              ./scripts/manage_coverage.sh full "''${@:2}"
              ;;
            "ci")
              ./scripts/ci_test_runner.sh "''${@:2}"
              ;;
            "up")
              docker-compose up -d "''${@:2}"
              ;;
            "down")
              docker-compose down "''${@:2}"
              ;;
            "logs")
              docker-compose logs -f "''${@:2}"
              ;;
            "shell")
              docker-compose exec api bash "''${@:2}"
              ;;
            "lint")
              ruff check . "''${@:2}"
              ;;
            "format")
              ruff format . "''${@:2}"
              ;;
            "type-check")
              mypy . "''${@:2}"
              ;;
            "security")
              bandit -r . "''${@:2}"
              ;;
            "deps-check")
              safety check "''${@:2}"
              ;;
            "clean")
              find . -name "*.pyc" -delete
              find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
              rm -rf .pytest_cache
              rm -rf test-results/*
              echo "Cleaned up development artifacts"
              ;;
            "setup")
              echo "Setting up TurdParty development environment..."
              mkdir -p logs data/uploads data/temp_files test-results coverage-archive
              echo "✅ Directories created"
              
              if [ ! -f ".env" ]; then
                cp .env.example .env 2>/dev/null || echo "No .env.example found"
              fi
              
              echo "✅ Development environment ready"
              echo "Run 'tp-dev up' to start services"
              ;;
            "help"|*)
              echo "TurdParty Development Helper"
              echo ""
              echo "Usage: tp-dev <command> [options]"
              echo ""
              echo "Commands:"
              echo "  test         Run all tests"
              echo "  test-unit    Run unit tests only"
              echo "  test-fast    Quick unit tests"
              echo "  coverage     Generate coverage reports"
              echo "  ci           Run CI pipeline"
              echo "  up           Start services"
              echo "  down         Stop services"
              echo "  logs         Show service logs"
              echo "  shell        Enter API container shell"
              echo "  lint         Run code linting"
              echo "  format       Format code"
              echo "  type-check   Run type checking"
              echo "  security     Run security scan"
              echo "  deps-check   Check dependency vulnerabilities"
              echo "  clean        Clean up development artifacts"
              echo "  setup        Set up development environment"
              echo "  help         Show this help"
              ;;
          esac
        '';

      in
      {
        devShells.default = pkgs.mkShell {
          name = "turdparty-dev-shell";
          
          buildInputs = with pkgs; [
            # Core development environment
            pythonEnv
            nodeEnv
            devScripts
            
            # Shell and terminal
            zsh
            starship
            
            # Version control
            git
            git-lfs
            gh
            
            # Container tools
            docker
            docker-compose
            
            # Database tools
            postgresql_15
            redis
            
            # Development utilities
            fzf
            mc
            tree
            watch
            tmux
            htop
            
            # File and text processing
            jq
            yq
            ripgrep
            fd
            bat
            eza
            
            # Network tools
            curl
            wget
            httpie
            
            # Archive tools
            unzip
            zip
            gzip
            tar
            
            # Security tools
            gnupg
            openssh
            
            # Build tools
            gnumake
            gcc
            
            # Documentation
            pandoc
            
            # Shell tools
            shellcheck
            shfmt
            
            # System tools
            strace
            lsof
            bc
            
            # Editors
            vim
            nano
          ];

          shellHook = ''
            # Welcome message
            echo "🚀 TurdParty Development Environment (Nix Flake)"
            echo "📁 Project: $(basename $(pwd))"
            
            # Check git status
            if git rev-parse --git-dir > /dev/null 2>&1; then
              echo "🌿 Git Branch: $(git branch --show-current)"
              echo "📝 Git Status: $(git status --porcelain | wc -l) changes"
            fi
            
            echo "🐍 Python: $(python --version)"
            echo "📦 Node.js: $(node --version)"
            echo ""
            echo "🔧 Quick commands:"
            echo "  tp-dev help    - Show all available commands"
            echo "  tp-dev setup   - Set up development environment"
            echo "  tp-dev test    - Run tests"
            echo "  tp-dev up      - Start services"
            echo ""
            
            # Set up environment
            export PYTHONPATH="$PWD:$PYTHONPATH"
            export DEVELOPMENT=true
            export DEBUG=true
            export TURDPARTY_ENV=development
            export COMPOSE_PROJECT_NAME=turdpartycollab
            export DOCKER_BUILDKIT=1
            
            # Create directories
            mkdir -p logs data/uploads data/temp_files test-results coverage-archive
            
            # Configure zsh if available
            if [ -n "$ZSH_VERSION" ]; then
              # Git integration
              autoload -Uz vcs_info
              precmd() { vcs_info }
              zstyle ':vcs_info:git:*' formats ' (%b)'
              zstyle ':vcs_info:*' enable git
              
              # Custom prompt
              setopt PROMPT_SUBST
              PROMPT='%F{cyan}[TurdParty-Nix]%f %F{green}%n@%m%f:%F{blue}%~%f%F{red}''${vcs_info_msg_0_}%f
%F{yellow}❯%f '
              
              # Completion
              autoload -U compinit
              compinit
              
              # History
              HISTSIZE=10000
              SAVEHIST=10000
              HISTFILE=~/.zsh_history
              setopt SHARE_HISTORY HIST_IGNORE_DUPS HIST_IGNORE_ALL_DUPS HIST_IGNORE_SPACE
              
              # Options
              setopt AUTO_CD CORRECT EXTENDED_GLOB
              
              # Aliases
              alias ll='exa -la --git'
              alias ls='exa'
              alias cat='bat'
              alias grep='rg'
              alias find='fd'
              alias top='htop'
              
              # Git aliases
              alias gs='git status'
              alias ga='git add'
              alias gc='git commit'
              alias gp='git push'
              alias gl='git log --oneline --graph --decorate'
              alias gb='git branch'
              alias gco='git checkout'
              alias gd='git diff'
            fi
            
            # Start zsh if not already running
            if [ -z "$ZSH_VERSION" ] && [ -x "${pkgs.zsh}/bin/zsh" ]; then
              exec ${pkgs.zsh}/bin/zsh
            fi
          '';

          # Environment variables
          NIX_SHELL_PRESERVE_PROMPT = "1";
          SHELL = "${pkgs.zsh}/bin/zsh";
          EDITOR = "vim";
          PAGER = "bat";
          PYTHONDONTWRITEBYTECODE = "1";
          PYTHONUNBUFFERED = "1";
          NODE_ENV = "development";
          GIT_EDITOR = "vim";
        };

        # Package outputs
        packages.default = devScripts;
        
        # App outputs for running scripts
        apps.default = {
          type = "app";
          program = "${devScripts}/bin/tp-dev";
        };
        
        # Formatter
        formatter = pkgs.nixpkgs-fmt;
      });
}
