const { chromium } = require('playwright');

(async () => {
    const browser = await chromium.launch();
    const page = await browser.newPage();
    
    // Capture all console messages
    const consoleLogs = [];
    page.on('console', msg => {
        consoleLogs.push(`${msg.type()}: ${msg.text()}`);
        console.log(`[BROWSER] ${msg.type()}: ${msg.text()}`);
    });
    
    // Capture JavaScript errors
    page.on('pageerror', error => {
        console.log(`[JS ERROR] ${error.message}`);
    });
    
    console.log('🔍 Navigating to status page...');
    await page.goto('http://status.turdparty.localhost');
    
    console.log('⏳ Waiting for page to load...');
    await page.waitForTimeout(3000);
    
    console.log('🔍 Checking for tab buttons...');
    const tabButtons = await page.locator('.tab-button').count();
    console.log(`Found ${tabButtons} tab buttons`);
    
    if (tabButtons > 0) {
        console.log('🔍 Trying to click Services tab...');
        await page.locator('.tab-button[data-tab="services"]').click();
        await page.waitForTimeout(1000);
        
        const serviceTabClass = await page.locator('.tab-button[data-tab="services"]').getAttribute('class');
        console.log(`Services tab class after click: ${serviceTabClass}`);
    }
    
    console.log('\n=== All Console Logs ===');
    consoleLogs.forEach(log => console.log(log));
    
    await browser.close();
})();
