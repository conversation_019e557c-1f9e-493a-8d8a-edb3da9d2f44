#!/bin/bash

# Install Playwright Browser Dependencies using Nix
# This script uses nix-shell to provide the required libraries for Playwright

set -e

echo "🎭 Installing Playwright Browser Dependencies with Nix"
echo "===================================================="

# Create a nix expression for Playwright dependencies
cat > /tmp/playwright-deps.nix << 'EOF'
{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Core system libraries
    glib
    nss
    nspr
    dbus
    atk
    at-spi2-atk
    at-spi2-core
    cups
    libdrm
    gtk3
    gdk-pixbuf
    cairo
    pango
    harfbuzz

    # X11 libraries
    xorg.libX11
    xorg.libXcomposite
    xorg.libXdamage
    xorg.libXext
    xorg.libXfixes
    xorg.libXrandr
    xorg.libxcb
    xorg.libXScrnSaver
    xorg.libXtst
    xorg.libXi
    xorg.libXrender
    xorg.libXcursor
    xorg.libXt
    xorg.libX11

    # Audio
    alsa-lib

    # Graphics and GPU
    mesa
    libGL
    libGLU
    libxkbcommon
    libglvnd

    # System libraries that were missing
    systemd
    udev
    eudev
    libgudev
    libevdev

    # ICU libraries for WebKit
    icu

    # Additional WebKit dependencies
    libxml2
    sqlite
    libxslt
    libepoxy
    lcms2
    libevent
    libopus
    harfbuzz
    libgcrypt
    libgpg-error
    libjpeg
    libpng
    zlib
    openjpeg
    libwebp
    enchant2
    libsecret
    libtasn1
    hyphen

    # Flite speech synthesis libraries
    flite

    # Additional system libraries
    libatomic_ops
    pcre
    libpsl
    nghttp2
    libffi

    # Wayland support
    wayland
    wayland-protocols

    # Fonts and text
    fontconfig
    freetype

    # Additional libraries
    expat
    zlib
    libuuid

    # Node.js and npm for Playwright
    nodejs

    # Browser packages
    chromium
    firefox
  ];
  
  # Set environment variables for library paths
  shellHook = ''
    export LD_LIBRARY_PATH="${pkgs.lib.makeLibraryPath [
      pkgs.glib
      pkgs.nss
      pkgs.nspr
      pkgs.dbus
      pkgs.atk
      pkgs.at-spi2-atk
      pkgs.at-spi2-core
      pkgs.cups
      pkgs.libdrm
      pkgs.gtk3
      pkgs.gdk-pixbuf
      pkgs.cairo
      pkgs.pango
      pkgs.harfbuzz
      pkgs.xorg.libX11
      pkgs.xorg.libXcomposite
      pkgs.xorg.libXdamage
      pkgs.xorg.libXext
      pkgs.xorg.libXfixes
      pkgs.xorg.libXrandr
      pkgs.xorg.libxcb
      pkgs.xorg.libXScrnSaver
      pkgs.xorg.libXtst
      pkgs.xorg.libXi
      pkgs.xorg.libXrender
      pkgs.xorg.libXcursor
      pkgs.xorg.libXt
      pkgs.alsa-lib
      pkgs.mesa
      pkgs.libGL
      pkgs.libGLU
      pkgs.libxkbcommon
      pkgs.libglvnd
      pkgs.systemd
      pkgs.udev
      pkgs.eudev
      pkgs.libgudev
      pkgs.libevdev
      pkgs.icu
      pkgs.libxml2
      pkgs.sqlite
      pkgs.libxslt
      pkgs.libepoxy
      pkgs.lcms2
      pkgs.libevent
      pkgs.libopus
      pkgs.libgcrypt
      pkgs.libgpg-error
      pkgs.libjpeg
      pkgs.libpng
      pkgs.zlib
      pkgs.openjpeg
      pkgs.libwebp
      pkgs.enchant2
      pkgs.libsecret
      pkgs.libtasn1
      pkgs.hyphen
      pkgs.flite
      pkgs.libatomic_ops
      pkgs.pcre
      pkgs.libpsl
      pkgs.nghttp2
      pkgs.libffi
      pkgs.wayland
      pkgs.fontconfig
      pkgs.freetype
      pkgs.expat
      pkgs.libuuid
    ]}:$LD_LIBRARY_PATH"
    
    echo "🎭 Playwright environment ready!"
    echo "📚 Available libraries: $LD_LIBRARY_PATH"
  '';
}
EOF

echo "📦 Created Nix expression for Playwright dependencies"

# Test if we can enter the nix shell
echo "🧪 Testing Nix shell environment..."
nix-shell /tmp/playwright-deps.nix --run "echo '✅ Nix shell works!'"

echo ""
echo "✅ Playwright Nix environment configured successfully!"
echo ""
echo "🎯 To run Playwright tests with dependencies:"
echo "   nix-shell /tmp/playwright-deps.nix --run 'cd frontend && ./run-status-tests.sh'"
echo ""
echo "🔧 Or enter the shell manually:"
echo "   nix-shell /tmp/playwright-deps.nix"
echo "   cd frontend"
echo "   ./run-status-tests.sh"
