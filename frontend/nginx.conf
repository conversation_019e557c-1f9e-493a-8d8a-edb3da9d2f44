server {
    listen 80;
    server_name localhost;
    
    # Main application root
    root /usr/share/nginx/html;
    index index.html index.htm;
    
    # Enable gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Main application
    location / {
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Documentation served from mounted volume
    location /docs/ {
        alias /usr/share/nginx/html/docs/;
        try_files $uri $uri/ $uri/index.html =404;
        
        # Set proper MIME types
        location ~* \.html$ {
            add_header Content-Type "text/html; charset=utf-8";
        }
        
        location ~* \.css$ {
            add_header Content-Type "text/css; charset=utf-8";
        }
        
        location ~* \.js$ {
            add_header Content-Type "application/javascript; charset=utf-8";
        }
        
        location ~* \.json$ {
            add_header Content-Type "application/json; charset=utf-8";
        }
        
        # Cache documentation assets
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1h;
            add_header Cache-Control "public";
        }
    }
    
    # API documentation (Swagger UI)
    location /docs/api/ {
        alias /usr/share/nginx/html/docs/api/;
        try_files $uri $uri/ $uri/index.html =404;
        
        # Allow iframe for Swagger UI
        add_header X-Frame-Options "SAMEORIGIN";
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Proxy API requests to backend
    location /api/ {
        proxy_pass http://host.docker.internal:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_read_timeout 86400;
    }
    
    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root /usr/share/nginx/html;
        internal;
    }
    
    location = /50x.html {
        root /usr/share/nginx/html;
        internal;
    }
    
    # Deny access to hidden files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Logging
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
}
