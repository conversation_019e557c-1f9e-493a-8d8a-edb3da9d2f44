import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright configuration for TurdParty UI testing
 * Optimized for testing the actual running application
 */
export default defineConfig({
  testDir: './tests/playwright',
  
  // Test files to include
  testMatch: ['**/current-ui-workflow.spec.ts'],
  
  // Timeout settings
  timeout: 60000, // 1 minute per test
  expect: {
    timeout: 10000, // 10 seconds for assertions
  },
  
  // Test execution settings
  fullyParallel: false, // Run tests sequentially to avoid conflicts
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 1,
  workers: 1, // Single worker to avoid conflicts
  
  // Reporter configuration
  reporter: [
    ['list'], // Console output
    ['html', { 
      outputFolder: 'playwright-report',
      open: 'never' // Don't auto-open report
    }],
    ['json', { 
      outputFile: 'test-results/ui-test-results.json' 
    }]
  ],
  
  // Global test settings
  use: {
    // Base URL for the application
    baseURL: 'http://localhost:3000',
    
    // Browser settings
    headless: true, // Can be overridden with --headed
    
    // Viewport settings
    viewport: { width: 1280, height: 720 },
    
    // Interaction settings
    actionTimeout: 10000, // 10 seconds for actions
    navigationTimeout: 30000, // 30 seconds for navigation
    
    // Recording settings
    trace: 'on-first-retry', // Record trace on retry
    screenshot: 'only-on-failure', // Screenshot on failure
    video: 'retain-on-failure', // Video on failure
    
    // Ignore HTTPS errors (for local development)
    ignoreHTTPSErrors: true,
    
    // Additional browser args for stability
    launchOptions: {
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ],
    },
  },

  // Browser projects
  projects: [
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        // Override any device-specific settings
        viewport: { width: 1280, height: 720 },
      },
    },
    
    // Uncomment to test on other browsers
    // {
    //   name: 'firefox',
    //   use: { ...devices['Desktop Firefox'] },
    // },
    // {
    //   name: 'webkit',
    //   use: { ...devices['Desktop Safari'] },
    // },
  ],

  // Output directory for test artifacts
  outputDir: 'test-results/',
  
  // Global setup and teardown
  globalSetup: undefined, // No global setup needed for UI tests
  globalTeardown: undefined,
  
  // Web server configuration (not needed since we test against running app)
  webServer: undefined,
  
  // Test metadata
  metadata: {
    'test-type': 'ui-workflow',
    'application': 'TurdParty',
    'target-url': 'http://localhost:3000',
    'description': 'End-to-end UI workflow testing for TurdParty file upload and VM injection'
  },
});
