#!/bin/bash

# TurdParty UI Testing Script (Docker Version)
# This script runs Playwright tests using Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 TurdParty UI Testing Suite (Docker)${NC}"
echo "=========================================="

# Check if we're in the frontend directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Error: This script must be run from the frontend directory${NC}"
    exit 1
fi

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed or not available${NC}"
    exit 1
fi

# Check if services are running
echo -e "${YELLOW}🔍 Checking if TurdParty services are running...${NC}"

# Check if frontend is accessible
if ! curl -f http://localhost:3000/health >/dev/null 2>&1; then
    echo -e "${RED}❌ Frontend service is not accessible at http://localhost:3000${NC}"
    echo -e "${YELLOW}💡 Please start the services first:${NC}"
    echo "   cd ../compose && docker compose up -d"
    exit 1
fi

echo -e "${GREEN}✅ Services are running${NC}"

# Parse command line arguments
TEST_FILE=""
HEADED=""
DEBUG=""
REPORT=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --test)
            TEST_FILE="$2"
            shift 2
            ;;
        --headed)
            HEADED="--headed"
            shift
            ;;
        --debug)
            DEBUG="--debug"
            shift
            ;;
        --report)
            REPORT="--reporter=html"
            shift
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --test <file>    Run specific test file (e.g., current-ui-workflow.spec.ts)"
            echo "  --headed         Run tests in headed mode (visible browser)"
            echo "  --debug          Run tests in debug mode"
            echo "  --report         Generate HTML report"
            echo "  --help           Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                                    # Run all tests"
            echo "  $0 --test current-ui-workflow.spec.ts # Run specific test"
            echo "  $0 --headed --debug                   # Run with visible browser and debug"
            echo "  $0 --report                           # Generate HTML report"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ Unknown option: $1${NC}"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Create a temporary Docker container to run tests
echo -e "${YELLOW}🐳 Creating Docker container for testing...${NC}"

# Build a test image with Playwright
cat > Dockerfile.test << 'EOF'
FROM node:18-alpine

# Install dependencies for Playwright
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont

# Set Chromium path for Playwright
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install --legacy-peer-deps

# Install Playwright browsers
RUN npx playwright install --with-deps chromium || true

# Copy source code
COPY . .

# Set the entrypoint
ENTRYPOINT ["npx", "playwright", "test"]
EOF

# Build the test image
echo -e "${YELLOW}🔨 Building test image...${NC}"
docker build -f Dockerfile.test -t turdparty-ui-tests .

# Determine test command
if [ -n "$TEST_FILE" ]; then
    TEST_PATH="tests/playwright/$TEST_FILE"
    echo -e "${YELLOW}🎯 Running specific test: $TEST_FILE${NC}"
    TEST_ARGS="$TEST_PATH"
else
    echo -e "${YELLOW}🎯 Running all UI workflow tests...${NC}"
    TEST_ARGS="tests/playwright/current-ui-workflow.spec.ts"
fi

# Add options to command
if [ -n "$HEADED" ]; then
    TEST_ARGS="$TEST_ARGS $HEADED"
    echo -e "${BLUE}👁️  Running in headed mode (visible browser)${NC}"
fi

if [ -n "$DEBUG" ]; then
    TEST_ARGS="$TEST_ARGS $DEBUG"
    echo -e "${BLUE}🐛 Running in debug mode${NC}"
fi

if [ -n "$REPORT" ]; then
    TEST_ARGS="$TEST_ARGS $REPORT"
    echo -e "${BLUE}📊 Will generate HTML report${NC}"
fi

echo ""
echo -e "${BLUE}🚀 Starting tests in Docker container...${NC}"
echo "Test args: $TEST_ARGS"
echo ""

# Run the tests in Docker
if docker run --rm \
    --network host \
    -v "$(pwd)/test-results:/app/test-results" \
    -v "$(pwd)/playwright-report:/app/playwright-report" \
    -e PLAYWRIGHT_BROWSERS_PATH=/ms-playwright \
    turdparty-ui-tests $TEST_ARGS; then
    
    echo ""
    echo -e "${GREEN}✅ All tests passed successfully!${NC}"
    
    # Show report if generated
    if [ -n "$REPORT" ]; then
        echo -e "${BLUE}📊 HTML report generated at: playwright-report/index.html${NC}"
        echo -e "${YELLOW}💡 Open the report with: open playwright-report/index.html${NC}"
    fi
    
    echo ""
    echo -e "${GREEN}🎉 TurdParty UI testing completed successfully!${NC}"
    
else
    echo ""
    echo -e "${RED}❌ Some tests failed${NC}"
    echo -e "${YELLOW}💡 Check the test output above for details${NC}"
    
    if [ -n "$REPORT" ]; then
        echo -e "${BLUE}📊 HTML report with failure details: playwright-report/index.html${NC}"
        echo -e "${YELLOW}💡 Open the report with: open playwright-report/index.html${NC}"
    fi
    
    echo ""
    echo -e "${YELLOW}🔧 Troubleshooting tips:${NC}"
    echo "  • Ensure all services are running: docker compose up -d"
    echo "  • Check API logs: docker logs turdpartycollab_api"
    echo "  • Verify frontend is accessible: curl http://localhost:3000/health"
    echo "  • Run with --headed --debug for visual debugging"
    
    exit 1
fi

# Clean up
echo -e "${YELLOW}🧹 Cleaning up...${NC}"
rm -f Dockerfile.test
docker rmi turdparty-ui-tests >/dev/null 2>&1 || true

echo -e "${GREEN}✨ Testing complete!${NC}"
