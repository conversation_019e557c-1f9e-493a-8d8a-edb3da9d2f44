#!/bin/bash

# TurdParty UI Testing Script (Nix Version)
# This script runs Playwright tests using Nix for Node.js

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 TurdParty UI Testing Suite (Nix)${NC}"
echo "====================================="

# Check if we're in the frontend directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Error: This script must be run from the frontend directory${NC}"
    exit 1
fi

# Check if Nix is available
if ! command -v nix-shell &> /dev/null; then
    echo -e "${RED}❌ Nix is not installed or not available${NC}"
    echo -e "${YELLOW}💡 Please install Nix or use the Docker runner instead${NC}"
    exit 1
fi

# Check if services are running
echo -e "${YELLOW}🔍 Checking if TurdParty services are running...${NC}"

# Check if frontend is accessible
if ! curl -f http://localhost:3000/health >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Frontend service not accessible, but continuing with mocked tests${NC}"
    echo -e "${BLUE}💡 Tests will use mocked API responses and don't require running services${NC}"
else
    echo -e "${GREEN}✅ Frontend service is accessible${NC}"
fi

# Parse command line arguments
TEST_FILE=""
HEADED=""
DEBUG=""
REPORT=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --test)
            TEST_FILE="$2"
            shift 2
            ;;
        --headed)
            HEADED="--headed"
            shift
            ;;
        --debug)
            DEBUG="--debug"
            shift
            ;;
        --report)
            REPORT="--reporter=html"
            shift
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --test <file>    Run specific test file (e.g., current-ui-workflow.spec.ts)"
            echo "  --headed         Run tests in headed mode (visible browser)"
            echo "  --debug          Run tests in debug mode"
            echo "  --report         Generate HTML report"
            echo "  --help           Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                                    # Run all tests"
            echo "  $0 --test current-ui-workflow.spec.ts # Run specific test"
            echo "  $0 --headed --debug                   # Run with visible browser and debug"
            echo "  $0 --report                           # Generate HTML report"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ Unknown option: $1${NC}"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Determine test command
if [ -n "$TEST_FILE" ]; then
    TEST_PATH="tests/playwright/$TEST_FILE"
    if [ ! -f "$TEST_PATH" ]; then
        echo -e "${RED}❌ Test file not found: $TEST_PATH${NC}"
        exit 1
    fi
    echo -e "${YELLOW}🎯 Running specific test: $TEST_FILE${NC}"
    TEST_COMMAND="npx playwright test $TEST_PATH"
else
    echo -e "${YELLOW}🎯 Running all UI workflow tests...${NC}"
    TEST_COMMAND="npx playwright test tests/playwright/current-ui-workflow.spec.ts"
fi

# Add options to command
if [ -n "$HEADED" ]; then
    TEST_COMMAND="$TEST_COMMAND $HEADED"
    echo -e "${BLUE}👁️  Running in headed mode (visible browser)${NC}"
fi

if [ -n "$DEBUG" ]; then
    TEST_COMMAND="$TEST_COMMAND $DEBUG"
    echo -e "${BLUE}🐛 Running in debug mode${NC}"
fi

if [ -n "$REPORT" ]; then
    TEST_COMMAND="$TEST_COMMAND $REPORT"
    echo -e "${BLUE}📊 Will generate HTML report${NC}"
fi

echo ""
echo -e "${BLUE}🚀 Starting tests with Nix...${NC}"
echo "Command: $TEST_COMMAND"
echo ""

# Create a temporary shell script for Nix
cat > .test-runner.sh << EOF
#!/bin/bash
set -e

echo "🔧 Setting up Node.js environment..."

# Install dependencies if node_modules doesn't exist or is incomplete
if [ ! -d "node_modules/@playwright" ]; then
    echo "📦 Installing dependencies..."
    npm install --legacy-peer-deps
fi

# Install Playwright browsers if needed
if [ ! -d "node_modules/@playwright/test" ]; then
    echo "🎭 Installing Playwright..."
    npx playwright install --with-deps
fi

echo "🎬 Running tests..."
$TEST_COMMAND
EOF

chmod +x .test-runner.sh

# Run the tests in Nix shell with Node.js
if nix-shell -p nodejs_18 --run "./.test-runner.sh"; then
    echo ""
    echo -e "${GREEN}✅ All tests passed successfully!${NC}"
    
    # Show report if generated
    if [ -n "$REPORT" ]; then
        echo -e "${BLUE}📊 HTML report generated at: playwright-report/index.html${NC}"
        echo -e "${YELLOW}💡 Open the report with: open playwright-report/index.html${NC}"
    fi
    
    echo ""
    echo -e "${GREEN}🎉 TurdParty UI testing completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}📋 Test Summary:${NC}"
    echo "  ✅ Main page display"
    echo "  ✅ Complete upload to VM injection workflow"
    echo "  ✅ File upload error handling"
    echo "  ✅ File size validation"
    echo "  ✅ Workflow step navigation"
    echo "  ✅ Debug information display"
    echo "  ✅ Dark mode toggle"
    echo ""
    echo -e "${YELLOW}💡 Next steps:${NC}"
    echo "  • Check the uploaded files in MinIO"
    echo "  • Verify VM injection status"
    echo "  • Monitor ELK logs for analysis results"
    
else
    echo ""
    echo -e "${RED}❌ Some tests failed${NC}"
    echo -e "${YELLOW}💡 Check the test output above for details${NC}"
    
    if [ -n "$REPORT" ]; then
        echo -e "${BLUE}📊 HTML report with failure details: playwright-report/index.html${NC}"
        echo -e "${YELLOW}💡 Open the report with: open playwright-report/index.html${NC}"
    fi
    
    echo ""
    echo -e "${YELLOW}🔧 Troubleshooting tips:${NC}"
    echo "  • Ensure all services are running: docker compose up -d"
    echo "  • Check API logs: docker logs turdpartycollab_api"
    echo "  • Verify frontend is accessible: curl http://localhost:3000/health"
    echo "  • Run with --headed --debug for visual debugging"
    echo "  • Try the Docker runner: ./run-ui-tests-docker.sh"
    
    # Clean up
    rm -f .test-runner.sh
    exit 1
fi

# Clean up
rm -f .test-runner.sh

echo -e "${GREEN}✨ Testing complete!${NC}"
