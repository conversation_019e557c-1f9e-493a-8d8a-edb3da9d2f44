#!/usr/bin/env python3
"""
Screenshot Capture Script for TurdParty Application

This script uses <PERSON><PERSON> to capture screenshots of the main functionality
of the TurdParty application. It navigates through the main pages and captures
screenshots of each page, saving them to the docs/screenshots directory.
"""

import os
import sys
import asyncio
from pathlib import Path
from playwright.async_api import async_playwright

# Define the base URL
BASE_URL = "http://localhost:3000"  # Adjust if your app runs on a different port

# Define the screenshots directory
SCREENSHOTS_DIR = Path(__file__).parent.parent / "docs" / "screenshots"

async def setup_authentication(page):
    """Set up authentication for the tests."""
    await page.goto(f"{BASE_URL}/")
    
    # Check if we need to log in (look for login form or already logged in state)
    is_logged_in = await page.evaluate("""() => {
        // Check for presence of auth token in localStorage
        return !!localStorage.getItem('authToken');
    }""")

    if not is_logged_in:
        print("Not logged in, attempting to get test token...")
        
        try:
            # Get a test token from the API
            response = await page.request.get("http://localhost:8000/api/auth/test-token")
            data = await response.json()
            
            if data.get('access_token'):
                # Store the token in localStorage
                await page.evaluate("""(token) => {
                    localStorage.setItem('authToken', token);
                    // Also store user info if needed
                    localStorage.setItem('user', JSON.stringify({
                        id: '00000000-0000-0000-0000-000000000000',
                        username: 'testuser',
                        email: '<EMAIL>',
                        is_active: true,
                        is_superuser: true
                    }));
                }""", data['access_token'])
                
                print("Successfully obtained and stored test token")
            else:
                print(f"Failed to get test token: {data}")
        except Exception as e:
            print(f"Error getting test token: {e}")
        
        # Reload the page to apply the token
        await page.reload()
    else:
        print("Already logged in")
    
    # Give time for auth state to be processed
    await page.wait_for_timeout(1000)

async def capture_screenshots():
    """Capture screenshots of the main functionality."""
    async with async_playwright() as p:
        # Launch the browser
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context()
        page = await context.new_page()
        
        # Set up authentication
        await setup_authentication(page)
        
        # Ensure screenshots directory exists
        os.makedirs(SCREENSHOTS_DIR, exist_ok=True)
        
        # Home page
        print("Capturing Home page...")
        await page.goto(f"{BASE_URL}/")
        await page.wait_for_load_state("networkidle")
        await page.screenshot(path=str(SCREENSHOTS_DIR / "01-home-page.png"), full_page=True)
        
        # VM Status page
        print("Capturing VM Status page...")
        await page.goto(f"{BASE_URL}/vm-status")
        await page.wait_for_load_state("networkidle")
        await page.screenshot(path=str(SCREENSHOTS_DIR / "02-vm-status.png"), full_page=True)
        
        # Try to capture VM details if available
        vm_list_item = page.locator(".ant-list-item").first
        if await vm_list_item.is_visible():
            await vm_list_item.click()
            await page.wait_for_timeout(1000)
            await page.screenshot(path=str(SCREENSHOTS_DIR / "03-vm-details.png"), full_page=True)
            
            # Capture different tabs
            await page.locator('div[role="tab"]').filter(has_text="Injections").click()
            await page.wait_for_timeout(500)
            await page.screenshot(path=str(SCREENSHOTS_DIR / "04-vm-injections.png"), full_page=True)
            
            await page.locator('div[role="tab"]').filter(has_text="Logs").click()
            await page.wait_for_timeout(500)
            await page.screenshot(path=str(SCREENSHOTS_DIR / "05-vm-logs.png"), full_page=True)
        
        # Files page
        print("Capturing Files page...")
        await page.goto(f"{BASE_URL}/files")
        await page.wait_for_load_state("networkidle")
        await page.screenshot(path=str(SCREENSHOTS_DIR / "06-files.png"), full_page=True)
        
        # VMs page
        print("Capturing VMs page...")
        await page.goto(f"{BASE_URL}/vms")
        await page.wait_for_load_state("networkidle")
        await page.screenshot(path=str(SCREENSHOTS_DIR / "07-vms.png"), full_page=True)
        
        # Injections page
        print("Capturing Injections page...")
        await page.goto(f"{BASE_URL}/injections")
        await page.wait_for_load_state("networkidle")
        await page.screenshot(path=str(SCREENSHOTS_DIR / "08-injections.png"), full_page=True)
        
        # Documentation page
        print("Capturing Documentation page...")
        await page.goto(f"{BASE_URL}/docs")
        await page.wait_for_load_state("networkidle")
        await page.screenshot(path=str(SCREENSHOTS_DIR / "09-docs.png"), full_page=True)
        
        # Language switcher functionality
        print("Capturing Language switcher functionality...")
        await page.goto(f"{BASE_URL}/")
        await page.wait_for_load_state("networkidle")
        
        # Find and click the language switcher
        language_switcher = page.locator(".ant-select").filter(has_text="English")
        if await language_switcher.is_visible():
            await language_switcher.click()
            await page.wait_for_timeout(500)
            await page.screenshot(path=str(SCREENSHOTS_DIR / "10-language-switcher-open.png"))
            
            # Select German
            await page.locator(".ant-select-item-option").filter(has_text="Deutsch").click()
            await page.wait_for_timeout(1000)
            await page.screenshot(path=str(SCREENSHOTS_DIR / "11-german-language.png"), full_page=True)
            
            # Go to VM Status page to see translations
            await page.goto(f"{BASE_URL}/vm-status")
            await page.wait_for_load_state("networkidle")
            await page.screenshot(path=str(SCREENSHOTS_DIR / "12-vm-status-german.png"), full_page=True)
            
            # Switch back to English
            await language_switcher.click()
            await page.wait_for_timeout(500)
            await page.locator(".ant-select-item-option").filter(has_text="English").click()
            await page.wait_for_timeout(1000)
        
        # Close the browser
        await browser.close()
        
        print(f"Screenshots have been saved to {SCREENSHOTS_DIR}")
        print("Note: This directory is excluded from git via .gitignore")

if __name__ == "__main__":
    # Run the screenshot capture function
    asyncio.run(capture_screenshots()) 