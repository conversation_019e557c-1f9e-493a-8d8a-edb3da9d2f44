#!/bin/bash

# Exit on error
set -e

echo "Setting up Play<PERSON> for TurdParty UI testing..."

# Install Node.js dependencies
echo "Installing Node.js dependencies..."
npm install

# Install Playwright browsers
echo "Installing Playwright browsers..."
npx playwright install --with-deps

# Create directory for auth state
mkdir -p playwright/.auth

# Run the authentication setup
echo "Running authentication setup..."
npx playwright test tests/playwright/auth.setup.ts

# Run the tests
echo "Running Playwright tests..."
npx playwright test

echo "Tests completed. View the report with: npm run test:report" 