import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, Layout, theme } from 'antd';
import { AuthProvider } from './hooks/useAuth';
import { setupGlobalErrorLogging } from './utils/errorLogger';
import { setupPerformanceMonitoring } from './utils/performanceMonitor';
import ErrorBoundary from './components/ErrorBoundary';

// Import pages
import MainPage from './pages/MainPage';
import FileUploadPage from './pages/File_upload';
import FileSelectionPage from './pages/File_selection';
import VagrantVMPage from './pages/Vagrant_vm';
import VMInjectionPage from './pages/Vm_injection';
import VmStatusPage from './pages/Vm_status';
import DocsPage from './pages/DocsPage';
import StatusPage from './pages/StatusPage';
import PerformanceMonitor from './components/PerformanceMonitor';

// Import components
import LanguageSwitcher from './components/LanguageSwitcher';

// Import styles
import './App.css';

const { Header } = Layout;

const App: React.FC = () => {
  // Use state for dark mode
  const [isDarkMode, setIsDarkMode] = useState<boolean>(localStorage.getItem('darkMode') === 'true');
  
  // Setup global error logging and performance monitoring
  useEffect(() => {
    setupGlobalErrorLogging();
    // Temporarily disable performance monitoring to improve performance
    // setupPerformanceMonitoring();
  }, []);
  
  // Listen for changes to localStorage
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'darkMode') {
        setIsDarkMode(e.newValue === 'true');
      }
    };
    
    // For changes in the same window
    const handleLocalChange = () => {
      setIsDarkMode(localStorage.getItem('darkMode') === 'true');
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    // Create a custom event for same-window updates
    window.addEventListener('darkModeChange', handleLocalChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('darkModeChange', handleLocalChange);
    };
  }, []);
  
  return (
    <ErrorBoundary componentName="App">
      <ConfigProvider
        theme={{
          algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
        }}
      >
        <AuthProvider>
          <Router>
            <Layout>
              <Header style={{ display: 'flex', justifyContent: 'flex-end', padding: '0 16px' }}>
                <LanguageSwitcher />
              </Header>
              <Routes>
                <Route
                  path="/"
                  element={
                    <ErrorBoundary componentName="MainPage">
                      <MainPage />
                    </ErrorBoundary>
                  }
                />
                <Route
                  path="/file_upload"
                  element={
                    <ErrorBoundary componentName="FileUploadPage">
                      <FileUploadPage />
                    </ErrorBoundary>
                  }
                />
                <Route
                  path="/file_selection"
                  element={
                    <ErrorBoundary componentName="FileSelectionPage">
                      <FileSelectionPage />
                    </ErrorBoundary>
                  }
                />
                <Route
                  path="/vagrant_vm"
                  element={
                    <ErrorBoundary componentName="VagrantVMPage" showDetails>
                      <VagrantVMPage />
                    </ErrorBoundary>
                  }
                />
                <Route
                  path="/vm_injection"
                  element={
                    <ErrorBoundary componentName="VmInjectionPage">
                      <VMInjectionPage />
                    </ErrorBoundary>
                  }
                />
                <Route
                  path="/vm_status"
                  element={
                    <ErrorBoundary componentName="VmStatusPage" showDetails>
                      <VmStatusPage />
                    </ErrorBoundary>
                  }
                />
                <Route
                  path="/docs"
                  element={
                    <ErrorBoundary componentName="DocsPage">
                      <DocsPage />
                    </ErrorBoundary>
                  }
                />
                <Route path="/status" element={
                  <ErrorBoundary componentName="StatusPage">
                    <StatusPage />
                  </ErrorBoundary>
                } />
                <Route path="/performance" element={
                  <ErrorBoundary componentName="PerformanceMonitor">
                    <PerformanceMonitor />
                  </ErrorBoundary>
                } />
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </Layout>
          </Router>
        </AuthProvider>
      </ConfigProvider>
    </ErrorBoundary>
  );
};

export default App; 