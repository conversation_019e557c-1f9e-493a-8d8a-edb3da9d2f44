import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Typography, Space, Divider } from 'antd';
import { BugOutlined, ReloadOutlined, HomeOutlined } from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  componentName?: string;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorCount: number;
}

/**
 * Enhanced error boundary component with detailed logging and recovery options
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render shows the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Capture and log detailed component stack information
    this.setState(prevState => ({
      errorInfo,
      errorCount: prevState.errorCount + 1
    }));

    // Log error details to console in a structured format
    const details = {
      component: this.props.componentName || 'Unknown',
      message: error.message,
      name: error.name,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      count: this.state.errorCount + 1
    };

    console.group(`[ErrorBoundary] Error in ${this.props.componentName || 'component'}`);
    console.error('Error details:', details);
    console.groupEnd();

    // Call the optional onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Optional: Send error to server logging endpoint
    /*
    try {
      fetch('/api/logs/ui-error', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'UI_ERROR',
          source: this.props.componentName,
          error: {
            message: error.message,
            name: error.name,
            stack: error.stack
          },
          componentStack: errorInfo.componentStack,
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent
        })
      }).catch(err => {
        console.error('Failed to send error to server:', err);
      });
    } catch (e) {
      console.error('Error while reporting error:', e);
    }
    */
  }

  resetError = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  }

  navigateHome = (): void => {
    this.resetError();
    window.location.href = '/';
  }

  render(): ReactNode {
    const { hasError, error, errorInfo, errorCount } = this.state;
    const { children, fallback, showDetails = false } = this.props;

    if (hasError) {
      // You can render any custom fallback UI
      if (fallback) {
        return fallback;
      }

      return (
        <Card className="error-boundary-container">
          <Alert
            type="error"
            showIcon
            icon={<BugOutlined />}
            message="Something went wrong"
            description="We're sorry, but an error occurred while rendering this component."
            action={
              <Space direction="vertical">
                <Button 
                  type="primary" 
                  icon={<ReloadOutlined />} 
                  onClick={this.resetError}
                >
                  Try Again
                </Button>
                <Button 
                  icon={<HomeOutlined />} 
                  onClick={this.navigateHome}
                >
                  Go to Homepage
                </Button>
              </Space>
            }
          />

          {showDetails && (
            <>
              <Divider />
              <div className="error-details">
                <Title level={5}>Error Details</Title>
                <Paragraph>
                  <Text strong>Message:</Text> {error?.message || 'Unknown error'}
                </Paragraph>

                {error && error.stack && (
                  <Paragraph>
                    <Text strong>Stack:</Text>
                    <pre className="error-stack">{error.stack}</pre>
                  </Paragraph>
                )}

                {errorInfo && errorInfo.componentStack && (
                  <Paragraph>
                    <Text strong>Component Stack:</Text>
                    <pre className="error-stack">{errorInfo.componentStack}</pre>
                  </Paragraph>
                )}

                <Paragraph>
                  <Text type="secondary">
                    Component: {this.props.componentName || 'Unknown'}
                  </Text>
                </Paragraph>

                {errorCount > 1 && (
                  <Paragraph>
                    <Text type="warning">
                      This error has occurred {errorCount} times.
                    </Text>
                  </Paragraph>
                )}
              </div>
            </>
          )}
        </Card>
      );
    }

    // Render normally if there's no error
    return children;
  }
}

export default ErrorBoundary; 