/* Evidence Box Styles for TurdParty Reports */

.evidence-box {
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  margin: 20px 0;
  background: #f8f9fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
}

.evidence-header {
  padding: 15px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 6px 6px 0 0;
  transition: background 0.3s ease;
}

.evidence-header:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.evidence-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.evidence-summary {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.evidence-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.expand-icon {
  font-size: 14px;
  transition: transform 0.3s ease;
  user-select: none;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.evidence-content {
  padding: 20px;
  background: white;
  border-radius: 0 0 6px 6px;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

.evidence-section {
  margin-bottom: 25px;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 20px;
}

.evidence-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.evidence-section h4 {
  color: #495057;
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  border-left: 4px solid #667eea;
  padding-left: 10px;
}

.query-block, .command-block {
  margin-bottom: 20px;
  background: #f8f9fa;
  border-radius: 6px;
  overflow: hidden;
}

.query-block h5, .command-block h5 {
  background: #e9ecef;
  margin: 0;
  padding: 10px 15px;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
}

.sql-query, .bash-command {
  background: #2d3748;
  color: #e2e8f0;
  padding: 15px;
  margin: 0;
  font-family: 'Fira Code', 'Monaco', 'Menlo', monospace;
  font-size: 13px;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre;
}

.sql-query {
  border-left: 4px solid #4299e1;
}

.bash-command {
  border-left: 4px solid #48bb78;
}

.query-result {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  margin: 10px 15px 15px 15px;
  padding: 10px;
}

.query-result strong {
  color: #2d3748;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.query-result pre {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 10px;
  margin: 5px 0 0 0;
  font-size: 12px;
  overflow-x: auto;
  color: #2d3748;
}

.ecs-links {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

@media (max-width: 768px) {
  .ecs-links {
    grid-template-columns: 1fr;
  }
}

.link-group h5 {
  color: #495057;
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
}

.ecs-link, .kibana-link {
  display: block;
  padding: 10px 15px;
  margin: 5px 0;
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #495057;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 13px;
  font-weight: 500;
}

.ecs-link:hover {
  background: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
}

.kibana-link:hover {
  background: #f3e5f5;
  border-color: #9c27b0;
  color: #7b1fa2;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(156, 39, 176, 0.2);
}

.ecs-sample {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  max-height: 400px;
  overflow-y: auto;
}

.ecs-sample pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: 15px;
  margin: 0;
  font-family: 'Fira Code', 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
}

.evidence-footer {
  background: #f8f9fa;
  padding: 10px 15px;
  border-top: 1px solid #e9ecef;
  border-radius: 0 0 4px 4px;
  margin: 20px -20px -20px -20px;
}

.evidence-footer small {
  color: #6c757d;
  font-size: 11px;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .evidence-box {
    background: #1a202c;
    border-color: #2d3748;
  }
  
  .evidence-content {
    background: #2d3748;
    color: #e2e8f0;
  }
  
  .evidence-section h4 {
    color: #e2e8f0;
  }
  
  .query-block, .command-block {
    background: #1a202c;
  }
  
  .query-block h5, .command-block h5 {
    background: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
  }
  
  .ecs-link, .kibana-link {
    background: #1a202c;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  
  .evidence-footer {
    background: #1a202c;
    border-color: #4a5568;
  }
}
