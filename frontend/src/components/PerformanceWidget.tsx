import React, { useState, useEffect } from 'react';
import { Card, Statistic, Spin, Typography, Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import { ClockCircleOutlined, DashboardOutlined } from '@ant-design/icons';
import axios from 'axios';
import API_ENDPOINTS from '../utils/apiConfig';

const { Title, Text } = Typography;

interface PerformanceData {
  average_duration: number;
  count: number;
}

const PerformanceWidget: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<PerformanceData | null>(null);
  const navigate = useNavigate();
  
  const fetchPerformanceData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.get(API_ENDPOINTS.LOGS.PERFORMANCE_SUMMARY + '?limit=50');
      setData({
        average_duration: response.data.average_duration,
        count: response.data.count
      });
    } catch (err) {
      setError('Failed to load performance data');
      console.error('Error fetching performance data:', err);
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchPerformanceData();
    
    // Refresh data every 30 seconds
    const interval = setInterval(fetchPerformanceData, 30000);
    
    return () => clearInterval(interval);
  }, []);
  
  const handleViewDetails = () => {
    navigate('/performance');
  };
  
  if (loading) {
    return (
      <Card title="Performance" style={{ width: '100%', marginBottom: '16px' }}>
        <div style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
          <Spin />
        </div>
      </Card>
    );
  }
  
  if (error) {
    return (
      <Card title="Performance" style={{ width: '100%', marginBottom: '16px' }}>
        <Text type="danger">{error}</Text>
        <Button type="link" onClick={fetchPerformanceData}>Retry</Button>
      </Card>
    );
  }
  
  return (
    <Card 
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>Performance Metrics</span>
          <Button type="link" onClick={handleViewDetails}>View Details</Button>
        </div>
      } 
      style={{ width: '100%', marginBottom: '16px' }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-around' }}>
        <Statistic 
          title="Average Response Time" 
          value={data?.average_duration || 0} 
          precision={2}
          suffix="ms"
          prefix={<ClockCircleOutlined />}
        />
        <Statistic 
          title="Metrics Collected" 
          value={data?.count || 0}
          prefix={<DashboardOutlined />}
        />
      </div>
    </Card>
  );
};

export default PerformanceWidget; 