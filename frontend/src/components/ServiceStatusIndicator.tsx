import React, { useEffect, useState } from 'react';
import { Badge, Tooltip, Space, Button } from 'antd';
import { CloudServerOutlined, ApiOutlined, SyncOutlined } from '@ant-design/icons';
import axios from 'axios';
import API_ENDPOINTS from '../utils/apiConfig';

interface ServiceStatus {
  minio: 'success' | 'error' | 'processing';
  vagrant: 'success' | 'error' | 'processing';
}

const ServiceStatusIndicator: React.FC = () => {
  const [status, setStatus] = useState<ServiceStatus>({
    minio: 'processing',
    vagrant: 'processing'
  });
  const [lastChecked, setLastChecked] = useState<Date>(new Date());
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const checkServiceStatus = async () => {
    setIsLoading(true);
    try {
      // Check MinIO status
      const minioStatus = await axios.get(`${process.env.REACT_APP_API_URL}${API_ENDPOINTS.HEALTH.MINIO_STATUS}`, {
        timeout: 5000
      });
      
      // Check Vagrant gRPC status
      const vagrantStatus = await axios.get(`${process.env.REACT_APP_API_URL}${API_ENDPOINTS.HEALTH.VAGRANT_GRPC_STATUS}`, {
        timeout: 5000
      });
      
      setStatus({
        minio: minioStatus.data.status === 'ok' ? 'success' : 'error',
        vagrant: vagrantStatus.data.status === 'ok' ? 'success' : 'error'
      });
    } catch (error: any) {
      console.error('Error checking service status:', error);
      
      // If there's an error, we'll try to determine which service is down
      setStatus(prevStatus => ({
        minio: error.response?.config?.url?.includes('minio') ? 'error' : prevStatus.minio,
        vagrant: error.response?.config?.url?.includes('vagrant') ? 'error' : prevStatus.vagrant
      }));
    } finally {
      setIsLoading(false);
      setLastChecked(new Date());
    }
  };

  useEffect(() => {
    // Check status immediately on component mount
    checkServiceStatus();
    
    // Set up interval to check status every 30 seconds
    const interval = setInterval(checkServiceStatus, 30000);
    
    // Clean up interval on component unmount
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="service-status-indicator" style={{ 
      position: 'fixed', 
      bottom: '20px', 
      right: '20px', 
      zIndex: 1000,
      background: 'rgba(255, 255, 255, 0.8)',
      padding: '8px 12px',
      borderRadius: '4px',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
    }}>
      <Space>
        <Tooltip title={`MinIO API: ${status.minio === 'success' ? 'Available' : status.minio === 'processing' ? 'Checking...' : 'Unavailable'} (Last checked: ${lastChecked.toLocaleTimeString()})`}>
          <Badge 
            status={status.minio} 
            text={<CloudServerOutlined style={{ fontSize: '18px' }} />} 
          />
        </Tooltip>
        <Tooltip title={`Vagrant gRPC: ${status.vagrant === 'success' ? 'Available' : status.vagrant === 'processing' ? 'Checking...' : 'Unavailable'} (Last checked: ${lastChecked.toLocaleTimeString()})`}>
          <Badge 
            status={status.vagrant} 
            text={<ApiOutlined style={{ fontSize: '18px' }} />} 
          />
        </Tooltip>
        <Tooltip title="Refresh status">
          <Button 
            type="text" 
            icon={<SyncOutlined style={{ fontSize: '16px' }} spin={isLoading} />} 
            size="small" 
            onClick={checkServiceStatus}
            style={{ marginLeft: '4px' }}
            loading={isLoading}
          />
        </Tooltip>
      </Space>
    </div>
  );
};

export default ServiceStatusIndicator; 