import React, { useState, useEffect, useRef } from 'react';

interface CommandOutput {
  type: 'command_output' | 'error';
  stdout?: string;
  stderr?: string;
  exit_code?: number;
  is_complete?: boolean;
  execution_time?: number;
  message?: string;
}

interface RealTimeTerminalProps {
  vmId: string;
  className?: string;
}

const RealTimeTerminal: React.FC<RealTimeTerminalProps> = ({ vmId, className = '' }) => {
  const [command, setCommand] = useState('');
  const [output, setOutput] = useState<CommandOutput[]>([]);
  const [isExecuting, setIsExecuting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  
  const outputRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const socketRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    // Auto-scroll to bottom when new output arrives
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, [output]);

  const connectWebSocket = () => {
    try {
      const wsUrl = `ws://localhost:8000/api/v1/vms/${vmId}/commands/execute`;
      const ws = new WebSocket(wsUrl);
      socketRef.current = ws;

      ws.onopen = () => {
        setIsConnected(true);
        setConnectionError(null);
        console.log(`Connected to terminal for VM ${vmId}`);
        
        // Add welcome message
        setOutput(prev => [...prev, {
          type: 'command_output',
          stdout: `Connected to VM ${vmId} terminal\nType commands and press Enter to execute.\n\n`
        }]);
      };

      ws.onmessage = (event) => {
        try {
          const data: CommandOutput = JSON.parse(event.data);
          setOutput(prev => [...prev, data]);
          
          if (data.is_complete) {
            setIsExecuting(false);
          }
        } catch (error) {
          console.error('Error parsing command output:', error);
          setOutput(prev => [...prev, {
            type: 'error',
            message: 'Error parsing server response'
          }]);
        }
      };

      ws.onclose = (event) => {
        setIsConnected(false);
        setIsExecuting(false);
        console.log(`Terminal disconnected: ${event.code} ${event.reason}`);
        
        if (event.code !== 1000) {
          setConnectionError('Connection lost. Please refresh to reconnect.');
        }
      };

      ws.onerror = (error) => {
        console.error('Terminal WebSocket error:', error);
        setConnectionError('Connection error occurred');
        setIsConnected(false);
        setIsExecuting(false);
      };

    } catch (error) {
      console.error('Error creating terminal connection:', error);
      setConnectionError('Failed to create terminal connection');
    }
  };

  useEffect(() => {
    connectWebSocket();

    return () => {
      if (socketRef.current) {
        socketRef.current.close(1000, 'Component unmounting');
      }
    };
  }, [vmId]);

  const executeCommand = async () => {
    if (!command.trim() || isExecuting || !isConnected) return;

    const trimmedCommand = command.trim();
    
    // Add to command history
    setCommandHistory(prev => {
      const newHistory = [trimmedCommand, ...prev.filter(cmd => cmd !== trimmedCommand)];
      return newHistory.slice(0, 50); // Keep last 50 commands
    });
    setHistoryIndex(-1);

    setIsExecuting(true);
    setCommand('');

    // Add command to output
    setOutput(prev => [...prev, {
      type: 'command_output',
      stdout: `$ ${trimmedCommand}\n`
    }]);

    try {
      if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
        socketRef.current.send(JSON.stringify({
          command: trimmedCommand,
          working_directory: '/tmp'
        }));
      } else {
        throw new Error('WebSocket connection not available');
      }
    } catch (error) {
      setOutput(prev => [...prev, {
        type: 'error',
        message: `Command execution error: ${error}`
      }]);
      setIsExecuting(false);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      executeCommand();
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      if (historyIndex < commandHistory.length - 1) {
        const newIndex = historyIndex + 1;
        setHistoryIndex(newIndex);
        setCommand(commandHistory[newIndex]);
      }
    } else if (event.key === 'ArrowDown') {
      event.preventDefault();
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1;
        setHistoryIndex(newIndex);
        setCommand(commandHistory[newIndex]);
      } else if (historyIndex === 0) {
        setHistoryIndex(-1);
        setCommand('');
      }
    }
  };

  const clearOutput = () => {
    setOutput([]);
  };

  const reconnect = () => {
    setConnectionError(null);
    connectWebSocket();
  };

  const formatOutput = (item: CommandOutput, index: number) => {
    let content = '';
    let className = 'whitespace-pre-wrap font-mono text-sm ';

    if (item.stdout) {
      content += item.stdout;
      className += 'text-green-300';
    }
    
    if (item.stderr) {
      content += item.stderr;
      className += 'text-red-300';
    }
    
    if (item.message) {
      content += item.message;
      className += item.type === 'error' ? 'text-red-300' : 'text-yellow-300';
    }
    
    if (item.is_complete && item.exit_code !== undefined) {
      const exitInfo = `\n[Process exited with code ${item.exit_code}]`;
      const timeInfo = item.execution_time ? ` (${item.execution_time.toFixed(2)}s)` : '';
      content += exitInfo + timeInfo + '\n';
      className += ' text-gray-400';
    }

    return (
      <div key={index} className={className}>
        {content}
      </div>
    );
  };

  return (
    <div className={`terminal-container bg-gray-900 text-white rounded-lg shadow-lg ${className}`}>
      {/* Terminal Header */}
      <div className="terminal-header bg-gray-800 px-4 py-2 rounded-t-lg flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="flex space-x-1">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          </div>
          <h3 className="text-sm font-medium text-gray-300">
            VM Terminal - {vmId}
          </h3>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <span className={`inline-block w-2 h-2 rounded-full ${
              isConnected ? 'bg-green-400' : 'bg-red-400'
            }`}></span>
            <span className="text-xs text-gray-400">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          
          <button
            onClick={clearOutput}
            className="text-xs text-gray-400 hover:text-white px-2 py-1 rounded"
            title="Clear terminal"
          >
            Clear
          </button>
          
          {!isConnected && (
            <button
              onClick={reconnect}
              className="text-xs text-blue-400 hover:text-blue-300 px-2 py-1 rounded"
              title="Reconnect"
            >
              Reconnect
            </button>
          )}
        </div>
      </div>

      {/* Connection Error */}
      {connectionError && (
        <div className="bg-red-900 text-red-200 px-4 py-2 text-sm">
          <strong>Connection Error:</strong> {connectionError}
        </div>
      )}

      {/* Terminal Output */}
      <div 
        ref={outputRef}
        className="terminal-output bg-black p-4 h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800"
      >
        {output.length === 0 ? (
          <div className="text-gray-500 text-sm">
            Terminal ready. Type a command and press Enter.
          </div>
        ) : (
          output.map((item, index) => formatOutput(item, index))
        )}
        
        {isExecuting && (
          <div className="text-yellow-300 text-sm animate-pulse">
            Executing command...
          </div>
        )}
      </div>

      {/* Terminal Input */}
      <div className="terminal-input bg-gray-800 px-4 py-3 rounded-b-lg flex items-center space-x-2">
        <span className="text-green-400 font-mono text-sm">$</span>
        <input
          ref={inputRef}
          type="text"
          value={command}
          onChange={(e) => setCommand(e.target.value)}
          onKeyDown={handleKeyPress}
          disabled={isExecuting || !isConnected}
          placeholder={isConnected ? "Enter command..." : "Not connected"}
          className="flex-1 bg-transparent text-white font-mono text-sm outline-none placeholder-gray-500 disabled:opacity-50"
          autoComplete="off"
          spellCheck={false}
        />
        
        <button
          onClick={executeCommand}
          disabled={isExecuting || !command.trim() || !isConnected}
          className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isExecuting ? 'Running...' : 'Execute'}
        </button>
      </div>

      {/* Command History Hint */}
      {commandHistory.length > 0 && (
        <div className="px-4 py-1 bg-gray-800 text-xs text-gray-400 rounded-b-lg">
          Use ↑/↓ arrows to navigate command history ({commandHistory.length} commands)
        </div>
      )}
    </div>
  );
};

export default RealTimeTerminal;
