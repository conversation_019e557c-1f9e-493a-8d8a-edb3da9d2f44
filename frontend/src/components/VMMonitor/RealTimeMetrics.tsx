import React, { useState, useEffect, useRef } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface VMMetrics {
  vm_id: string;
  vm_type: string;
  timestamp: number;
  status: string;
  cpu_percent: number;
  memory_percent: number;
  memory_used_mb: number;
  memory_limit_bytes: number;
  disk_usage_percent?: number;
  network_rx_bytes: number;
  network_tx_bytes: number;
  top_processes: ProcessInfo[];
  uptime_seconds: number;
  error?: string;
}

interface ProcessInfo {
  pid: number;
  name: string;
  cpu_percent: number;
  memory_mb: number;
}

interface RealTimeVMMetricsProps {
  vmId: string;
  vmType?: string;
}

const RealTimeVMMetrics: React.FC<RealTimeVMMetricsProps> = ({ 
  vmId, 
  vmType = "docker" 
}) => {
  const [metrics, setMetrics] = useState<VMMetrics[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const socketRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const connectWebSocket = () => {
    try {
      const wsUrl = `ws://localhost:8000/api/v1/vms/${vmId}/metrics/stream?vm_type=${vmType}`;
      const ws = new WebSocket(wsUrl);
      socketRef.current = ws;

      ws.onopen = () => {
        setIsConnected(true);
        setConnectionError(null);
        console.log(`Connected to VM metrics stream for ${vmId}`);
      };

      ws.onmessage = (event) => {
        try {
          const newMetrics: VMMetrics = JSON.parse(event.data);
          setLastUpdate(new Date());
          
          if (newMetrics.error) {
            setConnectionError(newMetrics.error);
            return;
          }

          setMetrics(prev => {
            const updated = [...prev, newMetrics];
            // Keep only last 60 data points (1 minute at 1s intervals)
            return updated.slice(-60);
          });
        } catch (error) {
          console.error('Error parsing metrics data:', error);
        }
      };

      ws.onclose = (event) => {
        setIsConnected(false);
        console.log(`Disconnected from VM metrics stream: ${event.code} ${event.reason}`);
        
        // Attempt to reconnect after 5 seconds
        if (event.code !== 1000) { // Not a normal closure
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log('Attempting to reconnect...');
            connectWebSocket();
          }, 5000);
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionError('Connection error occurred');
        setIsConnected(false);
      };

    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      setConnectionError('Failed to create connection');
    }
  };

  useEffect(() => {
    connectWebSocket();

    return () => {
      if (socketRef.current) {
        socketRef.current.close(1000, 'Component unmounting');
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [vmId, vmType]);

  const chartData = {
    labels: metrics.map(m => new Date(m.timestamp).toLocaleTimeString()),
    datasets: [
      {
        label: 'CPU %',
        data: metrics.map(m => m.cpu_percent),
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        tension: 0.1,
        fill: false,
      },
      {
        label: 'Memory %',
        data: metrics.map(m => m.memory_percent),
        borderColor: 'rgb(54, 162, 235)',
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        tension: 0.1,
        fill: false,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: `VM ${vmId} - Real-time Metrics`,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        title: {
          display: true,
          text: 'Percentage (%)',
        },
      },
      x: {
        title: {
          display: true,
          text: 'Time',
        },
      },
    },
    animation: {
      duration: 0, // Disable animations for real-time updates
    },
  };

  const latestMetrics = metrics[metrics.length - 1];

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}h ${minutes}m ${secs}s`;
  };

  return (
    <div className="vm-metrics-container p-6 bg-white rounded-lg shadow-lg">
      {/* Connection Status */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold text-gray-800">
          VM Metrics - {vmId}
        </h2>
        <div className="flex items-center space-x-2">
          <span className={`inline-block w-3 h-3 rounded-full ${
            isConnected ? 'bg-green-500' : 'bg-red-500'
          }`}></span>
          <span className={`text-sm font-medium ${
            isConnected ? 'text-green-600' : 'text-red-600'
          }`}>
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
          {lastUpdate && (
            <span className="text-xs text-gray-500">
              Last update: {lastUpdate.toLocaleTimeString()}
            </span>
          )}
        </div>
      </div>

      {/* Error Display */}
      {connectionError && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <strong>Error:</strong> {connectionError}
        </div>
      )}

      {/* Current Metrics Summary */}
      {latestMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-blue-600 mb-1">CPU Usage</h3>
            <div className="text-2xl font-bold text-blue-800">
              {latestMetrics.cpu_percent.toFixed(1)}%
            </div>
          </div>
          
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-green-600 mb-1">Memory Usage</h3>
            <div className="text-2xl font-bold text-green-800">
              {latestMetrics.memory_percent.toFixed(1)}%
            </div>
            <div className="text-xs text-green-600">
              {latestMetrics.memory_used_mb.toFixed(0)} MB
            </div>
          </div>
          
          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-purple-600 mb-1">Network I/O</h3>
            <div className="text-sm font-bold text-purple-800">
              ↓ {formatBytes(latestMetrics.network_rx_bytes)}
            </div>
            <div className="text-sm font-bold text-purple-800">
              ↑ {formatBytes(latestMetrics.network_tx_bytes)}
            </div>
          </div>
          
          <div className="bg-orange-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-orange-600 mb-1">Uptime</h3>
            <div className="text-lg font-bold text-orange-800">
              {formatUptime(latestMetrics.uptime_seconds)}
            </div>
            <div className="text-xs text-orange-600">
              Status: {latestMetrics.status}
            </div>
          </div>
        </div>
      )}

      {/* Metrics Chart */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-3">Performance Trends</h3>
        <div className="h-64">
          {metrics.length > 0 ? (
            <Line data={chartData} options={chartOptions} />
          ) : (
            <div className="flex items-center justify-center h-full bg-gray-50 rounded">
              <span className="text-gray-500">Waiting for metrics data...</span>
            </div>
          )}
        </div>
      </div>

      {/* Top Processes */}
      {latestMetrics && latestMetrics.top_processes.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-700 mb-3">Top Processes</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border border-gray-200 rounded-lg">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">PID</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">CPU %</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Memory (MB)</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {latestMetrics.top_processes.slice(0, 5).map((proc, index) => (
                  <tr key={`${proc.pid}-${index}`} className="hover:bg-gray-50">
                    <td className="px-4 py-2 text-sm text-gray-900">{proc.pid}</td>
                    <td className="px-4 py-2 text-sm text-gray-900 font-medium">{proc.name}</td>
                    <td className="px-4 py-2 text-sm text-gray-900">{proc.cpu_percent.toFixed(1)}%</td>
                    <td className="px-4 py-2 text-sm text-gray-900">{proc.memory_mb.toFixed(1)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default RealTimeVMMetrics;
