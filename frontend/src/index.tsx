import React, { Suspense } from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { Spin } from 'antd';
import './i18n'; // Import i18n configuration
import ErrorBoundary from './components/ErrorBoundary';
import { logErrorToBackend } from './utils/errorLogger';
import configureAxios from './utils/axiosConfig';

// Configure axios defaults
configureAxios();

// Set up global error handlers
window.addEventListener('error', (event) => {
  logErrorToBackend(
    event.error || event.message,
    'window.onerror',
    {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    }
  );
});

window.addEventListener('unhandledrejection', (event) => {
  const error = event.reason instanceof Error 
    ? event.reason 
    : new Error(String(event.reason));
  
  logErrorToBackend(
    error,
    'unhandledrejection',
    { originalReason: event.reason }
  );
});

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

// Loading component for suspense fallback
const Loader = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '100vh' 
  }}>
    <Spin size="large" />
  </div>
);

root.render(
  <React.StrictMode>
    <ErrorBoundary componentName="Root">
      <Suspense fallback={<Loader />}>
        <App />
      </Suspense>
    </ErrorBoundary>
  </React.StrictMode>
); 