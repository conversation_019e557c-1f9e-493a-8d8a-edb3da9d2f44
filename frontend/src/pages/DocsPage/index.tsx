import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import './styles.css';
import { 
  Card, Button, Switch, Typography, Space, Divider
} from 'antd';
import { 
  ArrowLeftOutlined, BulbOutlined, BulbFilled
} from '@ant-design/icons';

const { Title } = Typography;

const DocsPage: React.FC = () => {
  const navigate = useNavigate();
  const [darkMode, setDarkMode] = useState<boolean>(localStorage.getItem('darkMode') === 'true');
  
  useEffect(() => {
    // Apply dark mode to the iframe content
    const applyDarkMode = () => {
      try {
        const iframe = document.getElementById('docs-frame') as HTMLIFrameElement;
        if (iframe && iframe.contentDocument) {
          const iframeDoc = iframe.contentDocument;
          
          // Create or update the style element
          let styleEl = iframeDoc.getElementById('dark-mode-style');
          if (!styleEl) {
            styleEl = iframeDoc.createElement('style');
            styleEl.id = 'dark-mode-style';
            iframeDoc.head.appendChild(styleEl);
          }
          
          if (darkMode) {
            styleEl.textContent = `
              body { background-color: #1f1f1f; color: #f0f0f0; }
              pre { background-color: #2d2d2d; border: 1px solid #444; }
              code { background-color: #2d2d2d; color: #e6e6e6; }
              a { color: #1890ff; }
              h1, h2, h3, h4, h5, h6 { color: #f0f0f0; }
              table { border-color: #444; }
              th, td { border-color: #444; }
              hr { border-color: #444; }
            `;
            iframeDoc.body.classList.add('dark-mode');
          } else {
            styleEl.textContent = '';
            iframeDoc.body.classList.remove('dark-mode');
          }
        }
      } catch (e) {
        console.error('Error applying dark mode to iframe:', e);
      }
    };
    
    // Apply dark mode when iframe loads
    const iframe = document.getElementById('docs-frame') as HTMLIFrameElement;
    if (iframe) {
      iframe.onload = applyDarkMode;
    }
    
    // Apply immediately if iframe is already loaded
    applyDarkMode();
    
    // Save preference
    localStorage.setItem('darkMode', darkMode.toString());
  }, [darkMode]);
  
  const handleDarkModeToggle = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);
    localStorage.setItem('darkMode', newDarkMode.toString());
    
    // Dispatch a custom event to notify App component
    window.dispatchEvent(new Event('darkModeChange'));
  };
  
  return (
    <div className="docs-page-container">
      <Card className="docs-page-card">
        <div className="docs-page-header">
          <Space>
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={() => navigate('/')}
            >
              Back to Main
            </Button>
            <Title level={3} style={{ margin: 0 }}>TurdParty Documentation</Title>
          </Space>
          
          <Space>
            <Switch 
              checked={darkMode} 
              onChange={handleDarkModeToggle} 
              checkedChildren={<BulbFilled />} 
              unCheckedChildren={<BulbOutlined />} 
            />
            <span>{darkMode ? 'Dark Mode' : 'Light Mode'}</span>
          </Space>
        </div>
        
        <Divider />
        
        <div className="docs-frame-container">
          <iframe
            id="docs-frame"
            src="http://localhost:8000/docs"
            title="TurdParty Documentation"
            className="docs-frame"
          ></iframe>
        </div>
      </Card>
    </div>
  );
};

export default DocsPage; 