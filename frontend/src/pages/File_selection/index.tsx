import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './styles.css';
import { Table, Button, Spin, Alert, Modal, Form, Input, Select, Space, Tag, message, Tooltip } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, FileOutlined, FolderOutlined } from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import type { TableProps } from 'antd/es/table';

interface FileInfo {
  id: string;
  filename: string;
  file_size: number;
  content_type: string;
  download_url: string;
}

interface FileSelectionData {
  id: string;
  name: string;
  description: string;
  file_upload_id: string;
  target_path: string;
  permissions: string;
  created_on: string;
  modified_on: string | null;
  owner_id: string;
  is_active: boolean;
  file_info: FileInfo | null;
}

interface FileSelectionListResponse {
  items: FileSelectionData[];
  total: number;
}

interface FileUploadData {
  id: string;
  filename: string;
  file_size: number;
  content_type: string;
  description: string;
  download_url: string;
  is_active?: boolean;
}

interface FileUploadListResponse {
  items: FileUploadData[];
  total: number;
}

const FileSelectionPage: React.FC = () => {
  const [data, setData] = useState<FileSelectionData[]>([]);
  const [fileUploads, setFileUploads] = useState<FileUploadData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadingFiles, setLoadingFiles] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [editItem, setEditItem] = useState<FileSelectionData | null>(null);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { token } = useAuth();
  
  const fetchFileSelectionData = async () => {
    try {
      setLoading(true);
      const response = await axios.get<FileSelectionListResponse>('/api/file_selection', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      setData(response.data.items);
      setError(null);
    } catch (err) {
      console.error('Error fetching file selection data:', err);
      setError('Failed to load file selection data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const fetchFileUploads = async () => {
    try {
      setLoadingFiles(true);
      const response = await axios.get<FileUploadListResponse>('/api/file_upload', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      setFileUploads(response.data.items.filter(file => file.is_active !== false));
      setError(null);
    } catch (err) {
      console.error('Error fetching file uploads:', err);
      message.error('Failed to load available files');
    } finally {
      setLoadingFiles(false);
    }
  };

  useEffect(() => {
    fetchFileSelectionData();
    fetchFileUploads();
  }, [token]);

  const showCreateModal = () => {
    setEditItem(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const showEditModal = (item: FileSelectionData) => {
    setEditItem(item);
    form.setFieldsValue({
      name: item.name,
      description: item.description,
      file_upload_id: item.file_upload_id,
      target_path: item.target_path,
      permissions: item.permissions
    });
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editItem) {
        // Update existing item
        await axios.put(`/api/file_selection/${editItem.id}`, values, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        message.success('File selection updated successfully');
      } else {
        // Create new item
        await axios.post('/api/file_selection', values, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        message.success('File selection created successfully');
      }
      
      setIsModalVisible(false);
      fetchFileSelectionData();
    } catch (err) {
      console.error('Form submission error:', err);
      message.error('Failed to save file selection');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await axios.delete(`/api/file_selection/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      message.success('File selection deleted successfully');
      fetchFileSelectionData();
    } catch (err) {
      console.error('Error deleting file selection:', err);
      message.error('Failed to delete file selection');
    }
  };

  const columns: TableProps<FileSelectionData>['columns'] = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <span>
          <FileOutlined style={{ marginRight: 8 }} />
          {text}
        </span>
      ),
    },
    {
      title: 'File',
      dataIndex: 'file_info',
      key: 'file',
      render: (fileInfo) => fileInfo ? (
        <Tooltip title={`${fileInfo.filename} (${formatFileSize(fileInfo.file_size)})`}>
          <a href={fileInfo.download_url} target="_blank" rel="noopener noreferrer">
            {fileInfo.filename}
          </a>
        </Tooltip>
      ) : 'File not found',
    },
    {
      title: 'Target Path',
      dataIndex: 'target_path',
      key: 'target_path',
      render: (text) => (
        <Tooltip title={text}>
          <span>
            <FolderOutlined style={{ marginRight: 8 }} />
            {text}
          </span>
        </Tooltip>
      ),
    },
    {
      title: 'Permissions',
      dataIndex: 'permissions',
      key: 'permissions',
      render: (text) => <code>{text}</code>,
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (text) => text || 'No description',
    },
    {
      title: 'Created',
      dataIndex: 'created_on',
      key: 'created_on',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          <Button 
            icon={<EditOutlined />} 
            onClick={() => showEditModal(record)}
            title="Edit"
          />
          <Button 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record.id)}
            title="Delete"
          />
        </Space>
      ),
    },
  ];

  const formatFileSize = (size: number) => {
    const kb = size / 1024;
    if (kb < 1024) {
      return `${kb.toFixed(2)} KB`;
    } else {
      return `${(kb / 1024).toFixed(2)} MB`;
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <p>Loading file selection data...</p>
      </div>
    );
  }

  return (
    <div className="file-selection-container">
      <div className="file-selection-header">
        <h1>File Selection Management</h1>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={showCreateModal}
        >
          Create New Selection
        </Button>
      </div>
      
      {error && <Alert message={error} type="error" className="file-selection-alert" />}
      
      <div className="file-selection-table">
        <Table 
          columns={columns} 
          dataSource={data.map(item => ({ ...item, key: item.id }))} 
          pagination={{ pageSize: 10 }}
          bordered
        />
      </div>
      
      <Modal
        title={editItem ? `Edit ${editItem.name}` : "Create New File Selection"}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="Name"
            rules={[{ required: true, message: 'Please enter a name' }]}
          >
            <Input placeholder="Enter a name for this selection" />
          </Form.Item>
          
          <Form.Item
            name="file_upload_id"
            label="Select File"
            rules={[{ required: true, message: 'Please select a file' }]}
          >
            <Select
              placeholder="Select a file"
              loading={loadingFiles}
              optionFilterProp="children"
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
              options={fileUploads.map(file => ({
                value: file.id,
                label: `${file.filename} (${formatFileSize(file.file_size)})`,
              }))}
            />
          </Form.Item>
          
          <Form.Item
            name="target_path"
            label="Target Path"
            rules={[{ required: true, message: 'Please enter a target path' }]}
          >
            <Input 
              placeholder="Enter the path where the file should be placed in the VM" 
              addonBefore="/"
            />
          </Form.Item>
          
          <Form.Item
            name="permissions"
            label="File Permissions"
            initialValue="0644"
            rules={[
              { required: true, message: 'Please enter file permissions' },
              { pattern: /^[0-7]{4}$/, message: 'Permissions must be in octal format (e.g., 0644)' }
            ]}
          >
            <Input placeholder="0644" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea rows={4} placeholder="Enter a description for this selection" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FileSelectionPage;
