import React, { useEffect, useState, useCallback } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './styles.css';
import { Card, Button, Spin, Alert, Modal, Form, Input, Table, Space, Tag, message, Tooltip, Popconfirm, Typography, Divider } from 'antd';
import { EditOutlined, DeleteOutlined, DownloadOutlined, UploadOutlined, ReloadOutlined, CheckCircleOutlined, ClockCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import type { TableProps } from 'antd/es/table';
import API_ENDPOINTS from '../../utils/apiConfig';
import { FileUpload } from '../../components/FileUpload';

// Additional imports
import { AxiosError } from 'axios';

interface FileUploadData {
  id: string;
  filename: string;
  file_size: number;
  content_type: string;
  file_hash: string;
  description: string;
  download_url: string;
  created_on: string;
  modified_on: string | null;
  owner_id: string;
  is_active: boolean;
}

interface FileUploadListResponse {
  items: FileUploadData[];
  total: number;
}

interface RecentUpload {
  id: string;
  filename: string;
  size: number;
  uploadTime: Date;
  status: 'completed' | 'processing' | 'failed';
  message?: string;
}

const FileUploadPage: React.FC = () => {
  const [data, setData] = useState<FileUploadData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isUploadModalVisible, setIsUploadModalVisible] = useState<boolean>(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState<boolean>(false);
  const [editItem, setEditItem] = useState<FileUploadData | null>(null);
  const [refreshingToken, setRefreshingToken] = useState(false);
  const [requestTimeout, setRequestTimeout] = useState<NodeJS.Timeout | null>(null);
  const [recentUploads, setRecentUploads] = useState<RecentUpload[]>([]);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { token, refreshToken } = useAuth();
  
  // Use useCallback to memoize this function as it's used in useEffect
  const fetchFileUploadData = useCallback(async () => {
    if (!token) {
      setError('Authentication token is missing. Please try refreshing the page or log in again.');
      setLoading(false);
      return;
    }
    
    // Clear any existing timeout
    if (requestTimeout) {
      clearTimeout(requestTimeout);
    }
    
    // Set a timeout to prevent infinite loading
    const timeout = setTimeout(() => {
      if (loading) {
        setLoading(false);
        setError('Request timed out. Please try again or check your connection.');
        console.warn('File upload data request timed out');
      }
    }, 15000); // 15 second timeout
    
    setRequestTimeout(timeout);
    
    try {
      setLoading(true);
      console.log('Fetching file upload data from:', API_ENDPOINTS.FILE_UPLOAD.BASE);
      
      const response = await axios.get<FileUploadListResponse>(API_ENDPOINTS.FILE_UPLOAD.BASE, {
        headers: {
          Authorization: `Bearer ${token}`
        },
        timeout: 10000 // 10 second axios timeout
      });
      
      console.log('File upload data response:', response.status, response.statusText);
      
      if (response.data && Array.isArray(response.data.items)) {
        setData(response.data.items);
        setError(null);
      } else if (response.data && Array.isArray(response.data)) {
        // Handle the case where API returns a direct array instead of items property
        setData(response.data);
        setError(null);
      } else {
        console.warn('Unexpected response format:', response.data);
        setData([]);
        setError('Received unexpected data format from server');
      }
    } catch (err: any) {
      console.error('Error fetching file upload data:', err);
      
      // Enhanced error logging
      if (axios.isAxiosError(err)) {
        const axiosError = err as AxiosError;
        console.error('Status:', axiosError.response?.status);
        console.error('Status Text:', axiosError.response?.statusText);
        console.error('Data:', axiosError.response?.data);
      }
      
      // Check if it's an auth error
      if (err.response?.status === 401) {
        setError('Authentication error: Your session may have expired. Please try refreshing your token.');
      } else if (err.code === 'ECONNABORTED') {
        setError('Request timed out. Please try again later.');
      } else if (!err.response) {
        setError('Network error: Unable to reach the server. Please check your connection.');
      } else {
        setError(`Failed to load file uploads: ${err.response?.data?.detail || err.message || 'Unknown error'}`);
      }
      
      // Set empty data to avoid showing stale data
      setData([]);
    } finally {
      setLoading(false);
      // Clear the timeout we set earlier
      if (requestTimeout) {
        clearTimeout(requestTimeout);
        setRequestTimeout(null);
      }
    }
  }, [token, loading, requestTimeout]);

  useEffect(() => {
    // Try to fetch data when component mounts and token is available
    if (token) {
      fetchFileUploadData();
    } else {
      console.log('No token available, waiting for token');
      setLoading(false);
      setError('Authentication token is missing. Please try refreshing your token.');
    }
    
    // Cleanup timeout on component unmount
    return () => {
      if (requestTimeout) {
        clearTimeout(requestTimeout);
      }
    };
  }, [token, fetchFileUploadData]);

  const handleRefreshToken = async () => {
    setRefreshingToken(true);
    setError(null);
    
    try {
      console.log('FileUploadPage: Attempting to refresh token...');
      const success = await refreshToken();
      console.log('FileUploadPage: Token refresh result:', success);
      
      if (success) {
        message.success('Successfully refreshed authentication token');
        // Attempt to fetch data with the new token
        await fetchFileUploadData();
      } else {
        console.error('FileUploadPage: Token refresh failed');
        setError('Failed to refresh authentication token. Please try again or reload the page.');
        message.error('Failed to refresh authentication token');
      }
    } catch (err) {
      console.error('FileUploadPage: Error refreshing token:', err);
      setError('Error refreshing authentication token. Please reload the page and try again.');
    } finally {
      setRefreshingToken(false);
    }
  };

  const showUploadModal = () => {
    setIsUploadModalVisible(true);
  };

  const showEditModal = (item: FileUploadData) => {
    setEditItem(item);
    form.setFieldsValue({
      filename: item.filename,
      description: item.description,
      is_active: item.is_active
    });
    setIsEditModalVisible(true);
  };

  const handleUploadCancel = () => {
    setIsUploadModalVisible(false);
  };

  const handleEditCancel = () => {
    setIsEditModalVisible(false);
    setEditItem(null);
    form.resetFields();
  };

  const handleUploadSuccess = (response: any) => {
    message.success('File uploaded successfully');
    fetchFileUploadData(); // Refresh the file list
    setIsUploadModalVisible(false); // Close the modal
    
    // Add to recent uploads
    if (response && response.id) {
      const newUpload: RecentUpload = {
        id: response.id,
        filename: response.filename,
        size: response.file_size || 0,
        uploadTime: new Date(),
        status: 'completed',
      };
      
      setRecentUploads(prev => [newUpload, ...prev].slice(0, 5)); // Keep only last 5 uploads
    }
  };

  const handleUploadError = (error: any) => {
    console.error('Upload error:', error);
    message.error('Failed to upload file');
    
    // Add to recent uploads with failed status
    const newUpload: RecentUpload = {
      id: `failed-${Date.now()}`,
      filename: error.fileName || 'Unknown file',
      size: error.fileSize || 0,
      uploadTime: new Date(),
      status: 'failed',
      message: error.message || 'Upload failed'
    };
    
    setRecentUploads(prev => [newUpload, ...prev].slice(0, 5)); // Keep only last 5 uploads
  };

  const handleEdit = async () => {
    if (!editItem) return;
    
    try {
      const values = await form.validateFields();
      
      await axios.put(API_ENDPOINTS.FILE_UPLOAD.BY_ID(editItem.id), values, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      setIsEditModalVisible(false);
      message.success('File updated successfully');
      fetchFileUploadData();
    } catch (err) {
      console.error('Form submission error:', err);
      message.error('Failed to update file');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await axios.delete(API_ENDPOINTS.FILE_UPLOAD.BY_ID(id), {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      message.success('File deleted successfully');
      fetchFileUploadData();
    } catch (err) {
      console.error('Error deleting file:', err);
      message.error('Failed to delete file');
    }
  };

  const handleDownload = (url: string, filename: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const columns: TableProps<FileUploadData>['columns'] = [
    {
      title: 'Filename',
      dataIndex: 'filename',
      key: 'filename',
      render: (text, record) => (
        <a onClick={() => handleDownload(record.download_url, record.filename)}>
          {text}
        </a>
      ),
    },
    {
      title: 'Size',
      dataIndex: 'file_size',
      key: 'file_size',
      render: (size) => {
        const kb = size / 1024;
        if (kb < 1024) {
          return `${kb.toFixed(2)} KB`;
        } else {
          return `${(kb / 1024).toFixed(2)} MB`;
        }
      },
    },
    {
      title: 'Type',
      dataIndex: 'content_type',
      key: 'content_type',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (text) => text || 'No description',
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Uploaded',
      dataIndex: 'created_on',
      key: 'created_on',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Download">
            <Button 
              icon={<DownloadOutlined />} 
              onClick={() => handleDownload(record.download_url, record.filename)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button 
              icon={<EditOutlined />} 
              onClick={() => showEditModal(record)}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Popconfirm
              title="Are you sure you want to delete this file?"
              onConfirm={() => handleDelete(record.id)}
              okText="Yes"
              cancelText="No"
            >
              <Button 
                danger 
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const renderRecentUploads = () => {
    if (recentUploads.length === 0) {
      return null;
    }
    
    return (
      <div className="recent-uploads-section">
        <Divider orientation="left">Recent Uploads</Divider>
        <ul className="recent-uploads-list">
          {recentUploads.map(upload => (
            <li key={upload.id} className={`recent-upload-item status-${upload.status}`}>
              <div className="upload-item-icon">
                {upload.status === 'completed' ? (
                  <CheckCircleOutlined style={{ color: '#52c41a' }} />
                ) : upload.status === 'processing' ? (
                  <ClockCircleOutlined style={{ color: '#1890ff' }} />
                ) : (
                  <CloseCircleOutlined style={{ color: '#f5222d' }} />
                )}
              </div>
              <div className="upload-item-details">
                <div className="upload-item-filename">{upload.filename}</div>
                <div className="upload-item-info">
                  <span>{(upload.size / 1024 / 1024).toFixed(2)} MB</span>
                  <span>•</span>
                  <span>{formatRelativeTime(upload.uploadTime)}</span>
                </div>
              </div>
              <div className="upload-item-status">
                <Tag color={
                  upload.status === 'completed' ? 'success' : 
                  upload.status === 'processing' ? 'processing' : 
                  'error'
                }>
                  {upload.status.charAt(0).toUpperCase() + upload.status.slice(1)}
                </Tag>
              </div>
            </li>
          ))}
        </ul>
      </div>
    );
  };
  
  const formatRelativeTime = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    
    if (diffSec < 60) return `${diffSec}s ago`;
    if (diffSec < 3600) return `${Math.floor(diffSec / 60)}m ago`;
    if (diffSec < 86400) return `${Math.floor(diffSec / 3600)}h ago`;
    return `${Math.floor(diffSec / 86400)}d ago`;
  };

  if (loading) {
    return (
      <div className="file-upload-container">
        <div className="file-upload-header">
          <h1>File Upload Management</h1>
        </div>
        
        <Card className="loading-card">
          <div className="loading-container">
            <Spin size="large" />
            <Typography.Title level={4} style={{ marginTop: 16 }}>
              Loading file upload data...
            </Typography.Title>
            <Typography.Text type="secondary">
              This may take a few moments. Please wait.
            </Typography.Text>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="file-upload-container">
      <div className="file-upload-header">
        <h1>File Upload Management</h1>
        <Button 
          type="primary" 
          icon={<UploadOutlined />} 
          onClick={showUploadModal}
        >
          Upload New File
        </Button>
      </div>
      
      {error && (
        <Alert 
          message="Error" 
          description={
            <div>
              {error}
              {error.includes('Authentication') && (
                <div style={{ marginTop: '10px' }}>
                  <Button 
                    type="primary" 
                    icon={<ReloadOutlined />} 
                    onClick={handleRefreshToken} 
                    loading={refreshingToken}
                  >
                    Refresh Authentication Token
                  </Button>
                </div>
              )}
              {error.includes('connection') && (
                <div style={{ marginTop: '10px' }}>
                  <Button 
                    type="primary" 
                    icon={<ReloadOutlined />} 
                    onClick={() => fetchFileUploadData()}
                  >
                    Retry Connection
                  </Button>
                </div>
              )}
            </div>
          } 
          type="error" 
          className="file-upload-alert" 
          closable
        />
      )}
      
      <div className="file-upload-table">
        <Table 
          columns={columns} 
          dataSource={data.map(item => ({ ...item, key: item.id }))} 
          pagination={{ pageSize: 10 }}
          bordered
        />
      </div>
      
      {renderRecentUploads()}
      
      {/* Upload Modal */}
      <Modal
        title="Upload File"
        visible={isUploadModalVisible}
        onCancel={handleUploadCancel}
        footer={null}
        width={800}
      >
        {token ? (
          <FileUpload
            onUploadSuccess={handleUploadSuccess}
            onUploadError={handleUploadError}
            token={token}
          />
        ) : (
          <Alert 
            message="Authentication Error" 
            description={
              <div>
                Unable to upload files: Authentication token is missing. Please try refreshing your token or reload the page.
                <div style={{ marginTop: '10px' }}>
                  <Button 
                    type="primary" 
                    icon={<ReloadOutlined />} 
                    onClick={handleRefreshToken} 
                    loading={refreshingToken}
                  >
                    Get Authentication Token
                  </Button>
                </div>
              </div>
            }
            type="error" 
            showIcon 
          />
        )}
      </Modal>
      
      {/* Edit Modal */}
      <Modal
        title={editItem ? `Edit ${editItem.filename}` : ""}
        open={isEditModalVisible}
        onOk={handleEdit}
        onCancel={handleEditCancel}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="filename"
            label="Filename"
            rules={[{ required: true, message: 'Please enter a filename' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea rows={4} />
          </Form.Item>
          <Form.Item
            name="is_active"
            label="Status"
            valuePropName="checked"
          >
            <Input type="checkbox" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FileUploadPage;
