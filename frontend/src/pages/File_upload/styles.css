.file-upload-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.file-upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.file-upload-table {
  margin-top: 20px;
}

.file-upload-alert {
  margin-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-card {
  margin: 20px 0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Customize the upload dragger */
.ant-upload-drag {
  border: 2px dashed #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.3s;
}

.ant-upload-drag:hover {
  border-color: #1890ff;
}

.ant-upload-drag-icon {
  font-size: 48px;
  color: #1890ff;
}

.ant-upload-text {
  margin: 0 0 4px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
}

.ant-upload-hint {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

/* Table styles */
.file-upload-table .ant-table-thead > tr > th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.file-upload-table .ant-table-tbody > tr:hover > td {
  background-color: #f0f7ff;
}

/* Button spacing in action column */
.file-upload-table .ant-space {
  gap: 8px;
}

/* File size column alignment */
.file-upload-table .ant-table-cell:nth-child(2) {
  text-align: right;
}

/* Status tag styling */
.file-upload-table .ant-tag {
  min-width: 60px;
  text-align: center;
}

/* Dark mode styles */
body.dark-mode .loading-card {
  background-color: #1f1f1f;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
}

body.dark-mode .file-upload-table .ant-table-thead > tr > th {
  background-color: #2a2a2a;
}

body.dark-mode .file-upload-table .ant-table-tbody > tr:hover > td {
  background-color: #111a2c;
}

/* Responsive styles */
@media (max-width: 768px) {
  .file-upload-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .file-upload-header button {
    margin-top: 10px;
  }
}

/* File upload page styles */
.file-upload-page {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-container p {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.45);
}

/* Recent uploads section */
.recent-uploads-section {
  margin-bottom: 24px;
}

.recent-uploads-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recent-upload-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s;
}

.recent-upload-item:hover {
  background-color: #f5f5f5;
}

.recent-upload-item:last-child {
  border-bottom: none;
}

.upload-item-icon {
  margin-right: 16px;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-item-details {
  flex: 1;
  min-width: 0;
}

.upload-item-filename {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.upload-item-info {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.upload-item-info span:not(:last-child) {
  margin-right: 8px;
}

.upload-item-status {
  margin-left: 16px;
}

/* Status-specific styles */
.recent-upload-item.status-completed .upload-item-filename {
  color: #52c41a;
}

.recent-upload-item.status-processing .upload-item-filename {
  color: #1890ff;
}

.recent-upload-item.status-failed .upload-item-filename {
  color: #f5222d;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .recent-upload-item {
    border-bottom-color: #303030;
  }
  
  .recent-upload-item:hover {
    background-color: #1f1f1f;
  }
  
  .upload-item-filename {
    color: rgba(255, 255, 255, 0.85);
  }
  
  .upload-item-info {
    color: rgba(255, 255, 255, 0.45);
  }
  
  .loading-container p {
    color: rgba(255, 255, 255, 0.45);
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .file-upload-page {
    padding: 8px;
  }
  
  .recent-upload-item {
    padding: 8px 12px;
  }
  
  .upload-item-icon {
    margin-right: 12px;
    font-size: 20px;
  }
  
  .upload-item-filename {
    font-size: 14px;
  }
  
  .upload-item-info {
    font-size: 11px;
  }
}
