.status-page-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.status-page-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 70vh;
}

.status-page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.status-overview-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.status-badge-container {
  display: flex;
  align-items: center;
  height: 100%;
}

.component-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  transition: all 0.3s;
}

.component-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.status-cards-container {
  margin-bottom: 24px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .status-overview-card,
  .component-card {
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.05);
  }
  
  .component-card:hover {
    box-shadow: 0 4px 16px rgba(255, 255, 255, 0.08);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .status-page-container {
    padding: 16px;
  }
  
  .status-page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .status-page-header button {
    align-self: flex-start;
  }
} 