import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import './styles.css';
import axios from 'axios';
import { 
  Table, Button, Spin, Alert, Modal, Form, Input, Select, 
  Space, Tag, message, Tooltip, InputNumber, Switch, Tabs, 
  Card, Statistic, Badge, Descriptions, Divider, Typography, Empty
} from 'antd';
import { 
  PlusOutlined, EditOutlined, DeleteOutlined, 
  PlayCircleOutlined, PauseCircleOutlined, ReloadOutlined, 
  StopOutlined, DesktopOutlined, CodeOutlined, SettingOutlined,
  ReloadOutlined as RefreshIcon, WarningOutlined, QuestionCircleOutlined
} from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import type { TableProps } from 'antd/es/table';
import API_ENDPOINTS from '../../utils/apiConfig';

const { TabPane } = Tabs;
const { Title, Paragraph, Text } = Typography;

// Network retry configuration
const MAX_RETRIES = 3;
const RETRY_DELAY = 2000; // 2 seconds

interface VagrantVMData {
  id: string;
  name: string;
  description: string;
  template: string;
  memory_mb: number;
  cpus: number;
  disk_gb: number;
  status: string;
  ip_address: string | null;
  ssh_port: number | null;
  vagrant_id: string | null;
  created_on: string;
  modified_on: string | null;
  owner_id: string;
  last_action: string | null;
  last_action_time: string | null;
  error_message: string | null;
  domain: string;
}

interface VagrantVMListResponse {
  items: VagrantVMData[];
  total: number;
}

interface VagrantVMStatusResponse {
  id: string;
  name: string;
  status: string;
  ip_address: string | null;
  ssh_port: number | null;
  last_action: string | null;
  last_action_time: string | null;
  error_message: string | null;
  domain: string;
}

interface TemplateInfo {
  value: string;
  name: string;
  description: string;
}

const VagrantVMPage: React.FC = () => {
  // State
  const [vms, setVms] = useState<VagrantVMData[]>([]);
  const [templates, setTemplates] = useState<TemplateInfo[]>([]);
  const [selectedVM, setSelectedVM] = useState<VagrantVMData | null>(null);
  const [vmsLoading, setVmsLoading] = useState<boolean>(true);
  const [templatesLoading, setTemplatesLoading] = useState<boolean>(true);
  const [vmsError, setVmsError] = useState<Error | null>(null);
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const [isCreateModalVisible, setIsCreateModalVisible] = useState<boolean>(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState<boolean>(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState<boolean>(false);
  const [retryingConnection, setRetryingConnection] = useState<boolean>(false);
  const [retryCount, setRetryCount] = useState<number>(0);

  const pollingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { token } = useAuth();
  
  // Function to sleep/delay
  const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
  
  // Fetch VMs with retry logic
  const fetchVMs = useCallback(async (retry = 0): Promise<void> => {
    if (!token) return;
    
    try {
      // Don't show loading state on retries or during polling
      if (retry === 0 && vms.length === 0) {
        setVmsLoading(true);
      }
      
      if (retry > 0) {
        setRetryingConnection(true);
      }
      
      const response = await axios.get<VagrantVMListResponse>(API_ENDPOINTS.VAGRANT_VM.BASE, {
        headers: { Authorization: `Bearer ${token}` },
        timeout: 10000 // 10 second timeout
      });
      
      console.log('VM Data:', response.data);
      setVms(response.data.items);
      setVmsError(null);
      setRetryingConnection(false);
      setRetryCount(0);
    } catch (error) {
      console.error('Error fetching VMs:', error);
      
      // Implement retry logic
      if (retry < MAX_RETRIES) {
        console.log(`Retrying connection (${retry + 1}/${MAX_RETRIES})...`);
        setRetryCount(retry + 1);
        await sleep(RETRY_DELAY);
        return fetchVMs(retry + 1);
      }
      
      setVmsError(error instanceof Error ? error : new Error('Failed to fetch VMs'));
      setRetryingConnection(false);
    } finally {
      if (retry === 0 || retry === MAX_RETRIES) {
        setVmsLoading(false);
      }
    }
  }, [token, vms.length]);
  
  // Fetch templates with retry logic
  const fetchTemplates = useCallback(async (retry = 0): Promise<void> => {
    if (!token) return;
    
    try {
      setTemplatesLoading(true);
      
      // Try OS templates first
      try {
        const response = await axios.get<TemplateInfo[]>(API_ENDPOINTS.VAGRANT_VM.OS_TEMPLATES, {
          headers: { Authorization: `Bearer ${token}` },
          timeout: 5000
        });
        
        if (response.data && response.data.length > 0) {
          setTemplates(response.data);
          setTemplatesLoading(false);
          return;
        }
      } catch (error) {
        console.warn('OS templates not available, trying regular templates');
      }
      
      // Fall back to regular templates
      try {
        const fallbackResponse = await axios.get<TemplateInfo[]>(API_ENDPOINTS.VAGRANT_VM.TEMPLATES, {
          headers: { Authorization: `Bearer ${token}` },
          timeout: 5000
        });
        
        if (fallbackResponse.data && fallbackResponse.data.length > 0) {
          setTemplates(fallbackResponse.data);
          setTemplatesLoading(false);
          return;
        }
      } catch (error) {
        console.warn('Regular templates not available, using hardcoded templates');
      }
      
      // Use hardcoded templates as last resort
      setTemplates([
        { value: 'ubuntu_2004', name: 'Ubuntu 20.04 LTS', description: 'Ubuntu 20.04 LTS (Focal Fossa) - Long term support release' },
        { value: 'ubuntu_2204', name: 'Ubuntu 22.04 LTS', description: 'Ubuntu 22.04 LTS (Jammy Jellyfish) - Latest LTS release' },
        { value: 'debian_11', name: 'Debian 11', description: 'Debian 11 (Bullseye) - Stable Debian release' },
        { value: 'centos_7', name: 'CentOS 7', description: 'CentOS 7 - Enterprise-focused Linux distribution' },
        { value: 'centos_stream_9', name: 'CentOS Stream 9', description: 'CentOS Stream 9 - Continuous delivery distribution' },
        { value: 'fedora_37', name: 'Fedora 37', description: 'Fedora 37 - Latest Fedora Workstation release' },
        { value: 'windows_10', name: 'Windows 10', description: 'Windows 10 Professional - Modern Windows desktop OS' },
        { value: 'windows_server_2019', name: 'Windows Server 2019', description: 'Windows Server 2019 - Enterprise server OS' }
      ]);
    } catch (error) {
      console.error('Error fetching templates:', error);
      
      // Implement retry logic
      if (retry < MAX_RETRIES) {
        console.log(`Retrying template fetch (${retry + 1}/${MAX_RETRIES})...`);
        await sleep(RETRY_DELAY);
        return fetchTemplates(retry + 1);
      }
    } finally {
      setTemplatesLoading(false);
    }
  }, [token]);
  
  // Function to set up polling with debounce
  const setupPolling = useCallback((): void => {
    // Clear any existing timeout
    if (pollingTimeoutRef.current) {
      clearTimeout(pollingTimeoutRef.current);
    }
    
    pollingTimeoutRef.current = setTimeout(() => {
      if (!actionLoading) {
        fetchVMs();
      }
      setupPolling(); // Set up the next poll
    }, 10000); // Poll every 10 seconds
  }, [actionLoading, fetchVMs]);
  
  // Manually refresh data
  const handleRefresh = (): void => {
    fetchVMs();
  };
  
  // Initial data loading
  useEffect(() => {
    fetchVMs();
    fetchTemplates();
    
    // Set up polling
    setupPolling();
    
    // Clean up on unmount
    return () => {
      if (pollingTimeoutRef.current) {
        clearTimeout(pollingTimeoutRef.current);
      }
    };
  }, [token, fetchVMs, fetchTemplates, setupPolling]);
  
  // Create VM
  const handleCreate = async (): Promise<void> => {
    try {
      setActionLoading(true);
      const values = await form.validateFields();
      
      await axios.post(API_ENDPOINTS.VAGRANT_VM.BASE, values, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setIsCreateModalVisible(false);
      message.success('VM created successfully');
      fetchVMs();
    } catch (error) {
      console.error('Error creating VM:', error);
      message.error('Failed to create VM. Please try again.');
    } finally {
      setActionLoading(false);
      form.resetFields();
    }
  };
  
  // Edit VM
  const handleEdit = async (): Promise<void> => {
    try {
      setActionLoading(true);
      const values = await form.validateFields();
      
      if (!selectedVM) {
        message.error('No VM selected for editing');
        return;
      }
      
      await axios.put(API_ENDPOINTS.VAGRANT_VM.BY_ID(selectedVM.id), values, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setIsEditModalVisible(false);
      message.success('VM updated successfully');
      fetchVMs();
    } catch (error) {
      console.error('Error updating VM:', error);
      message.error('Failed to update VM. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };
  
  // Delete VM
  const handleDelete = async (id: string): Promise<void> => {
    try {
      setActionLoading(true);
      await axios.delete(API_ENDPOINTS.VAGRANT_VM.BY_ID(id), {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      message.success('VM deleted successfully');
      fetchVMs();
    } catch (error) {
      console.error('Error deleting VM:', error);
      message.error('Failed to delete VM. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };
  
  // VM Action (start, stop, etc.)
  const handleVMAction = async (id: string, action: string): Promise<void> => {
    try {
      setActionLoading(true);
      await axios.post(API_ENDPOINTS.VAGRANT_VM.ACTION(id), { action }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      message.success(`VM ${action} initiated successfully`);
      fetchVMs();
    } catch (error) {
      console.error(`Error performing VM action (${action}):`, error);
      message.error(`Failed to ${action} VM. Please try again.`);
    } finally {
      setActionLoading(false);
    }
  };
  
  // Show modals
  const showCreateModal = (): void => {
    form.resetFields();
    // Set default values
    form.setFieldsValue({
      memory_mb: 2048,
      cpus: 2,
      disk_gb: 20,
      domain: 'TurdParty'
    });
    setIsCreateModalVisible(true);
  };
  
  const showEditModal = (vm: VagrantVMData): void => {
    setSelectedVM(vm);
    form.setFieldsValue({
      name: vm.name,
      description: vm.description,
      memory_mb: vm.memory_mb,
      cpus: vm.cpus,
      disk_gb: vm.disk_gb
    });
    setIsEditModalVisible(true);
  };
  
  const showDetailModal = (vm: VagrantVMData): void => {
    setSelectedVM(vm);
    setIsDetailModalVisible(true);
  };
  
  const handleCreateCancel = (): void => {
    setIsCreateModalVisible(false);
  };
  
  const handleEditCancel = (): void => {
    setIsEditModalVisible(false);
    setSelectedVM(null);
  };
  
  const handleDetailCancel = (): void => {
    setIsDetailModalVisible(false);
    setSelectedVM(null);
  };
  
  // Helper functions for status display
  const getStatusColor = (status: string): string => {
    switch (status?.toUpperCase()) {
      case 'RUNNING':
        return 'green';
      case 'STOPPED':
      case 'SUSPENDED':
        return 'orange';
      case 'PENDING':
      case 'STARTING':
      case 'STOPPING':
        return 'processing';
      case 'ERROR':
        return 'red';
      case 'UNKNOWN':
      default:
        return 'default';
    }
  };
  
  const getStatusIcon = (status: string): React.ReactNode => {
    switch (status?.toUpperCase()) {
      case 'RUNNING':
        return <PlayCircleOutlined />;
      case 'STOPPED':
        return <StopOutlined />;
      case 'SUSPENDED':
        return <PauseCircleOutlined />;
      case 'PENDING':
      case 'STARTING':
      case 'STOPPING':
        return <Spin size="small" />;
      case 'ERROR':
        return <WarningOutlined />;
      case 'UNKNOWN':
      default:
        return <QuestionCircleOutlined />;
    }
  };
  
  // Table columns configuration
  const columns: TableProps<VagrantVMData>['columns'] = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <a onClick={() => showDetailModal(record)}>{text}</a>
      )
    },
    {
      title: 'Template',
      dataIndex: 'template',
      key: 'template',
      render: (text) => {
        const template = templates.find((t) => t.value === text);
        return template ? template.name : text;
      }
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (text) => (
        <Tag icon={getStatusIcon(text)} color={getStatusColor(text)}>
          {text?.toUpperCase() || 'UNKNOWN'}
        </Tag>
      )
    },
    {
      title: 'IP Address',
      dataIndex: 'ip_address',
      key: 'ip_address',
      render: (text) => text || '-'
    },
    {
      title: 'Memory / CPUs',
      key: 'resources',
      render: (_, record) => (
        <>
          {record.memory_mb ? `${record.memory_mb} MB` : '-'} / {record.cpus ? `${record.cpus} CPUs` : '-'}
        </>
      )
    },
    {
      title: 'Created',
      dataIndex: 'created_on',
      key: 'created_on',
      render: (text) => {
        if (!text) return '-';
        return new Date(text).toLocaleString();
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => {
        const status = record.status?.toUpperCase();
        return (
          <Space size="small">
            {status === 'RUNNING' ? (
              <Tooltip title="Stop VM">
                <Button
                  icon={<StopOutlined />}
                  onClick={() => handleVMAction(record.id, 'stop')}
                  loading={actionLoading}
                  disabled={actionLoading}
                />
              </Tooltip>
            ) : status === 'STOPPED' || status === 'ERROR' ? (
              <Tooltip title="Start VM">
                <Button
                  icon={<PlayCircleOutlined />}
                  onClick={() => handleVMAction(record.id, 'start')}
                  loading={actionLoading}
                  disabled={actionLoading}
                  type="primary"
                />
              </Tooltip>
            ) : null}
            
            <Tooltip title="Edit VM">
              <Button
                icon={<EditOutlined />}
                onClick={() => showEditModal(record)}
                disabled={actionLoading || ['STARTING', 'STOPPING', 'PENDING'].includes(status || '')}
              />
            </Tooltip>
            
            <Tooltip title="Delete VM">
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(record.id)}
                disabled={actionLoading}
              />
            </Tooltip>
          </Space>
        );
      }
    }
  ];

  return (
    <div className="vagrant-vm-container">
      <div className="vagrant-vm-header">
        <div className="left-section">
          <h1>Vagrant VM Management</h1>
          {retryingConnection && (
            <Tag color="processing" icon={<Spin size="small" />}>
              Reconnecting ({retryCount}/{MAX_RETRIES})...
            </Tag>
          )}
        </div>
        <div className="right-section">
          <Button 
            icon={<RefreshIcon />} 
            onClick={handleRefresh}
            style={{ marginRight: 8 }}
            disabled={vmsLoading || actionLoading}
          />
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={showCreateModal}
          >
            Create New VM
          </Button>
        </div>
      </div>
      
      {vmsError && (
        <Alert 
          message="Network Error" 
          description={
            <div>
              <p>Failed to connect to the server. Please check your connection and try again.</p>
              <Button 
                type="primary" 
                icon={<RefreshIcon />} 
                onClick={handleRefresh}
                style={{ marginTop: 8 }}
              >
                Retry Connection
              </Button>
            </div>
          }
          type="error" 
          showIcon
          icon={<WarningOutlined />}
          className="vagrant-vm-alert" 
        />
      )}
      
      <div className="vagrant-vm-table">
        <Table 
          columns={columns} 
          dataSource={vms.map(item => ({ ...item, key: item.id }))} 
          pagination={{ pageSize: 10 }}
          bordered
          loading={vmsLoading}
          locale={{
            emptyText: vmsError ? (
              <Empty 
                description="Could not load VMs" 
                image={Empty.PRESENTED_IMAGE_SIMPLE} 
              />
            ) : (
              <Empty 
                description="No VMs found" 
                image={Empty.PRESENTED_IMAGE_SIMPLE} 
              />
            )
          }}
        />
      </div>
      
      {/* Create VM Modal */}
      <Modal
        title="Create New Vagrant VM"
        open={isCreateModalVisible}
        onOk={handleCreate}
        onCancel={handleCreateCancel}
        confirmLoading={actionLoading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="VM Name"
            rules={[{ required: true, message: 'Please enter a name' }]}
          >
            <Input placeholder="Enter a name for this VM" />
          </Form.Item>
          
          <Form.Item
            name="template"
            label="VM Template"
            rules={[{ required: true, message: 'Please select a template' }]}
          >
            <Select
              placeholder="Select a template"
              loading={templatesLoading}
              options={templates.map(t => ({ value: t.value, label: t.name }))}
            />
          </Form.Item>
          
          <div className="resource-inputs">
            <Form.Item
              name="cpus"
              label="CPUs"
              rules={[{ required: true, message: 'Please enter CPU count' }]}
            >
              <InputNumber min={1} max={8} />
            </Form.Item>
            
            <Form.Item
              name="memory_mb"
              label="Memory (MB)"
              rules={[{ required: true, message: 'Please enter memory size' }]}
            >
              <InputNumber min={512} max={16384} step={512} />
            </Form.Item>
            
            <Form.Item
              name="disk_gb"
              label="Disk (GB)"
              rules={[{ required: true, message: 'Please enter disk size' }]}
            >
              <InputNumber min={5} max={100} />
            </Form.Item>
          </div>
          
          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea rows={4} placeholder="Enter a description for this VM" />
          </Form.Item>
          
          <Form.Item
            name="domain"
            label="Domain"
            initialValue="TurdParty"
            hidden
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* Edit VM Modal */}
      <Modal
        title={selectedVM ? `Edit ${selectedVM.name}` : "Edit VM"}
        open={isEditModalVisible}
        onOk={handleEdit}
        onCancel={handleEditCancel}
        confirmLoading={actionLoading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="VM Name"
            rules={[{ required: true, message: 'Please enter a name' }]}
          >
            <Input placeholder="Enter a name for this VM" />
          </Form.Item>
          
          <div className="resource-inputs">
            <Form.Item
              name="cpus"
              label="CPUs"
              rules={[{ required: true, message: 'Please enter CPU count' }]}
            >
              <InputNumber min={1} max={8} />
            </Form.Item>
            
            <Form.Item
              name="memory_mb"
              label="Memory (MB)"
              rules={[{ required: true, message: 'Please enter memory size' }]}
            >
              <InputNumber min={512} max={16384} step={512} />
            </Form.Item>
            
            <Form.Item
              name="disk_gb"
              label="Disk (GB)"
              rules={[{ required: true, message: 'Please enter disk size' }]}
            >
              <InputNumber min={5} max={100} />
            </Form.Item>
          </div>
          
          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea rows={4} placeholder="Enter a description for this VM" />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* VM Detail Modal */}
      <Modal
        title={selectedVM ? `${selectedVM.name} Details` : "VM Details"}
        open={isDetailModalVisible}
        onCancel={handleDetailCancel}
        footer={null}
        width={700}
      >
        {selectedVM && (
          <Tabs defaultActiveKey="info">
            <TabPane tab="Information" key="info">
              <Descriptions bordered column={2}>
                <Descriptions.Item label="Name" span={2}>{selectedVM.name}</Descriptions.Item>
                <Descriptions.Item label="ID" span={2}>{selectedVM.id}</Descriptions.Item>
                <Descriptions.Item label="Status">
                  <Tag icon={getStatusIcon(selectedVM.status)} color={getStatusColor(selectedVM.status)}>
                    {selectedVM.status?.toUpperCase() || 'UNKNOWN'}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="Template">
                  {templates.find(t => t.value === selectedVM.template)?.name || selectedVM.template}
                </Descriptions.Item>
                <Descriptions.Item label="Memory">{selectedVM.memory_mb} MB</Descriptions.Item>
                <Descriptions.Item label="CPUs">{selectedVM.cpus}</Descriptions.Item>
                <Descriptions.Item label="Disk">{selectedVM.disk_gb} GB</Descriptions.Item>
                <Descriptions.Item label="Domain">{selectedVM.domain}</Descriptions.Item>
                <Descriptions.Item label="IP Address" span={2}>
                  {selectedVM.ip_address || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="SSH Port">
                  {selectedVM.ssh_port || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="Vagrant ID">
                  {selectedVM.vagrant_id || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="Created On" span={2}>
                  {selectedVM.created_on ? new Date(selectedVM.created_on).toLocaleString() : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="Last Modified" span={2}>
                  {selectedVM.modified_on ? new Date(selectedVM.modified_on).toLocaleString() : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="Last Action" span={2}>
                  {selectedVM.last_action ? (
                    <>
                      {selectedVM.last_action.toUpperCase()}
                      {selectedVM.last_action_time ? (
                        <span style={{ marginLeft: 8, color: '#888' }}>
                          ({new Date(selectedVM.last_action_time).toLocaleString()})
                        </span>
                      ) : null}
                    </>
                  ) : '-'}
                </Descriptions.Item>
                {selectedVM.error_message && (
                  <Descriptions.Item label="Error Message" span={2}>
                    <Alert
                      message="Error"
                      description={selectedVM.error_message}
                      type="error"
                      showIcon
                    />
                  </Descriptions.Item>
                )}
                <Descriptions.Item label="Description" span={2}>
                  {selectedVM.description || '-'}
                </Descriptions.Item>
              </Descriptions>
            </TabPane>
            <TabPane tab="Actions" key="actions">
              <Card>
                <div className="vm-actions-container">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Button 
                      type="primary" 
                      icon={<PlayCircleOutlined />} 
                      disabled={selectedVM.status?.toUpperCase() === 'RUNNING' || actionLoading}
                      onClick={() => handleVMAction(selectedVM.id, 'start')}
                      loading={actionLoading}
                      block
                    >
                      Start VM
                    </Button>
                    <Button 
                      icon={<StopOutlined />}
                      disabled={selectedVM.status?.toUpperCase() !== 'RUNNING' || actionLoading}
                      onClick={() => handleVMAction(selectedVM.id, 'stop')}
                      loading={actionLoading}
                      danger
                      block
                    >
                      Stop VM
                    </Button>
                    <Button 
                      icon={<ReloadOutlined />}
                      disabled={selectedVM.status?.toUpperCase() !== 'RUNNING' || actionLoading}
                      onClick={() => handleVMAction(selectedVM.id, 'restart')}
                      loading={actionLoading}
                      block
                    >
                      Restart VM
                    </Button>
                    <Divider />
                    <Button 
                      icon={<EditOutlined />}
                      onClick={() => {
                        handleDetailCancel();
                        showEditModal(selectedVM);
                      }}
                      block
                    >
                      Edit VM
                    </Button>
                    <Button 
                      icon={<DeleteOutlined />}
                      danger
                      onClick={() => {
                        handleDetailCancel();
                        handleDelete(selectedVM.id);
                      }}
                      loading={actionLoading}
                      block
                    >
                      Delete VM
                    </Button>
                  </Space>
                </div>
              </Card>
            </TabPane>
          </Tabs>
        )}
      </Modal>
    </div>
  );
};

export default VagrantVMPage;
