.vagrant-vm-container {
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.vagrant-vm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.vagrant-vm-header .left-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.vagrant-vm-header h1 {
  margin-bottom: 0;
  font-size: 24px;
  font-weight: 600;
}

.vagrant-vm-alert {
  margin-bottom: 20px;
}

.vagrant-vm-table {
  margin-bottom: 24px;
}

.vagrant-vm-table .ant-table-thead > tr > th {
  background-color: #f7f7f7;
  font-weight: 600;
}

.resource-inputs {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

@media (max-width: 768px) {
  .resource-inputs {
    grid-template-columns: 1fr;
  }
}

.vm-actions-container {
  padding: 16px 0;
}

.ant-descriptions-header {
  margin-bottom: 16px;
}

/* Status colors */
.ant-tag-green {
  background-color: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.ant-tag-red {
  background-color: #fff1f0;
  border-color: #ffa39e;
  color: #f5222d;
}

.ant-tag-processing {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.ant-tag-orange {
  background-color: #fff7e6;
  border-color: #ffd591;
  color: #fa8c16;
}

/* Loading animations */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.loading-container p {
  margin-top: 16px;
  color: #8c8c8c;
}

/* Modal styles */
.ant-modal-body {
  padding: 24px;
}

.template-option {
  padding: 8px 0;
}

.template-name {
  font-weight: 600;
}

.template-description {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
}

/* Button spacing in table */
.ant-table-cell .ant-space {
  gap: 8px;
}

/* Detail view styles */
.vm-details {
  padding: 8px;
}

.vm-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.vm-status-card {
  margin-bottom: 16px;
}

.last-action {
  margin-top: 8px;
  font-size: 12px;
}

.vm-description {
  margin-top: 16px;
}

/* Error message styles */
.ant-alert-with-description {
  margin: 8px 0;
}

/* Form styles */
.ant-form-item-label > label {
  font-weight: 500;
}

.ant-input-number {
  width: 100%;
}

/* Tab styles */
.ant-tabs-tab {
  padding: 12px 16px !important;
}
