/**
 * API configuration utility
 * Centralizes API URL management and versioning
 */

// API version prefix - Updated for TurdParty enhanced system
export const API_PREFIX = 'http://api.turdparty.localhost/api/v1';

/**
 * Get the full API URL for a given endpoint
 * @param endpoint - The API endpoint path (without the /api/v1 prefix)
 * @returns The full API URL with the correct prefix
 */
export const getApiUrl = (endpoint: string): string => {
  // Remove any leading slash from the endpoint
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;

  // If the endpoint already includes the API prefix, return it as is
  if (cleanEndpoint.startsWith('api/v1/')) {
    // Extract the part after api/v1/
    const path = cleanEndpoint.substring(7);
    return `${API_PREFIX}/${path}`;
  }

  // If the endpoint already includes the old /api/ prefix, replace it
  if (cleanEndpoint.startsWith('api/')) {
    return `${API_PREFIX}/${cleanEndpoint.substring(4)}`;
  }

  // List of endpoints that require trailing slashes
  const needsTrailingSlash = [
    'health',
    'docs',
    'docs/all',
    'health/system-status',
    'health/test-runs',
    'file_upload',
    'file_upload/folder',
    'folder_upload',
    'file_selection',
    'vagrant_vm',
    'vagrant_vm/templates',
    'vagrant_vm/os_templates',
    'vm_injection',
    'template_injection'
  ];

  // Add trailing slash for endpoints that need it
  const trailingSlash =
    (needsTrailingSlash.includes(cleanEndpoint) ||
     needsTrailingSlash.some(path => cleanEndpoint.startsWith(path + '/')) ||
     cleanEndpoint.endsWith('/')) &&
    !cleanEndpoint.endsWith('/') ? '/' : '';

  // Otherwise, add the API prefix
  return `${API_PREFIX}/${cleanEndpoint}${trailingSlash}`;
};

/**
 * Function to ensure an endpoint has a trailing slash if needed
 * This is used as a safety measure to guarantee correct URLs
 */
export const ensureTrailingSlash = (url: string): string => {
  if (!url) return url;

  // Don't add trailing slash to URLs with query parameters
  if (url.includes('?')) return url;

  // Don't add trailing slash to URLs that already have one
  if (url.endsWith('/')) return url;

  // Add trailing slash to API endpoints that require it
  if (url.includes('file_upload') ||
      url.includes('health') ||
      url.includes('docs')) {
    return `${url}/`;
  }

  return url;
};

/**
 * API endpoints object
 * Contains all API endpoints used in the application
 */
export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    TEST_TOKEN: getApiUrl('auth/test-token'),
    LOGIN: getApiUrl('auth/login'),
    REFRESH: getApiUrl('auth/refresh'),
  },

  // Enhanced File Management endpoints
  FILES: {
    BASE: getApiUrl('files'),
    LIST: getApiUrl('files'),
    UPLOAD: getApiUrl('files/upload'),
    BY_ID: (id: string) => getApiUrl(`files/${id}`),
    DOWNLOAD: (id: string) => getApiUrl(`files/${id}/download`),
    DELETE: (id: string) => getApiUrl(`files/${id}`),
  },

  // File upload endpoints (updated to match actual API)
  FILE_UPLOAD: {
    BASE: ensureTrailingSlash(getApiUrl('files')),
    UPLOAD: getApiUrl('files/upload'),
    BY_ID: (id: string) => getApiUrl(`files/${id}`),
    DOWNLOAD: (id: string) => getApiUrl(`files/${id}/download-url`),
  },

  // Folder upload endpoint
  FOLDER_UPLOAD: {
    BASE: ensureTrailingSlash(getApiUrl('folder_upload')),
  },

  // File selection endpoints
  FILE_SELECTION: {
    BASE: ensureTrailingSlash(getApiUrl('file_selection')),
    BY_ID: (id: string) => getApiUrl(`file_selection/${id}`),
  },

  // Enhanced VM Management endpoints
  VMS: {
    BASE: getApiUrl('vms'),
    LIST: getApiUrl('vms'),
    CREATE: getApiUrl('vms'),
    BY_ID: (id: string) => getApiUrl(`vms/${id}`),
    UPDATE: (id: string) => getApiUrl(`vms/${id}`),
    DELETE: (id: string) => getApiUrl(`vms/${id}`),
    ACTION: (id: string) => getApiUrl(`vms/${id}/action`),
    INJECT: (id: string) => getApiUrl(`vms/${id}/inject`),
    METRICS: (id: string) => getApiUrl(`vms/${id}/metrics`),
    LOGS: (id: string) => getApiUrl(`vms/${id}/logs`),
    TEMPLATES: getApiUrl('vms/templates'),
  },

  // Legacy Vagrant VM endpoints (for backward compatibility)
  VAGRANT_VM: {
    BASE: ensureTrailingSlash(getApiUrl('vagrant_vm')),
    BY_ID: (id: string) => getApiUrl(`vagrant_vm/${id}`),
    ACTION: (id: string) => getApiUrl(`vagrant_vm/${id}/action`),
    LOGS: (id: string) => getApiUrl(`vagrant_vm/${id}/logs`),
    RESOURCES: (id: string) => getApiUrl(`vagrant_vm/${id}/resources`),
    TEMPLATES: ensureTrailingSlash(getApiUrl('vagrant_vm/templates')),
    OS_TEMPLATES: ensureTrailingSlash(getApiUrl('vagrant_vm/os_templates')),
  },

  // VM injection endpoints
  VM_INJECTION: {
    // Use the consolidated API endpoints
    BASE: ensureTrailingSlash(getApiUrl('virtual-machines/injections')),
    BY_ID: (id: string) => getApiUrl(`virtual-machines/injections/${id}`),
    RETRY: (id: string) => getApiUrl(`virtual-machines/injections/${id}/retry`),
    // Keep the old endpoints for backward compatibility
    LEGACY_BASE: ensureTrailingSlash(getApiUrl('vm_injection')),
    LEGACY_BY_ID: (id: string) => getApiUrl(`vm_injection/${id}`),
    LEGACY_RETRY: (id: string) => getApiUrl(`vm_injection/${id}/retry`),
  },

  // Template injection endpoints
  TEMPLATE_INJECTION: {
    BASE: ensureTrailingSlash(getApiUrl('template_injection')),
  },

  // Logs endpoints
  LOGS: {
    UI_ERROR: getApiUrl('logs/ui-error'),
    PERFORMANCE: getApiUrl('logs/performance'),
    PERFORMANCE_SUMMARY: getApiUrl('logs/performance/summary'),
  },

  // Health endpoints
  HEALTH: {
    BASE: ensureTrailingSlash(getApiUrl('health')),
    SYSTEM_STATUS: ensureTrailingSlash(getApiUrl('health/system-status')),
    MINIO_STATUS: getApiUrl('minio_status/status'),
    VAGRANT_GRPC_STATUS: getApiUrl('health/service/vagrant-grpc'),
    TEST_RUNS: ensureTrailingSlash(getApiUrl('health/test-runs')),
  },

  // Documentation endpoints
  DOCS: {
    BASE: ensureTrailingSlash(getApiUrl('docs')),
    ALL: ensureTrailingSlash(getApiUrl('docs/all')),
  },

  // Security testing endpoints
  SECURITY_TESTING: {
    BASE: ensureTrailingSlash(getApiUrl('security_testing')),
    BY_ID: (id: string) => getApiUrl(`security_testing/${id}`),
    RUN: (id: string) => getApiUrl(`security_testing/${id}/run`),
    RESULTS: (id: string) => getApiUrl(`security_testing/${id}/results`),
  },
};

// Log API configuration for debugging
console.debug('API Configuration loaded with prefix:', API_PREFIX);
console.debug('Example endpoints:', {
  auth: API_ENDPOINTS.AUTH.TEST_TOKEN,
  fileUpload: API_ENDPOINTS.FILE_UPLOAD.BASE,
  health: API_ENDPOINTS.HEALTH.BASE
});

export default API_ENDPOINTS;