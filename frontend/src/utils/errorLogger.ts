import axios from 'axios';
import API_ENDPOINTS from './apiConfig';

interface ErrorLogPayload {
  message: string;
  source: string;
  stack?: string;
  componentName?: string;
  additionalInfo?: Record<string, any>;
  timestamp: string;
}

/**
 * Logs an error to the backend API
 * @param error The error object
 * @param source The source of the error (e.g., component name, file path)
 * @param additionalInfo Any additional information to include in the log
 */
export const logErrorToBackend = async (
  error: Error | string,
  source: string,
  additionalInfo: Record<string, any> = {}
): Promise<void> => {
  try {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const errorStack = typeof error === 'string' ? undefined : error.stack;
    
    const payload: ErrorLogPayload = {
      message: errorMessage,
      source,
      stack: errorStack,
      additionalInfo,
      timestamp: new Date().toISOString()
    };
    
    // Send the error to the backend
    await axios.post(API_ENDPOINTS.LOGS.UI_ERROR, payload);
    
    // Also log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`[${source}] ${errorMessage}`, error, additionalInfo);
    }
  } catch (loggingError) {
    // If logging fails, at least log to console
    console.error('Failed to log error to backend:', loggingError);
    console.error('Original error:', error);
  }
};

/**
 * Higher-order function to wrap component methods with error logging
 * @param fn The function to wrap
 * @param componentName The name of the component
 */
export const withErrorLogging = <T extends any[], R>(
  fn: (...args: T) => R,
  componentName: string
): ((...args: T) => R) => {
  return (...args: T) => {
    try {
      return fn(...args);
    } catch (error) {
      logErrorToBackend(error as Error, componentName);
      throw error; // Re-throw the error to not swallow it
    }
  };
};

/**
 * Creates a global error boundary for React components
 */
export const setupGlobalErrorLogging = (): void => {
  // Set up global error handler for uncaught exceptions
  window.addEventListener('error', (event) => {
    logErrorToBackend(
      event.error || event.message,
      'window.onerror',
      {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      }
    );
  });

  // Set up global error handler for unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const error = event.reason instanceof Error 
      ? event.reason 
      : new Error(String(event.reason));
    
    logErrorToBackend(
      error,
      'unhandledrejection',
      { originalReason: event.reason }
    );
  });
};

export default {
  logErrorToBackend,
  withErrorLogging,
  setupGlobalErrorLogging
}; 