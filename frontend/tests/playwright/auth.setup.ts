import { test as setup, expect } from '@playwright/test';

// Store the authentication state that can be used by all tests
setup('authenticate', async ({ page }) => {
  // Navigate to the login page or home page
  await page.goto('/');
  
  // Check if we need to log in (look for login form or already logged in state)
  const isLoggedIn = await page.evaluate(() => {
    // Check for presence of auth token in localStorage
    return !!localStorage.getItem('authToken');
  });

  if (!isLoggedIn) {
    console.log('Not logged in, attempting to get test token...');
    
    try {
      // Get a test token from the API
      const response = await fetch('http://localhost:8000/api/auth/test-token');
      const data = await response.json();
      
      if (data.access_token) {
        // Store the token in localStorage
        await page.evaluate((token) => {
          localStorage.setItem('authToken', token);
          // Also store user info if needed
          localStorage.setItem('user', JSON.stringify({
            id: '00000000-0000-0000-0000-000000000000',
            username: 'testuser',
            email: '<EMAIL>',
            is_active: true,
            is_superuser: true
          }));
        }, data.access_token);
        
        console.log('Successfully obtained and stored test token');
      } else {
        console.error('Failed to get test token:', data);
      }
    } catch (error) {
      console.error('Error getting test token:', error);
    }
    
    // Reload the page to apply the token
    await page.reload();
  } else {
    console.log('Already logged in');
  }
  
  // Verify we are authenticated
  await page.waitForTimeout(1000); // Give time for auth state to be processed
  
  // Save the authentication state to be used in tests
  await page.context().storageState({ path: 'playwright/.auth/user.json' });
}); 