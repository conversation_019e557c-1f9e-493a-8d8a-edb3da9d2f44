import { test, expect } from '@playwright/test';
import fs from 'fs';
import path from 'path';

// Comment out authentication for now
// test.use({ storageState: 'playwright/.auth/user.json' });

// Ensure screenshots directory exists
const screenshotsDir = path.resolve(process.cwd(), 'docs/screenshots');
if (!fs.existsSync(screenshotsDir)) {
  fs.mkdirSync(screenshotsDir, { recursive: true });
}

test.describe('Capture screenshots of main functionality', () => {
  // Home page
  test('Home page', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.screenshot({ path: path.join(screenshotsDir, '01-home-page.png'), fullPage: true });
  });

  // VM Status page
  test('VM Status page', async ({ page }) => {
    await page.goto('/vm-status');
    await page.waitForLoadState('networkidle');
    await page.screenshot({ path: path.join(screenshotsDir, '02-vm-status.png'), fullPage: true });
    
    // Try to capture VM details if available
    const vmListItem = page.locator('.ant-list-item').first();
    if (await vmListItem.isVisible()) {
      await vmListItem.click();
      await page.waitForTimeout(1000);
      await page.screenshot({ path: path.join(screenshotsDir, '03-vm-details.png'), fullPage: true });
      
      // Capture different tabs
      await page.locator('div[role="tab"]').filter({ hasText: 'Injections' }).click();
      await page.waitForTimeout(500);
      await page.screenshot({ path: path.join(screenshotsDir, '04-vm-injections.png'), fullPage: true });
      
      await page.locator('div[role="tab"]').filter({ hasText: 'Logs' }).click();
      await page.waitForTimeout(500);
      await page.screenshot({ path: path.join(screenshotsDir, '05-vm-logs.png'), fullPage: true });
    }
  });

  // Files page
  test('Files page', async ({ page }) => {
    await page.goto('/files');
    await page.waitForLoadState('networkidle');
    await page.screenshot({ path: path.join(screenshotsDir, '06-files.png'), fullPage: true });
  });

  // VMs page
  test('VMs page', async ({ page }) => {
    await page.goto('/vms');
    await page.waitForLoadState('networkidle');
    await page.screenshot({ path: path.join(screenshotsDir, '07-vms.png'), fullPage: true });
  });

  // Injections page
  test('Injections page', async ({ page }) => {
    await page.goto('/injections');
    await page.waitForLoadState('networkidle');
    await page.screenshot({ path: path.join(screenshotsDir, '08-injections.png'), fullPage: true });
  });

  // Documentation page
  test('Documentation page', async ({ page }) => {
    await page.goto('/docs');
    await page.waitForLoadState('networkidle');
    await page.screenshot({ path: path.join(screenshotsDir, '09-docs.png'), fullPage: true });
  });

  // Language switcher functionality
  test('Language switcher', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Find and click the language switcher
    const languageSwitcher = page.locator('.ant-select').filter({ hasText: 'English' });
    if (await languageSwitcher.isVisible()) {
      await languageSwitcher.click();
      await page.waitForTimeout(500);
      await page.screenshot({ path: path.join(screenshotsDir, '10-language-switcher-open.png') });
      
      // Select German
      await page.locator('.ant-select-item-option').filter({ hasText: 'Deutsch' }).click();
      await page.waitForTimeout(1000);
      await page.screenshot({ path: path.join(screenshotsDir, '11-german-language.png'), fullPage: true });
      
      // Go to VM Status page to see translations
      await page.goto('/vm-status');
      await page.waitForLoadState('networkidle');
      await page.screenshot({ path: path.join(screenshotsDir, '12-vm-status-german.png'), fullPage: true });
      
      // Switch back to English
      await languageSwitcher.click();
      await page.waitForTimeout(500);
      await page.locator('.ant-select-item-option').filter({ hasText: 'English' }).click();
      await page.waitForTimeout(1000);
    }
  });
}); 