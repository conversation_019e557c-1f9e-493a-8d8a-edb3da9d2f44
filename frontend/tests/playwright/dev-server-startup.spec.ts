import { test, expect } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import * as child_process from 'child_process';
import { promisify } from 'util';

const exec = promisify(child_process.exec);

/**
 * This test suite verifies that the development server can be started and
 * basic file upload functionality works.
 * 
 * Note: This test suite requires additional setup and likely needs to be
 * run separately from other tests, as it involves starting the actual server.
 */
test.describe('Development Server', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  // Create a temporary test file
  let tempDir: string;
  let testFilePath: string;
  let serverProcess: child_process.ChildProcess | null = null;
  const serverUrl = 'http://localhost:3000'; // Assuming the dev server runs on port 3000
  
  test.beforeAll(async () => {
    // Create a temporary directory for test files
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'dev-server-test-'));
    
    // Create a test file for upload
    testFilePath = path.join(tempDir, 'dev-server-test-file.txt');
    fs.writeFileSync(testFilePath, 'This is a test file for the development server');
    
    // Attempt to start the dev server
    // Note: This is a simplified example. Real implementation may need adjustment
    // based on your specific project structure and startup script
    console.log('Starting development server...');
    try {
      // Check if there's a dev script in package.json
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      if (packageJson.scripts && packageJson.scripts.dev) {
        // Start the server using npm run dev
        serverProcess = child_process.spawn('npm', ['run', 'dev'], {
          detached: true,
          stdio: 'pipe'
        });
        
        // Log server output for debugging
        if (serverProcess.stdout) {
          serverProcess.stdout.on('data', (data) => {
            console.log(`Server stdout: ${data}`);
          });
        }
        
        if (serverProcess.stderr) {
          serverProcess.stderr.on('data', (data) => {
            console.error(`Server stderr: ${data}`);
          });
        }
        
        // Wait for the server to start
        console.log('Waiting for server to start...');
        
        // A simple approach to wait for server to be ready - wait 10 seconds
        // In a real test, you would want to poll the server until it responds
        await new Promise(resolve => setTimeout(resolve, 10000));
      } else {
        console.log('No dev script found in package.json, checking for dev.sh');
        
        // Check if there's a dev.sh script
        if (fs.existsSync('dev.sh')) {
          serverProcess = child_process.spawn('./dev.sh', [], {
            detached: true,
            stdio: 'pipe',
            shell: true
          });
          
          // Log server output for debugging
          if (serverProcess.stdout) {
            serverProcess.stdout.on('data', (data) => {
              console.log(`Server stdout: ${data}`);
            });
          }
          
          if (serverProcess.stderr) {
            serverProcess.stderr.on('data', (data) => {
              console.error(`Server stderr: ${data}`);
            });
          }
          
          // Wait for the server to start
          console.log('Waiting for server to start...');
          
          // Wait 15 seconds for server to be ready
          await new Promise(resolve => setTimeout(resolve, 15000));
        } else {
          throw new Error('Could not find a way to start the development server');
        }
      }
      
      // Verify server is running by making a request
      console.log('Verifying server is running...');
      let serverRunning = false;
      for (let i = 0; i < 5; i++) {
        try {
          // Use built-in http module to make a simple request
          const http = require('http');
          await new Promise<void>((resolve, reject) => {
            const req = http.get(serverUrl, (res: any) => {
              if (res.statusCode === 200) {
                serverRunning = true;
                resolve();
              } else {
                reject(new Error(`Server responded with status code ${res.statusCode}`));
              }
            });
            req.on('error', reject);
            req.end();
          });
          break;
        } catch (e) {
          console.log(`Server not ready yet (attempt ${i + 1}/5), waiting...`);
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
      
      if (!serverRunning) {
        throw new Error('Server did not start properly');
      }
      
      console.log('Development server started successfully');
    } catch (e) {
      console.error(`Failed to start development server: ${e}`);
      throw e;
    }
  });
  
  test.afterAll(async () => {
    // Clean up temporary files and directories
    fs.rmSync(tempDir, { recursive: true, force: true });
    
    // Stop the server if it was started
    if (serverProcess) {
      console.log('Stopping development server...');
      try {
        // Kill the process and all its children
        if (process.platform === 'win32') {
          // On Windows, use taskkill to kill the process tree
          if (serverProcess.pid) {
            await exec(`taskkill /pid ${serverProcess.pid} /T /F`);
          }
        } else {
          // On Unix-like systems, kill the process group
          if (serverProcess.pid) {
            process.kill(-serverProcess.pid, 'SIGINT');
          }
        }
      } catch (e) {
        console.error(`Error stopping server: ${e}`);
      }
    }
  });
  
  test('should load the file upload page', async ({ page }) => {
    console.log('Testing file upload page loading...');
    // Navigate to the file upload page on the development server
    await page.goto(`${serverUrl}/file_upload`);
    
    // Verify the page loaded successfully
    const title = await page.title();
    console.log(`Page title: ${title}`);
    
    // Check for the expected elements
    await expect(page.locator('.file-upload-container')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('button:has-text("Upload File")')).toBeVisible();
    
    // Take a screenshot for visual verification
    await page.screenshot({ path: path.join(tempDir, 'file-upload-page.png') });
    
    console.log('File upload page loaded successfully');
  });
  
  test('should upload a file successfully', async ({ page }) => {
    console.log('Testing file upload functionality...');
    // Navigate to the file upload page
    await page.goto(`${serverUrl}/file_upload`);
    
    // Wait for the page to load
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
    
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(testFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Development server test');
    
    // Take a screenshot before submitting
    await page.screenshot({ path: path.join(tempDir, 'before-upload.png') });
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Wait for the upload to complete
    await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 15000 });
    
    // Verify the file appears in the table
    await page.waitForSelector('.file-upload-table', { timeout: 10000 });
    const tableContent = await page.locator('.file-upload-table').textContent();
    expect(tableContent?.includes('dev-server-test-file.txt')).toBeTruthy();
    
    // Take a screenshot after upload
    await page.screenshot({ path: path.join(tempDir, 'after-upload.png') });
    
    console.log('File upload test completed successfully');
  });
  
  test('should download the uploaded file', async ({ page }) => {
    console.log('Testing file download functionality...');
    // Navigate to the file upload page
    await page.goto(`${serverUrl}/file_upload`);
    
    // Wait for the page to load
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
    
    // Look for the file we uploaded in the previous test
    await page.waitForSelector('text=dev-server-test-file.txt', { timeout: 10000 });
    
    // Find and click the download button for our file
    // This depends on the actual UI structure, so may need adjustment
    const downloadPromise = page.waitForEvent('download');
    
    // Look for a download button in the row containing our filename
    const row = page.locator('tr:has-text("dev-server-test-file.txt")');
    const downloadButton = row.locator('button[aria-label="download"]');
    await downloadButton.click();
    
    // Wait for the download to start
    const download = await downloadPromise;
    
    // Wait for the download to complete
    const downloadPath = path.join(tempDir, 'downloaded-file.txt');
    await download.saveAs(downloadPath);
    
    // Verify the downloaded file exists and has the expected content
    expect(fs.existsSync(downloadPath)).toBeTruthy();
    const fileContent = fs.readFileSync(downloadPath, 'utf8');
    expect(fileContent).toContain('This is a test file for the development server');
    
    console.log('File download test completed successfully');
  });
  
  test('should delete the uploaded file', async ({ page }) => {
    console.log('Testing file deletion functionality...');
    // Navigate to the file upload page
    await page.goto(`${serverUrl}/file_upload`);
    
    // Wait for the page to load
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
    
    // Look for the file we uploaded in the previous test
    await page.waitForSelector('text=dev-server-test-file.txt', { timeout: 10000 });
    
    // Find and click the delete button for our file
    // This depends on the actual UI structure, so may need adjustment
    const row = page.locator('tr:has-text("dev-server-test-file.txt")');
    const deleteButton = row.locator('button[aria-label="delete"]');
    
    // Take a screenshot before deletion
    await page.screenshot({ path: path.join(tempDir, 'before-delete.png') });
    
    // Click the delete button
    await deleteButton.click();
    
    // Confirm deletion if a confirmation dialog appears
    try {
      await page.click('button:has-text("Yes")');
    } catch (e) {
      // Ignore if there's no confirmation dialog
    }
    
    // Wait for success message
    await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 5000 });
    
    // Verify the file no longer appears in the table
    await expect(page.locator('text=dev-server-test-file.txt')).not.toBeVisible({ timeout: 5000 });
    
    // Take a screenshot after deletion
    await page.screenshot({ path: path.join(tempDir, 'after-delete.png') });
    
    console.log('File deletion test completed successfully');
  });
  
  test('should display API endpoints and status', async ({ page }) => {
    console.log('Testing API status page...');
    // Navigate to the API status page or a similar page that displays API information
    // This depends on your application structure and may need adjustment
    
    try {
      await page.goto(`${serverUrl}/api/status`);
      
      // Check if the page loads and contains API information
      const content = await page.content();
      expect(content).toContain('API');
      
      // Take a screenshot for reference
      await page.screenshot({ path: path.join(tempDir, 'api-status.png') });
      
      console.log('API status page loaded successfully');
    } catch (e) {
      console.log('Could not find API status page, this test is optional');
    }
  });
}); 