import { test, expect } from '@playwright/test';

test.describe('Docs Page UI', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the docs page
    await page.goto('/docs');
    
    // Wait for the page to load
    await page.waitForSelector('.docs-page-container', { timeout: 10000 });
  });
  
  test('should display the docs page with all components', async ({ page }) => {
    // Check if the page title is visible
    await expect(page.locator('h3:has-text("API Documentation")')).toBeVisible();
    
    // Check if the back button is visible
    await expect(page.locator('button:has-text("Back to Main")')).toBeVisible();
    
    // Check if the dark mode toggle is visible
    await expect(page.locator('.ant-switch')).toBeVisible();
    
    // Check if the iframe is visible
    await expect(page.locator('iframe#docs-frame')).toBeVisible();
  });
  
  test('should toggle dark mode', async ({ page }) => {
    // Get the initial state of the switch
    const initialChecked = await page.locator('.ant-switch').isChecked();
    
    // Click the switch to toggle dark mode
    await page.click('.ant-switch');
    
    // Check if the switch state has changed
    await expect(page.locator('.ant-switch')).toHaveAttribute('aria-checked', (!initialChecked).toString());
    
    // Check if the text changes
    if (initialChecked) {
      await expect(page.locator('span:has-text("Light Mode")')).toBeVisible();
    } else {
      await expect(page.locator('span:has-text("Dark Mode")')).toBeVisible();
    }
    
    // Toggle back
    await page.click('.ant-switch');
    
    // Check if the switch state has changed back
    await expect(page.locator('.ant-switch')).toHaveAttribute('aria-checked', initialChecked.toString());
    
    // Check if the text changes back
    if (initialChecked) {
      await expect(page.locator('span:has-text("Dark Mode")')).toBeVisible();
    } else {
      await expect(page.locator('span:has-text("Light Mode")')).toBeVisible();
    }
  });
  
  test('should navigate back to main page', async ({ page }) => {
    // Click the back button
    await page.click('button:has-text("Back to Main")');
    
    // Check if we're redirected to the main page
    await page.waitForSelector('.main-page-container', { timeout: 10000 });
    await expect(page.locator('h2:has-text("TurdParty File Injection")')).toBeVisible();
  });
  
  test('should load the API docs in the iframe', async ({ page }) => {
    // Check if the iframe has loaded
    const iframe = page.frameLocator('#docs-frame');
    
    // Wait for the iframe to load content
    await expect(iframe.locator('body')).toBeVisible({ timeout: 20000 });
    
    // This is a basic check - we can't easily check the content of the iframe
    // if it's from a different origin due to browser security restrictions
  });
}); 