import { test, expect } from '@playwright/test';

test.describe('File Selection UI', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the file selection page
    await page.goto('/file_selection');
    
    // Wait for the page to load
    await page.waitForSelector('.file-selection-container', { timeout: 10000 });
  });
  
  test('should display the file selection page', async ({ page }) => {
    // Check if the page title is visible
    await expect(page.locator('h1:has-text("File Selection")')).toBeVisible();
    
    // Check if the create button is visible
    await expect(page.locator('button:has-text("Create New")')).toBeVisible();
    
    // Check if the file selection table is visible
    await expect(page.locator('.file-selection-table')).toBeVisible();
  });
  
  test('should open create modal when clicking create button', async ({ page }) => {
    // Click the create button
    await page.click('button:has-text("Create New")');
    
    // Check if the modal is visible
    await expect(page.locator('.ant-modal-title:has-text("Create File Selection")')).toBeVisible();
    
    // Check if the form elements are visible
    await expect(page.locator('input[placeholder="Enter a name"]')).toBeVisible();
    await expect(page.locator('textarea[placeholder="Enter a description"]')).toBeVisible();
    await expect(page.locator('.ant-select')).toBeVisible(); // File selection dropdown
    await expect(page.locator('input[placeholder="Enter target path"]')).toBeVisible();
    await expect(page.locator('input[placeholder="Enter file permissions"]')).toBeVisible();
    
    // Close the modal
    await page.click('button:has-text("Cancel")');
  });
  
  test('should create a new file selection', async ({ page }) => {
    // First, ensure we have at least one file uploaded
    await page.goto('/file_upload');
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
    
    // Check if we have files, if not, upload one
    const fileCount = await page.locator('.ant-table-row').count();
    if (fileCount === 0) {
      // Upload a file first
      await page.click('button:has-text("Upload File")');
      await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
      
      const fileChooserPromise = page.waitForEvent('filechooser');
      await page.click('.ant-upload');
      const fileChooser = await fileChooserPromise;
      await fileChooser.setFiles({
        name: 'test-file-for-selection.txt',
        mimeType: 'text/plain',
        buffer: Buffer.from('This is a test file for selection testing'),
      });
      
      await page.fill('textarea[placeholder="Enter file description"]', 'Test file for selection');
      await page.click('button:has-text("Upload")');
      await expect(page.locator('.ant-modal-title:has-text("Upload File")')).not.toBeVisible({ timeout: 10000 });
    }
    
    // Now go to file selection page
    await page.goto('/file_selection');
    await page.waitForSelector('.file-selection-container', { timeout: 10000 });
    
    // Click the create button
    await page.click('button:has-text("Create New")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Create File Selection")')).toBeVisible();
    
    // Fill in the form
    await page.fill('input[placeholder="Enter a name"]', 'Test File Selection');
    await page.fill('textarea[placeholder="Enter a description"]', 'This is a test file selection');
    
    // Select a file from the dropdown
    await page.click('.ant-select');
    await page.waitForSelector('.ant-select-dropdown');
    await page.click('.ant-select-item');
    
    // Fill in target path and permissions
    await page.fill('input[placeholder="Enter target path"]', '/tmp/test-file.txt');
    await page.fill('input[placeholder="Enter file permissions"]', '0644');
    
    // Submit the form
    await page.click('button:has-text("Create")');
    
    // Wait for the creation to complete and modal to close
    await expect(page.locator('.ant-modal-title:has-text("Create File Selection")')).not.toBeVisible({ timeout: 10000 });
    
    // Check if success message appears
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Check if the file selection appears in the table
    await expect(page.locator('td:has-text("Test File Selection")')).toBeVisible({ timeout: 10000 });
  });
  
  test('should edit a file selection', async ({ page }) => {
    // Find the first edit button in the table
    const editButton = page.locator('.ant-table-row').first().locator('button[aria-label="Edit file selection"]');
    
    // Check if the button exists
    await expect(editButton).toBeVisible();
    
    // Click the edit button
    await editButton.click();
    
    // Check if the modal is visible
    await expect(page.locator('.ant-modal-title:has-text("Edit File Selection")')).toBeVisible();
    
    // Update the description
    await page.fill('textarea[placeholder="Enter a description"]', 'Updated description for testing');
    
    // Submit the form
    await page.click('button:has-text("Update")');
    
    // Wait for the update to complete and modal to close
    await expect(page.locator('.ant-modal-title:has-text("Edit File Selection")')).not.toBeVisible({ timeout: 10000 });
    
    // Check if success message appears
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Check if the updated description appears in the table
    await expect(page.locator('td:has-text("Updated description for testing")')).toBeVisible({ timeout: 10000 });
  });
  
  test('should delete a file selection', async ({ page }) => {
    // Count the number of rows before deletion
    const initialRowCount = await page.locator('.ant-table-row').count();
    
    // Find the first delete button in the table
    const deleteButton = page.locator('.ant-table-row').first().locator('button[aria-label="Delete file selection"]');
    
    // Check if the button exists
    await expect(deleteButton).toBeVisible();
    
    // Click the delete button
    await deleteButton.click();
    
    // Confirm deletion in the modal
    await page.click('button:has-text("Yes")');
    
    // Check if success message appears
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Verify row was deleted
    await expect(page.locator('.ant-table-row')).toHaveCount(initialRowCount - 1);
  });
}); 