import { test, expect } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

/**
 * This test suite focuses on accessibility and usability aspects of the file upload component
 */
test.describe('File Upload Accessibility and Usability', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  // Create a temporary test file
  let tempDir: string;
  let testFilePath: string;
  
  test.beforeAll(async () => {
    // Create a temporary directory for test files
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'upload-a11y-test-'));
    
    // Create a test file
    testFilePath = path.join(tempDir, 'accessibility-test.txt');
    fs.writeFileSync(testFilePath, 'This is a test file for accessibility testing');
  });
  
  test.afterAll(async () => {
    // Clean up temporary files and directories
    fs.rmSync(tempDir, { recursive: true, force: true });
  });
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the file upload page
    await page.goto('/file_upload');
    
    // Wait for the page to load
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
  });
  
  test('should have proper heading structure', async ({ page }) => {
    // Check if the page has a proper h1 heading
    await expect(page.locator('h1')).toBeVisible();
    
    // Check if the heading text is descriptive
    const headingText = await page.locator('h1').textContent();
    expect(headingText?.toLowerCase()).toContain('file');
    expect(headingText?.toLowerCase()).toContain('upload');
  });
  
  test('should have accessible button labels', async ({ page }) => {
    // Check if the upload button has a descriptive label
    const uploadButton = page.locator('button:has-text("Upload File")');
    await expect(uploadButton).toBeVisible();
    
    // Check if the button has an accessible name
    const buttonText = await uploadButton.textContent();
    expect(buttonText).toBeTruthy();
    expect(buttonText?.trim().length).toBeGreaterThan(0);
    
    // Check other action buttons in the table
    const actionButtons = page.locator('button[aria-label]');
    const count = await actionButtons.count();
    
    for (let i = 0; i < count; i++) {
      const button = actionButtons.nth(i);
      const ariaLabel = await button.getAttribute('aria-label');
      expect(ariaLabel).toBeTruthy();
      expect(ariaLabel?.trim().length).toBeGreaterThan(0);
    }
  });
  
  test('should have keyboard navigable interface', async ({ page }) => {
    // Test keyboard navigation to the upload button
    await page.keyboard.press('Tab');
    
    // Keep pressing Tab until we reach the upload button or hit a reasonable limit
    let tabCount = 1;
    const maxTabs = 20;
    let uploadButtonFocused = false;
    
    while (tabCount < maxTabs) {
      const focusedElement = await page.evaluate(() => document.activeElement?.textContent);
      if (focusedElement?.includes('Upload File')) {
        uploadButtonFocused = true;
        break;
      }
      await page.keyboard.press('Tab');
      tabCount++;
    }
    
    expect(uploadButtonFocused).toBe(true);
    
    // Press Enter to open the upload modal
    await page.keyboard.press('Enter');
    
    // Check if the modal is visible
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Test keyboard navigation within the modal
    await page.keyboard.press('Tab');
    
    // Verify we can reach the cancel button with keyboard
    let cancelButtonFocused = false;
    tabCount = 1;
    
    while (tabCount < maxTabs) {
      const focusedElement = await page.evaluate(() => document.activeElement?.textContent);
      if (focusedElement?.includes('Cancel')) {
        cancelButtonFocused = true;
        break;
      }
      await page.keyboard.press('Tab');
      tabCount++;
    }
    
    expect(cancelButtonFocused).toBe(true);
    
    // Press Enter to close the modal
    await page.keyboard.press('Enter');
    
    // Check if the modal is closed
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).not.toBeVisible();
  });
  
  test('should have visible focus indicators', async ({ page }) => {
    // Tab to the upload button
    await page.keyboard.press('Tab');
    
    // Keep pressing Tab until we reach the upload button
    let tabCount = 1;
    const maxTabs = 20;
    
    while (tabCount < maxTabs) {
      const focusedElement = await page.evaluate(() => document.activeElement?.textContent);
      if (focusedElement?.includes('Upload File')) {
        break;
      }
      await page.keyboard.press('Tab');
      tabCount++;
    }
    
    // Take a screenshot to verify focus indicator is visible
    await page.screenshot({ path: path.join(tempDir, 'focus-indicator.png') });
    
    // Check if the focused element has a visible focus indicator
    // This is a visual check, so we can't automate it completely
    // But we can check for common focus indicator CSS properties
    const hasFocusStyle = await page.evaluate(() => {
      const focusedElement = document.activeElement;
      if (!focusedElement) return false;
      
      const style = window.getComputedStyle(focusedElement);
      return style.outlineWidth !== '0px' || 
             style.boxShadow !== 'none' ||
             style.borderColor !== window.getComputedStyle(document.body).borderColor;
    });
    
    expect(hasFocusStyle).toBe(true);
  });
  
  test('should provide feedback during upload process', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(testFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Accessibility test');
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Check for visual feedback during upload (progress indicator, spinner, etc.)
    try {
      // Look for common loading indicators
      const hasLoadingIndicator = await page.locator('.ant-spin, .ant-progress, [role="progressbar"]').isVisible({ timeout: 2000 });
      
      if (!hasLoadingIndicator) {
        // If no dedicated loading component, check for disabled submit button
        const isSubmitDisabled = await page.locator('button:has-text("Upload")').isDisabled();
        expect(isSubmitDisabled).toBe(true);
      }
    } catch (e) {
      // If we can't find a loading indicator, the upload might be too fast
      // In that case, check for success message
      await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 5000 });
    }
    
    // Wait for the upload to complete
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).not.toBeVisible({ timeout: 10000 });
    
    // Check for success message
    await expect(page.locator('.ant-message-success')).toBeVisible();
  });
  
  test('should have responsive design', async ({ page }) => {
    // Test different viewport sizes
    const viewportSizes = [
      { width: 1920, height: 1080 }, // Desktop
      { width: 1024, height: 768 },  // Tablet landscape
      { width: 768, height: 1024 },  // Tablet portrait
      { width: 375, height: 667 }    // Mobile
    ];
    
    for (const size of viewportSizes) {
      // Set viewport size
      await page.setViewportSize(size);
      
      // Take a screenshot for visual verification
      await page.screenshot({ 
        path: path.join(tempDir, `responsive-${size.width}x${size.height}.png`),
        fullPage: true
      });
      
      // Check if the upload button is visible at all viewport sizes
      await expect(page.locator('button:has-text("Upload File")')).toBeVisible();
      
      // Check if the file table is visible
      await expect(page.locator('.file-upload-table')).toBeVisible();
    }
  });
  
  test('should handle drag and drop file upload if supported', async ({ page }) => {
    // Check if drag and drop is supported
    const hasDragDropSupport = await page.evaluate(() => {
      const div = document.createElement('div');
      return ('draggable' in div || ('ondragstart' in div && 'ondrop' in div));
    });
    
    if (!hasDragDropSupport) {
      console.log('Drag and drop not supported in this browser environment');
      return;
    }
    
    // Look for a drop zone element
    const dropZoneVisible = await page.locator('.ant-upload-drag, [data-testid="drop-zone"]').isVisible();
    
    if (!dropZoneVisible) {
      console.log('No visible drop zone found, skipping drag and drop test');
      return;
    }
    
    // Test drag and drop functionality
    // Note: This is difficult to fully automate with Playwright
    // We can simulate the drop event using JavaScript
    await page.evaluate(() => {
      const dropZone = document.querySelector('.ant-upload-drag') || document.querySelector('[data-testid="drop-zone"]');
      if (!dropZone) return;
      
      // Create a mock file
      const mockFile = new File(['test content'], 'test-drag-drop.txt', { type: 'text/plain' });
      
      // Create a mock drop event
      const mockDataTransfer = new DataTransfer();
      mockDataTransfer.items.add(mockFile);
      
      // Dispatch drop event
      const dropEvent = new DragEvent('drop', { dataTransfer: mockDataTransfer });
      dropZone.dispatchEvent(dropEvent);
    });
    
    // Check if the file was accepted
    // This depends on the implementation, but we can check for common patterns
    try {
      // Check for file name in the UI
      await expect(page.locator('text=test-drag-drop.txt')).toBeVisible({ timeout: 5000 });
    } catch (e) {
      console.log('Could not verify drag and drop result, UI might not update as expected');
    }
  });
}); 