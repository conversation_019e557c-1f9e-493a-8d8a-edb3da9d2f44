import { test, expect, request } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

/**
 * This test suite is specifically designed to debug file upload API issues
 * It tests both the old and new API paths to identify any inconsistencies
 */
test.describe('File Upload API Debugging', () => {
  // Create temporary test files
  let tempDir: string;
  let testFilePath: string;
  let token: string;
  
  test.beforeAll(async ({ request }) => {
    console.log('Setting up test files and getting auth token...');
    
    // Create a temporary directory for test files
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'api-debug-'));
    console.log(`Created temp directory: ${tempDir}`);
    
    // Create a test file
    testFilePath = path.join(tempDir, 'api-test-file.txt');
    fs.writeFileSync(testFilePath, 'This is a test file for debugging API upload issues');
    console.log(`Created test file: ${testFilePath}`);
    
    // Get auth token
    const response = await request.post('/api/v1/auth/test-token');
    const data = await response.json();
    token = data.access_token;
    console.log('Obtained auth token');
  });
  
  test.afterAll(async () => {
    // Clean up temporary files and directories
    console.log('Cleaning up test files...');
    fs.rmSync(tempDir, { recursive: true, force: true });
    console.log('Cleanup complete');
  });
  
  test('should test file upload with new API path', async ({ request }) => {
    console.log('Testing file upload with new API path (/api/v1/file_upload)...');
    
    // Create FormData
    const fileContent = fs.readFileSync(testFilePath);
    const fileName = path.basename(testFilePath);
    
    // Log request details
    console.log(`Uploading file: ${fileName}`);
    console.log(`File size: ${fileContent.length} bytes`);
    
    // Create multipart form data
    const formData = new FormData();
    const blob = new Blob([fileContent], { type: 'text/plain' });
    formData.append('file', blob, fileName);
    formData.append('description', 'Test file upload with new API path');
    
    // Make the request
    const response = await request.post('/api/v1/file_upload/', {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      multipart: {
        file: {
          name: fileName,
          mimeType: 'text/plain',
          buffer: fileContent,
        },
        description: 'Test file upload with new API path',
      }
    });
    
    // Log response details
    console.log(`Response status: ${response.status()}`);
    
    try {
      const responseBody = await response.json();
      console.log('Response body:', responseBody);
      
      // Assert response
      expect(response.status()).toBe(201);
      expect(responseBody.filename).toBe(fileName);
      expect(responseBody.description).toBe('Test file upload with new API path');
      
      return responseBody.id; // Return the file ID for use in other tests
    } catch (error) {
      console.error('Error parsing response:', error);
      console.log('Response text:', await response.text());
      throw error;
    }
  });
  
  test('should test file upload with old API path', async ({ request }) => {
    console.log('Testing file upload with old API path (/api/file_upload)...');
    
    // Create FormData
    const fileContent = fs.readFileSync(testFilePath);
    const fileName = path.basename(testFilePath);
    
    // Log request details
    console.log(`Uploading file: ${fileName}`);
    console.log(`File size: ${fileContent.length} bytes`);
    
    // Make the request to the old API path
    const response = await request.post('/api/file_upload/', {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      multipart: {
        file: {
          name: fileName,
          mimeType: 'text/plain',
          buffer: fileContent,
        },
        description: 'Test file upload with old API path',
      }
    });
    
    // Log response details
    console.log(`Response status: ${response.status()}`);
    
    try {
      // If the response is a redirect (307), log the redirect URL
      if (response.status() === 307) {
        const redirectUrl = response.headers()['location'];
        console.log(`Redirect URL: ${redirectUrl}`);
        
        // Follow the redirect manually
        console.log('Following redirect...');
        const redirectResponse = await request.post(redirectUrl, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
          multipart: {
            file: {
              name: fileName,
              mimeType: 'text/plain',
              buffer: fileContent,
            },
            description: 'Test file upload with old API path (redirected)',
          }
        });
        
        console.log(`Redirect response status: ${redirectResponse.status()}`);
        const redirectResponseBody = await redirectResponse.json();
        console.log('Redirect response body:', redirectResponseBody);
        
        // Assert redirect response
        expect(redirectResponse.status()).toBe(201);
        expect(redirectResponseBody.filename).toBe(fileName);
        
        return redirectResponseBody.id;
      } else {
        // If not a redirect, parse the response body
        const responseBody = await response.json();
        console.log('Response body:', responseBody);
        
        // Assert response
        expect(response.status()).toBe(201);
        expect(responseBody.filename).toBe(fileName);
        expect(responseBody.description).toBe('Test file upload with old API path');
        
        return responseBody.id;
      }
    } catch (error) {
      console.error('Error parsing response:', error);
      console.log('Response text:', await response.text());
      throw error;
    }
  });
  
  test('should test file download with new API path', async ({ request }) => {
    // First upload a file
    const fileId = await test.step('Upload file', async () => {
      return await test('should test file upload with new API path', async ({ request }) => {
        // Implementation is the same as the test above
        const fileContent = fs.readFileSync(testFilePath);
        const fileName = path.basename(testFilePath);
        
        const response = await request.post('/api/v1/file_upload/', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
          multipart: {
            file: {
              name: fileName,
              mimeType: 'text/plain',
              buffer: fileContent,
            },
            description: 'Test file for download',
          }
        });
        
        const responseBody = await response.json();
        return responseBody.id;
      });
    });
    
    console.log(`Testing file download with new API path for file ID: ${fileId}`);
    
    // Download the file
    const response = await request.get(`/api/v1/file_upload/download/${fileId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      }
    });
    
    // Log response details
    console.log(`Response status: ${response.status()}`);
    console.log(`Content-Type: ${response.headers()['content-type']}`);
    console.log(`Content-Disposition: ${response.headers()['content-disposition']}`);
    
    // Assert response
    expect(response.status()).toBe(200);
    
    // Verify file content
    const downloadedContent = await response.body();
    const originalContent = fs.readFileSync(testFilePath);
    
    expect(Buffer.compare(downloadedContent, originalContent)).toBe(0);
  });
  
  test('should test file download with old API path', async ({ request }) => {
    // First upload a file
    const fileId = await test.step('Upload file', async () => {
      return await test('should test file upload with new API path', async ({ request }) => {
        // Implementation is the same as the test above
        const fileContent = fs.readFileSync(testFilePath);
        const fileName = path.basename(testFilePath);
        
        const response = await request.post('/api/v1/file_upload/', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
          multipart: {
            file: {
              name: fileName,
              mimeType: 'text/plain',
              buffer: fileContent,
            },
            description: 'Test file for download with old path',
          }
        });
        
        const responseBody = await response.json();
        return responseBody.id;
      });
    });
    
    console.log(`Testing file download with old API path for file ID: ${fileId}`);
    
    // Download the file using the old API path
    const response = await request.get(`/api/file_upload/download/${fileId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      }
    });
    
    // Log response details
    console.log(`Response status: ${response.status()}`);
    
    // If the response is a redirect (307), log the redirect URL
    if (response.status() === 307) {
      const redirectUrl = response.headers()['location'];
      console.log(`Redirect URL: ${redirectUrl}`);
      
      // Follow the redirect manually
      console.log('Following redirect...');
      const redirectResponse = await request.get(redirectUrl, {
        headers: {
          'Authorization': `Bearer ${token}`,
        }
      });
      
      console.log(`Redirect response status: ${redirectResponse.status()}`);
      
      // Assert redirect response
      expect(redirectResponse.status()).toBe(200);
      
      // Verify file content
      const downloadedContent = await redirectResponse.body();
      const originalContent = fs.readFileSync(testFilePath);
      
      expect(Buffer.compare(downloadedContent, originalContent)).toBe(0);
    } else {
      // If not a redirect, verify the response directly
      expect(response.status()).toBe(200);
      
      // Verify file content
      const downloadedContent = await response.body();
      const originalContent = fs.readFileSync(testFilePath);
      
      expect(Buffer.compare(downloadedContent, originalContent)).toBe(0);
    }
  });
}); 