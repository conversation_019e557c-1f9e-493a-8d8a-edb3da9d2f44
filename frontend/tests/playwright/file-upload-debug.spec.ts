import { test, expect, Page } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

/**
 * This test suite is specifically designed to debug file upload issues
 * It includes detailed logging, network monitoring, and step-by-step verification
 */
test.describe('File Upload Debugging', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  // Create temporary test files
  let tempDir: string;
  let testFilePath: string;
  let largeFilePath: string;
  
  test.beforeAll(async () => {
    console.log('Setting up test files...');
    
    // Create a temporary directory for test files
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'upload-debug-'));
    console.log(`Created temp directory: ${tempDir}`);
    
    // Create a small test file
    testFilePath = path.join(tempDir, 'test-file.txt');
    fs.writeFileSync(testFilePath, 'This is a test file for debugging upload issues');
    console.log(`Created test file: ${testFilePath}`);
    
    // Create a larger test file (1MB)
    largeFilePath = path.join(tempDir, 'large-file.bin');
    const buffer = Buffer.alloc(1024 * 1024); // 1MB of zeros
    fs.writeFileSync(largeFilePath, buffer);
    console.log(`Created large test file: ${largeFilePath}`);
  });
  
  test.afterAll(async () => {
    // Clean up temporary files and directories
    console.log('Cleaning up test files...');
    fs.rmSync(tempDir, { recursive: true, force: true });
    console.log('Cleanup complete');
  });
  
  /**
   * Helper function to log network requests
   */
  async function setupNetworkLogging(page: Page) {
    // Log all network requests
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        console.log(`>> Request: ${request.method()} ${request.url()}`);
        if (request.postData()) {
          console.log(`   Data: ${request.postData()?.substring(0, 100)}...`);
        }
      }
    });
    
    // Log all network responses
    page.on('response', async response => {
      if (response.url().includes('/api/')) {
        const status = response.status();
        console.log(`<< Response: ${status} ${response.url()}`);
        
        if (status >= 400) {
          try {
            const text = await response.text();
            console.log(`   Error: ${text.substring(0, 200)}`);
          } catch (e) {
            console.log(`   Error: Could not get response text - ${e}`);
          }
        }
      }
    });
  }
  
  test('should debug single file upload with network monitoring', async ({ page }) => {
    // Set up network logging
    await setupNetworkLogging(page);
    
    console.log('Navigating to file upload page...');
    await page.goto('/file_upload');
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
    console.log('File upload page loaded');
    
    // Check current API endpoints being used
    const requests: string[] = [];
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        requests.push(`${request.method()} ${request.url()}`);
      }
    });
    
    // Wait for initial data load
    await page.waitForTimeout(2000);
    console.log('Initial API requests:', requests);
    
    // Click the upload button
    console.log('Opening upload modal...');
    await page.click('button:has-text("Upload File")');
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    console.log('Upload modal opened');
    
    // Set file input
    console.log('Selecting file...');
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(testFilePath);
    console.log('File selected');
    
    // Add description
    const description = 'Debug test file upload';
    await page.fill('textarea[placeholder="Enter file description"]', description);
    console.log('Description added');
    
    // Intercept the form submission to log the exact request
    page.once('request', request => {
      if (request.url().includes('/file_upload')) {
        console.log('Upload request details:');
        console.log(`URL: ${request.url()}`);
        console.log(`Method: ${request.method()}`);
        console.log(`Headers: ${JSON.stringify(request.headers())}`);
      }
    });
    
    // Submit the form
    console.log('Submitting upload form...');
    await page.click('button:has-text("Upload")');
    
    // Wait for the upload to complete or fail
    try {
      await expect(page.locator('.ant-modal-title:has-text("Upload File")')).not.toBeVisible({ timeout: 10000 });
      console.log('Upload modal closed');
      
      // Check for success message
      const successVisible = await page.locator('.ant-message-success').isVisible();
      if (successVisible) {
        console.log('Success message displayed');
      } else {
        console.log('No success message displayed');
        
        // Check for error message
        const errorVisible = await page.locator('.ant-message-error').isVisible();
        if (errorVisible) {
          const errorText = await page.locator('.ant-message-error').textContent();
          console.log(`Error message: ${errorText}`);
        }
      }
      
      // Check if the file appears in the table
      const fileName = path.basename(testFilePath);
      const fileVisible = await page.locator(`td:has-text("${fileName}")`).isVisible();
      console.log(`File visible in table: ${fileVisible}`);
      
    } catch (e) {
      console.log(`Test failed: ${e}`);
      
      // Take a screenshot for debugging
      await page.screenshot({ path: path.join(tempDir, 'upload-failure.png') });
      console.log(`Screenshot saved to ${path.join(tempDir, 'upload-failure.png')}`);
      
      throw e;
    }
  });
  
  test('should debug API endpoint URLs', async ({ page }) => {
    // Set up network logging
    await setupNetworkLogging(page);
    
    console.log('Checking API endpoint URLs...');
    
    // Navigate to the file upload page
    await page.goto('/file_upload');
    
    // Wait for page to load and API calls to complete
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
    await page.waitForTimeout(2000);
    
    // Inspect the network requests to see what endpoints are being called
    console.log('Checking network requests for API version prefix...');
    
    // Manually trigger a file list refresh to see the API call
    console.log('Triggering file list refresh...');
    
    // Use the browser console to check the API endpoints configuration
    const apiConfig = await page.evaluate(() => {
      // @ts-ignore - This is for debugging only
      return window.API_ENDPOINTS ? window.API_ENDPOINTS : 'API_ENDPOINTS not found in window';
    });
    
    console.log('API Endpoints configuration:', apiConfig);
    
    // Check axios default baseURL if set
    const axiosDefaults = await page.evaluate(() => {
      // @ts-ignore - This is for debugging only
      return window.axios && window.axios.defaults ? window.axios.defaults.baseURL : 'axios defaults not found';
    });
    
    console.log('Axios defaults:', axiosDefaults);
  });
  
  test('should debug file upload with explicit API URL', async ({ page }) => {
    // Set up network logging
    await setupNetworkLogging(page);
    
    console.log('Testing file upload with explicit API URL...');
    
    // Navigate to the file upload page
    await page.goto('/file_upload');
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
    
    // Override the upload endpoint in the browser context
    await page.evaluate(() => {
      // @ts-ignore - This is for debugging only
      window.DEBUG_UPLOAD_URL = '/api/v1/file_upload';
    });
    
    // Intercept fetch/XHR requests to /api/file_upload and redirect to /api/v1/file_upload
    await page.route('**/api/file_upload', route => {
      const url = route.request().url();
      const newUrl = url.replace('/api/file_upload', '/api/v1/file_upload');
      console.log(`Redirecting ${url} to ${newUrl}`);
      route.continue({ url: newUrl });
    });
    
    // Click the upload button
    console.log('Opening upload modal...');
    await page.click('button:has-text("Upload File")');
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    console.log('Selecting file...');
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(testFilePath);
    
    // Add description
    const description = 'Debug test with explicit API URL';
    await page.fill('textarea[placeholder="Enter file description"]', description);
    
    // Submit the form
    console.log('Submitting upload form...');
    await page.click('button:has-text("Upload")');
    
    // Wait for the upload to complete or fail
    try {
      await expect(page.locator('.ant-modal-title:has-text("Upload File")')).not.toBeVisible({ timeout: 10000 });
      console.log('Upload modal closed');
      
      // Check for success or error messages
      const successVisible = await page.locator('.ant-message-success').isVisible();
      const errorVisible = await page.locator('.ant-message-error').isVisible();
      
      console.log(`Success message visible: ${successVisible}`);
      console.log(`Error message visible: ${errorVisible}`);
      
      if (errorVisible) {
        const errorText = await page.locator('.ant-message-error').textContent();
        console.log(`Error message: ${errorText}`);
      }
    } catch (e) {
      console.log(`Test failed: ${e}`);
      await page.screenshot({ path: path.join(tempDir, 'explicit-url-failure.png') });
      throw e;
    }
  });
  
  test('should test file upload with mock API response', async ({ page }) => {
    console.log('Testing file upload with mock API response...');
    
    // Mock the file upload API endpoint
    await page.route('**/api/v1/file_upload', route => {
      console.log(`Mocking API response for ${route.request().url()}`);
      route.fulfill({
        status: 201,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '123e4567-e89b-12d3-a456-426614174000',
          filename: path.basename(testFilePath),
          file_size: fs.statSync(testFilePath).size,
          content_type: 'text/plain',
          file_hash: 'mock-file-hash',
          description: 'Mock API response test',
          download_url: '/api/v1/file_upload/download/123e4567-e89b-12d3-a456-426614174000',
          created_on: new Date().toISOString(),
          modified_on: null,
          owner_id: '00000000-0000-0000-0000-000000000000',
          is_active: true
        })
      });
    });
    
    // Navigate to the file upload page
    await page.goto('/file_upload');
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
    
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(testFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Mock API response test');
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Wait for the upload to complete
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).not.toBeVisible({ timeout: 10000 });
    
    // Check if success message appears
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Check if the file appears in the table
    await expect(page.locator('td:has-text("test-file.txt")')).toBeVisible({ timeout: 10000 });
    
    console.log('Mock API test completed successfully');
  });
}); 