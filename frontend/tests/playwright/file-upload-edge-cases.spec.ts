import { test, expect, Page } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

/**
 * This test suite focuses on edge cases and error handling for file uploads
 */
test.describe('File Upload Edge Cases', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  // Create temporary test files
  let tempDir: string;
  let emptyFilePath: string;
  let largeFilePath: string;
  let specialCharsFilePath: string;
  let unsupportedFilePath: string;
  let duplicateFilePath: string;
  
  test.beforeAll(async () => {
    console.log('Setting up test files for edge cases...');
    
    // Create a temporary directory for test files
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'upload-edge-cases-'));
    console.log(`Created temp directory: ${tempDir}`);
    
    // Create an empty file
    emptyFilePath = path.join(tempDir, 'empty-file.txt');
    fs.writeFileSync(emptyFilePath, '');
    console.log(`Created empty file: ${emptyFilePath}`);
    
    // Create a large file (10MB)
    largeFilePath = path.join(tempDir, 'large-file.bin');
    const largeBuffer = Buffer.alloc(10 * 1024 * 1024); // 10MB of zeros
    fs.writeFileSync(largeFilePath, largeBuffer);
    console.log(`Created large file: ${largeFilePath}`);
    
    // Create a file with special characters in the name
    specialCharsFilePath = path.join(tempDir, 'special@#$%^&*()_+.txt');
    fs.writeFileSync(specialCharsFilePath, 'File with special characters in the name');
    console.log(`Created file with special characters: ${specialCharsFilePath}`);
    
    // Create a file with unsupported extension
    unsupportedFilePath = path.join(tempDir, 'unsupported.xyz');
    fs.writeFileSync(unsupportedFilePath, 'File with unsupported extension');
    console.log(`Created file with unsupported extension: ${unsupportedFilePath}`);
    
    // Create a file for duplicate testing
    duplicateFilePath = path.join(tempDir, 'duplicate-file.txt');
    fs.writeFileSync(duplicateFilePath, 'This file will be uploaded twice to test duplicate handling');
    console.log(`Created duplicate file: ${duplicateFilePath}`);
  });
  
  test.afterAll(async () => {
    // Clean up temporary files and directories
    console.log('Cleaning up test files...');
    fs.rmSync(tempDir, { recursive: true, force: true });
    console.log('Cleanup complete');
  });
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the file upload page
    await page.goto('/file_upload');
    
    // Wait for the page to load
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
  });
  
  test('should handle empty file upload', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input to empty file
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(emptyFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Empty file upload test');
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Check if the application handles empty files appropriately
    // This could be a success message if empty files are allowed, or an error message if they're not
    try {
      // Check for error message first (more likely for empty files)
      const errorVisible = await page.locator('.ant-message-error').isVisible({ timeout: 5000 });
      if (errorVisible) {
        const errorText = await page.locator('.ant-message-error').textContent();
        console.log(`Error message for empty file: ${errorText}`);
        // Verify the error message is appropriate for empty files
        expect(errorText).toContain('empty');
      } else {
        // If no error, check if upload succeeded
        await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 5000 });
        // Verify the file appears in the table
        await expect(page.locator('td:has-text("empty-file.txt")')).toBeVisible({ timeout: 5000 });
      }
    } catch (e) {
      console.log(`Empty file test failed: ${e}`);
      throw e;
    }
  });
  
  test('should handle large file upload', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input to large file
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(largeFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Large file upload test');
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Check if the application handles large files appropriately
    // This could be a success message if the file size is within limits, or an error message if it's too large
    try {
      // Check for error message first (more likely for large files)
      const errorVisible = await page.locator('.ant-message-error').isVisible({ timeout: 10000 });
      if (errorVisible) {
        const errorText = await page.locator('.ant-message-error').textContent();
        console.log(`Error message for large file: ${errorText}`);
        // Verify the error message is appropriate for large files
        expect(errorText).toContain('size');
      } else {
        // If no error, check if upload succeeded (may take longer for large files)
        await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 30000 });
        // Verify the file appears in the table
        await expect(page.locator('td:has-text("large-file.bin")')).toBeVisible({ timeout: 5000 });
      }
    } catch (e) {
      console.log(`Large file test failed: ${e}`);
      throw e;
    }
  });
  
  test('should handle file with special characters in name', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input to file with special characters
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(specialCharsFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Special characters filename test');
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Wait for the upload to complete
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).not.toBeVisible({ timeout: 10000 });
    
    // Check if success message appears
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Verify the file appears in the table (using a partial match since special chars might be encoded)
    const fileName = path.basename(specialCharsFilePath);
    const baseNameWithoutExtension = fileName.split('.')[0].substring(0, 5);
    await expect(page.locator(`td:has-text("${baseNameWithoutExtension}")`)).toBeVisible({ timeout: 5000 });
  });
  
  test('should handle duplicate file upload', async ({ page }) => {
    // Upload the file first time
    await page.click('button:has-text("Upload File")');
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    const fileChooserPromise1 = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser1 = await fileChooserPromise1;
    await fileChooser1.setFiles(duplicateFilePath);
    
    await page.fill('textarea[placeholder="Enter file description"]', 'First upload');
    await page.click('button:has-text("Upload")');
    
    // Wait for the first upload to complete
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).not.toBeVisible({ timeout: 10000 });
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Upload the same file again
    await page.click('button:has-text("Upload File")');
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    const fileChooserPromise2 = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser2 = await fileChooserPromise2;
    await fileChooser2.setFiles(duplicateFilePath);
    
    await page.fill('textarea[placeholder="Enter file description"]', 'Duplicate upload');
    await page.click('button:has-text("Upload")');
    
    // Check how the application handles duplicate files
    // This could be a success message if duplicates are allowed, or an error message if they're not
    try {
      // Check for error message first
      const errorVisible = await page.locator('.ant-message-error').isVisible({ timeout: 5000 });
      if (errorVisible) {
        const errorText = await page.locator('.ant-message-error').textContent();
        console.log(`Error message for duplicate file: ${errorText}`);
        // Verify the error message is appropriate for duplicate files
        expect(errorText).toContain('duplicate');
      } else {
        // If no error, check if upload succeeded
        await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 5000 });
        
        // Count the number of rows with the duplicate filename
        const fileName = path.basename(duplicateFilePath);
        const rowCount = await page.locator(`td:has-text("${fileName}")`).count();
        expect(rowCount).toBeGreaterThan(1);
      }
    } catch (e) {
      console.log(`Duplicate file test failed: ${e}`);
      throw e;
    }
  });
  
  test('should handle cancellation during upload', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input to large file to ensure upload takes some time
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(largeFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Cancellation test');
    
    // Start the upload
    await page.click('button:has-text("Upload")');
    
    // Wait a short time for the upload to start
    await page.waitForTimeout(500);
    
    // Cancel the upload by clicking the cancel button or X in the modal
    // This depends on how the UI is implemented
    try {
      // Try to find and click a cancel button first
      const cancelButton = page.locator('button:has-text("Cancel")');
      if (await cancelButton.isVisible({ timeout: 1000 })) {
        await cancelButton.click();
      } else {
        // If no cancel button, try the X in the modal
        await page.click('.ant-modal-close');
      }
      
      // Verify the modal is closed
      await expect(page.locator('.ant-modal-title:has-text("Upload File")')).not.toBeVisible({ timeout: 5000 });
      
      // Check that the file doesn't appear in the table
      const fileName = path.basename(largeFilePath);
      await expect(page.locator(`td:has-text("${fileName}")`)).not.toBeVisible({ timeout: 5000 });
    } catch (e) {
      console.log(`Cancellation test failed: ${e}`);
      throw e;
    }
  });
}); 