import { test, expect } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import * as crypto from 'crypto';

/**
 * This test suite focuses on performance aspects of the file upload component
 */
test.describe('File Upload Performance', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  // Create temporary test files of different sizes
  let tempDir: string;
  let smallFilePath: string;
  let mediumFilePath: string;
  let largeFilePath: string;
  
  test.beforeAll(async () => {
    // Create a temporary directory for test files
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'upload-perf-test-'));
    
    // Create test files of different sizes
    smallFilePath = path.join(tempDir, 'small-file.txt');
    mediumFilePath = path.join(tempDir, 'medium-file.bin');
    largeFilePath = path.join(tempDir, 'large-file.bin');
    
    // 10KB file
    fs.writeFileSync(smallFilePath, crypto.randomBytes(10 * 1024));
    
    // 1MB file
    fs.writeFileSync(mediumFilePath, crypto.randomBytes(1024 * 1024));
    
    // 5MB file
    fs.writeFileSync(largeFilePath, crypto.randomBytes(5 * 1024 * 1024));
  });
  
  test.afterAll(async () => {
    // Clean up temporary files and directories
    fs.rmSync(tempDir, { recursive: true, force: true });
  });
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the file upload page
    await page.goto('/file_upload');
    
    // Wait for the page to load
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
  });
  
  test('should measure page load performance', async ({ page }) => {
    // Measure page load time
    const startTime = Date.now();
    
    await page.goto('/file_upload');
    
    // Wait for the page to be fully loaded
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    console.log(`Page load time: ${loadTime}ms`);
    
    // Assert that page load time is within acceptable limits
    // Adjust the threshold based on your application's requirements
    expect(loadTime).toBeLessThan(5000);
  });
  
  test('should measure small file upload performance', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(smallFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Small file performance test');
    
    // Measure upload time
    const startTime = Date.now();
    
    // Start listening for network requests
    const uploadRequestPromise = page.waitForRequest(request => 
      request.url().includes('/api/upload') && request.method() === 'POST'
    );
    
    // Start listening for network responses
    const uploadResponsePromise = page.waitForResponse(response => 
      response.url().includes('/api/upload') && response.request().method() === 'POST'
    );
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Wait for the upload request to be sent
    const uploadRequest = await uploadRequestPromise;
    const requestSentTime = Date.now() - startTime;
    
    // Wait for the upload response to be received
    const uploadResponse = await uploadResponsePromise;
    const responseReceivedTime = Date.now() - startTime;
    
    // Wait for the success message
    await expect(page.locator('.ant-message-success')).toBeVisible();
    const uiUpdatedTime = Date.now() - startTime;
    
    console.log(`Small file upload metrics:
      - Request sent: ${requestSentTime}ms
      - Response received: ${responseReceivedTime}ms
      - UI updated: ${uiUpdatedTime}ms
    `);
    
    // Assert that upload time is within acceptable limits
    // Adjust the thresholds based on your application's requirements
    expect(responseReceivedTime).toBeLessThan(3000);
    expect(uiUpdatedTime).toBeLessThan(4000);
  });
  
  test('should measure medium file upload performance', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(mediumFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Medium file performance test');
    
    // Measure upload time
    const startTime = Date.now();
    
    // Start listening for network requests
    const uploadRequestPromise = page.waitForRequest(request => 
      request.url().includes('/api/upload') && request.method() === 'POST'
    );
    
    // Start listening for network responses
    const uploadResponsePromise = page.waitForResponse(response => 
      response.url().includes('/api/upload') && response.request().method() === 'POST'
    );
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Wait for the upload request to be sent
    const uploadRequest = await uploadRequestPromise;
    const requestSentTime = Date.now() - startTime;
    
    // Wait for the upload response to be received
    const uploadResponse = await uploadResponsePromise;
    const responseReceivedTime = Date.now() - startTime;
    
    // Wait for the success message
    await expect(page.locator('.ant-message-success')).toBeVisible();
    const uiUpdatedTime = Date.now() - startTime;
    
    console.log(`Medium file upload metrics:
      - Request sent: ${requestSentTime}ms
      - Response received: ${responseReceivedTime}ms
      - UI updated: ${uiUpdatedTime}ms
    `);
    
    // Assert that upload time is within acceptable limits
    // Adjust the thresholds based on your application's requirements
    expect(responseReceivedTime).toBeLessThan(5000);
    expect(uiUpdatedTime).toBeLessThan(6000);
  });
  
  test('should measure large file upload performance', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(largeFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Large file performance test');
    
    // Measure upload time
    const startTime = Date.now();
    
    // Start listening for network requests
    const uploadRequestPromise = page.waitForRequest(request => 
      request.url().includes('/api/upload') && request.method() === 'POST'
    );
    
    // Start listening for network responses
    const uploadResponsePromise = page.waitForResponse(response => 
      response.url().includes('/api/upload') && response.request().method() === 'POST'
    );
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Wait for the upload request to be sent
    const uploadRequest = await uploadRequestPromise;
    const requestSentTime = Date.now() - startTime;
    
    // Wait for the upload response to be received
    const uploadResponse = await uploadResponsePromise;
    const responseReceivedTime = Date.now() - startTime;
    
    // Wait for the success message
    await expect(page.locator('.ant-message-success')).toBeVisible();
    const uiUpdatedTime = Date.now() - startTime;
    
    console.log(`Large file upload metrics:
      - Request sent: ${requestSentTime}ms
      - Response received: ${responseReceivedTime}ms
      - UI updated: ${uiUpdatedTime}ms
    `);
    
    // Assert that upload time is within acceptable limits
    // Adjust the thresholds based on your application's requirements
    expect(responseReceivedTime).toBeLessThan(15000);
    expect(uiUpdatedTime).toBeLessThan(16000);
  });
  
  test('should measure file listing performance', async ({ page }) => {
    // Upload a few files first to ensure there's data to list
    // This is optional if you already have files in the system
    
    // Measure the time it takes to refresh the file list
    const startTime = Date.now();
    
    // Click the refresh button if available, or navigate to the page again
    try {
      const refreshButton = page.locator('button[aria-label="refresh"]');
      if (await refreshButton.isVisible()) {
        await refreshButton.click();
      } else {
        await page.reload();
      }
    } catch (e) {
      // If no refresh button, just reload the page
      await page.reload();
    }
    
    // Wait for the file table to be visible
    await page.waitForSelector('.file-upload-table', { timeout: 10000 });
    
    // Wait for network activity to settle
    await page.waitForLoadState('networkidle');
    
    const listingTime = Date.now() - startTime;
    
    console.log(`File listing time: ${listingTime}ms`);
    
    // Assert that listing time is within acceptable limits
    expect(listingTime).toBeLessThan(3000);
  });
  
  test('should measure UI responsiveness during file upload', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input for a large file
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(largeFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'UI responsiveness test');
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Try to interact with the UI during upload
    // Measure response time for UI interactions
    
    // Wait a short time for the upload to start
    await page.waitForTimeout(500);
    
    // Try to click on various UI elements and measure response time
    const uiInteractions = [
      { selector: 'a:has-text("Home")', name: 'Home link' },
      { selector: 'button:has-text("Upload File")', name: 'Upload button' },
      { selector: '.ant-table-thead', name: 'Table header' }
    ];
    
    for (const interaction of uiInteractions) {
      const startTime = Date.now();
      
      try {
        await page.click(interaction.selector, { timeout: 1000 });
        const responseTime = Date.now() - startTime;
        
        console.log(`UI interaction with ${interaction.name}: ${responseTime}ms`);
        
        // Assert that UI response time is within acceptable limits
        expect(responseTime).toBeLessThan(500);
      } catch (e) {
        console.log(`Could not interact with ${interaction.name} during upload`);
      }
    }
    
    // Wait for the upload to complete
    await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 20000 });
  });
  
  test('should measure concurrent uploads performance', async ({ page }) => {
    // This test requires multiple file uploads at once
    // Some implementations might not support this, so we'll check first
    
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Check if multiple file selection is supported
    const fileInput = page.locator('input[type="file"]');
    const multiple = await fileInput.getAttribute('multiple');
    
    if (!multiple) {
      console.log('Multiple file upload not supported, skipping concurrent uploads test');
      // Close the modal
      await page.click('button:has-text("Cancel")');
      return;
    }
    
    // Set multiple files for upload
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles([smallFilePath, mediumFilePath]);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Concurrent uploads test');
    
    // Measure upload time
    const startTime = Date.now();
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Wait for the success message
    await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 20000 });
    
    const uploadTime = Date.now() - startTime;
    
    console.log(`Concurrent uploads time: ${uploadTime}ms`);
    
    // Assert that concurrent upload time is within acceptable limits
    expect(uploadTime).toBeLessThan(10000);
  });
}); 