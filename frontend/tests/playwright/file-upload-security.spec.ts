import { test, expect } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

/**
 * This test suite focuses on security aspects of the file upload component
 */
test.describe('File Upload Security', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  // Create temporary test files
  let tempDir: string;
  let maliciousJsFilePath: string;
  let maliciousPhpFilePath: string;
  let maliciousSvgFilePath: string;
  let oversizedFilePath: string;
  let validFilePath: string;
  
  test.beforeAll(async () => {
    // Create a temporary directory for test files
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'upload-security-test-'));
    
    // Create test files with potentially malicious content
    maliciousJsFilePath = path.join(tempDir, 'malicious.js');
    fs.writeFileSync(maliciousJsFilePath, 'alert("XSS Attack"); // Malicious JavaScript');
    
    maliciousPhpFilePath = path.join(tempDir, 'malicious.php');
    fs.writeFileSync(maliciousPhpFilePath, '<?php system($_GET["cmd"]); // Command injection ?>');
    
    maliciousSvgFilePath = path.join(tempDir, 'malicious.svg');
    fs.writeFileSync(maliciousSvgFilePath, `
      <svg xmlns="http://www.w3.org/2000/svg" width="100" height="100">
        <script>alert('XSS via SVG')</script>
        <rect width="100" height="100" fill="blue" />
      </svg>
    `);
    
    // Create an oversized file (11MB if the limit is 10MB)
    // This is a placeholder - in a real test, you'd want to check the actual size limit
    oversizedFilePath = path.join(tempDir, 'oversized.bin');
    const buffer = Buffer.alloc(11 * 1024 * 1024, 'x');
    fs.writeFileSync(oversizedFilePath, buffer);
    
    // Create a valid file for comparison
    validFilePath = path.join(tempDir, 'valid.txt');
    fs.writeFileSync(validFilePath, 'This is a valid text file');
  });
  
  test.afterAll(async () => {
    // Clean up temporary files and directories
    fs.rmSync(tempDir, { recursive: true, force: true });
  });
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the file upload page
    await page.goto('/file_upload');
    
    // Wait for the page to load
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
  });
  
  test('should reject malicious JavaScript files', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(maliciousJsFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Security test - JS file');
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Check for error message or rejection
    try {
      // Look for error message
      await expect(page.locator('.ant-message-error, .ant-alert-error, .error-message')).toBeVisible({ timeout: 5000 });
      
      // If we get here, the file was properly rejected
      console.log('JavaScript file was properly rejected');
    } catch (e) {
      // If no error message, check if the upload succeeded (which would be a security issue)
      const successMessage = await page.locator('.ant-message-success').isVisible();
      
      if (successMessage) {
        // This is a potential security issue - the malicious file was accepted
        console.error('SECURITY ISSUE: Malicious JavaScript file was accepted');
        
        // Take a screenshot for evidence
        await page.screenshot({ path: path.join(tempDir, 'security-js-accepted.png') });
        
        // Fail the test
        expect(successMessage).toBeFalsy();
        console.log('Malicious JavaScript file should be rejected');
      } else {
        // No clear error or success message, check if the modal is still open
        const modalVisible = await page.locator('.ant-modal-title:has-text("Upload File")').isVisible();
        expect(modalVisible).toBeTruthy();
        console.log('Upload modal should remain open when file is rejected');
      }
    }
  });
  
  test('should reject malicious PHP files', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(maliciousPhpFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Security test - PHP file');
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Check for error message or rejection
    try {
      // Look for error message
      await expect(page.locator('.ant-message-error, .ant-alert-error, .error-message')).toBeVisible({ timeout: 5000 });
      
      // If we get here, the file was properly rejected
      console.log('PHP file was properly rejected');
    } catch (e) {
      // If no error message, check if the upload succeeded (which would be a security issue)
      const successMessage = await page.locator('.ant-message-success').isVisible();
      
      if (successMessage) {
        // This is a potential security issue - the malicious file was accepted
        console.error('SECURITY ISSUE: Malicious PHP file was accepted');
        
        // Take a screenshot for evidence
        await page.screenshot({ path: path.join(tempDir, 'security-php-accepted.png') });
        
        // Fail the test
        expect(successMessage).toBeFalsy();
        console.log('Malicious PHP file should be rejected');
      } else {
        // No clear error or success message, check if the modal is still open
        const modalVisible = await page.locator('.ant-modal-title:has-text("Upload File")').isVisible();
        expect(modalVisible).toBeTruthy();
        console.log('Upload modal should remain open when file is rejected');
      }
    }
  });
  
  test('should handle malicious SVG files safely', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(maliciousSvgFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Security test - SVG file');
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Check for error message or rejection
    try {
      // Look for error message
      await expect(page.locator('.ant-message-error, .ant-alert-error, .error-message')).toBeVisible({ timeout: 5000 });
      
      // If we get here, the file was properly rejected
      console.log('SVG file with script was properly rejected');
    } catch (e) {
      // If no error message, check if the upload succeeded
      const successMessage = await page.locator('.ant-message-success').isVisible();
      
      if (successMessage) {
        // The SVG was accepted, but this might be OK if the application sanitizes SVGs
        console.log('SVG file was accepted - checking if scripts are sanitized');
        
        // Navigate to the file if possible to check if the script executes
        // This is a complex test that depends on the application's behavior
        // For now, we'll just log a warning
        console.warn('WARNING: SVG file with script was accepted - manual verification needed');
        
        // Take a screenshot for evidence
        await page.screenshot({ path: path.join(tempDir, 'security-svg-accepted.png') });
      } else {
        // No clear error or success message, check if the modal is still open
        const modalVisible = await page.locator('.ant-modal-title:has-text("Upload File")').isVisible();
        expect(modalVisible).toBeTruthy();
        console.log('Upload modal should remain open when file is rejected');
      }
    }
  });
  
  test('should reject oversized files', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(oversizedFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Security test - Oversized file');
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Check for error message about file size
    try {
      // Look for error message
      await expect(page.locator('.ant-message-error, .ant-alert-error, .error-message')).toBeVisible({ timeout: 5000 });
      
      // If we get here, the file was properly rejected
      console.log('Oversized file was properly rejected');
      
      // Verify the error message mentions file size
      const errorText = await page.locator('.ant-message-error, .ant-alert-error, .error-message').textContent();
      expect(errorText?.toLowerCase().includes('size')).toBeTruthy();
    } catch (e) {
      // If no error message, check if the upload succeeded (which would be a security issue)
      const successMessage = await page.locator('.ant-message-success').isVisible();
      
      if (successMessage) {
        // This is a potential security issue - the oversized file was accepted
        console.error('SECURITY ISSUE: Oversized file was accepted');
        
        // Take a screenshot for evidence
        await page.screenshot({ path: path.join(tempDir, 'security-oversized-accepted.png') });
        
        // Fail the test
        expect(successMessage).toBeFalsy();
        console.log('Oversized file should be rejected');
      } else {
        // No clear error or success message, check if the modal is still open
        const modalVisible = await page.locator('.ant-modal-title:has-text("Upload File")').isVisible();
        expect(modalVisible).toBeTruthy();
        console.log('Upload modal should remain open when file is rejected');
      }
    }
  });
  
  test('should sanitize file names with path traversal attempts', async ({ page }) => {
    // Create a file with a path traversal attempt in the name
    const traversalFilePath = path.join(tempDir, '..\\..\\etc\\passwd.txt');
    fs.writeFileSync(traversalFilePath, 'This file has a path traversal attempt in its name');
    
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(traversalFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Security test - Path traversal');
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Check if the upload succeeds (it might, but the filename should be sanitized)
    try {
      await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 5000 });
      
      // If the upload succeeded, verify the file is listed with a sanitized name
      await page.waitForSelector('.file-upload-table', { timeout: 5000 });
      
      // Check if the raw path traversal string appears in the table
      const tableContent = await page.locator('.file-upload-table').textContent() || '';
      expect(tableContent.includes('..\\..\\etc\\passwd')).toBeFalsy();
      
      console.log('Path traversal attempt was handled safely');
    } catch (e) {
      // If the upload was rejected, that's also acceptable
      console.log('Path traversal attempt was rejected');
    }
  });
  
  test('should handle null byte injection attempts', async ({ page }) => {
    // Create a file with a null byte in the name
    // Note: Node.js might not allow null bytes in filenames, so we'll simulate this
    const nullByteFilePath = path.join(tempDir, 'nullbyte.txt');
    fs.writeFileSync(nullByteFilePath, 'This file simulates a null byte injection attempt');
    
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(nullByteFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Security test - Null byte');
    
    // Modify the file name in the UI if possible to include a null byte
    // This is a simulation since we can't easily create a real null byte filename
    try {
      // This is a simplified approach - in a real test, you'd need to find the filename input
      // and modify it to include a null byte
      await page.evaluate(() => {
        // Try to find a filename input and modify it
        const filenameInput = document.querySelector('input[type="text"][value*=".txt"]') as HTMLInputElement;
        if (filenameInput) {
          filenameInput.value = 'malicious.php\0.txt';
        }
      });
    } catch (e) {
      console.log('Could not simulate null byte injection in filename');
    }
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Check if the upload succeeds
    await expect(page.locator('.ant-message-success, .ant-message-error')).toBeVisible({ timeout: 5000 });
    
    // If the upload succeeded, verify the file extension is still .txt
    if (await page.locator('.ant-message-success').isVisible()) {
      await page.waitForSelector('.file-upload-table', { timeout: 5000 });
      
      // Check the file extension in the table
      const tableContent = await page.locator('.file-upload-table').textContent() || '';
      
      // The file should be stored with .txt extension, not .php
      const hasPhpExtension = tableContent.includes('.php');
      expect(hasPhpExtension).toBeFalsy();
      console.log('File should not be stored with .php extension');
      
      console.log('Null byte injection attempt was handled safely');
    } else {
      // If the upload was rejected, that's also acceptable
      console.log('Null byte injection attempt was rejected');
    }
  });
  
  test('should validate MIME type of uploaded files', async ({ page }) => {
    // Create a text file with a misleading extension
    const misleadingFilePath = path.join(tempDir, 'text-disguised-as.exe');
    fs.writeFileSync(misleadingFilePath, 'This is actually a text file with a misleading extension');
    
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(misleadingFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Security test - MIME type validation');
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Check the result - either rejection (good) or acceptance with proper MIME type handling
    const successVisible = await page.locator('.ant-message-success').isVisible({ timeout: 5000 });
    const errorVisible = await page.locator('.ant-message-error, .ant-alert-error').isVisible({ timeout: 1000 });
    
    if (successVisible) {
      // If accepted, the application should handle it based on content, not extension
      console.log('File with misleading extension was accepted - checking handling');
      
      // Ideally, we would check if the file is treated as text, not executable
      // This is application-specific and might require additional checks
    } else if (errorVisible) {
      // If rejected, that's a good security practice
      console.log('File with misleading extension was properly rejected');
    } else {
      // No clear message, check if the modal is still open
      const modalVisible = await page.locator('.ant-modal-title:has-text("Upload File")').isVisible();
      expect(modalVisible).toBeTruthy();
      console.log('Upload modal should remain open when file is rejected');
    }
  });
  
  test('should prevent XSS in file name display', async ({ page }) => {
    // Create a file with XSS payload in the name
    const xssFilePath = path.join(tempDir, 'xss_test_<img src=x onerror=alert(1)>.txt');
    fs.writeFileSync(xssFilePath, 'This file has an XSS payload in its name');
    
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(xssFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Security test - XSS in filename');
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Check if the upload succeeds
    try {
      await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 5000 });
      
      // If the upload succeeded, check if the XSS payload is properly escaped in the UI
      await page.waitForSelector('.file-upload-table', { timeout: 5000 });
      
      // Check if any alert dialogs appear (they shouldn't)
      const dialogPromise = page.waitForEvent('dialog', { timeout: 1000 }).catch(e => null);
      
      // Force a re-render of the table if possible
      await page.reload();
      await page.waitForSelector('.file-upload-table', { timeout: 10000 });
      
      // Wait a moment to see if any dialogs appear
      const dialog = await dialogPromise;
      expect(dialog).toBeNull();
      
      // Check the HTML source to verify the filename is properly escaped
      const htmlSource = await page.content();
      expect(htmlSource.includes('<img src=x onerror=alert(1)>')).toBeFalsy();
      
      console.log('XSS payload in filename was properly escaped');
    } catch (e) {
      // If the upload was rejected, that's also acceptable
      console.log('File with XSS in filename was rejected');
    }
  });
  
  test('should upload valid files successfully', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(validFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Security test - Valid file');
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Check for success message
    await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 5000 });
    
    // Verify the file appears in the table
    await page.waitForSelector('.file-upload-table', { timeout: 5000 });
    const tableContent = await page.locator('.file-upload-table').textContent();
    expect(tableContent?.includes('valid.txt')).toBeTruthy();
    
    console.log('Valid file was uploaded successfully');
  });
}); 