import { test, expect } from '@playwright/test';

test.describe('File Upload UI', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the file upload page
    await page.goto('/file_upload');
    
    // Wait for the page to load
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
  });
  
  test('should display the file upload page', async ({ page }) => {
    // Check if the page title is visible
    await expect(page.locator('h1:has-text("File Upload")')).toBeVisible();
    
    // Check if the upload button is visible
    await expect(page.locator('button:has-text("Upload File")')).toBeVisible();
    
    // Check if the file table is visible
    await expect(page.locator('.file-upload-table')).toBeVisible();
  });
  
  test('should open upload modal when clicking upload button', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Check if the modal is visible
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Check if the form elements are visible
    await expect(page.locator('.ant-upload')).toBeVisible();
    await expect(page.locator('textarea[placeholder="Enter file description"]')).toBeVisible();
    
    // Close the modal
    await page.click('button:has-text("Cancel")');
  });
  
  test('should upload a file', async ({ page }) => {
    // Create a test file
    const testFileName = 'test-file.txt';
    const testFileContent = 'This is a test file for upload testing';
    
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input (this is tricky with Ant Design's Upload component)
    // We'll use the file chooser event
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles({
      name: testFileName,
      mimeType: 'text/plain',
      buffer: Buffer.from(testFileContent),
    });
    
    // Add description
    await page.fill('textarea[placeholder="Enter file description"]', 'Test file description');
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Wait for the upload to complete and modal to close
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).not.toBeVisible({ timeout: 10000 });
    
    // Check if success message appears
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Check if the file appears in the table
    await expect(page.locator(`td:has-text("${testFileName}")`)).toBeVisible({ timeout: 10000 });
  });
  
  test('should download a file', async ({ page }) => {
    // Find the first download button in the table
    const downloadButton = page.locator('.ant-table-row').first().locator('button[aria-label="Download file"]');
    
    // Check if the button exists
    await expect(downloadButton).toBeVisible();
    
    // Click the download button and wait for download
    const downloadPromise = page.waitForEvent('download');
    await downloadButton.click();
    const download = await downloadPromise;
    
    // Verify download started
    expect(download.suggestedFilename()).toBeTruthy();
  });
  
  test('should delete a file', async ({ page }) => {
    // Count the number of rows before deletion
    const initialRowCount = await page.locator('.ant-table-row').count();
    
    // Find the first delete button in the table
    const deleteButton = page.locator('.ant-table-row').first().locator('button[aria-label="Delete file"]');
    
    // Check if the button exists
    await expect(deleteButton).toBeVisible();
    
    // Click the delete button
    await deleteButton.click();
    
    // Confirm deletion in the modal
    await page.click('button:has-text("Yes")');
    
    // Check if success message appears
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Verify row was deleted
    await expect(page.locator('.ant-table-row')).toHaveCount(initialRowCount - 1);
  });
}); 