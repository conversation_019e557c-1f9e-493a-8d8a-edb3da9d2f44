import { test, expect } from '@playwright/test';

test.describe('Main Page UI', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the main page
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForSelector('.main-page-container', { timeout: 10000 });
  });
  
  test('should display the main page with all components', async ({ page }) => {
    // Check if the page title is visible
    await expect(page.locator('h2:has-text("TurdParty File Injection")')).toBeVisible();
    
    // Check if the navigation buttons are visible
    await expect(page.locator('button:has-text("File Upload")')).toBeVisible();
    await expect(page.locator('button:has-text("VM Management")')).toBeVisible();
    await expect(page.locator('button:has-text("VM Injection")')).toBeVisible();
    await expect(page.locator('button:has-text("Docs")')).toBeVisible();
    
    // Check if the dark mode toggle is visible
    await expect(page.locator('.ant-switch')).toBeVisible();
    
    // Check if the steps are visible
    await expect(page.locator('.ant-steps')).toBeVisible();
    
    // Check if the first step (file upload) is active and visible
    await expect(page.locator('.ant-steps-item-active:has-text("Upload File")')).toBeVisible();
    
    // Check if the file upload form is visible
    await expect(page.locator('.ant-upload')).toBeVisible();
    await expect(page.locator('textarea[placeholder="Enter file description"]')).toBeVisible();
    await expect(page.locator('button:has-text("Upload and Continue")')).toBeVisible();
  });
  
  test('should toggle dark mode', async ({ page }) => {
    // Get the initial state of the switch
    const initialChecked = await page.locator('.ant-switch').isChecked();
    
    // Click the switch to toggle dark mode
    await page.click('.ant-switch');
    
    // Check if the switch state has changed
    await expect(page.locator('.ant-switch')).toHaveAttribute('aria-checked', (!initialChecked).toString());
    
    // Toggle back
    await page.click('.ant-switch');
    
    // Check if the switch state has changed back
    await expect(page.locator('.ant-switch')).toHaveAttribute('aria-checked', initialChecked.toString());
  });
  
  test('should navigate to docs page', async ({ page, context }) => {
    // Create a page promise to wait for the new tab
    const pagePromise = context.waitForEvent('page');
    
    // Click the docs button
    await page.click('button:has-text("Docs")');
    
    // Wait for the new page to open
    const newPage = await pagePromise;
    await newPage.waitForLoadState();
    
    // Check if the new page URL contains '/docs'
    expect(newPage.url()).toContain('/docs');
  });
  
  test('should navigate through the workflow steps', async ({ page }) => {
    // Upload a file
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles({
      name: 'test-file.txt',
      mimeType: 'text/plain',
      buffer: Buffer.from('This is a test file for the main page workflow'),
    });
    
    await page.fill('textarea[placeholder="Enter file description"]', 'Test file for main page workflow');
    
    // Mock the API response for file upload
    await page.route('/api/file_upload/', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '123e4567-e89b-12d3-a456-426614174000',
          filename: 'test-file.txt',
          file_size: 42,
          content_type: 'text/plain',
          file_hash: 'abc123',
          description: 'Test file for main page workflow',
          download_url: '/api/file_upload/download/123e4567-e89b-12d3-a456-426614174000',
          created_on: new Date().toISOString()
        })
      });
    });
    
    // Click upload button
    await page.click('button:has-text("Upload and Continue")');
    
    // Wait for step 2 to be active
    await expect(page.locator('.ant-steps-item-active:has-text("Configure Path")')).toBeVisible({ timeout: 10000 });
    
    // Fill in target path and permissions
    await page.fill('input[placeholder="Enter target path on VM"]', '/tmp/test-workflow-file.txt');
    await page.fill('input[placeholder="Enter file permissions (octal)"]', '0755');
    
    // Mock the API response for file selection
    await page.route('/api/file_selection/', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '123e4567-e89b-12d3-a456-426614174001',
          name: 'test-file.txt',
          description: 'File selection created from main page',
          file_upload_id: '123e4567-e89b-12d3-a456-426614174000',
          target_path: '/tmp/test-workflow-file.txt',
          permissions: '0755',
          created_on: new Date().toISOString()
        })
      });
    });
    
    // Click save button
    await page.click('button:has-text("Save and Continue")');
    
    // Wait for step 3 to be active
    await expect(page.locator('.ant-steps-item-active:has-text("Create VM")')).toBeVisible({ timeout: 10000 });
    
    // Fill in VM name and select template
    await page.fill('input[placeholder="Enter VM name"]', 'Test Workflow VM');
    await page.selectOption('select', { label: 'Ubuntu 20.04 LTS' });
    
    // Mock the API response for VM creation
    await page.route('/api/vagrant_vm/', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '123e4567-e89b-12d3-a456-426614174002',
          name: 'Test Workflow VM',
          description: 'VM created from main page',
          template: 'ubuntu_2004',
          memory_mb: 1024,
          cpus: 1,
          disk_gb: 20,
          status: 'pending',
          created_on: new Date().toISOString()
        })
      });
    });
    
    // Mock the VM status API to return 'running'
    await page.route('/api/vagrant_vm/*/status', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '123e4567-e89b-12d3-a456-426614174002',
          name: 'Test Workflow VM',
          status: 'running',
          ip_address: '*************',
          ssh_port: 2222,
          last_action: 'start',
          last_action_time: new Date().toISOString(),
          error_message: null
        })
      });
    });
    
    // Click create VM button
    await page.click('button:has-text("Create VM and Continue")');
    
    // Wait for step 4 to be active (this might take longer due to polling)
    await expect(page.locator('.ant-steps-item-active:has-text("Inject File")')).toBeVisible({ timeout: 30000 });
    
    // Fill in injection command
    await page.fill('textarea[placeholder="Enter command to run after injection (e.g., cat /tmp/uploaded_file)"]', 
      'cat /tmp/test-workflow-file.txt');
    
    // Mock the API response for VM injection
    await page.route('/api/vm_injection/', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '123e4567-e89b-12d3-a456-426614174003',
          vagrant_vm_id: '123e4567-e89b-12d3-a456-426614174002',
          file_selection_id: '123e4567-e89b-12d3-a456-426614174001',
          description: 'Injection created from main page',
          status: 'pending',
          additional_command: 'cat /tmp/test-workflow-file.txt',
          created_on: new Date().toISOString()
        })
      });
    });
    
    // Mock the injection status API to return 'completed'
    await page.route('/api/vm_injection/*/status', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '123e4567-e89b-12d3-a456-426614174003',
          vagrant_vm_id: '123e4567-e89b-12d3-a456-426614174002',
          file_selection_id: '123e4567-e89b-12d3-a456-426614174001',
          status: 'completed',
          error_message: null,
          completed_on: new Date().toISOString()
        })
      });
    });
    
    // Click inject file button
    await page.click('button:has-text("Inject File")');
    
    // Wait for step 5 to be active
    await expect(page.locator('.ant-steps-item-active:has-text("Complete")')).toBeVisible({ timeout: 10000 });
    
    // Check if completion message is visible
    await expect(page.locator('h3:has-text("Injection Process Complete")')).toBeVisible();
    
    // Check if the buttons are visible
    await expect(page.locator('button:has-text("View Injection Details")')).toBeVisible();
    await expect(page.locator('button:has-text("Start New Injection")')).toBeVisible();
  });
}); 