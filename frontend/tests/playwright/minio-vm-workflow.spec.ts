import { test, expect } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';

test.describe('Minio Upload and VM Injection Workflow', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  // Test file paths
  const testFilesDir = path.join(__dirname, '../test-files');
  const testScriptPath = path.join(testFilesDir, 'test-script.sh');
  const testConfigPath = path.join(testFilesDir, 'test-config.json');
  
  // Create test files before tests
  test.beforeAll(async () => {
    // Create test files directory if it doesn't exist
    if (!fs.existsSync(testFilesDir)) {
      fs.mkdirSync(testFilesDir, { recursive: true });
    }
    
    // Create a test shell script
    const scriptContent = `#!/bin/bash
echo "TurdParty Test Script"
echo "Running on $(hostname)"
echo "Current directory: $(pwd)"
echo "Files in current directory:"
ls -la
echo "Test completed successfully"
exit 0
`;
    fs.writeFileSync(testScriptPath, scriptContent);
    
    // Create a test config file
    const configContent = `{
  "name": "test-config",
  "version": "1.0.0",
  "description": "Test configuration file for TurdParty",
  "settings": {
    "enabled": true,
    "logLevel": "info",
    "maxRetries": 3,
    "timeout": 30
  }
}
`;
    fs.writeFileSync(testConfigPath, configContent);
  });
  
  // Clean up test files after tests
  test.afterAll(async () => {
    if (fs.existsSync(testScriptPath)) {
      fs.unlinkSync(testScriptPath);
    }
    if (fs.existsSync(testConfigPath)) {
      fs.unlinkSync(testConfigPath);
    }
    if (fs.existsSync(testFilesDir)) {
      fs.rmdirSync(testFilesDir);
    }
  });
  
  test('should upload files to Minio, inject them into a VM, and verify status', async ({ page }) => {
    // Step 1: Upload files to Minio
    await uploadFilesToMinio(page);
    
    // Step 2: Create a file selection
    await createFileSelection(page);
    
    // Step 3: Create a VM
    await createVM(page);
    
    // Step 4: Inject files into VM
    await injectFilesIntoVM(page);
    
    // Step 5: Verify injection status
    await verifyInjectionStatus(page);
  });
  
  async function uploadFilesToMinio(page) {
    // Navigate to file upload page
    await page.goto('/file_upload');
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
    
    // Mock the file upload API response
    await page.route('**/api/file_upload/', async (route) => {
      const method = route.request().method();
      if (method === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'test-file-1',
            filename: 'test-script.sh',
            file_size: 256,
            content_type: 'text/plain',
            upload_time: new Date().toISOString(),
            description: 'Test script for VM injection',
            owner_id: 'test-user',
            download_url: '/api/file_upload/test-file-1/download'
          })
        });
      } else {
        await route.continue();
      }
    });
    
    // Upload the test script
    await page.setInputFiles('input[type="file"]', testScriptPath);
    await page.fill('textarea[placeholder="Enter file description"]', 'Test script for VM injection');
    await page.click('button:has-text("Upload")');
    
    // Wait for success message
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Upload the test config file
    await page.setInputFiles('input[type="file"]', testConfigPath);
    await page.fill('textarea[placeholder="Enter file description"]', 'Test config for VM injection');
    await page.click('button:has-text("Upload")');
    
    // Wait for success message
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Verify files are displayed in the list
    await expect(page.locator('.file-list-item:has-text("test-script.sh")')).toBeVisible();
    await expect(page.locator('.file-list-item:has-text("test-config.json")')).toBeVisible();
  }
  
  async function createFileSelection(page) {
    // Navigate to file selection page
    await page.goto('/file_selection');
    await page.waitForSelector('.file-selection-container', { timeout: 10000 });
    
    // Mock API responses
    await page.route('**/api/file_upload/', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [
            {
              id: 'test-file-1',
              filename: 'test-script.sh',
              file_size: 256,
              content_type: 'text/plain',
              upload_time: new Date().toISOString(),
              description: 'Test script for VM injection',
              owner_id: 'test-user',
              download_url: '/api/file_upload/test-file-1/download'
            },
            {
              id: 'test-file-2',
              filename: 'test-config.json',
              file_size: 128,
              content_type: 'application/json',
              upload_time: new Date().toISOString(),
              description: 'Test config for VM injection',
              owner_id: 'test-user',
              download_url: '/api/file_upload/test-file-2/download'
            }
          ],
          total: 2
        })
      });
    });
    
    await page.route('**/api/file_selection/', async (route) => {
      const method = route.request().method();
      if (method === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'test-selection-1',
            name: 'Linux Test Files',
            file_upload_id: 'test-file-1',
            target_path: '/opt/test-scripts/test-script.sh',
            permissions: '755',
            owner: 'root',
            group: 'root',
            created_on: new Date().toISOString(),
            modified_on: null,
            owner_id: 'test-user'
          })
        });
      } else {
        await route.continue();
      }
    });
    
    // Click create button
    await page.click('button:has-text("Create Selection")');
    
    // Fill the form
    await page.waitForSelector('.ant-modal-content');
    await page.fill('input[placeholder="Enter selection name"]', 'Linux Test Files');
    
    // Select the test script file
    await page.click('.ant-select-selector');
    await page.click('.ant-select-item:has-text("test-script.sh")');
    
    // Set target path
    await page.fill('input[placeholder="Enter target path"]', '/opt/test-scripts/test-script.sh');
    
    // Set permissions
    await page.fill('input[placeholder="Enter file permissions"]', '755');
    
    // Submit the form
    await page.click('button:has-text("Create")');
    
    // Wait for success message
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Verify the selection is displayed in the list
    await expect(page.locator('.file-selection-list-item:has-text("Linux Test Files")')).toBeVisible();
  }
  
  async function createVM(page) {
    // Navigate to VM page
    await page.goto('/vagrant_vm');
    await page.waitForSelector('.vagrant-vm-container', { timeout: 10000 });
    
    // Mock API responses
    await page.route('**/api/vagrant_vm/', async (route) => {
      const method = route.request().method();
      if (method === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'test-vm-1',
            name: 'Test Linux VM',
            description: 'VM for testing file injection',
            template: 'ubuntu',
            memory_mb: 1024,
            cpus: 1,
            disk_gb: 10,
            status: 'running',
            ip_address: '*************',
            ssh_port: 2222,
            vagrant_id: 'test-vagrant-id',
            created_on: new Date().toISOString(),
            modified_on: null,
            owner_id: 'test-user',
            last_action: 'create',
            last_action_time: new Date().toISOString(),
            error_message: null
          })
        });
      } else if (method === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            items: [
              {
                id: 'test-vm-1',
                name: 'Test Linux VM',
                description: 'VM for testing file injection',
                template: 'ubuntu',
                memory_mb: 1024,
                cpus: 1,
                disk_gb: 10,
                status: 'running',
                ip_address: '*************',
                ssh_port: 2222,
                vagrant_id: 'test-vagrant-id',
                created_on: new Date().toISOString(),
                modified_on: null,
                owner_id: 'test-user',
                last_action: 'create',
                last_action_time: new Date().toISOString(),
                error_message: null
              }
            ],
            total: 1
          })
        });
      } else {
        await route.continue();
      }
    });
    
    // Click create button
    await page.click('button:has-text("Create VM")');
    
    // Fill the form
    await page.waitForSelector('.ant-modal-content');
    await page.fill('input[placeholder="Enter VM name"]', 'Test Linux VM');
    await page.fill('textarea[placeholder="Enter VM description"]', 'VM for testing file injection');
    
    // Select template
    await page.click('.ant-select-selector');
    await page.click('.ant-select-item:has-text("ubuntu")');
    
    // Set resources
    await page.fill('input[type="number"][placeholder="Memory (MB)"]', '1024');
    await page.fill('input[type="number"][placeholder="CPUs"]', '1');
    await page.fill('input[type="number"][placeholder="Disk (GB)"]', '10');
    
    // Submit the form
    await page.click('button:has-text("Create")');
    
    // Wait for success message
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Verify the VM is displayed in the list
    await expect(page.locator('.vagrant-vm-list-item:has-text("Test Linux VM")')).toBeVisible();
    await expect(page.locator('tag:has-text("RUNNING")')).toBeVisible();
  }
  
  async function injectFilesIntoVM(page) {
    // Navigate to VM injection page
    await page.goto('/vm_injection');
    await page.waitForSelector('.vm-injection-container', { timeout: 10000 });
    
    // Mock API responses
    await page.route('**/api/vagrant_vm/', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [
            {
              id: 'test-vm-1',
              name: 'Test Linux VM',
              status: 'running'
            }
          ],
          total: 1
        })
      });
    });
    
    await page.route('**/api/file_selection/', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [
            {
              id: 'test-selection-1',
              name: 'Linux Test Files',
              file_upload_id: 'test-file-1',
              target_path: '/opt/test-scripts/test-script.sh'
            }
          ],
          total: 1
        })
      });
    });
    
    await page.route('**/api/vm_injection/', async (route) => {
      const method = route.request().method();
      if (method === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'test-injection-1',
            vagrant_vm_id: 'test-vm-1',
            file_selection_id: 'test-selection-1',
            description: 'Test file injection',
            status: 'pending',
            additional_command: 'chmod +x /opt/test-scripts/test-script.sh && /opt/test-scripts/test-script.sh',
            error_message: null,
            created_on: new Date().toISOString(),
            modified_on: null,
            completed_on: null,
            owner_id: 'test-user'
          })
        });
      } else if (method === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            items: [
              {
                id: 'test-injection-1',
                vagrant_vm_id: 'test-vm-1',
                file_selection_id: 'test-selection-1',
                description: 'Test file injection',
                status: 'pending',
                additional_command: 'chmod +x /opt/test-scripts/test-script.sh && /opt/test-scripts/test-script.sh',
                error_message: null,
                created_on: new Date().toISOString(),
                modified_on: null,
                completed_on: null,
                owner_id: 'test-user',
                vm_info: {
                  id: 'test-vm-1',
                  name: 'Test Linux VM',
                  status: 'running',
                  ip_address: '*************'
                },
                file_info: {
                  id: 'test-file-1',
                  filename: 'test-script.sh',
                  file_size: 256,
                  content_type: 'text/plain',
                  download_url: '/api/file_upload/test-file-1/download'
                }
              }
            ],
            total: 1
          })
        });
      } else {
        await route.continue();
      }
    });
    
    // Click create button
    await page.click('button:has-text("Create Injection")');
    
    // Fill the form
    await page.waitForSelector('.ant-modal-content');
    
    // Select VM
    await page.click('.ant-select-selector:has-text("Select VM")');
    await page.click('.ant-select-item:has-text("Test Linux VM")');
    
    // Select file selection
    await page.click('.ant-select-selector:has-text("Select File")');
    await page.click('.ant-select-item:has-text("Linux Test Files")');
    
    // Add description
    await page.fill('textarea[placeholder="Enter injection description"]', 'Test file injection');
    
    // Add command to run after injection
    await page.fill('textarea[placeholder="Enter command to run after injection"]', 'chmod +x /opt/test-scripts/test-script.sh && /opt/test-scripts/test-script.sh');
    
    // Submit the form
    await page.click('button:has-text("Create")');
    
    // Wait for success message
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Verify the injection is displayed in the list
    await expect(page.locator('.vm-injection-table')).toContainText('test-script.sh');
    await expect(page.locator('.vm-injection-table')).toContainText('PENDING');
  }
  
  async function verifyInjectionStatus(page) {
    // Navigate to VM status page
    await page.goto('/vm_status');
    await page.waitForSelector('.vm-status-container', { timeout: 10000 });
    
    // Mock API responses
    await page.route('**/api/vagrant_vm/', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [
            {
              id: 'test-vm-1',
              name: 'Test Linux VM',
              status: 'running',
              description: 'VM for testing file injection',
              template: 'ubuntu',
              memory_mb: 1024,
              cpus: 1,
              disk_gb: 10,
              ip_address: '*************',
              ssh_port: 2222,
              created_on: new Date().toISOString()
            }
          ],
          total: 1
        })
      });
    });
    
    await page.route('**/api/vagrant_vm/test-vm-1', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 'test-vm-1',
          name: 'Test Linux VM',
          status: 'running',
          description: 'VM for testing file injection',
          template: 'ubuntu',
          memory_mb: 1024,
          cpus: 1,
          disk_gb: 10,
          ip_address: '*************',
          ssh_port: 2222,
          vagrant_id: 'test-vagrant-id',
          created_on: new Date().toISOString(),
          modified_on: null,
          owner_id: 'test-user',
          last_action: 'create',
          last_action_time: new Date().toISOString(),
          error_message: null
        })
      });
    });
    
    await page.route('**/api/vm_injection/**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [
            {
              id: 'test-injection-1',
              vagrant_vm_id: 'test-vm-1',
              file_selection_id: 'test-selection-1',
              description: 'Test file injection',
              status: 'completed',
              additional_command: 'chmod +x /opt/test-scripts/test-script.sh && /opt/test-scripts/test-script.sh',
              error_message: null,
              created_on: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
              modified_on: new Date(Date.now() - 200000).toISOString(), // 3.33 minutes ago
              completed_on: new Date(Date.now() - 200000).toISOString(), // 3.33 minutes ago
              owner_id: 'test-user',
              file_info: {
                id: 'test-file-1',
                filename: 'test-script.sh',
                file_size: 256,
                content_type: 'text/plain',
                download_url: '/api/file_upload/test-file-1/download'
              }
            }
          ],
          total: 1
        })
      });
    });
    
    await page.route('**/api/vagrant_vm/test-vm-1/logs', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          logs: [
            {
              timestamp: new Date(Date.now() - 300000).toISOString(),
              level: 'INFO',
              message: 'Starting file injection process'
            },
            {
              timestamp: new Date(Date.now() - 250000).toISOString(),
              level: 'INFO',
              message: 'Copying file test-script.sh to /opt/test-scripts/test-script.sh'
            },
            {
              timestamp: new Date(Date.now() - 240000).toISOString(),
              level: 'INFO',
              message: 'Setting file permissions to 755'
            },
            {
              timestamp: new Date(Date.now() - 230000).toISOString(),
              level: 'INFO',
              message: 'Executing command: chmod +x /opt/test-scripts/test-script.sh && /opt/test-scripts/test-script.sh'
            },
            {
              timestamp: new Date(Date.now() - 220000).toISOString(),
              level: 'INFO',
              message: 'Command output: TurdParty Test Script'
            },
            {
              timestamp: new Date(Date.now() - 210000).toISOString(),
              level: 'INFO',
              message: 'Command output: Running on test-linux-vm'
            },
            {
              timestamp: new Date(Date.now() - 200000).toISOString(),
              level: 'INFO',
              message: 'Command output: Test completed successfully'
            },
            {
              timestamp: new Date(Date.now() - 200000).toISOString(),
              level: 'INFO',
              message: 'File injection completed successfully'
            }
          ]
        })
      });
    });
    
    await page.route('**/api/vagrant_vm/test-vm-1/resources', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          cpu_percent: 15.5,
          memory_used_mb: 512,
          memory_total_mb: 1024,
          disk_used_gb: 3.5,
          disk_total_gb: 10
        })
      });
    });
    
    // Wait for VM list to load and click on the VM
    await page.waitForSelector('.vm-list-item');
    await page.click('.vm-list-item:has-text("Test Linux VM")');
    
    // Check VM details
    await expect(page.locator('.vm-status-title:has-text("Test Linux VM")')).toBeVisible();
    await expect(page.locator('tag:has-text("RUNNING")')).toBeVisible();
    
    // Switch to Injections tab
    await page.click('div[role="tab"]:has-text("Injections")');
    
    // Verify injection details
    await expect(page.locator('.vm-status-injections')).toContainText('test-script.sh');
    await expect(page.locator('.vm-status-injections')).toContainText('COMPLETED');
    
    // Switch to Logs tab
    await page.click('div[role="tab"]:has-text("Logs")');
    
    // Verify logs
    await expect(page.locator('.vm-status-logs')).toContainText('File injection completed successfully');
    await expect(page.locator('.vm-status-logs')).toContainText('Test completed successfully');
  }
}); 