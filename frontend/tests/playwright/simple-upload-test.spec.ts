import { test, expect } from '@playwright/test';

test.describe('File Upload Simple Test', () => {
  test('should make a basic API request', async ({ request }) => {
    // Test the health endpoint
    const response = await request.get('/api/v1/health/');
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.status).toBe('ok');
  });

  test('API should be reachable at localhost', async ({ request }) => {
    const response = await request.get('http://localhost:3050/api/v1/health/');
    expect(response.ok()).toBeTruthy();
  });
}); 