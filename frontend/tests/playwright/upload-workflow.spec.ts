import { test, expect } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

test.describe('File Upload Workflow Tests', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  // Create temporary test files and directories before all tests
  let tempDir: string;
  let singleFilePath: string;
  let multipleFilesDir: string;
  let multipleFilePaths: string[] = [];
  
  test.beforeAll(async () => {
    // Create a temporary directory for test files
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'upload-test-'));
    
    // Create a single test file
    singleFilePath = path.join(tempDir, 'test-file.txt');
    fs.writeFileSync(singleFilePath, 'This is a test file for upload testing');
    
    // Create a directory with multiple files
    multipleFilesDir = path.join(tempDir, 'test-folder');
    fs.mkdirSync(multipleFilesDir, { recursive: true });
    
    // Create multiple files in the directory
    const fileNames = ['file1.txt', 'file2.txt', 'file3.txt'];
    fileNames.forEach((fileName, index) => {
      const filePath = path.join(multipleFilesDir, fileName);
      fs.writeFileSync(filePath, `This is test file ${index + 1} for folder upload testing`);
      multipleFilePaths.push(filePath);
    });
    
    // Create a nested directory with files
    const nestedDir = path.join(multipleFilesDir, 'nested');
    fs.mkdirSync(nestedDir, { recursive: true });
    
    // Create files in the nested directory
    const nestedFileNames = ['nested1.txt', 'nested2.txt'];
    nestedFileNames.forEach((fileName, index) => {
      const filePath = path.join(nestedDir, fileName);
      fs.writeFileSync(filePath, `This is nested test file ${index + 1} for folder upload testing`);
      multipleFilePaths.push(filePath);
    });
  });
  
  test.afterAll(async () => {
    // Clean up temporary files and directories
    fs.rmSync(tempDir, { recursive: true, force: true });
  });
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the file upload page
    await page.goto('/file_upload');
    
    // Wait for the page to load
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
  });
  
  test('should upload a single file and verify it appears in the list', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Set file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(singleFilePath);
    
    // Add description
    const description = 'Single file upload test';
    await page.fill('textarea[placeholder="Enter file description"]', description);
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Wait for the upload to complete and modal to close
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).not.toBeVisible({ timeout: 10000 });
    
    // Check if success message appears
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Check if the file appears in the table
    const fileName = path.basename(singleFilePath);
    await expect(page.locator(`td:has-text("${fileName}")`)).toBeVisible({ timeout: 10000 });
    
    // Verify the description is displayed
    await expect(page.locator(`td:has-text("${description}")`)).toBeVisible();
    
    // Verify file size is displayed
    const fileSize = fs.statSync(singleFilePath).size;
    const fileSizeText = fileSize < 1024 ? `${fileSize} B` : `${Math.round(fileSize / 1024 * 10) / 10} KB`;
    await expect(page.locator(`td:has-text("${fileSizeText}")`)).toBeVisible();
  });
  
  test('should upload multiple files as a folder and verify they appear in the list', async ({ page }) => {
    // Click the upload button
    await page.click('button:has-text("Upload File")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    // Switch to folder upload tab if available
    const folderTabSelector = '.ant-tabs-tab:has-text("Upload Folder")';
    if (await page.locator(folderTabSelector).count() > 0) {
      await page.click(folderTabSelector);
    }
    
    // Set folder input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    
    // Set multiple files
    await fileChooser.setFiles(multipleFilePaths);
    
    // Add folder name and description
    const folderName = 'test-folder';
    const description = 'Multiple files upload test';
    
    // If there's a folder name input, fill it
    const folderNameSelector = 'input[placeholder="Enter folder name"]';
    if (await page.locator(folderNameSelector).count() > 0) {
      await page.fill(folderNameSelector, folderName);
    }
    
    await page.fill('textarea[placeholder="Enter file description"]', description);
    
    // Submit the form
    await page.click('button:has-text("Upload")');
    
    // Wait for the upload to complete and modal to close
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).not.toBeVisible({ timeout: 15000 });
    
    // Check if success message appears
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Check if at least one of the files appears in the table
    const firstFileName = path.basename(multipleFilePaths[0]);
    await expect(page.locator(`td:has-text("${firstFileName}")`)).toBeVisible({ timeout: 10000 });
    
    // Count the number of uploaded files (should match the number of files we uploaded)
    // This might need adjustment based on how the UI displays folder uploads
    const uploadedFilesCount = await page.locator('.ant-table-row').count();
    expect(uploadedFilesCount).toBeGreaterThanOrEqual(multipleFilePaths.length);
  });
  
  test('should select files for VM injection', async ({ page }) => {
    // Navigate to the file selection page
    await page.goto('/file_selection');
    
    // Wait for the page to load
    await page.waitForSelector('.file-selection-container', { timeout: 10000 });
    
    // Click the create selection button
    await page.click('button:has-text("Create Selection")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Create File Selection")')).toBeVisible();
    
    // Fill in the selection form
    await page.fill('input[placeholder="Enter selection name"]', 'Test Selection');
    await page.fill('textarea[placeholder="Enter selection description"]', 'Test selection for VM injection');
    
    // Select a file from the dropdown
    await page.click('.ant-select-selector');
    await page.waitForSelector('.ant-select-dropdown');
    
    // Click the first file in the dropdown
    await page.click('.ant-select-item');
    
    // Set target path
    await page.fill('input[placeholder="Enter target path"]', '/tmp/test-file.txt');
    
    // Set permissions
    await page.fill('input[placeholder="Enter file permissions"]', '0644');
    
    // Submit the form
    await page.click('button:has-text("Create")');
    
    // Wait for the creation to complete and modal to close
    await expect(page.locator('.ant-modal-title:has-text("Create File Selection")')).not.toBeVisible({ timeout: 10000 });
    
    // Check if success message appears
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Check if the selection appears in the table
    await expect(page.locator('td:has-text("Test Selection")')).toBeVisible({ timeout: 10000 });
    
    // Verify target path is displayed
    await expect(page.locator('td:has-text("/tmp/test-file.txt")')).toBeVisible();
    
    // Verify permissions are displayed
    await expect(page.locator('td:has-text("0644")')).toBeVisible();
  });
  
  test('should test the complete workflow from upload to VM injection', async ({ page }) => {
    // Step 1: Upload a file
    await page.goto('/file_upload');
    await page.waitForSelector('.file-upload-container', { timeout: 10000 });
    
    await page.click('button:has-text("Upload File")');
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
    
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.ant-upload');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(singleFilePath);
    
    await page.fill('textarea[placeholder="Enter file description"]', 'Workflow test file');
    await page.click('button:has-text("Upload")');
    
    await expect(page.locator('.ant-modal-title:has-text("Upload File")')).not.toBeVisible({ timeout: 10000 });
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Step 2: Create a file selection
    await page.goto('/file_selection');
    await page.waitForSelector('.file-selection-container', { timeout: 10000 });
    
    await page.click('button:has-text("Create Selection")');
    await expect(page.locator('.ant-modal-title:has-text("Create File Selection")')).toBeVisible();
    
    await page.fill('input[placeholder="Enter selection name"]', 'Workflow Test Selection');
    await page.fill('textarea[placeholder="Enter selection description"]', 'Selection for workflow test');
    
    await page.click('.ant-select-selector');
    await page.waitForSelector('.ant-select-dropdown');
    await page.click('.ant-select-item');
    
    await page.fill('input[placeholder="Enter target path"]', '/tmp/workflow-test.txt');
    await page.fill('input[placeholder="Enter file permissions"]', '0644');
    
    await page.click('button:has-text("Create")');
    await expect(page.locator('.ant-modal-title:has-text("Create File Selection")')).not.toBeVisible({ timeout: 10000 });
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Step 3: Navigate to VM page and create a VM (if not already created)
    await page.goto('/vagrant_vm');
    await page.waitForSelector('.vagrant-vm-container', { timeout: 10000 });
    
    // Check if there's already a VM
    const vmCount = await page.locator('.ant-table-row').count();
    
    if (vmCount === 0) {
      // Create a new VM
      await page.click('button:has-text("Create VM")');
      await expect(page.locator('.ant-modal-title:has-text("Create Vagrant VM")')).toBeVisible();
      
      await page.fill('input[placeholder="Enter VM name"]', 'Test VM');
      await page.fill('textarea[placeholder="Enter VM description"]', 'VM for workflow test');
      
      // Select a template
      await page.click('.ant-select-selector');
      await page.waitForSelector('.ant-select-dropdown');
      await page.click('.ant-select-item');
      
      await page.click('button:has-text("Create")');
      await expect(page.locator('.ant-modal-title:has-text("Create Vagrant VM")')).not.toBeVisible({ timeout: 10000 });
      await expect(page.locator('.ant-message-success')).toBeVisible();
    }
    
    // Step 4: Create a VM injection
    await page.goto('/vm_injection');
    await page.waitForSelector('.vm-injection-container', { timeout: 10000 });
    
    await page.click('button:has-text("Create Injection")');
    await expect(page.locator('.ant-modal-title:has-text("Create VM Injection")')).toBeVisible();
    
    // Select VM
    await page.click('.ant-select-selector:has-text("Select VM")');
    await page.waitForSelector('.ant-select-dropdown');
    await page.click('.ant-select-item');
    
    // Select file selection
    await page.click('.ant-select-selector:has-text("Select File")');
    await page.waitForSelector('.ant-select-dropdown');
    await page.click('.ant-select-item:has-text("Workflow Test Selection")');
    
    await page.fill('textarea[placeholder="Enter injection description"]', 'Workflow test injection');
    
    await page.click('button:has-text("Create")');
    await expect(page.locator('.ant-modal-title:has-text("Create VM Injection")')).not.toBeVisible({ timeout: 10000 });
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Verify injection appears in the table
    await expect(page.locator('td:has-text("Workflow test injection")')).toBeVisible({ timeout: 10000 });
  });
}); 