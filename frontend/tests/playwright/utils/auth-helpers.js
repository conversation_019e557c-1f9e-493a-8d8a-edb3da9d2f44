// @ts-check

/**
 * Login helper function for authentication in tests
 * @param {import('@playwright/test').Page} page 
 * @param {string} username 
 * @param {string} password 
 */
async function login(page, username, password) {
  // Navigate to login page if not already there
  if (!page.url().includes('/login')) {
    await page.goto('/login');
  }
  
  // Fill login form
  await page.fill('input[name="username"], input[placeholder="Username"]', username);
  await page.fill('input[name="password"], input[placeholder="Password"]', password);
  
  // Submit form
  await page.click('button[type="submit"]');
  
  // Wait for navigation to complete
  await page.waitForLoadState('networkidle');
  
  // Verify successful login
  // This could be checking for user avatar, profile name, or dashboard elements
  const isLoggedIn = await page.isVisible('.user-avatar, .user-profile, .logout-button');
  if (!isLoggedIn) {
    console.warn('Login may have failed, UI elements indicating logged-in state not found');
  }
}

/**
 * Logout helper function for tests
 * @param {import('@playwright/test').Page} page 
 */
async function logout(page) {
  // Click on logout button or user menu first if needed
  const userMenu = await page.$('.user-menu, .user-dropdown');
  if (userMenu) {
    await userMenu.click();
    await page.waitForTimeout(500);
  }
  
  // Click logout button
  await page.click('button:has-text("Logout"), .logout-button, a:has-text("Logout")');
  
  // Wait for logout to complete
  await page.waitForLoadState('networkidle');
  
  // Verify logout successful
  const loginForm = await page.$('form:has(input[name="username"]), form:has(input[name="password"])');
  if (!loginForm) {
    console.warn('Logout may have failed, login form not visible after logout');
  }
}

module.exports = {
  login,
  logout
}; 