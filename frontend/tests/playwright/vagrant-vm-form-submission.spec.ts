import { test, expect, Page } from '@playwright/test';
import fs from 'fs';
import path from 'path';

// Ensure logs directory exists
const logsDir = path.join(__dirname, '..', '..', '..', 'tests', 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Set up logging
const logFile = path.join(logsDir, 'vagrant_vm_form_submission.log');
const logger = {
  log: (message: string) => {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] INFO: ${message}\n`;
    fs.appendFileSync(logFile, logMessage);
    console.log(message);
  },
  error: (message: string) => {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ERROR: ${message}\n`;
    fs.appendFileSync(logFile, logMessage);
    console.error(message);
  }
};

// Clear log file at the beginning of the test run
fs.writeFileSync(logFile, '');

// Test data
const VALID_VM = {
  name: 'test-form-vm',
  box: 'ubuntu/focal64',
  cpu: 2,
  memory: 2048,
  description: 'Test VM for form submission tests'
};

// Define a type that includes our skip properties
type VMFormData = Partial<typeof VALID_VM> & {
  skipName?: boolean;
  skipBox?: boolean;
  skipCPU?: boolean;
  skipMemory?: boolean;
};

// Helper function to fill the VM creation form
async function fillVMForm(page: Page, vmData: VMFormData = {}) {
  const data = { ...VALID_VM, ...vmData };
  
  // Fill the form fields
  if ('name' in vmData || !vmData.skipName) {
    await page.getByLabel('VM Name').fill(data.name);
  }
  
  if ('box' in vmData || !vmData.skipBox) {
    await page.getByLabel('Box').fill(data.box);
  }
  
  if ('cpu' in vmData || !vmData.skipCPU) {
    await page.getByLabel('CPU Cores').fill(data.cpu.toString());
  }
  
  if ('memory' in vmData || !vmData.skipMemory) {
    await page.getByLabel('Memory (MB)').fill(data.memory.toString());
  }
  
  if (data.description) {
    await page.getByLabel('Description').fill(data.description);
  }
}

// Helper function to submit the form and check for validation errors
async function submitFormAndCheckValidation(page: Page, expectedErrorText?: string) {
  // Submit the form
  await page.getByRole('button', { name: 'Create VM' }).click();
  
  if (expectedErrorText) {
    // Check for validation error
    const errorElement = page.locator('.error-message, .validation-error');
    await expect(errorElement).toBeVisible();
    
    if (typeof expectedErrorText === 'string') {
      await expect(errorElement).toContainText(expectedErrorText);
    }
    
    return false;
  } else {
    // Check for successful submission
    await page.waitForURL('**/vagrant/vms/*');
    return true;
  }
}

test.describe('Vagrant VM Form Submission', () => {
  test.beforeEach(async ({ page }) => {
    logger.log('Starting test: Navigating to Vagrant VM page');
    await page.goto('/vagrant-vms');
    
    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Vagrant VMs")');
    logger.log('Successfully loaded Vagrant VM page');
  });

  test('should display the VM creation form', async ({ page }) => {
    logger.log('Testing VM creation form display');
    
    // Click the "Create VM" button
    await page.click('button:has-text("Create VM")');
    
    // Verify the form is displayed
    const formVisible = await page.isVisible('form[data-testid="vagrant-vm-form"]');
    expect(formVisible).toBeTruthy();
    
    // Verify form fields
    const nameFieldVisible = await page.isVisible('input[name="name"]');
    const boxFieldVisible = await page.isVisible('select[name="box"]');
    const cpuFieldVisible = await page.isVisible('input[name="cpu"]');
    const memoryFieldVisible = await page.isVisible('input[name="memory"]');
    
    expect(nameFieldVisible).toBeTruthy();
    expect(boxFieldVisible).toBeTruthy();
    expect(cpuFieldVisible).toBeTruthy();
    expect(memoryFieldVisible).toBeTruthy();
    
    logger.log('VM creation form displayed successfully with all required fields');
  });

  test('should validate form fields', async ({ page }) => {
    logger.log('Testing form field validation');
    
    // Click the "Create VM" button
    await page.click('button:has-text("Create VM")');
    
    // Try to submit the form without filling in required fields
    await page.click('button[type="submit"]');
    
    // Check for validation error messages
    const nameError = await page.isVisible('text=Name is required');
    const boxError = await page.isVisible('text=Box is required');
    
    expect(nameError).toBeTruthy();
    expect(boxError).toBeTruthy();
    
    logger.log('Form validation working correctly for required fields');
  });

  test('should submit the form successfully', async ({ page }) => {
    logger.log('Testing successful form submission');
    
    // Click the "Create VM" button
    await page.click('button:has-text("Create VM")');
    
    // Fill in the form
    await page.fill('input[name="name"]', 'test-vm');
    await page.selectOption('select[name="box"]', { label: 'ubuntu/focal64' });
    await page.fill('input[name="cpu"]', '2');
    await page.fill('input[name="memory"]', '2048');
    
    // Mock the API response
    await page.route('**/api/vagrant/vms', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 'test-vm-id',
          name: 'test-vm',
          box: 'ubuntu/focal64',
          cpu: 2,
          memory: 2048,
          status: 'CREATING'
        })
      });
    });
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Check for success message
    await page.waitForSelector('text=VM creation started');
    const successMessage = await page.isVisible('text=VM creation started');
    expect(successMessage).toBeTruthy();
    
    logger.log('Form submitted successfully with success message displayed');
  });

  test('should handle API errors during submission', async ({ page }) => {
    logger.log('Testing API error handling during form submission');
    
    // Click the "Create VM" button
    await page.click('button:has-text("Create VM")');
    
    // Fill in the form
    await page.fill('input[name="name"]', 'error-vm');
    await page.selectOption('select[name="box"]', { label: 'ubuntu/focal64' });
    await page.fill('input[name="cpu"]', '2');
    await page.fill('input[name="memory"]', '2048');
    
    // Mock the API error response
    await page.route('**/api/vagrant/vms', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          detail: 'Internal server error'
        })
      });
    });
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Check for error message
    await page.waitForSelector('text=Failed to create VM');
    const errorMessage = await page.isVisible('text=Failed to create VM');
    expect(errorMessage).toBeTruthy();
    
    logger.log('API error handled correctly with error message displayed');
  });

  test('should handle validation errors from API', async ({ page }) => {
    logger.log('Testing API validation error handling');
    
    // Click the "Create VM" button
    await page.click('button:has-text("Create VM")');
    
    // Fill in the form with a duplicate name
    await page.fill('input[name="name"]', 'duplicate-vm');
    await page.selectOption('select[name="box"]', { label: 'ubuntu/focal64' });
    await page.fill('input[name="cpu"]', '2');
    await page.fill('input[name="memory"]', '2048');
    
    // Mock the API validation error response
    await page.route('**/api/vagrant/vms', route => {
      route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          detail: 'VM with this name already exists'
        })
      });
    });
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Check for validation error message
    await page.waitForSelector('text=VM with this name already exists');
    const validationError = await page.isVisible('text=VM with this name already exists');
    expect(validationError).toBeTruthy();
    
    logger.log('API validation error handled correctly with error message displayed');
  });

  test('should cancel form submission', async ({ page }) => {
    logger.log('Testing form cancellation');
    
    // Click the "Create VM" button
    await page.click('button:has-text("Create VM")');
    
    // Fill in some form fields
    await page.fill('input[name="name"]', 'cancelled-vm');
    
    // Click the cancel button
    await page.click('button:has-text("Cancel")');
    
    // Verify the form is no longer visible
    const formVisible = await page.isVisible('form[data-testid="vagrant-vm-form"]');
    expect(formVisible).toBeFalsy();
    
    logger.log('Form cancellation working correctly');
  });

  test('should list existing VMs', async ({ page }) => {
    logger.log('Testing VM listing');
    
    // Mock the API response for VM listing
    await page.route('**/api/vagrant/vms', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'vm-1',
            name: 'test-vm-1',
            box: 'ubuntu/focal64',
            cpu: 2,
            memory: 2048,
            status: 'RUNNING'
          },
          {
            id: 'vm-2',
            name: 'test-vm-2',
            box: 'ubuntu/bionic64',
            cpu: 1,
            memory: 1024,
            status: 'STOPPED'
          }
        ])
      });
    });
    
    // Refresh the page to trigger the VM listing
    await page.reload();
    
    // Check if VMs are listed
    await page.waitForSelector('text=test-vm-1');
    const vm1Visible = await page.isVisible('text=test-vm-1');
    const vm2Visible = await page.isVisible('text=test-vm-2');
    
    expect(vm1Visible).toBeTruthy();
    expect(vm2Visible).toBeTruthy();
    
    // Check if status is displayed correctly
    const runningStatus = await page.isVisible('text=RUNNING');
    const stoppedStatus = await page.isVisible('text=STOPPED');
    
    expect(runningStatus).toBeTruthy();
    expect(stoppedStatus).toBeTruthy();
    
    logger.log('VM listing displayed correctly with proper statuses');
  });

  test('should handle VM action buttons', async ({ page }) => {
    logger.log('Testing VM action buttons');
    
    // Mock the API response for VM listing
    await page.route('**/api/vagrant/vms', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'vm-1',
            name: 'test-vm-1',
            box: 'ubuntu/focal64',
            cpu: 2,
            memory: 2048,
            status: 'RUNNING'
          }
        ])
      });
    });
    
    // Refresh the page to trigger the VM listing
    await page.reload();
    
    // Wait for the VM to be displayed
    await page.waitForSelector('text=test-vm-1');
    
    // Mock the API response for VM stop action
    await page.route('**/api/vagrant/vms/vm-1/halt', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 'vm-1',
          name: 'test-vm-1',
          status: 'STOPPING'
        })
      });
    });
    
    // Click the stop button
    await page.click('button[aria-label="Stop VM"]');
    
    // Check for confirmation dialog
    await page.waitForSelector('text=Are you sure you want to stop this VM?');
    
    // Confirm the action
    await page.click('button:has-text("Confirm")');
    
    // Check for success message
    await page.waitForSelector('text=VM is stopping');
    const stoppingMessage = await page.isVisible('text=VM is stopping');
    expect(stoppingMessage).toBeTruthy();
    
    logger.log('VM stop action handled correctly with confirmation dialog and success message');
  });

  test('should handle VM deletion', async ({ page }) => {
    logger.log('Testing VM deletion');
    
    // Mock the API response for VM listing
    await page.route('**/api/vagrant/vms', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'vm-1',
            name: 'test-vm-1',
            box: 'ubuntu/focal64',
            cpu: 2,
            memory: 2048,
            status: 'STOPPED'
          }
        ])
      });
    });
    
    // Refresh the page to trigger the VM listing
    await page.reload();
    
    // Wait for the VM to be displayed
    await page.waitForSelector('text=test-vm-1');
    
    // Mock the API response for VM delete action
    await page.route('**/api/vagrant/vms/vm-1', route => {
      if (route.request().method() === 'DELETE') {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            message: 'VM deleted successfully'
          })
        });
      } else {
        route.continue();
      }
    });
    
    // Click the delete button
    await page.click('button[aria-label="Delete VM"]');
    
    // Check for confirmation dialog
    await page.waitForSelector('text=Are you sure you want to delete this VM?');
    
    // Confirm the action
    await page.click('button:has-text("Confirm")');
    
    // Check for success message
    await page.waitForSelector('text=VM deleted successfully');
    const deletedMessage = await page.isVisible('text=VM deleted successfully');
    expect(deletedMessage).toBeTruthy();
    
    logger.log('VM deletion handled correctly with confirmation dialog and success message');
  });

  test('should handle page refresh during VM creation', async ({ page }) => {
    logger.log('Testing page refresh during VM creation');
    
    // Click the "Create VM" button
    await page.click('button:has-text("Create VM")');
    
    // Fill in the form
    await page.fill('input[name="name"]', 'refresh-test-vm');
    await page.selectOption('select[name="box"]', { label: 'ubuntu/focal64' });
    await page.fill('input[name="cpu"]', '2');
    await page.fill('input[name="memory"]', '2048');
    
    // Mock the API response
    await page.route('**/api/vagrant/vms', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 'refresh-vm-id',
          name: 'refresh-test-vm',
          box: 'ubuntu/focal64',
          cpu: 2,
          memory: 2048,
          status: 'CREATING'
        })
      });
    });
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Check for success message
    await page.waitForSelector('text=VM creation started');
    
    // Mock the API response for VM listing after refresh
    await page.route('**/api/vagrant/vms', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'refresh-vm-id',
            name: 'refresh-test-vm',
            box: 'ubuntu/focal64',
            cpu: 2,
            memory: 2048,
            status: 'CREATING'
          }
        ])
      });
    });
    
    // Simulate page refresh
    await page.reload();
    
    // Check if the VM is still listed with the correct status
    await page.waitForSelector('text=refresh-test-vm');
    const vmVisible = await page.isVisible('text=refresh-test-vm');
    const creatingStatus = await page.isVisible('text=CREATING');
    
    expect(vmVisible).toBeTruthy();
    expect(creatingStatus).toBeTruthy();
    
    logger.log('Page refresh during VM creation handled correctly, VM status preserved');
  });

  test('should create VM with valid data', async ({ page }) => {
    // Fill the form with valid data
    await fillVMForm(page);
    
    // Submit the form and check for success
    const success = await submitFormAndCheckValidation(page);
    expect(success).toBe(true);
    
    // Check that the VM details page is displayed
    await expect(page.getByText(VALID_VM.name)).toBeVisible();
    await expect(page.getByText(VALID_VM.box)).toBeVisible();
    await expect(page.getByText(`${VALID_VM.cpu} cores`)).toBeVisible();
    await expect(page.getByText(`${VALID_VM.memory} MB`)).toBeVisible();
    
    // Cleanup: Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
  });
  
  test('should validate required fields', async ({ page }) => {
    // Submit empty form
    await submitFormAndCheckValidation(page, 'required');
    
    // Test each required field individually
    
    // Missing name
    await fillVMForm(page, { skipName: true });
    await submitFormAndCheckValidation(page, 'Name is required');
    
    // Missing box
    await page.reload();
    await fillVMForm(page, { skipBox: true });
    await submitFormAndCheckValidation(page, 'Box is required');
    
    // Missing CPU
    await page.reload();
    await fillVMForm(page, { skipCPU: true });
    await submitFormAndCheckValidation(page, 'CPU is required');
    
    // Missing memory
    await page.reload();
    await fillVMForm(page, { skipMemory: true });
    await submitFormAndCheckValidation(page, 'Memory is required');
  });
  
  test('should validate VM name format', async ({ page }) => {
    // Test invalid VM names
    const invalidNames = [
      { name: 'invalid name with spaces', error: 'spaces' },
      { name: 'invalid@name', error: 'special characters' },
      { name: 'invalid_name!', error: 'special characters' },
      { name: 'invalid;name', error: 'special characters' },
      { name: 'a', error: 'too short' }
    ];
    
    for (const { name, error } of invalidNames) {
      await fillVMForm(page, { name });
      await submitFormAndCheckValidation(page, error);
      await page.reload();
    }
    
    // Test valid VM names
    const validNames = [
      'valid-name',
      'valid_name',
      'validname123',
      'valid-name-123',
      'valid_name_123'
    ];
    
    for (const name of validNames) {
      await fillVMForm(page, { name });
      const success = await submitFormAndCheckValidation(page);
      expect(success).toBe(true);
      
      // Cleanup: Delete the VM
      await page.getByRole('button', { name: 'Delete' }).click();
      await page.getByRole('button', { name: 'Confirm' }).click();
      await page.waitForURL('/vagrant/vms');
      
      // Navigate back to VM creation page
      await page.goto('/vagrant/vms/new');
    }
  });
  
  test('should validate CPU and memory values', async ({ page }) => {
    // Test invalid CPU values
    const invalidCPUValues = [
      { cpu: 0, error: 'must be greater than 0' },
      { cpu: -1, error: 'must be greater than 0' },
      { cpu: 100, error: 'exceeds maximum' }
    ];
    
    for (const { cpu, error } of invalidCPUValues) {
      await fillVMForm(page, { cpu });
      await submitFormAndCheckValidation(page, error);
      await page.reload();
    }
    
    // Test invalid memory values
    const invalidMemoryValues = [
      { memory: 0, error: 'must be greater than 0' },
      { memory: -1, error: 'must be greater than 0' },
      { memory: 100, error: 'below minimum' },
      { memory: 1000000, error: 'exceeds maximum' }
    ];
    
    for (const { memory, error } of invalidMemoryValues) {
      await fillVMForm(page, { memory });
      await submitFormAndCheckValidation(page, error);
      await page.reload();
    }
    
    // Test valid CPU and memory values
    const validValues = [
      { cpu: 1, memory: 512 },
      { cpu: 2, memory: 1024 },
      { cpu: 4, memory: 4096 },
      { cpu: 8, memory: 8192 }
    ];
    
    for (const { cpu, memory } of validValues) {
      await fillVMForm(page, { cpu, memory });
      const success = await submitFormAndCheckValidation(page);
      expect(success).toBe(true);
      
      // Cleanup: Delete the VM
      await page.getByRole('button', { name: 'Delete' }).click();
      await page.getByRole('button', { name: 'Confirm' }).click();
      await page.waitForURL('/vagrant/vms');
      
      // Navigate back to VM creation page
      await page.goto('/vagrant/vms/new');
    }
  });
  
  test('should validate box format and availability', async ({ page }) => {
    // Test invalid box values
    const invalidBoxValues = [
      { box: 'invalid box', error: 'invalid format' },
      { box: 'invalid/box/extra', error: 'invalid format' },
      { box: 'nonexistent/box', error: 'not found' }
    ];
    
    for (const { box, error } of invalidBoxValues) {
      await fillVMForm(page, { box });
      await submitFormAndCheckValidation(page, error);
      await page.reload();
    }
    
    // Test valid box values
    const validBoxes = [
      'ubuntu/focal64',
      'centos/7',
      'debian/bullseye64'
    ];
    
    for (const box of validBoxes) {
      await fillVMForm(page, { box });
      const success = await submitFormAndCheckValidation(page);
      expect(success).toBe(true);
      
      // Cleanup: Delete the VM
      await page.getByRole('button', { name: 'Delete' }).click();
      await page.getByRole('button', { name: 'Confirm' }).click();
      await page.waitForURL('/vagrant/vms');
      
      // Navigate back to VM creation page
      await page.goto('/vagrant/vms/new');
    }
  });
  
  test('should handle form submission with special characters safely', async ({ page }) => {
    // Test description with special characters
    const specialCharDescriptions = [
      'Description with "quotes"',
      'Description with \'single quotes\'',
      'Description with <tags>',
      'Description with & ampersand',
      'Description with multi\nline\ntext'
    ];
    
    for (const description of specialCharDescriptions) {
      await fillVMForm(page, { description });
      const success = await submitFormAndCheckValidation(page);
      expect(success).toBe(true);
      
      // Check that the description is displayed correctly
      const sanitizedDescription = description.replace(/</g, '&lt;').replace(/>/g, '&gt;');
      const descriptionElement = page.locator('.vm-description');
      const descriptionText = await descriptionElement.innerHTML();
      
      // Check that the description contains the content (allowing for HTML encoding)
      expect(
        descriptionText.includes(sanitizedDescription) || 
        descriptionText.includes(description)
      ).toBeTruthy();
      
      // Cleanup: Delete the VM
      await page.getByRole('button', { name: 'Delete' }).click();
      await page.getByRole('button', { name: 'Confirm' }).click();
      await page.waitForURL('/vagrant/vms');
      
      // Navigate back to VM creation page
      await page.goto('/vagrant/vms/new');
    }
  });
  
  test('should prevent duplicate VM names', async ({ page }) => {
    // Create a VM with a specific name
    const vmName = 'duplicate-test-vm';
    await fillVMForm(page, { name: vmName });
    const success = await submitFormAndCheckValidation(page);
    expect(success).toBe(true);
    
    // Navigate back to VM creation page
    await page.goto('/vagrant/vms/new');
    
    // Try to create another VM with the same name
    await fillVMForm(page, { name: vmName });
    await submitFormAndCheckValidation(page, 'already exists');
    
    // Cleanup: Delete the first VM
    await page.goto('/vagrant/vms');
    await page.getByText(vmName).click();
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
  });
  
  test('should handle form cancellation', async ({ page }) => {
    // Fill the form
    await fillVMForm(page);
    
    // Click the Cancel button
    await page.getByRole('button', { name: 'Cancel' }).click();
    
    // Check that we're back at the VM list
    await page.waitForURL('/vagrant/vms');
    
    // Verify no VM was created
    const vmElements = page.locator('.vm-list-item');
    const vmCount = await vmElements.count();
    const vmNames = [];
    
    for (let i = 0; i < vmCount; i++) {
      const nameElement = vmElements.nth(i).locator('.vm-name');
      const name = await nameElement.textContent() || '';
      vmNames.push(name);
    }
    
    expect(vmNames).not.toContain(VALID_VM.name);
  });
  
  test('should support advanced options', async ({ page }) => {
    // Check if advanced options section exists
    const advancedOptionsButton = page.getByText('Advanced Options');
    const hasAdvancedOptions = await advancedOptionsButton.isVisible();
    
    if (hasAdvancedOptions) {
      // Click to expand advanced options
      await advancedOptionsButton.click();
      
      // Check for advanced fields
      const advancedFields = [
        'Network Type',
        'Port Forwarding',
        'Shared Folders',
        'Provisioning Script'
      ];
      
      for (const field of advancedFields) {
        const fieldElement = page.getByText(field, { exact: false });
        const isVisible = await fieldElement.isVisible();
        
        if (isVisible) {
          // Test with a port forwarding rule
          if (field === 'Port Forwarding') {
            await page.getByRole('button', { name: 'Add Port Forwarding' }).click();
            await page.getByLabel('Host Port').fill('8080');
            await page.getByLabel('Guest Port').fill('80');
          }
          
          // Test with a shared folder
          if (field === 'Shared Folders') {
            await page.getByRole('button', { name: 'Add Shared Folder' }).click();
            await page.getByLabel('Host Path').fill('/tmp');
            await page.getByLabel('Guest Path').fill('/vagrant/shared');
          }
          
          // Test with a provisioning script
          if (field === 'Provisioning Script') {
            await page.getByLabel('Provisioning Script').fill('#!/bin/bash\necho "Hello from provisioning script"');
          }
        }
      }
      
      // Submit the form with advanced options
      await fillVMForm(page);
      const success = await submitFormAndCheckValidation(page);
      expect(success).toBe(true);
      
      // Check that the VM details page shows advanced options
      if (await page.getByText('Port Forwarding').isVisible()) {
        await expect(page.getByText('8080 → 80')).toBeVisible();
      }
      
      if (await page.getByText('Shared Folders').isVisible()) {
        await expect(page.getByText('/tmp → /vagrant/shared')).toBeVisible();
      }
      
      // Cleanup: Delete the VM
      await page.getByRole('button', { name: 'Delete' }).click();
      await page.getByRole('button', { name: 'Confirm' }).click();
      await page.waitForURL('/vagrant/vms');
    } else {
      // Skip this test if advanced options are not available
      test.skip();
    }
  });
  
  test('should handle form with maximum allowed values', async ({ page }) => {
    // Find the maximum allowed values from the form
    const cpuInput = page.getByLabel('CPU Cores');
    const memoryInput = page.getByLabel('Memory (MB)');
    
    const maxCPU = await cpuInput.getAttribute('max') || '16';
    const maxMemory = await memoryInput.getAttribute('max') || '16384';
    
    // Fill the form with maximum values
    await fillVMForm(page, {
      cpu: parseInt(maxCPU),
      memory: parseInt(maxMemory)
    });
    
    // Submit the form
    const success = await submitFormAndCheckValidation(page);
    expect(success).toBe(true);
    
    // Check that the VM details page shows the correct values
    await expect(page.getByText(`${maxCPU} cores`)).toBeVisible();
    await expect(page.getByText(`${maxMemory} MB`)).toBeVisible();
    
    // Cleanup: Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
  });
  
  test('should handle form submission during server errors', async ({ page }) => {
    // Intercept the form submission API call
    await page.route('**/api/vagrant/vms', async (route) => {
      // Simulate a server error
      await route.fulfill({
        status: 500,
        body: JSON.stringify({ error: 'Internal Server Error' })
      });
    });
    
    // Fill and submit the form
    await fillVMForm(page);
    await page.getByRole('button', { name: 'Create VM' }).click();
    
    // Check for error message
    await expect(page.locator('.error-message, .alert-error')).toBeVisible();
    await expect(page.locator('.error-message, .alert-error')).toContainText('error');
    
    // Remove the route interception
    await page.unroute('**/api/vagrant/vms');
  });
  
  test('should handle form submission with extremely long values', async ({ page }) => {
    logger.log('Testing form submission with extremely long values');
    
    // Create extremely long values
    const longName = 'test-vm-' + 'x'.repeat(50);
    const longDescription = 'Description ' + 'x'.repeat(1000);
    
    // Fill the form with long values
    await fillVMForm(page, {
      name: longName.substring(0, 63), // Most systems limit to 63 chars
      description: longDescription
    });
    
    // Submit the form
    const success = await submitFormAndCheckValidation(page);
    
    // Check if submission was successful or if there was a validation error
    if (success) {
      // If successful, verify the values were saved correctly
      await expect(page.locator('.vm-name')).toContainText(longName.substring(0, 63));
      
      // Cleanup: Delete the VM
      await page.getByRole('button', { name: 'Delete' }).click();
      await page.getByRole('button', { name: 'Confirm' }).click();
      await page.waitForURL('/vagrant/vms');
    } else {
      // If there was a validation error, check that it's related to the length
      await expect(page.locator('.error-message, .validation-error')).toContainText('length');
    }
    
    logger.log('Long value test completed');
  });
  
  test('should handle form submission with boundary values', async ({ page }) => {
    logger.log('Testing form submission with boundary values');
    
    // Get min/max values from form attributes
    const cpuInput = page.getByLabel('CPU Cores');
    const memoryInput = page.getByLabel('Memory (MB)');
    
    const minCPU = await cpuInput.getAttribute('min') || '1';
    const minMemory = await memoryInput.getAttribute('min') || '512';
    
    // Test with minimum allowed values
    await fillVMForm(page, {
      cpu: parseInt(minCPU),
      memory: parseInt(minMemory)
    });
    
    // Submit the form
    const success = await submitFormAndCheckValidation(page);
    expect(success).toBe(true);
    
    // Check that the VM details page shows the correct values
    await expect(page.getByText(`${minCPU} cores`)).toBeVisible();
    await expect(page.getByText(`${minMemory} MB`)).toBeVisible();
    
    // Cleanup: Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
    
    logger.log('Boundary value test completed');
  });
  
  test('should handle form submission with non-ASCII characters', async ({ page }) => {
    logger.log('Testing form submission with non-ASCII characters');
    
    // Test with non-ASCII characters in the description
    const nonAsciiDescription = 'Description with non-ASCII characters: 日本語 Español Français Русский 中文';
    
    // Fill the form with non-ASCII description
    await fillVMForm(page, {
      description: nonAsciiDescription
    });
    
    // Submit the form
    const success = await submitFormAndCheckValidation(page);
    
    if (success) {
      // Check that the description is displayed correctly
      const descriptionElement = page.locator('.vm-description');
      const descriptionText = await descriptionElement.innerHTML();
      
      // Check that the description contains at least some of the non-ASCII content
      expect(
        descriptionText.includes('日本語') || 
        descriptionText.includes('Español')
      ).toBeTruthy();
      
      // Cleanup: Delete the VM
      await page.getByRole('button', { name: 'Delete' }).click();
      await page.getByRole('button', { name: 'Confirm' }).click();
      await page.waitForURL('/vagrant/vms');
    } else {
      // If there was a validation error, check that it's related to character encoding
      await expect(page.locator('.error-message, .validation-error')).toContainText('character');
    }
    
    logger.log('Non-ASCII character test completed');
  });
  
  test('should handle form submission with script injection attempts', async ({ page }) => {
    logger.log('Testing form submission with script injection attempts');
    
    // Test with script injection in the description
    const scriptInjection = '<script>alert("XSS")</script>';
    const scriptInjectionDescription = `Description with script: ${scriptInjection}`;
    
    // Fill the form with script injection
    await fillVMForm(page, {
      description: scriptInjectionDescription
    });
    
    // Submit the form
    const success = await submitFormAndCheckValidation(page);
    expect(success).toBe(true);
    
    // Check that the script is not executed but displayed as text
    const descriptionElement = page.locator('.vm-description');
    const descriptionHTML = await descriptionElement.innerHTML();
    
    // The script should be escaped
    expect(descriptionHTML).not.toContain('<script>');
    expect(
      descriptionHTML.includes('&lt;script&gt;') || 
      descriptionHTML.includes('&amp;lt;script&amp;gt;')
    ).toBeTruthy();
    
    // Cleanup: Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
    
    logger.log('Script injection test completed');
  });
  
  test('should handle form submission with SQL injection attempts', async ({ page }) => {
    logger.log('Testing form submission with SQL injection attempts');
    
    // Test with SQL injection in the name
    const sqlInjectionAttempts = [
      "test'; DROP TABLE vms; --",
      "test' OR '1'='1",
      "test\"; SELECT * FROM vms; --"
    ];
    
    for (const sqlInjection of sqlInjectionAttempts) {
      // Fill the form with SQL injection
      await fillVMForm(page, {
        name: sqlInjection
      });
      
      // Submit the form
      await submitFormAndCheckValidation(page, 'invalid');
      
      // Reload the page for the next attempt
      await page.reload();
    }
    
    logger.log('SQL injection test completed');
  });
  
  test('should handle concurrent form submissions', async ({ page, browser }) => {
    logger.log('Testing concurrent form submissions');
    
    // Create a second page context
    const secondContext = await browser.newContext();
    const secondPage = await secondContext.newPage();
    
    // Login on the second page
    await secondPage.goto('/login');
    await secondPage.getByLabel('Username').fill('admin');
    await secondPage.getByLabel('Password').fill('admin');
    await secondPage.getByRole('button', { name: 'Login' }).click();
    await secondPage.waitForURL('/dashboard');
    
    // Navigate to VM creation page on both pages
    await page.goto('/vagrant/vms/new');
    await secondPage.goto('/vagrant/vms/new');
    
    // Fill the form with the same VM name on both pages
    const concurrentVMName = 'concurrent-test-vm';
    await fillVMForm(page, { name: concurrentVMName });
    await secondPage.getByLabel('VM Name').fill(concurrentVMName);
    await secondPage.getByLabel('Box').fill(VALID_VM.box);
    await secondPage.getByLabel('CPU Cores').fill(VALID_VM.cpu.toString());
    await secondPage.getByLabel('Memory (MB)').fill(VALID_VM.memory.toString());
    
    // Submit the form on the first page
    const firstSuccess = await submitFormAndCheckValidation(page);
    expect(firstSuccess).toBe(true);
    
    // Submit the form on the second page (should fail due to duplicate name)
    await secondPage.getByRole('button', { name: 'Create VM' }).click();
    const errorElement = secondPage.locator('.error-message, .validation-error');
    await expect(errorElement).toBeVisible();
    await expect(errorElement).toContainText('already exists');
    
    // Cleanup: Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
    
    // Close the second context
    await secondContext.close();
    
    logger.log('Concurrent submission test completed');
  });
  
  test('should handle form submission with network disconnection', async ({ page, context }) => {
    logger.log('Testing form submission with network disconnection');
    
    // Fill the form
    await fillVMForm(page);
    
    // Simulate network disconnection
    await context.setOffline(true);
    
    // Submit the form
    await page.getByRole('button', { name: 'Create VM' }).click();
    
    // Check for network error message
    await expect(page.locator('.error-message, .network-error')).toBeVisible();
    
    // Restore network connection
    await context.setOffline(false);
    
    // Try submitting again
    await page.getByRole('button', { name: 'Create VM' }).click();
    
    // Check for successful submission
    await page.waitForURL('**/vagrant/vms/*');
    
    // Cleanup: Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
    
    logger.log('Network disconnection test completed');
  });
  
  test('should handle form submission with browser refresh', async ({ page }) => {
    logger.log('Testing form submission with browser refresh');
    
    // Fill the form
    await fillVMForm(page);
    
    // Refresh the page
    await page.reload();
    
    // Check if form data is preserved (if browser has form restoration)
    const nameValue = await page.getByLabel('VM Name').inputValue();
    const boxValue = await page.getByLabel('Box').inputValue();
    
    // If form data is not preserved, fill it again
    if (!nameValue || !boxValue) {
      await fillVMForm(page);
    }
    
    // Submit the form
    const success = await submitFormAndCheckValidation(page);
    expect(success).toBe(true);
    
    // Cleanup: Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
    
    logger.log('Browser refresh test completed');
  });
  
  test('should handle form submission with browser back/forward navigation', async ({ page }) => {
    logger.log('Testing form submission with browser back/forward navigation');
    
    // Navigate to VM list first
    await page.goto('/vagrant/vms');
    
    // Navigate to VM creation page
    await page.goto('/vagrant/vms/new');
    
    // Fill the form
    await fillVMForm(page);
    
    // Go back
    await page.goBack();
    
    // Go forward
    await page.goForward();
    
    // Check if form data is preserved
    const nameValue = await page.getByLabel('VM Name').inputValue();
    const boxValue = await page.getByLabel('Box').inputValue();
    
    // If form data is not preserved, fill it again
    if (!nameValue || !boxValue) {
      await fillVMForm(page);
    }
    
    // Submit the form
    const success = await submitFormAndCheckValidation(page);
    expect(success).toBe(true);
    
    // Cleanup: Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
    
    logger.log('Browser navigation test completed');
  });
  
  test('should handle form submission with tab switching', async ({ page, context }) => {
    logger.log('Testing form submission with tab switching');
    
    // Open a new tab
    const newPage = await context.newPage();
    await newPage.goto('/dashboard');
    
    // Fill the form on the original page
    await fillVMForm(page);
    
    // Switch to the new tab (simulated by focusing on it)
    await newPage.bringToFront();
    
    // Switch back to the original tab
    await page.bringToFront();
    
    // Submit the form
    const success = await submitFormAndCheckValidation(page);
    expect(success).toBe(true);
    
    // Cleanup: Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
    
    // Close the new tab
    await newPage.close();
    
    logger.log('Tab switching test completed');
  });
  
  test('should handle form submission with slow network', async ({ page, context }) => {
    logger.log('Testing form submission with slow network');
    
    // Simulate slow network
    await context.route('**/*', async (route) => {
      // Wait for a delay before continuing the request
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.continue();
    });
    
    // Fill the form
    await fillVMForm(page);
    
    // Submit the form
    await page.getByRole('button', { name: 'Create VM' }).click();
    
    // Check for loading indicator
    await expect(page.locator('.loading-indicator, .spinner')).toBeVisible();
    
    // Wait for the submission to complete
    await page.waitForURL('**/vagrant/vms/*', { timeout: 30000 });
    
    // Remove the slow network simulation
    await context.unroute('**/*');
    
    // Cleanup: Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
    
    logger.log('Slow network test completed');
  });
  
  test('should handle form submission with accessibility navigation', async ({ page }) => {
    logger.log('Testing form submission with keyboard navigation');
    
    // Navigate to the form using keyboard
    await page.keyboard.press('Tab'); // Focus on first element
    
    // Fill the form using keyboard navigation
    await page.keyboard.type(VALID_VM.name);
    await page.keyboard.press('Tab');
    await page.keyboard.type(VALID_VM.box);
    await page.keyboard.press('Tab');
    await page.keyboard.type(VALID_VM.cpu.toString());
    await page.keyboard.press('Tab');
    await page.keyboard.type(VALID_VM.memory.toString());
    await page.keyboard.press('Tab');
    if (VALID_VM.description) {
      await page.keyboard.type(VALID_VM.description);
      await page.keyboard.press('Tab');
    }
    
    // Submit the form using Enter key
    await page.keyboard.press('Enter');
    
    // Wait for the submission to complete
    await page.waitForURL('**/vagrant/vms/*');
    
    // Cleanup: Delete the VM using keyboard
    await page.keyboard.press('Tab'); // Navigate to Delete button
    await page.keyboard.press('Enter');
    await page.waitForSelector('text=Are you sure');
    await page.keyboard.press('Tab'); // Navigate to Confirm button
    await page.keyboard.press('Enter');
    await page.waitForURL('/vagrant/vms');
    
    logger.log('Keyboard navigation test completed');
  });
  
  test('should handle form submission with different screen sizes', async ({ browser }) => {
    logger.log('Testing form submission with different screen sizes');
    
    // Define different viewport sizes to test
    const viewportSizes = [
      { width: 1920, height: 1080 }, // Desktop
      { width: 1024, height: 768 },  // Tablet landscape
      { width: 768, height: 1024 },  // Tablet portrait
      { width: 375, height: 812 }    // Mobile
    ];
    
    for (const viewport of viewportSizes) {
      // Create a new context with the viewport size
      const context = await browser.newContext({
        viewport
      });
      
      const page = await context.newPage();
      
      // Login
      await page.goto('/login');
      await page.getByLabel('Username').fill('admin');
      await page.getByLabel('Password').fill('admin');
      await page.getByRole('button', { name: 'Login' }).click();
      await page.waitForURL('/dashboard');
      
      // Navigate to VM creation page
      await page.goto('/vagrant/vms/new');
      
      // Fill the form
      await fillVMForm(page);
      
      // Submit the form
      const success = await submitFormAndCheckValidation(page);
      
      if (success) {
        // Cleanup: Delete the VM
        await page.getByRole('button', { name: 'Delete' }).click();
        await page.getByRole('button', { name: 'Confirm' }).click();
        await page.waitForURL('/vagrant/vms');
      }
      
      // Close the context
      await context.close();
    }
    
    logger.log('Responsive design test completed');
  });

  test('should handle file uploads for VM provisioning', async ({ page }) => {
    logger.log('Testing file uploads for VM provisioning');
    
    // Navigate to VM creation page
    await page.goto('/vagrant/vms/new');
    
    // Check if file upload field exists
    const fileUploadField = page.locator('input[type="file"]');
    const hasFileUpload = await fileUploadField.isVisible();
    
    if (hasFileUpload) {
      // Create a temporary file for upload
      const tempFilePath = path.join(logsDir, 'provision-script.sh');
      fs.writeFileSync(tempFilePath, '#!/bin/bash\necho "Hello from uploaded script"\napt-get update\napt-get install -y nginx');
      
      // Fill the basic VM form
      await fillVMForm(page);
      
      // Expand advanced options if needed
      const advancedOptionsButton = page.getByText('Advanced Options');
      if (await advancedOptionsButton.isVisible()) {
        await advancedOptionsButton.click();
      }
      
      // Upload the file
      await fileUploadField.setInputFiles(tempFilePath);
      
      // Check that the file name appears in the UI
      await expect(page.getByText('provision-script.sh')).toBeVisible();
      
      // Submit the form
      const success = await submitFormAndCheckValidation(page);
      expect(success).toBe(true);
      
      // Check that the uploaded file is mentioned in the VM details
      await expect(page.getByText('Provisioning Script')).toBeVisible();
      
      // Cleanup: Delete the VM and temporary file
      await page.getByRole('button', { name: 'Delete' }).click();
      await page.getByRole('button', { name: 'Confirm' }).click();
      await page.waitForURL('/vagrant/vms');
      
      // Delete the temporary file
      fs.unlinkSync(tempFilePath);
    } else {
      // If file upload is not supported, log and skip
      logger.log('File upload not supported in this UI');
      test.skip();
    }
    
    logger.log('File upload test completed');
  });
  
  test('should handle multiple file uploads', async ({ page }) => {
    logger.log('Testing multiple file uploads');
    
    // Navigate to VM creation page
    await page.goto('/vagrant/vms/new');
    
    // Check if multiple file upload field exists
    const fileUploadField = page.locator('input[type="file"][multiple]');
    const hasMultipleFileUpload = await fileUploadField.isVisible();
    
    if (hasMultipleFileUpload) {
      // Create temporary files for upload
      const tempFilePath1 = path.join(logsDir, 'provision-script1.sh');
      const tempFilePath2 = path.join(logsDir, 'provision-script2.sh');
      const tempFilePath3 = path.join(logsDir, 'config.yaml');
      
      fs.writeFileSync(tempFilePath1, '#!/bin/bash\necho "Script 1"');
      fs.writeFileSync(tempFilePath2, '#!/bin/bash\necho "Script 2"');
      fs.writeFileSync(tempFilePath3, 'config:\n  key: value');
      
      // Fill the basic VM form
      await fillVMForm(page);
      
      // Expand advanced options if needed
      const advancedOptionsButton = page.getByText('Advanced Options');
      if (await advancedOptionsButton.isVisible()) {
        await advancedOptionsButton.click();
      }
      
      // Upload multiple files
      await fileUploadField.setInputFiles([tempFilePath1, tempFilePath2, tempFilePath3]);
      
      // Check that the file names appear in the UI
      await expect(page.getByText('provision-script1.sh')).toBeVisible();
      await expect(page.getByText('provision-script2.sh')).toBeVisible();
      await expect(page.getByText('config.yaml')).toBeVisible();
      
      // Submit the form
      const success = await submitFormAndCheckValidation(page);
      expect(success).toBe(true);
      
      // Cleanup: Delete the VM and temporary files
      await page.getByRole('button', { name: 'Delete' }).click();
      await page.getByRole('button', { name: 'Confirm' }).click();
      await page.waitForURL('/vagrant/vms');
      
      // Delete the temporary files
      fs.unlinkSync(tempFilePath1);
      fs.unlinkSync(tempFilePath2);
      fs.unlinkSync(tempFilePath3);
    } else {
      // If multiple file upload is not supported, log and skip
      logger.log('Multiple file upload not supported in this UI');
      test.skip();
    }
    
    logger.log('Multiple file upload test completed');
  });
  
  test('should validate file uploads', async ({ page }) => {
    logger.log('Testing file upload validation');
    
    // Navigate to VM creation page
    await page.goto('/vagrant/vms/new');
    
    // Check if file upload field exists
    const fileUploadField = page.locator('input[type="file"]');
    const hasFileUpload = await fileUploadField.isVisible();
    
    if (hasFileUpload) {
      // Create a temporary file with invalid content (e.g., binary data or very large file)
      const tempFilePath = path.join(logsDir, 'invalid-file.bin');
      // Create a 5MB file with random data
      const buffer = Buffer.alloc(5 * 1024 * 1024, 'x');
      fs.writeFileSync(tempFilePath, buffer);
      
      // Fill the basic VM form
      await fillVMForm(page);
      
      // Expand advanced options if needed
      const advancedOptionsButton = page.getByText('Advanced Options');
      if (await advancedOptionsButton.isVisible()) {
        await advancedOptionsButton.click();
      }
      
      // Upload the invalid file
      await fileUploadField.setInputFiles(tempFilePath);
      
      // Submit the form and expect validation error
      await submitFormAndCheckValidation(page, 'file');
      
      // Delete the temporary file
      fs.unlinkSync(tempFilePath);
    } else {
      // If file upload is not supported, log and skip
      logger.log('File upload not supported in this UI');
      test.skip();
    }
    
    logger.log('File upload validation test completed');
  });
  
  test('should launch VM and verify status changes', async ({ page }) => {
    logger.log('Testing VM launch and status changes');
    
    // Create a VM first
    await page.goto('/vagrant/vms/new');
    const vmName = 'launch-test-vm';
    await fillVMForm(page, { name: vmName });
    
    // Submit the form
    const success = await submitFormAndCheckValidation(page);
    expect(success).toBe(true);
    
    // Check initial status (should be CREATED or CREATING)
    const statusElement = page.locator('.vm-status');
    await expect(statusElement).toBeVisible();
    const initialStatus = await statusElement.textContent();
    expect(initialStatus).toMatch(/CREAT(ED|ING)/);
    
    // Click the Launch/Start button
    const launchButton = page.getByRole('button', { name: /Launch|Start/ });
    await launchButton.click();
    
    // Confirm the action if there's a confirmation dialog
    const confirmButton = page.getByRole('button', { name: 'Confirm' });
    if (await confirmButton.isVisible()) {
      await confirmButton.click();
    }
    
    // Check that status changes to LAUNCHING or STARTING
    await expect(statusElement).toContainText(/LAUNCH|START/);
    
    // Wait for status to change to RUNNING (with timeout)
    try {
      await expect(statusElement).toContainText('RUNNING', { timeout: 60000 });
      logger.log('VM successfully reached RUNNING state');
    } catch (e) {
      // If it doesn't reach RUNNING in time, log but don't fail the test
      logger.log('VM did not reach RUNNING state within timeout period');
    }
    
    // Stop the VM
    const stopButton = page.getByRole('button', { name: 'Stop' });
    await stopButton.click();
    
    // Confirm the action if there's a confirmation dialog
    if (await confirmButton.isVisible()) {
      await confirmButton.click();
    }
    
    // Check that status changes to STOPPING
    await expect(statusElement).toContainText(/STOP/);
    
    // Cleanup: Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
    
    logger.log('VM launch and status change test completed');
  });
  
  test('should handle VM launch with custom options', async ({ page }) => {
    logger.log('Testing VM launch with custom options');
    
    // Navigate to VM creation page
    await page.goto('/vagrant/vms/new');
    
    // Check if launch options section exists
    const launchOptionsButton = page.getByText('Launch Options');
    const hasLaunchOptions = await launchOptionsButton.isVisible();
    
    if (hasLaunchOptions) {
      // Expand launch options
      await launchOptionsButton.click();
      
      // Fill the basic VM form
      await fillVMForm(page);
      
      // Set custom launch options if available
      const autoStartCheckbox = page.getByLabel('Auto-start VM');
      if (await autoStartCheckbox.isVisible()) {
        await autoStartCheckbox.check();
      }
      
      const headlessCheckbox = page.getByLabel('Launch in headless mode');
      if (await headlessCheckbox.isVisible()) {
        await headlessCheckbox.check();
      }
      
      // Submit the form
      const success = await submitFormAndCheckValidation(page);
      expect(success).toBe(true);
      
      // Check if VM is automatically launching
      const statusElement = page.locator('.vm-status');
      await expect(statusElement).toContainText(/LAUNCH|START|RUNNING/);
      
      // Cleanup: Stop and delete the VM
      const stopButton = page.getByRole('button', { name: 'Stop' });
      if (await stopButton.isVisible()) {
        await stopButton.click();
        
        // Confirm the action if there's a confirmation dialog
        const confirmButton = page.getByRole('button', { name: 'Confirm' });
        if (await confirmButton.isVisible()) {
          await confirmButton.click();
        }
        
        // Wait for VM to stop
        await expect(statusElement).toContainText(/STOPPED|CREATED/, { timeout: 30000 });
      }
      
      // Delete the VM
      await page.getByRole('button', { name: 'Delete' }).click();
      await page.getByRole('button', { name: 'Confirm' }).click();
      await page.waitForURL('/vagrant/vms');
    } else {
      // If launch options are not supported, log and skip
      logger.log('Launch options not supported in this UI');
      test.skip();
    }
    
    logger.log('VM launch with custom options test completed');
  });
  
  test('should handle VM console access', async ({ page }) => {
    logger.log('Testing VM console access');
    
    // Create and launch a VM first
    await page.goto('/vagrant/vms/new');
    const vmName = 'console-test-vm';
    await fillVMForm(page, { name: vmName });
    
    // Submit the form
    const success = await submitFormAndCheckValidation(page);
    expect(success).toBe(true);
    
    // Launch the VM
    const launchButton = page.getByRole('button', { name: /Launch|Start/ });
    await launchButton.click();
    
    // Confirm the action if there's a confirmation dialog
    const confirmButton = page.getByRole('button', { name: 'Confirm' });
    if (await confirmButton.isVisible()) {
      await confirmButton.click();
    }
    
    // Wait for VM to be in a state where console is available
    const statusElement = page.locator('.vm-status');
    try {
      await expect(statusElement).toContainText('RUNNING', { timeout: 60000 });
    } catch (e) {
      // If it doesn't reach RUNNING in time, log but continue
      logger.log('VM did not reach RUNNING state within timeout period');
    }
    
    // Look for console access button
    const consoleButton = page.getByRole('button', { name: /Console|Terminal|SSH/ });
    
    if (await consoleButton.isVisible()) {
      // Click the console button
      await consoleButton.click();
      
      // Check if console/terminal element appears
      const consoleElement = page.locator('.console-window, .terminal-window, iframe.console');
      await expect(consoleElement).toBeVisible({ timeout: 10000 });
      
      // Check if console contains expected content
      const hasConsoleContent = await page.locator('.console-window, .terminal-window, .console-output').isVisible();
      
      if (hasConsoleContent) {
        // Wait for some console output to appear
        await page.waitForTimeout(5000);
        
        // Try typing a command if interactive console is available
        const consoleInput = page.locator('.console-input');
        if (await consoleInput.isVisible()) {
          await consoleInput.fill('echo "Hello from test"');
          await page.keyboard.press('Enter');
          
          // Check for command output
          await page.waitForTimeout(2000);
        }
      }
      
      // Close console if there's a close button
      const closeButton = page.getByRole('button', { name: 'Close' });
      if (await closeButton.isVisible()) {
        await closeButton.click();
      }
    } else {
      logger.log('Console access not available for this VM');
    }
    
    // Cleanup: Stop and delete the VM
    await page.goto('/vagrant/vms');
    await page.getByText(vmName).click();
    
    const stopButton = page.getByRole('button', { name: 'Stop' });
    if (await stopButton.isVisible()) {
      await stopButton.click();
      
      // Confirm the action if there's a confirmation dialog
      if (await confirmButton.isVisible()) {
        await confirmButton.click();
      }
      
      // Wait for VM to stop
      await expect(statusElement).toContainText(/STOPPED|CREATED/, { timeout: 30000 });
    }
    
    // Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
    
    logger.log('VM console access test completed');
  });
  
  test('should handle VM snapshot creation and restoration', async ({ page }) => {
    logger.log('Testing VM snapshot functionality');
    
    // Create a VM first
    await page.goto('/vagrant/vms/new');
    const vmName = 'snapshot-test-vm';
    await fillVMForm(page, { name: vmName });
    
    // Submit the form
    const success = await submitFormAndCheckValidation(page);
    expect(success).toBe(true);
    
    // Check if snapshot functionality is available
    const snapshotButton = page.getByRole('button', { name: /Snapshot|Backup/ });
    
    if (await snapshotButton.isVisible()) {
      // Click the snapshot button
      await snapshotButton.click();
      
      // Fill snapshot name and description if form appears
      const snapshotNameInput = page.getByLabel('Snapshot Name');
      if (await snapshotNameInput.isVisible()) {
        await snapshotNameInput.fill('test-snapshot');
        
        const snapshotDescInput = page.getByLabel('Description');
        if (await snapshotDescInput.isVisible()) {
          await snapshotDescInput.fill('Test snapshot for automated testing');
        }
        
        // Create the snapshot
        await page.getByRole('button', { name: 'Create Snapshot' }).click();
      }
      
      // Wait for snapshot creation to complete
      await page.waitForSelector('text=Snapshot created', { timeout: 30000 });
      
      // Check if snapshot is listed
      await expect(page.getByText('test-snapshot')).toBeVisible();
      
      // Test snapshot restoration if available
      const restoreButton = page.getByRole('button', { name: 'Restore' });
      if (await restoreButton.isVisible()) {
        await restoreButton.click();
        
        // Confirm restoration
        const confirmButton = page.getByRole('button', { name: 'Confirm' });
        if (await confirmButton.isVisible()) {
          await confirmButton.click();
        }
        
        // Wait for restoration to complete
        await page.waitForSelector('text=Snapshot restored', { timeout: 30000 });
      }
      
      // Test snapshot deletion if available
      const deleteSnapshotButton = page.getByRole('button', { name: 'Delete Snapshot' });
      if (await deleteSnapshotButton.isVisible()) {
        await deleteSnapshotButton.click();
        
        // Confirm deletion
        const confirmButton = page.getByRole('button', { name: 'Confirm' });
        if (await confirmButton.isVisible()) {
          await confirmButton.click();
        }
        
        // Wait for deletion to complete
        await page.waitForSelector('text=Snapshot deleted', { timeout: 30000 });
      }
    } else {
      logger.log('Snapshot functionality not available for this VM');
    }
    
    // Cleanup: Delete the VM
    await page.goto('/vagrant/vms');
    await page.getByText(vmName).click();
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
    
    logger.log('VM snapshot test completed');
  });

  test('should display upload progress indicator for large files', async ({ page }) => {
    logger.log('Testing upload progress indicator for large files');
    
    // Navigate to VM creation page
    await page.goto('/vagrant/vms/new');
    
    // Check if file upload field exists
    const fileUploadField = page.locator('input[type="file"]');
    const hasFileUpload = await fileUploadField.isVisible();
    
    if (hasFileUpload) {
      // Create a temporary large file for upload (10MB)
      const tempFilePath = path.join(logsDir, 'large-provision-script.sh');
      // Generate a large script with many echo statements
      let fileContent = '#!/bin/bash\n';
      for (let i = 0; i < 100000; i++) {
        fileContent += `echo "Line ${i} of test script"\n`;
      }
      fs.writeFileSync(tempFilePath, fileContent);
      
      // Fill the basic VM form
      await fillVMForm(page);
      
      // Expand advanced options if needed
      const advancedOptionsButton = page.getByText('Advanced Options');
      if (await advancedOptionsButton.isVisible()) {
        await advancedOptionsButton.click();
      }
      
      // Intercept network requests to slow down the upload
      await page.route('**/api/upload', async (route) => {
        // Wait before continuing to simulate slow upload
        await new Promise(resolve => setTimeout(resolve, 2000));
        await route.continue();
      });
      
      // Upload the file
      await fileUploadField.setInputFiles(tempFilePath);
      
      // Check for progress indicator
      const progressIndicator = page.locator('.upload-progress, .progress-bar, progress');
      const hasProgressIndicator = await progressIndicator.isVisible();
      
      if (hasProgressIndicator) {
        // Verify progress indicator shows activity
        const initialValue = await progressIndicator.getAttribute('value');
        
        // Wait a moment and check if value changed
        await page.waitForTimeout(1000);
        const updatedValue = await progressIndicator.getAttribute('value');
        
        // Log the progress values
        logger.log(`Initial progress: ${initialValue}, Updated progress: ${updatedValue}`);
        
        // If the progress indicator has numeric values, they should be different
        if (initialValue !== null && updatedValue !== null) {
          expect(initialValue).not.toEqual(updatedValue);
        }
      } else {
        // If no specific progress indicator, check for any loading state
        const loadingIndicator = page.locator('.loading, .spinner, .uploading');
        await expect(loadingIndicator).toBeVisible();
      }
      
      // Remove the route interception
      await page.unroute('**/api/upload');
      
      // Wait for upload to complete
      await page.waitForSelector('text=provision-script.sh, text=large-provision-script.sh', { timeout: 30000 });
      
      // Submit the form
      const success = await submitFormAndCheckValidation(page);
      
      if (success) {
        // Cleanup: Delete the VM
        await page.getByRole('button', { name: 'Delete' }).click();
        await page.getByRole('button', { name: 'Confirm' }).click();
        await page.waitForURL('/vagrant/vms');
      }
      
      // Delete the temporary file
      fs.unlinkSync(tempFilePath);
    } else {
      // If file upload is not supported, log and skip
      logger.log('File upload not supported in this UI');
      test.skip();
    }
    
    logger.log('Upload progress indicator test completed');
  });
  
  test('should allow cancellation of file uploads', async ({ page }) => {
    logger.log('Testing cancellation of file uploads');
    
    // Navigate to VM creation page
    await page.goto('/vagrant/vms/new');
    
    // Check if file upload field exists
    const fileUploadField = page.locator('input[type="file"]');
    const hasFileUpload = await fileUploadField.isVisible();
    
    if (hasFileUpload) {
      // Create a temporary large file for upload (20MB)
      const tempFilePath = path.join(logsDir, 'very-large-file.sh');
      // Generate a large script
      let fileContent = '#!/bin/bash\n';
      for (let i = 0; i < 200000; i++) {
        fileContent += `echo "Line ${i} of test script"\n`;
      }
      fs.writeFileSync(tempFilePath, fileContent);
      
      // Fill the basic VM form
      await fillVMForm(page);
      
      // Expand advanced options if needed
      const advancedOptionsButton = page.getByText('Advanced Options');
      if (await advancedOptionsButton.isVisible()) {
        await advancedOptionsButton.click();
      }
      
      // Intercept network requests to slow down the upload
      await page.route('**/api/upload', async (route) => {
        // Wait a long time to ensure we can cancel before it completes
        await new Promise(resolve => setTimeout(resolve, 5000));
        await route.continue();
      });
      
      // Start the upload
      await fileUploadField.setInputFiles(tempFilePath);
      
      // Look for cancel button
      const cancelButton = page.getByRole('button', { name: /Cancel|Abort|Stop/ }).first();
      
      if (await cancelButton.isVisible()) {
        // Wait a moment to ensure upload has started
        await page.waitForTimeout(1000);
        
        // Click the cancel button
        await cancelButton.click();
        
        // Verify the upload was cancelled
        const cancelledIndicator = page.locator('.upload-cancelled, text=cancelled, text=canceled');
        const uploadField = page.locator('input[type="file"]');
        
        // Either we should see a cancelled message or the upload field should be reset
        const isCancelled = await cancelledIndicator.isVisible() || 
                           (await uploadField.inputValue()) === '';
        
        expect(isCancelled).toBeTruthy();
      } else {
        // If no specific cancel button, try the form reset button
        const resetButton = page.getByRole('button', { name: /Reset|Clear/ });
        
        if (await resetButton.isVisible()) {
          await resetButton.click();
          
          // Verify the form was reset
          const uploadField = page.locator('input[type="file"]');
          expect(await uploadField.inputValue()).toBe('');
        } else {
          logger.log('No cancel button found, skipping cancellation test');
        }
      }
      
      // Remove the route interception
      await page.unroute('**/api/upload');
      
      // Delete the temporary file
      fs.unlinkSync(tempFilePath);
    } else {
      // If file upload is not supported, log and skip
      logger.log('File upload not supported in this UI');
      test.skip();
    }
    
    logger.log('Upload cancellation test completed');
  });
  
  test('should handle drag and drop file uploads', async ({ page }) => {
    logger.log('Testing drag and drop file uploads');
    
    // Navigate to VM creation page
    await page.goto('/vagrant/vms/new');
    
    // Check if there's a drop zone for files
    const dropZone = page.locator('.drop-zone, [data-testid="drop-zone"], .file-upload-area');
    const hasDropZone = await dropZone.isVisible();
    
    if (hasDropZone) {
      // Create a temporary file for upload
      const tempFilePath = path.join(logsDir, 'drag-drop-script.sh');
      fs.writeFileSync(tempFilePath, '#!/bin/bash\necho "File uploaded via drag and drop"');
      
      // Fill the basic VM form
      await fillVMForm(page);
      
      // Expand advanced options if needed
      const advancedOptionsButton = page.getByText('Advanced Options');
      if (await advancedOptionsButton.isVisible()) {
        await advancedOptionsButton.click();
      }
      
      // Simulate drag and drop
      // Note: This is a workaround since Playwright doesn't directly support drag and drop from the file system
      // We'll use the dataTransfer API to simulate it
      
      await dropZone.evaluate((element) => {
        // Create drag events
        const dragEnterEvent = new Event('dragenter', { bubbles: true });
        const dragOverEvent = new Event('dragover', { bubbles: true });
        dragOverEvent.preventDefault = () => {}; // Mock preventDefault
        
        // Dispatch events
        element.dispatchEvent(dragEnterEvent);
        element.dispatchEvent(dragOverEvent);
      });
      
      // Since we can't truly drag from filesystem, we'll use the regular upload method
      // but verify the drop zone shows active state
      const isActive = await dropZone.evaluate((element) => {
        return element.classList.contains('active') || 
               element.classList.contains('drag-over') || 
               element.classList.contains('highlight');
      });
      
      // If the drop zone has an active state, log it
      if (isActive) {
        logger.log('Drop zone shows active state during drag');
      }
      
      // Now use the regular file input as a fallback
      const fileUploadField = page.locator('input[type="file"]');
      if (await fileUploadField.isVisible()) {
        await fileUploadField.setInputFiles(tempFilePath);
      } else {
        // Try to set files directly on the drop zone
        await dropZone.setInputFiles(tempFilePath);
      }
      
      // Check that the file name appears in the UI
      await expect(page.getByText('drag-drop-script.sh')).toBeVisible();
      
      // Submit the form
      const success = await submitFormAndCheckValidation(page);
      
      if (success) {
        // Cleanup: Delete the VM
        await page.getByRole('button', { name: 'Delete' }).click();
        await page.getByRole('button', { name: 'Confirm' }).click();
        await page.waitForURL('/vagrant/vms');
      }
      
      // Delete the temporary file
      fs.unlinkSync(tempFilePath);
    } else {
      // If drag and drop is not supported, log and skip
      logger.log('Drag and drop not supported in this UI');
      test.skip();
    }
    
    logger.log('Drag and drop test completed');
  });
  
  test('should handle file uploads with special characters in filenames', async ({ page }) => {
    logger.log('Testing file uploads with special characters in filenames');
    
    // Navigate to VM creation page
    await page.goto('/vagrant/vms/new');
    
    // Check if file upload field exists
    const fileUploadField = page.locator('input[type="file"]');
    const hasFileUpload = await fileUploadField.isVisible();
    
    if (hasFileUpload) {
      // Create temporary files with special characters in names
      const specialFilenames = [
        'script with spaces.sh',
        'script-with-dashes.sh',
        'script_with_underscores.sh',
        'script.with.dots.sh',
        'script+with+plus.sh',
        'script(with)parentheses.sh',
        'script[with]brackets.sh',
        'script{with}braces.sh',
        'script@<EMAIL>',
        'script#with#hash.sh'
      ];
      
      const tempFilePaths = [];
      
      // Create each file
      for (const filename of specialFilenames) {
        const filePath = path.join(logsDir, filename);
        fs.writeFileSync(filePath, `#!/bin/bash\necho "This is ${filename}"`);
        tempFilePaths.push(filePath);
      }
      
      // Test each file individually
      for (const [index, filePath] of tempFilePaths.entries()) {
        // Reload the page for each test
        if (index > 0) {
          await page.reload();
        }
        
        // Fill the basic VM form
        await fillVMForm(page, { name: `special-char-vm-${index}` });
        
        // Expand advanced options if needed
        const advancedOptionsButton = page.getByText('Advanced Options');
        if (await advancedOptionsButton.isVisible()) {
          await advancedOptionsButton.click();
        }
        
        // Upload the file
        await fileUploadField.setInputFiles(filePath);
        
        // Get the filename from the path
        const filename = path.basename(filePath);
        
        // Check that the file name appears in the UI (or at least part of it)
        // Some UIs might sanitize or truncate filenames
        const filenameBase = filename.split('.')[0];
        await expect(page.getByText(filenameBase, { exact: false })).toBeVisible();
        
        // Submit the form
        const success = await submitFormAndCheckValidation(page);
        
        if (success) {
          // Cleanup: Delete the VM
          await page.getByRole('button', { name: 'Delete' }).click();
          await page.getByRole('button', { name: 'Confirm' }).click();
          await page.waitForURL('/vagrant/vms');
        } else {
          // If submission failed, log which filename caused issues
          logger.log(`Submission failed with filename: ${filename}`);
          break;
        }
      }
      
      // Delete all temporary files
      for (const filePath of tempFilePaths) {
        fs.unlinkSync(filePath);
      }
    } else {
      // If file upload is not supported, log and skip
      logger.log('File upload not supported in this UI');
      test.skip();
    }
    
    logger.log('Special character filename test completed');
  });
  
  test('should handle file uploads with different MIME types', async ({ page }) => {
    logger.log('Testing file uploads with different MIME types');
    
    // Navigate to VM creation page
    await page.goto('/vagrant/vms/new');
    
    // Check if file upload field exists
    const fileUploadField = page.locator('input[type="file"]');
    const hasFileUpload = await fileUploadField.isVisible();
    
    if (hasFileUpload) {
      // Create temporary files with different MIME types
      const fileTypes = [
        { name: 'script.sh', content: '#!/bin/bash\necho "Bash script"' },
        { name: 'script.py', content: '#!/usr/bin/env python3\nprint("Python script")' },
        { name: 'config.json', content: '{"name": "test", "value": 123}' },
        { name: 'config.yaml', content: 'name: test\nvalue: 123' },
        { name: 'config.xml', content: '<?xml version="1.0"?><config><name>test</name><value>123</value></config>' },
        { name: 'config.ini', content: '[section]\nname=test\nvalue=123' },
        { name: 'Vagrantfile', content: 'Vagrant.configure("2") do |config|\n  config.vm.box = "ubuntu/focal64"\nend' },
        { name: 'Dockerfile', content: 'FROM ubuntu:20.04\nRUN apt-get update' },
        { name: 'README.md', content: '# Test VM\nThis is a test VM.' },
        { name: 'index.html', content: '<!DOCTYPE html><html><body><h1>Test</h1></body></html>' }
      ];
      
      const tempFilePaths = [];
      
      // Create each file
      for (const fileType of fileTypes) {
        const filePath = path.join(logsDir, fileType.name);
        fs.writeFileSync(filePath, fileType.content);
        tempFilePaths.push(filePath);
      }
      
      // Test each file individually
      for (const [index, filePath] of tempFilePaths.entries()) {
        // Reload the page for each test
        if (index > 0) {
          await page.reload();
        }
        
        // Fill the basic VM form
        await fillVMForm(page, { name: `mime-type-vm-${index}` });
        
        // Expand advanced options if needed
        const advancedOptionsButton = page.getByText('Advanced Options');
        if (await advancedOptionsButton.isVisible()) {
          await advancedOptionsButton.click();
        }
        
        // Upload the file
        await fileUploadField.setInputFiles(filePath);
        
        // Get the filename from the path
        const filename = path.basename(filePath);
        
        // Check that the file name appears in the UI
        await expect(page.getByText(filename)).toBeVisible();
        
        // Submit the form
        const success = await submitFormAndCheckValidation(page);
        
        if (success) {
          // Cleanup: Delete the VM
          await page.getByRole('button', { name: 'Delete' }).click();
          await page.getByRole('button', { name: 'Confirm' }).click();
          await page.waitForURL('/vagrant/vms');
        } else {
          // If submission failed, log which file type caused issues
          logger.log(`Submission failed with file type: ${filename}`);
          break;
        }
      }
      
      // Delete all temporary files
      for (const filePath of tempFilePaths) {
        fs.unlinkSync(filePath);
      }
    } else {
      // If file upload is not supported, log and skip
      logger.log('File upload not supported in this UI');
      test.skip();
    }
    
    logger.log('MIME type test completed');
  });
  
  test('should handle replacing uploaded files', async ({ page }) => {
    logger.log('Testing replacement of uploaded files');
    
    // Navigate to VM creation page
    await page.goto('/vagrant/vms/new');
    
    // Check if file upload field exists
    const fileUploadField = page.locator('input[type="file"]');
    const hasFileUpload = await fileUploadField.isVisible();
    
    if (hasFileUpload) {
      // Create two temporary files
      const tempFilePath1 = path.join(logsDir, 'original-script.sh');
      const tempFilePath2 = path.join(logsDir, 'replacement-script.sh');
      
      fs.writeFileSync(tempFilePath1, '#!/bin/bash\necho "Original script"');
      fs.writeFileSync(tempFilePath2, '#!/bin/bash\necho "Replacement script"');
      
      // Fill the basic VM form
      await fillVMForm(page);
      
      // Expand advanced options if needed
      const advancedOptionsButton = page.getByText('Advanced Options');
      if (await advancedOptionsButton.isVisible()) {
        await advancedOptionsButton.click();
      }
      
      // Upload the first file
      await fileUploadField.setInputFiles(tempFilePath1);
      
      // Check that the first file name appears in the UI
      await expect(page.getByText('original-script.sh')).toBeVisible();
      
      // Now upload the second file to replace the first
      await fileUploadField.setInputFiles(tempFilePath2);
      
      // Check that the second file name appears and the first is gone
      await expect(page.getByText('replacement-script.sh')).toBeVisible();
      
      // The original file should no longer be visible
      // Note: Some UIs might keep both files, so we'll check if the count of elements is at least 1
      const originalFileElements = page.getByText('original-script.sh');
      const replacementFileElements = page.getByText('replacement-script.sh');
      
      const originalCount = await originalFileElements.count();
      const replacementCount = await replacementFileElements.count();
      
      // Log the counts
      logger.log(`Original file elements: ${originalCount}, Replacement file elements: ${replacementCount}`);
      
      // Either the original should be gone, or the replacement should be present
      expect(originalCount === 0 || replacementCount > 0).toBeTruthy();
      
      // Submit the form
      const success = await submitFormAndCheckValidation(page);
      
      if (success) {
        // Cleanup: Delete the VM
        await page.getByRole('button', { name: 'Delete' }).click();
        await page.getByRole('button', { name: 'Confirm' }).click();
        await page.waitForURL('/vagrant/vms');
      }
      
      // Delete the temporary files
      fs.unlinkSync(tempFilePath1);
      fs.unlinkSync(tempFilePath2);
    } else {
      // If file upload is not supported, log and skip
      logger.log('File upload not supported in this UI');
      test.skip();
    }
    
    logger.log('File replacement test completed');
  });
}); 