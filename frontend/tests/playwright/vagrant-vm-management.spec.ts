import { test, expect, Page } from '@playwright/test';
import { readFileSync } from 'fs';
import path from 'path';

// Test data
const TEST_VM = {
  name: 'test-playwright-vm',
  box: 'ubuntu/focal64',
  cpu: 2,
  memory: 2048,
};

// Helper functions
async function createVM(page: Page, vmData = TEST_VM) {
  // Navigate to VM creation page
  await page.goto('/vagrant/vms/new');
  
  // Fill the form
  await page.getByLabel('VM Name').fill(vmData.name);
  await page.getByLabel('Box').fill(vmData.box);
  await page.getByLabel('CPU Cores').fill(vmData.cpu.toString());
  await page.getByLabel('Memory (MB)').fill(vmData.memory.toString());
  
  // Submit the form
  await page.getByRole('button', { name: 'Create VM' }).click();
  
  // Wait for the VM to be created
  await page.waitForURL('**/vagrant/vms/*');
  
  // Return the VM ID from the URL
  const url = page.url();
  const vmId = url.split('/').pop() || '';
  return vmId;
}

async function waitForVMStatus(page: Page, vmId: string, expectedStatus: string, timeout = 60000) {
  // Navigate to VM details page
  await page.goto(`/vagrant/vms/${vmId}`);
  
  // Wait for the status to match the expected status
  await page.waitForFunction(
    (status) => {
      const statusElement = document.querySelector('.vm-status');
      return statusElement && statusElement.textContent?.includes(status);
    },
    expectedStatus,
    { timeout }
  );
}

async function uploadFileToVM(page: Page, vmId: string, filePath: string, remoteDestination: string) {
  // Navigate to VM file upload page
  await page.goto(`/vagrant/vms/${vmId}/files`);
  
  // Fill the destination field
  await page.getByLabel('Destination Path').fill(remoteDestination);
  
  // Upload the file
  const fileInput = page.locator('input[type="file"]');
  await fileInput.setInputFiles(filePath);
  
  // Submit the upload
  await page.getByRole('button', { name: 'Upload' }).click();
  
  // Wait for the upload to complete
  await page.waitForSelector('.upload-success', { timeout: 30000 });
}

async function executeCommand(page: Page, vmId: string, command: string): Promise<string> {
  // Navigate to VM terminal page
  await page.goto(`/vagrant/vms/${vmId}/terminal`);
  
  // Fill the command field
  await page.getByLabel('Command').fill(command);
  
  // Execute the command
  await page.getByRole('button', { name: 'Execute' }).click();
  
  // Wait for the command to complete
  await page.waitForSelector('.command-output', { timeout: 30000 });
  
  // Return the command output
  const output = await page.locator('.command-output').textContent();
  return output || '';
}

test.describe('Vagrant VM Management UI', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.getByLabel('Username').fill('admin');
    await page.getByLabel('Password').fill('admin');
    await page.getByRole('button', { name: 'Login' }).click();
    
    // Wait for successful login
    await page.waitForURL('/dashboard');
  });
  
  test('should display the VM dashboard', async ({ page }) => {
    // Navigate to VM dashboard
    await page.goto('/vagrant/vms');
    
    // Check that the dashboard is displayed
    await expect(page.getByRole('heading', { name: 'Vagrant VMs' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Create New VM' })).toBeVisible();
    
    // Check that the VM list is displayed
    await expect(page.locator('.vm-list')).toBeVisible();
  });
  
  test('should create a new VM', async ({ page }) => {
    // Create a new VM
    const vmId = await createVM(page);
    
    // Check that the VM details page is displayed
    await expect(page.getByText(TEST_VM.name)).toBeVisible();
    await expect(page.getByText(TEST_VM.box)).toBeVisible();
    await expect(page.getByText(`${TEST_VM.cpu} cores`)).toBeVisible();
    await expect(page.getByText(`${TEST_VM.memory} MB`)).toBeVisible();
    
    // Check that the VM actions are available
    await expect(page.getByRole('button', { name: 'Start' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Delete' })).toBeVisible();
    
    // Cleanup: Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
  });
  
  test('should manage VM lifecycle (start, stop, destroy)', async ({ page }) => {
    // Create a new VM
    const vmId = await createVM(page);
    
    // Start the VM
    await page.getByRole('button', { name: 'Start' }).click();
    await page.getByText('Starting VM...').waitFor();
    
    // Wait for the VM to be running
    await waitForVMStatus(page, vmId, 'RUNNING');
    
    // Check that the Stop button is now available
    await expect(page.getByRole('button', { name: 'Stop' })).toBeVisible();
    
    // Stop the VM
    await page.getByRole('button', { name: 'Stop' }).click();
    await page.getByText('Stopping VM...').waitFor();
    
    // Wait for the VM to be stopped
    await waitForVMStatus(page, vmId, 'STOPPED');
    
    // Check that the Start button is available again
    await expect(page.getByRole('button', { name: 'Start' })).toBeVisible();
    
    // Cleanup: Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
  });
  
  test('should upload files to VM', async ({ page }) => {
    // Create a new VM
    const vmId = await createVM(page);
    
    // Start the VM
    await page.getByRole('button', { name: 'Start' }).click();
    await waitForVMStatus(page, vmId, 'RUNNING');
    
    // Create a test file to upload
    const testFilePath = path.join(__dirname, 'test-upload.txt');
    const testFileContent = 'This is a test file for upload';
    require('fs').writeFileSync(testFilePath, testFileContent);
    
    // Upload the file to the VM
    await uploadFileToVM(page, vmId, testFilePath, '/tmp/test-upload.txt');
    
    // Verify the file was uploaded by executing a command to check its content
    const commandOutput = await executeCommand(page, vmId, 'cat /tmp/test-upload.txt');
    expect(commandOutput).toContain(testFileContent);
    
    // Cleanup: Stop and delete the VM
    await page.goto(`/vagrant/vms/${vmId}`);
    await page.getByRole('button', { name: 'Stop' }).click();
    await waitForVMStatus(page, vmId, 'STOPPED');
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
    
    // Clean up the test file
    require('fs').unlinkSync(testFilePath);
  });
  
  test('should execute commands on VM', async ({ page }) => {
    // Create a new VM
    const vmId = await createVM(page);
    
    // Start the VM
    await page.getByRole('button', { name: 'Start' }).click();
    await waitForVMStatus(page, vmId, 'RUNNING');
    
    // Execute a simple command
    const output = await executeCommand(page, vmId, 'echo "Hello from Playwright test"');
    expect(output).toContain('Hello from Playwright test');
    
    // Execute a command to check system info
    const systemInfo = await executeCommand(page, vmId, 'uname -a');
    expect(systemInfo).toContain('Linux');
    
    // Cleanup: Stop and delete the VM
    await page.goto(`/vagrant/vms/${vmId}`);
    await page.getByRole('button', { name: 'Stop' }).click();
    await waitForVMStatus(page, vmId, 'STOPPED');
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
  });
  
  test('should handle VM errors gracefully', async ({ page }) => {
    // Create a new VM with invalid settings to trigger an error
    await page.goto('/vagrant/vms/new');
    
    // Fill the form with invalid data (non-existent box)
    await page.getByLabel('VM Name').fill('error-test-vm');
    await page.getByLabel('Box').fill('non-existent/box');
    await page.getByLabel('CPU Cores').fill('2');
    await page.getByLabel('Memory (MB)').fill('2048');
    
    // Submit the form
    await page.getByRole('button', { name: 'Create VM' }).click();
    
    // Wait for the VM to be created (it will be created but with errors)
    await page.waitForURL('**/vagrant/vms/*');
    
    // Try to start the VM, which should fail
    await page.getByRole('button', { name: 'Start' }).click();
    
    // Wait for the error message
    await page.waitForSelector('.error-message', { timeout: 30000 });
    
    // Check that the error message is displayed
    const errorMessage = await page.locator('.error-message').textContent() || '';
    expect(errorMessage).toContain('error');
    
    // Cleanup: Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
  });
  
  test('should display VM status history', async ({ page }) => {
    // Create a new VM
    const vmId = await createVM(page);
    
    // Navigate to VM history page
    await page.goto(`/vagrant/vms/${vmId}/history`);
    
    // Check that the history page is displayed
    await expect(page.getByRole('heading', { name: 'VM Status History' })).toBeVisible();
    
    // Check that the initial status is displayed
    await expect(page.locator('.status-history-item')).toBeVisible();
    await expect(page.locator('.status-history-item').first()).toContainText('CREATED');
    
    // Start the VM to create a new history entry
    await page.goto(`/vagrant/vms/${vmId}`);
    await page.getByRole('button', { name: 'Start' }).click();
    await waitForVMStatus(page, vmId, 'RUNNING');
    
    // Navigate back to history page
    await page.goto(`/vagrant/vms/${vmId}/history`);
    
    // Check that the new status is displayed
    await expect(page.locator('.status-history-item').nth(1)).toContainText('RUNNING');
    
    // Cleanup: Stop and delete the VM
    await page.goto(`/vagrant/vms/${vmId}`);
    await page.getByRole('button', { name: 'Stop' }).click();
    await waitForVMStatus(page, vmId, 'STOPPED');
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
  });
  
  test('should handle multiple file uploads', async ({ page }) => {
    // Create a new VM
    const vmId = await createVM(page);
    
    // Start the VM
    await page.getByRole('button', { name: 'Start' }).click();
    await waitForVMStatus(page, vmId, 'RUNNING');
    
    // Navigate to VM file upload page
    await page.goto(`/vagrant/vms/${vmId}/files`);
    
    // Create multiple test files
    const testFiles = [
      { name: 'test-file1.txt', content: 'Content of file 1' },
      { name: 'test-file2.txt', content: 'Content of file 2' },
      { name: 'test-file3.txt', content: 'Content of file 3' }
    ];
    
    const filePaths = testFiles.map(file => {
      const filePath = path.join(__dirname, file.name);
      require('fs').writeFileSync(filePath, file.content);
      return filePath;
    });
    
    // Fill the destination directory
    await page.getByLabel('Destination Path').fill('/tmp/');
    
    // Upload multiple files
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(filePaths);
    
    // Submit the upload
    await page.getByRole('button', { name: 'Upload' }).click();
    
    // Wait for all uploads to complete
    await page.waitForSelector('.all-uploads-complete', { timeout: 60000 });
    
    // Verify each file was uploaded by executing commands
    for (const file of testFiles) {
      const commandOutput = await executeCommand(page, vmId, `cat /tmp/${file.name}`);
      expect(commandOutput).toContain(file.content);
    }
    
    // Cleanup: Stop and delete the VM
    await page.goto(`/vagrant/vms/${vmId}`);
    await page.getByRole('button', { name: 'Stop' }).click();
    await waitForVMStatus(page, vmId, 'STOPPED');
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
    
    // Clean up the test files
    filePaths.forEach(filePath => require('fs').unlinkSync(filePath));
  });
  
  test('should display real-time VM metrics', async ({ page }) => {
    // Create a new VM
    const vmId = await createVM(page);
    
    // Start the VM
    await page.getByRole('button', { name: 'Start' }).click();
    await waitForVMStatus(page, vmId, 'RUNNING');
    
    // Navigate to VM metrics page
    await page.goto(`/vagrant/vms/${vmId}/metrics`);
    
    // Check that the metrics page is displayed
    await expect(page.getByRole('heading', { name: 'VM Metrics' })).toBeVisible();
    
    // Wait for metrics to load
    await page.waitForSelector('.metrics-loaded', { timeout: 30000 });
    
    // Check that CPU and memory metrics are displayed
    await expect(page.locator('.cpu-usage-chart')).toBeVisible();
    await expect(page.locator('.memory-usage-chart')).toBeVisible();
    
    // Generate some load on the VM to see metrics change
    await executeCommand(page, vmId, 'dd if=/dev/zero of=/dev/null bs=1M count=1000');
    
    // Wait for metrics to update
    await page.waitForTimeout(5000);
    
    // Check that metrics have updated
    const cpuUsageText = await page.locator('.cpu-usage-value').textContent() || '0';
    expect(parseFloat(cpuUsageText)).toBeGreaterThan(0);
    
    // Cleanup: Stop and delete the VM
    await page.goto(`/vagrant/vms/${vmId}`);
    await page.getByRole('button', { name: 'Stop' }).click();
    await waitForVMStatus(page, vmId, 'STOPPED');
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
  });
  
  test('should validate VM creation form', async ({ page }) => {
    // Navigate to VM creation page
    await page.goto('/vagrant/vms/new');
    
    // Submit empty form
    await page.getByRole('button', { name: 'Create VM' }).click();
    
    // Check validation errors
    await expect(page.locator('.error-message')).toBeVisible();
    await expect(page.locator('.error-message')).toContainText('required');
    
    // Fill with invalid values
    await page.getByLabel('VM Name').fill('invalid name with spaces');
    await page.getByLabel('CPU Cores').fill('-1');
    await page.getByLabel('Memory (MB)').fill('100000000');
    
    // Submit form with invalid values
    await page.getByRole('button', { name: 'Create VM' }).click();
    
    // Check validation errors
    await expect(page.locator('.error-message')).toBeVisible();
    await expect(page.locator('.error-message')).toContainText('invalid');
    
    // Fill with valid values
    await page.getByLabel('VM Name').fill('valid-name');
    await page.getByLabel('Box').fill('ubuntu/focal64');
    await page.getByLabel('CPU Cores').fill('2');
    await page.getByLabel('Memory (MB)').fill('2048');
    
    // Submit form with valid values
    await page.getByRole('button', { name: 'Create VM' }).click();
    
    // Wait for the VM to be created
    await page.waitForURL('**/vagrant/vms/*');
    
    // Cleanup: Delete the VM
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForURL('/vagrant/vms');
  });
}); 