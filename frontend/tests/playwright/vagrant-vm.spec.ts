import { test, expect } from '@playwright/test';

test.describe('Vagrant VM UI', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the Vagrant VM page
    await page.goto('/vagrant_vm');
    
    // Wait for the page to load
    await page.waitForSelector('.vagrant-vm-container', { timeout: 10000 });
  });
  
  test('should display the Vagrant VM page', async ({ page }) => {
    // Check if the page title is visible
    await expect(page.locator('h1:has-text("Vagrant VM Management")')).toBeVisible();
    
    // Check if the create button is visible
    await expect(page.locator('button:has-text("Create VM")')).toBeVisible();
    
    // Check if the VM table is visible
    await expect(page.locator('.vagrant-vm-table')).toBeVisible();
  });
  
  test('should open create modal when clicking create button', async ({ page }) => {
    // Click the create button
    await page.click('button:has-text("Create VM")');
    
    // Check if the modal is visible
    await expect(page.locator('.ant-modal-title:has-text("Create Vagrant VM")')).toBeVisible();
    
    // Check if the form elements are visible
    await expect(page.locator('input[placeholder="Enter VM name"]')).toBeVisible();
    await expect(page.locator('textarea[placeholder="Enter VM description"]')).toBeVisible();
    await expect(page.locator('label:has-text("Template")')).toBeVisible();
    await expect(page.locator('label:has-text("Memory (MB)")')).toBeVisible();
    await expect(page.locator('label:has-text("CPUs")')).toBeVisible();
    await expect(page.locator('label:has-text("Disk (GB)")')).toBeVisible();
    await expect(page.locator('label:has-text("Auto Start")')).toBeVisible();
    
    // Close the modal
    await page.click('button:has-text("Cancel")');
  });
  
  test('should create a new Vagrant VM', async ({ page }) => {
    // Click the create button
    await page.click('button:has-text("Create VM")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Create Vagrant VM")')).toBeVisible();
    
    // Fill in the form
    await page.fill('input[placeholder="Enter VM name"]', 'Test VM');
    await page.fill('textarea[placeholder="Enter VM description"]', 'This is a test VM');
    
    // Select a template
    await page.click('.ant-select');
    await page.waitForSelector('.ant-select-dropdown');
    await page.click('.ant-select-item:has-text("Ubuntu 20.04")');
    
    // Set resources
    await page.fill('input[aria-label="Memory (MB)"]', '1024');
    await page.fill('input[aria-label="CPUs"]', '1');
    await page.fill('input[aria-label="Disk (GB)"]', '20');
    
    // Enable auto start
    await page.click('button.ant-switch');
    
    // Submit the form
    await page.click('button:has-text("Create")');
    
    // Wait for the creation to complete and modal to close
    await expect(page.locator('.ant-modal-title:has-text("Create Vagrant VM")')).not.toBeVisible({ timeout: 10000 });
    
    // Check if success message appears
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Check if the VM appears in the table
    await expect(page.locator('td:has-text("Test VM")')).toBeVisible({ timeout: 10000 });
  });
  
  test('should perform VM actions', async ({ page }) => {
    // Find a VM that's not running
    const vmRow = page.locator('.ant-table-row').first();
    const vmStatus = await vmRow.locator('td:nth-child(4)').textContent();
    
    if (vmStatus?.toLowerCase().includes('stopped')) {
      // Start the VM
      await vmRow.locator('button[aria-label="Start VM"]').click();
      
      // Check if success message appears
      await expect(page.locator('.ant-message-success')).toBeVisible();
      
      // Wait for status to change
      await expect(vmRow.locator('tag:has-text("running")')).toBeVisible({ timeout: 30000 });
    } else if (vmStatus?.toLowerCase().includes('running')) {
      // Stop the VM
      await vmRow.locator('button[aria-label="Stop VM"]').click();
      
      // Confirm in the modal
      await page.click('button:has-text("Yes")');
      
      // Check if success message appears
      await expect(page.locator('.ant-message-success')).toBeVisible();
      
      // Wait for status to change
      await expect(vmRow.locator('tag:has-text("stopped")')).toBeVisible({ timeout: 30000 });
    }
  });
  
  test('should show VM details', async ({ page }) => {
    // Click on the first VM row to show details
    await page.locator('.ant-table-row').first().click();
    
    // Check if the details modal is visible
    await expect(page.locator('.ant-modal-title:has-text("VM Details")')).toBeVisible();
    
    // Check if details sections are visible
    await expect(page.locator('div:has-text("VM Overview")')).toBeVisible();
    await expect(page.locator('div:has-text("VM Status")')).toBeVisible();
    await expect(page.locator('div:has-text("VM Configuration")')).toBeVisible();
    
    // Close the modal
    await page.click('button:has-text("Close")');
  });
  
  test('should delete a VM', async ({ page }) => {
    // Count the number of rows before deletion
    const initialRowCount = await page.locator('.ant-table-row').count();
    
    // Find the first delete button in the table
    const deleteButton = page.locator('.ant-table-row').first().locator('button[aria-label="Delete VM"]');
    
    // Check if the button exists
    await expect(deleteButton).toBeVisible();
    
    // Click the delete button
    await deleteButton.click();
    
    // Confirm deletion in the modal
    await page.click('button:has-text("Yes")');
    
    // Check if success message appears
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Verify row was deleted
    await expect(page.locator('.ant-table-row')).toHaveCount(initialRowCount - 1);
  });
}); 