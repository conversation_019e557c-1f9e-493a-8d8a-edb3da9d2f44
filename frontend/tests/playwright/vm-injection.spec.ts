import { test, expect } from '@playwright/test';

test.describe('VM Injection UI', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the VM injection page
    await page.goto('/vm_injection');
    
    // Wait for the page to load
    await page.waitForSelector('.vm-injection-container', { timeout: 10000 });
  });
  
  test('should display the VM injection page', async ({ page }) => {
    // Check if the page title is visible
    await expect(page.locator('h1:has-text("VM Injection")')).toBeVisible();
    
    // Check if the create button is visible
    await expect(page.locator('button:has-text("Create Injection")')).toBeVisible();
    
    // Check if the injection table is visible
    await expect(page.locator('.vm-injection-table')).toBeVisible();
  });
  
  test('should open create modal when clicking create button', async ({ page }) => {
    // Click the create button
    await page.click('button:has-text("Create Injection")');
    
    // Check if the modal is visible
    await expect(page.locator('.ant-modal-title:has-text("Create VM Injection")')).toBeVisible();
    
    // Check if the form elements are visible
    await expect(page.locator('label:has-text("VM")')).toBeVisible();
    await expect(page.locator('label:has-text("File Selection")')).toBeVisible();
    await expect(page.locator('textarea[placeholder="Enter injection description"]')).toBeVisible();
    await expect(page.locator('textarea[placeholder="Enter command to run after injection"]')).toBeVisible();
    
    // Close the modal
    await page.click('button:has-text("Cancel")');
  });
  
  test('should create a new VM injection', async ({ page }) => {
    // First, ensure we have at least one running VM and one file selection
    
    // Check for running VMs
    await page.goto('/vagrant_vm');
    await page.waitForSelector('.vagrant-vm-container', { timeout: 10000 });
    
    // Look for a running VM
    const runningVMExists = await page.locator('tag:has-text("running")').count() > 0;
    
    if (!runningVMExists) {
      // Create and start a VM if none are running
      await page.click('button:has-text("Create VM")');
      await expect(page.locator('.ant-modal-title:has-text("Create Vagrant VM")')).toBeVisible();
      
      await page.fill('input[placeholder="Enter VM name"]', 'Test VM for Injection');
      await page.fill('textarea[placeholder="Enter VM description"]', 'VM for injection testing');
      
      await page.click('.ant-select');
      await page.waitForSelector('.ant-select-dropdown');
      await page.click('.ant-select-item:has-text("Ubuntu 20.04")');
      
      await page.fill('input[aria-label="Memory (MB)"]', '1024');
      await page.fill('input[aria-label="CPUs"]', '1');
      await page.fill('input[aria-label="Disk (GB)"]', '20');
      
      // Enable auto start
      await page.click('button.ant-switch');
      
      await page.click('button:has-text("Create")');
      await expect(page.locator('.ant-modal-title:has-text("Create Vagrant VM")')).not.toBeVisible({ timeout: 10000 });
      
      // Wait for VM to start
      await page.waitForTimeout(5000);
    }
    
    // Check for file selections
    await page.goto('/file_selection');
    await page.waitForSelector('.file-selection-container', { timeout: 10000 });
    
    const fileSelectionExists = await page.locator('.ant-table-row').count() > 0;
    
    if (!fileSelectionExists) {
      // First upload a file
      await page.goto('/file_upload');
      await page.waitForSelector('.file-upload-container', { timeout: 10000 });
      
      await page.click('button:has-text("Upload File")');
      await expect(page.locator('.ant-modal-title:has-text("Upload File")')).toBeVisible();
      
      const fileChooserPromise = page.waitForEvent('filechooser');
      await page.click('.ant-upload');
      const fileChooser = await fileChooserPromise;
      await fileChooser.setFiles({
        name: 'test-file-for-injection.txt',
        mimeType: 'text/plain',
        buffer: Buffer.from('This is a test file for injection testing'),
      });
      
      await page.fill('textarea[placeholder="Enter file description"]', 'Test file for injection');
      await page.click('button:has-text("Upload")');
      await expect(page.locator('.ant-modal-title:has-text("Upload File")')).not.toBeVisible({ timeout: 10000 });
      
      // Create file selection
      await page.goto('/file_selection');
      await page.waitForSelector('.file-selection-container', { timeout: 10000 });
      
      await page.click('button:has-text("Create New")');
      await expect(page.locator('.ant-modal-title:has-text("Create File Selection")')).toBeVisible();
      
      await page.fill('input[placeholder="Enter a name"]', 'Test File Selection for Injection');
      await page.fill('textarea[placeholder="Enter a description"]', 'File selection for injection testing');
      
      await page.click('.ant-select');
      await page.waitForSelector('.ant-select-dropdown');
      await page.click('.ant-select-item');
      
      await page.fill('input[placeholder="Enter target path"]', '/tmp/test-injection-file.txt');
      await page.fill('input[placeholder="Enter file permissions"]', '0644');
      
      await page.click('button:has-text("Create")');
      await expect(page.locator('.ant-modal-title:has-text("Create File Selection")')).not.toBeVisible({ timeout: 10000 });
    }
    
    // Now go to VM injection page
    await page.goto('/vm_injection');
    await page.waitForSelector('.vm-injection-container', { timeout: 10000 });
    
    // Click the create button
    await page.click('button:has-text("Create Injection")');
    
    // Wait for the modal to appear
    await expect(page.locator('.ant-modal-title:has-text("Create VM Injection")')).toBeVisible();
    
    // Select VM
    await page.click('.ant-select').first();
    await page.waitForSelector('.ant-select-dropdown');
    await page.click('.ant-select-item').first();
    
    // Select file selection
    await page.click('.ant-select').nth(1);
    await page.waitForSelector('.ant-select-dropdown');
    await page.click('.ant-select-item').first();
    
    // Fill in description and command
    await page.fill('textarea[placeholder="Enter injection description"]', 'Test VM injection');
    await page.fill('textarea[placeholder="Enter command to run after injection"]', 'cat /tmp/test-injection-file.txt');
    
    // Submit the form
    await page.click('button:has-text("Create")');
    
    // Wait for the creation to complete and modal to close
    await expect(page.locator('.ant-modal-title:has-text("Create VM Injection")')).not.toBeVisible({ timeout: 10000 });
    
    // Check if success message appears
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Check if the injection appears in the table
    await expect(page.locator('td:has-text("Test VM injection")')).toBeVisible({ timeout: 10000 });
  });
  
  test('should show injection details', async ({ page }) => {
    // Find the first details button in the table
    const detailsButton = page.locator('.ant-table-row').first().locator('button[aria-label="View injection details"]');
    
    // Check if the button exists
    await expect(detailsButton).toBeVisible();
    
    // Click the details button
    await detailsButton.click();
    
    // Check if the modal is visible
    await expect(page.locator('.ant-modal-title:has-text("Injection Details")')).toBeVisible();
    
    // Check if details sections are visible
    await expect(page.locator('div:has-text("Injection Status")')).toBeVisible();
    await expect(page.locator('div:has-text("VM Information")')).toBeVisible();
    await expect(page.locator('div:has-text("File Information")')).toBeVisible();
    
    // Close the modal
    await page.click('button:has-text("Close")');
  });
  
  test('should retry a failed injection', async ({ page }) => {
    // Find a failed injection if any
    const failedInjectionRow = page.locator('.ant-table-row:has(.ant-tag:has-text("failed"))').first();
    const failedInjectionExists = await failedInjectionRow.count() > 0;
    
    if (failedInjectionExists) {
      // Click the retry button
      await failedInjectionRow.locator('button[aria-label="Retry injection"]').click();
      
      // Check if success message appears
      await expect(page.locator('.ant-message-success')).toBeVisible();
      
      // Check if status changes to pending or in progress
      await expect(failedInjectionRow.locator('.ant-tag:has-text("pending"), .ant-tag:has-text("in progress")')).toBeVisible({ timeout: 10000 });
    } else {
      // Skip this test if no failed injections
      test.skip();
    }
  });
  
  test('should delete an injection', async ({ page }) => {
    // Count the number of rows before deletion
    const initialRowCount = await page.locator('.ant-table-row').count();
    
    // Find the first delete button in the table
    const deleteButton = page.locator('.ant-table-row').first().locator('button[aria-label="Delete injection"]');
    
    // Check if the button exists
    await expect(deleteButton).toBeVisible();
    
    // Click the delete button
    await deleteButton.click();
    
    // Confirm deletion in the modal
    await page.click('button:has-text("Yes")');
    
    // Check if success message appears
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Verify row was deleted
    await expect(page.locator('.ant-table-row')).toHaveCount(initialRowCount - 1);
  });
}); 