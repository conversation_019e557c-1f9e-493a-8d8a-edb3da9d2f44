import { test, expect } from '@playwright/test';

/**
 * Tests for VM Status page error handling and API connection features
 */
test.describe('VM Status Error Handling', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });

  test('should show error state for network errors and retry functionality', async ({ page }) => {
    // Mock network error responses for all API calls
    await page.route('**/api/v1/virtual-machines/**', route => route.abort('failed'));
    await page.route('**/api/v1/vagrant_vm/**', route => route.abort('failed'));
    await page.route('**/api/vagrant_vm/**', route => route.abort('failed'));

    // Navigate to the VM status page
    await page.goto('/vm_status');

    // Wait for the page to load
    await page.waitForSelector('.vm-status-container', { timeout: 10000 });

    // Check if the error message is displayed
    await expect(page.locator('.ant-alert-warning')).toBeVisible();
    await expect(page.locator('.ant-alert-warning')).toContainText('Network error');

    // Check if the API offline indicator is shown
    await expect(page.locator('tag:has-text("API Offline")')).toBeVisible();

    // Check if retry button is visible
    await expect(page.locator('button:has-text("Retry Connection")')).toBeVisible();

    // Check if VM list shows the appropriate message
    await expect(page.locator('.vm-list-card')).toContainText('No VMs found');

    // Now change the mock to return a successful response
    await page.unroute('**/api/v1/virtual-machines/**');
    await page.unroute('**/api/v1/vagrant_vm/**');
    await page.unroute('**/api/vagrant_vm/**');

    await page.route('**/api/v1/virtual-machines', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [
            {
              id: '1',
              name: 'Test VM 1',
              status: 'running',
              description: 'Test VM 1 description',
              template: 'ubuntu',
              memory_mb: 1024,
              cpus: 1,
              disk_gb: 10,
              ip_address: '*************',
              ssh_port: 2222,
              created_on: '2025-03-10T12:00:00Z'
            }
          ],
          total: 1
        })
      });
    });

    // Click the retry button
    await page.click('button:has-text("Retry Connection")');

    // Check if the error message disappears
    await expect(page.locator('.ant-alert-warning')).not.toBeVisible({timeout: 10000});

    // Check if the API offline indicator is removed
    await expect(page.locator('tag:has-text("API Offline")')).not.toBeVisible({timeout: 10000});

    // Check if VM list is now populated
    await expect(page.locator('.vm-list-item')).toBeVisible({timeout: 10000});
    await expect(page.locator('.vm-list-item')).toContainText('Test VM 1');
  });

  test('should handle authentication errors gracefully', async ({ page }) => {
    // Mock unauthorized error response
    await page.route('**/api/v1/virtual-machines/**', async (route) => {
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({
          detail: 'Invalid authentication credentials'
        })
      });
    });

    // Navigate to the VM status page
    await page.goto('/vm_status');

    // Wait for the page to load
    await page.waitForSelector('.vm-status-container', { timeout: 10000 });

    // Check if the appropriate error message is displayed
    await expect(page.locator('.ant-alert-error')).toBeVisible();
    await expect(page.locator('.ant-alert-error')).toContainText('Authentication error');
  });

  test('should handle server errors gracefully', async ({ page }) => {
    // Mock server error response
    await page.route('**/api/v1/virtual-machines/**', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          detail: 'Internal server error'
        })
      });
    });

    // Navigate to the VM status page
    await page.goto('/vm_status');

    // Wait for the page to load
    await page.waitForSelector('.vm-status-container', { timeout: 10000 });

    // Check if the appropriate error message is displayed
    await expect(page.locator('.ant-alert')).toBeVisible();
    await expect(page.locator('.ant-alert')).toContainText('Error');
  });

  test('should handle timeouts gracefully', async ({ page }) => {
    // Mock timeout by delaying the response beyond the client timeout
    await page.route('**/api/v1/virtual-machines/**', async (route) => {
      // Delay longer than the client timeout (which is 8000ms)
      await new Promise(resolve => setTimeout(resolve, 10000));
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ items: [] })
      });
    });

    // Navigate to the VM status page
    await page.goto('/vm_status');

    // Wait for the page to load
    await page.waitForSelector('.vm-status-container', { timeout: 15000 });

    // Check if the appropriate error message is displayed
    await expect(page.locator('.ant-alert')).toBeVisible();
    await expect(page.locator('.ant-alert')).toContainText('timeout');
  });

  test('should update VM list and details in real-time with auto-refresh', async ({ page }) => {
    // First response has one VM
    await page.route('**/api/v1/virtual-machines', async (route, request) => {
      // Check if this is the first call
      if (!page.evaluate(() => window.__vmListCalled)) {
        await page.evaluate(() => { window.__vmListCalled = true; });

        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            items: [
              {
                id: '1',
                name: 'Test VM 1',
                status: 'starting',
                template: 'ubuntu',
                memory_mb: 1024,
                cpus: 1,
                disk_gb: 10,
                created_on: '2025-03-10T12:00:00Z'
              }
            ],
            total: 1
          })
        });
      } else {
        // Second call updates the VM status
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            items: [
              {
                id: '1',
                name: 'Test VM 1',
                status: 'running', // Status changed from starting to running
                template: 'ubuntu',
                memory_mb: 1024,
                cpus: 1,
                disk_gb: 10,
                ip_address: '*************', // IP now assigned
                ssh_port: 2222, // Port now assigned
                created_on: '2025-03-10T12:00:00Z'
              }
            ],
            total: 1
          })
        });
      }
    });

    // API endpoints for VM details
    await page.route('**/api/v1/virtual-machines/1', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '1',
          name: 'Test VM 1',
          status: 'running',
          template: 'ubuntu',
          memory_mb: 1024,
          cpus: 1,
          disk_gb: 10,
          ip_address: '*************',
          ssh_port: 2222,
          created_on: '2025-03-10T12:00:00Z'
        })
      });
    });

    // Other APIs (injections, logs, resources)
    await page.route('**/api/v1/virtual-machines/injections/**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ items: [] })
      });
    });

    await page.route('**/api/v1/virtual-machines/1/logs', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ logs: [] })
      });
    });

    await page.route('**/api/v1/virtual-machines/1/resources', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          cpu_percent: 0,
          memory_used_mb: 0,
          memory_total_mb: 1024,
          disk_used_gb: 0,
          disk_total_gb: 10
        })
      });
    });

    // Navigate to the VM status page
    await page.goto('/vm_status');

    // Wait for the page to load
    await page.waitForSelector('.vm-status-container', { timeout: 10000 });

    // First we should see 'starting' status
    await expect(page.locator('.vm-list-item')).toContainText('starting');

    // Wait for auto-refresh to happen and check for status change
    await expect(page.locator('.vm-list-item')).toContainText('running', { timeout: 10000 });
  });

  test('should handle switching between API endpoints', async ({ page }) => {
    // Primary endpoint fails
    await page.route('**/api/v1/virtual-machines/**', route => route.abort('failed'));

    // Fallback endpoint works
    await page.route('**/api/vagrant_vm/**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [
            {
              id: '1',
              name: 'Fallback VM',
              status: 'running',
              template: 'ubuntu',
              memory_mb: 1024,
              cpus: 1,
              disk_gb: 10,
              created_on: '2025-03-10T12:00:00Z'
            }
          ],
          total: 1
        })
      });
    });

    // Navigate to the VM status page
    await page.goto('/vm_status');

    // Wait for the page to load
    await page.waitForSelector('.vm-status-container', { timeout: 10000 });

    // Check if fallback data was loaded
    await expect(page.locator('.vm-list-item')).toContainText('Fallback VM');
  });

  test('should show cached data when API is unreachable', async ({ page }) => {
    // First load - successful
    await page.route('**/api/v1/virtual-machines/**', async (route, request) => {
      // Check if this is the first call
      if (!page.evaluate(() => window.__cachedDataCall)) {
        await page.evaluate(() => { window.__cachedDataCall = true; });

        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            items: [
              {
                id: '1',
                name: 'Cached VM Data',
                status: 'running',
                template: 'ubuntu',
                memory_mb: 1024,
                cpus: 1,
                disk_gb: 10,
                created_on: '2025-03-10T12:00:00Z'
              }
            ],
            total: 1
          })
        });
      } else {
        // Subsequent calls fail
        await route.abort('failed');
      }
    });

    // Navigate to the VM status page
    await page.goto('/vm_status');

    // Wait for the page to load with data
    await page.waitForSelector('.vm-list-item:has-text("Cached VM Data")', { timeout: 10000 });

    // Manually refresh to trigger a failure
    await page.click('button:has-text("Refresh")');

    // Check for error message
    await expect(page.locator('.ant-alert-warning')).toBeVisible();

    // Check that the cached data is still displayed
    await expect(page.locator('.vm-list-item')).toContainText('Cached VM Data');

    // Check for fallback message
    await expect(page.locator('text=Using cached data')).toBeVisible({timeout: 15000});
  });
});