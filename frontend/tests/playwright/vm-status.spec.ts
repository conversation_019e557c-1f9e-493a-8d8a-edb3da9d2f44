import { test, expect } from '@playwright/test';

test.describe('VM Status UI', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the VM status page
    await page.goto('/vm_status');
    
    // Wait for the page to load
    await page.waitForSelector('.vm-status-container', { timeout: 10000 });
  });
  
  test('should display the VM status dashboard', async ({ page }) => {
    // Check if the page title is visible
    await expect(page.locator('h2:has-text("VM Status Dashboard")')).toBeVisible();
    
    // Check if the refresh button is visible
    await expect(page.locator('button:has-text("Refresh")')).toBeVisible();
    
    // Check if the VM list sidebar is visible
    await expect(page.locator('.vm-status-sidebar')).toBeVisible();
  });
  
  test('should display VM list in the sidebar', async ({ page }) => {
    // Mock API response for VM list
    await page.route('**/api/vagrant_vm/', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [
            {
              id: '1',
              name: 'Test VM 1',
              status: 'running',
              description: 'Test VM 1 description',
              template: 'ubuntu',
              memory_mb: 1024,
              cpus: 1,
              disk_gb: 10,
              ip_address: '*************',
              ssh_port: 2222,
              created_on: '2025-03-10T12:00:00Z'
            },
            {
              id: '2',
              name: 'Test VM 2',
              status: 'stopped',
              description: 'Test VM 2 description',
              template: 'centos',
              memory_mb: 2048,
              cpus: 2,
              disk_gb: 20,
              ip_address: null,
              ssh_port: null,
              created_on: '2025-03-09T12:00:00Z'
            }
          ],
          total: 2
        })
      });
    });
    
    // Reload the page to trigger the mocked API response
    await page.reload();
    
    // Check if VM list items are visible
    await expect(page.locator('.vm-list-item')).toHaveCount(2);
    await expect(page.locator('.vm-list-item:has-text("Test VM 1")')).toBeVisible();
    await expect(page.locator('.vm-list-item:has-text("Test VM 2")')).toBeVisible();
  });
  
  test('should display VM details when a VM is selected', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/vagrant_vm/', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [
            {
              id: '1',
              name: 'Test VM 1',
              status: 'running',
              description: 'Test VM 1 description',
              template: 'ubuntu',
              memory_mb: 1024,
              cpus: 1,
              disk_gb: 10,
              ip_address: '*************',
              ssh_port: 2222,
              created_on: '2025-03-10T12:00:00Z'
            }
          ],
          total: 1
        })
      });
    });
    
    await page.route('**/api/vagrant_vm/1', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '1',
          name: 'Test VM 1',
          status: 'running',
          description: 'Test VM 1 description',
          template: 'ubuntu',
          memory_mb: 1024,
          cpus: 1,
          disk_gb: 10,
          ip_address: '*************',
          ssh_port: 2222,
          vagrant_id: 'test-vagrant-id',
          created_on: '2025-03-10T12:00:00Z',
          modified_on: null,
          owner_id: 'test-owner-id',
          last_action: 'start',
          last_action_time: '2025-03-10T12:30:00Z',
          error_message: null
        })
      });
    });
    
    await page.route('**/api/vm_injection/**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [
            {
              id: '1',
              vagrant_vm_id: '1',
              file_selection_id: 'file-1',
              description: 'Test injection',
              status: 'completed',
              additional_command: 'echo "Hello World"',
              error_message: null,
              created_on: '2025-03-10T13:00:00Z',
              modified_on: '2025-03-10T13:05:00Z',
              completed_on: '2025-03-10T13:05:00Z',
              owner_id: 'test-owner-id',
              file_info: {
                id: 'file-1',
                filename: 'test-file.txt',
                file_size: 1024,
                content_type: 'text/plain',
                download_url: '/api/file_upload/file-1/download'
              }
            }
          ],
          total: 1
        })
      });
    });
    
    await page.route('**/api/vagrant_vm/1/logs', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          logs: [
            {
              timestamp: '2025-03-10T13:00:00Z',
              level: 'INFO',
              message: 'VM started successfully'
            },
            {
              timestamp: '2025-03-10T13:05:00Z',
              level: 'INFO',
              message: 'File injection completed'
            }
          ]
        })
      });
    });
    
    await page.route('**/api/vagrant_vm/1/resources', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          cpu_percent: 25.5,
          memory_used_mb: 512,
          memory_total_mb: 1024,
          disk_used_gb: 3.5,
          disk_total_gb: 10
        })
      });
    });
    
    // Reload the page to trigger the mocked API responses
    await page.reload();
    
    // Wait for VM list to load and click on the first VM
    await page.waitForSelector('.vm-list-item');
    await page.click('.vm-list-item:has-text("Test VM 1")');
    
    // Check if VM details are displayed
    await expect(page.locator('.vm-status-title:has-text("Test VM 1")')).toBeVisible();
    await expect(page.locator('tag:has-text("RUNNING")')).toBeVisible();
    
    // Check if tabs are displayed
    await expect(page.locator('div[role="tab"]:has-text("Overview")')).toBeVisible();
    await expect(page.locator('div[role="tab"]:has-text("Injections")')).toBeVisible();
    await expect(page.locator('div[role="tab"]:has-text("Logs")')).toBeVisible();
    
    // Check overview tab content
    await expect(page.locator('div.ant-descriptions-item-content:has-text("Test VM 1")')).toBeVisible();
    await expect(page.locator('div.ant-descriptions-item-content:has-text("ubuntu")')).toBeVisible();
    await expect(page.locator('div.ant-descriptions-item-content:has-text("*************")')).toBeVisible();
    
    // Check resource usage
    await expect(page.locator('.resource-usage-container')).toBeVisible();
    await expect(page.locator('.ant-statistic-title:has-text("CPU Usage")')).toBeVisible();
    await expect(page.locator('.ant-statistic-title:has-text("Memory Usage")')).toBeVisible();
    await expect(page.locator('.ant-statistic-title:has-text("Disk Usage")')).toBeVisible();
  });
  
  test('should switch between tabs', async ({ page }) => {
    // Mock API responses (similar to previous test)
    await page.route('**/api/vagrant_vm/', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [{ id: '1', name: 'Test VM 1', status: 'running' }],
          total: 1
        })
      });
    });
    
    await page.route('**/api/vagrant_vm/1**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '1', name: 'Test VM 1', status: 'running', template: 'ubuntu'
        })
      });
    });
    
    await page.route('**/api/vm_injection/**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [{ 
            id: '1', 
            vagrant_vm_id: '1', 
            status: 'completed',
            created_on: '2025-03-10T13:00:00Z',
            file_info: { filename: 'test-file.txt' }
          }],
          total: 1
        })
      });
    });
    
    await page.route('**/api/vagrant_vm/1/logs', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          logs: [{ timestamp: '2025-03-10T13:00:00Z', level: 'INFO', message: 'VM started' }]
        })
      });
    });
    
    await page.route('**/api/vagrant_vm/1/resources', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          cpu_percent: 25, memory_used_mb: 512, memory_total_mb: 1024,
          disk_used_gb: 3, disk_total_gb: 10
        })
      });
    });
    
    // Reload the page and select the VM
    await page.reload();
    await page.waitForSelector('.vm-list-item');
    await page.click('.vm-list-item');
    
    // Check overview tab is active by default
    await expect(page.locator('div[role="tabpanel"]')).toContainText('VM Information');
    
    // Switch to Injections tab
    await page.click('div[role="tab"]:has-text("Injections")');
    await expect(page.locator('div[role="tabpanel"]')).toContainText('File Injections');
    await expect(page.locator('div[role="tabpanel"]')).toContainText('test-file.txt');
    
    // Switch to Logs tab
    await page.click('div[role="tab"]:has-text("Logs")');
    await expect(page.locator('div[role="tabpanel"]')).toContainText('VM Logs');
    await expect(page.locator('div[role="tabpanel"]')).toContainText('VM started');
  });
  
  test('should handle refresh button click', async ({ page }) => {
    // Mock API responses
    let requestCount = 0;
    
    await page.route('**/api/vagrant_vm/', async (route) => {
      requestCount++;
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [{ 
            id: '1', 
            name: requestCount > 1 ? 'Updated VM Name' : 'Test VM 1', 
            status: 'running' 
          }],
          total: 1
        })
      });
    });
    
    // Other route mocks similar to previous tests
    await page.route('**/api/vagrant_vm/1**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ id: '1', name: 'Test VM 1', status: 'running' })
      });
    });
    
    // Load the page
    await page.reload();
    await page.waitForSelector('.vm-list-item');
    
    // Check initial VM name
    await expect(page.locator('.vm-list-item')).toContainText('Test VM 1');
    
    // Click refresh button
    await page.click('button:has-text("Refresh")');
    
    // Check updated VM name
    await expect(page.locator('.vm-list-item')).toContainText('Updated VM Name');
  });
}); 