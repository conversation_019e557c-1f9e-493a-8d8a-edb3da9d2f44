import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, theme, Layout } from 'antd';
import { AuthProvider } from './hooks/useAuth';

// Import pages
import MainPage from './pages/MainPage';
import FileUploadPage from './pages/File_upload';
import FileSelectionPage from './pages/File_selection';
import VagrantVMPage from './pages/Vagrant_vm';
import VMInjectionPage from './pages/Vm_injection';
import VmStatusPage from './pages/Vm_status';
import DocsPage from './pages/DocsPage';

// Import components
import LanguageSwitcher from './components/LanguageSwitcher';

// Import styles
import './App.css';

const { Header } = Layout;

const App: React.FC = () => {
  // Check if dark mode is enabled
  const isDarkMode = localStorage.getItem('darkMode') === 'true';
  
  return (
    <ConfigProvider
      theme={{
        algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
      }}
    >
      <AuthProvider>
        <Router>
          <Layout>
            <Header style={{ display: 'flex', justifyContent: 'flex-end', padding: '0 16px' }}>
              <LanguageSwitcher />
            </Header>
            <Routes>
              <Route path="/" element={<MainPage />} />
              <Route path="/file_upload" element={<FileUploadPage />} />
              <Route path="/file_selection" element={<FileSelectionPage />} />
              <Route path="/vagrant_vm" element={<VagrantVMPage />} />
              <Route path="/vm_injection" element={<VMInjectionPage />} />
              <Route path="/vm_status" element={<VmStatusPage />} />
              <Route path="/docs" element={<DocsPage />} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Layout>
        </Router>
      </AuthProvider>
    </ConfigProvider>
  );
};

export default App; 