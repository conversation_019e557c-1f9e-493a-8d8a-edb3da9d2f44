import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios from 'axios';

interface AuthContextType {
  token: string | null;
  user: any | null;
  login: (token: string, user: any) => void;
  logout: () => void;
  isAuthenticated: boolean;
}

// Define a type for the response data
interface TestTokenResponse {
  access_token: string;
  [key: string]: any;
}

const AuthContext = createContext<AuthContextType>({
  token: null,
  user: null,
  login: () => {},
  logout: () => {},
  isAuthenticated: false,
});

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [token, setToken] = useState<string | null>(localStorage.getItem('authToken'));
  const [user, setUser] = useState<any | null>(
    localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user') || '{}') : null
  );
  
  // Set up axios interceptor for authentication
  useEffect(() => {
    const requestInterceptor = axios.interceptors.request.use(
      (config) => {
        if (token) {
          // Fix for config.headers possibly being undefined
          if (!config.headers) {
            config.headers = {};
          }
          config.headers['Authorization'] = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );
    
    return () => {
      axios.interceptors.request.eject(requestInterceptor);
    };
  }, [token]);
  
  // Check for test token on initial load if not authenticated
  useEffect(() => {
    const getTestToken = async () => {
      if (!token) {
        try {
          const response = await axios.get<TestTokenResponse>('/api/auth/test-token');
          // Type assertion to fix response.data being unknown
          const data = response.data as TestTokenResponse;
          if (data.access_token) {
            const testUser = {
              id: '00000000-0000-0000-0000-000000000000',
              username: 'testuser',
              email: '<EMAIL>',
              is_active: true,
              is_superuser: true
            };
            login(data.access_token, testUser);
            console.log('Using test token for authentication');
          }
        } catch (error) {
          console.error('Error getting test token:', error);
        }
      }
    };
    
    getTestToken();
  }, []);
  
  const login = (newToken: string, newUser: any) => {
    localStorage.setItem('authToken', newToken);
    localStorage.setItem('user', JSON.stringify(newUser));
    setToken(newToken);
    setUser(newUser);
  };
  
  const logout = () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    setToken(null);
    setUser(null);
  };
  
  return (
    <AuthContext.Provider
      value={{
        token,
        user,
        login,
        logout,
        isAuthenticated: !!token,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext); 