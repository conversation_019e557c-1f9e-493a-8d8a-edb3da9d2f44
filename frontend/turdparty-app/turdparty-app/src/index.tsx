import React, { Suspense } from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { Spin } from 'antd';
import './i18n'; // Import i18n configuration

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

// Loading component for suspense fallback
const Loader = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '100vh' 
  }}>
    <Spin size="large" />
  </div>
);

root.render(
  <React.StrictMode>
    <Suspense fallback={<Loader />}>
      <App />
    </Suspense>
  </React.StrictMode>
); 