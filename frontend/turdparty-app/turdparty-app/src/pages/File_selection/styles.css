.file-selection-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.file-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.file-selection-table {
  margin-top: 20px;
}

.file-selection-alert {
  margin-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

/* Table styles */
.file-selection-table .ant-table-thead > tr > th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.file-selection-table .ant-table-tbody > tr:hover > td {
  background-color: #f0f7ff;
}

/* Button spacing in action column */
.file-selection-table .ant-space {
  gap: 8px;
}

/* File path styling */
.file-selection-table .ant-table-cell:nth-child(3) {
  font-family: monospace;
  color: #0066cc;
}

/* Permissions styling */
.file-selection-table code {
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: monospace;
}

/* Form styles */
.ant-form-item-label > label {
  font-weight: 500;
}

.ant-select-selection-item {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Target path input styling */
.ant-input-group-addon {
  background-color: #f5f5f5;
  color: #666;
  font-weight: 500;
}
