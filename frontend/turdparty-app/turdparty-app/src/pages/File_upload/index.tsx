import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './styles.css';
import { Card, Button, Spin, Alert, Modal, Form, Input, Upload, Table, Space, Tag, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, UploadOutlined, DownloadOutlined, InboxOutlined } from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import type { TableProps } from 'antd/es/table';

interface FileUploadData {
  id: string;
  filename: string;
  file_size: number;
  content_type: string;
  file_hash: string;
  description: string;
  download_url: string;
  created_on: string;
  modified_on: string | null;
  owner_id: string;
  is_active: boolean;
}

interface FileUploadListResponse {
  items: FileUploadData[];
  total: number;
}

const FileUploadPage: React.FC = () => {
  const [data, setData] = useState<FileUploadData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isUploadModalVisible, setIsUploadModalVisible] = useState<boolean>(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState<boolean>(false);
  const [editItem, setEditItem] = useState<FileUploadData | null>(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState<boolean>(false);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { token } = useAuth();
  
  const fetchFileUploadData = async () => {
    try {
      setLoading(true);
      const response = await axios.get<FileUploadListResponse>('/api/file_upload', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      setData(response.data.items);
      setError(null);
    } catch (err) {
      console.error('Error fetching file upload data:', err);
      setError('Failed to load file upload data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFileUploadData();
  }, [token]);

  const showUploadModal = () => {
    setFileList([]);
    setIsUploadModalVisible(true);
  };

  const showEditModal = (item: FileUploadData) => {
    setEditItem(item);
    form.setFieldsValue({
      filename: item.filename,
      description: item.description,
      is_active: item.is_active
    });
    setIsEditModalVisible(true);
  };

  const handleUploadCancel = () => {
    setIsUploadModalVisible(false);
    setFileList([]);
  };

  const handleEditCancel = () => {
    setIsEditModalVisible(false);
    setEditItem(null);
    form.resetFields();
  };

  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.error('Please select a file to upload');
      return;
    }

    const formData = new FormData();
    fileList.forEach(file => {
      formData.append('file', file as any);
    });
    
    // Add description if provided
    const description = form.getFieldValue('description');
    if (description) {
      formData.append('description', description);
    }

    setUploading(true);

    try {
      await axios.post('/api/file_upload', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      setFileList([]);
      setIsUploadModalVisible(false);
      message.success('File uploaded successfully');
      fetchFileUploadData();
    } catch (error) {
      console.error('Upload error:', error);
      message.error('File upload failed');
    } finally {
      setUploading(false);
    }
  };

  const handleEdit = async () => {
    if (!editItem) return;
    
    try {
      const values = await form.validateFields();
      
      await axios.put(`/api/file_upload/${editItem.id}`, values, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      setIsEditModalVisible(false);
      message.success('File updated successfully');
      fetchFileUploadData();
    } catch (err) {
      console.error('Form submission error:', err);
      message.error('Failed to update file');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await axios.delete(`/api/file_upload/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      message.success('File deleted successfully');
      fetchFileUploadData();
    } catch (err) {
      console.error('Error deleting file:', err);
      message.error('Failed to delete file');
    }
  };

  const handleDownload = (url: string, filename: string) => {
    // Create a temporary link element
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const uploadProps: UploadProps = {
    onRemove: file => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: file => {
      setFileList([file]);
      return false;
    },
    fileList,
    maxCount: 1,
  };

  const columns: TableProps<FileUploadData>['columns'] = [
    {
      title: 'Filename',
      dataIndex: 'filename',
      key: 'filename',
      render: (text, record) => (
        <a onClick={() => handleDownload(record.download_url, record.filename)}>
          {text}
        </a>
      ),
    },
    {
      title: 'Size',
      dataIndex: 'file_size',
      key: 'file_size',
      render: (size) => {
        const kb = size / 1024;
        if (kb < 1024) {
          return `${kb.toFixed(2)} KB`;
        } else {
          return `${(kb / 1024).toFixed(2)} MB`;
        }
      },
    },
    {
      title: 'Type',
      dataIndex: 'content_type',
      key: 'content_type',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (text) => text || 'No description',
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Uploaded',
      dataIndex: 'created_on',
      key: 'created_on',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          <Button 
            icon={<DownloadOutlined />} 
            onClick={() => handleDownload(record.download_url, record.filename)}
            title="Download"
          />
          <Button 
            icon={<EditOutlined />} 
            onClick={() => showEditModal(record)}
            title="Edit"
          />
          <Button 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record.id)}
            title="Delete"
          />
        </Space>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <p>Loading file upload data...</p>
      </div>
    );
  }

  return (
    <div className="file-upload-container">
      <div className="file-upload-header">
        <h1>File Upload Management</h1>
        <Button 
          type="primary" 
          icon={<UploadOutlined />} 
          onClick={showUploadModal}
        >
          Upload New File
        </Button>
      </div>
      
      {error && <Alert message={error} type="error" className="file-upload-alert" />}
      
      <div className="file-upload-table">
        <Table 
          columns={columns} 
          dataSource={data.map(item => ({ ...item, key: item.id }))} 
          pagination={{ pageSize: 10 }}
          bordered
        />
      </div>
      
      {/* Upload Modal */}
      <Modal
        title="Upload New File"
        open={isUploadModalVisible}
        onOk={handleUpload}
        onCancel={handleUploadCancel}
        okText="Upload"
        confirmLoading={uploading}
      >
        <Form layout="vertical">
          <Form.Item>
            <Upload.Dragger {...uploadProps}>
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">Click or drag file to this area to upload</p>
              <p className="ant-upload-hint">
                Support for a single file upload. Strictly prohibited from uploading company data or other
                banned files.
              </p>
            </Upload.Dragger>
          </Form.Item>
          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea rows={4} placeholder="Enter a description for this file" />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* Edit Modal */}
      <Modal
        title={editItem ? `Edit ${editItem.filename}` : ""}
        open={isEditModalVisible}
        onOk={handleEdit}
        onCancel={handleEditCancel}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="filename"
            label="Filename"
            rules={[{ required: true, message: 'Please enter a filename' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea rows={4} />
          </Form.Item>
          <Form.Item
            name="is_active"
            label="Status"
            valuePropName="checked"
          >
            <Input type="checkbox" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FileUploadPage;
