.file-upload-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.file-upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.file-upload-table {
  margin-top: 20px;
}

.file-upload-alert {
  margin-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

/* Customize the upload dragger */
.ant-upload-drag {
  border: 2px dashed #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.3s;
}

.ant-upload-drag:hover {
  border-color: #1890ff;
}

.ant-upload-drag-icon {
  font-size: 48px;
  color: #1890ff;
}

.ant-upload-text {
  margin: 0 0 4px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
}

.ant-upload-hint {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

/* Table styles */
.file-upload-table .ant-table-thead > tr > th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.file-upload-table .ant-table-tbody > tr:hover > td {
  background-color: #f0f7ff;
}

/* Button spacing in action column */
.file-upload-table .ant-space {
  gap: 8px;
}

/* File size column alignment */
.file-upload-table .ant-table-cell:nth-child(2) {
  text-align: right;
}

/* Status tag styling */
.file-upload-table .ant-tag {
  min-width: 60px;
  text-align: center;
}
