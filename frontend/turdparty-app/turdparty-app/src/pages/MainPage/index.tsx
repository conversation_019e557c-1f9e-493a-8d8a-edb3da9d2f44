import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './styles.css';
import { 
  Card, Button, Spin, Alert, Upload, Form, Input, Select, 
  Steps, Divider, Switch, Typography, message, Space, Row, Col, Radio, Tabs
} from 'antd';
import { 
  UploadOutlined, SendOutlined, FileOutlined, FolderOutlined,
  DesktopOutlined, BulbOutlined, BulbFilled, 
  RocketOutlined, CheckCircleOutlined, InboxOutlined
} from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;
const { TextArea } = Input;
const { Dragger } = Upload;
const { TabPane } = Tabs;

interface FileInfo {
  id: string;
  filename: string;
  file_size: number;
  content_type: string;
  download_url: string;
}

interface TemplateInfo {
  id: string;
  name: string;
  description: string;
  category: string;
}

interface FileUploadResponse {
  id: string;
  filename: string;
  file_size: number;
  content_type: string;
  file_hash: string;
  description: string;
  download_url: string;
  created_on: string;
}

const MainPage: React.FC = () => {
  const navigate = useNavigate();
  const { token } = useAuth();
  
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [darkMode, setDarkMode] = useState<boolean>(localStorage.getItem('darkMode') === 'true');
  
  // File upload state
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [fileDescription, setFileDescription] = useState<string>('');
  const [uploadedFileId, setUploadedFileId] = useState<string | null>(null);
  const [uploadType, setUploadType] = useState<'file' | 'folder'>('file');
  
  // Template selection state
  const [templates, setTemplates] = useState<TemplateInfo[]>([
    { id: 'template1', name: 'Basic Web App', description: 'A simple web application template with HTML, CSS, and JavaScript', category: 'web' },
    { id: 'template2', name: 'React App', description: 'A React application template with TypeScript and Webpack', category: 'web' },
    { id: 'template3', name: 'Node.js API', description: 'A Node.js API template with Express and MongoDB', category: 'backend' },
    { id: 'template4', name: 'Python Flask App', description: 'A Python Flask application template', category: 'backend' },
    { id: 'template5', name: 'Django App', description: 'A Python Django application template', category: 'backend' },
    { id: 'template6', name: 'Vue.js App', description: 'A Vue.js application template with Vuex and Vue Router', category: 'web' },
    { id: 'template7', name: 'Angular App', description: 'An Angular application template with TypeScript', category: 'web' },
    { id: 'template8', name: 'Spring Boot API', description: 'A Java Spring Boot API template', category: 'backend' }
  ]);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [templateCategory, setTemplateCategory] = useState<string>('all');
  
  // Target configuration state
  const [targetPath, setTargetPath] = useState<string>('/app');
  const [targetPermissions, setTargetPermissions] = useState<string>('0755');
  
  useEffect(() => {
    // Apply dark mode to /docs iframe if it exists
    const docsFrame = document.getElementById('docs-frame') as HTMLIFrameElement;
    if (docsFrame) {
      try {
        const frameDoc = docsFrame.contentDocument || docsFrame.contentWindow?.document;
        if (frameDoc) {
          if (darkMode) {
            frameDoc.body.classList.add('dark-mode');
          } else {
            frameDoc.body.classList.remove('dark-mode');
          }
        }
      } catch (e) {
        console.error('Cannot access iframe document:', e);
      }
    }
    
    // Save preference
    localStorage.setItem('darkMode', darkMode.toString());
  }, [darkMode]);
  
  const handleDarkModeToggle = () => {
    setDarkMode(!darkMode);
  };
  
  // File upload handlers
  const fileUploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    maxCount: 1,
    fileList,
    beforeUpload: (file) => {
      setFileList([file]);
      return false; // Prevent auto upload
    },
    onRemove: () => {
      setFileList([]);
      return true;
    }
  };
  
  const folderUploadProps: UploadProps = {
    name: 'file',
    multiple: true,
    directory: true,
    fileList,
    beforeUpload: (file) => {
      setFileList(prev => [...prev, file]);
      return false; // Prevent auto upload
    },
    onRemove: (file) => {
      setFileList(prev => prev.filter(f => f.uid !== file.uid));
      return true;
    }
  };
  
  const handleFileUpload = async () => {
    if (fileList.length === 0) {
      message.error('Please select a file or folder to upload');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const formData = new FormData();
      
      if (uploadType === 'file') {
        formData.append('file', fileList[0] as any);
        formData.append('description', fileDescription);
        
        const response = await axios.post('/api/v1/files/upload', formData, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'multipart/form-data'
          }
        });
        
        setUploadedFileId(response.data.id);
        setSuccess(`File uploaded successfully: ${response.data.filename}`);
      } else {
        // For folder uploads, we need to preserve the folder structure
        // We'll create a special formData with path information
        fileList.forEach(file => {
          formData.append('files', file as any);
          // Add the relative path for each file
          formData.append('paths', (file as any).webkitRelativePath || file.name);
        });
        formData.append('description', fileDescription);
        
        const response = await axios.post('/api/folder_upload/', formData, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'multipart/form-data'
          }
        });
        
        setUploadedFileId(response.data.folder_id);
        setSuccess(`Folder uploaded successfully with ${fileList.length} files`);
      }
      
      setCurrentStep(1);
    } catch (err: any) {
      setError(`Error uploading ${uploadType}: ${err.response?.data?.detail || err.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  // Template selection handlers
  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
  };
  
  const handleTemplateConfirm = () => {
    if (!selectedTemplate) {
      message.error('Please select a template');
      return;
    }
    
    setCurrentStep(2);
  };
  
  const filteredTemplates = templateCategory === 'all' 
    ? templates 
    : templates.filter(t => t.category === templateCategory);
  
  // Target configuration handlers
  const handleTargetConfirm = async () => {
    if (!uploadedFileId || !selectedTemplate) {
      message.error('Missing upload or template selection');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.post('/api/template_injection/', {
        file_upload_id: uploadedFileId,
        template_id: selectedTemplate,
        target_path: targetPath,
        permissions: targetPermissions
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      setSuccess('Template injection created successfully');
      setCurrentStep(3);
    } catch (err: any) {
      setError(`Error creating template injection: ${err.response?.data?.detail || err.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  const resetWorkflow = () => {
    setCurrentStep(0);
    setFileList([]);
    setFileDescription('');
    setUploadedFileId(null);
    setSelectedTemplate(null);
    setTargetPath('/app');
    setTargetPermissions('0755');
    setSuccess(null);
    setError(null);
  };
  
  return (
    <div className="main-page-container">
      <Card className="main-page-card">
        <div className="main-page-header">
          <Title level={2}>TurdParty File Upload & Template Selection</Title>
          <Space>
            <Button 
              type="primary" 
              icon={<FileOutlined />} 
              onClick={() => navigate('/file_upload')}
            >
              File Management
            </Button>
            <Button 
              type="primary" 
              icon={<DesktopOutlined />} 
              onClick={() => navigate('/vagrant_vm')}
            >
              VM Management
            </Button>
            <Switch 
              checked={darkMode} 
              onChange={handleDarkModeToggle} 
              checkedChildren="Dark" 
              unCheckedChildren="Light" 
            />
          </Space>
        </div>
        
        <Divider />
        
        <Steps current={currentStep} className="main-page-steps">
          <Step title="Upload" description="Select a file or folder" />
          <Step title="Template" description="Choose a template" />
          <Step title="Configure" description="Set target path and permissions" />
          <Step title="Complete" description="Process complete" />
        </Steps>
        
        <Divider />
        
        {error && (
          <Alert
            message="Error"
            description={error}
            type="error"
            showIcon
            closable
            className="main-page-alert"
            onClose={() => setError(null)}
          />
        )}
        
        {success && (
          <Alert
            message="Success"
            description={success}
            type="success"
            showIcon
            closable
            className="main-page-alert"
            onClose={() => setSuccess(null)}
          />
        )}
        
        <Spin spinning={loading} tip="Processing...">
          <div className="main-page-content">
            {currentStep === 0 && (
              <Card title="Step 1: Upload File or Folder" className="step-card">
                <Form layout="vertical">
                  <Form.Item label="Upload Type">
                    <Radio.Group 
                      value={uploadType} 
                      onChange={(e) => {
                        setUploadType(e.target.value);
                        setFileList([]);
                      }}
                    >
                      <Radio.Button value="file">Single File</Radio.Button>
                      <Radio.Button value="folder">Folder</Radio.Button>
                    </Radio.Group>
                  </Form.Item>
                  
                  <Form.Item label="Select File or Folder">
                    {uploadType === 'file' ? (
                      <Dragger {...fileUploadProps}>
                        <p className="ant-upload-drag-icon">
                          <FileOutlined />
                        </p>
                        <p className="ant-upload-text">Click or drag file to this area to upload</p>
                        <p className="ant-upload-hint">
                          Support for a single file upload.
                        </p>
                      </Dragger>
                    ) : (
                      <Dragger {...folderUploadProps}>
                        <p className="ant-upload-drag-icon">
                          <FolderOutlined />
                        </p>
                        <p className="ant-upload-text">Click to select a folder to upload</p>
                        <p className="ant-upload-hint">
                          Support for folder upload with preserved structure.
                        </p>
                      </Dragger>
                    )}
                  </Form.Item>
                  
                  <Form.Item label="Description">
                    <TextArea
                      rows={4}
                      placeholder="Enter a description"
                      value={fileDescription}
                      onChange={(e) => setFileDescription(e.target.value)}
                    />
                  </Form.Item>
                  
                  <Form.Item>
                    <Button 
                      type="primary" 
                      onClick={handleFileUpload}
                      disabled={fileList.length === 0}
                    >
                      Upload and Continue
                    </Button>
                  </Form.Item>
                </Form>
              </Card>
            )}
            
            {currentStep === 1 && (
              <Card title="Step 2: Select Template" className="step-card">
                <Form layout="vertical">
                  <Form.Item label="Template Category">
                    <Radio.Group 
                      value={templateCategory} 
                      onChange={(e) => setTemplateCategory(e.target.value)}
                    >
                      <Radio.Button value="all">All</Radio.Button>
                      <Radio.Button value="web">Web</Radio.Button>
                      <Radio.Button value="backend">Backend</Radio.Button>
                    </Radio.Group>
                  </Form.Item>
                  
                  <div className="template-grid">
                    {filteredTemplates.map(template => (
                      <Card 
                        key={template.id}
                        className={`template-card ${selectedTemplate === template.id ? 'selected' : ''}`}
                        hoverable
                        onClick={() => handleTemplateSelect(template.id)}
                      >
                        <div className="template-card-content">
                          <Title level={4}>{template.name}</Title>
                          <Text type="secondary">{template.description}</Text>
                          <Tag color={template.category === 'web' ? 'blue' : 'green'}>
                            {template.category}
                          </Tag>
                        </div>
                      </Card>
                    ))}
                  </div>
                  
                  <Form.Item>
                    <Button 
                      type="primary" 
                      onClick={handleTemplateConfirm}
                      disabled={!selectedTemplate}
                    >
                      Select Template and Continue
                    </Button>
                  </Form.Item>
                </Form>
              </Card>
            )}
            
            {currentStep === 2 && (
              <Card title="Step 3: Configure Target" className="step-card">
                <Form layout="vertical">
                  <Form.Item label="Target Path">
                    <Input
                      placeholder="Enter target path"
                      value={targetPath}
                      onChange={(e) => setTargetPath(e.target.value)}
                      prefix={<FolderOutlined />}
                    />
                  </Form.Item>
                  
                  <Form.Item label="File Permissions">
                    <Input
                      placeholder="Enter file permissions (octal)"
                      value={targetPermissions}
                      onChange={(e) => setTargetPermissions(e.target.value)}
                    />
                  </Form.Item>
                  
                  <Form.Item>
                    <Button 
                      type="primary" 
                      onClick={handleTargetConfirm}
                    >
                      Confirm and Process
                    </Button>
                  </Form.Item>
                </Form>
              </Card>
            )}
            
            {currentStep === 3 && (
              <Card title="Step 4: Process Complete" className="step-card">
                <div className="completion-card">
                  <CheckCircleOutlined className="completion-icon" />
                  <Title level={3}>Template Injection Complete</Title>
                  <Paragraph>
                    Your file/folder has been successfully uploaded and the template has been applied.
                  </Paragraph>
                  
                  <Divider />
                  
                  <Row gutter={16}>
                    <Col span={12}>
                      <Button 
                        type="primary" 
                        icon={<RocketOutlined />}
                        onClick={() => navigate('/file_upload')}
                        block
                      >
                        View Uploaded Files
                      </Button>
                    </Col>
                    <Col span={12}>
                      <Button 
                        onClick={resetWorkflow}
                        block
                      >
                        Start New Upload
                      </Button>
                    </Col>
                  </Row>
                </div>
              </Card>
            )}
          </div>
        </Spin>
      </Card>
    </div>
  );
};

export default MainPage; 