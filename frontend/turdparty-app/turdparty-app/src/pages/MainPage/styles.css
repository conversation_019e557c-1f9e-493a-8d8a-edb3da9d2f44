.main-page-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.main-page-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.main-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.main-page-header h2 {
  margin-bottom: 0;
}

.main-page-steps {
  margin-bottom: 30px;
}

.main-page-alert {
  margin-bottom: 20px;
}

.main-page-content {
  padding: 10px 0;
}

.step-card {
  margin-bottom: 20px;
}

.completion-card {
  text-align: center;
  padding: 20px;
}

.completion-icon {
  font-size: 64px;
  color: #52c41a;
  margin-bottom: 20px;
}

/* Upload component styling */
.ant-upload-drag {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.3s;
  padding: 20px;
}

.ant-upload-drag:hover {
  border-color: #1890ff;
}

.ant-upload-drag-icon {
  font-size: 48px;
  color: #1890ff;
}

.ant-upload-text {
  font-size: 16px;
  margin: 8px 0;
}

.ant-upload-hint {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}

/* Form styling */
.ant-form-item-label > label {
  font-weight: 500;
}

.ant-select-selection-item {
  font-weight: normal;
}

/* Steps styling */
.ant-steps-item-title {
  font-weight: 500;
}

.ant-steps-item-description {
  font-size: 12px;
}

/* Dark mode for docs */
.dark-mode {
  background-color: #1f1f1f;
  color: #f0f0f0;
}

.dark-mode pre {
  background-color: #2d2d2d;
  border: 1px solid #444;
}

.dark-mode code {
  background-color: #2d2d2d;
  color: #e6e6e6;
}

.dark-mode a {
  color: #1890ff;
}

.dark-mode h1, .dark-mode h2, .dark-mode h3, .dark-mode h4, .dark-mode h5, .dark-mode h6 {
  color: #f0f0f0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .main-page-header h2 {
    margin-bottom: 16px;
  }
  
  .template-grid {
    grid-template-columns: 1fr;
  }
}

/* Template grid and cards */
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.template-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
}

.template-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.template-card.selected {
  border: 2px solid #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.template-card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.template-card-content h4 {
  margin-bottom: 8px;
}

.template-card-content .ant-typography-secondary {
  flex-grow: 1;
  margin-bottom: 12px;
}

/* File upload area */
.ant-upload-drag {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  transition: border-color 0.3s;
}

.ant-upload-drag:hover {
  border-color: #1890ff;
}

.ant-upload-drag-icon {
  font-size: 48px;
  color: #1890ff;
}

/* Radio buttons */
.ant-radio-group {
  margin-bottom: 16px;
}

/* Dark mode template cards */
body.dark-mode .template-card {
  background-color: #2a2a2a;
  border-color: #555;
}

body.dark-mode .template-card.selected {
  border-color: #1890ff;
}

body.dark-mode .ant-tag {
  border-color: transparent;
}

/* Folder upload styles */
.folder-upload-info {
  margin-top: 16px;
  padding: 12px;
  background-color: #f6f6f6;
  border-radius: 4px;
}

body.dark-mode .folder-upload-info {
  background-color: #3a3a3a;
}

.file-list-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

body.dark-mode .file-list-item {
  border-bottom-color: #555;
}

.file-list-item-icon {
  margin-right: 8px;
  font-size: 16px;
}

.file-list-item-name {
  flex-grow: 1;
}

.file-list-item-size {
  color: #888;
  font-size: 12px;
}

body.dark-mode .file-list-item-size {
  color: #aaa;
} 