import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './styles.css';
import { 
  Table, Button, Spin, Alert, Modal, Form, Input, Select, 
  Space, Tag, message, Tooltip, InputNumber, Switch, Tabs, 
  Card, Statistic, Badge, Descriptions, Divider, Typography
} from 'antd';
import { 
  PlusOutlined, EditOutlined, DeleteOutlined, 
  PlayCircleOutlined, PauseCircleOutlined, ReloadOutlined, 
  StopOutlined, DesktopOutlined, CodeOutlined, SettingOutlined
} from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import type { TableProps } from 'antd/es/table';

const { TabPane } = Tabs;
const { Title, Paragraph, Text } = Typography;

interface VagrantVMData {
  id: string;
  name: string;
  description: string;
  template: string;
  memory_mb: number;
  cpus: number;
  disk_gb: number;
  status: string;
  ip_address: string | null;
  ssh_port: number | null;
  vagrant_id: string | null;
  created_on: string;
  modified_on: string | null;
  owner_id: string;
  last_action: string | null;
  last_action_time: string | null;
  error_message: string | null;
}

interface VagrantVMListResponse {
  items: VagrantVMData[];
  total: number;
}

interface VagrantVMStatusResponse {
  id: string;
  name: string;
  status: string;
  ip_address: string | null;
  ssh_port: number | null;
  last_action: string | null;
  last_action_time: string | null;
  error_message: string | null;
}

const VagrantVMPage: React.FC = () => {
  const [data, setData] = useState<VagrantVMData[]>([]);
  const [selectedVM, setSelectedVM] = useState<VagrantVMData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isCreateModalVisible, setIsCreateModalVisible] = useState<boolean>(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState<boolean>(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState<boolean>(false);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { token } = useAuth();
  
  const fetchVagrantVMData = async () => {
    try {
      setLoading(true);
      const response = await axios.get<VagrantVMListResponse>('/api/vagrant_vm', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      setData(response.data.items);
      setError(null);
    } catch (err) {
      console.error('Error fetching Vagrant VM data:', err);
      setError('Failed to load Vagrant VM data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVagrantVMData();
    
    // Set up polling for VM status updates
    const intervalId = setInterval(() => {
      if (!actionLoading) {
        fetchVagrantVMData();
      }
    }, 10000); // Poll every 10 seconds
    
    return () => clearInterval(intervalId);
  }, [token, actionLoading]);

  const showCreateModal = () => {
    form.resetFields();
    form.setFieldsValue({
      memory_mb: 1024,
      cpus: 1,
      disk_gb: 20,
      template: 'ubuntu_2004',
      auto_start: true
    });
    setIsCreateModalVisible(true);
  };

  const showEditModal = (vm: VagrantVMData) => {
    setSelectedVM(vm);
    form.setFieldsValue({
      name: vm.name,
      description: vm.description,
      memory_mb: vm.memory_mb,
      cpus: vm.cpus,
      disk_gb: vm.disk_gb
    });
    setIsEditModalVisible(true);
  };

  const showDetailModal = (vm: VagrantVMData) => {
    setSelectedVM(vm);
    setIsDetailModalVisible(true);
  };

  const handleCreateCancel = () => {
    setIsCreateModalVisible(false);
  };

  const handleEditCancel = () => {
    setIsEditModalVisible(false);
    setSelectedVM(null);
  };

  const handleDetailCancel = () => {
    setIsDetailModalVisible(false);
    setSelectedVM(null);
  };

  const handleCreate = async () => {
    try {
      const values = await form.validateFields();
      
      await axios.post('/api/vagrant_vm', values, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      setIsCreateModalVisible(false);
      message.success('Vagrant VM created successfully');
      fetchVagrantVMData();
    } catch (err) {
      console.error('Form submission error:', err);
      message.error('Failed to create Vagrant VM');
    }
  };

  const handleEdit = async () => {
    if (!selectedVM) return;
    
    try {
      const values = await form.validateFields();
      
      await axios.put(`/api/vagrant_vm/${selectedVM.id}`, values, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      setIsEditModalVisible(false);
      message.success('Vagrant VM updated successfully');
      fetchVagrantVMData();
    } catch (err) {
      console.error('Form submission error:', err);
      message.error('Failed to update Vagrant VM');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      setActionLoading(true);
      await axios.delete(`/api/vagrant_vm/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      message.success('Vagrant VM deleted successfully');
      fetchVagrantVMData();
    } catch (err) {
      console.error('Error deleting Vagrant VM:', err);
      message.error('Failed to delete Vagrant VM');
    } finally {
      setActionLoading(false);
    }
  };

  const handleVMAction = async (id: string, action: string) => {
    try {
      setActionLoading(true);
      await axios.post(`/api/vagrant_vm/${id}/action`, { action }, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      message.success(`VM ${action} action initiated`);
      fetchVagrantVMData();
    } catch (err) {
      console.error(`Error performing ${action} action:`, err);
      message.error(`Failed to ${action} VM`);
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'green';
      case 'stopped':
        return 'red';
      case 'pending':
        return 'blue';
      case 'error':
        return 'volcano';
      case 'destroyed':
        return 'gray';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <PlayCircleOutlined style={{ color: 'green' }} />;
      case 'stopped':
        return <PauseCircleOutlined style={{ color: 'red' }} />;
      case 'pending':
        return <Spin size="small" />;
      case 'error':
        return <StopOutlined style={{ color: 'red' }} />;
      case 'destroyed':
        return <DeleteOutlined style={{ color: 'gray' }} />;
      default:
        return null;
    }
  };

  const columns: TableProps<VagrantVMData>['columns'] = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <a onClick={() => showDetailModal(record)}>
          <DesktopOutlined style={{ marginRight: 8 }} />
          {text}
        </a>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag icon={getStatusIcon(status)} color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Template',
      dataIndex: 'template',
      key: 'template',
      render: (template) => {
        const templateName = template.replace(/_/g, ' ').toUpperCase();
        return <Tag color="blue">{templateName}</Tag>;
      },
    },
    {
      title: 'Resources',
      key: 'resources',
      render: (_, record) => (
        <span>
          {record.cpus} CPU{record.cpus > 1 ? 's' : ''} | {record.memory_mb} MB | {record.disk_gb} GB
        </span>
      ),
    },
    {
      title: 'IP Address',
      dataIndex: 'ip_address',
      key: 'ip_address',
      render: (ip) => ip || '-',
    },
    {
      title: 'Created',
      dataIndex: 'created_on',
      key: 'created_on',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          {record.status === 'stopped' && (
            <Button 
              icon={<PlayCircleOutlined />} 
              onClick={() => handleVMAction(record.id, 'start')}
              title="Start"
              type="primary"
              size="small"
              loading={actionLoading}
            />
          )}
          {record.status === 'running' && (
            <>
              <Button 
                icon={<PauseCircleOutlined />} 
                onClick={() => handleVMAction(record.id, 'stop')}
                title="Stop"
                size="small"
                loading={actionLoading}
              />
              <Button 
                icon={<ReloadOutlined />} 
                onClick={() => handleVMAction(record.id, 'restart')}
                title="Restart"
                size="small"
                loading={actionLoading}
              />
            </>
          )}
          <Button 
            icon={<EditOutlined />} 
            onClick={() => showEditModal(record)}
            title="Edit"
            size="small"
            disabled={record.status === 'pending'}
          />
          <Button 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record.id)}
            title="Delete"
            size="small"
            disabled={record.status === 'pending'}
          />
        </Space>
      ),
    },
  ];

  if (loading && data.length === 0) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <p>Loading Vagrant VM data...</p>
      </div>
    );
  }

  return (
    <div className="vagrant-vm-container">
      <div className="vagrant-vm-header">
        <h1>Vagrant VM Management</h1>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={showCreateModal}
        >
          Create New VM
        </Button>
      </div>
      
      {error && <Alert message={error} type="error" className="vagrant-vm-alert" />}
      
      <div className="vagrant-vm-table">
        <Table 
          columns={columns} 
          dataSource={data.map(item => ({ ...item, key: item.id }))} 
          pagination={{ pageSize: 10 }}
          bordered
          loading={loading}
        />
      </div>
      
      {/* Create VM Modal */}
      <Modal
        title="Create New Vagrant VM"
        open={isCreateModalVisible}
        onOk={handleCreate}
        onCancel={handleCreateCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="VM Name"
            rules={[{ required: true, message: 'Please enter a name' }]}
          >
            <Input placeholder="Enter a name for this VM" />
          </Form.Item>
          
          <Form.Item
            name="template"
            label="VM Template"
            rules={[{ required: true, message: 'Please select a template' }]}
          >
            <Select
              placeholder="Select a template"
              options={[
                { value: 'ubuntu_2004', label: 'Ubuntu 20.04 LTS' },
                { value: 'ubuntu_2204', label: 'Ubuntu 22.04 LTS' },
                { value: 'debian_11', label: 'Debian 11' },
                { value: 'centos_7', label: 'CentOS 7' },
                { value: 'centos_8', label: 'CentOS 8' },
                { value: 'windows_10', label: 'Windows 10' },
                { value: 'windows_server_2019', label: 'Windows Server 2019' },
                { value: 'custom', label: 'Custom Vagrantfile' }
              ]}
            />
          </Form.Item>
          
          <div className="resource-inputs">
            <Form.Item
              name="cpus"
              label="CPUs"
              rules={[{ required: true, message: 'Please enter CPU count' }]}
            >
              <InputNumber min={1} max={8} />
            </Form.Item>
            
            <Form.Item
              name="memory_mb"
              label="Memory (MB)"
              rules={[{ required: true, message: 'Please enter memory size' }]}
            >
              <InputNumber min={512} max={16384} step={512} />
            </Form.Item>
            
            <Form.Item
              name="disk_gb"
              label="Disk (GB)"
              rules={[{ required: true, message: 'Please enter disk size' }]}
            >
              <InputNumber min={5} max={100} />
            </Form.Item>
          </div>
          
          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea rows={4} placeholder="Enter a description for this VM" />
          </Form.Item>
          
          <Form.Item
            name="auto_start"
            label="Auto Start"
            valuePropName="checked"
          >
            <Switch defaultChecked />
          </Form.Item>
          
          <Form.Item
            name="custom_vagrantfile"
            label="Custom Vagrantfile"
            hidden={form.getFieldValue('template') !== 'custom'}
          >
            <Input.TextArea rows={8} placeholder="Enter your custom Vagrantfile content" />
          </Form.Item>
          
          <Form.Item
            name="provision_script"
            label="Provision Script"
          >
            <Input.TextArea rows={4} placeholder="Enter a shell script to run during provisioning" />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* Edit VM Modal */}
      <Modal
        title={selectedVM ? `Edit ${selectedVM.name}` : ""}
        open={isEditModalVisible}
        onOk={handleEdit}
        onCancel={handleEditCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="VM Name"
            rules={[{ required: true, message: 'Please enter a name' }]}
          >
            <Input placeholder="Enter a name for this VM" />
          </Form.Item>
          
          <div className="resource-inputs">
            <Form.Item
              name="cpus"
              label="CPUs"
              rules={[{ required: true, message: 'Please enter CPU count' }]}
            >
              <InputNumber min={1} max={8} />
            </Form.Item>
            
            <Form.Item
              name="memory_mb"
              label="Memory (MB)"
              rules={[{ required: true, message: 'Please enter memory size' }]}
            >
              <InputNumber min={512} max={16384} step={512} />
            </Form.Item>
            
            <Form.Item
              name="disk_gb"
              label="Disk (GB)"
              rules={[{ required: true, message: 'Please enter disk size' }]}
            >
              <InputNumber min={5} max={100} />
            </Form.Item>
          </div>
          
          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea rows={4} placeholder="Enter a description for this VM" />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* VM Detail Modal */}
      <Modal
        title={selectedVM ? `${selectedVM.name} Details` : ""}
        open={isDetailModalVisible}
        onCancel={handleDetailCancel}
        footer={null}
        width={700}
      >
        {selectedVM && (
          <div className="vm-details">
            <Tabs defaultActiveKey="overview">
              <TabPane tab="Overview" key="overview">
                <div className="vm-overview">
                  <div className="vm-status-card">
                    <Card>
                      <Statistic
                        title="Status"
                        value={selectedVM.status.toUpperCase()}
                        valueStyle={{ color: getStatusColor(selectedVM.status) === 'green' ? '#3f8600' : '#cf1322' }}
                        prefix={getStatusIcon(selectedVM.status)}
                      />
                      {selectedVM.last_action && (
                        <div className="last-action">
                          <Text type="secondary">Last action: {selectedVM.last_action}</Text>
                          <br />
                          <Text type="secondary">
                            {selectedVM.last_action_time && 
                              `at ${new Date(selectedVM.last_action_time).toLocaleString()}`
                            }
                          </Text>
                        </div>
                      )}
                    </Card>
                  </div>
                  
                  <Descriptions title="VM Information" bordered column={2}>
                    <Descriptions.Item label="ID">{selectedVM.id}</Descriptions.Item>
                    <Descriptions.Item label="Template">{selectedVM.template.replace(/_/g, ' ').toUpperCase()}</Descriptions.Item>
                    <Descriptions.Item label="CPUs">{selectedVM.cpus}</Descriptions.Item>
                    <Descriptions.Item label="Memory">{selectedVM.memory_mb} MB</Descriptions.Item>
                    <Descriptions.Item label="Disk">{selectedVM.disk_gb} GB</Descriptions.Item>
                    <Descriptions.Item label="IP Address">{selectedVM.ip_address || 'Not assigned'}</Descriptions.Item>
                    <Descriptions.Item label="SSH Port">{selectedVM.ssh_port || 'Not assigned'}</Descriptions.Item>
                    <Descriptions.Item label="Created">{new Date(selectedVM.created_on).toLocaleString()}</Descriptions.Item>
                  </Descriptions>
                  
                  {selectedVM.description && (
                    <div className="vm-description">
                      <Title level={5}>Description</Title>
                      <Paragraph>{selectedVM.description}</Paragraph>
                    </div>
                  )}
                  
                  {selectedVM.error_message && (
                    <Alert
                      message="Error"
                      description={selectedVM.error_message}
                      type="error"
                      showIcon
                      style={{ marginTop: 16 }}
                    />
                  )}
                </div>
              </TabPane>
              
              <TabPane tab="Actions" key="actions">
                <div className="vm-actions">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Card title="VM Control">
                      <Space>
                        <Button 
                          type="primary" 
                          icon={<PlayCircleOutlined />} 
                          onClick={() => handleVMAction(selectedVM.id, 'start')}
                          disabled={selectedVM.status === 'running' || selectedVM.status === 'pending'}
                          loading={actionLoading}
                        >
                          Start
                        </Button>
                        <Button 
                          icon={<PauseCircleOutlined />} 
                          onClick={() => handleVMAction(selectedVM.id, 'stop')}
                          disabled={selectedVM.status !== 'running' || selectedVM.status === 'pending'}
                          loading={actionLoading}
                        >
                          Stop
                        </Button>
                        <Button 
                          icon={<ReloadOutlined />} 
                          onClick={() => handleVMAction(selectedVM.id, 'restart')}
                          disabled={selectedVM.status !== 'running' || selectedVM.status === 'pending'}
                          loading={actionLoading}
                        >
                          Restart
                        </Button>
                        <Button 
                          danger 
                          icon={<DeleteOutlined />} 
                          onClick={() => {
                            handleVMAction(selectedVM.id, 'destroy');
                            setIsDetailModalVisible(false);
                          }}
                          disabled={selectedVM.status === 'pending'}
                          loading={actionLoading}
                        >
                          Destroy
                        </Button>
                      </Space>
                    </Card>
                    
                    <Card title="VM Management">
                      <Space>
                        <Button 
                          icon={<EditOutlined />} 
                          onClick={() => {
                            setIsDetailModalVisible(false);
                            showEditModal(selectedVM);
                          }}
                          disabled={selectedVM.status === 'pending'}
                        >
                          Edit VM
                        </Button>
                        <Button 
                          icon={<SettingOutlined />} 
                          disabled={selectedVM.status !== 'running'}
                        >
                          SSH Connection
                        </Button>
                      </Space>
                    </Card>
                  </Space>
                </div>
              </TabPane>
            </Tabs>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default VagrantVMPage;
