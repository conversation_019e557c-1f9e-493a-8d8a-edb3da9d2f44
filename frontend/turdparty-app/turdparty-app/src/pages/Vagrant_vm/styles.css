.vagrant-vm-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.vagrant-vm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.vagrant-vm-table {
  margin-top: 20px;
}

.vagrant-vm-alert {
  margin-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

/* Table styles */
.vagrant-vm-table .ant-table-thead > tr > th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.vagrant-vm-table .ant-table-tbody > tr:hover > td {
  background-color: #f0f7ff;
}

/* Button spacing in action column */
.vagrant-vm-table .ant-space {
  gap: 8px;
}

/* Resource inputs in form */
.resource-inputs {
  display: flex;
  gap: 16px;
}

.resource-inputs .ant-form-item {
  flex: 1;
}

/* VM Details Modal */
.vm-details {
  margin-top: 16px;
}

.vm-overview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.vm-status-card {
  margin-bottom: 16px;
}

.vm-status-card .ant-statistic-title {
  font-size: 14px;
}

.vm-status-card .ant-statistic-content {
  font-size: 24px;
}

.last-action {
  margin-top: 8px;
  font-size: 12px;
}

.vm-description {
  margin-top: 16px;
}

.vm-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Status tag styling */
.ant-tag .anticon {
  margin-right: 4px;
}

/* Form styles */
.ant-form-item-label > label {
  font-weight: 500;
}

/* Descriptions styling */
.ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: #f5f5f5;
  font-weight: 500;
}

/* Tab styling */
.ant-tabs-tab {
  font-weight: 500;
}

/* Card styling */
.ant-card-head {
  background-color: #f5f5f5;
}

/* Button styling in action cards */
.vm-actions .ant-card-body .ant-space {
  flex-wrap: wrap;
}
