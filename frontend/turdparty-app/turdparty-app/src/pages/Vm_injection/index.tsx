import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './styles.css';
import { 
  Table, Button, Spin, Alert, Modal, Form, Input, Select, 
  Space, Tag, message, Tooltip, Steps, Card, Typography, 
  Descriptions, Divider, Badge
} from 'antd';
import { 
  PlusOutlined, EditOutlined, DeleteOutlined, 
  CheckCircleOutlined, CloseCircleOutlined, SyncOutlined, 
  ClockCircleOutlined, FileOutlined, DesktopOutlined, 
  SendOutlined, CodeOutlined, InfoCircleOutlined, RedoOutlined
} from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import type { TableProps } from 'antd/es/table';

const { Step } = Steps;
const { Title, Paragraph, Text } = Typography;

interface FileInfo {
  id: string;
  filename: string;
  file_size: number;
  content_type: string;
  download_url: string;
}

interface VMInfo {
  id: string;
  name: string;
  status: string;
  ip_address: string | null;
}

interface VMInjectionData {
  id: string;
  vagrant_vm_id: string;
  file_selection_id: string;
  description: string | null;
  status: string;
  additional_command: string | null;
  error_message: string | null;
  created_on: string;
  modified_on: string | null;
  completed_on: string | null;
  owner_id: string;
  vm_info: VMInfo | null;
  file_info: FileInfo | null;
}

interface VMInjectionListResponse {
  items: VMInjectionData[];
  total: number;
}

interface VMInjectionStatusResponse {
  id: string;
  vagrant_vm_id: string;
  file_selection_id: string;
  status: string;
  error_message: string | null;
  completed_on: string | null;
}

interface VagrantVMData {
  id: string;
  name: string;
  status: string;
}

interface VagrantVMListResponse {
  items: VagrantVMData[];
  total: number;
}

interface FileSelectionData {
  id: string;
  name: string;
  file_upload_id: string;
  target_path: string;
  file_info: FileInfo | null;
}

interface FileSelectionListResponse {
  items: FileSelectionData[];
  total: number;
}

const VMInjectionPage: React.FC = () => {
  const [data, setData] = useState<VMInjectionData[]>([]);
  const [vms, setVMs] = useState<VagrantVMData[]>([]);
  const [fileSelections, setFileSelections] = useState<FileSelectionData[]>([]);
  const [selectedInjection, setSelectedInjection] = useState<VMInjectionData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadingVMs, setLoadingVMs] = useState<boolean>(false);
  const [loadingSelections, setLoadingSelections] = useState<boolean>(false);
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isCreateModalVisible, setIsCreateModalVisible] = useState<boolean>(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState<boolean>(false);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { token } = useAuth();
  
  const fetchVMInjectionData = async () => {
    try {
      setLoading(true);
      const response = await axios.get<VMInjectionListResponse>('/api/vm_injection', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      setData(response.data.items);
      setError(null);
    } catch (err) {
      console.error('Error fetching VM injection data:', err);
      setError('Failed to load VM injection data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const fetchVMs = async () => {
    try {
      setLoadingVMs(true);
      const response = await axios.get<VagrantVMListResponse>('/api/vagrant_vm', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      // Only show running VMs for injection
      setVMs(response.data.items.filter(vm => vm.status === 'running'));
      setError(null);
    } catch (err) {
      console.error('Error fetching VMs:', err);
      message.error('Failed to load available VMs');
    } finally {
      setLoadingVMs(false);
    }
  };

  const fetchFileSelections = async () => {
    try {
      setLoadingSelections(true);
      const response = await axios.get<FileSelectionListResponse>('/api/file_selection', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      setFileSelections(response.data.items);
      setError(null);
    } catch (err) {
      console.error('Error fetching file selections:', err);
      message.error('Failed to load available file selections');
    } finally {
      setLoadingSelections(false);
    }
  };

  useEffect(() => {
    fetchVMInjectionData();
    
    // Set up polling for injection status updates
    const intervalId = setInterval(() => {
      if (!actionLoading) {
        fetchVMInjectionData();
      }
    }, 5000); // Poll every 5 seconds
    
    return () => clearInterval(intervalId);
  }, [token, actionLoading]);

  const showCreateModal = () => {
    fetchVMs();
    fetchFileSelections();
    form.resetFields();
    setIsCreateModalVisible(true);
  };

  const showDetailModal = (injection: VMInjectionData) => {
    setSelectedInjection(injection);
    setIsDetailModalVisible(true);
  };

  const handleCreateCancel = () => {
    setIsCreateModalVisible(false);
  };

  const handleDetailCancel = () => {
    setIsDetailModalVisible(false);
    setSelectedInjection(null);
  };

  const handleCreate = async () => {
    try {
      const values = await form.validateFields();
      
      setActionLoading(true);
      await axios.post('/api/vm_injection', values, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      setIsCreateModalVisible(false);
      message.success('VM injection created successfully');
      fetchVMInjectionData();
    } catch (err) {
      console.error('Form submission error:', err);
      message.error('Failed to create VM injection');
    } finally {
      setActionLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      setActionLoading(true);
      await axios.delete(`/api/vm_injection/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      message.success('VM injection deleted successfully');
      fetchVMInjectionData();
    } catch (err) {
      console.error('Error deleting VM injection:', err);
      message.error('Failed to delete VM injection');
    } finally {
      setActionLoading(false);
    }
  };

  const handleRetry = async (id: string) => {
    try {
      setActionLoading(true);
      await axios.post(`/api/vm_injection/${id}/retry`, {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      message.success('VM injection retry initiated');
      fetchVMInjectionData();
    } catch (err) {
      console.error('Error retrying VM injection:', err);
      message.error('Failed to retry VM injection');
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'failed':
        return 'red';
      case 'in_progress':
        return 'blue';
      case 'pending':
        return 'orange';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined style={{ color: 'green' }} />;
      case 'failed':
        return <CloseCircleOutlined style={{ color: 'red' }} />;
      case 'in_progress':
        return <SyncOutlined spin style={{ color: 'blue' }} />;
      case 'pending':
        return <ClockCircleOutlined style={{ color: 'orange' }} />;
      default:
        return null;
    }
  };

  const getStatusStep = (status: string) => {
    switch (status) {
      case 'completed':
        return 2;
      case 'in_progress':
        return 1;
      case 'pending':
        return 0;
      case 'failed':
        return 1; // Failed during injection
      default:
        return 0;
    }
  };

  const columns: TableProps<VMInjectionData>['columns'] = [
    {
      title: 'VM',
      dataIndex: 'vm_info',
      key: 'vm',
      render: (vmInfo) => vmInfo ? (
        <span>
          <DesktopOutlined style={{ marginRight: 8 }} />
          {vmInfo.name}
        </span>
      ) : 'VM not found',
    },
    {
      title: 'File',
      dataIndex: 'file_info',
      key: 'file',
      render: (fileInfo) => fileInfo ? (
        <span>
          <FileOutlined style={{ marginRight: 8 }} />
          {fileInfo.filename}
        </span>
      ) : 'File not found',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag icon={getStatusIcon(status)} color={getStatusColor(status)}>
          {status.replace('_', ' ').toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_on',
      key: 'created_on',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: 'Completed',
      dataIndex: 'completed_on',
      key: 'completed_on',
      render: (date) => date ? new Date(date).toLocaleString() : '-',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          <Button 
            icon={<InfoCircleOutlined />} 
            onClick={() => showDetailModal(record)}
            title="Details"
            size="small"
          />
          {record.status === 'failed' && (
            <Button 
              icon={<RedoOutlined />} 
              onClick={() => handleRetry(record.id)}
              title="Retry"
              size="small"
              loading={actionLoading}
            />
          )}
          <Button 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record.id)}
            title="Delete"
            size="small"
            disabled={record.status === 'in_progress' || record.status === 'pending'}
            loading={actionLoading}
          />
        </Space>
      ),
    },
  ];

  if (loading && data.length === 0) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <p>Loading VM injection data...</p>
      </div>
    );
  }

  return (
    <div className="vm-injection-container">
      <div className="vm-injection-header">
        <h1>VM Injection Management</h1>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={showCreateModal}
        >
          Create New Injection
        </Button>
      </div>
      
      {error && <Alert message={error} type="error" className="vm-injection-alert" />}
      
      <div className="vm-injection-table">
        <Table 
          columns={columns} 
          dataSource={data.map(item => ({ ...item, key: item.id }))} 
          pagination={{ pageSize: 10 }}
          bordered
          loading={loading}
        />
      </div>
      
      {/* Create Injection Modal */}
      <Modal
        title="Create New VM Injection"
        open={isCreateModalVisible}
        onOk={handleCreate}
        onCancel={handleCreateCancel}
        width={600}
        confirmLoading={actionLoading}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="vagrant_vm_id"
            label="Select VM"
            rules={[{ required: true, message: 'Please select a VM' }]}
          >
            <Select
              placeholder="Select a running VM"
              loading={loadingVMs}
              optionFilterProp="children"
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
              options={vms.map(vm => ({
                value: vm.id,
                label: vm.name,
              }))}
              notFoundContent={vms.length === 0 ? 'No running VMs available' : null}
            />
          </Form.Item>
          
          <Form.Item
            name="file_selection_id"
            label="Select File"
            rules={[{ required: true, message: 'Please select a file' }]}
          >
            <Select
              placeholder="Select a file selection"
              loading={loadingSelections}
              optionFilterProp="children"
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
              options={fileSelections.map(selection => ({
                value: selection.id,
                label: `${selection.name} (${selection.file_info?.filename || 'Unknown'} → ${selection.target_path})`,
              }))}
            />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea rows={4} placeholder="Enter a description for this injection" />
          </Form.Item>
          
          <Form.Item
            name="additional_command"
            label="Additional Command"
            help="Command to run after file injection (optional)"
          >
            <Input.TextArea rows={2} placeholder="Enter a command to run after injection" />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* Injection Detail Modal */}
      <Modal
        title={selectedInjection ? `Injection Details` : ""}
        open={isDetailModalVisible}
        onCancel={handleDetailCancel}
        footer={null}
        width={700}
      >
        {selectedInjection && (
          <div className="injection-details">
            <Card className="injection-status-card">
              <Steps current={getStatusStep(selectedInjection.status)} status={selectedInjection.status === 'failed' ? 'error' : 'process'}>
                <Step title="Pending" description="Waiting to start" />
                <Step title="In Progress" description="Injecting file" />
                <Step title="Completed" description="File injected successfully" />
              </Steps>
              
              <div className="injection-status">
                <Tag icon={getStatusIcon(selectedInjection.status)} color={getStatusColor(selectedInjection.status)}>
                  {selectedInjection.status.replace('_', ' ').toUpperCase()}
                </Tag>
                {selectedInjection.completed_on && (
                  <Text type="secondary">
                    Completed at {new Date(selectedInjection.completed_on).toLocaleString()}
                  </Text>
                )}
              </div>
            </Card>
            
            <Divider />
            
            <Descriptions title="Injection Information" bordered column={2}>
              <Descriptions.Item label="ID">{selectedInjection.id}</Descriptions.Item>
              <Descriptions.Item label="Created">{new Date(selectedInjection.created_on).toLocaleString()}</Descriptions.Item>
              
              <Descriptions.Item label="VM" span={2}>
                {selectedInjection.vm_info ? (
                  <span>
                    <DesktopOutlined style={{ marginRight: 8 }} />
                    {selectedInjection.vm_info.name}
                    {selectedInjection.vm_info.ip_address && (
                      <Text type="secondary" style={{ marginLeft: 8 }}>
                        ({selectedInjection.vm_info.ip_address})
                      </Text>
                    )}
                  </span>
                ) : 'VM not found'}
              </Descriptions.Item>
              
              <Descriptions.Item label="File" span={2}>
                {selectedInjection.file_info ? (
                  <span>
                    <FileOutlined style={{ marginRight: 8 }} />
                    {selectedInjection.file_info.filename}
                  </span>
                ) : 'File not found'}
              </Descriptions.Item>
              
              {selectedInjection.additional_command && (
                <Descriptions.Item label="Additional Command" span={2}>
                  <code>{selectedInjection.additional_command}</code>
                </Descriptions.Item>
              )}
            </Descriptions>
            
            {selectedInjection.description && (
              <div className="injection-description">
                <Title level={5}>Description</Title>
                <Paragraph>{selectedInjection.description}</Paragraph>
              </div>
            )}
            
            {selectedInjection.error_message && (
              <Alert
                message="Error"
                description={selectedInjection.error_message}
                type="error"
                showIcon
                style={{ marginTop: 16 }}
              />
            )}
            
            {selectedInjection.status === 'failed' && (
              <div className="retry-action" style={{ marginTop: 16 }}>
                <Button 
                  type="primary" 
                  icon={<RedoOutlined />} 
                  onClick={() => {
                    handleRetry(selectedInjection.id);
                    setIsDetailModalVisible(false);
                  }}
                  loading={actionLoading}
                >
                  Retry Injection
                </Button>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default VMInjectionPage;
