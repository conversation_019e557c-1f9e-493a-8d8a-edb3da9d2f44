import React, { useEffect, useState } from 'react';
import axios from 'axios';
import './styles.css';
import { 
  Table, Button, Spin, Alert, Card, Typography, Descriptions, 
  Tag, Space, Statistic, Tabs, Badge, Divider, Progress, Timeline
} from 'antd';
import { 
  DesktopOutlined, CheckCircleOutlined, CloseCircleOutlined, 
  SyncOutlined, ClockCircleOutlined, InfoCircleOutlined,
  FileOutlined, CodeOutlined, ReloadOutlined, EyeOutlined
} from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import { useTranslation } from 'react-i18next';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

interface VMInfo {
  id: string;
  name: string;
  description: string;
  template: string;
  memory_mb: number;
  cpus: number;
  disk_gb: number;
  status: string;
  ip_address: string | null;
  ssh_port: number | null;
  vagrant_id: string | null;
  created_on: string;
  modified_on: string | null;
  owner_id: string;
  last_action: string | null;
  last_action_time: string | null;
  error_message: string | null;
}

interface VMInjectionInfo {
  id: string;
  vagrant_vm_id: string;
  file_selection_id: string;
  description: string | null;
  status: string;
  additional_command: string | null;
  error_message: string | null;
  created_on: string;
  modified_on: string | null;
  completed_on: string | null;
  owner_id: string;
  file_info: {
    id: string;
    filename: string;
    file_size: number;
    content_type: string;
    download_url: string;
  } | null;
}

interface VMLogEntry {
  timestamp: string;
  level: string;
  message: string;
}

interface VMResourceUsage {
  cpu_percent: number;
  memory_used_mb: number;
  memory_total_mb: number;
  disk_used_gb: number;
  disk_total_gb: number;
}

const VmStatusPage: React.FC = () => {
  const [vms, setVMs] = useState<VMInfo[]>([]);
  const [selectedVM, setSelectedVM] = useState<VMInfo | null>(null);
  const [injections, setInjections] = useState<VMInjectionInfo[]>([]);
  const [logs, setLogs] = useState<VMLogEntry[]>([]);
  const [resourceUsage, setResourceUsage] = useState<VMResourceUsage | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>("overview");
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);
  const { token } = useAuth();
  const { t } = useTranslation('components/vm_status');

  useEffect(() => {
    fetchVMs();
    
    // Set up refresh interval
    const interval = setInterval(() => {
      if (selectedVM) {
        fetchVMDetails(selectedVM.id);
      }
    }, 10000); // Refresh every 10 seconds
    
    setRefreshInterval(interval);
    
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, []);

  useEffect(() => {
    if (selectedVM) {
      fetchVMDetails(selectedVM.id);
    }
  }, [selectedVM]);

  const fetchVMs = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/vagrant_vm/`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setVMs(response.data.items);
      
      // Select the first VM by default if available
      if (response.data.items.length > 0 && !selectedVM) {
        setSelectedVM(response.data.items[0]);
      }
      
      setError(null);
    } catch (err) {
      console.error('Error fetching VMs:', err);
      setError('Failed to fetch VMs. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const fetchVMDetails = async (vmId: string) => {
    try {
      // Fetch VM details
      const vmResponse = await axios.get(`${process.env.REACT_APP_API_URL}/api/vagrant_vm/${vmId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setSelectedVM(vmResponse.data);
      
      // Fetch injections for this VM
      const injectionsResponse = await axios.get(`${process.env.REACT_APP_API_URL}/api/vm_injection/`, {
        params: { vagrant_vm_id: vmId },
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setInjections(injectionsResponse.data.items);
      
      // Fetch VM logs
      const logsResponse = await axios.get(`${process.env.REACT_APP_API_URL}/api/vagrant_vm/${vmId}/logs`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setLogs(logsResponse.data.logs || []);
      
      // Fetch resource usage
      const resourceResponse = await axios.get(`${process.env.REACT_APP_API_URL}/api/vagrant_vm/${vmId}/resources`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setResourceUsage(resourceResponse.data);
      
      setError(null);
    } catch (err) {
      console.error('Error fetching VM details:', err);
      setError('Failed to fetch VM details. Please try again later.');
    }
  };

  const handleRefresh = () => {
    if (selectedVM) {
      fetchVMDetails(selectedVM.id);
    } else {
      fetchVMs();
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running':
        return 'green';
      case 'stopped':
        return 'red';
      case 'starting':
      case 'stopping':
      case 'provisioning':
        return 'blue';
      case 'error':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running':
        return <CheckCircleOutlined />;
      case 'stopped':
        return <StopOutlined />;
      case 'starting':
      case 'stopping':
      case 'provisioning':
        return <SyncOutlined spin />;
      case 'error':
        return <CloseCircleOutlined />;
      default:
        return <InfoCircleOutlined />;
    }
  };

  const getInjectionStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'green';
      case 'failed':
        return 'red';
      case 'pending':
      case 'in_progress':
        return 'blue';
      default:
        return 'default';
    }
  };

  const getInjectionStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return <CheckCircleOutlined />;
      case 'failed':
        return <CloseCircleOutlined />;
      case 'pending':
        return <ClockCircleOutlined />;
      case 'in_progress':
        return <SyncOutlined spin />;
      default:
        return <InfoCircleOutlined />;
    }
  };

  const getTranslatedStatus = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running':
        return t('vm_status.status.running');
      case 'stopped':
        return t('vm_status.status.stopped');
      case 'starting':
        return t('vm_status.status.starting');
      case 'stopping':
        return t('vm_status.status.stopping');
      case 'provisioning':
        return t('vm_status.status.provisioning');
      case 'error':
        return t('vm_status.status.error');
      default:
        return status.toUpperCase();
    }
  };

  const getTranslatedInjectionStatus = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return t('vm_status.injection.completed');
      case 'failed':
        return t('vm_status.injection.failed');
      case 'pending':
        return t('vm_status.injection.pending');
      case 'in_progress':
        return t('vm_status.injection.in_progress');
      default:
        return status.replace('_', ' ').toUpperCase();
    }
  };

  const renderOverviewTab = () => {
    if (!selectedVM) return <Alert message={t('vm_status.select_vm')} type="info" />;
    
    return (
      <div className="vm-status-overview">
        <Card className="vm-status-card">
          <Descriptions title={t('vm_status.vm_info')} bordered>
            <Descriptions.Item label="Name">{selectedVM.name}</Descriptions.Item>
            <Descriptions.Item label="Status">
              <Tag icon={getStatusIcon(selectedVM.status)} color={getStatusColor(selectedVM.status)}>
                {getTranslatedStatus(selectedVM.status)}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Template">{selectedVM.template}</Descriptions.Item>
            <Descriptions.Item label="IP Address">{selectedVM.ip_address || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="SSH Port">{selectedVM.ssh_port || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Created On">{new Date(selectedVM.created_on).toLocaleString()}</Descriptions.Item>
            <Descriptions.Item label="Last Action">{selectedVM.last_action || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Last Action Time">
              {selectedVM.last_action_time ? new Date(selectedVM.last_action_time).toLocaleString() : 'N/A'}
            </Descriptions.Item>
            <Descriptions.Item label={t('vm_status.description')} span={3}>
              {selectedVM.description || t('vm_status.no_description')}
            </Descriptions.Item>
            {selectedVM.error_message && (
              <Descriptions.Item label="Error Message" span={3}>
                <Alert message={selectedVM.error_message} type="error" />
              </Descriptions.Item>
            )}
          </Descriptions>
        </Card>
        
        <Divider />
        
        <Card className="vm-status-card">
          <Title level={4}>{t('vm_status.resource_usage')}</Title>
          {resourceUsage ? (
            <div className="resource-usage-container">
              <div className="resource-usage-item">
                <Statistic 
                  title={t('vm_status.cpu_usage')} 
                  value={resourceUsage.cpu_percent} 
                  suffix="%" 
                  precision={1} 
                />
                <Progress 
                  percent={resourceUsage.cpu_percent} 
                  status={resourceUsage.cpu_percent > 90 ? "exception" : "normal"} 
                />
              </div>
              
              <div className="resource-usage-item">
                <Statistic 
                  title={t('vm_status.memory_usage')} 
                  value={(resourceUsage.memory_used_mb / resourceUsage.memory_total_mb) * 100} 
                  suffix="%" 
                  precision={1} 
                />
                <Progress 
                  percent={(resourceUsage.memory_used_mb / resourceUsage.memory_total_mb) * 100} 
                  status={(resourceUsage.memory_used_mb / resourceUsage.memory_total_mb) > 0.9 ? "exception" : "normal"} 
                />
                <Text type="secondary">
                  {resourceUsage.memory_used_mb.toFixed(1)} MB / {resourceUsage.memory_total_mb.toFixed(1)} MB
                </Text>
              </div>
              
              <div className="resource-usage-item">
                <Statistic 
                  title={t('vm_status.disk_usage')} 
                  value={(resourceUsage.disk_used_gb / resourceUsage.disk_total_gb) * 100} 
                  suffix="%" 
                  precision={1} 
                />
                <Progress 
                  percent={(resourceUsage.disk_used_gb / resourceUsage.disk_total_gb) * 100} 
                  status={(resourceUsage.disk_used_gb / resourceUsage.disk_total_gb) > 0.9 ? "exception" : "normal"} 
                />
                <Text type="secondary">
                  {resourceUsage.disk_used_gb.toFixed(1)} GB / {resourceUsage.disk_total_gb.toFixed(1)} GB
                </Text>
              </div>
            </div>
          ) : (
            <Alert message={t('vm_status.no_resource_data')} type="info" />
          )}
        </Card>
      </div>
    );
  };

  const renderInjectionsTab = () => {
    if (!selectedVM) return <Alert message={t('vm_status.select_vm')} type="info" />;
    
    const columns = [
      {
        title: 'File',
        dataIndex: 'file_info',
        key: 'file',
        render: (fileInfo) => fileInfo ? (
          <span>
            <FileOutlined style={{ marginRight: 8 }} />
            {fileInfo.filename}
          </span>
        ) : 'N/A',
      },
      {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        render: (status) => (
          <Tag icon={getInjectionStatusIcon(status)} color={getInjectionStatusColor(status)}>
            {getTranslatedInjectionStatus(status)}
          </Tag>
        ),
      },
      {
        title: 'Created',
        dataIndex: 'created_on',
        key: 'created_on',
        render: (date) => new Date(date).toLocaleString(),
      },
      {
        title: 'Completed',
        dataIndex: 'completed_on',
        key: 'completed_on',
        render: (date) => date ? new Date(date).toLocaleString() : '-',
      },
    ];
    
    return (
      <div className="vm-status-injections">
        <Title level={4}>{t('vm_status.file_injections')}</Title>
        {injections.length > 0 ? (
          <Table 
            dataSource={injections} 
            columns={columns} 
            rowKey="id" 
            expandable={{
              expandedRowRender: (record) => (
                <div>
                  <p><strong>{t('vm_status.description')}:</strong> {record.description || t('vm_status.no_description')}</p>
                  {record.additional_command && (
                    <p><strong>{t('vm_status.additional_command')}:</strong> <code>{record.additional_command}</code></p>
                  )}
                  {record.error_message && (
                    <Alert message={record.error_message} type="error" />
                  )}
                </div>
              ),
            }}
          />
        ) : (
          <Alert message={t('vm_status.no_injections')} type="info" />
        )}
      </div>
    );
  };

  const renderLogsTab = () => {
    if (!selectedVM) return <Alert message={t('vm_status.select_vm')} type="info" />;
    
    return (
      <div className="vm-status-logs">
        <Title level={4}>{t('vm_status.vm_logs')}</Title>
        {logs.length > 0 ? (
          <Timeline mode="left">
            {logs.map((log, index) => (
              <Timeline.Item 
                key={index}
                color={log.level === 'ERROR' ? 'red' : log.level === 'WARNING' ? 'orange' : 'blue'}
                label={new Date(log.timestamp).toLocaleString()}
              >
                <div className={`log-entry log-${log.level.toLowerCase()}`}>
                  <Badge status={log.level === 'ERROR' ? 'error' : log.level === 'WARNING' ? 'warning' : 'processing'} />
                  <Text code>{log.message}</Text>
                </div>
              </Timeline.Item>
            ))}
          </Timeline>
        ) : (
          <Alert message={t('vm_status.no_logs')} type="info" />
        )}
      </div>
    );
  };

  if (loading && !selectedVM) {
    return (
      <div className="vm-status-container">
        <div className="vm-status-loading">
          <Spin size="large" />
          <p>{t('vm_status.loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="vm-status-container">
      <div className="vm-status-header">
        <Title level={2}>
          <DesktopOutlined /> {t('vm_status.title')}
        </Title>
        <Button 
          type="primary" 
          icon={<ReloadOutlined />} 
          onClick={handleRefresh}
        >
          {t('vm_status.refresh')}
        </Button>
      </div>
      
      {error && <Alert message={error} type="error" style={{ marginBottom: 16 }} />}
      
      <div className="vm-status-content">
        <div className="vm-status-sidebar">
          <Card title={t('vm_status.vm_list')} className="vm-list-card">
            {vms.length > 0 ? (
              <div className="vm-list">
                {vms.map(vm => (
                  <div 
                    key={vm.id} 
                    className={`vm-list-item ${selectedVM?.id === vm.id ? 'selected' : ''}`}
                    onClick={() => setSelectedVM(vm)}
                  >
                    <Badge 
                      status={
                        vm.status.toLowerCase() === 'running' ? 'success' : 
                        vm.status.toLowerCase() === 'stopped' ? 'error' : 
                        'processing'
                      } 
                    />
                    <span className="vm-list-item-name">{vm.name}</span>
                  </div>
                ))}
              </div>
            ) : (
              <Alert message={t('vm_status.no_vms')} type="info" />
            )}
          </Card>
        </div>
        
        <div className="vm-status-details">
          {selectedVM ? (
            <Card 
              title={
                <div className="vm-status-title">
                  <DesktopOutlined /> {selectedVM.name}
                  <Tag 
                    icon={getStatusIcon(selectedVM.status)} 
                    color={getStatusColor(selectedVM.status)}
                    style={{ marginLeft: 8 }}
                  >
                    {getTranslatedStatus(selectedVM.status)}
                  </Tag>
                </div>
              }
              className="vm-details-card"
            >
              <Tabs 
                activeKey={activeTab} 
                onChange={setActiveTab}
                type="card"
              >
                <TabPane 
                  tab={<span><InfoCircleOutlined />{t('vm_status.overview')}</span>} 
                  key="overview"
                >
                  {renderOverviewTab()}
                </TabPane>
                <TabPane 
                  tab={<span><CodeOutlined />{t('vm_status.injections')}</span>} 
                  key="injections"
                >
                  {renderInjectionsTab()}
                </TabPane>
                <TabPane 
                  tab={<span><EyeOutlined />{t('vm_status.logs')}</span>} 
                  key="logs"
                >
                  {renderLogsTab()}
                </TabPane>
              </Tabs>
            </Card>
          ) : (
            <Alert 
              message={t('vm_status.select_vm')} 
              type="info" 
              showIcon 
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default VmStatusPage; 