.vm-status-container {
  padding: 24px;
  width: 100%;
}

.vm-status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.vm-status-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.vm-status-content {
  display: flex;
  gap: 24px;
}

.vm-status-sidebar {
  width: 250px;
  flex-shrink: 0;
}

.vm-status-details {
  flex-grow: 1;
}

.vm-list-card {
  height: 100%;
}

.vm-list {
  max-height: 500px;
  overflow-y: auto;
}

.vm-list-item {
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.3s;
}

.vm-list-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.vm-list-item.selected {
  background-color: rgba(24, 144, 255, 0.1);
}

.vm-list-item-name {
  margin-left: 8px;
  font-weight: 500;
}

.vm-details-card {
  width: 100%;
}

.vm-status-title {
  display: flex;
  align-items: center;
}

.vm-status-overview,
.vm-status-injections,
.vm-status-logs {
  margin-top: 16px;
}

.vm-status-card {
  margin-bottom: 16px;
}

.resource-usage-container {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.resource-usage-item {
  flex: 1;
  min-width: 200px;
}

.log-entry {
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
}

.log-error {
  background-color: rgba(255, 77, 79, 0.1);
}

.log-warning {
  background-color: rgba(250, 173, 20, 0.1);
}

.log-info {
  background-color: rgba(24, 144, 255, 0.1);
}

/* Responsive styles */
@media (max-width: 768px) {
  .vm-status-content {
    flex-direction: column;
  }
  
  .vm-status-sidebar {
    width: 100%;
    margin-bottom: 16px;
  }
  
  .resource-usage-item {
    min-width: 100%;
  }
} 