"""
TurdParty Main Application Entry Point

This file serves as the main entry point for the TurdParty API application.
It imports and exposes the FastAPI application from the appropriate module.
"""

import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Try to import from services/api first (preferred), then fall back to api/v1
try:
    # Import from the services/api structure (new structure)
    from services.api.src.main import app
    print("✅ Using services/api/src/main.py application")
except ImportError:
    try:
        # Fall back to api/v1 structure (legacy structure)
        from api.v1.application import get_application
        app = get_application()
        print("✅ Using api/v1/application.py application")
    except ImportError as e:
        print(f"❌ Failed to import application: {e}")
        raise

if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("PORT", "8000"))
    host = os.getenv("HOST", "0.0.0.0")
    
    print(f"🚀 Starting TurdParty API on {host}:{port}")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )
