# 💩🎉TurdParty🎉💩 10Baht Overlays

This directory contains our customizations to the 10Baht Packer templates without modifying the upstream submodule.

## 📁 Structure

```
overlays/10b/
├── windows10/
│   ├── packer-turdparty.json        # Our customized Packer configuration
│   ├── files/
│   │   ├── fibratus-turdparty.yml   # Fibratus monitoring configuration
│   │   └── configure-fibratus.ps1   # Fibratus setup script
│   ├── scripts/                     # Custom provisioning scripts (future)
│   └── turdparty-vagrantfile.template  # Custom Vagrant template
└── README.md                        # This file
```

## 🎯 Purpose

**Problem**: We need to customize the 10Baht Packer templates for TurdParty integration, but we don't want to modify the upstream submodule.

**Solution**: Overlay system that applies our customizations during the build process while keeping the submodule clean.

## 🔧 How It Works

1. **Clean Submodule**: `vendor/10b/packer-templates/` remains unmodified
2. **Our Customizations**: Stored in `overlays/10b/`
3. **Build Process**: `scripts/apply-overlays.sh` merges them during build
4. **Temporary Directory**: Build happens in `.overlay-temp/` with merged files

## 📋 Key Customizations

### **Packer Configuration (`packer-turdparty.json`)**
- **SHA256 checksums** instead of MD5
- **Optimized QEMU settings** (4GB RAM, 4 CPUs, q35 machine)
- **KVM acceleration** enabled
- **TurdParty-specific VM name** (`win-10-turdparty`)
- **Custom output path** (`win10-turdparty/windows10-turdparty.box`)

### **Fibratus Integration**
- **fibratus-turdparty.yml**: Complete monitoring configuration
- **configure-fibratus.ps1**: Automated setup and service management
- **ELK integration**: Real-time event streaming to Elasticsearch

### **Vagrant Template**
- **turdparty-vagrantfile.template**: Custom VM provisioning
- **Environment variables**: VM ID, workflow ID, analysis UUID injection
- **gRPC communication**: Port 40000 integration
- **Automatic Fibratus startup**: Monitoring begins on VM boot

## 🚀 Usage

The overlay system is automatically used by our build script:

```bash
# This automatically applies overlays and builds with our customizations
./scripts/build-windows-vm.sh isos/Win10_22H2_English_x64v1.iso
```

## 🔄 Updating

### **To Update Upstream Templates:**
```bash
cd vendor/10b/packer-templates
git pull origin main
```

### **To Modify Our Customizations:**
1. Edit files in `overlays/10b/windows10/`
2. Test with `./scripts/apply-overlays.sh`
3. Build with `./scripts/build-windows-vm.sh`

## ✅ Benefits

- ✅ **Clean submodule**: No upstream pollution
- ✅ **Version controlled**: Our changes tracked in our repo
- ✅ **Maintainable**: Easy to update upstream without conflicts
- ✅ **Portable**: Self-contained customizations
- ✅ **Testable**: Can validate overlays independently

## 🎯 TurdParty Integration

Our overlays provide:
- **Complete malware analysis VM** with Fibratus monitoring
- **ELK integration** for real-time event streaming
- **gRPC communication** for workflow management
- **Automated provisioning** with TurdParty-specific tools
- **Performance optimization** for analysis workloads

The **💩🎉TurdParty🎉💩** overlay system keeps our customizations organized and maintainable! 🚀
