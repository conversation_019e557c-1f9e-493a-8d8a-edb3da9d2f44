# 💩🎉TurdParty🎉💩 Fibratus Configuration Script
# Configures Fibratus for malware analysis and ELK integration

Write-Host "🔧 Configuring Fibratus for TurdParty..." -ForegroundColor Green

# Set execution policy for scripts
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Force

# Create TurdParty directories
$turdpartyDirs = @(
    "C:\TurdParty",
    "C:\TurdParty\logs",
    "C:\TurdParty\config",
    "C:\TurdParty\samples",
    "C:\TurdParty\results"
)

foreach ($dir in $turdpartyDirs) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Force -Path $dir
        Write-Host "✅ Created directory: $dir" -ForegroundColor Green
    }
}

# Copy Fibratus configuration
$fibratusConfigSource = "C:\temp\fibratus-turdparty.yml"
$fibratusConfigDest = "C:\Program Files\Fibratus\fibratus.yml"

if (Test-Path $fibratusConfigSource) {
    Copy-Item $fibratusConfigSource $fibratusConfigDest -Force
    Write-Host "✅ Fibratus configuration installed" -ForegroundColor Green
} else {
    Write-Host "⚠️ Fibratus config not found at $fibratusConfigSource" -ForegroundColor Yellow
}

# Create Fibratus service configuration
$serviceConfig = @"
# TurdParty Fibratus Service Configuration
[Unit]
Description=Fibratus kernel event stream processor for TurdParty
After=network.target

[Service]
Type=simple
ExecStart="C:\Program Files\Fibratus\fibratus.exe" run --config "C:\Program Files\Fibratus\fibratus.yml"
Restart=always
RestartSec=5
User=SYSTEM

[Install]
WantedBy=multi-user.target
"@

# Save service configuration
$serviceConfig | Out-File -FilePath "C:\TurdParty\config\fibratus-service.conf" -Encoding UTF8

# Create Fibratus startup script
$startupScript = @"
# TurdParty Fibratus Startup Script
param(
    [string]`$VmId = `$env:TURDPARTY_VM_ID,
    [string]`$WorkflowId = `$env:TURDPARTY_WORKFLOW_ID,
    [string]`$AnalysisUuid = `$env:TURDPARTY_ANALYSIS_UUID
)

Write-Host "🚀 Starting Fibratus for TurdParty analysis..." -ForegroundColor Green
Write-Host "VM ID: `$VmId" -ForegroundColor Cyan
Write-Host "Workflow ID: `$WorkflowId" -ForegroundColor Cyan
Write-Host "Analysis UUID: `$AnalysisUuid" -ForegroundColor Cyan

# Set environment variables for Fibratus
[Environment]::SetEnvironmentVariable("TURDPARTY_VM_ID", `$VmId, "Process")
[Environment]::SetEnvironmentVariable("TURDPARTY_WORKFLOW_ID", `$WorkflowId, "Process")
[Environment]::SetEnvironmentVariable("TURDPARTY_ANALYSIS_UUID", `$AnalysisUuid, "Process")

# Start Fibratus
try {
    `$fibratusPath = "C:\Program Files\Fibratus\fibratus.exe"
    `$configPath = "C:\Program Files\Fibratus\fibratus.yml"
    
    if (Test-Path `$fibratusPath) {
        Write-Host "🔍 Starting Fibratus monitoring..." -ForegroundColor Green
        Start-Process -FilePath `$fibratusPath -ArgumentList "run", "--config", `$configPath -NoNewWindow -PassThru
        Write-Host "✅ Fibratus started successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Fibratus not found at `$fibratusPath" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Failed to start Fibratus: `$_" -ForegroundColor Red
    exit 1
}

# Create status file
`$status = @{
    "fibratus_started" = `$true
    "vm_id" = `$VmId
    "workflow_id" = `$WorkflowId
    "analysis_uuid" = `$AnalysisUuid
    "started_at" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "config_file" = `$configPath
    "log_directory" = "C:\TurdParty\logs"
}

`$status | ConvertTo-Json | Out-File -FilePath "C:\TurdParty\fibratus-status.json"

Write-Host "🎯 Fibratus monitoring active for TurdParty analysis" -ForegroundColor Green
"@

# Save startup script
$startupScript | Out-File -FilePath "C:\TurdParty\start-fibratus.ps1" -Encoding UTF8

# Create Fibratus stop script
$stopScript = @"
# TurdParty Fibratus Stop Script

Write-Host "🛑 Stopping Fibratus monitoring..." -ForegroundColor Yellow

try {
    # Stop Fibratus processes
    Get-Process -Name "fibratus" -ErrorAction SilentlyContinue | Stop-Process -Force
    Write-Host "✅ Fibratus processes stopped" -ForegroundColor Green
    
    # Update status file
    `$status = @{
        "fibratus_started" = `$false
        "stopped_at" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        "reason" = "manual_stop"
    }
    
    `$status | ConvertTo-Json | Out-File -FilePath "C:\TurdParty\fibratus-status.json"
    
} catch {
    Write-Host "⚠️ Error stopping Fibratus: `$_" -ForegroundColor Yellow
}

Write-Host "🏁 Fibratus monitoring stopped" -ForegroundColor Green
"@

# Save stop script
$stopScript | Out-File -FilePath "C:\TurdParty\stop-fibratus.ps1" -Encoding UTF8

# Create health check script
$healthScript = @"
# TurdParty Fibratus Health Check Script

function Test-FibratusHealth {
    `$health = @{
        "fibratus_running" = `$false
        "config_valid" = `$false
        "log_directory_exists" = `$false
        "elasticsearch_reachable" = `$false
        "last_check" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
    
    # Check if Fibratus is running
    `$fibratusProcess = Get-Process -Name "fibratus" -ErrorAction SilentlyContinue
    if (`$fibratusProcess) {
        `$health.fibratus_running = `$true
        Write-Host "✅ Fibratus is running (PID: `$(`$fibratusProcess.Id))" -ForegroundColor Green
    } else {
        Write-Host "❌ Fibratus is not running" -ForegroundColor Red
    }
    
    # Check configuration file
    `$configPath = "C:\Program Files\Fibratus\fibratus.yml"
    if (Test-Path `$configPath) {
        `$health.config_valid = `$true
        Write-Host "✅ Configuration file exists" -ForegroundColor Green
    } else {
        Write-Host "❌ Configuration file missing" -ForegroundColor Red
    }
    
    # Check log directory
    if (Test-Path "C:\TurdParty\logs") {
        `$health.log_directory_exists = `$true
        Write-Host "✅ Log directory exists" -ForegroundColor Green
    } else {
        Write-Host "❌ Log directory missing" -ForegroundColor Red
    }
    
    # Test Elasticsearch connectivity (basic check)
    try {
        `$response = Invoke-WebRequest -Uri "http://elasticsearch.turdparty.localhost:9200" -TimeoutSec 5 -ErrorAction Stop
        if (`$response.StatusCode -eq 200) {
            `$health.elasticsearch_reachable = `$true
            Write-Host "✅ Elasticsearch is reachable" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Elasticsearch not reachable: `$_" -ForegroundColor Red
    }
    
    # Save health status
    `$health | ConvertTo-Json | Out-File -FilePath "C:\TurdParty\fibratus-health.json"
    
    return `$health
}

# Run health check
`$healthResult = Test-FibratusHealth

# Return appropriate exit code
if (`$healthResult.fibratus_running -and `$healthResult.config_valid) {
    Write-Host "🎯 Fibratus health check: HEALTHY" -ForegroundColor Green
    exit 0
} else {
    Write-Host "⚠️ Fibratus health check: UNHEALTHY" -ForegroundColor Yellow
    exit 1
}
"@

# Save health check script
$healthScript | Out-File -FilePath "C:\TurdParty\check-fibratus-health.ps1" -Encoding UTF8

# Set up Windows service (if possible)
Write-Host "🔧 Configuring Fibratus as Windows service..." -ForegroundColor Green

# Create service wrapper script
$serviceWrapper = @"
# Fibratus Service Wrapper for TurdParty
param([string]`$Action = "start")

switch (`$Action.ToLower()) {
    "start" {
        Write-Host "Starting TurdParty Fibratus service..." -ForegroundColor Green
        & "C:\TurdParty\start-fibratus.ps1"
    }
    "stop" {
        Write-Host "Stopping TurdParty Fibratus service..." -ForegroundColor Yellow
        & "C:\TurdParty\stop-fibratus.ps1"
    }
    "status" {
        & "C:\TurdParty\check-fibratus-health.ps1"
    }
    default {
        Write-Host "Usage: fibratus-service.ps1 [start|stop|status]" -ForegroundColor Yellow
    }
}
"@

# Save service wrapper
$serviceWrapper | Out-File -FilePath "C:\TurdParty\fibratus-service.ps1" -Encoding UTF8

# Create scheduled task for auto-start
Write-Host "📅 Creating scheduled task for Fibratus auto-start..." -ForegroundColor Green

try {
    $action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\TurdParty\start-fibratus.ps1"
    $trigger = New-ScheduledTaskTrigger -AtStartup
    $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
    $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
    
    Register-ScheduledTask -TaskName "TurdParty-Fibratus" -Action $action -Trigger $trigger -Settings $settings -Principal $principal -Force
    Write-Host "✅ Scheduled task created successfully" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Failed to create scheduled task: $_" -ForegroundColor Yellow
}

# Final status
Write-Host "🎉 Fibratus configuration completed!" -ForegroundColor Green
Write-Host "📁 Configuration files:" -ForegroundColor Cyan
Write-Host "   - C:\Program Files\Fibratus\fibratus.yml" -ForegroundColor White
Write-Host "   - C:\TurdParty\start-fibratus.ps1" -ForegroundColor White
Write-Host "   - C:\TurdParty\stop-fibratus.ps1" -ForegroundColor White
Write-Host "   - C:\TurdParty\check-fibratus-health.ps1" -ForegroundColor White
Write-Host "   - C:\TurdParty\fibratus-service.ps1" -ForegroundColor White

Write-Host "🚀 Fibratus is ready for TurdParty malware analysis!" -ForegroundColor Green
