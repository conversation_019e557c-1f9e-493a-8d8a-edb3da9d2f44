# 💩🎉TurdParty🎉💩 Fibratus Configuration
# Optimized for malware analysis and ELK integration

# Kernel event stream configuration
kstream:
  # Enable all relevant event types for malware analysis
  enable-thread: true
  enable-image: true
  enable-file: true
  enable-registry: true
  enable-network: true
  enable-process: true
  enable-handle: true
  enable-mem: true
  enable-va: true
  enable-dns: true
  
  # Buffer configuration for high-volume analysis
  buffer-size: 1024
  flush-interval: 1s
  
  # Process filtering - monitor all processes
  exclude-procs: []
  include-procs: []

# Output configuration for TurdParty ELK integration
output:
  # Primary output to Elasticsearch
  elasticsearch:
    enabled: true
    servers: ["http://elasticsearch.turdparty.localhost:9200"]
    index: "turdparty-fibratus-%{+yyyy.MM.dd}"
    template: "turdparty-fibratus"
    
    # ECS format for standardized logging
    format: "ecs"
    
    # Bulk settings for performance
    bulk:
      max-size: 1000
      flush-interval: 5s
      timeout: 30s
    
    # Authentication (if needed)
    # username: "fibratus"
    # password: "password"
    
    # TLS configuration (if needed)
    # tls:
    #   enabled: true
    #   verify: false

  # Secondary output to local file for backup
  file:
    enabled: true
    path: "C:\\TurdParty\\logs\\fibratus-%{+yyyy-MM-dd}.json"
    format: "json"
    rotate:
      max-size: 100MB
      max-files: 10

  # Console output for debugging (disabled in production)
  console:
    enabled: false
    format: "pretty"

# Filters for malware analysis focus
filters:
  # File system events
  - name: "file-operations"
    condition: >
      kevt.name in ('CreateFile', 'WriteFile', 'DeleteFile', 'SetFileInformation') and
      (ps.name != 'System' and ps.name != 'Registry')
    
  # Registry modifications
  - name: "registry-changes"
    condition: >
      kevt.name in ('RegCreateKey', 'RegSetValue', 'RegDeleteKey', 'RegDeleteValue') and
      (registry.key.name contains 'Run' or 
       registry.key.name contains 'CurrentVersion' or
       registry.key.name contains 'Services' or
       registry.key.name contains 'Policies')
  
  # Network connections
  - name: "network-activity"
    condition: >
      kevt.name in ('Connect', 'Accept', 'Send', 'Recv') and
      net.dip != '127.0.0.1' and net.dip != '::1'
  
  # Process creation and termination
  - name: "process-lifecycle"
    condition: >
      kevt.name in ('CreateProcess', 'TerminateProcess') and
      ps.name != 'System'
  
  # Memory operations (potential code injection)
  - name: "memory-operations"
    condition: >
      kevt.name in ('VirtualAlloc', 'VirtualProtect', 'MapViewOfFile') and
      ps.name != 'System'

# Enrichment configuration
enrichers:
  # Process enrichment
  ps:
    enabled: true
    # Include process tree information
    include-ancestors: true
    include-children: true
    
  # File enrichment
  file:
    enabled: true
    # Include file metadata
    include-metadata: true
    include-version-info: true
    
  # Network enrichment  
  net:
    enabled: true
    # Include DNS resolution
    include-dns: true
    # Include GeoIP information
    include-geoip: false  # Disabled for performance

# TurdParty specific configuration
turdparty:
  # VM identification
  vm_id: "${TURDPARTY_VM_ID}"
  workflow_id: "${TURDPARTY_WORKFLOW_ID}"
  analysis_uuid: "${TURDPARTY_ANALYSIS_UUID}"
  
  # Analysis timeframe
  analysis_duration: 1800  # 30 minutes
  
  # Custom fields to add to all events
  custom_fields:
    turdparty.vm: true
    turdparty.analysis_type: "malware"
    turdparty.monitoring_agent: "fibratus"
    turdparty.version: "2.2.1"

# Performance tuning
performance:
  # CPU usage limits
  max-cpu-percent: 25
  
  # Memory limits
  max-memory-mb: 512
  
  # Event rate limiting
  max-events-per-second: 10000
  
  # Sampling for high-volume events
  sampling:
    enabled: true
    rate: 0.1  # Sample 10% of high-volume events

# Logging configuration
logging:
  level: "info"
  file: "C:\\TurdParty\\logs\\fibratus.log"
  max-size: 50MB
  max-files: 5

# Health monitoring
health:
  enabled: true
  port: 8080
  endpoint: "/health"
  
# Metrics for monitoring
metrics:
  enabled: true
  port: 8081
  endpoint: "/metrics"
  format: "prometheus"

# Security settings
security:
  # Run with minimal privileges
  drop-privileges: true
  
  # Secure communication
  tls:
    enabled: false  # Disabled for internal network
    
# Alerting configuration
alerts:
  # Critical events that should trigger immediate alerts
  rules:
    - name: "suspicious-process-creation"
      condition: >
        kevt.name = 'CreateProcess' and
        (ps.exe contains 'powershell' or ps.exe contains 'cmd' or ps.exe contains 'wscript')
      severity: "high"
      
    - name: "registry-persistence"
      condition: >
        kevt.name = 'RegSetValue' and
        registry.key.name contains 'Run'
      severity: "medium"
      
    - name: "network-beacon"
      condition: >
        kevt.name = 'Connect' and
        net.dport in (80, 443, 8080, 8443)
      severity: "low"

# Integration with TurdParty gRPC
grpc:
  enabled: true
  server: "localhost:40000"
  
  # Event streaming configuration
  stream:
    enabled: true
    buffer-size: 1000
    flush-interval: 2s
