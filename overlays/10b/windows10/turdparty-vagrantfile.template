# -*- mode: ruby -*-
# vi: set ft=ruby :
#
# 💩🎉TurdParty🎉💩 Windows VM Template
# Integrates with TurdParty gRPC infrastructure on port 40000
#

Vagrant.require_version ">= 2.0.0"

Vagrant.configure("2") do |config|
  # Use the 10Baht built Windows box
  config.vm.box = "10Baht/windows10-turdparty"
  config.vm.box_check_update = false
  
  # Windows-specific configuration
  config.vm.communicator = "winrm"
  config.vm.guest = :windows
  
  # WinRM configuration for TurdParty communication
  config.winrm.username = "vagrant"
  config.winrm.password = "vagrant"
  config.winrm.transport = :plaintext
  config.winrm.basic_auth_only = true
  config.winrm.timeout = 300
  config.winrm.retry_limit = 20
  config.winrm.retry_delay = 10
  
  # Network configuration for TurdParty integration
  # RDP access for debugging
  config.vm.network :forwarded_port, guest: 3389, host: 3389, id: "rdp", auto_correct: true
  
  # WinRM for Vagrant communication
  config.vm.network :forwarded_port, guest: 5985, host: 5985, id: "winrm", auto_correct: true
  config.vm.network :forwarded_port, guest: 5986, host: 5986, id: "winrm-ssl", auto_correct: true
  
  # TurdParty gRPC communication port
  config.vm.network :forwarded_port, guest: 40000, host: 40000, id: "turdparty-grpc", auto_correct: true
  
  # Additional ports for malware analysis
  config.vm.network :forwarded_port, guest: 80, host: 8080, id: "http", auto_correct: true
  config.vm.network :forwarded_port, guest: 443, host: 8443, id: "https", auto_correct: true
  config.vm.network :forwarded_port, guest: 53, host: 5353, id: "dns", auto_correct: true
  
  # Private network for isolated analysis
  config.vm.network "private_network", type: "dhcp"
  
  # Shared folders for file injection and extraction
  config.vm.synced_folder ".", "/vagrant", disabled: false,
    SharedFoldersEnableSymlinksCreate: false,
    mount_options: ["dmode=777", "fmode=666"]
  
  # TurdParty shared directory for malware samples
  config.vm.synced_folder "./turdparty-shared", "/TurdParty", create: true,
    SharedFoldersEnableSymlinksCreate: false,
    mount_options: ["dmode=777", "fmode=666"]
  
  # Provider-specific configurations
  
  # VirtualBox provider (primary)
  config.vm.provider "virtualbox" do |vb|
    vb.name = "TurdParty-Windows-Analysis"
    vb.memory = 4096
    vb.cpus = 2
    vb.gui = false  # Headless by default
    
    # Performance optimizations
    vb.customize ["modifyvm", :id, "--vram", "128"]
    vb.customize ["modifyvm", :id, "--accelerate3d", "off"]
    vb.customize ["modifyvm", :id, "--clipboard", "disabled"]
    vb.customize ["modifyvm", :id, "--draganddrop", "disabled"]
    
    # Security settings for malware analysis
    vb.customize ["modifyvm", :id, "--audio", "none"]
    vb.customize ["modifyvm", :id, "--usb", "off"]
    vb.customize ["modifyvm", :id, "--usbehci", "off"]
    vb.customize ["modifyvm", :id, "--usbxhci", "off"]
    
    # Network isolation
    vb.customize ["modifyvm", :id, "--natdnshostresolver1", "on"]
    vb.customize ["modifyvm", :id, "--natdnsproxy1", "on"]
    
    # Suppress GUI messages
    vb.customize ["setextradata", "global", "GUI/SuppressMessages", "all"]
  end
  
  # libvirt provider (alternative for Linux hosts)
  config.vm.provider "libvirt" do |libvirt|
    libvirt.memory = 4096
    libvirt.cpus = 2
    libvirt.driver = "kvm"
    libvirt.nested = true
    libvirt.cpu_mode = "host-passthrough"
    
    # Graphics and input
    libvirt.graphics_type = "vnc"
    libvirt.graphics_ip = "0.0.0.0"
    libvirt.graphics_port = 5900
    libvirt.input :type => "tablet", :bus => "usb"
    
    # Storage
    libvirt.disk_bus = "virtio"
    libvirt.nic_model_type = "virtio"
    
    # Guest agent for better integration
    libvirt.channel :type => 'unix', :target_name => 'org.qemu.guest_agent.0', :target_type => 'virtio'
  end
  
  # VMware provider (if available)
  config.vm.provider "vmware_desktop" do |vmware|
    vmware.vmx["memsize"] = "4096"
    vmware.vmx["numvcpus"] = "2"
    vmware.vmx["ethernet0.virtualDev"] = "vmxnet3"
    vmware.vmx["scsi0.virtualDev"] = "lsisas1068"
    vmware.vmx["displayName"] = "TurdParty Windows Analysis VM"
  end
  
  # Provisioning for TurdParty integration
  config.vm.provision "shell", privileged: true, inline: <<-SHELL
    # Create TurdParty directories
    if (!(Test-Path "C:\\TurdParty")) {
      New-Item -ItemType Directory -Force -Path "C:\\TurdParty"
      New-Item -ItemType Directory -Force -Path "C:\\TurdParty\\samples"
      New-Item -ItemType Directory -Force -Path "C:\\TurdParty\\logs"
      New-Item -ItemType Directory -Force -Path "C:\\TurdParty\\results"
    }
    
    # Set up TurdParty environment
    [Environment]::SetEnvironmentVariable("TURDPARTY_VM", "true", "Machine")
    [Environment]::SetEnvironmentVariable("TURDPARTY_ANALYSIS_MODE", "windows", "Machine")
    [Environment]::SetEnvironmentVariable("TURDPARTY_VM_ID", "#{vm_id}", "Machine")
    [Environment]::SetEnvironmentVariable("TURDPARTY_WORKFLOW_ID", "#{workflow_id}", "Machine")
    [Environment]::SetEnvironmentVariable("TURDPARTY_ANALYSIS_UUID", "#{analysis_uuid}", "Machine")
    
    # Create status file
    "TurdParty Windows VM ready for malware analysis" | Out-File -FilePath "C:\\TurdParty\\status.txt"
    
    # Log VM information
    $vmInfo = @{
      "hostname" = $env:COMPUTERNAME
      "os" = (Get-WmiObject -Class Win32_OperatingSystem).Caption
      "memory_gb" = [math]::Round((Get-WmiObject -Class Win32_ComputerSystem).TotalPhysicalMemory / 1GB, 2)
      "cpu_cores" = (Get-WmiObject -Class Win32_Processor).NumberOfCores
      "fibratus_installed" = Test-Path "C:\\Program Files\\Fibratus\\fibratus.exe"
      "turdparty_ready" = $true
      "timestamp" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
    
    $vmInfo | ConvertTo-Json | Out-File -FilePath "C:\\TurdParty\\vm-info.json"

    # Start Fibratus monitoring
    Write-Host "🔍 Starting Fibratus monitoring..." -ForegroundColor Green
    if (Test-Path "C:\\TurdParty\\start-fibratus.ps1") {
        try {
            & "C:\\TurdParty\\start-fibratus.ps1"
            Write-Host "✅ Fibratus monitoring started" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ Failed to start Fibratus: $_" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️ Fibratus startup script not found" -ForegroundColor Yellow
    }

    Write-Host "💩🎉TurdParty🎉💩 Windows VM provisioned successfully!"
  SHELL
  
  # Post-up message
  config.vm.post_up_message = <<-MSG
    💩🎉TurdParty🎉💩 Windows Analysis VM is ready!
    
    🔗 Access Information:
    - RDP: localhost:3389 (vagrant/vagrant)
    - WinRM: localhost:5985
    - TurdParty gRPC: localhost:40000
    
    📁 Shared Directories:
    - Host ./turdparty-shared ↔ VM C:\\TurdParty
    - Host . ↔ VM C:\\vagrant
    
    🛡️ Security Features:
    - Network isolation enabled
    - USB/Audio disabled
    - Fibratus monitoring pre-installed
    
    Ready for malware analysis! 🦠🔬
  MSG
end
