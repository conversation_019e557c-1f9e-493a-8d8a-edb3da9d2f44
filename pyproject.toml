[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "turdparty"
version = "0.1.0"
description = "TurdParty - File injection and VM processing system"
authors = [
    {name = "TurdParty Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "httpx>=0.25.0",
    "aiofiles>=23.2.1",
    "python-multipart>=0.0.6",
    "minio>=7.2.0",
    "elasticsearch>=8.11.0",
    "redis>=5.0.1",
    "python-logstash>=0.4.8",
    "celery>=5.3.4",
    "sqlalchemy>=2.0.23",
    "alembic>=1.13.0",
    "psycopg2-binary>=2.9.9",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-benchmark>=4.0.0",
    "pytest-xdist>=3.5.0",
    "pytest-mock>=3.12.0",
    "pytest-timeout>=2.2.0",
    "pytest-nunit>=1.0.0",
    "hypothesis>=6.92.0",
    "factory-boy>=3.3.0",
    "faker>=20.1.0",
    "locust>=2.17.0",
    "parameterized>=0.9.0",
    "ruff>=0.1.6",
    "mypy>=1.7.0",
    "types-redis>=4.6.0",
    "types-requests>=2.31.0",
    "types-psycopg2>=2.9.0",
    "pydoclint>=0.3.0",
    "bandit>=1.7.5",
    "safety>=2.3.0",
    "pre-commit>=3.6.0",
    "tox>=4.11.0",
    "APScheduler>=3.10.0",
]

[project.urls]
Homepage = "https://github.com/tenbahtsecurity/turdparty"
Repository = "https://github.com/tenbahtsecurity/turdparty.git"
Documentation = "https://github.com/tenbahtsecurity/turdparty/docs"
"Bug Tracker" = "https://github.com/tenbahtsecurity/turdparty/issues"

# Pytest Configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "-q",
    "--strict-markers",
    "--strict-config",
    # Coverage options (enabled)
    "--cov=services",
    "--cov-report=html:coverage-reports/html",
    "--cov-report=xml:coverage-reports/coverage.xml",
    "--cov-report=term-missing",
    "--cov-report=json:coverage-reports/coverage.json",
    "--cov-fail-under=80",
    "--junitxml=test-results.xml",
    "--benchmark-skip",  # Skip benchmarks by default
]
testpaths = ["tests"]
# asyncio_mode = "auto"  # Requires pytest-asyncio
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "performance: Performance tests",
    "security: Security tests",
    "slow: Slow running tests",
    "benchmark: Benchmark tests",
    "property: Property-based tests",
    "e2e: End-to-end tests",
    "asyncio: Async tests",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

# Coverage Configuration
[tool.coverage.run]
source = ["api", "services"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/virtualenv/*",
    "*/.tox/*",
    "*/migrations/*",
    "*/alembic/*",
    "setup.py",
    "conftest.py",
]
branch = true
parallel = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "def __str__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
skip_covered = false
precision = 2

[tool.coverage.html]
directory = "coverage-reports/html"

[tool.coverage.json]
output = "coverage-reports/coverage.json"

# Ruff Configuration
[tool.ruff]
target-version = "py311"
line-length = 88

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG", # flake8-unused-arguments
    "SIM", # flake8-simplify
    "TCH", # flake8-type-checking
    "PTH", # flake8-use-pathlib
    "ERA", # eradicate
    "PL",  # pylint
    "RUF", # ruff-specific rules
]
ignore = [
    "E501",   # line too long, handled by black
    "B008",   # do not perform function calls in argument defaults
    "C901",   # too complex
    "PLR0913", # too many arguments
    "PLR0915", # too many statements
]

[tool.ruff.lint.per-file-ignores]
"tests/**/*" = ["PLR2004", "S101", "ARG", "FBT"]
"__init__.py" = ["F401"]

[tool.ruff.lint.isort]
known-first-party = ["api", "services"]
force-sort-within-sections = true

[tool.ruff.lint.mccabe]
max-complexity = 10

# MyPy Configuration
[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_decorators = true
disallow_untyped_defs = true
ignore_missing_imports = true
no_implicit_optional = true
no_implicit_reexport = true
show_error_codes = true
strict_equality = true
warn_redundant_casts = true
warn_return_any = true
warn_unreachable = true
warn_unused_configs = true
warn_unused_ignores = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false

# Bandit Configuration
[tool.bandit]
exclude_dirs = ["tests", "migrations", "alembic"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection in tests

# Bandit test IDs:
# B101: assert_used
# B601: paramiko_calls
# B602: subprocess_popen_with_shell_equals_true
# B603: subprocess_without_shell_equals_true
# B604: any_other_function_with_shell_equals_true
# B605: start_process_with_a_shell
# B606: start_process_with_no_shell
# B607: start_process_with_partial_path

[tool.bandit.assert_used]
skips = ["*_test.py", "test_*.py"]

# pydoclint Configuration
[tool.pydoclint]
style = "google"
exclude = "tests/"
require-return-section-when-returning-nothing = false
arg-type-hints-in-docstring = false
check-return-sections = true
check-yield-sections = true
