# Playwright E2E Testing Dependencies for TurdParty

# Core testing framework
pytest>=8.0.0
pytest-asyncio>=0.23.0
pytest-html>=4.0.0
pytest-cov>=5.0.0
pytest-mock>=3.14.0
pytest-timeout>=2.3.0

# Playwright for E2E testing
playwright>=1.50.0

# FastAPI and dependencies for API testing
fastapi>=0.104.0
uvicorn>=0.24.0
httpx>=0.27.0
pydantic>=2.5.0

# Additional testing utilities
hypothesis>=6.100.0
factory-boy>=3.3.0
faker>=25.0.0

# HTTP client for API testing
requests>=2.31.0

# JSON and data handling
python-multipart>=0.0.6
aiofiles>=23.0.0

# Logging and monitoring
structlog>=23.0.0
python-logstash>=0.4.8

# Development utilities
python-dotenv>=1.0.0
blake3>=0.4.0

# Database and storage (for integration tests)
asyncpg>=0.29.0
redis>=5.0.0

# Mocking and test data
responses>=0.24.0
freezegun>=1.4.0

# Performance testing
pytest-benchmark>=4.0.0

# Parallel testing
pytest-xdist>=3.6.0
