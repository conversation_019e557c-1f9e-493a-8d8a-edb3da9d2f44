# Scripts Directory

This directory contains utility scripts for development, testing, and deployment of the TurdParty project.

## Planned Scripts

### Development Scripts
- `setup-dev.sh` - Set up development environment
- `start-services.sh` - Start all development services
- `stop-services.sh` - Stop all development services

### Testing Scripts
- `run-all-tests.sh` - Run complete test suite
- `run-unit-tests.sh` - Run unit tests only
- `run-integration-tests.sh` - Run integration tests only
- `run-e2e-tests.sh` - Run end-to-end tests only

### Deployment Scripts
- `deploy-staging.sh` - Deploy to staging environment
- `deploy-production.sh` - Deploy to production environment
- `backup-data.sh` - Backup application data

### Utility Scripts
- `clean-docker.sh` - Clean up Docker containers and images
- `reset-database.sh` - Reset development database
- `generate-docs.sh` - Generate API documentation

## Usage

All scripts should be executable and run from the project root:

```bash
# Make script executable
chmod +x scripts/script-name.sh

# Run script from project root
./scripts/script-name.sh
```

## Development Notes

- Scripts should be written in bash for compatibility
- Include proper error handling and logging
- Use the project's environment variables
- Follow the project's coding standards
- Include help text with `-h` or `--help` flags

## Current Status

This directory is currently empty but will be populated with utility scripts as the project develops.
