#!/bin/bash
# 💩🎉TurdParty🎉💩 Overlay Application Script
# Applies our customizations to the 10Baht Packer templates without modifying the submodule

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Print functions
print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
OVERLAYS_DIR="$PROJECT_ROOT/overlays/10b"
PACKER_DIR="$PROJECT_ROOT/vendor/10b/packer-templates/windows/windows10"
TEMP_DIR="$PROJECT_ROOT/.overlay-temp"

main() {
    local quiet_mode="${1:-false}"

    if [[ "$quiet_mode" != "true" ]]; then
        print_status "💩🎉TurdParty🎉💩 Overlay Application"
        print_status "===================================="
    fi
    
    # Check if overlay directory exists
    if [[ ! -d "$OVERLAYS_DIR" ]]; then
        print_error "Overlay directory not found: $OVERLAYS_DIR"
        exit 1
    fi
    
    # Check if packer directory exists
    if [[ ! -d "$PACKER_DIR" ]]; then
        print_error "Packer directory not found: $PACKER_DIR"
        print_error "Make sure the 10Baht submodule is initialized"
        exit 1
    fi
    
    if [[ "$quiet_mode" != "true" ]]; then
        print_status "Applying TurdParty overlays to 10Baht templates..."
    fi
    
    # Create temporary working directory
    rm -rf "$TEMP_DIR"
    mkdir -p "$TEMP_DIR/windows10"
    
    # Copy original packer template to temp
    if [[ "$quiet_mode" != "true" ]]; then
        print_status "Copying original 10Baht template..."
    fi
    cp -r "$PACKER_DIR"/* "$TEMP_DIR/windows10/"
    
    # Apply our overlays
    print_status "Applying TurdParty customizations..."
    
    # Copy our custom Packer configuration
    if [[ -f "$OVERLAYS_DIR/windows10/packer-turdparty.json" ]]; then
        cp "$OVERLAYS_DIR/windows10/packer-turdparty.json" "$TEMP_DIR/windows10/"
        print_success "Applied custom Packer configuration"
    fi
    
    # Copy our custom files
    if [[ -d "$OVERLAYS_DIR/windows10/files" ]]; then
        mkdir -p "$TEMP_DIR/windows10/files"
        cp -r "$OVERLAYS_DIR/windows10/files"/* "$TEMP_DIR/windows10/files/"
        print_success "Applied custom files"
    fi
    
    # Copy our custom scripts (if any exist)
    if [[ -d "$OVERLAYS_DIR/windows10/scripts" ]] && [[ -n "$(ls -A "$OVERLAYS_DIR/windows10/scripts" 2>/dev/null)" ]]; then
        mkdir -p "$TEMP_DIR/windows10/scripts"
        cp -r "$OVERLAYS_DIR/windows10/scripts"/* "$TEMP_DIR/windows10/scripts/"
        print_success "Applied custom scripts"
    fi

    # Copy our custom config files (if any exist)
    if [[ -d "$OVERLAYS_DIR/windows10/config" ]] && [[ -n "$(ls -A "$OVERLAYS_DIR/windows10/config" 2>/dev/null)" ]]; then
        mkdir -p "$TEMP_DIR/windows10/config"
        cp -r "$OVERLAYS_DIR/windows10/config"/* "$TEMP_DIR/windows10/config/"
        print_success "Applied custom config files"
    fi
    
    # Copy Vagrantfile template
    if [[ -f "$OVERLAYS_DIR/windows10/turdparty-vagrantfile.template" ]]; then
        cp "$OVERLAYS_DIR/windows10/turdparty-vagrantfile.template" "$TEMP_DIR/windows10/"
        print_success "Applied custom Vagrantfile template"
    fi
    
    if [[ "$quiet_mode" != "true" ]]; then
        print_success "Overlays applied successfully!"
        print_status "Working directory: $TEMP_DIR/windows10"
        print_status "Use this directory for Packer builds"
    fi

    # Return the temp directory path for use by build scripts
    echo "$TEMP_DIR/windows10"
}

# Cleanup function (only if not in quiet mode)
cleanup() {
    local quiet_mode="${1:-false}"
    if [[ -d "$TEMP_DIR" ]] && [[ "$quiet_mode" != "true" ]]; then
        print_status "Cleaning up temporary directory..."
        rm -rf "$TEMP_DIR"
    fi
}

# Set trap for cleanup on exit (only if not quiet mode)
if [[ "${1:-false}" != "true" ]]; then
    trap cleanup EXIT
fi

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
