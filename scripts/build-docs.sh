#!/bin/bash

# Build TurdParty Documentation
# This script builds the Sphinx documentation for the TurdParty API

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Building TurdParty API Documentation..."

# Check if we're in the right directory
if [ ! -f "docs/conf.py" ]; then
    print_error "docs/conf.py not found. Please run this script from the project root."
    exit 1
fi

# Create necessary directories
mkdir -p docs/_build
mkdir -p docs/_static

# Create a simple HTML documentation without Sphinx for now
print_status "Creating simple HTML documentation..."

cat > docs/_build/index.html << 'EOF'
<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TurdParty VM WebSocket API Documentation</title>
    <script src="https://unpkg.com/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        /* Dark Mode Variables */
        :root {
            --bg-primary: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --bg-tertiary: #3a3a3a;
            --text-primary: #e0e0e0;
            --text-secondary: #b0b0b0;
            --text-muted: #888888;
            --border-color: #444444;
            --accent-primary: #3498DB;
            --accent-secondary: #2ECC71;
            --accent-danger: #E74C3C;
            --accent-warning: #F39C12;
            --status-green: #28a745;
            --status-amber: #ffc107;
            --status-red: #dc3545;
        }

        /* Light mode override for system preference */
        @media (prefers-color-scheme: light) {
            :root:not([data-theme="dark"]) {
                --bg-primary: #ffffff;
                --bg-secondary: #f8f9fa;
                --bg-tertiary: #e9ecef;
                --text-primary: #212529;
                --text-secondary: #495057;
                --text-muted: #6c757d;
                --border-color: #dee2e6;
            }
        }

        /* Light theme explicit override */
        [data-theme="light"] {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --text-primary: #212529;
            --text-secondary: #495057;
            --text-muted: #6c757d;
            --border-color: #dee2e6;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: var(--bg-primary);
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        .header {
            background: linear-gradient(135deg, #2980B9, #27AE60);
            color: white;
            padding: 40px 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }

        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            padding: 8px 16px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            background: var(--bg-secondary);
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            border: 1px solid var(--border-color);
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: var(--accent-primary);
            border-bottom: 2px solid var(--accent-secondary);
            padding-bottom: 10px;
        }

        /* Status Legend Styling */
        .status-legend {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .status-legend h4 {
            margin-top: 0;
            color: var(--text-primary);
        }

        .legend-items {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin: 15px 0;
        }

        .legend-item {
            padding: 8px 12px;
            border-radius: 5px;
            font-weight: 500;
            font-size: 14px;
        }

        .legend-item.operational {
            background: var(--status-green);
            color: white;
        }

        .legend-item.degraded {
            background: var(--status-amber);
            color: black;
        }

        .legend-item.outage {
            background: var(--status-red);
            color: white;
        }

        .legend-item.maintenance {
            background: var(--accent-primary);
            color: white;
        }
        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .api-card {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            transition: transform 0.2s ease;
            color: var(--text-primary);
        }
        .api-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
        }
        .api-card h3 {
            margin-top: 0;
            color: var(--accent-primary);
        }
        .endpoint {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            color: var(--text-primary);
        }
        .method {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-weight: bold;
            font-size: 0.8em;
            margin-right: 10px;
        }
        .method.get { background: #28a745; color: white; }
        .method.post { background: #007bff; color: white; }
        .method.delete { background: #dc3545; color: white; }
        .method.ws { background: #6f42c1; color: white; }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
        }
        .feature-list li:before {
            content: "✅ ";
            margin-right: 10px;
        }
        .quick-start {
            background: var(--bg-tertiary);
            border: 1px solid var(--accent-secondary);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            color: var(--text-primary);
        }

        .quick-start h3 {
            color: var(--accent-secondary);
            margin-top: 0;
        }
        .code-block {
            background: #1e1e1e;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .nav-links {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .nav-link {
            background: var(--accent-primary);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        .nav-link:hover {
            background: var(--accent-secondary);
            color: white;
        }
    </style>
</head>
<body>
    <div class="header">
        <button id="theme-toggle" class="theme-toggle">🌙 Dark</button>
        <h1>🚀 TurdParty VM WebSocket API</h1>
        <p>Modern Malware Analysis Platform with Real-time VM Management</p>
    </div>

    <div class="content">
        <div class="nav-links">
            <a href="http://localhost:8000/docs" class="nav-link">📚 Interactive API Docs</a>
            <a href="http://localhost:8000/redoc" class="nav-link">📖 ReDoc Documentation</a>
            <a href="http://kibana.turdparty.localhost" class="nav-link">📊 Kibana Dashboard</a>
            <a href="http://status.turdparty.localhost" class="nav-link">🔍 System Status</a>
        </div>

        <div class="section">
            <h2>🎯 Overview</h2>
            <p>The TurdParty VM WebSocket API provides comprehensive virtual machine management capabilities for malware analysis, featuring real-time monitoring, command execution, and file operations through REST APIs and WebSocket connections.</p>
            
            <ul class="feature-list">
                <li><strong>Real-time VM Management</strong> - Create, monitor, and control VMs</li>
                <li><strong>WebSocket Streaming</strong> - Live metrics and command execution</li>
                <li><strong>ECS Logging</strong> - Structured logging for monitoring</li>
                <li><strong>Multi-VM Support</strong> - Docker containers and Vagrant VMs</li>
                <li><strong>Security-First Design</strong> - Built for malware analysis</li>
                <li><strong>Performance Optimised</strong> - Concurrent request handling</li>
            </ul>
        </div>

        <div class="section">
            <h2>🚀 Quick Start</h2>
            <div class="quick-start">
                <h3>1. Check API Health</h3>
                <div class="code-block">curl http://localhost:8000/health</div>
                
                <h3>2. Create a VM</h3>
                <div class="code-block">curl -X POST http://localhost:8000/api/v1/vms/ \
  -H "Content-Type: application/json" \
  -d '{
    "name": "analysis-vm",
    "template": "ubuntu:20.04",
    "vm_type": "docker",
    "memory_mb": 1024,
    "cpus": 2,
    "domain": "TurdParty"
  }'</div>
                
                <h3>3. Monitor VM Metrics (WebSocket)</h3>
                <div class="code-block">wscat -c ws://localhost:8000/api/v1/vms/{vm_id}/metrics/stream</div>
            </div>
        </div>

        <div class="section">
            <h2>📡 API Endpoints</h2>
            <div class="api-grid">
                <div class="api-card">
                    <h3>VM Management</h3>
                    <div class="endpoint">
                        <span class="method get">GET</span> /api/v1/vms/
                    </div>
                    <div class="endpoint">
                        <span class="method post">POST</span> /api/v1/vms/
                    </div>
                    <div class="endpoint">
                        <span class="method get">GET</span> /api/v1/vms/{vm_id}
                    </div>
                    <div class="endpoint">
                        <span class="method delete">DELETE</span> /api/v1/vms/{vm_id}
                    </div>
                </div>

                <div class="api-card">
                    <h3>WebSocket Streams</h3>
                    <div class="endpoint">
                        <span class="method ws">WS</span> /api/v1/vms/{vm_id}/metrics/stream
                    </div>
                    <div class="endpoint">
                        <span class="method ws">WS</span> /api/v1/vms/{vm_id}/commands/execute
                    </div>
                    <div class="endpoint">
                        <span class="method ws">WS</span> /api/v1/vms/{vm_id}/files/upload
                    </div>
                </div>

                <div class="api-card">
                    <h3>System Information</h3>
                    <div class="endpoint">
                        <span class="method get">GET</span> /health
                    </div>
                    <div class="endpoint">
                        <span class="method get">GET</span> /api/v1/status
                    </div>
                    <div class="endpoint">
                        <span class="method get">GET</span> /api/v1/vms/templates
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔌 WebSocket Features</h2>
            <div class="api-grid">
                <div class="api-card">
                    <h3>📊 Real-time Metrics</h3>
                    <p>Stream live VM performance data including CPU, memory, disk I/O, and network statistics.</p>
                </div>
                <div class="api-card">
                    <h3>💻 Command Execution</h3>
                    <p>Execute commands interactively with real-time output streaming and exit code reporting.</p>
                </div>
                <div class="api-card">
                    <h3>📁 File Operations</h3>
                    <p>Upload files with progress tracking and monitor file system changes in real-time.</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🛠️ System Architecture</h2>
            <p>The TurdParty system consists of several integrated components with real-time status monitoring:</p>

            <div class="mermaid" id="system-architecture">
                graph TB
                    subgraph "🌐 Frontend & User Interface"
                        Frontend[React Frontend]
                        Docs[Documentation]
                        Status[Status Dashboard]
                    end

                    subgraph "🔧 API & Application Services"
                        API[FastAPI Main Application]
                        VM[VM Management Service]
                        WS[WebSocket Manager]
                    end

                    subgraph "📊 Monitoring & Management"
                        Traefik[Traefik Reverse Proxy]
                        Cachet[Status Page]
                    end

                    subgraph "🗄️ Data Storage & Infrastructure"
                        ES[Elasticsearch]
                        LS[Logstash]
                        KB[Kibana]
                        MinIO[Object Storage]
                    end

                    subgraph "🖥️ Task Processing & Workers"
                        VMWorker[VM Worker Processes]
                        FileWorker[File Processing]
                        QueueWorker[Queue Tasks]
                    end

                    Frontend --> API
                    Docs --> Frontend
                    Status --> API
                    API --> VM
                    API --> WS
                    VM --> VMWorker
                    API --> ES
                    LS --> ES
                    ES --> KB
                    API --> MinIO
                    Traefik --> Frontend
                    Traefik --> API
                    Cachet --> API

                    classDef operational fill:#28a745,stroke:#333,stroke-width:2px,color:#fff
                    classDef degraded fill:#ffc107,stroke:#333,stroke-width:2px,color:#000
                    classDef outage fill:#dc3545,stroke:#333,stroke-width:2px,color:#fff
                    classDef maintenance fill:#007bff,stroke:#333,stroke-width:2px,color:#fff
            </div>

            <div class="status-legend">
                <h4>🚦 Service Status Legend:</h4>
                <div class="legend-items">
                    <span class="legend-item operational">🟢 Operational</span>
                    <span class="legend-item degraded">🟡 Degraded</span>
                    <span class="legend-item outage">🔴 Outage</span>
                    <span class="legend-item maintenance">🔵 Maintenance</span>
                </div>
                <p><em>Click on any service node above to view detailed status information.</em></p>
            </div>

            <ul class="feature-list">
                <li><strong>FastAPI Server</strong> - REST API and WebSocket endpoints</li>
                <li><strong>VM Management Service</strong> - Docker and Vagrant VM control</li>
                <li><strong>ELK Stack</strong> - Elasticsearch, Logstash, and Kibana for logging</li>
                <li><strong>Traefik Proxy</strong> - Reverse proxy and load balancing</li>
                <li><strong>Real-time Status</strong> - Live service health monitoring</li>
            </ul>
        </div>

        <div class="section">
            <h2>📈 Monitoring & Logging</h2>
            <p>Comprehensive monitoring and logging capabilities:</p>
            <ul class="feature-list">
                <li><strong>ECS-compliant Logging</strong> - Structured logs in Elastic Common Schema</li>
                <li><strong>Real-time Dashboards</strong> - Kibana visualizations and alerts</li>
                <li><strong>Performance Metrics</strong> - API response times and system resources</li>
                <li><strong>WebSocket Analytics</strong> - Connection tracking and data flow monitoring</li>
            </ul>
        </div>

        <div class="section">
            <h2>🔗 Access Points</h2>
            <div class="api-grid">
                <div class="api-card">
                    <h3>🌐 Web Interfaces</h3>
                    <ul>
                        <li><a href="http://localhost:8000/docs">Swagger UI</a></li>
                        <li><a href="http://kibana.turdparty.localhost">Kibana Dashboard</a></li>
                        <li><a href="http://status.turdparty.localhost">Status Page</a></li>
                    </ul>
                </div>
                <div class="api-card">
                    <h3>🔧 Development</h3>
                    <ul>
                        <li>API Server: localhost:8000</li>
                        <li>Elasticsearch: localhost:9200</li>
                        <li>Vagrant gRPC: localhost:40000</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Theme Toggle and RAG Status Integration -->
    <script src="_static/theme-toggle.js"></script>
    <script src="_static/rag-status.js"></script>

    <script>
        // Initialize Mermaid with dark theme
        mermaid.initialize({
            theme: 'dark',
            themeVariables: {
                primaryColor: '#3498DB',
                primaryTextColor: '#e0e0e0',
                primaryBorderColor: '#444444',
                lineColor: '#666666',
                secondaryColor: '#2ECC71',
                tertiaryColor: '#E74C3C',
                background: '#2d2d2d',
                mainBkg: '#2d2d2d',
                secondBkg: '#3a3a3a',
                tertiaryBkg: '#1a1a1a'
            }
        });

        // Check API status and update system status
        fetch('/api/v1/status')
            .then(response => response.json())
            .then(data => {
                document.getElementById('status').innerHTML = '✅ System operational - ' + data.vms.total + ' VMs available';

                // Update service status in Mermaid diagram
                if (window.turdPartyStatus) {
                    window.turdPartyStatus.currentStatus = data;
                    window.turdPartyStatus.updateDiagram('system-architecture');
                }
            })
            .catch(error => {
                document.getElementById('status').innerHTML = '⚠️ System status unknown';
                document.getElementById('status').style.background = 'var(--accent-warning)';
                document.getElementById('status').style.borderColor = 'var(--accent-warning)';
                document.getElementById('status').style.color = '#000';
            });
    </script>
</body>
</html>
EOF

print_success "HTML documentation created at docs/_build/index.html"

# Create a simple README for the docs
cat > docs/README.md << 'EOF'
# TurdParty API Documentation

This directory contains the comprehensive documentation for the TurdParty VM WebSocket API.

## Quick Access

- **Interactive API Documentation**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc
- **HTML Documentation**: Open `_build/index.html` in your browser
- **Kibana Dashboard**: http://kibana.turdparty.localhost

## Building Documentation

To build the full Sphinx documentation:

```bash
# Install documentation dependencies
pip install sphinx sphinx-rtd-theme myst-parser

# Build HTML documentation
make html

# Serve documentation locally
make serve
```

## Documentation Structure

- `api/` - API reference documentation
- `websocket/` - WebSocket endpoint documentation
- `vm/` - Virtual machine management guides
- `monitoring/` - Monitoring and logging documentation
- `installation.rst` - Installation and setup guide
- `_static/` - Static assets (CSS, images)
- `_build/` - Generated documentation output

## Features Documented

- ✅ REST API endpoints with examples
- ✅ WebSocket real-time communication
- ✅ VM management and lifecycle
- ✅ ECS logging and monitoring
- ✅ Installation and configuration
- ✅ Performance optimization
- ✅ Security considerations
- ✅ Troubleshooting guides

## Contributing

When adding new documentation:

1. Follow the existing structure and style
2. Include code examples and use cases
3. Update the table of contents in `index.rst`
4. Test documentation builds locally
5. Ensure all links work correctly

For more information, see the main project README.
EOF

print_success "Documentation README created"

# Create a simple serve script
cat > docs/serve.sh << 'EOF'
#!/bin/bash
echo "Serving TurdParty documentation at http://localhost:8080"
echo "Press Ctrl+C to stop the server"
cd _build && python3 -m http.server 8080
EOF

chmod +x docs/serve.sh

print_success "Documentation serve script created"

print_status "Documentation build complete!"
echo ""
echo "📚 Access your documentation:"
echo "   • HTML: Open docs/_build/index.html in your browser"
echo "   • Serve: Run 'cd docs && ./serve.sh' for local server"
echo "   • API Docs: http://localhost:8000/docs (if API is running)"
echo ""
echo "🎯 Next steps:"
echo "   • Install Sphinx for full documentation: pip install sphinx sphinx-rtd-theme"
echo "   • Build full docs: cd docs && make html"
echo "   • Customize styling in _static/custom.css"
