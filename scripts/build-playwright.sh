#!/bin/bash
# Build and run Playwright E2E Testing Container for TurdParty

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${PURPLE}💩🎉TurdParty🎉💩 - Playwright E2E Testing Setup${NC}"
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

echo -e "${BLUE}🔨 Building Playwright Docker image...${NC}"

# Build the Playwright image
docker build -t turdpartycollab_playwright:latest -f docker/playwright/Dockerfile .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Playwright image built successfully!${NC}"
else
    echo -e "${RED}❌ Failed to build Playwright image${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}🚀 Starting Playwright service with Docker Compose...${NC}"

# Start the Playwright service using the testing profile
docker-compose -f compose/docker-compose.yml --profile testing up -d playwright

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Playwright service started successfully!${NC}"
    echo ""
    echo -e "${YELLOW}📊 Service Information:${NC}"
    echo -e "  🌐 Dashboard: ${BLUE}http://playwright.turdparty.localhost${NC}"
    echo -e "  🏥 Health Check: ${BLUE}http://playwright.turdparty.localhost/health${NC}"
    echo -e "  📋 Container: ${BLUE}turdpartycollab_playwright${NC}"
    echo ""
    echo -e "${YELLOW}🧪 Running E2E Tests:${NC}"
    echo -e "  To run tests: ${BLUE}docker exec turdpartycollab_playwright python -m pytest tests/e2e/ -v${NC}"
    echo -e "  To view logs: ${BLUE}docker logs -f turdpartycollab_playwright${NC}"
    echo ""
    echo -e "${YELLOW}📁 Test Results:${NC}"
    echo -e "  Reports: ${BLUE}./reports/${NC}"
    echo -e "  Results: ${BLUE}./test-results/${NC}"
    echo ""
else
    echo -e "${RED}❌ Failed to start Playwright service${NC}"
    exit 1
fi

# Wait a moment for the service to start
echo -e "${BLUE}⏳ Waiting for service to be ready...${NC}"
sleep 5

# Check if the service is healthy
echo -e "${BLUE}🏥 Checking service health...${NC}"
if curl -f http://localhost:8080/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Playwright service is healthy and ready!${NC}"
else
    echo -e "${YELLOW}⚠️  Service may still be starting up. Check logs if needed.${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Playwright E2E Testing setup complete!${NC}"
echo -e "${BLUE}💡 Next steps:${NC}"
echo "  1. Ensure other TurdParty services are running"
echo "  2. Access the dashboard at http://playwright.turdparty.localhost"
echo "  3. Run E2E tests to validate the complete workflow"
echo ""
