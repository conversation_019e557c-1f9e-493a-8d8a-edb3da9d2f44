#!/bin/bash
# TurdParty Sphinx Documentation Builder
# Builds comprehensive Sphinx documentation with Getting Started guide

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCS_DIR="$PROJECT_ROOT/docs"

# Print functions
print_header() {
    echo -e "\n${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║${NC} ${CYAN}$1${NC} ${PURPLE}║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}\n"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if we're in the right directory
    if [ ! -f "$PROJECT_ROOT/docker-compose.yml" ]; then
        print_error "Not in TurdParty project root directory"
        exit 1
    fi
    
    # Check if docs directory exists
    if [ ! -d "$DOCS_DIR" ]; then
        print_error "Documentation directory not found: $DOCS_DIR"
        exit 1
    fi
    
    # Check if Nix is available
    if ! command -v nix-shell &> /dev/null; then
        print_error "Nix package manager not found. Please install Nix."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to clean previous builds
clean_build() {
    print_status "Cleaning previous builds..."
    
    cd "$DOCS_DIR"
    
    if [ -d "_build" ]; then
        rm -rf _build/*
        print_status "Cleaned _build directory"
    fi
    
    if [ -d "_static/generated" ]; then
        rm -rf _static/generated/*
        print_status "Cleaned generated static files"
    fi
    
    print_success "Build directory cleaned"
}

# Function to build Sphinx documentation
build_sphinx() {
    print_status "Building Sphinx documentation..."
    
    cd "$DOCS_DIR"
    
    # Build with Nix shell for dependencies
    if nix-shell -p gnumake sphinx python3Packages.sphinx-rtd-theme --run "make html" 2>&1; then
        print_success "Sphinx documentation built successfully"
    else
        print_error "Failed to build Sphinx documentation"
        exit 1
    fi
}

# Function to validate build
validate_build() {
    print_status "Validating documentation build..."
    
    local build_dir="$DOCS_DIR/_build/html"
    
    # Check if main files exist
    local required_files=(
        "index.html"
        "getting-started/index.html"
        "getting-started/components/api-layer.html"
        "getting-started/components/storage-systems.html"
        "getting-started/components/vm-management.html"
        "getting-started/quickstart/prerequisites.html"
        "getting-started/quickstart/installation.html"
    )
    
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$build_dir/$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        print_success "All required documentation files generated"
    else
        print_warning "Missing documentation files:"
        for file in "${missing_files[@]}"; do
            echo -e "  ${RED}✗${NC} $file"
        done
    fi
    
    # Count generated files
    local html_count=$(find "$build_dir" -name "*.html" | wc -l)
    local total_size=$(du -sh "$build_dir" 2>/dev/null | cut -f1 || echo "unknown")
    
    print_status "Generated $html_count HTML files (total size: $total_size)"
}

# Function to restart documentation service
restart_docs_service() {
    print_status "Restarting documentation service..."
    
    cd "$PROJECT_ROOT"
    
    # Check if docker-compose is available
    if command -v docker-compose &> /dev/null; then
        docker-compose restart docs
    elif command -v nix-shell &> /dev/null; then
        nix-shell -p docker-compose --run "docker-compose restart docs"
    else
        print_warning "Docker Compose not available. Please restart docs service manually:"
        echo "  docker-compose restart docs"
        return
    fi
    
    print_success "Documentation service restarted"
}

# Function to show access information
show_access_info() {
    print_header "📚 Documentation Access Information"
    
    echo -e "${GREEN}🌐 Web Access:${NC}"
    echo -e "  • Main Documentation: ${CYAN}http://docs.turdparty.localhost/${NC}"
    echo -e "  • Getting Started: ${CYAN}http://docs.turdparty.localhost/getting-started/${NC}"
    echo -e "  • API Layer Guide: ${CYAN}http://docs.turdparty.localhost/getting-started/components/api-layer.html${NC}"
    echo -e "  • Storage Systems: ${CYAN}http://docs.turdparty.localhost/getting-started/components/storage-systems.html${NC}"
    echo -e "  • VM Management: ${CYAN}http://docs.turdparty.localhost/getting-started/components/vm-management.html${NC}"
    echo -e "  • Prerequisites: ${CYAN}http://docs.turdparty.localhost/getting-started/quickstart/prerequisites.html${NC}"
    echo -e "  • Installation: ${CYAN}http://docs.turdparty.localhost/getting-started/quickstart/installation.html${NC}"
    
    echo -e "\n${GREEN}📁 Local Files:${NC}"
    echo -e "  • Build Directory: ${CYAN}$DOCS_DIR/_build/html/${NC}"
    echo -e "  • Source Files: ${CYAN}$DOCS_DIR/getting-started/${NC}"
    
    echo -e "\n${GREEN}🔧 Development:${NC}"
    echo -e "  • Rebuild: ${CYAN}$0${NC}"
    echo -e "  • Local Server: ${CYAN}cd $DOCS_DIR/_build/html && python3 -m http.server 8080${NC}"
    echo -e "  • Edit Sources: ${CYAN}$DOCS_DIR/getting-started/${NC}"
}

# Function to show usage
show_usage() {
    echo -e "${CYAN}TurdParty Sphinx Documentation Builder${NC}"
    echo -e "${CYAN}=====================================${NC}"
    echo ""
    echo -e "${GREEN}Usage:${NC} $0 [OPTIONS]"
    echo ""
    echo -e "${GREEN}Options:${NC}"
    echo -e "  ${YELLOW}--clean${NC}     Clean build directory before building"
    echo -e "  ${YELLOW}--no-restart${NC} Don't restart documentation service"
    echo -e "  ${YELLOW}--help${NC}      Show this help message"
    echo ""
    echo -e "${GREEN}Examples:${NC}"
    echo -e "  $0                    # Build documentation"
    echo -e "  $0 --clean            # Clean and build"
    echo -e "  $0 --no-restart       # Build without restarting service"
}

# Main function
main() {
    local clean_build_flag=false
    local restart_service=true
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --clean)
                clean_build_flag=true
                shift
                ;;
            --no-restart)
                restart_service=false
                shift
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    print_header "🚀 TurdParty Sphinx Documentation Builder"
    
    # Execute build steps
    check_prerequisites
    
    if [ "$clean_build_flag" = true ]; then
        clean_build
    fi
    
    build_sphinx
    validate_build
    
    if [ "$restart_service" = true ]; then
        restart_docs_service
    fi
    
    show_access_info
    
    print_success "Documentation build complete!"
}

# Run main function with all arguments
main "$@"
