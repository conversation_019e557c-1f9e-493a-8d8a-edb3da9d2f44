#!/bin/bash
# 💩🎉TurdParty🎉💩 Windows 10 VM Builder with Comprehensive ECS Logging
# Builds Windows 10 VM with detailed logging to Elasticsearch for debugging

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Print functions
print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Build configuration
BUILD_ID=$(uuidgen)
BUILD_START_TIME=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
ELASTICSEARCH_URL="http://elasticsearch.turdparty.localhost:9200"
LOGSTASH_URL="http://logstash.turdparty.localhost:5000"
BUILD_INDEX="turdparty-vm-build-$(date +%Y.%m.%d)"

# Logging functions
log_to_elasticsearch() {
    local level="$1"
    local stage="$2"
    local message="$3"
    local details="${4:-{}}"
    local error_message="${5:-}"
    
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
    
    # Create ECS-compliant log entry
    local ecs_entry=$(cat <<EOF
{
  "@timestamp": "$timestamp",
  "ecs": {
    "version": "8.11.0"
  },
  "event": {
    "dataset": "turdparty.vm_build",
    "category": ["process"],
    "type": ["info"],
    "action": "vm_build_step",
    "outcome": "$([ "$level" = "ERROR" ] && echo "failure" || echo "success")"
  },
  "service": {
    "name": "vm-builder",
    "type": "build",
    "version": "1.0.0"
  },
  "process": {
    "name": "build-windows-vm",
    "pid": $$,
    "title": "Windows 10 VM Build"
  },
  "host": {
    "hostname": "$(hostname)",
    "os": {
      "type": "linux",
      "platform": "linux"
    }
  },
  "turdparty": {
    "build_id": "$BUILD_ID",
    "build_stage": "$stage",
    "build_start_time": "$BUILD_START_TIME",
    "phase": "vm_build"
  },
  "log": {
    "level": "$level",
    "logger": "vm-builder"
  },
  "message": "$message",
  "labels": {
    "environment": "development",
    "component": "vm-builder",
    "build_type": "windows10"
  }
EOF
    )
    
    # Add error details if present
    if [[ -n "$error_message" ]]; then
        ecs_entry=$(echo "$ecs_entry" | jq --arg error "$error_message" '. + {"error": {"message": $error}}')
    fi
    
    # Add custom details
    if [[ "$details" != "{}" ]]; then
        ecs_entry=$(echo "$ecs_entry" | jq --argjson details "$details" '. + {"details": $details}')
    fi
    
    # Close JSON
    ecs_entry="$ecs_entry}"
    
    # Send to Elasticsearch (try direct first, then via Logstash)
    if ! curl -s -X POST "$ELASTICSEARCH_URL/$BUILD_INDEX/_doc" \
        -H "Content-Type: application/json" \
        -d "$ecs_entry" >/dev/null 2>&1; then
        
        # Fallback to Logstash
        curl -s -X POST "$LOGSTASH_URL" \
            -H "Content-Type: application/json" \
            -d "$ecs_entry" >/dev/null 2>&1 || true
    fi
    
    # Also log to console
    case "$level" in
        "ERROR") print_error "$message" ;;
        "WARN") print_warning "$message" ;;
        "SUCCESS") print_success "$message" ;;
        *) print_status "$message" ;;
    esac
}

# Enhanced logging wrapper for commands
run_with_logging() {
    local stage="$1"
    local description="$2"
    shift 2
    local cmd="$@"
    
    log_to_elasticsearch "INFO" "$stage" "Starting: $description" "{\"command\": \"$cmd\"}"
    
    local start_time=$(date +%s)
    local output_file="/tmp/build-${BUILD_ID}-${stage}.log"
    
    if eval "$cmd" 2>&1 | tee "$output_file"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        log_to_elasticsearch "SUCCESS" "$stage" "Completed: $description" "{\"duration_seconds\": $duration, \"output_file\": \"$output_file\"}"
        return 0
    else
        local exit_code=$?
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        local error_output=$(tail -20 "$output_file" | jq -R -s .)
        
        log_to_elasticsearch "ERROR" "$stage" "Failed: $description" "{\"duration_seconds\": $duration, \"exit_code\": $exit_code, \"output_file\": \"$output_file\"}" "Command failed with exit code $exit_code"
        
        return $exit_code
    fi
}

# Main build function
main() {
    local iso_path="${1:-}"
    
    log_to_elasticsearch "INFO" "initialization" "💩🎉TurdParty🎉💩 Windows 10 VM Build Started" "{\"iso_path\": \"$iso_path\", \"build_id\": \"$BUILD_ID\"}"
    
    # Validate input
    if [[ -z "$iso_path" ]]; then
        log_to_elasticsearch "ERROR" "validation" "No ISO path provided" "{}" "Usage: $0 <iso_path>"
        exit 1
    fi
    
    if [[ ! -f "$iso_path" ]]; then
        log_to_elasticsearch "ERROR" "validation" "ISO file not found: $iso_path" "{}" "File does not exist"
        exit 1
    fi
    
    # Convert to absolute path
    if [[ ! "$iso_path" = /* ]]; then
        iso_path="$PROJECT_ROOT/$iso_path"
    fi
    
    log_to_elasticsearch "SUCCESS" "validation" "ISO file validated" "{\"absolute_path\": \"$iso_path\", \"size_bytes\": $(stat -c%s "$iso_path")}"
    
    # Check prerequisites
    run_with_logging "prerequisites" "Checking build prerequisites" "check_prerequisites"
    
    # Apply overlays
    run_with_logging "overlays" "Applying TurdParty overlays" "apply_overlays_with_logging"
    
    # Calculate ISO hash
    run_with_logging "hash_calculation" "Calculating ISO SHA256 hash" "calculate_iso_hash \"$iso_path\""
    
    # Execute Packer build
    run_with_logging "packer_build" "Executing Packer build (this may take 30-60 minutes)" "execute_packer_build \"$iso_path\""
    
    log_to_elasticsearch "SUCCESS" "completion" "Windows 10 VM build completed successfully" "{\"build_duration_minutes\": $(( ($(date +%s) - $(date -d "$BUILD_START_TIME" +%s)) / 60 ))}"
}

# Helper functions
check_prerequisites() {
    command -v packer >/dev/null || { echo "Packer not found"; return 1; }
    command -v qemu-system-x86_64 >/dev/null || { echo "QEMU not found"; return 1; }
    command -v xorriso >/dev/null || { echo "xorriso not found"; return 1; }
    [[ -r /dev/kvm ]] || { echo "/dev/kvm not accessible"; return 1; }
    echo "All prerequisites satisfied"
}

apply_overlays_with_logging() {
    OVERLAY_DIR=$(bash "$SCRIPT_DIR/apply-overlays.sh" true 2>/dev/null | tail -1)
    if [[ -z "$OVERLAY_DIR" ]] || [[ ! -d "$OVERLAY_DIR" ]]; then
        echo "Failed to apply overlays"
        return 1
    fi
    echo "Overlays applied to: $OVERLAY_DIR"
    export OVERLAY_DIR
}

calculate_iso_hash() {
    local iso_path="$1"
    ISO_SHA256=$(sha256sum "$iso_path" | awk '{print $1}')
    echo "SHA256: $ISO_SHA256"
    export ISO_SHA256
}

execute_packer_build() {
    local iso_path="$1"
    cd "$OVERLAY_DIR"
    
    # Create output directory
    mkdir -p "$PROJECT_ROOT/vagrant_templates/win10-turdparty"
    
    # Run Packer with comprehensive logging
    PACKER_LOG=1 PACKER_LOG_PATH="/tmp/packer-${BUILD_ID}.log" \
    packer build \
        -var "iso_path=$iso_path" \
        -var "iso_sha256=$ISO_SHA256" \
        -var "build_uuid=$BUILD_ID" \
        packer-turdparty.json
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
