#!/bin/bash
#
# 10Baht Windows VM Builder
# Builds Windows 10 VM using 10Baht Packer templates
#

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
ISOS_DIR="$PROJECT_ROOT/isos"
PACKER_DIR="$PROJECT_ROOT/vendor/10b/packer-templates/windows/windows10"

# Default ISO path
DEFAULT_ISO_PATH="$ISOS_DIR/windows10.iso"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [ISO_PATH]

Build Windows 10 VM using 10Baht Packer templates.

Arguments:
    ISO_PATH    Path to Windows 10 ISO file (optional)
                Default: $DEFAULT_ISO_PATH

Environment Variables:
    WINDOWS_10_ISO_PATH    Alternative way to specify ISO path
    PACKER_LOG            Set to 1 for verbose Packer output

Examples:
    # Use default ISO location
    $0

    # Specify ISO path
    $0 /path/to/windows10.iso

    # Use environment variable
    export WINDOWS_10_ISO_PATH="/path/to/windows10.iso"
    $0

    # Verbose output
    PACKER_LOG=1 $0

Output:
    Built Vagrant box will be available as: 10Baht/windows10-turdparty

EOF
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."

    # Check if packer is installed
    if ! command -v packer &> /dev/null; then
        print_error "Packer is not installed. Please install Packer first."
        exit 1
    fi

    # Check if QEMU is available
    if ! command -v qemu-system-x86_64 &> /dev/null; then
        print_error "QEMU is not installed. Please install QEMU/KVM first."
        exit 1
    fi

    # Check if the packer templates directory exists
    if [[ ! -d "$PACKER_DIR" ]]; then
        print_error "10Baht Packer templates not found at: $PACKER_DIR"
        print_error "Please ensure the submodule is properly initialized:"
        print_error "  git submodule update --init --recursive"
        exit 1
    fi

    print_success "Prerequisites check passed"
}

# Function to validate ISO file
validate_iso() {
    local iso_path="$1"

    print_status "Validating ISO file: $iso_path"

    if [[ ! -f "$iso_path" ]]; then
        print_error "ISO file not found: $iso_path"
        print_error ""
        print_error "⚠️  IMPORTANT: You need a Windows 10 ISO (not Windows 11)"
        print_error "The 10Baht Packer templates are specifically designed for Windows 10."
        print_error ""
        print_error "Please place your Windows 10 ISO file at: $DEFAULT_ISO_PATH"
        print_error "Or specify the correct path as an argument."
        print_error ""
        print_error "Where to get Windows 10 ISO:"
        print_error "- Microsoft Download Center: https://www.microsoft.com/software-download/windows10"
        print_error "- Windows 10 Media Creation Tool"
        print_error "- MSDN/Visual Studio Subscriptions"
        exit 1
    fi

    # Check file size (Windows 10 ISO should be at least 3GB)
    local file_size
    file_size=$(stat -c%s "$iso_path" 2>/dev/null || stat -f%z "$iso_path" 2>/dev/null)
    local min_size=$((3 * 1024 * 1024 * 1024))  # 3GB in bytes

    if [[ $file_size -lt $min_size ]]; then
        print_warning "ISO file seems small ($(numfmt --to=iec $file_size)). Expected at least 3GB for Windows 10."
        print_warning "Continuing anyway..."
    else
        print_success "ISO file size: $(numfmt --to=iec $file_size)"
    fi

    # Check if file is actually an ISO (basic check)
    if ! file "$iso_path" | grep -q "ISO 9660"; then
        print_warning "File does not appear to be an ISO 9660 image"
        print_warning "Continuing anyway..."
    fi

    # Check for Windows version in filename (basic heuristic)
    if [[ "$iso_path" =~ [Ww]in11|[Ww]indows.*11 ]]; then
        print_error "❌ ERROR: This appears to be a Windows 11 ISO!"
        print_error "The 10Baht Packer templates are designed for Windows 10 only."
        print_error "Please use a Windows 10 ISO instead."
        print_error ""
        print_error "Where to get Windows 10 ISO:"
        print_error "- Microsoft Download Center: https://www.microsoft.com/software-download/windows10"
        exit 1
    fi

    print_success "ISO validation completed"
}

# Function to build the VM
build_vm() {
    local iso_path="$1"

    print_status "Starting Windows 10 VM build..."
    print_status "ISO: $iso_path"

    # Apply TurdParty overlays
    print_status "Applying TurdParty overlays..."
    OVERLAY_DIR=$(bash "$SCRIPT_DIR/apply-overlays.sh" true 2>/dev/null | tail -1)
    if [[ $? -ne 0 ]] || [[ -z "$OVERLAY_DIR" ]]; then
        print_error "Failed to apply overlays"
        exit 1
    fi
    print_success "Overlays applied to: $OVERLAY_DIR"

    # Convert relative path to absolute path before changing directory
    if [[ ! "$iso_path" = /* ]]; then
        iso_path="$PROJECT_ROOT/$iso_path"
    fi

    # Convert relative path to absolute path before changing directory
    if [[ ! "$iso_path" = /* ]]; then
        iso_path="$PROJECT_ROOT/$iso_path"
    fi

    # Calculate SHA256 hash for ISO validation
    print_status "Calculating ISO SHA256 hash..."
    iso_sha256=$(sha256sum "$iso_path" | cut -d' ' -f1)
    print_success "ISO SHA256: $iso_sha256"

    # Change to overlay directory (contains our customized templates)
    cd "$OVERLAY_DIR"

    # Set environment variables for the build
    export WINDOWS_10_ISO_PATH="$iso_path"
    export PACKER_LOG="${PACKER_LOG:-0}"

    # Check if build script exists
    if [[ ! -f "build.sh" ]]; then
        print_error "Build script not found: $PACKER_DIR/build.sh"
        exit 1
    fi

    # Make build script executable
    chmod +x build.sh

    print_status "Executing Packer build..."
    print_status "This may take 30-60 minutes depending on your system..."

    # Create TurdParty shared directory for VM communication
    mkdir -p "$PROJECT_ROOT/turdparty-shared"

    # Run the build using our custom Packer configuration
    print_status "Executing Packer build..."
    if packer build \
        -var "iso_path=$iso_path" \
        -var "iso_sha256=$iso_sha256" \
        -var "build_uuid=$(uuidgen)" \
        packer-turdparty.json; then
        print_success "Windows 10 VM build completed successfully!"
        print_success "Vagrant box should be available as: 10Baht/windows10-turdparty"

        # Check if box was actually created
        if vagrant box list | grep -q "10Baht/windows10-turdparty"; then
            print_success "Vagrant box confirmed in local registry"
            print_success "Box is ready for TurdParty gRPC integration on port 40000"
        else
            print_warning "Vagrant box not found in local registry"
            print_warning "You may need to manually add the box:"
            print_warning "  vagrant box add 10Baht/windows10-turdparty /path/to/built/box"
        fi

        # Show integration information
        print_status "TurdParty Integration Ready:"
        print_status "- gRPC communication: localhost:40000"
        print_status "- Shared directory: $PROJECT_ROOT/turdparty-shared"
        print_status "- Fibratus monitoring: Pre-installed"
        print_status "- Template name: 10Baht/windows10-turdparty"
    else
        print_error "Windows 10 VM build failed!"
        print_error "Check the Packer logs above for details"
        exit 1
    fi
}

# Main function
main() {
    # Parse arguments
    if [[ $# -gt 1 ]]; then
        show_usage
        exit 1
    fi

    if [[ $# -eq 1 ]]; then
        if [[ "$1" == "-h" || "$1" == "--help" ]]; then
            show_usage
            exit 0
        fi
        ISO_PATH="$1"
    else
        # Use environment variable or default
        ISO_PATH="${WINDOWS_10_ISO_PATH:-$DEFAULT_ISO_PATH}"
    fi

    print_status "💩🎉TurdParty🎉💩 Windows 10 VM Builder"
    print_status "========================================"

    # Run the build process
    check_prerequisites
    validate_iso "$ISO_PATH"
    build_vm "$ISO_PATH"

    print_success "Build process completed!"
    print_status "You can now use the Windows VM in TurdParty workflows"
}

# Run main function
main "$@"
