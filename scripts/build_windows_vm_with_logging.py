#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 Windows 10 VM Builder with Comprehensive ECS Logging
Builds Windows 10 VM with detailed logging to Elasticsearch for debugging
"""

import os
import sys
import json
import uuid
import time
import hashlib
import subprocess
import requests
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, Optional
import logging

# Add project root to path for imports
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

class VMBuildLogger:
    """ECS-compliant logger for VM build process"""
    
    def __init__(self):
        self.build_id = str(uuid.uuid4())
        self.build_start_time = datetime.now(timezone.utc)
        self.elasticsearch_url = "http://elasticsearch.turdparty.localhost:9200"
        self.logstash_url = "http://logstash.turdparty.localhost:5000"
        self.build_index = f"turdparty-vm-build-{datetime.now().strftime('%Y.%m.%d')}"
        
        # Setup console logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.console_logger = logging.getLogger(__name__)
        
    def log_event(self, level: str, stage: str, message: str, 
                  details: Optional[Dict[str, Any]] = None,
                  error_message: Optional[str] = None):
        """Log event to both Elasticsearch and console"""
        
        timestamp = datetime.now(timezone.utc).isoformat()
        
        # Create ECS-compliant log entry
        ecs_entry = {
            "@timestamp": timestamp,
            "ecs": {"version": "8.11.0"},
            "event": {
                "dataset": "turdparty.vm_build",
                "category": ["process"],
                "type": ["info"],
                "action": "vm_build_step",
                "outcome": "failure" if level == "ERROR" else "success"
            },
            "service": {
                "name": "vm-builder",
                "type": "build", 
                "version": "1.0.0"
            },
            "process": {
                "name": "build-windows-vm",
                "pid": os.getpid(),
                "title": "Windows 10 VM Build"
            },
            "host": {
                "hostname": os.uname().nodename,
                "os": {
                    "type": "linux",
                    "platform": "linux"
                }
            },
            "turdparty": {
                "build_id": self.build_id,
                "build_stage": stage,
                "build_start_time": self.build_start_time.isoformat(),
                "phase": "vm_build"
            },
            "log": {
                "level": level,
                "logger": "vm-builder"
            },
            "message": message,
            "labels": {
                "environment": "development",
                "component": "vm-builder",
                "build_type": "windows10"
            }
        }
        
        # Add error details if present
        if error_message:
            ecs_entry["error"] = {"message": error_message}
            
        # Add custom details
        if details:
            ecs_entry["details"] = details
            
        # Send to Elasticsearch
        self._send_to_elasticsearch(ecs_entry)
        
        # Log to console
        console_level = getattr(logging, level if level != "SUCCESS" else "INFO")
        self.console_logger.log(console_level, f"[{stage}] {message}")
        
    def _send_to_elasticsearch(self, ecs_entry: Dict[str, Any]):
        """Send log entry to Elasticsearch with fallback to local file"""

        # Always log to local file for debugging
        log_file = f"/tmp/vm-build-{self.build_id}.jsonl"
        try:
            with open(log_file, "a") as f:
                f.write(json.dumps(ecs_entry) + "\n")
        except Exception:
            pass

        try:
            # Try direct Elasticsearch first
            response = requests.post(
                f"{self.elasticsearch_url}/{self.build_index}/_doc",
                json=ecs_entry,
                timeout=5
            )
            if response.status_code not in [200, 201]:
                raise Exception(f"ES returned {response.status_code}")

        except Exception:
            try:
                # Fallback to Logstash
                requests.post(
                    self.logstash_url,
                    json=ecs_entry,
                    timeout=5
                )
            except Exception:
                # Log to console if all else fails
                self.console_logger.debug(f"Failed to send to ELK: {json.dumps(ecs_entry, indent=2)}")

class WindowsVMBuilder:
    """Windows 10 VM builder with comprehensive logging"""
    
    def __init__(self, iso_path: str):
        self.logger = VMBuildLogger()
        self.iso_path = Path(iso_path).resolve()
        self.project_root = PROJECT_ROOT
        self.overlay_dir = None
        self.iso_sha256 = None
        
    def build(self):
        """Execute the complete VM build process"""
        
        self.logger.log_event(
            "INFO", "initialization", 
            "💩🎉TurdParty🎉💩 Windows 10 VM Build Started",
            {"iso_path": str(self.iso_path), "build_id": self.logger.build_id}
        )
        
        try:
            self._validate_input()
            self._check_prerequisites()
            self._apply_overlays()
            self._calculate_iso_hash()
            self._execute_packer_build()
            
            build_duration = (datetime.now(timezone.utc) - self.logger.build_start_time).total_seconds() / 60
            self.logger.log_event(
                "SUCCESS", "completion",
                "Windows 10 VM build completed successfully",
                {"build_duration_minutes": round(build_duration, 2)}
            )
            
        except Exception as e:
            self.logger.log_event(
                "ERROR", "build_failure",
                f"VM build failed: {str(e)}",
                error_message=str(e)
            )
            raise
            
    def _validate_input(self):
        """Validate ISO file and input parameters"""
        
        if not self.iso_path.exists():
            raise FileNotFoundError(f"ISO file not found: {self.iso_path}")
            
        iso_size = self.iso_path.stat().st_size
        self.logger.log_event(
            "SUCCESS", "validation",
            "ISO file validated",
            {
                "absolute_path": str(self.iso_path),
                "size_bytes": iso_size,
                "size_gb": round(iso_size / (1024**3), 2)
            }
        )
        
    def _check_prerequisites(self):
        """Check all required tools and permissions"""
        
        required_tools = ["packer", "qemu-system-x86_64", "xorriso"]
        missing_tools = []
        
        for tool in required_tools:
            if subprocess.run(["which", tool], capture_output=True).returncode != 0:
                missing_tools.append(tool)
                
        if missing_tools:
            raise Exception(f"Missing required tools: {', '.join(missing_tools)}")
            
        # Check KVM access
        if not os.access("/dev/kvm", os.R_OK):
            raise Exception("/dev/kvm not accessible")
            
        self.logger.log_event(
            "SUCCESS", "prerequisites",
            "All prerequisites satisfied",
            {"checked_tools": required_tools}
        )
        
    def _apply_overlays(self):
        """Apply TurdParty overlays to Packer templates"""
        
        overlay_script = self.project_root / "scripts" / "apply-overlays.sh"
        
        result = subprocess.run(
            ["bash", str(overlay_script), "true"],
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            raise Exception(f"Failed to apply overlays: {result.stderr}")
            
        # Get the last line which should be the overlay directory
        self.overlay_dir = Path(result.stdout.strip().split('\n')[-1])
        
        if not self.overlay_dir.exists():
            raise Exception(f"Overlay directory not found: {self.overlay_dir}")
            
        self.logger.log_event(
            "SUCCESS", "overlays",
            "TurdParty overlays applied successfully",
            {"overlay_directory": str(self.overlay_dir)}
        )
        
    def _calculate_iso_hash(self):
        """Calculate SHA256 hash of ISO file"""
        
        start_time = time.time()
        
        hash_sha256 = hashlib.sha256()
        with open(self.iso_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
                
        self.iso_sha256 = hash_sha256.hexdigest()
        duration = time.time() - start_time
        
        self.logger.log_event(
            "SUCCESS", "hash_calculation",
            f"ISO SHA256 calculated: {self.iso_sha256}",
            {"duration_seconds": round(duration, 2)}
        )
        
    def _execute_packer_build(self):
        """Execute the Packer build with comprehensive logging"""
        
        # Create output directory
        output_dir = self.project_root / "vagrant_templates" / "win10-turdparty"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Prepare Packer command
        packer_cmd = [
            "packer", "build",
            "-var", f"iso_path={self.iso_path}",
            "-var", f"iso_sha256={self.iso_sha256}",
            "-var", f"build_uuid={self.logger.build_id}",
            "packer-turdparty.json"
        ]
        
        # Setup environment for detailed Packer logging
        env = os.environ.copy()
        env["PACKER_LOG"] = "1"
        env["PACKER_LOG_PATH"] = f"/tmp/packer-{self.logger.build_id}.log"
        
        self.logger.log_event(
            "INFO", "packer_build",
            "Starting Packer build (this may take 30-60 minutes)",
            {
                "command": " ".join(packer_cmd),
                "working_directory": str(self.overlay_dir),
                "log_file": env["PACKER_LOG_PATH"]
            }
        )
        
        # Execute Packer build
        start_time = time.time()
        
        try:
            result = subprocess.run(
                packer_cmd,
                cwd=self.overlay_dir,
                env=env,
                capture_output=True,
                text=True,
                timeout=3600  # 1 hour timeout
            )
            
            duration = time.time() - start_time
            
            if result.returncode == 0:
                self.logger.log_event(
                    "SUCCESS", "packer_build",
                    "Packer build completed successfully",
                    {
                        "duration_seconds": round(duration, 2),
                        "duration_minutes": round(duration / 60, 2),
                        "exit_code": result.returncode
                    }
                )
            else:
                raise subprocess.CalledProcessError(
                    result.returncode, packer_cmd, result.stdout, result.stderr
                )
                
        except subprocess.TimeoutExpired:
            self.logger.log_event(
                "ERROR", "packer_build",
                "Packer build timed out after 1 hour",
                {"duration_seconds": round(time.time() - start_time, 2)},
                "Build exceeded maximum time limit"
            )
            raise
            
        except subprocess.CalledProcessError as e:
            duration = time.time() - start_time
            self.logger.log_event(
                "ERROR", "packer_build",
                "Packer build failed",
                {
                    "duration_seconds": round(duration, 2),
                    "exit_code": e.returncode,
                    "stdout": e.stdout[-1000:] if e.stdout else "",  # Last 1000 chars
                    "stderr": e.stderr[-1000:] if e.stderr else ""   # Last 1000 chars
                },
                f"Packer exited with code {e.returncode}"
            )
            raise

def main():
    """Main entry point"""

    if len(sys.argv) != 2 or sys.argv[1] in ['--help', '-h', 'help']:
        print("Usage: python3 build_windows_vm_with_logging.py <iso_path>")
        print("")
        print("💩🎉TurdParty🎉💩 Windows 10 VM Builder with ECS Logging")
        print("Builds Windows 10 VM with comprehensive logging to Elasticsearch")
        print("")
        print("Arguments:")
        print("  <iso_path>    Path to Windows 10 ISO file")
        print("")
        print("Example:")
        print("  python3 build_windows_vm_with_logging.py isos/Win10_22H2_English_x64v1.iso")
        sys.exit(0 if sys.argv[1] in ['--help', '-h', 'help'] else 1)

    iso_path = sys.argv[1]
    
    try:
        builder = WindowsVMBuilder(iso_path)
        builder.build()
        print("\n🎉 Windows 10 VM build completed successfully!")
        print(f"📊 Check Elasticsearch index: {builder.logger.build_index}")
        print(f"🔍 Build ID: {builder.logger.build_id}")
        
    except Exception as e:
        print(f"\n❌ Build failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
