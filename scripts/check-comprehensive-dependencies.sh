#!/bin/bash

# 💩🎉TurdParty🎉💩 - Comprehensive Dependency Checker
# Complete dependency validation including Traefik and Health Checks
# This script runs Traefik checks first, then health check validation

set -euo pipefail

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Color definitions for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print section header
print_section_header() {
    local title=$1
    echo ""
    print_colored $PURPLE "╔══════════════════════════════════════════════════════════════════════════════╗"
    print_colored $PURPLE "║                                                                              ║"
    printf "${PURPLE}║  %-76s  ║${NC}\n" "$title"
    print_colored $PURPLE "║                                                                              ║"
    print_colored $PURPLE "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to print final results
print_final_results() {
    local traefik_result=$1
    local health_result=$2
    
    echo ""
    print_colored $BLUE "📊 COMPREHENSIVE DEPENDENCY CHECK RESULTS"
    print_colored $BLUE "═══════════════════════════════════════════"
    echo ""
    
    # Traefik results
    if [ $traefik_result -eq 0 ]; then
        print_colored $GREEN "✅ Traefik Dependencies: PASSED"
    else
        print_colored $RED "❌ Traefik Dependencies: FAILED"
    fi
    
    # Health check results
    if [ $health_result -eq 0 ]; then
        print_colored $GREEN "✅ Health Check System: PASSED"
    else
        print_colored $RED "❌ Health Check System: FAILED"
    fi
    
    echo ""
    
    # Overall result
    if [ $traefik_result -eq 0 ] && [ $health_result -eq 0 ]; then
        print_colored $GREEN "🎉 ALL DEPENDENCY CHECKS PASSED!"
        print_colored $GREEN "   TurdParty is ready to start with full functionality"
        return 0
    elif [ $traefik_result -eq 0 ]; then
        print_colored $YELLOW "⚠️  PARTIAL SUCCESS: Traefik OK, Health Check Issues"
        print_colored $YELLOW "   TurdParty can start but health monitoring may be limited"
        return 1
    elif [ $health_result -eq 0 ]; then
        print_colored $RED "❌ CRITICAL FAILURE: Traefik Issues"
        print_colored $RED "   TurdParty services will fail without Traefik"
        return 2
    else
        print_colored $RED "💥 COMPLETE FAILURE: Both Traefik and Health Checks Failed"
        print_colored $RED "   TurdParty cannot start safely"
        return 3
    fi
}

# Function to run Traefik dependency checks
run_traefik_checks() {
    local strict_mode=$1
    
    print_section_header "🔍 TRAEFIK DEPENDENCY VALIDATION"
    
    # Source and run the Traefik dependency check
    if [ -f "$SCRIPT_DIR/check-traefik-dependency.sh" ]; then
        # Run Traefik check with no-exit flag to continue to health checks
        if [ "$strict_mode" = true ]; then
            bash "$SCRIPT_DIR/check-traefik-dependency.sh" --no-exit
        else
            bash "$SCRIPT_DIR/check-traefik-dependency.sh" --relaxed --no-exit
        fi
        return $?
    else
        print_colored $RED "❌ Traefik dependency check script not found"
        return 1
    fi
}

# Function to run health check validation
run_health_checks() {
    print_section_header "🧪 HEALTH CHECK SYSTEM VALIDATION"
    
    # Check if Python is available
    if ! command -v python3 >/dev/null 2>&1; then
        print_colored $RED "❌ Python 3 not found - cannot run health check tests"
        return 1
    fi
    
    # Run health check test suite
    if [ -f "$SCRIPT_DIR/test-health-checks.py" ]; then
        print_colored $BLUE "🔍 Running health check test suite..."
        
        # Run in nix-shell if available, otherwise use system python
        if command -v nix-shell >/dev/null 2>&1 && [ -f "$PROJECT_ROOT/shell.nix" ]; then
            print_colored $CYAN "   Using Nix environment for health check tests"
            cd "$PROJECT_ROOT"
            if nix-shell --run "python3 scripts/test-health-checks.py --quick" 2>/dev/null; then
                print_colored $GREEN "✅ Health check test suite passed"
                return 0
            else
                print_colored $RED "❌ Health check test suite failed"
                return 1
            fi
        else
            print_colored $CYAN "   Using system Python for health check tests"
            if python3 "$SCRIPT_DIR/test-health-checks.py" --quick; then
                print_colored $GREEN "✅ Health check test suite passed"
                return 0
            else
                print_colored $RED "❌ Health check test suite failed"
                return 1
            fi
        fi
    else
        print_colored $RED "❌ Health check test script not found"
        return 1
    fi
}

# Function to run dependency graph validation
run_dependency_validation() {
    print_colored $BLUE "🔍 Validating service dependency graph..."
    
    # Run dependency graph validation
    if [ -f "$SCRIPT_DIR/health-check-manager.py" ]; then
        if command -v nix-shell >/dev/null 2>&1 && [ -f "$PROJECT_ROOT/shell.nix" ]; then
            cd "$PROJECT_ROOT"
            if nix-shell --run "python3 scripts/health-check-manager.py --validate" >/dev/null 2>&1; then
                print_colored $GREEN "✅ Service dependency graph is valid"
                return 0
            else
                print_colored $RED "❌ Service dependency graph validation failed"
                return 1
            fi
        else
            if python3 "$SCRIPT_DIR/health-check-manager.py" --validate >/dev/null 2>&1; then
                print_colored $GREEN "✅ Service dependency graph is valid"
                return 0
            else
                print_colored $RED "❌ Service dependency graph validation failed"
                return 1
            fi
        fi
    else
        print_colored $YELLOW "⚠️  Health check manager not found - skipping dependency validation"
        return 0
    fi
}

# Main comprehensive check function
run_comprehensive_checks() {
    local strict_mode=${1:-true}
    local exit_on_failure=${2:-true}
    
    print_colored $PURPLE "🔍 💩🎉TurdParty🎉💩 - Comprehensive Dependency Check"
    print_colored $PURPLE "═══════════════════════════════════════════════════════════════"
    print_colored $CYAN "   Validating Traefik dependencies and health check system"
    echo ""
    
    # Step 1: Run Traefik dependency checks
    run_traefik_checks "$strict_mode"
    local traefik_result=$?
    
    # Step 2: Run dependency graph validation
    run_dependency_validation
    local dep_validation_result=$?
    
    # Step 3: Run health check system validation
    run_health_checks
    local health_result=$?
    
    # Combine health check results
    if [ $dep_validation_result -eq 0 ] && [ $health_result -eq 0 ]; then
        health_result=0
    else
        health_result=1
    fi
    
    # Print final results
    print_final_results $traefik_result $health_result
    local overall_result=$?
    
    # Handle exit behavior
    if [ $overall_result -ne 0 ] && [ "$exit_on_failure" = true ]; then
        print_colored $RED "🛑 Exiting due to dependency check failures"
        exit $overall_result
    fi
    
    return $overall_result
}

# If script is run directly (not sourced), run the check
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Parse command line arguments
    STRICT_MODE=true
    EXIT_ON_FAILURE=true
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --relaxed)
                STRICT_MODE=false
                shift
                ;;
            --no-exit)
                EXIT_ON_FAILURE=false
                shift
                ;;
            --help|-h)
                echo "Usage: $0 [--relaxed] [--no-exit]"
                echo ""
                echo "💩🎉TurdParty🎉💩 Comprehensive Dependency Checker"
                echo ""
                echo "This script validates both Traefik dependencies and the health check system."
                echo ""
                echo "Options:"
                echo "  --relaxed    Use relaxed mode for Traefik checks"
                echo "  --no-exit    Don't exit on failure, just return error code"
                echo ""
                echo "Checks performed:"
                echo "  1. Traefik container status and API accessibility"
                echo "  2. Service dependency graph validation"
                echo "  3. Health check system functionality"
                echo ""
                echo "Default: Strict mode with exit on failure"
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done
    
    run_comprehensive_checks "$STRICT_MODE" "$EXIT_ON_FAILURE"
fi
