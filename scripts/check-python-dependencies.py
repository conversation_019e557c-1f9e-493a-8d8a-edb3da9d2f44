#!/usr/bin/env python3
"""
Python Dependency Checker for TurdParty

This script scans the codebase for Python imports and checks if they're available
in the current environment. It's used by the monitoring dashboard to ensure all
dependencies are properly installed.
"""

import ast
import importlib
import os
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple
import json
import subprocess


class PythonDependencyChecker:
    """Checks Python dependencies across the TurdParty codebase."""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.stdlib_modules = self._get_stdlib_modules()
        self.local_modules = self._get_local_modules()
        
    def _get_stdlib_modules(self) -> Set[str]:
        """Get list of Python standard library modules."""
        # Common stdlib modules (not exhaustive but covers most cases)
        return {
            'os', 'sys', 'json', 'time', 'datetime', 'pathlib', 'typing',
            'asyncio', 'logging', 'collections', 'functools', 'itertools',
            'subprocess', 'threading', 'multiprocessing', 'queue', 'socket',
            'urllib', 'http', 'email', 'html', 'xml', 'csv', 'sqlite3',
            'hashlib', 'hmac', 'secrets', 'uuid', 'random', 'math', 'statistics',
            'tempfile', 'shutil', 'glob', 'fnmatch', 'linecache', 'pickle',
            'copyreg', 'copy', 'pprint', 'reprlib', 'enum', 'numbers',
            'cmath', 'decimal', 'fractions', 'contextlib', 'abc', 'atexit',
            'traceback', 'gc', 'inspect', 'site', 'warnings', 'dataclasses',
            'graphlib', 'heapq', 'bisect', 'array', 'weakref', 'types',
            'operator', 'keyword', 'builtins', 'io', 'codecs', 'locale',
            'gettext', 'argparse', 'optparse', 'configparser', 'fileinput',
            'calendar', 'sched', 'mutex', 'mmap', 'readline', 'rlcompleter'
        }
    
    def _get_local_modules(self) -> Set[str]:
        """Get list of local project modules."""
        local_modules = set()
        
        # Scan for Python packages in the project
        for py_file in self.project_root.rglob("*.py"):
            if py_file.name == "__init__.py":
                # This is a package
                package_path = py_file.parent.relative_to(self.project_root)
                package_name = str(package_path).replace(os.sep, ".")
                local_modules.add(package_name)
            else:
                # This is a module
                module_path = py_file.relative_to(self.project_root)
                module_name = str(module_path.with_suffix("")).replace(os.sep, ".")
                local_modules.add(module_name)
        
        return local_modules
    
    def extract_imports_from_file(self, file_path: Path) -> List[str]:
        """Extract all import statements from a Python file."""
        imports = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name.split('.')[0])
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.append(node.module.split('.')[0])
                        
        except (SyntaxError, UnicodeDecodeError, FileNotFoundError) as e:
            print(f"Warning: Could not parse {file_path}: {e}")
        
        return imports
    
    def scan_all_imports(self) -> Dict[str, List[str]]:
        """Scan all Python files for imports."""
        all_imports = {}
        
        for py_file in self.project_root.rglob("*.py"):
            # Skip __pycache__ and .git directories
            if "__pycache__" in str(py_file) or ".git" in str(py_file):
                continue
                
            imports = self.extract_imports_from_file(py_file)
            if imports:
                relative_path = str(py_file.relative_to(self.project_root))
                all_imports[relative_path] = imports
        
        return all_imports
    
    def categorize_imports(self, all_imports: Dict[str, List[str]]) -> Dict[str, Set[str]]:
        """Categorize imports into stdlib, local, and external."""
        stdlib_imports = set()
        local_imports = set()
        external_imports = set()
        
        for file_path, imports in all_imports.items():
            for imp in imports:
                if imp in self.stdlib_modules:
                    stdlib_imports.add(imp)
                elif any(imp.startswith(local) for local in self.local_modules):
                    local_imports.add(imp)
                else:
                    external_imports.add(imp)
        
        return {
            'stdlib': stdlib_imports,
            'local': local_imports,
            'external': external_imports
        }
    
    def check_import_availability(self, imports: Set[str]) -> Dict[str, bool]:
        """Check if imports are available in the current environment."""
        availability = {}
        
        for imp in imports:
            try:
                importlib.import_module(imp)
                availability[imp] = True
            except ImportError:
                availability[imp] = False
        
        return availability
    
    def get_nix_packages_for_missing(self, missing_imports: List[str]) -> Dict[str, str]:
        """Get suggested nix package names for missing imports."""
        # Common mappings from Python module names to nix package names
        nix_mappings = {
            'fastapi': 'python312Packages.fastapi',
            'uvicorn': 'python312Packages.uvicorn',
            'pydantic': 'python312Packages.pydantic',
            'httpx': 'python312Packages.httpx',
            'requests': 'python312Packages.requests',
            'aiofiles': 'python312Packages.aiofiles',
            'minio': 'python312Packages.minio',
            'elasticsearch': 'python312Packages.elasticsearch',
            'redis': 'python312Packages.redis',
            'celery': 'python312Packages.celery',
            'sqlalchemy': 'python312Packages.sqlalchemy',
            'alembic': 'python312Packages.alembic',
            'psycopg2': 'python312Packages.psycopg2',
            'pytest': 'python312Packages.pytest',
            'hypothesis': 'python312Packages.hypothesis',
            'click': 'python312Packages.click',
            'rich': 'python312Packages.rich',
            'docker': 'python312Packages.docker',
            'websockets': 'python312Packages.websockets',
            'psutil': 'python312Packages.psutil',
            'blake3': 'python312Packages.blake3',
            'grpcio': 'python312Packages.grpcio',
            'structlog': 'python312Packages.structlog',
            'logstash': 'python312Packages.python-logstash',
            'asyncpg': 'python312Packages.asyncpg',
        }
        
        suggestions = {}
        for imp in missing_imports:
            if imp in nix_mappings:
                suggestions[imp] = nix_mappings[imp]
            else:
                # Try common patterns
                suggestions[imp] = f'python312Packages.{imp}'
        
        return suggestions
    
    def generate_report(self) -> Dict:
        """Generate a comprehensive dependency report."""
        print("🔍 Scanning Python dependencies...")
        
        # Scan all imports
        all_imports = self.scan_all_imports()
        
        # Categorize imports
        categorized = self.categorize_imports(all_imports)
        
        # Check availability of external imports
        external_availability = self.check_import_availability(categorized['external'])
        
        # Find missing imports
        missing_imports = [imp for imp, available in external_availability.items() if not available]
        
        # Get nix package suggestions
        nix_suggestions = self.get_nix_packages_for_missing(missing_imports)
        
        report = {
            'summary': {
                'total_files_scanned': len(all_imports),
                'total_imports': sum(len(imports) for imports in all_imports.values()),
                'stdlib_imports': len(categorized['stdlib']),
                'local_imports': len(categorized['local']),
                'external_imports': len(categorized['external']),
                'missing_imports': len(missing_imports),
                'availability_rate': (len(categorized['external']) - len(missing_imports)) / len(categorized['external']) * 100 if categorized['external'] else 100
            },
            'categorized_imports': {
                'stdlib': sorted(list(categorized['stdlib'])),
                'local': sorted(list(categorized['local'])),
                'external': sorted(list(categorized['external']))
            },
            'availability': external_availability,
            'missing_imports': missing_imports,
            'nix_suggestions': nix_suggestions,
            'files_scanned': list(all_imports.keys())
        }
        
        return report
    
    def print_report(self, report: Dict):
        """Print a human-readable dependency report."""
        summary = report['summary']
        
        print(f"\n📊 Python Dependency Report")
        print(f"=" * 50)
        print(f"Files scanned: {summary['total_files_scanned']}")
        print(f"Total imports: {summary['total_imports']}")
        print(f"Standard library: {summary['stdlib_imports']}")
        print(f"Local modules: {summary['local_imports']}")
        print(f"External packages: {summary['external_imports']}")
        print(f"Missing packages: {summary['missing_imports']}")
        print(f"Availability rate: {summary['availability_rate']:.1f}%")
        
        if report['missing_imports']:
            print(f"\n❌ Missing Dependencies:")
            for imp in report['missing_imports']:
                nix_pkg = report['nix_suggestions'].get(imp, f'python312Packages.{imp}')
                print(f"  - {imp} → {nix_pkg}")
        
        print(f"\n✅ Available External Dependencies:")
        available = [imp for imp, avail in report['availability'].items() if avail]
        for imp in sorted(available):
            print(f"  - {imp}")


def main():
    """Main function for CLI usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Check Python dependencies for TurdParty")
    parser.add_argument("--json", action="store_true", help="Output as JSON")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    
    args = parser.parse_args()
    
    checker = PythonDependencyChecker(args.project_root)
    report = checker.generate_report()
    
    if args.json:
        print(json.dumps(report, indent=2))
    else:
        checker.print_report(report)
    
    # Exit with error code if there are missing dependencies
    if report['missing_imports']:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
