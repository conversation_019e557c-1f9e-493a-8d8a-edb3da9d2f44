#!/bin/bash

# 💩🎉TurdParty🎉💩 - <PERSON><PERSON><PERSON><PERSON> Dependency Checker
# Critical dependency validation for TurdParty services
# This script MUST be called before starting any TurdParty services

set -euo pipefail

# Color definitions for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print red warning banner
print_red_warning() {
    local message=$1
    echo ""
    print_colored $RED "╔══════════════════════════════════════════════════════════════════════════════╗"
    print_colored $RED "║                                                                              ║"
    print_colored $RED "║                            🚨 CRITICAL ERROR 🚨                             ║"
    print_colored $RED "║                                                                              ║"
    print_colored $RED "╠══════════════════════════════════════════════════════════════════════════════╣"
    print_colored $RED "║                                                                              ║"
    printf "${RED}║  %-76s  ║${NC}\n" "$message"
    print_colored $RED "║                                                                              ║"
    print_colored $RED "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to print success banner
print_success_banner() {
    local message=$1
    echo ""
    print_colored $GREEN "╔══════════════════════════════════════════════════════════════════════════════╗"
    print_colored $GREEN "║                                                                              ║"
    print_colored $GREEN "║                            ✅ DEPENDENCY CHECK PASSED                        ║"
    print_colored $GREEN "║                                                                              ║"
    print_colored $GREEN "╠══════════════════════════════════════════════════════════════════════════════╣"
    print_colored $GREEN "║                                                                              ║"
    printf "${GREEN}║  %-76s  ║${NC}\n" "$message"
    print_colored $GREEN "║                                                                              ║"
    print_colored $GREEN "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to check if Traefik container is running
check_traefik_container() {
    print_colored $BLUE "🔍 Checking Traefik container status..."
    
    # Check if Traefik container exists and is running
    if docker ps --filter "name=traefik" --format "{{.Names}}" | grep -q "traefik"; then
        print_colored $GREEN "✅ Traefik container is running"
        return 0
    elif docker ps -a --filter "name=traefik" --format "{{.Names}}" | grep -q "traefik"; then
        print_colored $YELLOW "⚠️  Traefik container exists but is not running"
        return 1
    else
        print_colored $RED "❌ Traefik container not found"
        return 2
    fi
}

# Function to check if Traefik API is accessible
check_traefik_api() {
    print_colored $BLUE "🔍 Checking Traefik API accessibility..."
    
    # Try to access Traefik dashboard/API
    if curl -s --connect-timeout 5 http://localhost:8080/api/overview >/dev/null 2>&1; then
        print_colored $GREEN "✅ Traefik API is accessible on port 8080"
        return 0
    else
        print_colored $RED "❌ Traefik API not accessible on port 8080"
        return 1
    fi
}

# Function to check if Traefik networks exist
check_traefik_networks() {
    print_colored $BLUE "🔍 Checking Traefik networks..."
    
    local networks_ok=true
    
    # Check for traefik_network
    if docker network ls | grep -q "traefik_network"; then
        print_colored $GREEN "✅ traefik_network exists"
    else
        print_colored $RED "❌ traefik_network not found"
        networks_ok=false
    fi
    
    # Check for turdpartycollab_net
    if docker network ls | grep -q "turdpartycollab_net"; then
        print_colored $GREEN "✅ turdpartycollab_net exists"
    else
        print_colored $YELLOW "⚠️  turdpartycollab_net not found (will be created)"
    fi
    
    if [ "$networks_ok" = true ]; then
        return 0
    else
        return 1
    fi
}

# Function to check Traefik service discovery
check_traefik_services() {
    print_colored $BLUE "🔍 Checking Traefik service discovery..."
    
    # Check if Traefik can discover services
    if curl -s --connect-timeout 5 http://localhost:8080/api/http/services >/dev/null 2>&1; then
        local service_count=$(curl -s http://localhost:8080/api/http/services | jq '. | length' 2>/dev/null || echo "0")
        print_colored $GREEN "✅ Traefik service discovery working ($service_count services registered)"
        return 0
    else
        print_colored $RED "❌ Traefik service discovery not working"
        return 1
    fi
}

# Function to provide Traefik setup instructions
provide_traefik_instructions() {
    print_colored $CYAN "📋 To start Traefik, run one of the following:"
    echo ""
    print_colored $WHITE "   Option 1: Start Traefik with docker-compose"
    print_colored $CYAN "   docker-compose -f traefik/docker-compose.yml up -d"
    echo ""
    print_colored $WHITE "   Option 2: Start Traefik standalone"
    print_colored $CYAN "   docker run -d \\"
    print_colored $CYAN "     --name traefik \\"
    print_colored $CYAN "     -p 80:80 \\"
    print_colored $CYAN "     -p 8080:8080 \\"
    print_colored $CYAN "     -v /var/run/docker.sock:/var/run/docker.sock \\"
    print_colored $CYAN "     traefik:v2.10 \\"
    print_colored $CYAN "     --api.insecure=true \\"
    print_colored $CYAN "     --providers.docker=true \\"
    print_colored $CYAN "     --providers.docker.exposedbydefault=false \\"
    print_colored $CYAN "     --entrypoints.web.address=:80"
    echo ""
    print_colored $WHITE "   Option 3: Use TurdParty's Traefik setup script"
    print_colored $CYAN "   ./scripts/setup-traefik.sh"
    echo ""
}

# Main dependency check function
check_traefik_dependency() {
    local strict_mode=${1:-true}  # Default to strict mode
    local exit_on_failure=${2:-true}  # Default to exit on failure
    
    print_colored $PURPLE "🔍 💩🎉TurdParty🎉💩 - Traefik Dependency Check"
    print_colored $PURPLE "════════════════════════════════════════════════"
    echo ""
    
    local checks_passed=0
    local total_checks=4
    
    # Check 1: Container status
    if check_traefik_container; then
        checks_passed=$((checks_passed + 1))
    fi
    
    # Check 2: API accessibility
    if check_traefik_api; then
        checks_passed=$((checks_passed + 1))
    fi
    
    # Check 3: Networks
    if check_traefik_networks; then
        checks_passed=$((checks_passed + 1))
    fi
    
    # Check 4: Service discovery
    if check_traefik_services; then
        checks_passed=$((checks_passed + 1))
    fi
    
    echo ""
    print_colored $BLUE "📊 Dependency Check Results: $checks_passed/$total_checks checks passed"
    echo ""
    
    # Determine result based on strict mode
    if [ "$strict_mode" = true ]; then
        # Strict mode: All checks must pass
        if [ $checks_passed -eq $total_checks ]; then
            print_success_banner "Traefik is fully operational and ready for TurdParty services"
            return 0
        else
            print_red_warning "Traefik is not fully operational - TurdParty services will fail"
            provide_traefik_instructions
            
            if [ "$exit_on_failure" = true ]; then
                print_colored $RED "🛑 Exiting due to critical dependency failure"
                exit 1
            else
                return 1
            fi
        fi
    else
        # Relaxed mode: At least container must be running
        if [ $checks_passed -ge 1 ]; then
            print_colored $YELLOW "⚠️  Traefik is partially operational - some features may not work"
            return 0
        else
            print_red_warning "Traefik is completely unavailable - TurdParty services will fail"
            provide_traefik_instructions
            
            if [ "$exit_on_failure" = true ]; then
                print_colored $RED "🛑 Exiting due to critical dependency failure"
                exit 1
            else
                return 1
            fi
        fi
    fi
}

# If script is run directly (not sourced), run the check
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Parse command line arguments
    STRICT_MODE=true
    EXIT_ON_FAILURE=true
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --relaxed)
                STRICT_MODE=false
                shift
                ;;
            --no-exit)
                EXIT_ON_FAILURE=false
                shift
                ;;
            --help|-h)
                echo "Usage: $0 [--relaxed] [--no-exit]"
                echo ""
                echo "Options:"
                echo "  --relaxed    Use relaxed mode (only require container running)"
                echo "  --no-exit    Don't exit on failure, just return error code"
                echo ""
                echo "Default: Strict mode with exit on failure"
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done
    
    check_traefik_dependency "$STRICT_MODE" "$EXIT_ON_FAILURE"
fi
