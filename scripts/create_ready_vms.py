#!/usr/bin/env python3
"""
Quick VM creation script to get VMs ready for file injection.
Bypasses Celery worker issues and creates VMs directly.
"""

import asyncio
import logging
import sys
import uuid
from datetime import UTC, datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "services" / "api" / "src"))

import docker
from sqlalchemy import select

try:
    from database import get_db_session
    from models.vm_instance import VMInstance, VMStatus
except ImportError:
    # Fallback for different import paths
    from services.api.src.database import get_db_session
    from services.api.src.models.vm_instance import VMInstance, VMStatus

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_ready_vm(template: str = "ubuntu:20.04", count: int = 1):
    """Create ready VMs directly using Docker API."""
    
    # Initialize Docker client
    try:
        client = docker.from_env()
        client.ping()
        logger.info("✅ Docker client connected successfully")
    except Exception as e:
        logger.error(f"❌ Failed to connect to Docker: {e}")
        return False
    
    created_vms = []
    
    for i in range(count):
        try:
            # Generate VM details
            vm_id = str(uuid.uuid4())
            vm_name = f"ready-vm-{template.replace(':', '-').replace('/', '-')}-{uuid.uuid4().hex[:8]}"
            container_name = f"turdparty_vm_{vm_name}"
            
            logger.info(f"🔨 Creating VM {i+1}/{count}: {vm_name}")
            
            # Create database record first
            async with get_db_session() as session:
                vm_instance = VMInstance(
                    id=vm_id,
                    name=vm_name,
                    template=template,
                    memory_mb=1024,
                    cpus=1,
                    disk_gb=20,
                    status=VMStatus.CREATING,
                    created_at=datetime.now(UTC),
                    updated_at=datetime.now(UTC)
                )
                session.add(vm_instance)
                await session.commit()
                logger.info(f"📝 Database record created for {vm_name}")
            
            # Pull image if needed
            try:
                client.images.get(template)
                logger.info(f"🖼️ Image {template} already available")
            except docker.errors.ImageNotFound:
                logger.info(f"📥 Pulling image {template}...")
                client.images.pull(template)
                logger.info(f"✅ Image {template} pulled successfully")
            
            # Create container
            container = client.containers.run(
                image=template,
                name=container_name,
                detach=True,
                tty=True,
                stdin_open=True,
                mem_limit="1024m",
                cpu_count=1,
                network="turdpartycollab_net",
                labels={
                    "turdparty.vm": "true",
                    "turdparty.vm.name": vm_name,
                    "turdparty.vm.template": template,
                    "turdparty.vm.id": vm_id,
                },
                environment={
                    "TURDPARTY_VM": "true",
                    "TURDPARTY_VM_NAME": vm_name,
                    "TURDPARTY_VM_ID": vm_id,
                },
                volumes={"/tmp": {"bind": "/tmp", "mode": "rw"}},
                command="/bin/bash -c 'while true; do sleep 30; done'"
            )
            
            logger.info(f"🐳 Container created: {container.id[:12]}")
            
            # Update database record with container details
            async with get_db_session() as session:
                result = await session.execute(
                    select(VMInstance).where(VMInstance.id == vm_id)
                )
                vm_instance = result.scalar_one()
                
                vm_instance.vm_id = container.id
                vm_instance.status = VMStatus.RUNNING
                vm_instance.started_at = datetime.now(UTC)
                vm_instance.updated_at = datetime.now(UTC)
                
                await session.commit()
                logger.info(f"✅ VM {vm_name} is now RUNNING and ready for injection!")
            
            created_vms.append({
                "vm_id": vm_id,
                "vm_name": vm_name,
                "container_id": container.id,
                "template": template,
                "status": "running"
            })
            
        except Exception as e:
            logger.error(f"❌ Failed to create VM {i+1}: {e}")
            # Update database record to failed status
            try:
                async with get_db_session() as session:
                    result = await session.execute(
                        select(VMInstance).where(VMInstance.id == vm_id)
                    )
                    vm_instance = result.scalar_one_or_none()
                    if vm_instance:
                        vm_instance.status = VMStatus.FAILED
                        vm_instance.error_message = str(e)
                        vm_instance.updated_at = datetime.now(UTC)
                        await session.commit()
            except Exception:
                pass
    
    logger.info(f"🎉 Created {len(created_vms)} VMs successfully!")
    for vm in created_vms:
        logger.info(f"   - {vm['vm_name']} ({vm['vm_id'][:8]}...): {vm['status']}")
    
    return created_vms


async def list_ready_vms():
    """List all ready VMs."""
    try:
        async with get_db_session() as session:
            result = await session.execute(
                select(VMInstance).where(VMInstance.status == VMStatus.RUNNING)
            )
            running_vms = result.scalars().all()
            
            logger.info(f"🖥️ Found {len(running_vms)} running VMs:")
            for vm in running_vms:
                logger.info(f"   - {vm.name} ({vm.id[:8]}...): {vm.template} - Ready for injection")
            
            return running_vms
    except Exception as e:
        logger.error(f"❌ Failed to list VMs: {e}")
        return []


async def main():
    """Main function."""
    if len(sys.argv) > 1:
        if sys.argv[1] == "list":
            await list_ready_vms()
            return
        elif sys.argv[1] == "create":
            count = int(sys.argv[2]) if len(sys.argv) > 2 else 3
            template = sys.argv[3] if len(sys.argv) > 3 else "ubuntu:20.04"
            await create_ready_vm(template, count)
            return
    
    # Default: create 3 Ubuntu VMs
    logger.info("🚀 Creating 3 ready VMs for file injection testing...")
    await create_ready_vm("ubuntu:20.04", 3)
    
    logger.info("\n📋 Current ready VMs:")
    await list_ready_vms()


if __name__ == "__main__":
    asyncio.run(main())
