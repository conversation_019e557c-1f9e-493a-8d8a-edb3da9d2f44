#!/usr/bin/env python3
"""
🔍 VM Allocation Debug Script

This script captures comprehensive logs from Elasticsearch during VM allocation
attempts to debug why allocations are failing.

Features:
- Real-time log monitoring from multiple indices
- API request/response logging
- Enhanced VM pool interaction logging
- Detailed error analysis and reporting
"""

import asyncio
import json
import time
import sys
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Dict, Any, List

import httpx
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.live import Live
from rich.layout import Layout
from rich import box

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.service_urls import ServiceURLManager

class VMAllocationDebugger:
    """Debug VM allocation issues with comprehensive logging."""
    
    def __init__(self):
        self.console = Console()
        self.url_manager = ServiceURLManager('local')
        self.elasticsearch_url = self.url_manager.get_service_url('elasticsearch')
        self.api_base = "http://localhost:8000"  # Use direct localhost for debugging
        
        self.session_id = f"debug-{int(time.time())}"
        self.logs_captured = []
        self.api_calls = []
        
        self.console.print(f"[green]🔍 VM Allocation Debugger Initialized[/green]")
        self.console.print(f"   Session ID: {self.session_id}")
        self.console.print(f"   Elasticsearch: {self.elasticsearch_url}")
        self.console.print(f"   API Base: {self.api_base}")
    
    async def capture_recent_logs(self, minutes_back: int = 5) -> List[Dict[str, Any]]:
        """Capture recent logs from Elasticsearch."""
        try:
            # Calculate time range
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(minutes=minutes_back)
            
            # Search across multiple indices
            indices = [
                "ecs-turdparty-api-*",
                "turdparty-*",
                "ecs-turdparty-*"
            ]
            
            all_logs = []
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                for index_pattern in indices:
                    try:
                        query = {
                            "query": {
                                "bool": {
                                    "must": [
                                        {
                                            "range": {
                                                "@timestamp": {
                                                    "gte": start_time.isoformat(),
                                                    "lte": end_time.isoformat()
                                                }
                                            }
                                        }
                                    ],
                                    "should": [
                                        {"match": {"message": "pool"}},
                                        {"match": {"message": "allocation"}},
                                        {"match": {"message": "vm"}},
                                        {"match": {"message": "enhanced"}},
                                        {"match": {"message": "error"}},
                                        {"match": {"message": "failed"}},
                                        {"match": {"service.name": "turdparty-api"}},
                                        {"match": {"event.dataset": "turdparty.api"}}
                                    ],
                                    "minimum_should_match": 1
                                }
                            },
                            "sort": [{"@timestamp": {"order": "desc"}}],
                            "size": 100
                        }
                        
                        response = await client.post(
                            f"{self.elasticsearch_url}/{index_pattern}/_search",
                            json=query,
                            headers={"Content-Type": "application/json"}
                        )
                        
                        if response.status_code == 200:
                            data = response.json()
                            hits = data.get("hits", {}).get("hits", [])
                            for hit in hits:
                                log_entry = {
                                    "index": hit["_index"],
                                    "timestamp": hit["_source"].get("@timestamp"),
                                    "source": hit["_source"],
                                    "score": hit.get("_score", 0)
                                }
                                all_logs.append(log_entry)
                    
                    except Exception as e:
                        self.console.print(f"[yellow]⚠️ Failed to query {index_pattern}: {e}[/yellow]")
            
            # Sort by timestamp
            all_logs.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            return all_logs[:50]  # Return top 50 most recent
            
        except Exception as e:
            self.console.print(f"[red]❌ Failed to capture logs: {e}[/red]")
            return []
    
    async def test_vm_allocation_endpoint(self, template: str = "ubuntu:20.04") -> Dict[str, Any]:
        """Test VM allocation endpoint and capture the interaction."""
        self.console.print(f"[bold cyan]🎯 Testing VM allocation for template: {template}[/bold cyan]")
        
        allocation_request = {
            "priority": 2,
            "requester_id": f"debugger-{self.session_id}",
            "timeout_seconds": 30,
            "metadata": {
                "session_id": self.session_id,
                "debug_test": True,
                "template": template
            }
        }
        
        try:
            start_time = time.time()
            
            async with httpx.AsyncClient(timeout=35.0) as client:
                # Log the request
                self.api_calls.append({
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "type": "request",
                    "method": "POST",
                    "url": f"{self.api_base}/api/v1/pools/{template}/allocate",
                    "payload": allocation_request
                })
                
                response = await client.post(
                    f"{self.api_base}/api/v1/pools/{template}/allocate",
                    json=allocation_request,
                    headers={"Content-Type": "application/json"}
                )
                
                duration = time.time() - start_time
                
                # Log the response
                response_data = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "type": "response",
                    "status_code": response.status_code,
                    "duration": duration,
                    "headers": dict(response.headers),
                    "content": response.text
                }
                
                self.api_calls.append(response_data)
                
                self.console.print(f"[blue]📡 API Call completed in {duration:.2f}s[/blue]")
                self.console.print(f"   Status: {response.status_code}")
                self.console.print(f"   Response: {response.text[:100]}...")
                
                return {
                    "success": response.status_code in [200, 201],
                    "status_code": response.status_code,
                    "response": response.text,
                    "duration": duration
                }
                
        except Exception as e:
            error_data = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "type": "error",
                "error": str(e)
            }
            self.api_calls.append(error_data)
            
            self.console.print(f"[red]❌ API call failed: {e}[/red]")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def test_pool_status_endpoint(self) -> Dict[str, Any]:
        """Test pool status endpoint."""
        self.console.print(f"[bold cyan]📊 Testing pool status endpoint[/bold cyan]")
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(f"{self.api_base}/api/v1/pools/status")
                
                self.console.print(f"[blue]📊 Pool Status Response: {response.status_code}[/blue]")
                
                if response.status_code == 200:
                    data = response.json()
                    self.console.print(f"   Templates: {len(data.get('data', {}).get('templates', {}))}")
                    return {"success": True, "data": data}
                else:
                    self.console.print(f"   Error: {response.text}")
                    return {"success": False, "error": response.text}
                    
        except Exception as e:
            self.console.print(f"[red]❌ Pool status failed: {e}[/red]")
            return {"success": False, "error": str(e)}
    
    def analyze_logs(self, logs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze captured logs for VM allocation issues."""
        analysis = {
            "total_logs": len(logs),
            "error_logs": [],
            "api_logs": [],
            "pool_logs": [],
            "celery_logs": [],
            "database_logs": []
        }
        
        for log in logs:
            source = log.get("source", {})
            message = str(source.get("message", "")).lower()
            
            # Categorize logs
            if "error" in message or "failed" in message or "exception" in message:
                analysis["error_logs"].append(log)
            
            if "api" in message or "endpoint" in message or "request" in message:
                analysis["api_logs"].append(log)
            
            if "pool" in message or "allocation" in message or "vm" in message:
                analysis["pool_logs"].append(log)
            
            if "celery" in message or "task" in message or "worker" in message:
                analysis["celery_logs"].append(log)
            
            if "database" in message or "sql" in message or "postgres" in message:
                analysis["database_logs"].append(log)
        
        return analysis
    
    def display_analysis_report(self, analysis: Dict[str, Any], api_results: List[Dict[str, Any]]):
        """Display comprehensive analysis report."""
        
        # Create summary table
        summary_table = Table(title="🔍 VM Allocation Debug Summary", box=box.ROUNDED)
        summary_table.add_column("Category", style="cyan")
        summary_table.add_column("Count", style="white", justify="right")
        summary_table.add_column("Status", style="yellow")
        
        summary_table.add_row("Total Logs Captured", str(analysis["total_logs"]), "✅" if analysis["total_logs"] > 0 else "❌")
        summary_table.add_row("Error Logs", str(len(analysis["error_logs"])), "⚠️" if len(analysis["error_logs"]) > 0 else "✅")
        summary_table.add_row("API Logs", str(len(analysis["api_logs"])), "✅" if len(analysis["api_logs"]) > 0 else "❌")
        summary_table.add_row("Pool Logs", str(len(analysis["pool_logs"])), "✅" if len(analysis["pool_logs"]) > 0 else "❌")
        summary_table.add_row("Celery Logs", str(len(analysis["celery_logs"])), "✅" if len(analysis["celery_logs"]) > 0 else "❌")
        summary_table.add_row("Database Logs", str(len(analysis["database_logs"])), "✅" if len(analysis["database_logs"]) > 0 else "❌")
        
        self.console.print(summary_table)
        
        # Show API test results
        if api_results:
            api_table = Table(title="🎯 API Test Results", box=box.ROUNDED)
            api_table.add_column("Test", style="cyan")
            api_table.add_column("Status", style="white")
            api_table.add_column("Details", style="yellow")
            
            for result in api_results:
                test_name = result.get("test_name", "Unknown")
                success = result.get("success", False)
                status = "✅ Success" if success else "❌ Failed"
                details = result.get("error", result.get("response", ""))[:50] + "..." if len(str(result.get("error", result.get("response", "")))) > 50 else str(result.get("error", result.get("response", "")))
                
                api_table.add_row(test_name, status, details)
            
            self.console.print(api_table)
        
        # Show critical errors
        if analysis["error_logs"]:
            self.console.print(f"\n[red]🚨 Critical Errors Found ({len(analysis['error_logs'])}):[/red]")
            for i, error_log in enumerate(analysis["error_logs"][:5]):  # Show top 5 errors
                source = error_log.get("source", {})
                timestamp = error_log.get("timestamp", "Unknown")
                message = source.get("message", "No message")
                
                error_panel = Panel(
                    f"[red]{message}[/red]",
                    title=f"Error {i+1} - {timestamp}",
                    box=box.SIMPLE
                )
                self.console.print(error_panel)
    
    async def run_comprehensive_debug(self):
        """Run comprehensive VM allocation debugging."""
        self.console.print(Panel(
            "[bold magenta]🔍 VM Allocation Comprehensive Debug Session[/bold magenta]",
            box=box.DOUBLE
        ))
        
        # Step 1: Test pool status
        self.console.print("\n[bold]Step 1: Testing Pool Status Endpoint[/bold]")
        pool_status_result = await self.test_pool_status_endpoint()
        
        # Step 2: Test VM allocation
        self.console.print("\n[bold]Step 2: Testing VM Allocation Endpoint[/bold]")
        allocation_result = await self.test_vm_allocation_endpoint()
        
        # Step 3: Capture logs
        self.console.print("\n[bold]Step 3: Capturing Recent Logs[/bold]")
        logs = await self.capture_recent_logs(minutes_back=10)
        
        # Step 4: Analyze logs
        self.console.print("\n[bold]Step 4: Analyzing Logs[/bold]")
        analysis = self.analyze_logs(logs)
        
        # Step 5: Display results
        api_results = [
            {"test_name": "Pool Status", **pool_status_result},
            {"test_name": "VM Allocation", **allocation_result}
        ]
        
        self.display_analysis_report(analysis, api_results)
        
        # Save detailed report
        report_data = {
            "session_id": self.session_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "api_results": api_results,
            "log_analysis": analysis,
            "api_calls": self.api_calls,
            "captured_logs": logs[:10]  # Save top 10 logs
        }
        
        report_file = Path("/tmp/vm_allocation_debug_report.json")
        with open(report_file, "w") as f:
            json.dump(report_data, f, indent=2, default=str)
        
        self.console.print(f"\n[green]✅ Debug report saved: {report_file}[/green]")
        self.console.print(f"[bold magenta]🔍 VM Allocation Debug Session Complete![/bold magenta]")


async def main():
    """Main debug function."""
    debugger = VMAllocationDebugger()
    await debugger.run_comprehensive_debug()


if __name__ == "__main__":
    asyncio.run(main())
