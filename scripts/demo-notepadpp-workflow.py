#!/usr/bin/env python3
"""
TurdParty Notepad++ Workflow Demo

Comprehensive demonstration of the TurdParty malware analysis workflow using
Notepad++ as a test case. Shows the complete Celery-based processing pipeline
from file upload through analysis to report generation and viewing.

Key Features:
    - Complete workflow demonstration with realistic expectations
    - Detailed step-by-step process explanation
    - Expected results and performance metrics
    - Multiple viewing locations and access methods
    - Command-line examples for report access
    - Integration with all TurdParty services

Workflow Stages Demonstrated:
    1. File Upload: MinIO storage with metadata generation
    2. Celery Job Creation: Background task orchestration
    3. VM Pool Allocation: Windows VM provisioning
    4. File Injection: Secure file transfer to VM
    5. Installation Execution: Silent installer execution
    6. Monitoring & Data Collection: ECS event logging
    7. VM Cleanup: Resource management and cleanup

Expected Analysis Results:
    - Installation Footprint: ~156 files, ~23 directories, ~45 registry keys
    - Runtime Behavior: 3 processes, 0 network connections, ~45s execution
    - Security Analysis: 0/10 threat score, LOW risk, valid digital signature
    - ECS Data Volume: ~500 events across multiple categories

Viewing Locations:
    - API Endpoints: Complete reports via REST API
    - Web Interfaces: Frontend dashboard, Kibana analytics
    - Direct Data Access: Elasticsearch queries, MinIO artifacts
    - Command Line: curl examples for programmatic access

Usage:
    python scripts/demo-notepadpp-workflow.py

    Displays complete workflow demonstration with expected results
    and multiple access methods for viewing generated reports.

Integration:
    Uses ServiceURLManager for dynamic endpoint generation across
    different deployment environments (development, staging, production).
"""

import json
import time
from datetime import datetime
from pathlib import Path
import sys

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.service_urls import ServiceURLManager

class NotepadPPWorkflowDemo:
    """
    Demonstrate the complete Notepad++ workflow with expected results.

    Provides comprehensive workflow demonstration including all stages
    from file upload through analysis to report viewing, with realistic
    performance expectations and multiple access methods.
    """
    
    def __init__(self):
        """
        Initialize the Notepad++ workflow demo.

        Sets up service URL management and defines the test case parameters
        for the Notepad++ installation workflow demonstration.
        """
        self.url_manager = ServiceURLManager('development')
        self.notepadpp_uuid = "d5cc1f03-3041-46f5-8ad9-7af2b270ddbb"
        self.notepadpp_filename = "npp.8.5.8.Installer.x64.exe"
        
    def demonstrate_workflow(self):
        """
        Demonstrate the complete workflow steps.

        Walks through all seven stages of the TurdParty workflow with
        detailed explanations, expected results, and viewing locations.
        """
        print("🚀 TurdParty Notepad++ Workflow Demonstration")
        print(f"📅 Demo started at: {datetime.now()}")
        print(f"🎯 Target: {self.notepadpp_filename}")
        print(f"🔑 UUID: {self.notepadpp_uuid}")
        print("=" * 80)
        
        # Step 1: File Upload
        print("\n📤 Step 1: File Upload to MinIO")
        upload_url = self.url_manager.get_api_endpoint('files', 'upload')
        print(f"   📍 Endpoint: {upload_url}")
        print(f"   📁 File: {self.notepadpp_filename} (4.8 MB)")
        print(f"   ✅ Expected: File uploaded with UUID {self.notepadpp_uuid}")
        print(f"   🪣 MinIO Location: turdparty-uploads/{self.notepadpp_uuid}/{self.notepadpp_filename}")
        
        # Step 2: Celery Workflow Job
        print("\n🔄 Step 2: Celery Workflow Job Creation")
        vm_create_url = self.url_manager.get_api_endpoint('vms', 'create')
        print(f"   📍 Endpoint: {vm_create_url}")
        print(f"   🎯 Workflow Type: file_processing")
        print(f"   ✅ Expected: Workflow job queued in Celery")
        print(f"   📊 Celery Queues:")
        print(f"      - file_ops: File operations")
        print(f"      - vm_ops: VM management")
        print(f"      - injection_ops: File injection")
        print(f"      - workflow_ops: Orchestration")
        
        # Step 3: VM Pool Allocation
        print("\n🖥️ Step 3: VM Allocation from Pool")
        print(f"   🎯 VM Pool Manager: get_vm_for_processing()")
        print(f"   📋 Template: gusztavvargadr/windows-10")
        print(f"   💾 Memory: 4096 MB")
        print(f"   🖥️ CPUs: 2")
        print(f"   ✅ Expected: Windows VM allocated from pool")
        print(f"   🔄 Pool Maintenance: Triggered to replace allocated VM")
        
        # Step 4: File Injection
        print("\n💉 Step 4: File Injection into VM")
        print(f"   🎯 Celery Task: inject_file()")
        print(f"   📁 Source: MinIO bucket")
        print(f"   📂 Target: C:\\temp\\{self.notepadpp_filename}")
        print(f"   ✅ Expected: File injected successfully")
        print(f"   📊 ECS Event: file_injection logged to Elasticsearch")
        
        # Step 5: Installation Execution
        print("\n⚙️ Step 5: Installation Execution")
        print(f"   🎯 Command: {self.notepadpp_filename} /S")
        print(f"   📂 Working Dir: C:\\temp")
        print(f"   ⏱️ Timeout: 300 seconds")
        print(f"   ✅ Expected: Silent installation completed")
        print(f"   📊 ECS Events: Process execution, file creation, registry changes")
        
        # Step 6: Monitoring & Data Collection
        print("\n📊 Step 6: Monitoring & Data Collection")
        print(f"   🎯 VM Agent: Monitoring filesystem, registry, processes")
        print(f"   📈 Duration: 30 minutes maximum")
        print(f"   📊 ECS Indices:")
        print(f"      - turdparty-install-ecs-*: Installation events")
        print(f"      - turdparty-runtime-ecs-*: Runtime behavior")
        print(f"   ✅ Expected: Comprehensive execution data collected")
        
        # Step 7: VM Cleanup
        print("\n🧹 Step 7: VM Cleanup")
        print(f"   🎯 Celery Task: terminate_vm()")
        print(f"   ⏱️ Scheduled: After 30 minutes")
        print(f"   🔄 Pool Return: VM destroyed, replacement provisioned")
        print(f"   ✅ Expected: Clean VM lifecycle management")
        
        # Generate expected results
        self.show_expected_results()
        
        # Show viewing locations
        self.show_viewing_locations()
    
    def show_expected_results(self):
        """
        Show expected workflow results.

        Displays comprehensive expected results including file processing,
        VM execution, installation footprint, runtime behavior, and
        security analysis with realistic performance metrics.
        """
        print(f"\n{'='*80}")
        print("📊 EXPECTED WORKFLOW RESULTS")
        print(f"{'='*80}")
        
        print(f"\n📁 FILE PROCESSING:")
        print(f"   ✅ File UUID: {self.notepadpp_uuid}")
        print(f"   ✅ MinIO Upload: turdparty-uploads bucket")
        print(f"   ✅ Blake3 Hash: Generated for integrity")
        print(f"   ✅ Metadata: Application details stored")
        
        print(f"\n🖥️ VM EXECUTION:")
        print(f"   ✅ VM Template: Windows 10 Enterprise")
        print(f"   ✅ Installation: Notepad++ installed silently")
        print(f"   ✅ Exit Code: 0 (successful)")
        print(f"   ✅ Duration: ~45 seconds")
        
        print(f"\n📈 INSTALLATION FOOTPRINT:")
        print(f"   📁 Files Created: ~156 files")
        print(f"   📂 Directories: ~23 directories")
        print(f"   🔑 Registry Keys: ~45 keys")
        print(f"   ⚙️ Services: 0 services (text editor)")
        print(f"   💾 Disk Usage: ~15 MB")
        print(f"   📍 Install Path: C:\\Program Files\\Notepad++\\")
        
        print(f"\n⚡ RUNTIME BEHAVIOR:")
        print(f"   🔄 Processes: 3 processes spawned")
        print(f"   🌐 Network: 0 connections (offline installer)")
        print(f"   🖥️ Peak CPU: ~15%")
        print(f"   💾 Peak Memory: ~25 MB")
        print(f"   ⏱️ Execution Time: ~45 seconds")
        
        print(f"\n🔍 SECURITY ANALYSIS:")
        print(f"   📊 Threat Score: 0/10 (legitimate software)")
        print(f"   🔍 Risk Level: LOW")
        print(f"   ✅ Digital Signature: Valid (Don Ho)")
        print(f"   🛡️ Behavioral Patterns: Standard installer")
        print(f"   🔒 Persistence: Desktop shortcuts only")
        
        print(f"\n📊 ECS DATA VOLUME:")
        print(f"   📈 Total Events: ~500 events")
        print(f"   📁 File Events: ~180 events")
        print(f"   🔄 Process Events: ~25 events")
        print(f"   🔑 Registry Events: ~50 events")
        print(f"   🖥️ System Events: ~245 events")
    
    def show_viewing_locations(self):
        """
        Show where to view the results.

        Provides comprehensive guide to accessing workflow results through
        multiple interfaces including API endpoints, web dashboards, direct
        data access, and command-line tools.
        """
        print(f"\n{'='*80}")
        print("📍 WHERE TO VIEW NOTEPAD++ REPORT")
        print(f"{'='*80}")
        
        print(f"\n🌐 PRIMARY VIEWING LOCATIONS:")
        
        # API Endpoints
        print(f"\n📊 1. REPORTING API ENDPOINTS:")
        report_url = self.url_manager.get_api_endpoint('reporting', 'binary_report', file_uuid=self.notepadpp_uuid)
        summary_url = self.url_manager.get_api_endpoint('reporting', 'binary_summary', file_uuid=self.notepadpp_uuid)
        footprint_url = self.url_manager.get_api_endpoint('reporting', 'installation_footprint', file_uuid=self.notepadpp_uuid)
        runtime_url = self.url_manager.get_api_endpoint('reporting', 'runtime_behavior', file_uuid=self.notepadpp_uuid)
        
        print(f"   📋 Complete Report:")
        print(f"      {report_url}")
        print(f"   📊 Executive Summary:")
        print(f"      {summary_url}")
        print(f"   👣 Installation Footprint:")
        print(f"      {footprint_url}")
        print(f"   ⚡ Runtime Behavior:")
        print(f"      {runtime_url}")
        
        # Web Interfaces
        print(f"\n🌐 2. WEB INTERFACES:")
        frontend_url = self.url_manager.get_service_url('frontend')
        kibana_url = self.url_manager.get_service_url('kibana')
        status_url = self.url_manager.get_service_url('status')
        
        print(f"   🎨 Frontend Dashboard:")
        print(f"      {frontend_url}/reports")
        print(f"   📊 Kibana Analytics:")
        print(f"      {kibana_url}/app/discover")
        print(f"   📈 Status Dashboard:")
        print(f"      {status_url}/reports")
        
        # Direct Data Access
        print(f"\n🔍 3. DIRECT DATA ACCESS:")
        es_url = self.url_manager.get_service_url('elasticsearch')
        minio_url = self.url_manager.get_service_url('minio')
        
        print(f"   🔍 Elasticsearch Query:")
        print(f"      {es_url}/turdparty-*/_search")
        print(f"      Query: {{\"query\": {{\"term\": {{\"file_uuid.keyword\": \"{self.notepadpp_uuid}\"}}}}}}")
        print(f"   🪣 MinIO Artifacts:")
        print(f"      {minio_url}/turdparty-uploads/{self.notepadpp_uuid}/")
        
        # Command Line Access
        print(f"\n💻 4. COMMAND LINE ACCESS:")
        print(f"   📊 Generate Report:")
        print(f"      curl \"{report_url}\"")
        print(f"   🔍 Search ECS Data:")
        print(f"      curl \"{es_url}/turdparty-*/_search\" -d '{{\"query\": {{\"term\": {{\"file_uuid.keyword\": \"{self.notepadpp_uuid}\"}}}}}}'")
        
        # File Locations
        print(f"\n📁 5. GENERATED FILES:")
        print(f"   📄 JSON Report: /tmp/notepadpp-report-{int(time.time())}.json")
        print(f"   📊 ECS Logs: Elasticsearch indices")
        print(f"   🖼️ Screenshots: MinIO artifacts bucket")
        print(f"   💾 Memory Dumps: MinIO artifacts bucket")
        
        print(f"\n🎯 QUICK ACCESS COMMANDS:")
        print(f"   # View complete report")
        print(f"   curl -s \"{report_url}\" | jq .")
        print(f"   ")
        print(f"   # View summary only")
        print(f"   curl -s \"{summary_url}\" | jq .")
        print(f"   ")
        print(f"   # Search ECS data")
        print(f"   curl -s \"{es_url}/turdparty-*/_search\" \\")
        print(f"     -H \"Content-Type: application/json\" \\")
        print(f"     -d '{{\"query\": {{\"term\": {{\"file_uuid.keyword\": \"{self.notepadpp_uuid}\"}}}}}}'")
        
        print(f"\n✅ WORKFLOW DEMONSTRATION COMPLETE!")
        print(f"🎉 Once the Celery workflow runs, reports will be available at these locations!")

def main():
    """
    Main entry point for the Notepad++ workflow demonstration.

    Initializes and runs the complete workflow demonstration showing
    all stages, expected results, and viewing locations.
    """
    demo = NotepadPPWorkflowDemo()
    demo.demonstrate_workflow()

if __name__ == "__main__":
    main()
