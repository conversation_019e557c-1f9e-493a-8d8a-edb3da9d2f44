#!/usr/bin/env python3
"""
Dependency Status Service for TurdParty Monitoring Dashboard

This script provides dependency status information for the monitoring dashboard,
including Python packages, system tools, and service dependencies.
"""

import json
import subprocess
import sys
import importlib
from pathlib import Path
from typing import Dict, List, Any


class DependencyStatusService:
    """Service to check and report dependency status."""
    
    def __init__(self):
        self.core_dependencies = [
            'fastapi', 'uvicorn', 'pydantic', 'httpx', 'aiofiles',
            'minio', 'elasticsearch', 'redis', 'celery', 'sqlalchemy',
            'pytest', 'click', 'rich', 'docker', 'websockets', 'psutil',
            'blake3', 'grpc', 'structlog', 'logstash'
        ]
        
        self.system_tools = [
            'docker', 'docker-compose', 'git', 'curl', 'jq'
        ]
        
        self.nix_packages = [
            'python312', 'nodejs_20', 'postgresql_15', 'redis'
        ]
    
    def check_python_dependency(self, package: str) -> Dict[str, Any]:
        """Check if a Python package is available."""
        try:
            module = importlib.import_module(package)
            version = getattr(module, '__version__', 'unknown')
            return {
                'name': package,
                'status': 'available',
                'version': version,
                'type': 'python'
            }
        except ImportError:
            return {
                'name': package,
                'status': 'missing',
                'version': None,
                'type': 'python',
                'nix_package': f'python312Packages.{package}'
            }
    
    def check_system_tool(self, tool: str) -> Dict[str, Any]:
        """Check if a system tool is available."""
        try:
            result = subprocess.run(
                [tool, '--version'], 
                capture_output=True, 
                text=True, 
                timeout=5
            )
            if result.returncode == 0:
                # Extract version from output (first line usually)
                version_line = result.stdout.split('\n')[0] if result.stdout else result.stderr.split('\n')[0]
                return {
                    'name': tool,
                    'status': 'available',
                    'version': version_line.strip(),
                    'type': 'system'
                }
            else:
                return {
                    'name': tool,
                    'status': 'error',
                    'version': None,
                    'type': 'system',
                    'error': result.stderr.strip()
                }
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return {
                'name': tool,
                'status': 'missing',
                'version': None,
                'type': 'system'
            }
    
    def check_nix_environment(self) -> Dict[str, Any]:
        """Check if we're running in a nix environment."""
        nix_env_vars = ['NIX_SHELL', 'NIX_PATH', 'NIXPKGS_CONFIG']
        nix_indicators = sum(1 for var in nix_env_vars if var in subprocess.os.environ)
        
        try:
            # Check if nix-shell is available
            result = subprocess.run(['nix-shell', '--version'], capture_output=True, text=True, timeout=5)
            nix_available = result.returncode == 0
            nix_version = result.stdout.strip() if nix_available else None
        except (subprocess.TimeoutExpired, FileNotFoundError):
            nix_available = False
            nix_version = None
        
        return {
            'nix_available': nix_available,
            'nix_version': nix_version,
            'nix_environment': nix_indicators > 0,
            'nix_env_vars': {var: subprocess.os.environ.get(var) for var in nix_env_vars if var in subprocess.os.environ}
        }
    
    def run_dependency_checker(self) -> Dict[str, Any]:
        """Run the comprehensive dependency checker."""
        try:
            script_path = Path(__file__).parent / 'check-python-dependencies.py'
            result = subprocess.run(
                [sys.executable, str(script_path), '--json'],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode in [0, 1]:  # 0 = success, 1 = missing deps but ran successfully
                return json.loads(result.stdout)
            else:
                return {
                    'error': 'Failed to run dependency checker',
                    'stderr': result.stderr,
                    'returncode': result.returncode
                }
        except Exception as e:
            return {
                'error': f'Exception running dependency checker: {str(e)}'
            }
    
    def generate_status_report(self) -> Dict[str, Any]:
        """Generate comprehensive dependency status report."""
        print("🔍 Checking dependency status...")
        
        # Check core Python dependencies
        python_deps = []
        for dep in self.core_dependencies:
            python_deps.append(self.check_python_dependency(dep))
        
        # Check system tools
        system_deps = []
        for tool in self.system_tools:
            system_deps.append(self.check_system_tool(tool))
        
        # Check nix environment
        nix_status = self.check_nix_environment()
        
        # Run comprehensive dependency checker
        comprehensive_check = self.run_dependency_checker()
        
        # Calculate summary statistics
        python_available = sum(1 for dep in python_deps if dep['status'] == 'available')
        python_missing = sum(1 for dep in python_deps if dep['status'] == 'missing')
        system_available = sum(1 for dep in system_deps if dep['status'] == 'available')
        system_missing = sum(1 for dep in system_deps if dep['status'] == 'missing')
        
        total_deps = len(python_deps) + len(system_deps)
        total_available = python_available + system_available
        availability_rate = (total_available / total_deps * 100) if total_deps > 0 else 0
        
        # Determine overall status
        if python_missing == 0 and system_missing == 0:
            overall_status = 'healthy'
        elif python_missing <= 2 and system_missing <= 1:
            overall_status = 'warning'
        else:
            overall_status = 'critical'
        
        return {
            'timestamp': subprocess.datetime.now().isoformat(),
            'overall_status': overall_status,
            'summary': {
                'total_dependencies': total_deps,
                'available': total_available,
                'missing': python_missing + system_missing,
                'availability_rate': round(availability_rate, 1)
            },
            'python_dependencies': {
                'total': len(python_deps),
                'available': python_available,
                'missing': python_missing,
                'details': python_deps
            },
            'system_dependencies': {
                'total': len(system_deps),
                'available': system_available,
                'missing': system_missing,
                'details': system_deps
            },
            'nix_environment': nix_status,
            'comprehensive_analysis': comprehensive_check
        }
    
    def print_status_report(self, report: Dict[str, Any]):
        """Print human-readable dependency status report."""
        print(f"\n📊 Dependency Status Report")
        print(f"=" * 50)
        print(f"Overall Status: {report['overall_status'].upper()}")
        print(f"Availability Rate: {report['summary']['availability_rate']}%")
        print(f"Total Dependencies: {report['summary']['total_dependencies']}")
        print(f"Available: {report['summary']['available']}")
        print(f"Missing: {report['summary']['missing']}")
        
        # Python dependencies
        print(f"\n🐍 Python Dependencies ({report['python_dependencies']['available']}/{report['python_dependencies']['total']})")
        for dep in report['python_dependencies']['details']:
            status_icon = "✅" if dep['status'] == 'available' else "❌"
            version_info = f" (v{dep['version']})" if dep.get('version') and dep['version'] != 'unknown' else ""
            print(f"  {status_icon} {dep['name']}{version_info}")
        
        # System dependencies
        print(f"\n🔧 System Tools ({report['system_dependencies']['available']}/{report['system_dependencies']['total']})")
        for dep in report['system_dependencies']['details']:
            status_icon = "✅" if dep['status'] == 'available' else "❌"
            version_info = f" ({dep['version']})" if dep.get('version') else ""
            print(f"  {status_icon} {dep['name']}{version_info}")
        
        # Nix environment
        nix = report['nix_environment']
        nix_icon = "✅" if nix['nix_available'] and nix['nix_environment'] else "❌"
        print(f"\n❄️  Nix Environment")
        print(f"  {nix_icon} Nix Available: {nix['nix_available']}")
        print(f"  {nix_icon} In Nix Shell: {nix['nix_environment']}")
        if nix['nix_version']:
            print(f"  📦 Version: {nix['nix_version']}")


def main():
    """Main function for CLI usage."""
    import argparse
    from datetime import datetime
    
    # Add datetime to subprocess module for the report
    subprocess.datetime = datetime
    
    parser = argparse.ArgumentParser(description="Check dependency status for TurdParty")
    parser.add_argument("--json", action="store_true", help="Output as JSON")
    parser.add_argument("--monitor", action="store_true", help="Monitor mode for dashboard")
    
    args = parser.parse_args()
    
    service = DependencyStatusService()
    report = service.generate_status_report()
    
    if args.json or args.monitor:
        print(json.dumps(report, indent=2))
    else:
        service.print_status_report(report)
    
    # Exit with appropriate code
    if report['overall_status'] == 'critical':
        sys.exit(2)
    elif report['overall_status'] == 'warning':
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
