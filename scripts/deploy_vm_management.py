#!/usr/bin/env python3
"""
Deploy VM Management System

This script deploys the complete basic VM management system by:
1. Copying VM management modules to containers
2. Running database migration
3. Initializing VM pools
4. Verifying deployment

Usage:
    python scripts/deploy_vm_management.py
    python scripts/deploy_vm_management.py --dry-run
"""

import argparse
import logging
import subprocess
import sys
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_containers_running() -> bool:
    """Check if required containers are running."""
    required_containers = [
        "turdpartycollab_api",
        "turdpartycollab_celery_worker",
        "turdpartycollab_database"
    ]
    
    try:
        for container in required_containers:
            result = subprocess.run(
                ["docker", "ps", "--filter", f"name={container}", "--format", "{{.Names}}"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0 or container not in result.stdout:
                logger.error(f"❌ Container {container} is not running")
                return False
            else:
                logger.info(f"✅ Container {container} is running")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to check container status: {e}")
        return False


def copy_vm_management_modules() -> bool:
    """Copy VM management modules to containers."""
    try:
        # Files to copy to API container
        api_files = [
            ("services/workers/tasks/basic_vm_availability_manager.py", "/app/services/workers/tasks/"),
            ("services/api/src/routes/v1/basic_vm_allocation.py", "/app/services/api/src/routes/v1/"),
            ("scripts/migrate_vm_pool_schema.py", "/app/scripts/"),
            ("scripts/initialize_vm_pools.py", "/app/scripts/")
        ]
        
        # Files to copy to worker container
        worker_files = [
            ("services/workers/tasks/basic_vm_availability_manager.py", "/app/services/workers/tasks/")
        ]
        
        # Copy to API container
        logger.info("📁 Copying VM management modules to API container...")
        for src, dst in api_files:
            src_path = project_root / src
            if src_path.exists():
                copy_cmd = ["docker", "cp", str(src_path), f"turdpartycollab_api:{dst}"]
                result = subprocess.run(copy_cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"  ✅ Copied {src}")
                else:
                    logger.error(f"  ❌ Failed to copy {src}: {result.stderr}")
                    return False
            else:
                logger.warning(f"  ⚠️  Source file not found: {src}")
        
        # Copy to worker container
        logger.info("📁 Copying VM management modules to worker container...")
        for src, dst in worker_files:
            src_path = project_root / src
            if src_path.exists():
                copy_cmd = ["docker", "cp", str(src_path), f"turdpartycollab_celery_worker:{dst}"]
                result = subprocess.run(copy_cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"  ✅ Copied {src}")
                else:
                    logger.error(f"  ❌ Failed to copy {src}: {result.stderr}")
                    return False
            else:
                logger.warning(f"  ⚠️  Source file not found: {src}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to copy VM management modules: {e}")
        return False


def run_database_migration(dry_run: bool = False) -> bool:
    """Run database migration."""
    try:
        logger.info("🗄️  Running database migration...")
        
        migration_cmd = ["python", "/app/scripts/migrate_vm_pool_schema.py"]
        if dry_run:
            migration_cmd.append("--dry-run")
        
        docker_cmd = ["docker", "exec", "turdpartycollab_api"] + migration_cmd
        
        result = subprocess.run(docker_cmd, capture_output=False, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Database migration completed successfully")
            return True
        else:
            logger.error(f"❌ Database migration failed (exit code: {result.returncode})")
            return False
            
    except Exception as e:
        logger.error(f"❌ Database migration failed: {e}")
        return False


def restart_containers() -> bool:
    """Restart containers to pick up new modules."""
    try:
        containers_to_restart = ["turdpartycollab_api", "turdpartycollab_celery_worker"]
        
        logger.info("🔄 Restarting containers to load new modules...")
        
        for container in containers_to_restart:
            logger.info(f"  Restarting {container}...")
            
            restart_cmd = ["docker", "restart", container]
            result = subprocess.run(restart_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"  ✅ {container} restarted successfully")
            else:
                logger.error(f"  ❌ Failed to restart {container}: {result.stderr}")
                return False
        
        # Wait for containers to be ready
        logger.info("⏳ Waiting for containers to be ready...")
        time.sleep(30)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to restart containers: {e}")
        return False


def verify_deployment() -> bool:
    """Verify that VM management system is deployed correctly."""
    try:
        logger.info("🔍 Verifying VM management deployment...")
        
        # Test if we can import the VM management modules in the container
        test_cmd = [
            "docker", "exec", "turdpartycollab_api",
            "python", "-c",
            "from services.workers.tasks.basic_vm_availability_manager import availability_manager; print('VM management modules loaded successfully')"
        ]
        
        result = subprocess.run(test_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ VM management modules loaded successfully")
            return True
        else:
            logger.error(f"❌ Failed to load VM management modules: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Deployment verification failed: {e}")
        return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Deploy VM management system")
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without making changes"
    )
    parser.add_argument(
        "--skip-restart",
        action="store_true",
        help="Skip container restart (useful for development)"
    )
    
    args = parser.parse_args()
    
    logger.info("🚀 Starting VM Management System Deployment")
    logger.info("=" * 60)
    
    # Check if containers are running
    if not check_containers_running():
        logger.error("❌ Required containers are not running")
        logger.error("Please start the TurdParty stack: docker-compose up -d")
        sys.exit(1)
    
    # Copy VM management modules
    if not copy_vm_management_modules():
        logger.error("❌ Failed to copy VM management modules")
        sys.exit(1)
    
    # Run database migration
    if not run_database_migration(dry_run=args.dry_run):
        logger.error("❌ Database migration failed")
        sys.exit(1)
    
    if not args.dry_run:
        # Restart containers to pick up new modules
        if not args.skip_restart:
            if not restart_containers():
                logger.error("❌ Failed to restart containers")
                sys.exit(1)
        
        # Verify deployment
        if not verify_deployment():
            logger.error("❌ Deployment verification failed")
            sys.exit(1)
        
        logger.info("🎉 VM Management System deployed successfully!")
        logger.info("💡 You can now run VM management tests and initialize pools")
    else:
        logger.info("🎉 Dry run completed successfully!")
        logger.info("💡 Run without --dry-run to actually deploy the system")
    
    sys.exit(0)


if __name__ == "__main__":
    main()
