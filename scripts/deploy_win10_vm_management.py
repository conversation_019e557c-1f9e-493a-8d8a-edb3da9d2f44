#!/usr/bin/env python3
"""
Deploy Windows 10 VM Management

This script deploys VM management focused on Windows 10 malware analysis.
It sets up pools specifically optimized for Windows 10 VMs via Vagrant.

Usage:
    python scripts/deploy_win10_vm_management.py
    python scripts/deploy_win10_vm_management.py --dry-run
"""

import argparse
import logging
import subprocess
import sys
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_vagrant_vms() -> bool:
    """Check if Windows 10 VMs are available via Vagrant."""
    try:
        logger.info("🔍 Checking Vagrant Windows 10 VMs...")
        
        result = subprocess.run(
            ['vagrant', 'global-status'],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode != 0:
            logger.error(f"❌ Vagrant global-status failed: {result.stderr}")
            return False
        
        # Look for Windows VMs
        windows_vms = []
        for line in result.stdout.split('\n'):
            if 'windows' in line.lower() or '10baht' in line.lower():
                parts = line.split()
                if len(parts) >= 4:
                    vm_id = parts[0]
                    vm_name = parts[1]
                    vm_state = parts[3]
                    windows_vms.append({
                        "id": vm_id,
                        "name": vm_name,
                        "state": vm_state
                    })
        
        if not windows_vms:
            logger.warning("⚠️  No Windows VMs found in Vagrant")
            logger.info("💡 You may need to provision Windows VMs first")
            return False
        
        logger.info(f"✅ Found {len(windows_vms)} Windows VMs:")
        for vm in windows_vms:
            logger.info(f"  - {vm['name']} ({vm['id']}): {vm['state']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to check Vagrant VMs: {e}")
        return False


def run_win10_migration(dry_run: bool = False) -> bool:
    """Run database migration with Windows 10 focus."""
    try:
        logger.info("🗄️  Running Windows 10 focused database migration...")
        
        migration_cmd = ["python", "/app/scripts/migrate_vm_pool_schema.py"]
        if dry_run:
            migration_cmd.append("--dry-run")
        
        docker_cmd = ["docker", "exec", "turdpartycollab_api"] + migration_cmd
        
        result = subprocess.run(docker_cmd, capture_output=False, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Database migration completed successfully")
            return True
        else:
            logger.error(f"❌ Database migration failed (exit code: {result.returncode})")
            return False
            
    except Exception as e:
        logger.error(f"❌ Database migration failed: {e}")
        return False


def copy_vm_management_files() -> bool:
    """Copy VM management files to containers."""
    try:
        logger.info("📁 Copying Windows 10 VM management files...")
        
        files_to_copy = [
            "services/workers/tasks/basic_vm_availability_manager.py",
            "scripts/migrate_vm_pool_schema.py"
        ]
        
        for file_path in files_to_copy:
            src_path = project_root / file_path
            if src_path.exists():
                # Copy to API container
                copy_cmd = ["docker", "cp", str(src_path), f"turdpartycollab_api:/app/{file_path}"]
                result = subprocess.run(copy_cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"  ✅ Copied {file_path} to API container")
                else:
                    logger.error(f"  ❌ Failed to copy {file_path}: {result.stderr}")
                    return False
                
                # Copy to worker container if it's a worker task
                if "workers/tasks" in file_path:
                    copy_cmd = ["docker", "cp", str(src_path), f"turdpartycollab_celery_worker:/app/{file_path}"]
                    result = subprocess.run(copy_cmd, capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        logger.info(f"  ✅ Copied {file_path} to worker container")
                    else:
                        logger.error(f"  ❌ Failed to copy {file_path}: {result.stderr}")
                        return False
            else:
                logger.error(f"  ❌ Source file not found: {file_path}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to copy VM management files: {e}")
        return False


def check_win10_pool_status() -> bool:
    """Check Windows 10 pool status."""
    try:
        logger.info("🔍 Checking Windows 10 pool status...")
        
        # Test if we can access the VM management system
        test_cmd = [
            "docker", "exec", "turdpartycollab_api",
            "python", "-c",
            """
import sys
sys.path.append('/app')
from services.workers.tasks.basic_vm_availability_manager import availability_manager
status = availability_manager.get_pool_status()
win10_status = status.get('10Baht/windows10-turdparty', {})
print(f"Windows 10 Pool Status:")
print(f"  Ready: {win10_status.get('ready', 0)}")
print(f"  Total: {win10_status.get('total', 0)}")
print(f"  Creating: {win10_status.get('creating', 0)}")
print(f"  Min Required: {win10_status.get('min_ready', 0)}")
"""
        ]
        
        result = subprocess.run(test_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Windows 10 pool status check successful:")
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    logger.info(f"  {line}")
            return True
        else:
            logger.error(f"❌ Failed to check Windows 10 pool status: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Pool status check failed: {e}")
        return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Deploy Windows 10 VM management")
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without making changes"
    )
    
    args = parser.parse_args()
    
    logger.info("🚀 Starting Windows 10 VM Management Deployment")
    logger.info("=" * 60)
    
    # Check Vagrant Windows VMs
    if not check_vagrant_vms():
        logger.warning("⚠️  No Windows VMs found, but continuing with deployment")
        logger.info("💡 You can provision Windows VMs later")
    
    # Copy VM management files
    if not copy_vm_management_files():
        logger.error("❌ Failed to copy VM management files")
        sys.exit(1)
    
    # Run database migration
    if not run_win10_migration(dry_run=args.dry_run):
        logger.error("❌ Database migration failed")
        sys.exit(1)
    
    if not args.dry_run:
        # Restart worker container to pick up new modules
        logger.info("🔄 Restarting worker container...")
        restart_cmd = ["docker", "restart", "turdpartycollab_celery_worker"]
        result = subprocess.run(restart_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Worker container restarted successfully")
            
            # Wait for container to be ready
            logger.info("⏳ Waiting for worker to be ready...")
            time.sleep(15)
            
            # Check Windows 10 pool status
            if check_win10_pool_status():
                logger.info("🎉 Windows 10 VM Management deployed successfully!")
                logger.info("")
                logger.info("Next steps:")
                logger.info("1. Provision Windows 10 VMs if not already done")
                logger.info("2. Run VM management tests")
                logger.info("3. Initialize Windows 10 VM pools")
            else:
                logger.error("❌ Deployment verification failed")
                sys.exit(1)
        else:
            logger.error(f"❌ Failed to restart worker container: {result.stderr}")
            sys.exit(1)
    else:
        logger.info("🎉 Dry run completed successfully!")
        logger.info("💡 Run without --dry-run to actually deploy the system")
    
    sys.exit(0)


if __name__ == "__main__":
    main()
