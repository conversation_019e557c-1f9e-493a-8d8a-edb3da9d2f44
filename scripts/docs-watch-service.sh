#!/bin/bash

# Documentation Watch Service for TurdParty
# Monitors documentation files and rebuilds automatically

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCS_DIR="docs"
BUILD_DIR="docs/_build/html"
WATCH_INTERVAL=5
API_CHECK_INTERVAL=30
FRONTEND_RESTART_INTERVAL=300  # 5 minutes

# Counters
BUILD_COUNT=0
API_CHECK_COUNT=0
FRONTEND_RESTART_COUNT=0

print_status() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')]${NC} $1"
}

print_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')]${NC} $1"
}

# Function to check if files have changed
check_file_changes() {
    local last_build_time
    
    if [ -f "$BUILD_DIR/.last_build" ]; then
        last_build_time=$(cat "$BUILD_DIR/.last_build")
    else
        last_build_time=0
    fi
    
    # Check for newer files in docs directory
    if find "$DOCS_DIR" -name "*.rst" -o -name "*.md" -o -name "*.py" -newer "$BUILD_DIR/index.html" 2>/dev/null | grep -q .; then
        return 0  # Changes found
    fi
    
    # Check if API spec has changed
    if [ -f "$BUILD_DIR/api/openapi.json" ]; then
        local current_api_hash
        current_api_hash=$(curl -s http://localhost:8000/openapi.json 2>/dev/null | sha256sum | cut -d' ' -f1)
        local stored_api_hash
        stored_api_hash=$(cat "$BUILD_DIR/api/openapi.json" 2>/dev/null | sha256sum | cut -d' ' -f1)
        
        if [ "$current_api_hash" != "$stored_api_hash" ]; then
            print_status "API specification changes detected"
            return 0  # API changes found
        fi
    fi
    
    return 1  # No changes
}

# Function to rebuild documentation
rebuild_docs() {
    print_status "Rebuilding documentation..."
    
    if ./scripts/build-docs-auto.sh build >/dev/null 2>&1; then
        BUILD_COUNT=$((BUILD_COUNT + 1))
        echo "$(date +%s)" > "$BUILD_DIR/.last_build"
        print_success "Documentation rebuilt successfully (build #$BUILD_COUNT)"
        return 0
    else
        print_error "Documentation build failed"
        return 1
    fi
}

# Function to restart frontend if needed
restart_frontend_if_needed() {
    local restart_needed=false
    
    # Check if it's time for periodic restart
    if [ $((FRONTEND_RESTART_COUNT * FRONTEND_RESTART_INTERVAL)) -le $SECONDS ]; then
        restart_needed=true
        FRONTEND_RESTART_COUNT=$((FRONTEND_RESTART_COUNT + 1))
    fi
    
    # Check if frontend container is running
    if ! docker ps --format "{{.Names}}" | grep -q "turdpartycollab_frontend"; then
        print_warning "Frontend container not running"
        restart_needed=true
    fi
    
    if [ "$restart_needed" = true ]; then
        print_status "Restarting frontend to pick up documentation changes..."
        if docker-compose restart frontend >/dev/null 2>&1; then
            print_success "Frontend restarted successfully"
        else
            print_error "Failed to restart frontend"
        fi
    fi
}

# Function to show status
show_status() {
    local uptime_hours=$((SECONDS / 3600))
    local uptime_minutes=$(((SECONDS % 3600) / 60))
    
    echo ""
    echo "📊 Documentation Watch Service Status"
    echo "======================================"
    echo "⏱️  Uptime: ${uptime_hours}h ${uptime_minutes}m"
    echo "🔨 Builds: $BUILD_COUNT"
    echo "🔄 API Checks: $API_CHECK_COUNT"
    echo "🚀 Frontend Restarts: $FRONTEND_RESTART_COUNT"
    echo "📁 Watching: $DOCS_DIR"
    echo "🌐 Serving: http://frontend.turdparty.localhost/docs/"
    echo ""
}

# Function to cleanup on exit
cleanup() {
    print_status "Documentation watch service stopping..."
    show_status
    print_success "Documentation watch service stopped"
    exit 0
}

# Set up signal handlers
trap cleanup INT TERM

# Main watch loop
main() {
    print_status "Starting TurdParty Documentation Watch Service"
    print_status "Monitoring: $DOCS_DIR"
    print_status "Build output: $BUILD_DIR"
    print_status "Press Ctrl+C to stop"
    echo ""
    
    # Initial build
    print_status "Performing initial documentation build..."
    rebuild_docs
    
    # Start watch loop
    while true; do
        # Check for file changes
        if check_file_changes; then
            rebuild_docs
            restart_frontend_if_needed
        fi
        
        # Periodic API check
        if [ $((API_CHECK_COUNT * API_CHECK_INTERVAL)) -le $SECONDS ]; then
            API_CHECK_COUNT=$((API_CHECK_COUNT + 1))
            
            # Extract latest API docs
            if curl -s http://localhost:8000/openapi.json > "$BUILD_DIR/api/openapi.json.tmp" 2>/dev/null; then
                if ! cmp -s "$BUILD_DIR/api/openapi.json" "$BUILD_DIR/api/openapi.json.tmp" 2>/dev/null; then
                    mv "$BUILD_DIR/api/openapi.json.tmp" "$BUILD_DIR/api/openapi.json"
                    print_status "API documentation updated"
                else
                    rm -f "$BUILD_DIR/api/openapi.json.tmp"
                fi
            else
                rm -f "$BUILD_DIR/api/openapi.json.tmp"
                print_warning "Could not fetch API documentation"
            fi
        fi
        
        # Show periodic status
        if [ $((SECONDS % 300)) -eq 0 ] && [ $SECONDS -gt 0 ]; then
            show_status
        fi
        
        sleep $WATCH_INTERVAL
    done
}

# Check if we're in the right directory
if [ ! -f "docs/conf.py" ] && [ ! -f "docs/index.rst" ]; then
    print_error "Documentation directory not found. Please run from project root."
    exit 1
fi

# Ensure build directory exists
mkdir -p "$BUILD_DIR/api"

# Start the service
main
