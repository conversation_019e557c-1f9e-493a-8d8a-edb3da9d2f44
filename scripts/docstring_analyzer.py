#!/usr/bin/env python3
"""
Docstring Coverage Analyzer for TurdParty Project

This script analyzes Python files in the project to identify files that lack
comprehensive docstring coverage for modules, classes, and functions.
"""

import ast
import os
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
import json
from dataclasses import dataclass, asdict
from enum import Enum


class DocstringType(Enum):
    """Types of docstrings we analyze."""
    MODULE = "module"
    CLASS = "class"
    FUNCTION = "function"
    METHOD = "method"


@dataclass
class DocstringIssue:
    """Represents a missing or inadequate docstring."""
    file_path: str
    line_number: int
    element_type: str  # Changed from DocstringType to str for JSON serialization
    element_name: str
    issue_description: str
    severity: str  # "critical", "high", "medium", "low"


@dataclass
class FileAnalysis:
    """Analysis results for a single Python file."""
    file_path: str
    total_elements: int
    documented_elements: int
    coverage_percentage: float
    issues: List[DocstringIssue]
    has_module_docstring: bool


class DocstringAnalyzer:
    """Analyzes Python files for docstring coverage and quality."""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.exclude_patterns = {
            "__pycache__",
            ".git",
            ".pytest_cache",
            ".tox",
            "venv",
            "virtualenv",
            "node_modules",
            ".mypy_cache",
            "migrations",
            "alembic/versions"  # Auto-generated migration files
        }
        
    def should_exclude_file(self, file_path: Path) -> bool:
        """Check if file should be excluded from analysis."""
        path_str = str(file_path)
        
        # Exclude files matching patterns
        for pattern in self.exclude_patterns:
            if pattern in path_str:
                return True
                
        # Exclude test files from strict docstring requirements
        if "test_" in file_path.name or file_path.name.endswith("_test.py"):
            return True
            
        # Exclude configuration files
        if file_path.name in ["conf.py", "conftest.py", "setup.py"]:
            return True
            
        return False
    
    def get_docstring_quality_score(self, docstring: Optional[str]) -> Tuple[bool, str]:
        """
        Evaluate docstring quality.
        Returns (is_adequate, issue_description)
        """
        if not docstring:
            return False, "Missing docstring"
            
        docstring = docstring.strip()
        
        # Very short docstrings are inadequate
        if len(docstring) < 10:
            return False, "Docstring too short (less than 10 characters)"
            
        # Single line docstrings should be descriptive
        if "\n" not in docstring and len(docstring) < 20:
            return False, "Single-line docstring too brief"
            
        # Check for placeholder docstrings
        placeholder_phrases = [
            "todo", "fixme", "placeholder", "...", "pass",
            "not implemented", "implement me"
        ]
        
        docstring_lower = docstring.lower()
        for phrase in placeholder_phrases:
            if phrase in docstring_lower:
                return False, f"Contains placeholder text: '{phrase}'"
                
        return True, "Adequate docstring"
    
    def analyze_file(self, file_path: Path) -> Optional[FileAnalysis]:
        """Analyze a single Python file for docstring coverage."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            tree = ast.parse(content, filename=str(file_path))
            
        except (SyntaxError, UnicodeDecodeError) as e:
            print(f"Warning: Could not parse {file_path}: {e}")
            return None
            
        issues = []
        total_elements = 0
        documented_elements = 0
        
        # Check module docstring
        has_module_docstring = False
        module_docstring = ast.get_docstring(tree)
        if module_docstring:
            is_adequate, issue_desc = self.get_docstring_quality_score(module_docstring)
            if is_adequate:
                has_module_docstring = True
                documented_elements += 1
            else:
                issues.append(DocstringIssue(
                    file_path=str(file_path.relative_to(self.project_root)),
                    line_number=1,
                    element_type=DocstringType.MODULE.value,
                    element_name="<module>",
                    issue_description=issue_desc,
                    severity="high"
                ))
        else:
            issues.append(DocstringIssue(
                file_path=str(file_path.relative_to(self.project_root)),
                line_number=1,
                element_type=DocstringType.MODULE.value,
                element_name="<module>",
                issue_description="Missing module docstring",
                severity="high"
            ))
        
        total_elements += 1
        
        # Analyze classes and functions
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                total_elements += 1
                
                # Determine element type and severity
                if isinstance(node, ast.ClassDef):
                    element_type = DocstringType.CLASS.value
                    severity = "high"
                elif node.name.startswith('_') and not node.name.startswith('__'):
                    # Private functions/methods - lower priority
                    element_type = DocstringType.METHOD.value if self._is_method(node, tree) else DocstringType.FUNCTION.value
                    severity = "medium"
                else:
                    # Public functions/methods
                    element_type = DocstringType.METHOD.value if self._is_method(node, tree) else DocstringType.FUNCTION.value
                    severity = "high"
                
                docstring = ast.get_docstring(node)
                is_adequate, issue_desc = self.get_docstring_quality_score(docstring)
                
                if is_adequate:
                    documented_elements += 1
                else:
                    issues.append(DocstringIssue(
                        file_path=str(file_path.relative_to(self.project_root)),
                        line_number=node.lineno,
                        element_type=element_type,
                        element_name=node.name,
                        issue_description=issue_desc,
                        severity=severity
                    ))
        
        coverage_percentage = (documented_elements / total_elements * 100) if total_elements > 0 else 100
        
        return FileAnalysis(
            file_path=str(file_path.relative_to(self.project_root)),
            total_elements=total_elements,
            documented_elements=documented_elements,
            coverage_percentage=coverage_percentage,
            issues=issues,
            has_module_docstring=has_module_docstring
        )
    
    def _is_method(self, node: ast.FunctionDef, tree: ast.AST) -> bool:
        """Check if a function node is a method (inside a class)."""
        for parent in ast.walk(tree):
            if isinstance(parent, ast.ClassDef):
                if node in parent.body:
                    return True
        return False
    
    def analyze_project(self) -> List[FileAnalysis]:
        """Analyze all Python files in the project."""
        results = []
        
        # Find all Python files
        python_files = list(self.project_root.rglob("*.py"))
        
        for file_path in python_files:
            if self.should_exclude_file(file_path):
                continue
                
            analysis = self.analyze_file(file_path)
            if analysis:
                results.append(analysis)
        
        return results
    
    def generate_report(self, analyses: List[FileAnalysis]) -> Dict:
        """Generate comprehensive report from analyses."""
        # Sort by coverage percentage (worst first)
        analyses.sort(key=lambda x: x.coverage_percentage)
        
        # Calculate overall statistics
        total_files = len(analyses)
        total_elements = sum(a.total_elements for a in analyses)
        total_documented = sum(a.documented_elements for a in analyses)
        overall_coverage = (total_documented / total_elements * 100) if total_elements > 0 else 100
        
        # Categorize files by coverage
        critical_files = [a for a in analyses if a.coverage_percentage < 50]
        needs_improvement = [a for a in analyses if 50 <= a.coverage_percentage < 80]
        good_coverage = [a for a in analyses if a.coverage_percentage >= 80]
        
        # Count issues by severity
        all_issues = [issue for analysis in analyses for issue in analysis.issues]
        critical_issues = [i for i in all_issues if i.severity == "critical"]
        high_issues = [i for i in all_issues if i.severity == "high"]
        medium_issues = [i for i in all_issues if i.severity == "medium"]
        low_issues = [i for i in all_issues if i.severity == "low"]
        
        return {
            "summary": {
                "total_files_analyzed": total_files,
                "total_elements": total_elements,
                "total_documented_elements": total_documented,
                "overall_coverage_percentage": round(overall_coverage, 2),
                "files_with_critical_issues": len(critical_files),
                "files_needing_improvement": len(needs_improvement),
                "files_with_good_coverage": len(good_coverage)
            },
            "issue_counts": {
                "critical": len(critical_issues),
                "high": len(high_issues),
                "medium": len(medium_issues),
                "low": len(low_issues),
                "total": len(all_issues)
            },
            "files_by_category": {
                "critical_coverage": [asdict(a) for a in critical_files],
                "needs_improvement": [asdict(a) for a in needs_improvement],
                "good_coverage": [asdict(a) for a in good_coverage]
            },
            "all_issues": [asdict(issue) for issue in all_issues]
        }


def main():
    """Main entry point for the docstring analyzer."""
    print("🔍 TurdParty Docstring Coverage Analyzer")
    print("=" * 50)
    
    analyzer = DocstringAnalyzer()
    analyses = analyzer.analyze_project()
    report = analyzer.generate_report(analyses)
    
    # Save detailed report
    report_path = Path("reports/docstring_analysis.json")
    report_path.parent.mkdir(exist_ok=True)
    
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"📊 Analysis complete! Report saved to {report_path}")
    print(f"📈 Overall coverage: {report['summary']['overall_coverage_percentage']:.1f}%")
    print(f"📁 Files analyzed: {report['summary']['total_files_analyzed']}")
    print(f"⚠️  Total issues: {report['issue_counts']['total']}")
    
    return report


if __name__ == "__main__":
    main()
