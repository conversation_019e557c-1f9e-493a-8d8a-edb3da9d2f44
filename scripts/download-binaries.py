#!/usr/bin/env python3
"""
Download actual binaries for malware analysis.
No simulations - downloads real binaries from official sources.
"""

import os
import requests
import hashlib
import time
from pathlib import Path
from typing import Dict, Any
import blake3

class BinaryDownloader:
    """Download actual binaries from official sources."""
    
    def __init__(self):
        self.download_dir = "/tmp/turdparty_binaries"
        os.makedirs(self.download_dir, exist_ok=True)
        
        # Official download URLs for common software
        self.binary_sources = {
            "notepadpp": {
                "url": "https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.5.8/npp.8.5.8.Installer.x64.exe",
                "filename": "npp.8.5.8.Installer.x64.exe",
                "expected_size": 4796432,
                "description": "Notepad++ text editor"
            },
            "7zip": {
                "url": "https://www.7-zip.org/a/7z2301-x64.exe",
                "filename": "7z2301-x64.exe", 
                "expected_size": 1468416,
                "description": "7-Zip file archiver"
            },
            "putty": {
                "url": "https://the.earth.li/~sgtatham/putty/0.81/w64/putty-64bit-0.81-installer.msi",
                "filename": "putty-64bit-0.81-installer.msi",
                "expected_size": 3200000,
                "description": "PuTTY SSH client"
            },
            "firefox": {
                "url": "https://download.mozilla.org/?product=firefox-latest&os=win64&lang=en-US",
                "filename": "Firefox-Setup.exe",
                "expected_size": 55000000,  # Approximate
                "description": "Mozilla Firefox browser"
            },
            "vlc": {
                "url": "https://get.videolan.org/vlc/3.0.20/win64/vlc-3.0.20-win64.exe",
                "filename": "vlc-3.0.20-win64.exe",
                "expected_size": 42000000,  # Approximate
                "description": "VLC media player"
            },
            "chrome": {
                "url": "https://dl.google.com/chrome/install/ChromeStandaloneSetup64.exe",
                "filename": "ChromeSetup.exe",
                "expected_size": 1500000,  # Online installer
                "description": "Google Chrome browser"
            },
            "vscode": {
                "url": "https://code.visualstudio.com/sha/download?build=stable&os=win32-x64-user",
                "filename": "VSCodeUserSetup-x64.exe",
                "expected_size": 95000000,  # Approximate
                "description": "Visual Studio Code editor"
            },
            "git": {
                "url": "https://github.com/git-for-windows/git/releases/download/v2.43.0.windows.1/Git-2.43.0-64-bit.exe",
                "filename": "Git-2.43.0-64-bit.exe",
                "expected_size": 48000000,  # Approximate
                "description": "Git version control system"
            },
            "nodejs": {
                "url": "https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi",
                "filename": "node-v20.10.0-x64.msi",
                "expected_size": 28000000,  # Approximate
                "description": "Node.js JavaScript runtime"
            },
            "python": {
                "url": "https://www.python.org/ftp/python/3.12.1/python-3.12.1-amd64.exe",
                "filename": "python-3.12.1-amd64.exe",
                "expected_size": 25000000,  # Approximate
                "description": "Python programming language"
            }
        }
    
    def calculate_hashes(self, file_path: str) -> Dict[str, str]:
        """Calculate multiple hashes for a file."""
        hashes = {
            "md5": hashlib.md5(),
            "sha1": hashlib.sha1(),
            "sha256": hashlib.sha256(),
            "blake3": blake3.blake3()
        }
        
        with open(file_path, "rb") as f:
            while chunk := f.read(8192):
                for hasher in hashes.values():
                    hasher.update(chunk)
        
        return {name: hasher.hexdigest() for name, hasher in hashes.items()}
    
    def download_binary(self, binary_name: str) -> Dict[str, Any]:
        """Download a specific binary."""
        if binary_name not in self.binary_sources:
            return {
                "success": False,
                "error": f"Binary {binary_name} not in sources",
                "binary_name": binary_name
            }
        
        source = self.binary_sources[binary_name]
        file_path = os.path.join(self.download_dir, source["filename"])
        
        print(f"📥 Downloading {binary_name}: {source['description']}")
        print(f"   URL: {source['url']}")
        print(f"   Target: {file_path}")
        
        try:
            # Download with progress
            response = requests.get(source["url"], stream=True, timeout=300)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            start_time = time.time()
            
            with open(file_path, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        # Progress indicator
                        if total_size > 0:
                            progress = (downloaded / total_size) * 100
                            print(f"\r   Progress: {progress:.1f}% ({downloaded:,}/{total_size:,} bytes)", end="")
            
            download_time = time.time() - start_time
            print(f"\n   ✅ Download completed in {download_time:.2f} seconds")
            
            # Verify file size
            actual_size = os.path.getsize(file_path)
            print(f"   📊 File size: {actual_size:,} bytes")
            
            # Calculate hashes
            print("   🔐 Calculating hashes...")
            hashes = self.calculate_hashes(file_path)
            
            # Get file metadata
            stat = os.stat(file_path)
            
            return {
                "success": True,
                "binary_name": binary_name,
                "file_path": file_path,
                "filename": source["filename"],
                "description": source["description"],
                "url": source["url"],
                "file_size": actual_size,
                "download_time": download_time,
                "hashes": hashes,
                "created_time": stat.st_ctime,
                "modified_time": stat.st_mtime,
                "expected_size": source["expected_size"],
                "size_match": abs(actual_size - source["expected_size"]) < (source["expected_size"] * 0.1)  # 10% tolerance
            }
            
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Download failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "binary_name": binary_name,
                "url": source["url"]
            }
        except Exception as e:
            print(f"   ❌ Unexpected error: {e}")
            return {
                "success": False,
                "error": str(e),
                "binary_name": binary_name
            }
    
    def download_all_binaries(self) -> Dict[str, Any]:
        """Download all available binaries."""
        print("🚀 Starting Real Binary Downloads")
        print("=" * 60)
        
        results = {}
        total_start = time.time()
        
        for binary_name in self.binary_sources.keys():
            result = self.download_binary(binary_name)
            results[binary_name] = result
            print()  # Add spacing between downloads
        
        total_time = time.time() - total_start
        
        # Summary
        successful = sum(1 for r in results.values() if r["success"])
        total = len(results)
        total_size = sum(r.get("file_size", 0) for r in results.values() if r["success"])
        
        print("=" * 60)
        print("📊 DOWNLOAD SUMMARY")
        print("=" * 60)
        print(f"✅ Successful: {successful}/{total} binaries")
        print(f"📦 Total size: {total_size:,} bytes ({total_size/1024/1024:.1f} MB)")
        print(f"⏱️ Total time: {total_time:.2f} seconds")
        print(f"📈 Average speed: {total_size/total_time/1024/1024:.1f} MB/s")
        print()
        
        # Individual results
        for binary_name, result in results.items():
            if result["success"]:
                print(f"✅ {binary_name}: {result['filename']} ({result['file_size']:,} bytes)")
                print(f"   Blake3: {result['hashes']['blake3'][:32]}...")
                print(f"   SHA256: {result['hashes']['sha256'][:32]}...")
            else:
                print(f"❌ {binary_name}: {result['error']}")
        
        return {
            "results": results,
            "summary": {
                "successful": successful,
                "total": total,
                "total_size": total_size,
                "total_time": total_time,
                "download_dir": self.download_dir
            }
        }

def main():
    """Main function to download binaries."""
    downloader = BinaryDownloader()
    results = downloader.download_all_binaries()
    
    # Save results to file
    import json
    results_file = f"/tmp/binary_downloads_{int(time.time())}.json"
    
    # Convert results to JSON-serializable format
    json_results = {}
    for binary_name, result in results["results"].items():
        json_results[binary_name] = {
            k: v for k, v in result.items() 
            if k not in ["created_time", "modified_time"]  # Skip non-serializable timestamps
        }
    
    with open(results_file, "w") as f:
        json.dump({
            "results": json_results,
            "summary": results["summary"]
        }, f, indent=2)
    
    print(f"\n📄 Results saved to: {results_file}")
    
    return results

if __name__ == "__main__":
    main()
