#!/bin/bash

# TurdParty API Service Entrypoint with Dependency Checks
# This script performs health checks before starting the API service

set -e

echo "🚀 Starting TurdParty API service with volume-mounted code..."

# Ensure scripts are executable (needed for volume mounts)
if [ -d "/app/scripts" ]; then
    chmod +x /app/scripts/*.sh 2>/dev/null || true
    echo "✅ Script permissions updated"
fi

# Function to check if a service is available
check_service() {
    local service_name=$1
    local host=$2
    local port=$3
    local max_attempts=30
    local attempt=1

    echo "Checking $service_name at $host:$port..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z "$host" "$port" 2>/dev/null; then
            echo "✅ $service_name is available"
            return 0
        fi
        
        echo "⏳ Waiting for $service_name... (attempt $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service_name is not available after $max_attempts attempts"
    return 1
}

# Check required services
echo "🔍 Performing dependency checks..."

# Check database
if ! check_service "PostgreSQL Database" "database" "5432"; then
    echo "⚠️  Database not available, but continuing..."
fi

# Check Redis
if ! check_service "Redis" "redis" "6379"; then
    echo "⚠️  Redis not available, but continuing..."
fi

# Check Elasticsearch
if ! check_service "Elasticsearch" "elasticsearch" "9200"; then
    echo "⚠️  Elasticsearch not available, but continuing..."
fi

echo "✅ Dependency checks completed"

# Set Python path to include the API service directory
export PYTHONPATH=/app/services/api:/app

# Start the API service
echo "🚀 Starting FastAPI application..."
cd /app/services/api
exec python -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload
