#!/usr/bin/env python3
"""
Sphinx RST Formatting Fix Utility

This utility fixes common RST (reStructuredText) formatting issues in Sphinx
documentation reports and ensures proper navigation structure. It addresses
formatting problems that prevent proper HTML generation and improves document
readability and navigation.

Key Features:
    - Automatic RST formatting issue detection and correction
    - List item spacing and indentation fixes
    - Definition list formatting improvements
    - Blank line normalization for proper document structure
    - Security features section formatting corrections
    - Batch processing of multiple RST files

Common Issues Fixed:
    1. **List Item Spacing**: Ensures proper spacing after list headers
    2. **Definition Lists**: Corrects indentation and spacing for definition lists
    3. **Section Headers**: Fixes spacing around section headers
    4. **Blank Line Management**: Normalizes blank lines for proper RST parsing
    5. **Special Sections**: Handles security features and other special sections

RST Formatting Rules Applied:
    - List items with headers get proper spacing
    - Definition lists maintain consistent indentation
    - Section headers have appropriate blank line separation
    - Special formatting sections are properly spaced
    - Overall document structure follows RST best practices

Usage:
    python scripts/fix-sphinx-rst-formatting.py

    Processes all RST files in the sphinx-reports directory and applies
    formatting fixes to ensure proper Sphinx HTML generation.

Integration:
    - Works with existing Sphinx documentation build process
    - Compatible with Nix shell environment for documentation building
    - Supports batch processing of multiple documentation files
    - Maintains original content while fixing formatting issues
"""

import os
import sys
from pathlib import Path

def fix_rst_file(file_path):
    """
    Fix RST formatting issues in a single file.

    Applies comprehensive formatting fixes including list spacing,
    definition list formatting, section header spacing, and blank
    line normalization to ensure proper RST parsing.

    Args:
        file_path: Path to the RST file to fix
    """
    print(f"🔧 Fixing {file_path.name}...")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Fix the main issues:
    # 1. Add proper spacing after list items
    # 2. Fix indentation issues
    # 3. Ensure proper blank lines
    
    lines = content.split('\n')
    fixed_lines = []
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Fix list items that need proper spacing
        if line.strip().startswith('- **') and line.strip().endswith(':'):
            fixed_lines.append(line)
            # Add blank line after list header
            if i + 1 < len(lines) and not lines[i + 1].strip() == '':
                fixed_lines.append('')
        
        # Fix definition list items
        elif line.strip().startswith(':') and ':' in line[1:]:
            fixed_lines.append(line)
            # Ensure proper spacing after definition lists
            if i + 1 < len(lines) and lines[i + 1].strip() and not lines[i + 1].startswith(':'):
                fixed_lines.append('')
        
        # Fix security features section
        elif '**Security Features:**' in line:
            fixed_lines.append(line)
            fixed_lines.append('')  # Add blank line
        
        else:
            fixed_lines.append(line)
        
        i += 1
    
    # Write fixed content
    with open(file_path, 'w') as f:
        f.write('\n'.join(fixed_lines))
    
    print(f"   ✅ Fixed {file_path.name}")

def regenerate_reports():
    """
    Regenerate all reports with fixed formatting.

    Processes all RST files in the sphinx-reports directory,
    applying formatting fixes to ensure proper Sphinx HTML generation.

    Returns:
        bool: True if all files were processed successfully, False otherwise
    """
    sphinx_dir = Path("sphinx-reports")
    
    if not sphinx_dir.exists():
        print("❌ sphinx-reports directory not found!")
        return False
    
    # Fix all RST files
    rst_files = list(sphinx_dir.glob("*.rst"))
    
    for rst_file in rst_files:
        if rst_file.name != "index.rst":  # Skip index for now
            fix_rst_file(rst_file)
    
    print(f"\n🎉 Fixed {len(rst_files)-1} RST files!")
    return True

def main():
    """
    Main execution function.

    Orchestrates the RST formatting fix process and provides
    user feedback on the results and next steps.

    Returns:
        int: Exit code (0 for success, 1 for failure)
    """
    print("🔧 Fixing Sphinx RST formatting issues...")
    
    if regenerate_reports():
        print("\n✅ All RST files have been fixed!")
        print("\n🔧 Now rebuild with:")
        print("   cd sphinx-reports")
        print("   nix-shell -p gnumake -p python311 -p python311Packages.sphinx --run 'make clean && make html'")
        return 0
    else:
        print("❌ Failed to fix RST files")
        return 1

if __name__ == "__main__":
    exit(main())
