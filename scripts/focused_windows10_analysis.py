#!/usr/bin/env python3
"""
Focused Windows 10 VM Analysis

Analyzes available executable files via Windows 10 VMs with comprehensive
pre/post install logging analysis and detailed reporting.
"""

import json
import subprocess
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional


class FocusedWindows10Analyzer:
    """Focused Windows 10 VM analysis for available executables."""
    
    def __init__(self):
        self.api_base = "http://api.turdparty.localhost/api/v1"
        self.test_vms = []
        self.analysis_results = []
        self.start_time = datetime.now()
        
    def http_request(self, method: str, endpoint: str, data: Dict = None) -> Dict[str, Any]:
        """Make HTTP request using curl."""
        url = f"{self.api_base}{endpoint}"
        
        if method.upper() == "GET":
            cmd = ["curl", "-s", url]
        elif method.upper() == "POST":
            cmd = ["curl", "-s", "-X", "POST", "-H", "Content-Type: application/json"]
            if data:
                cmd.extend(["-d", json.dumps(data)])
            cmd.append(url)
        elif method.upper() == "DELETE":
            cmd = ["curl", "-s", "-X", "DELETE", url]
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            if result.returncode == 0 and result.stdout.strip():
                try:
                    return {"success": True, "data": json.loads(result.stdout)}
                except json.JSONDecodeError:
                    return {"success": True, "data": {"raw": result.stdout}}
            else:
                return {"success": False, "error": result.stderr or "Request failed"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_celery_logs(self, filter_text: str = None) -> List[str]:
        """Get recent Celery worker logs."""
        try:
            cmd = ["docker", "logs", "turdpartycollab_celery_worker", "--tail", "50"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                if filter_text:
                    lines = [line for line in lines if filter_text in line]
                return lines
            else:
                return []
        except Exception:
            return []
    
    def get_executable_files(self) -> List[Dict[str, Any]]:
        """Get available executable files for analysis."""
        response = self.http_request("GET", "/files/")
        if not response["success"]:
            return []
        
        files_data = response["data"]
        if isinstance(files_data, dict) and "files" in files_data:
            files = files_data["files"]
        else:
            files = files_data
        
        # Filter for executable files
        executable_files = []
        for file_info in files:
            filename = file_info.get("filename", "").lower()
            file_size = file_info.get("file_size", 0)
            
            # Look for executables and prioritize larger files (likely real software)
            if any(ext in filename for ext in [".exe", ".msi", ".bat", ".cmd"]):
                executable_files.append(file_info)
        
        # Sort by file size (larger files first, likely real installers)
        executable_files.sort(key=lambda x: x.get("file_size", 0), reverse=True)
        
        return executable_files
    
    def capture_pre_install_logs(self, vm_id: str, vm_name: str) -> Dict[str, Any]:
        """Capture pre-install log baseline."""
        print(f"  📊 Capturing pre-install log baseline for {vm_name}")
        
        baseline_time = datetime.now()
        
        # Get current Celery logs as baseline
        baseline_logs = self.get_celery_logs()
        
        baseline = {
            "timestamp": baseline_time.isoformat(),
            "vm_id": vm_id,
            "vm_name": vm_name,
            "baseline_type": "pre_install",
            "celery_log_count": len(baseline_logs),
            "last_log_entries": baseline_logs[-5:] if baseline_logs else []
        }
        
        print(f"    ✅ Pre-install baseline: {len(baseline_logs)} log entries")
        return baseline
    
    def capture_post_install_logs(self, vm_id: str, vm_name: str, injection_id: str, 
                                 baseline_time: datetime) -> Dict[str, Any]:
        """Capture post-install log analysis."""
        print(f"  📊 Capturing post-install logs for {vm_name}")
        
        analysis_time = datetime.now()
        
        # Get logs related to this injection
        injection_logs = self.get_celery_logs(injection_id)
        vm_logs = self.get_celery_logs(vm_name)
        all_recent_logs = self.get_celery_logs()
        
        # Analyze log patterns
        grpc_logs = [log for log in all_recent_logs if "grpc" in log.lower()]
        injection_method_logs = [log for log in all_recent_logs if 
                               "Using Docker injection" in log or "Using gRPC injection" in log]
        error_logs = [log for log in all_recent_logs if "ERROR" in log or "Failed" in log]
        
        post_analysis = {
            "timestamp": analysis_time.isoformat(),
            "vm_id": vm_id,
            "vm_name": vm_name,
            "injection_id": injection_id,
            "analysis_type": "post_install",
            "log_analysis": {
                "injection_specific_logs": len(injection_logs),
                "vm_specific_logs": len(vm_logs),
                "total_recent_logs": len(all_recent_logs),
                "grpc_related_logs": len(grpc_logs),
                "injection_method_logs": injection_method_logs,
                "error_logs": error_logs,
                "sample_injection_logs": injection_logs[:10],
                "sample_vm_logs": vm_logs[:10]
            },
            "analysis_duration": str(analysis_time - baseline_time)
        }
        
        print(f"    ✅ Post-install analysis: {len(injection_logs)} injection logs, {len(vm_logs)} VM logs")
        return post_analysis
    
    def analyze_single_executable(self, file_info: Dict[str, Any], vm_id: str, vm_name: str) -> Dict[str, Any]:
        """Analyze a single executable on Windows 10 VM."""
        file_id = file_info["file_id"]
        filename = file_info["filename"]
        file_size = file_info.get("file_size", 0)
        
        print(f"\n🔬 Analyzing executable: {filename}")
        print(f"   File ID: {file_id}")
        print(f"   File Size: {file_size:,} bytes")
        print(f"   VM: {vm_name} ({vm_id})")
        
        analysis_start = datetime.now()
        
        # 1. Capture pre-install baseline
        pre_baseline = self.capture_pre_install_logs(vm_id, vm_name)
        
        # 2. Inject and execute the file
        print(f"  💉 Injecting and executing {filename}")
        
        # Use Windows path for executable
        target_path = f"C:\\TurdParty\\{filename}"
        if filename.lower().endswith('.exe'):
            target_path = f"C:\\TurdParty\\{filename}"
        elif filename.lower().endswith('.msi'):
            target_path = f"C:\\TurdParty\\{filename}"
        else:
            target_path = f"C:\\TurdParty\\{filename}"
        
        injection_config = {
            "file_id": file_id,
            "injection_path": target_path,
            "execute_after_injection": True,
            "permissions": "0755"
        }
        
        response = self.http_request("POST", f"/vms/{vm_id}/inject", injection_config)
        if not response["success"]:
            return {
                "file_info": file_info,
                "status": "injection_failed",
                "error": f"Injection failed: {response['error']}",
                "analysis_time": analysis_start.isoformat()
            }
        
        injection_data = response["data"]
        injection_id = injection_data["injection_id"]
        
        print(f"    ✅ Injection queued: {injection_id}")
        
        # 3. Wait for injection to complete
        print(f"  ⏳ Waiting for injection to complete...")
        injection_completed = False
        injection_status_data = None
        timeout = time.time() + 300  # 5 minute timeout
        
        while time.time() < timeout:
            response = self.http_request("GET", f"/vms/{vm_id}/injections/{injection_id}")
            if response["success"]:
                injection_status_data = response["data"]
                status = injection_status_data.get("status")
                print(f"    📊 Injection status: {status}")
                
                if status in ["MONITORING", "COMPLETED"]:
                    injection_completed = True
                    print(f"    ✅ Injection completed with status: {status}")
                    break
                elif status == "FAILED":
                    print(f"    ❌ Injection failed: {injection_status_data.get('error_message')}")
                    break
            time.sleep(15)  # Check every 15 seconds
        
        # 4. Capture post-install analysis regardless of completion status
        post_analysis = self.capture_post_install_logs(vm_id, vm_name, injection_id, analysis_start)
        
        # 5. Compile comprehensive analysis
        analysis_end = datetime.now()
        
        analysis_result = {
            "file_info": file_info,
            "vm_id": vm_id,
            "vm_name": vm_name,
            "injection_id": injection_id,
            "status": "completed" if injection_completed else "timeout_or_failed",
            "analysis_timeline": {
                "start_time": analysis_start.isoformat(),
                "end_time": analysis_end.isoformat(),
                "duration": str(analysis_end - analysis_start)
            },
            "pre_install_baseline": pre_baseline,
            "post_install_analysis": post_analysis,
            "injection_status": injection_status_data,
            "execution_summary": {
                "injection_successful": injection_completed,
                "execution_duration": str(analysis_end - analysis_start),
                "target_path": target_path,
                "file_size_mb": round(file_size / 1024 / 1024, 2),
                "vm_type": "Windows 10 (Vagrant)",
                "injection_method": "gRPC with SSH fallback"
            }
        }
        
        print(f"  ✅ Analysis completed for {filename}")
        return analysis_result
    
    def create_windows10_vm(self) -> Optional[tuple]:
        """Create a Windows 10 VM for analysis."""
        vm_name = f"win10-exec-analysis-{uuid.uuid4().hex[:8]}"
        print(f"🏠 Creating Windows 10 VM: {vm_name}")
        
        vm_config = {
            "name": vm_name,
            "template": "10Baht/windows10-turdparty",
            "vm_type": "vagrant",
            "memory_mb": 4096,
            "cpus": 2,
            "disk_gb": 50,
            "description": f"Windows 10 VM for executable analysis - {vm_name}"
        }
        
        response = self.http_request("POST", "/vms/", vm_config)
        if not response["success"]:
            print(f"  ❌ VM creation failed: {response['error']}")
            return None
        
        vm_data = response["data"]
        vm_id = vm_data["vm_id"]
        self.test_vms.append(vm_id)
        
        print(f"  ✅ VM created: {vm_id}")
        
        # Wait for VM to be ready
        print(f"  ⏳ Waiting for VM to be ready...")
        timeout = time.time() + 600  # 10 minute timeout for Windows VM
        
        while time.time() < timeout:
            response = self.http_request("GET", f"/vms/{vm_id}")
            if response["success"]:
                vm_status = response["data"]
                status = vm_status.get("status")
                print(f"    📊 VM status: {status}")
                
                if status == "running":
                    ip_address = vm_status.get("ip_address", "N/A")
                    print(f"  ✅ VM is ready: {ip_address}")
                    return vm_id, vm_name
            time.sleep(20)  # Check every 20 seconds
        
        print(f"  ❌ VM failed to reach ready state within timeout")
        return None
    
    def cleanup_resources(self):
        """Clean up test VMs."""
        print("\n🧹 Cleaning up test resources...")
        for vm_id in self.test_vms:
            try:
                self.http_request("DELETE", f"/vms/{vm_id}")
                print(f"  ✅ Cleaned up VM: {vm_id}")
            except Exception:
                pass
        self.test_vms.clear()
    
    def generate_analysis_report(self) -> str:
        """Generate comprehensive analysis report."""
        report = "\n" + "="*100 + "\n"
        report += "🎯 FOCUSED WINDOWS 10 VM EXECUTABLE ANALYSIS REPORT\n"
        report += "="*100 + "\n"
        
        report += f"\n📊 ANALYSIS SUMMARY:\n"
        report += f"  Analysis Start Time: {self.start_time.isoformat()}\n"
        report += f"  Total Executables Analyzed: {len(self.analysis_results)}\n"
        
        successful_analyses = [r for r in self.analysis_results if r.get("status") == "completed"]
        failed_analyses = [r for r in self.analysis_results if r.get("status") != "completed"]
        
        report += f"  Successful Analyses: {len(successful_analyses)}\n"
        report += f"  Failed/Timeout Analyses: {len(failed_analyses)}\n"
        
        if self.analysis_results:
            report += f"  Success Rate: {len(successful_analyses)/len(self.analysis_results)*100:.1f}%\n"
        
        report += f"\n📋 DETAILED EXECUTABLE ANALYSIS:\n"
        report += "-" * 80 + "\n"
        
        for i, result in enumerate(self.analysis_results, 1):
            file_info = result.get("file_info", {})
            filename = file_info.get("filename", "unknown")
            file_size = file_info.get("file_size", 0)
            status = result.get("status", "unknown")
            
            report += f"\n{i}. {filename}\n"
            report += f"   File Size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)\n"
            report += f"   Status: {status}\n"
            report += f"   File ID: {file_info.get('file_id', 'N/A')}\n"
            
            if "execution_summary" in result:
                summary = result["execution_summary"]
                report += f"   Target Path: {summary.get('target_path', 'N/A')}\n"
                report += f"   Injection Method: {summary.get('injection_method', 'N/A')}\n"
                report += f"   Duration: {summary.get('execution_duration', 'N/A')}\n"
                report += f"   Execution: {'✅' if summary.get('injection_successful') else '❌'}\n"
            
            if "post_install_analysis" in result:
                log_analysis = result["post_install_analysis"].get("log_analysis", {})
                report += f"   Log Entries: {log_analysis.get('injection_specific_logs', 0)} injection, {log_analysis.get('vm_specific_logs', 0)} VM\n"
                
                injection_methods = log_analysis.get("injection_method_logs", [])
                if injection_methods:
                    for method_log in injection_methods:
                        if "Using gRPC injection" in method_log:
                            report += f"   Method: ✅ gRPC injection detected\n"
                        elif "Using Docker injection" in method_log:
                            report += f"   Method: ✅ Docker injection detected\n"
            
            if status != "completed":
                report += f"   Error: {result.get('error', 'N/A')}\n"
            
            report += "-" * 50 + "\n"
        
        return report

    def run_focused_analysis(self) -> bool:
        """Run focused analysis of available executables."""
        print("🚀 Starting Focused Windows 10 VM Executable Analysis")
        print("=" * 100)

        try:
            # 1. Get available executable files
            print("\n📁 Getting available executable files...")
            executable_files = self.get_executable_files()

            if not executable_files:
                print("❌ No executable files found for analysis")
                return False

            print(f"✅ Found {len(executable_files)} executable files:")
            for i, file_info in enumerate(executable_files, 1):
                filename = file_info.get("filename", "unknown")
                file_size = file_info.get("file_size", 0)
                print(f"  {i}. {filename} ({file_size:,} bytes)")

            # 2. Create Windows 10 VM
            vm_result = self.create_windows10_vm()
            if not vm_result:
                print("❌ Failed to create Windows 10 VM")
                return False

            vm_id, vm_name = vm_result

            # 3. Analyze each executable
            print(f"\n🔬 Starting analysis of {len(executable_files)} executables...")

            for i, file_info in enumerate(executable_files, 1):
                filename = file_info.get("filename", f"file_{i}")
                print(f"\n{'='*80}")
                print(f"📦 ANALYZING EXECUTABLE {i}/{len(executable_files)}: {filename}")
                print(f"{'='*80}")

                try:
                    result = self.analyze_single_executable(file_info, vm_id, vm_name)
                    self.analysis_results.append(result)

                    if result.get("status") == "completed":
                        print(f"✅ Analysis {i} completed successfully")
                    else:
                        print(f"⚠️ Analysis {i} completed with issues: {result.get('error', 'See details')}")

                except Exception as e:
                    print(f"❌ Analysis {i} error: {e}")
                    self.analysis_results.append({
                        "file_info": file_info,
                        "status": "error",
                        "error": str(e),
                        "analysis_time": datetime.now().isoformat()
                    })

                # Brief pause between analyses to let system settle
                if i < len(executable_files):
                    print(f"⏳ Pausing 45 seconds before next analysis...")
                    time.sleep(45)

            # 4. Generate comprehensive report
            print(f"\n📊 Generating comprehensive analysis report...")
            report = self.generate_analysis_report()

            # Save report to file
            report_file = f"windows10_executable_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_file, "w") as f:
                f.write(report)

            print(report)
            print(f"\n📄 Report saved to: {report_file}")

            # 5. Summary
            successful = len([r for r in self.analysis_results if r.get("status") == "completed"])
            total = len(self.analysis_results)

            print(f"\n🎯 ANALYSIS COMPLETE!")
            print(f"   Total Executables: {total}")
            print(f"   Successful: {successful}")
            print(f"   Failed/Timeout: {total - successful}")
            if total > 0:
                print(f"   Success Rate: {successful/total*100:.1f}%")

            return total > 0

        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            import traceback
            traceback.print_exc()
            return False


if __name__ == "__main__":
    analyzer = FocusedWindows10Analyzer()

    try:
        success = analyzer.run_focused_analysis()
        exit_code = 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Analysis interrupted by user")
        exit_code = 130
    except Exception as e:
        print(f"\n❌ Analysis suite failed: {e}")
        import traceback
        traceback.print_exc()
        exit_code = 1
    finally:
        analyzer.cleanup_resources()

    exit(exit_code)
