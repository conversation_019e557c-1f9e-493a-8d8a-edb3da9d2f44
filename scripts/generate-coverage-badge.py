#!/usr/bin/env python3
"""
Coverage Badge Generator for TurdParty

This script extracts coverage information from the latest coverage XML file
and generates a coverage badge URL for use in README.md.
"""

import xml.etree.ElementTree as ET
from pathlib import Path
import sys
import re


def extract_coverage_from_xml(xml_path: Path) -> float:
    """Extract coverage percentage from coverage XML file."""
    try:
        tree = ET.parse(xml_path)
        root = tree.getroot()
        
        # Get line-rate from coverage element
        line_rate = float(root.get('line-rate', 0))
        coverage_percent = line_rate * 100
        
        return round(coverage_percent, 1)
    except Exception as e:
        print(f"Error parsing coverage XML: {e}")
        return 0.0


def get_badge_color(coverage: float) -> str:
    """Get badge color based on coverage percentage."""
    if coverage >= 80:
        return "brightgreen"
    elif coverage >= 60:
        return "yellow"
    elif coverage >= 40:
        return "orange"
    else:
        return "red"


def generate_badge_url(coverage: float) -> str:
    """Generate shields.io badge URL for coverage."""
    color = get_badge_color(coverage)
    # Format coverage to remove unnecessary decimals
    coverage_str = f"{coverage:.1f}".rstrip('0').rstrip('.')
    
    return f"https://img.shields.io/badge/Coverage-{coverage_str}%25-{color}"


def update_readme_badge(readme_path: Path, badge_url: str) -> bool:
    """Update the coverage badge in README.md."""
    try:
        with open(readme_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Pattern to match existing coverage badge
        coverage_pattern = r'\[\!\[Coverage\]\([^\)]+\)\]\([^\)]+\)'
        badge_markdown = f"[![Coverage]({badge_url})](tests/coverage/latest/)"
        
        if re.search(coverage_pattern, content):
            # Replace existing badge
            new_content = re.sub(coverage_pattern, badge_markdown, content)
        else:
            # Look for the badge section and add coverage badge
            badge_section_pattern = r'(\[\!\[Tests\]\([^\)]+\)\]\([^\)]+\))'
            if re.search(badge_section_pattern, content):
                # Add after Tests badge
                new_content = re.sub(
                    badge_section_pattern,
                    r'\1\n' + badge_markdown,
                    content
                )
            else:
                # Add at the beginning of the status section
                status_pattern = r'(## 🎉 Current Status: Production Ready)'
                if re.search(status_pattern, content):
                    new_content = re.sub(
                        status_pattern,
                        badge_markdown + '\n\n' + r'\1',
                        content
                    )
                else:
                    print("Could not find suitable location to insert coverage badge")
                    return False
        
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        return True
    except Exception as e:
        print(f"Error updating README: {e}")
        return False


def main():
    """Main function."""
    project_root = Path(__file__).parent.parent
    coverage_xml = project_root / "tests" / "coverage" / "latest" / "coverage.xml"
    readme_path = project_root / "README.md"
    
    if not coverage_xml.exists():
        print(f"Coverage XML file not found: {coverage_xml}")
        sys.exit(1)
    
    if not readme_path.exists():
        print(f"README.md not found: {readme_path}")
        sys.exit(1)
    
    # Extract coverage
    coverage = extract_coverage_from_xml(coverage_xml)
    print(f"📊 Current coverage: {coverage}%")
    
    # Generate badge URL
    badge_url = generate_badge_url(coverage)
    print(f"🏷️ Badge URL: {badge_url}")
    
    # Update README
    if update_readme_badge(readme_path, badge_url):
        print(f"✅ README.md updated with coverage badge")
    else:
        print(f"❌ Failed to update README.md")
        sys.exit(1)


if __name__ == "__main__":
    main()
