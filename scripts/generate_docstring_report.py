#!/usr/bin/env python3
"""
Generate Docstring Coverage Report for TurdParty Project

This script creates a summary report of Python files that need docstring improvements,
organized by priority and with actionable recommendations.
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime


def load_analysis_report(report_path: str = "reports/docstring_analysis.json") -> Dict[str, Any]:
    """Load the docstring analysis report."""
    try:
        with open(report_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: Report file {report_path} not found.")
        print("Please run 'python scripts/docstring_analyzer.py' first.")
        sys.exit(1)


def generate_summary_report(report_data: Dict[str, Any]) -> str:
    """Generate a human-readable summary report."""
    summary = report_data['summary']
    issues = report_data['issue_counts']
    
    report = f"""
# TurdParty Docstring Coverage Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Executive Summary
- **Overall Coverage**: {summary['overall_coverage_percentage']:.1f}%
- **Files Analyzed**: {summary['total_files_analyzed']}
- **Total Issues**: {issues['total']}
- **Files Needing Critical Attention**: {summary['files_with_critical_issues']}
- **Files Needing Improvement**: {summary['files_needing_improvement']}
- **Well-Documented Files**: {summary['files_with_good_coverage']}

## Issue Breakdown by Severity
- **High Priority**: {issues['high']} issues ({issues['high']/issues['total']*100:.1f}%)
- **Medium Priority**: {issues['medium']} issues ({issues['medium']/issues['total']*100:.1f}%)
- **Critical Priority**: {issues['critical']} issues
- **Low Priority**: {issues['low']} issues

## Files Requiring Immediate Attention (0-50% Coverage)
"""
    
    critical_files = report_data['files_by_category']['critical_coverage']
    for i, file_info in enumerate(critical_files, 1):
        report += f"\n{i}. **{file_info['file_path']}** ({file_info['coverage_percentage']:.1f}% coverage)\n"
        report += f"   - Issues: {len(file_info['issues'])}\n"
        
        # Show top 3 issues for each file
        for issue in file_info['issues'][:3]:
            report += f"   - Line {issue['line_number']}: {issue['element_name']} - {issue['issue_description']}\n"
        
        if len(file_info['issues']) > 3:
            report += f"   - ... and {len(file_info['issues']) - 3} more issues\n"
    
    report += "\n## Files Needing Improvement (50-80% Coverage)\n"
    
    improvement_files = report_data['files_by_category']['needs_improvement'][:10]  # Show top 10
    for i, file_info in enumerate(improvement_files, 1):
        report += f"\n{i}. **{file_info['file_path']}** ({file_info['coverage_percentage']:.1f}% coverage)\n"
        report += f"   - Issues: {len(file_info['issues'])}\n"
        
        # Show top 2 issues for each file
        for issue in file_info['issues'][:2]:
            report += f"   - Line {issue['line_number']}: {issue['element_name']} - {issue['issue_description']}\n"
    
    if len(improvement_files) < len(report_data['files_by_category']['needs_improvement']):
        remaining = len(report_data['files_by_category']['needs_improvement']) - len(improvement_files)
        report += f"\n... and {remaining} more files needing improvement\n"
    
    report += f"""
## Recommendations

### Immediate Actions (This Week)
1. **Add module docstrings** to all `__init__.py` files ({len([f for f in critical_files if f['file_path'].endswith('__init__.py')])} files)
2. **Document core service methods** in `api/services/vm_service.py`
3. **Add WebSocket endpoint documentation** in `services/api/src/main.py`

### Short-term Goals (Next 2 Weeks)
1. **Complete exception class documentation** in `api/exceptions/api_exceptions.py`
2. **Document API route handlers** with proper parameter and return descriptions
3. **Add comprehensive docstrings** to data model classes

### Quality Improvements
1. **Enhance existing brief docstrings** with detailed descriptions
2. **Add usage examples** to complex functions
3. **Implement automated docstring quality checks** in CI/CD

## Next Steps
1. Run `python scripts/docstring_analyzer.py` to regenerate this report
2. Use the detailed issue list in `docs/prd/Docstring-Issues-Detailed-List.md`
3. Follow the implementation plan in `docs/prd/PRD-Docstring-Coverage-Improvement.md`

---
*Report generated by TurdParty Docstring Analyzer*
"""
    
    return report


def generate_file_list(report_data: Dict[str, Any]) -> str:
    """Generate a simple list of files that need attention."""
    critical_files = [f['file_path'] for f in report_data['files_by_category']['critical_coverage']]
    improvement_files = [f['file_path'] for f in report_data['files_by_category']['needs_improvement']]
    
    file_list = "# Python Files Needing Docstring Improvements\n\n"
    file_list += "## Critical Priority (0-50% Coverage)\n"
    for file_path in critical_files:
        file_list += f"- {file_path}\n"
    
    file_list += "\n## High Priority (50-80% Coverage)\n"
    for file_path in improvement_files:
        file_list += f"- {file_path}\n"
    
    file_list += f"\n**Total files needing attention**: {len(critical_files) + len(improvement_files)}\n"
    
    return file_list


def main():
    """Main entry point for report generation."""
    print("📊 Generating TurdParty Docstring Coverage Report...")
    
    # Load analysis data
    report_data = load_analysis_report()
    
    # Generate summary report
    summary_report = generate_summary_report(report_data)
    
    # Save summary report
    summary_path = Path("reports/docstring_coverage_summary.md")
    summary_path.parent.mkdir(exist_ok=True)
    
    with open(summary_path, 'w') as f:
        f.write(summary_report)
    
    # Generate file list
    file_list = generate_file_list(report_data)
    
    # Save file list
    list_path = Path("reports/files_needing_docstrings.md")
    with open(list_path, 'w') as f:
        f.write(file_list)
    
    print(f"✅ Reports generated:")
    print(f"   📄 Summary: {summary_path}")
    print(f"   📋 File List: {list_path}")
    print(f"   📊 Detailed Analysis: reports/docstring_analysis.json")
    
    # Print quick summary to console
    summary = report_data['summary']
    print(f"\n📈 Quick Summary:")
    print(f"   Coverage: {summary['overall_coverage_percentage']:.1f}%")
    print(f"   Files analyzed: {summary['total_files_analyzed']}")
    print(f"   Issues found: {report_data['issue_counts']['total']}")
    print(f"   Critical files: {summary['files_with_critical_issues']}")
    print(f"   Files needing improvement: {summary['files_needing_improvement']}")


if __name__ == "__main__":
    main()
