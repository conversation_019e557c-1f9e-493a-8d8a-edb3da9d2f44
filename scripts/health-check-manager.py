#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 Health Check Manager

Comprehensive dependency-aware health check system for TurdParty services.
Provides intelligent health monitoring with dependency validation, circular
dependency detection, and comprehensive status reporting.

Key Features:
    - Dependency-aware health checking (checks dependencies first)
    - Circular dependency detection and validation
    - Docker container health status integration
    - Topological sorting for optimal check ordering
    - Comprehensive status reporting with visual indicators
    - JSON output mode for programmatic integration
    - Dependency graph validation and visualization

Service Dependencies:
    - Core Infrastructure: database, redis, storage (MinIO)
    - ELK Stack: elasticsearch, logstash, kibana, filebeat
    - Application Services: api, celery workers, frontend
    - Monitoring: vm_monitor, status, docs

Health Status Types:
    - HEALTHY: Service is running and responsive
    - STARTING: Service is in startup phase
    - UNHEALTHY: Service is running but not responsive
    - DEPENDENCY_FAILED: Service dependencies are not healthy
    - UNKNOWN: Service status cannot be determined

Usage:
    python scripts/health-check-manager.py              # Human-readable report
    python scripts/health-check-manager.py --json       # JSON output
    python scripts/health-check-manager.py --validate   # Dependency validation

Output Modes:
    - Default: Colorful status report with dependency information
    - JSON: Machine-readable output for monitoring systems
    - Validate: Dependency graph validation with circular dependency detection

Integration:
    - Docker container inspection for health status
    - Dependency graph validation for deployment safety
    - Monitoring system integration via JSON output
    - CI/CD pipeline integration for health validation
"""

import json
import subprocess
import time
import sys
from typing import Dict, List, Set, Optional
from dataclasses import dataclass
from enum import Enum


class HealthStatus(Enum):
    """
    Enumeration of possible health status values for services.

    Provides standardized status values for consistent health reporting
    across all TurdParty services and components.
    """
    UNKNOWN = "unknown"
    STARTING = "starting"
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEPENDENCY_FAILED = "dependency_failed"


@dataclass
class ServiceHealth:
    """
    Data class representing the health status of a service.

    Contains comprehensive health information including status,
    dependencies, timing, and error details for monitoring and reporting.

    Attributes:
        name: Service name identifier
        status: Current health status from HealthStatus enum
        dependencies: List of service names this service depends on
        last_check: Unix timestamp of last health check
        error_message: Optional error message if status is unhealthy
    """
    name: str
    status: HealthStatus
    dependencies: List[str]
    last_check: float
    error_message: Optional[str] = None


class HealthCheckManager:
    """
    Manages dependency-aware health checks for TurdParty services.

    Provides comprehensive health monitoring with intelligent dependency
    checking, circular dependency detection, and Docker integration.
    Ensures services are checked in proper dependency order.
    """
    
    def __init__(self, silent: bool = False):
        """
        Initialize the health check manager.

        Args:
            silent: If True, suppress validation warnings and output
        """
        self.services: Dict[str, ServiceHealth] = {}
        self.dependency_graph = self._build_dependency_graph()
        self.check_interval = 5  # seconds
        self.silent = silent

        # Validate dependency graph for circular dependencies (only if not silent)
        if not silent:
            circular_deps = self._detect_circular_dependencies()
            if circular_deps:
                print(f"⚠️  WARNING: Circular dependencies detected: {circular_deps}")
                print("This may cause health check deadlocks!")
            else:
                print("✅ No circular dependencies detected in service graph")
        
    def _build_dependency_graph(self) -> Dict[str, List[str]]:
        """
        Define service dependencies for the TurdParty platform.

        Returns:
            dict: Service dependency graph mapping service names to their dependencies
        """
        return {
            # Core infrastructure (no dependencies)
            "database": [],
            "redis": [],
            "storage": [],  # MinIO
            
            # ELK Stack (depends on each other)
            "elasticsearch": [],
            "logstash": ["elasticsearch"],
            "kibana": ["elasticsearch"],
            "filebeat": ["elasticsearch", "logstash"],
            
            # Application services
            "api": ["database", "redis", "storage"],
            "celery_worker": ["database", "redis"],
            "celery_beat": ["database", "redis"],
            "celery_flower": ["redis", "celery_worker"],
            
            # Frontend services
            "frontend": ["api"],

            # TEST: Uncomment the line below to test circular dependency detection
            # "api": ["database", "redis", "storage", "frontend"],  # This would create api -> frontend -> api cycle
            "status": [],
            "docs": [],
            
            # Monitoring
            "vm_monitor": ["elasticsearch"],
        }

    def _detect_circular_dependencies(self) -> List[List[str]]:
        """
        Detect circular dependencies using DFS (Depth-First Search).
        Returns a list of circular dependency chains.
        """
        def dfs(node: str, path: List[str], visited: Set[str], rec_stack: Set[str]) -> List[List[str]]:
            """DFS helper to detect cycles"""
            if node in rec_stack:
                # Found a cycle - return the cycle path
                cycle_start = path.index(node)
                return [path[cycle_start:] + [node]]

            if node in visited:
                return []

            visited.add(node)
            rec_stack.add(node)
            path.append(node)

            cycles = []
            for dependency in self.dependency_graph.get(node, []):
                cycles.extend(dfs(dependency, path.copy(), visited, rec_stack.copy()))

            rec_stack.remove(node)
            return cycles

        all_cycles = []
        visited = set()

        for service in self.dependency_graph.keys():
            if service not in visited:
                cycles = dfs(service, [], visited, set())
                all_cycles.extend(cycles)

        return all_cycles

    def _validate_dependency_graph(self) -> Dict[str, List[str]]:
        """
        Validate the dependency graph and return validation results.
        Returns a dict with validation issues.
        """
        issues = {
            "circular_dependencies": [],
            "missing_services": [],
            "orphaned_services": [],
            "self_dependencies": []
        }

        # Check for circular dependencies
        issues["circular_dependencies"] = self._detect_circular_dependencies()

        # Check for missing services (dependencies that don't exist)
        all_services = set(self.dependency_graph.keys())
        for service, deps in self.dependency_graph.items():
            for dep in deps:
                if dep not in all_services:
                    issues["missing_services"].append(f"{service} depends on non-existent service: {dep}")

        # Check for self-dependencies
        for service, deps in self.dependency_graph.items():
            if service in deps:
                issues["self_dependencies"].append(service)

        # Check for orphaned services (services that nothing depends on and have no dependencies)
        dependent_services = set()
        for deps in self.dependency_graph.values():
            dependent_services.update(deps)

        for service in all_services:
            if (service not in dependent_services and
                len(self.dependency_graph.get(service, [])) == 0):
                issues["orphaned_services"].append(service)

        return issues
    
    def _get_docker_service_health(self, service_name: str) -> HealthStatus:
        """Get health status from Docker container inspection"""
        try:
            container_name = f"turdpartycollab_{service_name}"

            # First, check if container exists and is running
            result = subprocess.run([
                'docker', 'inspect', container_name,
                '--format', '{{.State.Running}}'
            ], capture_output=True, text=True, timeout=10)

            if result.returncode != 0:
                return HealthStatus.UNKNOWN  # Container doesn't exist

            is_running = result.stdout.strip().lower() == 'true'
            if not is_running:
                return HealthStatus.UNHEALTHY  # Container exists but not running

            # Check health status if available
            result = subprocess.run([
                'docker', 'inspect', container_name,
                '--format', '{{if .State.Health}}{{.State.Health.Status}}{{else}}no-healthcheck{{end}}'
            ], capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                health_status = result.stdout.strip().lower()

                if health_status == 'healthy':
                    return HealthStatus.HEALTHY
                elif health_status == 'unhealthy':
                    return HealthStatus.UNHEALTHY
                elif health_status == 'starting':
                    return HealthStatus.STARTING
                elif health_status == 'no-healthcheck':
                    # No health check defined, assume healthy if running
                    return HealthStatus.HEALTHY

        except (subprocess.TimeoutExpired, Exception):
            pass

        return HealthStatus.UNKNOWN
    
    def _check_dependencies_healthy(self, service_name: str) -> bool:
        """Check if all dependencies are healthy"""
        dependencies = self.dependency_graph.get(service_name, [])
        
        for dep in dependencies:
            if dep not in self.services:
                return False
            if self.services[dep].status != HealthStatus.HEALTHY:
                return False
                
        return True
    
    def check_service_health(self, service_name: str) -> ServiceHealth:
        """Check health of a single service with dependency awareness"""
        dependencies = self.dependency_graph.get(service_name, [])
        
        # Check if dependencies are healthy first
        if not self._check_dependencies_healthy(service_name):
            return ServiceHealth(
                name=service_name,
                status=HealthStatus.DEPENDENCY_FAILED,
                dependencies=dependencies,
                last_check=time.time(),
                error_message="Dependencies not healthy"
            )
        
        # Get actual health status from Docker
        docker_status = self._get_docker_service_health(service_name)
        
        return ServiceHealth(
            name=service_name,
            status=docker_status,
            dependencies=dependencies,
            last_check=time.time()
        )
    
    def check_all_services(self) -> Dict[str, ServiceHealth]:
        """Check health of all services in dependency order"""
        # Topological sort to check dependencies first
        checked = set()
        result = {}
        
        def check_recursive(service_name: str):
            if service_name in checked:
                return
                
            # Check dependencies first
            for dep in self.dependency_graph.get(service_name, []):
                if dep not in checked:
                    check_recursive(dep)
            
            # Now check this service
            health = self.check_service_health(service_name)
            result[service_name] = health
            self.services[service_name] = health
            checked.add(service_name)
        
        # Check all services
        for service in self.dependency_graph.keys():
            check_recursive(service)
            
        return result
    
    def get_service_status_summary(self) -> Dict[str, int]:
        """Get summary counts by status"""
        summary = {status.value: 0 for status in HealthStatus}
        
        for service in self.services.values():
            summary[service.status.value] += 1
            
        return summary
    
    def print_status_report(self):
        """Print a formatted status report"""
        print("\n🏥 TurdParty Health Check Report")
        print("=" * 50)
        
        # Group by status
        by_status = {}
        for service in self.services.values():
            status = service.status.value
            if status not in by_status:
                by_status[status] = []
            by_status[status].append(service)
        
        # Print each status group
        status_icons = {
            "healthy": "✅",
            "starting": "🟡",
            "unhealthy": "❌",
            "dependency_failed": "🔗",
            "unknown": "❓"
        }
        
        for status, services in by_status.items():
            if services:
                icon = status_icons.get(status, "❓")
                print(f"\n{icon} {status.upper()} ({len(services)}):")
                for service in services:
                    deps = f" (deps: {', '.join(service.dependencies)})" if service.dependencies else ""
                    error = f" - {service.error_message}" if service.error_message else ""
                    print(f"  • {service.name}{deps}{error}")


def validate_dependencies():
    """
    Validate dependency graph and print comprehensive results.

    Performs complete dependency graph validation including circular
    dependency detection, missing service validation, and dependency
    tree visualization.

    Returns:
        bool: True if dependency graph is valid, False if issues found
    """
    print("\n🔍 TurdParty Dependency Validation")
    print("=" * 50)

    manager = HealthCheckManager()
    issues = manager._validate_dependency_graph()

    has_issues = False

    # Check circular dependencies
    if issues["circular_dependencies"]:
        has_issues = True
        print("\n🔄 CIRCULAR DEPENDENCIES DETECTED:")
        for i, cycle in enumerate(issues["circular_dependencies"], 1):
            cycle_str = " → ".join(cycle)
            print(f"  {i}. {cycle_str}")
        print("\n   ⚠️  These cycles can cause health check deadlocks!")

    # Check missing services
    if issues["missing_services"]:
        has_issues = True
        print("\n❌ MISSING SERVICE DEPENDENCIES:")
        for issue in issues["missing_services"]:
            print(f"  • {issue}")

    # Check self-dependencies
    if issues["self_dependencies"]:
        has_issues = True
        print("\n🔄 SELF-DEPENDENCIES DETECTED:")
        for service in issues["self_dependencies"]:
            print(f"  • {service} depends on itself")

    # Show orphaned services (informational, not necessarily an issue)
    if issues["orphaned_services"]:
        print("\n🏝️  ORPHANED SERVICES (no dependencies, nothing depends on them):")
        for service in issues["orphaned_services"]:
            print(f"  • {service}")
        print("   ℹ️  These are typically standalone services (not necessarily a problem)")

    if not has_issues:
        print("\n✅ DEPENDENCY GRAPH IS VALID!")
        print("   • No circular dependencies")
        print("   • No missing services")
        print("   • No self-dependencies")

    # Show dependency tree
    print("\n🌳 DEPENDENCY TREE:")
    _print_dependency_tree(manager.dependency_graph)

    return not has_issues

def _print_dependency_tree(dependency_graph: Dict[str, List[str]]):
    """Print a visual dependency tree"""
    # Group by dependency levels
    levels = {}

    def get_level(service: str, visited: Set[str] = None) -> int:
        if visited is None:
            visited = set()

        if service in visited:
            return 0  # Circular dependency, treat as level 0

        visited.add(service)
        deps = dependency_graph.get(service, [])

        if not deps:
            return 0  # No dependencies

        max_dep_level = max(get_level(dep, visited.copy()) for dep in deps)
        return max_dep_level + 1

    # Calculate levels for all services
    for service in dependency_graph.keys():
        level = get_level(service)
        if level not in levels:
            levels[level] = []
        levels[level].append(service)

    # Print by levels
    for level in sorted(levels.keys()):
        services = sorted(levels[level])
        level_name = "Foundation" if level == 0 else f"Level {level}"
        print(f"\n   {level_name}: {', '.join(services)}")

        # Show dependencies for each service
        for service in services:
            deps = dependency_graph.get(service, [])
            if deps:
                print(f"     └─ {service} depends on: {', '.join(sorted(deps))}")

def main():
    """
    Main entry point for the health check manager.

    Supports multiple operation modes:
    - Default: Human-readable health status report
    - --json: Machine-readable JSON output for monitoring
    - --validate: Dependency graph validation and visualization
    """
    if len(sys.argv) > 1:
        if sys.argv[1] == "--json":
            # JSON output mode (silent to avoid extra output)
            manager = HealthCheckManager(silent=True)
            health_data = manager.check_all_services()

            output = {
                "timestamp": time.time(),
                "services": {
                    name: {
                        "status": health.status.value,
                        "dependencies": health.dependencies,
                        "last_check": health.last_check,
                        "error_message": health.error_message
                    }
                    for name, health in health_data.items()
                },
                "summary": manager.get_service_status_summary()
            }

            print(json.dumps(output, indent=2))
        elif sys.argv[1] == "--validate":
            # Dependency validation mode
            success = validate_dependencies()
            sys.exit(0 if success else 1)
        else:
            print("Usage: health-check-manager.py [--json|--validate]")
            sys.exit(1)
    else:
        # Human-readable output
        manager = HealthCheckManager()
        manager.check_all_services()
        manager.print_status_report()


if __name__ == "__main__":
    main()
