#!/usr/bin/env python3
"""
Initialize VM Pools Script

This script initializes the basic VM pools by creating the minimum required
VMs for each template. Run this after deploying the basic VM availability
manager to ensure VMs are ready for immediate allocation.

Usage:
    python scripts/initialize_vm_pools.py
    
    # Or with specific templates
    python scripts/initialize_vm_pools.py --templates ubuntu:20.04 alpine:latest
    
    # Dry run to see what would be created
    python scripts/initialize_vm_pools.py --dry-run
"""

import argparse
import asyncio
import logging
import sys
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.workers.tasks.basic_vm_availability_manager import (
    availability_manager, maintain_vm_pools
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def initialize_pools(templates=None, dry_run=False):
    """Initialize VM pools with minimum required VMs."""
    try:
        logger.info("Starting VM pool initialization")
        
        # Get current pool status
        status = availability_manager.get_pool_status()
        
        if not status:
            logger.error("Failed to get pool status")
            return False
        
        # Filter templates if specified
        if templates:
            status = {k: v for k, v in status.items() if k in templates}
        
        total_needed = 0
        actions_planned = []
        
        # Calculate what needs to be done
        for template, pool_status in status.items():
            if pool_status["needs_provisioning"]:
                needed = pool_status["min_ready"] - pool_status["ready"]
                total_needed += needed
                
                actions_planned.append({
                    "template": template,
                    "current_ready": pool_status["ready"],
                    "min_required": pool_status["min_ready"],
                    "vms_to_create": needed
                })
                
                logger.info(
                    f"Template {template}: {pool_status['ready']}/{pool_status['min_ready']} ready, "
                    f"need to create {needed} VMs"
                )
        
        if total_needed == 0:
            logger.info("All pools are adequately provisioned!")
            return True
        
        if dry_run:
            logger.info(f"DRY RUN: Would create {total_needed} VMs across {len(actions_planned)} templates")
            for action in actions_planned:
                logger.info(f"  {action['template']}: {action['vms_to_create']} VMs")
            return True
        
        # Execute pool maintenance
        logger.info(f"Creating {total_needed} VMs across {len(actions_planned)} templates")
        
        # Trigger maintenance task
        result = maintain_vm_pools.delay()
        
        # Wait for completion
        logger.info("Waiting for pool maintenance to complete...")
        maintenance_result = result.get(timeout=300)  # 5 minute timeout
        
        if maintenance_result.get("maintenance_completed"):
            actions_taken = maintenance_result.get("actions_taken", [])
            logger.info(f"Pool maintenance completed successfully!")
            logger.info(f"Actions taken: {len(actions_taken)}")
            
            for action in actions_taken:
                logger.info(f"  Created VM {action['vm_id']} for template {action['template']}")
            
            # Wait a bit for VMs to start creating
            logger.info("Waiting 30 seconds for VM creation to begin...")
            time.sleep(30)
            
            # Check final status
            final_status = availability_manager.get_pool_status()
            logger.info("\nFinal pool status:")
            for template, pool_status in final_status.items():
                logger.info(
                    f"  {template}: {pool_status['ready']} ready, "
                    f"{pool_status['creating']} creating, "
                    f"{pool_status['total']} total"
                )
            
            return True
        else:
            logger.error("Pool maintenance failed")
            return False
            
    except Exception as e:
        logger.error(f"Pool initialization failed: {e}")
        return False


def check_pool_health():
    """Check and display current pool health."""
    try:
        logger.info("Checking pool health...")
        
        status = availability_manager.get_pool_status()
        
        if not status:
            logger.error("Failed to get pool status")
            return False
        
        healthy_pools = 0
        total_pools = len(status)
        
        logger.info(f"\nPool Health Report ({total_pools} templates):")
        logger.info("-" * 60)
        
        for template, pool_status in status.items():
            ready = pool_status["ready"]
            min_ready = pool_status["min_ready"]
            total = pool_status["total"]
            creating = pool_status["creating"]
            
            if ready >= min_ready:
                health = "✅ HEALTHY"
                healthy_pools += 1
            elif ready > 0:
                health = "⚠️  DEGRADED"
            else:
                health = "❌ CRITICAL"
            
            logger.info(
                f"{template:25} | {ready:2}/{min_ready:2} ready | {total:2} total | "
                f"{creating:2} creating | {health}"
            )
        
        logger.info("-" * 60)
        logger.info(f"Overall Health: {healthy_pools}/{total_pools} pools healthy")
        
        return healthy_pools == total_pools
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Initialize VM pools for TurdParty")
    parser.add_argument(
        "--templates",
        nargs="+",
        help="Specific templates to initialize (default: all)",
        choices=["ubuntu:20.04", "ubuntu:22.04", "alpine:latest", "10Baht/windows10-turdparty"]
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without actually doing it"
    )
    parser.add_argument(
        "--check-health",
        action="store_true",
        help="Check pool health without making changes"
    )
    
    args = parser.parse_args()
    
    if args.check_health:
        success = check_pool_health()
        sys.exit(0 if success else 1)
    
    success = initialize_pools(templates=args.templates, dry_run=args.dry_run)
    
    if success:
        logger.info("✅ Pool initialization completed successfully!")
        
        # Show final health check
        logger.info("\nPerforming final health check...")
        check_pool_health()
        
        sys.exit(0)
    else:
        logger.error("❌ Pool initialization failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
