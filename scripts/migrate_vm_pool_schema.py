#!/usr/bin/env python3
"""
VM Pool Schema Migration

This script migrates the existing database schema to support the basic VM
availability management system by adding necessary columns and indexes.

Usage:
    python scripts/migrate_vm_pool_schema.py
    python scripts/migrate_vm_pool_schema.py --dry-run
    python scripts/migrate_vm_pool_schema.py --rollback
"""

import argparse
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class VMPoolSchemaMigrator:
    """Migrate database schema for VM pool management."""
    
    def __init__(self, dry_run: bool = False):
        self.dry_run = dry_run
        self.migration_log = []
        
    def get_migration_sql(self) -> List[Dict[str, Any]]:
        """Get SQL statements for migration."""
        return [
            {
                "name": "add_pool_management_columns",
                "description": "Add pool management columns to vm_instances table",
                "sql": """
                    ALTER TABLE vm_instances 
                    ADD COLUMN IF NOT EXISTS pool_managed BOOLEAN DEFAULT FALSE,
                    ADD COLUMN IF NOT EXISTS pool_template VARCHAR(100),
                    ADD COLUMN IF NOT EXISTS ready_at TIMESTAMP,
                    ADD COLUMN IF NOT EXISTS last_allocated_at TIMESTAMP,
                    ADD COLUMN IF NOT EXISTS pool_generation INTEGER DEFAULT 1,
                    ADD COLUMN IF NOT EXISTS allocation_count INTEGER DEFAULT 0;
                """,
                "rollback_sql": """
                    ALTER TABLE vm_instances 
                    DROP COLUMN IF EXISTS pool_managed,
                    DROP COLUMN IF EXISTS pool_template,
                    DROP COLUMN IF EXISTS ready_at,
                    DROP COLUMN IF EXISTS last_allocated_at,
                    DROP COLUMN IF EXISTS pool_generation,
                    DROP COLUMN IF EXISTS allocation_count;
                """
            },
            {
                "name": "create_pool_ready_index",
                "description": "Create index for pool ready VM queries",
                "sql": """
                    CREATE INDEX IF NOT EXISTS idx_vm_instances_pool_ready 
                    ON vm_instances(pool_template, status) 
                    WHERE pool_managed = TRUE;
                """,
                "rollback_sql": """
                    DROP INDEX IF EXISTS idx_vm_instances_pool_ready;
                """
            },
            {
                "name": "create_pool_allocation_index",
                "description": "Create index for pool allocation tracking",
                "sql": """
                    CREATE INDEX IF NOT EXISTS idx_vm_instances_pool_allocation 
                    ON vm_instances(pool_template, last_allocated_at) 
                    WHERE pool_managed = TRUE;
                """,
                "rollback_sql": """
                    DROP INDEX IF EXISTS idx_vm_instances_pool_allocation;
                """
            },
            {
                "name": "create_vm_pool_configs_table",
                "description": "Create vm_pool_configs table for pool configuration",
                "sql": """
                    CREATE TABLE IF NOT EXISTS vm_pool_configs (
                        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                        template VARCHAR(100) NOT NULL UNIQUE,
                        vm_type VARCHAR(20) NOT NULL CHECK (vm_type IN ('docker', 'vagrant')),
                        min_pool_size INTEGER DEFAULT 3 CHECK (min_pool_size >= 0),
                        max_pool_size INTEGER DEFAULT 10 CHECK (max_pool_size >= min_pool_size),
                        target_pool_size INTEGER DEFAULT 5 CHECK (target_pool_size BETWEEN min_pool_size AND max_pool_size),
                        
                        -- Resource configuration
                        memory_mb INTEGER DEFAULT 1024 CHECK (memory_mb > 0),
                        cpus INTEGER DEFAULT 1 CHECK (cpus > 0),
                        disk_gb INTEGER DEFAULT 20 CHECK (disk_gb > 0),
                        
                        -- Pool management settings
                        enabled BOOLEAN DEFAULT TRUE,
                        auto_scale BOOLEAN DEFAULT TRUE,
                        maintenance_window VARCHAR(50) DEFAULT '02:00-04:00',
                        
                        -- Metadata
                        created_at TIMESTAMP DEFAULT NOW(),
                        updated_at TIMESTAMP DEFAULT NOW(),
                        created_by VARCHAR(100) DEFAULT 'migration'
                    );
                """,
                "rollback_sql": """
                    DROP TABLE IF EXISTS vm_pool_configs;
                """
            },
            {
                "name": "insert_default_pool_configs",
                "description": "Insert default pool configurations",
                "sql": """
                    INSERT INTO vm_pool_configs (template, vm_type, min_pool_size, max_pool_size, target_pool_size, memory_mb, cpus, disk_gb)
                    VALUES
                        ('10Baht/windows10-turdparty', 'vagrant', 2, 5, 3, 4096, 2, 50),
                        ('ubuntu:20.04', 'docker', 1, 3, 2, 1024, 1, 20)
                    ON CONFLICT (template) DO UPDATE SET
                        updated_at = NOW();
                """,
                "rollback_sql": """
                    DELETE FROM vm_pool_configs WHERE created_by = 'migration';
                """
            },
            {
                "name": "create_vm_pool_metrics_table",
                "description": "Create vm_pool_metrics table for monitoring",
                "sql": """
                    CREATE TABLE IF NOT EXISTS vm_pool_metrics (
                        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                        template VARCHAR(100) NOT NULL,
                        timestamp TIMESTAMP DEFAULT NOW(),
                        
                        -- Pool state metrics
                        total_vms INTEGER NOT NULL DEFAULT 0,
                        ready_vms INTEGER NOT NULL DEFAULT 0,
                        allocated_vms INTEGER NOT NULL DEFAULT 0,
                        creating_vms INTEGER NOT NULL DEFAULT 0,
                        terminating_vms INTEGER NOT NULL DEFAULT 0,
                        failed_vms INTEGER NOT NULL DEFAULT 0,
                        
                        -- Performance metrics
                        avg_startup_time_seconds INTEGER DEFAULT 0,
                        avg_allocation_time_seconds INTEGER DEFAULT 0,
                        utilization_percent DECIMAL(5,2) DEFAULT 0.0,
                        
                        -- Additional context
                        metadata JSONB DEFAULT '{}'::jsonb
                    );
                """,
                "rollback_sql": """
                    DROP TABLE IF EXISTS vm_pool_metrics;
                """
            },
            {
                "name": "create_pool_metrics_index",
                "description": "Create index for pool metrics queries",
                "sql": """
                    CREATE INDEX IF NOT EXISTS idx_vm_pool_metrics_template_timestamp 
                    ON vm_pool_metrics(template, timestamp);
                """,
                "rollback_sql": """
                    DROP INDEX IF EXISTS idx_vm_pool_metrics_template_timestamp;
                """
            }
        ]
    
    def execute_migration(self, connection) -> bool:
        """Execute the migration."""
        try:
            migrations = self.get_migration_sql()
            
            logger.info(f"Starting migration with {len(migrations)} steps...")
            
            for i, migration in enumerate(migrations, 1):
                logger.info(f"Step {i}/{len(migrations)}: {migration['description']}")
                
                if self.dry_run:
                    logger.info(f"DRY RUN - Would execute: {migration['sql'][:100]}...")
                    self.migration_log.append({
                        "step": i,
                        "name": migration["name"],
                        "status": "dry_run",
                        "timestamp": datetime.now().isoformat()
                    })
                else:
                    try:
                        from sqlalchemy import text
                        connection.execute(text(migration["sql"]))
                        logger.info(f"✅ Step {i} completed successfully")
                        
                        self.migration_log.append({
                            "step": i,
                            "name": migration["name"],
                            "status": "success",
                            "timestamp": datetime.now().isoformat()
                        })
                        
                    except Exception as e:
                        logger.error(f"❌ Step {i} failed: {e}")
                        
                        self.migration_log.append({
                            "step": i,
                            "name": migration["name"],
                            "status": "failed",
                            "error": str(e),
                            "timestamp": datetime.now().isoformat()
                        })
                        
                        return False
            
            if not self.dry_run:
                connection.commit()
                logger.info("✅ Migration completed successfully!")
            else:
                logger.info("✅ Dry run completed - no changes made")
            
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            if not self.dry_run:
                connection.rollback()
            return False
    
    def execute_rollback(self, connection) -> bool:
        """Execute rollback of the migration."""
        try:
            migrations = list(reversed(self.get_migration_sql()))
            
            logger.info(f"Starting rollback with {len(migrations)} steps...")
            
            for i, migration in enumerate(migrations, 1):
                logger.info(f"Rollback step {i}/{len(migrations)}: {migration['description']}")
                
                if self.dry_run:
                    logger.info(f"DRY RUN - Would execute: {migration['rollback_sql'][:100]}...")
                else:
                    try:
                        from sqlalchemy import text
                        connection.execute(text(migration["rollback_sql"]))
                        logger.info(f"✅ Rollback step {i} completed successfully")
                        
                    except Exception as e:
                        logger.error(f"❌ Rollback step {i} failed: {e}")
                        return False
            
            if not self.dry_run:
                connection.commit()
                logger.info("✅ Rollback completed successfully!")
            else:
                logger.info("✅ Rollback dry run completed - no changes made")
            
            return True
            
        except Exception as e:
            logger.error(f"Rollback failed: {e}")
            if not self.dry_run:
                connection.rollback()
            return False
    
    def verify_migration(self, connection) -> Dict[str, Any]:
        """Verify that migration was successful."""
        try:
            verification_results = {}
            
            # Check if columns exist
            from sqlalchemy import text
            result = connection.execute(text("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'vm_instances'
                AND column_name IN ('pool_managed', 'pool_template', 'ready_at', 'last_allocated_at')
            """)).fetchall()
            
            expected_columns = {'pool_managed', 'pool_template', 'ready_at', 'last_allocated_at'}
            found_columns = {row[0] for row in result}
            
            verification_results["vm_instances_columns"] = {
                "expected": list(expected_columns),
                "found": list(found_columns),
                "missing": list(expected_columns - found_columns),
                "success": expected_columns.issubset(found_columns)
            }
            
            # Check if tables exist
            result = connection.execute(text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_name IN ('vm_pool_configs', 'vm_pool_metrics')
            """)).fetchall()
            
            expected_tables = {'vm_pool_configs', 'vm_pool_metrics'}
            found_tables = {row[0] for row in result}
            
            verification_results["tables"] = {
                "expected": list(expected_tables),
                "found": list(found_tables),
                "missing": list(expected_tables - found_tables),
                "success": expected_tables.issubset(found_tables)
            }
            
            # Check if indexes exist
            result = connection.execute(text("""
                SELECT indexname
                FROM pg_indexes
                WHERE indexname IN ('idx_vm_instances_pool_ready', 'idx_vm_pool_metrics_template_timestamp')
            """)).fetchall()
            
            expected_indexes = {'idx_vm_instances_pool_ready', 'idx_vm_pool_metrics_template_timestamp'}
            found_indexes = {row[0] for row in result}
            
            verification_results["indexes"] = {
                "expected": list(expected_indexes),
                "found": list(found_indexes),
                "missing": list(expected_indexes - found_indexes),
                "success": expected_indexes.issubset(found_indexes)
            }
            
            # Overall success
            overall_success = all(
                result["success"] for result in verification_results.values()
            )
            
            verification_results["overall_success"] = overall_success
            
            return verification_results
            
        except Exception as e:
            logger.error(f"Verification failed: {e}")
            return {"error": str(e), "overall_success": False}


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Migrate VM pool schema")
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without making changes"
    )
    parser.add_argument(
        "--rollback",
        action="store_true",
        help="Rollback the migration"
    )
    parser.add_argument(
        "--verify",
        action="store_true",
        help="Verify migration was successful"
    )
    
    args = parser.parse_args()
    
    migrator = VMPoolSchemaMigrator(dry_run=args.dry_run)
    
    try:
        # Import database connection from API service
        api_src_path = project_root / "services" / "api" / "src"
        sys.path.insert(0, str(api_src_path))

        # Set up database connection
        import os

        # Use the same database connection as the API service
        # When running in container, use 'database' hostname
        # When running from host, this would need to be adjusted
        DATABASE_URL = os.getenv(
            "DATABASE_URL",
            "********************************************/turdparty"
        )

        logger.info(f"Connecting to database: {DATABASE_URL.replace('postgres:postgres@', 'postgres:***@')}")

        # Import SQLAlchemy and create engine
        from sqlalchemy import create_engine
        engine = create_engine(DATABASE_URL)

        with engine.connect() as conn:
            if args.verify:
                logger.info("🔍 Verifying migration...")
                verification = migrator.verify_migration(conn)
                
                if verification.get("overall_success", False):
                    logger.info("✅ Migration verification successful!")
                else:
                    logger.error("❌ Migration verification failed!")
                    for component, result in verification.items():
                        if isinstance(result, dict) and not result.get("success", True):
                            logger.error(f"  {component}: {result}")
                
                sys.exit(0 if verification.get("overall_success", False) else 1)
            
            elif args.rollback:
                logger.info("🔄 Starting migration rollback...")
                success = migrator.execute_rollback(conn)
                
            else:
                logger.info("🚀 Starting migration...")
                success = migrator.execute_migration(conn)
                
                if success and not args.dry_run:
                    logger.info("🔍 Verifying migration...")
                    verification = migrator.verify_migration(conn)
                    
                    if verification.get("overall_success", False):
                        logger.info("✅ Migration and verification successful!")
                    else:
                        logger.error("❌ Migration verification failed!")
                        success = False
            
            sys.exit(0 if success else 1)
            
    except ImportError as e:
        logger.error(f"Database connection import failed: {e}")
        logger.error("Ensure database modules are properly configured")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
