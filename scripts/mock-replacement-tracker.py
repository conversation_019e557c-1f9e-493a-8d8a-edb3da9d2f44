#!/usr/bin/env python3
"""
Mock Replacement Progress Tracker

This script tracks the progress of replacing mock testing with productive testing
across the TurdParty codebase. It generates reports and updates the tracking table.
"""

import ast
import json
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Any
import argparse

class MockReplacementTracker:
    """Track progress of mock replacement across the codebase"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.test_dirs = ["tests/unit", "tests/integration", "tests/e2e"]
        self.mock_patterns = [
            r'from unittest\.mock import',
            r'import unittest\.mock',
            r'@patch\(',
            r'@patch\.object\(',
            r'MagicMock\(',
            r'AsyncMock\(',
            r'Mock\(',
            r'mock_open\(',
            r'patch\(',
            r'patch\.object\(',
            r'pytest-mock',
            r'mocker\.',
        ]
        
        # Component tracking configuration
        self.components = {
            "Database Layer": {
                "files": ["test_*db*.py", "test_*database*.py", "test_*model*.py"],
                "mock_patterns": ["mock.*session", "mock.*engine", "mock.*query"],
                "target_state": "TestContainers PostgreSQL",
                "priority": "High"
            },
            "Storage Layer": {
                "files": ["test_*minio*.py", "test_*storage*.py", "test_*file*.py"],
                "mock_patterns": ["mock.*minio", "mock.*s3", "mock.*bucket"],
                "target_state": "TestContainers MinIO",
                "priority": "High"
            },
            "Caching Layer": {
                "files": ["test_*redis*.py", "test_*cache*.py"],
                "mock_patterns": ["mock.*redis", "mock.*cache"],
                "target_state": "TestContainers Redis",
                "priority": "High"
            },
            "VM Management": {
                "files": ["test_*vm*.py", "test_*docker*.py", "test_*vagrant*.py"],
                "mock_patterns": ["mock.*docker", "mock.*container", "mock.*vm"],
                "target_state": "Real Docker API",
                "priority": "High"
            },
            "File Injection": {
                "files": ["test_*injection*.py", "test_*upload*.py"],
                "mock_patterns": ["mock.*injection", "mock.*upload", "mock.*file"],
                "target_state": "Real file operations",
                "priority": "High"
            },
            "Worker Services": {
                "files": ["test_*worker*.py", "test_*celery*.py", "test_*task*.py"],
                "mock_patterns": ["mock.*celery", "mock.*task", "mock.*worker"],
                "target_state": "Real Celery workers",
                "priority": "High"
            },
            "Logging & Monitoring": {
                "files": ["test_*elk*.py", "test_*log*.py", "test_*monitor*.py"],
                "mock_patterns": ["mock.*elasticsearch", "mock.*logstash", "mock.*kibana"],
                "target_state": "TestContainers ELK",
                "priority": "Medium"
            },
            "API Layer": {
                "files": ["test_*route*.py", "test_*api*.py", "test_*endpoint*.py"],
                "mock_patterns": ["mock.*service", "mock.*client", "mock.*response"],
                "target_state": "Real service calls",
                "priority": "High"
            }
        }
    
    def find_test_files(self) -> List[Path]:
        """Find all test files in the project"""
        test_files = []
        for test_dir in self.test_dirs:
            test_path = self.project_root / test_dir
            if test_path.exists():
                test_files.extend(test_path.rglob("test_*.py"))
                test_files.extend(test_path.rglob("*_test.py"))
        return test_files
    
    def analyze_file_for_mocks(self, file_path: Path) -> Dict[str, Any]:
        """Analyze a single file for mock usage"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return {"error": str(e), "mock_count": 0, "mocks": []}
        
        mocks_found = []
        total_mock_count = 0
        
        for pattern in self.mock_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                mocks_found.append({
                    "pattern": pattern,
                    "line": line_num,
                    "context": self._get_line_context(content, line_num)
                })
                total_mock_count += 1
        
        return {
            "mock_count": total_mock_count,
            "mocks": mocks_found,
            "file_size": len(content.splitlines()),
            "last_modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
        }
    
    def _get_line_context(self, content: str, line_num: int) -> str:
        """Get context around a specific line"""
        lines = content.splitlines()
        if 0 <= line_num - 1 < len(lines):
            return lines[line_num - 1].strip()
        return ""
    
    def categorize_file(self, file_path: Path) -> str:
        """Categorize a test file based on its name and content"""
        file_name = file_path.name.lower()
        
        for component, config in self.components.items():
            for pattern in config["files"]:
                if re.match(pattern.replace("*", ".*"), file_name):
                    return component
        
        return "Other"
    
    def generate_component_report(self) -> Dict[str, Any]:
        """Generate a comprehensive report by component"""
        test_files = self.find_test_files()
        component_stats = {}
        
        for component in self.components.keys():
            component_stats[component] = {
                "files": [],
                "total_mocks": 0,
                "total_files": 0,
                "priority": self.components[component]["priority"],
                "target_state": self.components[component]["target_state"]
            }
        
        component_stats["Other"] = {
            "files": [],
            "total_mocks": 0,
            "total_files": 0,
            "priority": "Low",
            "target_state": "TBD"
        }
        
        for file_path in test_files:
            category = self.categorize_file(file_path)
            analysis = self.analyze_file_for_mocks(file_path)
            
            component_stats[category]["files"].append({
                "path": str(file_path.relative_to(self.project_root)),
                "mock_count": analysis["mock_count"],
                "mocks": analysis["mocks"],
                "file_size": analysis["file_size"],
                "last_modified": analysis["last_modified"]
            })
            component_stats[category]["total_mocks"] += analysis["mock_count"]
            component_stats[category]["total_files"] += 1
        
        return component_stats
    
    def calculate_progress_metrics(self, component_stats: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall progress metrics"""
        total_files = sum(stats["total_files"] for stats in component_stats.values())
        total_mocks = sum(stats["total_mocks"] for stats in component_stats.values())
        
        high_priority_components = [
            comp for comp, stats in component_stats.items() 
            if stats["priority"] == "High"
        ]
        high_priority_mocks = sum(
            component_stats[comp]["total_mocks"] for comp in high_priority_components
        )
        
        return {
            "total_test_files": total_files,
            "total_mock_usage": total_mocks,
            "high_priority_mocks": high_priority_mocks,
            "components_analyzed": len(component_stats),
            "avg_mocks_per_file": total_mocks / total_files if total_files > 0 else 0,
            "completion_percentage": 0,  # Will be calculated based on actual replacements
            "last_analysis": datetime.now().isoformat()
        }
    
    def generate_markdown_report(self, component_stats: Dict[str, Any], metrics: Dict[str, Any]) -> str:
        """Generate a markdown report"""
        report = f"""# Mock Replacement Progress Report

Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Summary Metrics

- **Total Test Files**: {metrics['total_test_files']}
- **Total Mock Usage**: {metrics['total_mock_usage']}
- **High Priority Mocks**: {metrics['high_priority_mocks']}
- **Average Mocks per File**: {metrics['avg_mocks_per_file']:.2f}
- **Completion Percentage**: {metrics['completion_percentage']:.1f}%

## Component Breakdown

| Component | Files | Mock Count | Priority | Target State | Status |
|-----------|-------|------------|----------|--------------|--------|
"""
        
        for component, stats in component_stats.items():
            if stats["total_files"] > 0:
                status = "🔴 Not Started" if stats["total_mocks"] > 0 else "✅ Complete"
                report += f"| {component} | {stats['total_files']} | {stats['total_mocks']} | {stats['priority']} | {stats['target_state']} | {status} |\n"
        
        report += "\n## Detailed File Analysis\n\n"
        
        for component, stats in component_stats.items():
            if stats["total_files"] > 0:
                report += f"### {component}\n\n"
                for file_info in stats["files"]:
                    if file_info["mock_count"] > 0:
                        report += f"- **{file_info['path']}**: {file_info['mock_count']} mocks\n"
                        for mock in file_info["mocks"][:3]:  # Show first 3 mocks
                            report += f"  - Line {mock['line']}: `{mock['context'][:80]}...`\n"
                        if len(file_info["mocks"]) > 3:
                            report += f"  - ... and {len(file_info['mocks']) - 3} more\n"
                report += "\n"
        
        return report
    
    def save_report(self, report: str, output_file: str = "mock-replacement-progress.md"):
        """Save the report to a file"""
        output_path = self.project_root / "docs" / output_file
        output_path.parent.mkdir(exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📊 Report saved to: {output_path}")
    
    def save_json_data(self, component_stats: Dict[str, Any], metrics: Dict[str, Any], 
                       output_file: str = "mock-replacement-data.json"):
        """Save raw data as JSON for further processing"""
        data = {
            "metrics": metrics,
            "components": component_stats,
            "generated_at": datetime.now().isoformat()
        }
        
        output_path = self.project_root / "docs" / output_file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)
        
        print(f"💾 Data saved to: {output_path}")
    
    def run_analysis(self, save_reports: bool = True) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Run complete analysis and generate reports"""
        print("🔍 Analyzing test files for mock usage...")
        
        component_stats = self.generate_component_report()
        metrics = self.calculate_progress_metrics(component_stats)
        
        print(f"📈 Analysis complete:")
        print(f"   - {metrics['total_test_files']} test files analyzed")
        print(f"   - {metrics['total_mock_usage']} mock usages found")
        print(f"   - {metrics['high_priority_mocks']} high-priority mocks to replace")
        
        if save_reports:
            report = self.generate_markdown_report(component_stats, metrics)
            self.save_report(report)
            self.save_json_data(component_stats, metrics)
        
        return component_stats, metrics

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Track mock replacement progress")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    parser.add_argument("--no-save", action="store_true", help="Don't save reports to files")
    parser.add_argument("--json-only", action="store_true", help="Only output JSON data")
    
    args = parser.parse_args()
    
    tracker = MockReplacementTracker(args.project_root)
    component_stats, metrics = tracker.run_analysis(save_reports=not args.no_save)
    
    if args.json_only:
        print(json.dumps({
            "metrics": metrics,
            "components": component_stats
        }, indent=2))
    else:
        print("\n" + "="*60)
        print("MOCK REPLACEMENT PROGRESS SUMMARY")
        print("="*60)
        
        for component, stats in component_stats.items():
            if stats["total_files"] > 0:
                status = "✅ COMPLETE" if stats["total_mocks"] == 0 else f"🔴 {stats['total_mocks']} MOCKS"
                print(f"{component:20} | {stats['total_files']:2} files | {status}")
        
        print("="*60)
        print(f"TOTAL: {metrics['total_mock_usage']} mocks across {metrics['total_test_files']} files")
        print("="*60)

if __name__ == "__main__":
    main()
