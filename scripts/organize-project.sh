#!/bin/bash
# TurdParty Project Organization Script
# Cleans up and organizes the main project directory

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create organized directory structure
create_directories() {
    log_info "Creating organized directory structure..."
    
    # Create main organization directories
    mkdir -p archive/{status-reports,old-docs,temp-files}
    mkdir -p documentation/{status,guides,references}
    mkdir -p deployment/{docker,scripts,configs}
    mkdir -p development/{tools,templates,examples}
    
    log_success "Directory structure created"
}

# Move status and completion files
organize_status_files() {
    log_info "Organizing status and completion files..."
    
    # Move all status/completion markdown files to archive
    for file in *_COMPLETE.md *_SUMMARY.md *_STATUS.md *_TODO.md *_RESULTS.md; do
        if [ -f "$file" ]; then
            mv "$file" archive/status-reports/
            log_info "Moved $file to archive/status-reports/"
        fi
    done
    
    # Move specific development files
    [ -f "DEVELOPMENT.md" ] && mv "DEVELOPMENT.md" documentation/guides/
    [ -f "TESTING_STATUS.md" ] && mv "TESTING_STATUS.md" documentation/status/
    
    log_success "Status files organized"
}

# Organize Docker and deployment files
organize_deployment() {
    log_info "Organizing deployment files..."
    
    # Move docker-compose files to deployment
    [ -f "docker-compose.yml" ] && mv "docker-compose.yml" deployment/docker/
    
    # Create symlinks for convenience
    if [ -f "deployment/docker/docker-compose.yml" ]; then
        ln -sf deployment/docker/docker-compose.yml docker-compose.yml
        log_info "Created symlink for docker-compose.yml"
    fi
    
    # Move deployment scripts
    for script in integration-test.sh test-domain-access.sh test-frontend-domain.sh; do
        if [ -f "$script" ]; then
            mv "$script" deployment/scripts/
            log_info "Moved $script to deployment/scripts/"
        fi
    done
    
    log_success "Deployment files organized"
}

# Organize development files
organize_development() {
    log_info "Organizing development files..."
    
    # Move development tools
    [ -f "debug_vm_creation.py" ] && mv "debug_vm_creation.py" development/tools/
    [ -f "test_elk_data_verification.py" ] && mv "test_elk_data_verification.py" development/tools/
    [ -f "main.py" ] && mv "main.py" development/examples/
    
    # Move test files
    for file in final-workflow-test.js simple-frontend-test.js test-demo.js; do
        if [ -f "$file" ]; then
            mv "$file" development/examples/
            log_info "Moved $file to development/examples/"
        fi
    done
    
    log_success "Development files organized"
}

# Clean up temporary and log directories
cleanup_temp_files() {
    log_info "Cleaning up temporary files and directories..."
    
    # Move logs to archive
    if [ -d "logs" ] && [ "$(ls -A logs)" ]; then
        mv logs archive/temp-files/logs-$(date +%Y%m%d)
        log_info "Archived logs directory"
    fi
    
    # Clean up empty upload directories
    [ -d "uploads" ] && [ ! "$(ls -A uploads)" ] && rmdir uploads
    
    # Move coverage archive
    [ -d "coverage-archive" ] && mv "coverage-archive" archive/temp-files/
    
    # Clean up test results if they're old
    if [ -d "test-results" ]; then
        find test-results -name "*.xml" -o -name "*.json" -o -name "*.html" | head -10 | while read file; do
            if [ -f "$file" ]; then
                mkdir -p archive/temp-files/test-results
                mv "$file" archive/temp-files/test-results/
            fi
        done
    fi
    
    log_success "Temporary files cleaned up"
}

# Update documentation structure
update_documentation() {
    log_info "Updating documentation structure..."
    
    # Create documentation index
    cat > documentation/README.md << 'EOF'
# TurdParty Documentation

This directory contains organized documentation for the TurdParty project.

## Structure

- `status/` - Current project status and progress reports
- `guides/` - Development and user guides
- `references/` - Technical references and specifications

## Main Documentation

The primary documentation is built using Sphinx and located in the `docs/` directory.
Access it via:

- **Local**: http://localhost:8000/docs/
- **Build**: `cd docs && source venv/bin/activate && sphinx-build -b html . _build/html`

## Quick Links

- [Getting Started](../docs/getting-started/index.rst)
- [API Reference](../docs/getting-started/api-reference.rst)
- [Platform Components](../docs/platform/components.rst)
- [Testing Documentation](../tests/docs/)
EOF
    
    log_success "Documentation structure updated"
}

# Create project overview
create_project_overview() {
    log_info "Creating project overview..."
    
    cat > PROJECT_OVERVIEW.md << 'EOF'
# TurdParty - Malware Analysis Platform

A comprehensive malware analysis platform with automated file processing, VM management, and real-time monitoring.

## Quick Start

```bash
# Start core services
docker-compose up -d

# Access the platform
open http://frontend.turdparty.localhost
open http://localhost:8000/docs  # Documentation
open http://kibana.turdparty.localhost  # Analytics
```

## Project Structure

```
├── api/                    # Core API implementation
├── services/              # Microservices (API, workers, frontend)
├── compose/               # Docker Compose configurations
├── docs/                  # Sphinx documentation
├── tests/                 # Comprehensive test suite
├── scripts/               # Utility and deployment scripts
├── frontend/              # React web interface
├── config/                # Configuration files
├── deployment/            # Deployment tools and configs
├── development/           # Development tools and examples
├── documentation/         # Organized project documentation
└── archive/               # Historical files and reports
```

## Key Features

- **Automated Malware Analysis** - Complete file processing pipeline
- **VM Pool Management** - Intelligent VM allocation and lifecycle
- **Real-time Monitoring** - ELK stack integration with live dashboards
- **Modern Web Interface** - React frontend with dark mode
- **Comprehensive Testing** - 21/21 passing tests with multiple frameworks
- **Production Ready** - Docker-based deployment with Traefik integration

## Documentation

- **Main Documentation**: [docs/](docs/) - Sphinx-generated comprehensive docs
- **API Reference**: [docs/getting-started/api-reference.rst](docs/getting-started/api-reference.rst)
- **Testing Guide**: [tests/docs/](tests/docs/)
- **Development Status**: [documentation/status/](documentation/status/)

## Development

```bash
# Install dependencies
nix-shell

# Run tests
./scripts/run-parallel-tests.sh

# Build documentation
./scripts/update-docs.sh

# Start development environment
./dev.sh
```

## Support

- **Issues**: Use GitHub Issues for bug reports and feature requests
- **Documentation**: Comprehensive docs at `/docs` endpoint
- **Testing**: Run `./scripts/run-parallel-tests.sh` for full test suite
EOF
    
    log_success "Project overview created"
}

# Main execution
main() {
    log_info "TurdParty Project Organization"
    log_info "============================="
    
    # Check if we're in the right directory
    if [ ! -f "README.md" ] || [ ! -d "services" ]; then
        log_error "This script must be run from the project root directory"
        exit 1
    fi
    
    # Create backup
    log_info "Creating backup of current state..."
    tar -czf "project-backup-$(date +%Y%m%d-%H%M%S).tar.gz" \
        --exclude='node_modules' \
        --exclude='venv' \
        --exclude='_build' \
        --exclude='.git' \
        . 2>/dev/null || log_warning "Backup creation had some warnings"
    
    # Execute organization steps
    create_directories
    organize_status_files
    organize_deployment
    organize_development
    cleanup_temp_files
    update_documentation
    create_project_overview
    
    log_success "Project organization completed!"
    log_info "Summary:"
    log_info "- Status files moved to archive/status-reports/"
    log_info "- Deployment files organized in deployment/"
    log_info "- Development tools moved to development/"
    log_info "- Documentation structure created"
    log_info "- Project overview created as PROJECT_OVERVIEW.md"
    log_info ""
    log_info "Next steps:"
    log_info "1. Review the organized structure"
    log_info "2. Update any scripts that reference moved files"
    log_info "3. Commit the organized structure to git"
}

# Run main function
main "$@"
