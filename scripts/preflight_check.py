#!/usr/bin/env python3
"""
TurdParty Pre-flight Check

Comprehensive check of all services before running VM management tests.
This ensures the entire TurdParty stack is running and ready.

Usage:
    python scripts/preflight_check.py
    python scripts/preflight_check.py --start-missing
    nix-shell --run "python scripts/preflight_check.py"
"""

import argparse
import json
import logging
import subprocess
import sys
import time
import requests
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TurdPartyPreflightChecker:
    """Comprehensive pre-flight check for TurdParty services."""

    def __init__(self, start_missing: bool = False):
        self.start_missing = start_missing
        self.check_results = {
            "start_time": datetime.now().isoformat(),
            "checks": {},
            "summary": {}
        }

        # Initialize ServiceURLManager
        self.service_url_manager = None
        self.service_urls = {}
        self._load_service_urls()

        # Required services for VM management
        self.required_services = {
            "vagrant": {
                "name": "Vagrant",
                "description": "VM provisioning and management",
                "critical": True
            },
            "docker": {
                "name": "Docker",
                "description": "Container runtime",
                "critical": True
            },
            "traefik": {
                "name": "Traefik",
                "description": "Reverse proxy and load balancer",
                "critical": True,
                "container": "traefik-controller",
                "service_key": "traefik"
            },
            "postgresql": {
                "name": "PostgreSQL",
                "description": "Primary database",
                "critical": True,
                "container": "turdpartycollab_database",
                "service_key": "postgres"
            },
            "redis": {
                "name": "Redis",
                "description": "Cache and message broker",
                "critical": True,
                "container": "turdpartycollab_redis",
                "service_key": "redis"
            },
            "elasticsearch": {
                "name": "Elasticsearch",
                "description": "Search and analytics engine",
                "critical": True,
                "container": "turdpartycollab_elasticsearch",
                "service_key": "elasticsearch"
            },
            "minio": {
                "name": "MinIO",
                "description": "Object storage",
                "critical": True,
                "container": "turdpartycollab_storage",
                "service_key": "minio"
            },
            "api": {
                "name": "TurdParty API",
                "description": "Main API service",
                "critical": True,
                "container": "turdpartycollab_api",
                "service_key": "api"
            },
            "frontend": {
                "name": "TurdParty Frontend",
                "description": "Web interface",
                "critical": False,
                "container": "turdpartycollab_frontend",
                "service_key": "frontend"
            },
            "workers": {
                "name": "Celery Workers",
                "description": "Background task processing",
                "critical": True,
                "container": "turdpartycollab_celery_worker"
            }
        }

    def _load_service_urls(self):
        """Load service URLs using ServiceURLManager."""
        try:
            from utils.service_urls import ServiceURLManager

            self.service_url_manager = ServiceURLManager('local')

            # Get all service URLs and health check URLs
            all_urls = self.service_url_manager.get_all_service_urls()
            health_urls = self.service_url_manager.health_check_urls()

            # Combine them
            for service_name in all_urls:
                self.service_urls[service_name] = {
                    "url": all_urls[service_name],
                    "health_url": health_urls.get(service_name, all_urls[service_name])
                }

            logger.info(f"Loaded service URLs via ServiceURLManager: {list(self.service_urls.keys())}")

        except ImportError:
            logger.warning("ServiceURLManager not available, using fallback URLs")
            # Fallback URLs
            self.service_urls = {
                'api': {
                    "url": 'http://api.turdparty.localhost',
                    "health_url": 'http://api.turdparty.localhost/health'
                },
                'frontend': {
                    "url": 'http://frontend.turdparty.localhost',
                    "health_url": 'http://frontend.turdparty.localhost/'
                },
                'elasticsearch': {
                    "url": 'http://elasticsearch.turdparty.localhost',
                    "health_url": 'http://elasticsearch.turdparty.localhost/_cluster/health'
                },
                'kibana': {
                    "url": 'http://kibana.turdparty.localhost',
                    "health_url": 'http://kibana.turdparty.localhost/api/status'
                },
                'minio': {
                    "url": 'http://storage-api.turdparty.localhost',
                    "health_url": 'http://storage-api.turdparty.localhost/minio/health/live'
                },
                'redis': {
                    "url": 'http://redis.turdparty.localhost',
                    "health_url": 'http://redis.turdparty.localhost/ping'
                },
                'postgres': {
                    "url": 'http://postgres.turdparty.localhost',
                    "health_url": 'http://postgres.turdparty.localhost/'
                },
                'traefik': {
                    "url": 'http://traefik.turdparty.localhost',
                    "health_url": 'http://traefik.turdparty.localhost/ping'
                }
            }
        except Exception as e:
            logger.warning(f"Error loading ServiceURLManager: {e}")
            # Use fallback URLs
            self.service_urls = {
                'api': {
                    "url": 'http://api.turdparty.localhost',
                    "health_url": 'http://api.turdparty.localhost/health'
                },
                'frontend': {
                    "url": 'http://frontend.turdparty.localhost',
                    "health_url": 'http://frontend.turdparty.localhost/'
                },
                'elasticsearch': {
                    "url": 'http://elasticsearch.turdparty.localhost',
                    "health_url": 'http://elasticsearch.turdparty.localhost/_cluster/health'
                },
                'kibana': {
                    "url": 'http://kibana.turdparty.localhost',
                    "health_url": 'http://kibana.turdparty.localhost/api/status'
                },
                'minio': {
                    "url": 'http://storage-api.turdparty.localhost',
                    "health_url": 'http://storage-api.turdparty.localhost/minio/health/live'
                },
                'redis': {
                    "url": 'http://redis.turdparty.localhost',
                    "health_url": 'http://redis.turdparty.localhost/ping'
                },
                'postgres': {
                    "url": 'http://postgres.turdparty.localhost',
                    "health_url": 'http://postgres.turdparty.localhost/'
                },
                'traefik': {
                    "url": 'http://traefik.turdparty.localhost',
                    "health_url": 'http://traefik.turdparty.localhost/ping'
                }
            }
    
    def check_vagrant(self) -> Dict[str, Any]:
        """Check Vagrant availability and status."""
        logger.info("🔍 Checking Vagrant...")
        
        try:
            # Check vagrant command
            result = subprocess.run(
                ['vagrant', '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0:
                return {
                    "status": "failed",
                    "error": "Vagrant command not available",
                    "suggestion": "Install Vagrant"
                }
            
            version = result.stdout.strip()
            
            # Check running VMs
            result = subprocess.run(
                ['vagrant', 'global-status'],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            running_vms = 0
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'running' in line.lower():
                        running_vms += 1
            
            return {
                "status": "ready",
                "version": version,
                "running_vms": running_vms,
                "message": f"Vagrant ready with {running_vms} running VMs"
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Vagrant check failed: {e}"
            }
    
    def check_docker(self) -> Dict[str, Any]:
        """Check Docker daemon status."""
        logger.info("🔍 Checking Docker...")
        
        try:
            # Check docker daemon
            result = subprocess.run(
                ['docker', 'info'],
                capture_output=True,
                text=True,
                timeout=15
            )
            
            if result.returncode != 0:
                return {
                    "status": "failed",
                    "error": "Docker daemon not running",
                    "suggestion": "Start Docker: sudo systemctl start docker"
                }
            
            # Get docker version
            result = subprocess.run(
                ['docker', '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            version = result.stdout.strip() if result.returncode == 0 else "Unknown"
            
            return {
                "status": "ready",
                "version": version,
                "message": "Docker daemon running"
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Docker check failed: {e}"
            }
    
    def check_container(self, container_name: str) -> Dict[str, Any]:
        """Check if a specific container is running."""
        try:
            result = subprocess.run(
                ['docker', 'ps', '--filter', f'name={container_name}', '--format', '{{.Names}}\t{{.Status}}'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0:
                return {
                    "status": "failed",
                    "error": "Docker command failed"
                }
            
            if container_name in result.stdout:
                status_line = result.stdout.strip()
                return {
                    "status": "running",
                    "container_status": status_line.split('\t')[1] if '\t' in status_line else "running",
                    "message": f"Container {container_name} is running"
                }
            else:
                return {
                    "status": "stopped",
                    "error": f"Container {container_name} not running",
                    "suggestion": f"Start container: docker-compose up -d {container_name.replace('turdpartycollab_', '')}"
                }
                
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Container check failed: {e}"
            }
    
    def check_service_url(self, service_name: str, url: str) -> Dict[str, Any]:
        """Check if a service URL is accessible."""
        try:
            # For Traefik routing, use localhost with Host header
            if '.turdparty.localhost' in url:
                host = url.split('//')[1].split('/')[0].split(':')[0]
                test_url = url.replace(host, 'localhost')
                headers = {'Host': host}
            else:
                test_url = url
                headers = {}
            
            response = requests.get(test_url, headers=headers, timeout=10)
            
            return {
                "status": "accessible",
                "status_code": response.status_code,
                "response_time_ms": int(response.elapsed.total_seconds() * 1000),
                "message": f"Service accessible (HTTP {response.status_code})"
            }
            
        except requests.exceptions.ConnectionError:
            return {
                "status": "unreachable",
                "error": "Connection refused",
                "suggestion": f"Check if {service_name} service is running"
            }
        except Exception as e:
            return {
                "status": "failed",
                "error": f"URL check failed: {e}"
            }
    
    def check_docker_compose_stack(self) -> Dict[str, Any]:
        """Check if docker-compose stack is running."""
        logger.info("🔍 Checking Docker Compose stack...")
        
        try:
            result = subprocess.run(
                ['docker-compose', 'ps'],
                capture_output=True,
                text=True,
                timeout=15
            )
            
            if result.returncode != 0:
                return {
                    "status": "failed",
                    "error": "docker-compose command failed",
                    "suggestion": "Run: docker-compose up -d"
                }
            
            # Parse docker-compose ps output
            lines = result.stdout.strip().split('\n')
            services = []
            
            for line in lines[2:]:  # Skip header lines
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 4:
                        service_name = parts[0]
                        state = parts[3] if len(parts) > 3 else "unknown"
                        services.append({
                            "name": service_name,
                            "state": state
                        })
            
            running_services = [s for s in services if 'up' in s['state'].lower()]
            
            return {
                "status": "checked",
                "total_services": len(services),
                "running_services": len(running_services),
                "services": services,
                "message": f"{len(running_services)}/{len(services)} services running"
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Docker Compose check failed: {e}"
            }
    
    def start_missing_services(self) -> Dict[str, Any]:
        """Start missing services using docker-compose."""
        logger.info("🚀 Starting missing services...")
        
        try:
            result = subprocess.run(
                ['docker-compose', 'up', '-d'],
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode != 0:
                return {
                    "status": "failed",
                    "error": f"Failed to start services: {result.stderr}"
                }
            
            # Wait a bit for services to start
            time.sleep(10)
            
            return {
                "status": "started",
                "message": "Services started successfully"
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Failed to start services: {e}"
            }
    
    def run_comprehensive_check(self) -> Dict[str, Any]:
        """Run comprehensive pre-flight check."""
        logger.info("🚀 Starting TurdParty Pre-flight Check")
        logger.info("=" * 60)
        
        results = {}
        critical_failures = 0
        total_checks = 0
        
        # 1. Check Vagrant
        logger.info("1️⃣ Checking Vagrant...")
        results["vagrant"] = self.check_vagrant()
        total_checks += 1
        if results["vagrant"]["status"] != "ready":
            critical_failures += 1
            logger.error(f"❌ Vagrant: {results['vagrant'].get('error', 'Failed')}")
        else:
            logger.info(f"✅ Vagrant: {results['vagrant']['message']}")
        
        # 2. Check Docker
        logger.info("2️⃣ Checking Docker...")
        results["docker"] = self.check_docker()
        total_checks += 1
        if results["docker"]["status"] != "ready":
            critical_failures += 1
            logger.error(f"❌ Docker: {results['docker'].get('error', 'Failed')}")
        else:
            logger.info(f"✅ Docker: {results['docker']['message']}")
        
        # 3. Check Docker Compose stack
        logger.info("3️⃣ Checking Docker Compose stack...")
        results["docker_compose"] = self.check_docker_compose_stack()
        total_checks += 1
        
        if results["docker_compose"]["status"] == "checked":
            running = results["docker_compose"]["running_services"]
            total = results["docker_compose"]["total_services"]
            
            if running == 0 and self.start_missing:
                logger.info("🚀 No services running, starting them...")
                start_result = self.start_missing_services()
                results["service_startup"] = start_result
                
                if start_result["status"] == "started":
                    # Re-check after starting
                    results["docker_compose"] = self.check_docker_compose_stack()
                    running = results["docker_compose"]["running_services"]
                    total = results["docker_compose"]["total_services"]
            
            if running < total:
                logger.warning(f"⚠️  Docker Compose: {running}/{total} services running")
                if not self.start_missing:
                    logger.info("💡 Use --start-missing to automatically start services")
            else:
                logger.info(f"✅ Docker Compose: All {total} services running")
        else:
            logger.error(f"❌ Docker Compose: {results['docker_compose'].get('error', 'Failed')}")
        
        # 4. Check individual containers
        logger.info("4️⃣ Checking individual containers...")
        container_results = {}
        
        for service_id, service_info in self.required_services.items():
            if "container" in service_info:
                container_name = service_info["container"]
                logger.info(f"   Checking {service_info['name']} ({container_name})...")
                
                container_results[service_id] = self.check_container(container_name)
                
                if container_results[service_id]["status"] == "running":
                    logger.info(f"   ✅ {service_info['name']}: Running")
                else:
                    logger.error(f"   ❌ {service_info['name']}: {container_results[service_id].get('error', 'Not running')}")
                    if service_info["critical"]:
                        critical_failures += 1
        
        results["containers"] = container_results
        
        # 5. Check service URLs
        logger.info("5️⃣ Checking service URLs...")
        url_results = {}

        for service_id, service_info in self.required_services.items():
            if "service_key" in service_info and service_info["service_key"] in self.service_urls:
                service_key = service_info["service_key"]
                service_config = self.service_urls[service_key]
                url = service_config["health_url"]

                logger.info(f"   Checking {service_info['name']} at {url}...")

                url_results[service_id] = self.check_service_url(service_info['name'], url)

                if url_results[service_id]["status"] == "accessible":
                    logger.info(f"   ✅ {service_info['name']}: Accessible")
                else:
                    logger.error(f"   ❌ {service_info['name']}: {url_results[service_id].get('error', 'Not accessible')}")

        results["urls"] = url_results
        
        # Calculate summary
        all_ready = critical_failures == 0
        
        summary = {
            "total_checks": total_checks,
            "critical_failures": critical_failures,
            "all_ready": all_ready,
            "infrastructure_ready": all_ready,
            "vm_management_ready": all_ready
        }
        
        self.check_results.update({
            "checks": results,
            "summary": summary,
            "end_time": datetime.now().isoformat()
        })
        
        # Print final summary
        logger.info("\n" + "=" * 60)
        logger.info("PRE-FLIGHT CHECK RESULTS")
        logger.info("=" * 60)
        logger.info(f"Total Checks: {total_checks}")
        logger.info(f"Critical Failures: {critical_failures}")
        logger.info(f"Infrastructure Ready: {'✅ YES' if all_ready else '❌ NO'}")
        logger.info(f"VM Management Ready: {'✅ YES' if all_ready else '❌ NO'}")
        
        if not all_ready:
            logger.error("\n🚨 CRITICAL ISSUES FOUND!")
            logger.error("VM management tests cannot proceed until all services are running.")
            if not self.start_missing:
                logger.info("💡 Try running with --start-missing to automatically start services")
        else:
            logger.info("\n🎉 ALL SYSTEMS GO!")
            logger.info("TurdParty is ready for VM management tests.")
        
        logger.info("=" * 60)
        
        return self.check_results
    
    def save_results(self, filename: str = None):
        """Save check results to file."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"preflight_check_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.check_results, f, indent=2)
        
        logger.info(f"📄 Results saved to: {filename}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="TurdParty pre-flight check")
    parser.add_argument(
        "--start-missing",
        action="store_true",
        help="Automatically start missing services"
    )
    parser.add_argument(
        "--save-results",
        action="store_true",
        help="Save check results to file"
    )
    
    args = parser.parse_args()
    
    checker = TurdPartyPreflightChecker(start_missing=args.start_missing)
    results = checker.run_comprehensive_check()
    
    if args.save_results:
        checker.save_results()
    
    # Exit with appropriate code
    sys.exit(0 if results["summary"]["all_ready"] else 1)


if __name__ == "__main__":
    main()
