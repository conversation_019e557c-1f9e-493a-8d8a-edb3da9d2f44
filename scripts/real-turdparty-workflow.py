#!/usr/bin/env python3
"""
Real TurdParty Workflow Implementation
Implements the complete end-to-end workflow using API endpoints.
"""

import os
import sys
import time
import json
import requests
import hashlib
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn, TimeElapsedColumn
from rich import box

# Import real binary downloader
sys.path.append(str(Path(__file__).parent))
import importlib.util
spec = importlib.util.spec_from_file_location("download_binaries", Path(__file__).parent / "download-binaries.py")
download_binaries = importlib.util.module_from_spec(spec)
spec.loader.exec_module(download_binaries)
BinaryDownloader = download_binaries.BinaryDownloader

# Import service URL manager
sys.path.append(str(Path(__file__).parent.parent))
try:
    from config.service_urls import ServiceURLManager
except ImportError:
    # Fallback to direct URL configuration
    class ServiceURLManager:
        def get_service_url(self, service):
            urls = {
                'api': 'http://api.turdparty.localhost',
                'elasticsearch': 'http://elasticsearch.turdparty.localhost',
                'kibana': 'http://kibana.turdparty.localhost'
            }
            return urls.get(service, f'http://{service}.turdparty.localhost')


class RealTurdPartyWorkflow:
    """Real TurdParty workflow implementation using API endpoints."""
    
    def __init__(self):
        self.console = Console()
        self.url_manager = ServiceURLManager()
        self.api_base_url = self.url_manager.get_service_url('api')
        self.downloader = BinaryDownloader()
        
        # Track workflow state
        self.workflow_results = {}
        self.total_files_processed = 0
        self.total_workflows_created = 0
        
        self.console.print(f"[green]✅ Initialized TurdParty Workflow[/green]")
        self.console.print(f"[cyan]🔗 API Base URL: {self.api_base_url}[/cyan]")
        self.console.print(f"[cyan]🔍 OS Detection: Auto-detects Windows PE vs Linux ELF binaries[/cyan]")
    
    def print_header(self):
        """Print workflow header."""
        header_text = "🎉💩🥳 Real TurdParty Workflow 🥳💩🎉"
        self.console.print(Panel(header_text, box=box.DOUBLE, padding=(1, 2), style="bold magenta"))
        
        info_table = Table(show_header=False, box=box.SIMPLE)
        info_table.add_column("Key", style="cyan")
        info_table.add_column("Value", style="white")
        
        info_table.add_row("🎯 Workflow", "Download → Upload → Workflow → VM → Inject → Monitor")
        info_table.add_row("📊 Pipeline", "API Endpoints with OS Detection")
        info_table.add_row("🔍 Monitoring", "Windows/Linux VM Execution")
        info_table.add_row("📋 Events", "Fibratus Data")
        info_table.add_row("⏰ Started", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        self.console.print(info_table)
        self.console.print()
    
    def phase_1_download_binary(self, binary_name: str) -> Dict[str, Any]:
        """Phase 1: Download binary."""
        self.console.print(f"[bold blue]📥 Phase 1: Downloading {binary_name}...[/bold blue]")
        
        try:
            download_result = self.downloader.download_binary(binary_name)
            
            if download_result["success"]:
                self.console.print(f"[green]✅ Downloaded: {download_result['file_size']:,} bytes[/green]")
                return download_result
            else:
                self.console.print(f"[red]❌ Download failed: {download_result['error']}[/red]")
                return download_result
                
        except Exception as e:
            self.console.print(f"[red]❌ Download error: {e}[/red]")
            return {"success": False, "error": str(e)}
    
    def phase_2_upload_to_api(self, download_result: Dict[str, Any], binary_name: str) -> Dict[str, Any]:
        """Phase 2: Upload binary to TurdParty API (MinIO storage)."""
        self.console.print(f"[bold blue]📤 Phase 2: Uploading {binary_name} to API...[/bold blue]")
        
        try:
            file_path = download_result["file_path"]
            filename = download_result["filename"]
            
            # Prepare multipart form data
            with open(file_path, "rb") as f:
                files = {
                    "file": (filename, f, "application/octet-stream")
                }
                data = {
                    "description": f"Real binary analysis: {download_result['description']}"
                }
                
                # Upload to API
                upload_url = f"{self.api_base_url}/api/v1/files/upload"
                self.console.print(f"[cyan]🔗 Uploading to: {upload_url}[/cyan]")
                
                response = requests.post(
                    upload_url,
                    files=files,
                    data=data,
                    timeout=300  # 5 minute timeout for large files
                )
                
                if response.status_code in [200, 201]:
                    upload_result = response.json()
                    self.console.print(f"[green]✅ Upload successful: File ID {upload_result['file_id']}[/green]")
                    return {
                        "success": True,
                        "file_id": upload_result["file_id"],
                        "api_response": upload_result
                    }
                else:
                    error_msg = f"API upload failed: {response.status_code} - {response.text}"
                    self.console.print(f"[red]❌ {error_msg}[/red]")
                    return {"success": False, "error": error_msg}
                    
        except Exception as e:
            error_msg = f"Upload error: {e}"
            self.console.print(f"[red]❌ {error_msg}[/red]")
            return {"success": False, "error": error_msg}
    
    def phase_3_create_workflow(self, file_id: str, binary_name: str) -> Dict[str, Any]:
        """Phase 3: Create workflow job via API."""
        self.console.print(f"[bold blue]🔄 Phase 3: Creating workflow for {binary_name}...[/bold blue]")
        
        try:
            workflow_url = f"{self.api_base_url}/api/v1/workflow/start"
            
            # Prepare workflow data
            workflow_data = {
                "file_id": file_id,
                "vm_template": "windows-analysis",
                "vm_memory_mb": 2048,
                "vm_cpus": 2,
                "injection_path": "C:\\temp\\analysis.exe",
                "description": f"Real workflow analysis of {binary_name}"
            }
            
            self.console.print(f"[cyan]🔗 Creating workflow: {workflow_url}[/cyan]")
            
            response = requests.post(
                workflow_url,
                data=workflow_data,
                timeout=60
            )
            
            if response.status_code in [200, 201]:
                workflow_result = response.json()
                workflow_id = workflow_result["workflow_job_id"]
                self.console.print(f"[green]✅ Workflow created: {workflow_id}[/green]")
                return {
                    "success": True,
                    "workflow_id": workflow_id,
                    "api_response": workflow_result
                }
            else:
                error_msg = f"Workflow creation failed: {response.status_code} - {response.text}"
                self.console.print(f"[red]❌ {error_msg}[/red]")
                return {"success": False, "error": error_msg}
                
        except Exception as e:
            error_msg = f"Workflow creation error: {e}"
            self.console.print(f"[red]❌ {error_msg}[/red]")
            return {"success": False, "error": error_msg}
    
    def _detect_binary_os(self, file_path: str) -> str:
        """Detect if binary is Windows or Linux based on file header."""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(4)
                if header.startswith(b'MZ'):
                    return "windows"  # PE executable
                elif header.startswith(b'\x7fELF'):
                    return "linux"    # ELF executable
                else:
                    # Default to Windows for unknown formats (most malware is Windows)
                    return "windows"
        except Exception:
            return "windows"  # Default fallback

    def phase_4_create_vm(self, workflow_id: str, binary_name: str, download_result: Dict[str, Any]) -> Dict[str, Any]:
        """Phase 4: Create VM for binary analysis with OS detection."""

        # Detect binary OS type
        file_path = download_result.get("file_path")
        binary_os = self._detect_binary_os(file_path) if file_path else "windows"

        vm_type_display = "Windows" if binary_os == "windows" else "Linux"
        self.console.print(f"[bold blue]🖥️ Phase 4: Creating {vm_type_display} VM for {binary_name}...[/bold blue]")
        self.console.print(f"[cyan]🔍 Detected binary type: {binary_os.upper()}[/cyan]")

        try:
            vm_url = f"{self.api_base_url}/api/v1/vms/"

            # VM configuration based on binary OS type
            if binary_os == "windows":
                # Windows VM configuration - use 10B Packer template
                vm_config = {
                    "name": f"turdparty-win-{binary_name}-{int(time.time())}",
                    "vm_type": "vagrant",  # Use Vagrant for Windows VMs
                    "template": "10Baht/windows10-turdparty",  # Use 10Baht built template
                    "memory_mb": 4096,  # 4GB RAM for Windows analysis
                    "cpus": 2,
                    "disk_gb": 40,
                    "domain": "TurdParty",  # Required domain field
                    "description": f"Windows 10 VM for {binary_name} analysis (10Baht Packer built)",
                    "auto_start": True
                }
                self.console.print(f"[green]🪟 Using 10Baht Windows 10 template (10Baht/windows10-turdparty)[/green]")
            else:
                # Linux VM configuration
                vm_config = {
                    "name": f"turdparty-lin-{binary_name}-{int(time.time())}",
                    "vm_type": "docker",  # Use Docker for Linux
                    "template": "ubuntu:20.04",  # Ubuntu 20.04 template
                    "memory_mb": 2048,  # 2GB RAM for Linux
                    "cpus": 2,
                    "disk_gb": 20,
                    "domain": "TurdParty",  # Required domain field
                    "description": f"Ubuntu VM for {binary_name} analysis",
                    "auto_start": True
                }
                self.console.print(f"[green]🐧 Using Ubuntu template for Linux binary[/green]")

            self.console.print(f"[cyan]🔗 Creating VM: {vm_url}[/cyan]")
            self.console.print(f"[cyan]📋 Template: {vm_config['template']}[/cyan]")

            response = requests.post(
                vm_url,
                json=vm_config,
                timeout=300  # 5 minute timeout for VM creation
            )

            if response.status_code in [200, 201]:
                vm_result = response.json()
                vm_id = vm_result["vm_id"]
                self.console.print(f"[green]✅ VM created: {vm_id}[/green]")

                # Wait for VM to be ready
                vm_ready = self._wait_for_vm_ready(vm_id, timeout_minutes=10)

                if vm_ready["success"]:
                    return {
                        "success": True,
                        "vm_id": vm_id,
                        "vm_os": binary_os,
                        "vm_info": vm_result,
                        "ready_info": vm_ready
                    }
                else:
                    return {
                        "success": False,
                        "error": f"VM creation timeout: {vm_ready['error']}",
                        "vm_id": vm_id
                    }
            else:
                error_msg = f"VM creation failed: {response.status_code} - {response.text}"
                self.console.print(f"[red]❌ {error_msg}[/red]")
                return {"success": False, "error": error_msg}

        except Exception as e:
            error_msg = f"VM creation error: {e}"
            self.console.print(f"[red]❌ {error_msg}[/red]")
            return {"success": False, "error": error_msg}

    def _wait_for_vm_ready(self, vm_id: str, timeout_minutes: int = 10) -> Dict[str, Any]:
        """Wait for VM to be ready for file injection with detailed progress."""
        self.console.print(f"[yellow]⏳ Waiting for VM {vm_id} to be ready...[/yellow]")

        max_attempts = timeout_minutes * 4  # Check every 15 seconds for more frequent updates
        attempt = 0
        last_status = None

        # Enhanced progress tracking with sub-stages
        status_progression = {
            "creating": {
                "progress": 25,
                "substages": ["initializing", "allocating_resources", "configuring_vm", "preparing_image"]
            },
            "provisioning": {
                "progress": 50,
                "substages": ["installing_os", "configuring_network", "installing_tools", "setting_up_monitoring"]
            },
            "starting": {
                "progress": 75,
                "substages": ["booting_vm", "loading_services", "establishing_network", "finalizing_setup"]
            },
            "running": {
                "progress": 100,
                "substages": ["vm_ready", "services_active", "monitoring_active", "ready_for_injection"]
            }
        }

        while attempt < max_attempts:
            try:
                response = requests.get(f"{self.api_base_url}/api/v1/vms/{vm_id}", timeout=30)

                if response.status_code == 200:
                    vm_data = response.json()
                    status = vm_data.get("status", "unknown")

                    # Show status change with enhanced progress
                    if status != last_status:
                        if status in status_progression:
                            stage_info = status_progression[status]
                            progress = stage_info["progress"]

                            # Show main progress
                            self.console.print(f"[cyan]🔄 VM Status: {status.upper()} ({progress}% complete)[/cyan]")

                            # Show substage information
                            substages = stage_info["substages"]
                            substage_progress = (attempt % 4) + 1  # Cycle through substages
                            if substage_progress <= len(substages):
                                current_substage = substages[substage_progress - 1]
                                substage_percent = progress - 25 + (substage_progress / len(substages) * 25)
                                self.console.print(f"[dim cyan]   └─ {current_substage.replace('_', ' ').title()} ({substage_percent:.1f}%)[/dim cyan]")
                        else:
                            self.console.print(f"[cyan]📊 VM Status: {status.upper()}[/cyan]")
                        last_status = status
                    else:
                        # Show periodic heartbeat with substage updates
                        elapsed_minutes = (attempt * 15) // 60
                        elapsed_seconds = (attempt * 15) % 60

                        if status in status_progression:
                            stage_info = status_progression[status]
                            substages = stage_info["substages"]
                            substage_index = (attempt % len(substages))
                            current_substage = substages[substage_index]

                            # Calculate more granular progress within the stage
                            base_progress = stage_info["progress"] - 25
                            substage_progress = base_progress + ((attempt % 4) / 4 * 25)

                            self.console.print(f"[dim cyan]⏱️ {current_substage.replace('_', ' ').title()} - {elapsed_minutes:02d}:{elapsed_seconds:02d} ({substage_progress:.1f}%)[/dim cyan]")
                        else:
                            self.console.print(f"[dim cyan]⏱️ Still {status} - {elapsed_minutes:02d}:{elapsed_seconds:02d} elapsed[/dim cyan]")

                    if status == "running":
                        ip_address = vm_data.get("ip_address")
                        if ip_address:
                            self.console.print(f"[green]✅ VM ready! IP: {ip_address}[/green]")
                            return {
                                "success": True,
                                "status": status,
                                "ip_address": ip_address,
                                "vm_data": vm_data
                            }
                        else:
                            self.console.print(f"[yellow]⚠️ VM running but no IP address yet...[/yellow]")
                    elif status in ["error", "failed"]:
                        error_details = vm_data.get("error_message", "Unknown error")
                        self.console.print(f"[red]❌ VM failed: {error_details}[/red]")
                        return {
                            "success": False,
                            "error": f"VM failed with status: {status} - {error_details}",
                            "vm_data": vm_data
                        }
                else:
                    self.console.print(f"[yellow]⚠️ API error {response.status_code}: {response.text}[/yellow]")

                time.sleep(15)  # Check every 15 seconds for more frequent updates
                attempt += 1

            except Exception as e:
                self.console.print(f"[yellow]⚠️ VM status check error: {e}[/yellow]")
                time.sleep(15)
                attempt += 1

        return {
            "success": False,
            "error": f"VM readiness timeout after {timeout_minutes} minutes"
        }

    def phase_5_inject_file(self, vm_id: str, file_id: str, binary_name: str, vm_os: str = "linux") -> Dict[str, Any]:
        """Phase 5: Inject file from MinIO into VM with OS-appropriate paths."""
        vm_type_display = "Windows" if vm_os == "windows" else "Linux"
        self.console.print(f"[bold blue]💉 Phase 5: Injecting {binary_name} into {vm_type_display} VM...[/bold blue]")

        try:
            # First, get file download URL from API
            file_url = f"{self.api_base_url}/api/v1/files/{file_id}/download-url"
            file_response = requests.get(file_url, timeout=60)

            if file_response.status_code != 200:
                return {
                    "success": False,
                    "error": f"Failed to get file download URL: {file_response.status_code}"
                }

            download_info = file_response.json()

            # Inject file into VM
            injection_url = f"{self.api_base_url}/api/v1/vms/{vm_id}/files/upload"

            # Set appropriate target path based on VM OS
            if vm_os == "windows":
                target_path = f"C:\\TurdParty\\{binary_name}.exe"  # Use TurdParty directory
                self.console.print(f"[cyan]📂 Windows target path: {target_path}[/cyan]")
            else:
                target_path = f"/tmp/{binary_name}"  # Remove .exe for Linux
                self.console.print(f"[cyan]📂 Linux target path: {target_path}[/cyan]")

            injection_data = {
                "source_url": download_info.get("download_url"),
                "target_path": target_path,
                "description": f"Injecting {binary_name} for {vm_os} analysis"
            }

            self.console.print(f"[cyan]🔗 Injecting file: {injection_url}[/cyan]")

            response = requests.post(
                injection_url,
                json=injection_data,
                timeout=300  # 5 minute timeout
            )

            if response.status_code in [200, 201]:
                injection_result = response.json()
                self.console.print(f"[green]✅ File injected successfully[/green]")
                return {
                    "success": True,
                    "injection_result": injection_result,
                    "target_path": injection_data["target_path"]
                }
            else:
                error_msg = f"File injection failed: {response.status_code} - {response.text}"
                self.console.print(f"[red]❌ {error_msg}[/red]")
                return {"success": False, "error": error_msg}

        except Exception as e:
            error_msg = f"File injection error: {e}"
            self.console.print(f"[red]❌ {error_msg}[/red]")
            return {"success": False, "error": error_msg}

    def phase_6_monitor_workflow(self, workflow_id: str, binary_name: str) -> Dict[str, Any]:
        """Phase 6: Monitor workflow progress via API with detailed updates."""
        self.console.print(f"[bold blue]👁️ Phase 6: Monitoring workflow {workflow_id}...[/bold blue]")

        try:
            status_url = f"{self.api_base_url}/api/v1/workflow/{workflow_id}"

            max_attempts = 120  # 30 minutes max (15 second intervals for more frequent updates)
            attempt = 0
            last_step = None
            last_progress = 0

            self.console.print(f"[cyan]🔗 Monitoring URL: {status_url}[/cyan]")

            while attempt < max_attempts:
                try:
                    response = requests.get(status_url, timeout=30)

                    if response.status_code == 200:
                        status_data = response.json()
                        current_progress = status_data.get("progress", 0)
                        current_step = status_data.get("current_step", "unknown")
                        status = status_data.get("status", "unknown")

                        # Ensure progress is numeric
                        try:
                            progress_value = float(current_progress) if current_progress else 0
                        except (ValueError, TypeError):
                            progress_value = attempt * 0.8  # Fallback progress based on time

                        # Show detailed status updates with enhanced granularity
                        elapsed_minutes = (attempt * 15) // 60
                        elapsed_seconds = (attempt * 15) % 60

                        # Define workflow substeps for more granular progress
                        workflow_substeps = {
                            "file_injection": ["preparing_injection", "transferring_file", "setting_permissions", "validating_transfer"],
                            "vm_execution": ["starting_execution", "monitoring_processes", "collecting_events", "analyzing_behavior"],
                            "data_collection": ["gathering_logs", "processing_events", "generating_reports", "finalizing_results"],
                            "monitoring": ["system_monitoring", "network_analysis", "file_changes", "registry_monitoring"]
                        }

                        # Calculate more granular progress
                        if current_step in workflow_substeps:
                            substeps = workflow_substeps[current_step]
                            substep_index = (attempt % len(substeps))
                            current_substep = substeps[substep_index]

                            # Enhanced progress calculation
                            base_progress = progress_value
                            substep_progress = base_progress + ((attempt % 4) / 4 * 5)  # Add up to 5% within substep

                            if current_step != last_step or abs(substep_progress - last_progress) > 2:
                                self.console.print(f"[cyan]🔄 {current_step.replace('_', ' ').title()}: {current_substep.replace('_', ' ').title()} ({substep_progress:.1f}%) - {elapsed_minutes:02d}:{elapsed_seconds:02d}[/cyan]")
                                last_step = current_step
                                last_progress = substep_progress
                            else:
                                # Periodic heartbeat with substep detail
                                if attempt % 4 == 0:  # Every minute
                                    self.console.print(f"[dim cyan]⏱️ {current_substep.replace('_', ' ').title()} - {elapsed_minutes:02d}:{elapsed_seconds:02d} ({substep_progress:.1f}%)[/dim cyan]")
                        else:
                            # Fallback for unknown steps
                            if current_step != last_step or abs(progress_value - last_progress) > 5:
                                self.console.print(f"[cyan]🔄 Step: {current_step} ({progress_value:.1f}%) - {elapsed_minutes:02d}:{elapsed_seconds:02d}[/cyan]")
                                last_step = current_step
                                last_progress = progress_value
                            else:
                                # Periodic heartbeat
                                if attempt % 4 == 0:  # Every minute
                                    self.console.print(f"[dim cyan]⏱️ Still {current_step} - {elapsed_minutes:02d}:{elapsed_seconds:02d} elapsed[/dim cyan]")

                        # Check if workflow is complete
                        if status in ["completed", "failed", "cancelled"]:
                            if status == "completed":
                                self.console.print(f"[green]✅ Workflow completed successfully after {elapsed_minutes:02d}:{elapsed_seconds:02d}[/green]")
                                return {
                                    "success": True,
                                    "status": status,
                                    "final_data": status_data
                                }
                            else:
                                error_details = status_data.get("error_message", "Unknown error")
                                self.console.print(f"[red]❌ Workflow {status}: {error_details}[/red]")
                                return {
                                    "success": False,
                                    "status": status,
                                    "error": f"Workflow {status}: {error_details}",
                                    "final_data": status_data
                                }

                        # Continue monitoring
                        time.sleep(15)  # Check every 15 seconds for more frequent updates
                        attempt += 1

                    elif response.status_code == 404:
                        self.console.print(f"[red]❌ Workflow not found: {workflow_id}[/red]")
                        return {
                            "success": False,
                            "error": "Workflow not found",
                            "status": "not_found"
                        }
                    else:
                        self.console.print(f"[yellow]⚠️ API error {response.status_code}: {response.text[:100]}...[/yellow]")
                        time.sleep(15)
                        attempt += 1

                except Exception as e:
                    self.console.print(f"[yellow]⚠️ Monitoring error: {e}[/yellow]")
                    time.sleep(15)
                    attempt += 1

            # Timeout reached
            elapsed_minutes = (max_attempts * 15) // 60
            self.console.print(f"[red]❌ Workflow monitoring timeout after {elapsed_minutes} minutes[/red]")
            return {
                "success": False,
                "error": f"Monitoring timeout after {elapsed_minutes} minutes",
                "status": "timeout"
            }

        except Exception as e:
            error_msg = f"Monitoring error: {e}"
            self.console.print(f"[red]❌ {error_msg}[/red]")
            return {"success": False, "error": error_msg}
    
    def process_binary(self, binary_name: str) -> Dict[str, Any]:
        """Process a single binary through the complete workflow with detailed progress."""
        self.console.print(f"\n[bold magenta]🚀 Processing: {binary_name}[/bold magenta]")
        self.console.print("=" * 80)

        # Show workflow phases overview
        phases = [
            "📥 Phase 1: Download Binary",
            "📤 Phase 2: Upload to API",
            "🔄 Phase 3: Create Workflow",
            "🖥️ Phase 4: Create VM",
            "💉 Phase 5: Inject File",
            "👁️ Phase 6: Monitor Execution"
        ]

        self.console.print("[cyan]Workflow Phases:[/cyan]")
        for i, phase in enumerate(phases, 1):
            self.console.print(f"  {phase}")
        self.console.print()
        
        result = {
            "binary_name": binary_name,
            "success": False,
            "phases": {}
        }
        
        # Phase 1: Download
        download_result = self.phase_1_download_binary(binary_name)
        result["phases"]["download"] = download_result
        
        if not download_result["success"]:
            return result
        
        # Phase 2: Upload to API
        upload_result = self.phase_2_upload_to_api(download_result, binary_name)
        result["phases"]["upload"] = upload_result
        
        if not upload_result["success"]:
            return result
        
        # Phase 3: Create Workflow
        workflow_result = self.phase_3_create_workflow(upload_result["file_id"], binary_name)
        result["phases"]["workflow"] = workflow_result
        
        if not workflow_result["success"]:
            return result
        
        # Phase 4: Create VM (with OS detection)
        vm_result = self.phase_4_create_vm(workflow_result["workflow_id"], binary_name, download_result)
        result["phases"]["vm_creation"] = vm_result

        if not vm_result["success"]:
            return result

        # Phase 5: Inject File (with OS-appropriate paths)
        vm_os = vm_result.get("vm_os", "linux")
        injection_result = self.phase_5_inject_file(
            vm_result["vm_id"], upload_result["file_id"], binary_name, vm_os)
        result["phases"]["file_injection"] = injection_result

        if not injection_result["success"]:
            return result

        # Phase 6: Monitor Workflow
        monitor_result = self.phase_6_monitor_workflow(workflow_result["workflow_id"], binary_name)
        result["phases"]["monitor"] = monitor_result

        result["success"] = monitor_result["success"]
        
        if result["success"]:
            self.total_workflows_created += 1
            self.console.print(f"[bold green]🎉 {binary_name} completed successfully![/bold green]")
        else:
            self.console.print(f"[bold red]❌ {binary_name} failed[/bold red]")
        
        self.total_files_processed += 1
        return result


def main():
    """Main execution function."""
    try:
        workflow = RealTurdPartyWorkflow()
        workflow.print_header()
        
        # Test with VLC binary first (as specified in PRD)
        test_binary = "vlc"
        
        workflow.console.print(f"[bold cyan]🎯 Testing Workflow with {test_binary}[/bold cyan]")
        
        result = workflow.process_binary(test_binary)
        
        # Print final summary
        workflow.console.print("\n" + "=" * 80)
        workflow.console.print("[bold magenta]📊 FINAL SUMMARY[/bold magenta]")
        
        summary_table = Table(show_header=False, box=box.SIMPLE)
        summary_table.add_column("Metric", style="cyan")
        summary_table.add_column("Value", style="white")
        
        summary_table.add_row("Binary Tested", test_binary)
        summary_table.add_row("Overall Success", "✅ Yes" if result["success"] else "❌ No")
        summary_table.add_row("Files Processed", str(workflow.total_files_processed))
        summary_table.add_row("Workflows Created", str(workflow.total_workflows_created))
        
        workflow.console.print(summary_table)
        
        # Print phase details
        for phase_name, phase_result in result["phases"].items():
            status = "✅" if phase_result.get("success") else "❌"
            workflow.console.print(f"  {phase_name.title()}: {status}")
        
        return 0 if result["success"] else 1
        
    except KeyboardInterrupt:
        console = Console()
        console.print("\n[red]❌ Workflow interrupted by user[/red]")
        return 1
    except Exception as e:
        console = Console()
        console.print(f"\n[red]❌ Workflow failed: {e}[/red]")
        return 1


if __name__ == "__main__":
    exit(main())
