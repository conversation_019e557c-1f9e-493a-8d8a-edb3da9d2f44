#!/bin/bash

# TurdParty Services Rebuild Script
# Rebuilds all services with Traefik domain registration

set -e

# ANSI color codes
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print banner
print_banner() {
    echo ""
    print_colored $CYAN "╔══════════════════════════════════════════════════════════════╗"
    print_colored $CYAN "║              🚀 TurdParty Services Rebuild                  ║"
    print_colored $CYAN "║         Registering with servicename.turdparty.localhost    ║"
    print_colored $CYAN "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to check prerequisites
check_prerequisites() {
    print_colored $PURPLE "🔍 Checking Prerequisites..."
    echo ""
    
    # Check Docker
    if ! command -v docker >/dev/null 2>&1; then
        print_colored $RED "❌ Docker not found. Please install Docker."
        exit 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        print_colored $RED "❌ Docker daemon not running. Please start Docker."
        exit 1
    fi
    
    print_colored $GREEN "✅ Docker available: $(docker --version)"
    
    # Check Docker Compose (try both docker-compose and docker compose)
    if command -v docker-compose >/dev/null 2>&1; then
        DOCKER_COMPOSE="docker-compose"
        print_colored $GREEN "✅ Docker Compose available: $(docker-compose --version)"
    elif docker compose version >/dev/null 2>&1; then
        DOCKER_COMPOSE="docker compose"
        print_colored $GREEN "✅ Docker Compose available: $(docker compose version)"
    else
        print_colored $RED "❌ Docker Compose not found. Please install Docker Compose."
        exit 1
    fi
    
    # Check if we're in the right directory
    if [ ! -f "compose/docker-compose.yml" ]; then
        print_colored $RED "❌ Please run this script from the project root directory."
        exit 1
    fi
    
    print_colored $GREEN "✅ Project structure verified"
    echo ""
}

# Function to create Traefik network if it doesn't exist
create_traefik_network() {
    print_colored $PURPLE "🌐 Setting up Traefik Network..."
    echo ""
    
    if ! docker network ls | grep -q "traefik_network"; then
        print_colored $BLUE "📡 Creating traefik_network..."
        docker network create traefik_network
        print_colored $GREEN "✅ traefik_network created"
    else
        print_colored $GREEN "✅ traefik_network already exists"
    fi
    echo ""
}

# Function to stop existing services
stop_services() {
    print_colored $PURPLE "🛑 Stopping Existing Services..."
    echo ""
    
    # Stop all compose files
    print_colored $BLUE "🔄 Stopping core services..."
    $DOCKER_COMPOSE -f compose/docker-compose.yml down || true

    print_colored $BLUE "🔄 Stopping worker services..."
    $DOCKER_COMPOSE -f compose/docker-compose.workers.yml down || true

    print_colored $BLUE "🔄 Stopping ELK services..."
    $DOCKER_COMPOSE -f compose/docker-compose.elk.yml down || true
    
    print_colored $GREEN "✅ All services stopped"
    echo ""
}

# Function to clean up containers and images
cleanup_containers() {
    print_colored $PURPLE "🧹 Cleaning Up Containers and Images..."
    echo ""
    
    # Remove TurdParty containers
    print_colored $BLUE "🗑️ Removing TurdParty containers..."
    docker ps -a --filter "name=turdpartycollab_" --format "{{.ID}}" | xargs -r docker rm -f || true
    
    # Remove dangling images
    print_colored $BLUE "🗑️ Removing dangling images..."
    docker image prune -f || true
    
    # Remove unused networks (except traefik_network)
    print_colored $BLUE "🗑️ Cleaning up unused networks..."
    docker network prune -f || true
    
    print_colored $GREEN "✅ Cleanup completed"
    echo ""
}

# Function to build and start core services
start_core_services() {
    print_colored $PURPLE "🏗️ Building and Starting Core Services..."
    echo ""
    
    print_colored $BLUE "📦 Building core services..."
    $DOCKER_COMPOSE -f compose/docker-compose.yml build --no-cache

    print_colored $BLUE "🚀 Starting core services..."
    $DOCKER_COMPOSE -f compose/docker-compose.yml up -d
    
    # Wait for services to be healthy
    print_colored $BLUE "⏳ Waiting for core services to be healthy..."
    sleep 30
    
    # Check service health
    local healthy_services=0
    local total_services=5

    for service in api database cache storage frontend; do
        if $DOCKER_COMPOSE -f compose/docker-compose.yml ps $service | grep -q "healthy\|Up"; then
            print_colored $GREEN "✅ $service is healthy"
            healthy_services=$((healthy_services + 1))
        else
            print_colored $YELLOW "⚠️ $service is not healthy yet"
        fi
    done
    
    if [ $healthy_services -eq $total_services ]; then
        print_colored $GREEN "✅ All core services are healthy"
    else
        print_colored $YELLOW "⚠️ $healthy_services/$total_services core services are healthy"
    fi
    
    echo ""
}

# Function to start worker services
start_worker_services() {
    print_colored $PURPLE "👷 Building and Starting Worker Services..."
    echo ""
    
    print_colored $BLUE "📦 Building worker services..."
    $DOCKER_COMPOSE -f compose/docker-compose.workers.yml build --no-cache

    print_colored $BLUE "🚀 Starting worker services..."
    $DOCKER_COMPOSE -f compose/docker-compose.workers.yml up -d
    
    print_colored $GREEN "✅ Worker services started"
    echo ""
}

# Function to start ELK services
start_elk_services() {
    print_colored $PURPLE "📊 Building and Starting ELK Services..."
    echo ""
    
    print_colored $BLUE "📦 Building ELK services..."
    $DOCKER_COMPOSE -f compose/docker-compose.elk.yml build --no-cache

    print_colored $BLUE "🚀 Starting ELK services..."
    $DOCKER_COMPOSE -f compose/docker-compose.elk.yml up -d
    
    print_colored $GREEN "✅ ELK services started"
    echo ""
}

# Function to verify service registration
verify_services() {
    print_colored $PURPLE "🔍 Verifying Service Registration..."
    echo ""
    
    # List of expected services with their domains
    declare -A services=(
        ["api"]="api.turdparty.localhost"
        ["frontend"]="frontend.turdparty.localhost"
        ["storage"]="storage.turdparty.localhost"
        ["status"]="status.turdparty.localhost"
        ["flower"]="flower.turdparty.localhost"
        ["elasticsearch"]="elasticsearch.turdparty.localhost"
        ["logstash"]="logstash.turdparty.localhost"
        ["kibana"]="kibana.turdparty.localhost"
    )
    
    print_colored $BLUE "📋 Checking service containers..."
    for service in "${!services[@]}"; do
        local container_name="turdpartycollab_${service}"
        if docker ps --filter "name=${container_name}" --format "{{.Names}}" | grep -q "${container_name}"; then
            print_colored $GREEN "✅ ${service} container running"
        else
            print_colored $YELLOW "⚠️ ${service} container not found"
        fi
    done
    
    echo ""
    print_colored $BLUE "🌐 Expected service domains:"
    for service in "${!services[@]}"; do
        print_colored $CYAN "   🔗 ${services[$service]}"
    done
    
    echo ""
    print_colored $BLUE "📊 Service status summary:"
    $DOCKER_COMPOSE -f compose/docker-compose.yml ps
    echo ""
}

# Function to display access information
display_access_info() {
    print_colored $PURPLE "🌐 Service Access Information..."
    echo ""
    
    print_colored $CYAN "📋 TurdParty Services (servicename.turdparty.localhost):"
    echo ""
    
    print_colored $GREEN "🔧 Core Services:"
    print_colored $BLUE "   🌐 API:          http://api.turdparty.localhost"
    print_colored $BLUE "   💻 Frontend:     http://frontend.turdparty.localhost"
    print_colored $BLUE "   📁 Storage:      http://storage.turdparty.localhost"
    print_colored $BLUE "   📊 Status:       http://status.turdparty.localhost"
    echo ""
    
    print_colored $GREEN "👷 Worker Services:"
    print_colored $BLUE "   🌸 Flower:       http://flower.turdparty.localhost"
    echo ""
    
    print_colored $GREEN "📊 ELK Stack:"
    print_colored $BLUE "   🔍 Elasticsearch: http://elasticsearch.turdparty.localhost"
    print_colored $BLUE "   📝 Logstash:     http://logstash.turdparty.localhost"
    print_colored $BLUE "   📈 Kibana:       http://kibana.turdparty.localhost"
    echo ""
    
    print_colored $GREEN "🔗 Direct Access (localhost):"
    print_colored $BLUE "   🌐 API:          http://localhost:8000"
    print_colored $BLUE "   💻 Frontend:     http://localhost:3000"
    print_colored $BLUE "   📁 Storage:      http://localhost:9000"
    print_colored $BLUE "   📊 Status:       http://localhost:8090"
    print_colored $BLUE "   🌸 Flower:       http://localhost:5555"
    print_colored $BLUE "   📈 Kibana:       http://localhost:5601"
    echo ""
    
    print_colored $YELLOW "💡 Note: Traefik domains require Traefik to be running"
    print_colored $YELLOW "💡 Add entries to /etc/hosts if needed:"
    print_colored $CYAN "   127.0.0.1 api.turdparty.localhost"
    print_colored $CYAN "   127.0.0.1 frontend.turdparty.localhost"
    print_colored $CYAN "   127.0.0.1 storage.turdparty.localhost"
    print_colored $CYAN "   127.0.0.1 status.turdparty.localhost"
    print_colored $CYAN "   127.0.0.1 flower.turdparty.localhost"
    print_colored $CYAN "   127.0.0.1 elasticsearch.turdparty.localhost"
    print_colored $CYAN "   127.0.0.1 logstash.turdparty.localhost"
    print_colored $CYAN "   127.0.0.1 kibana.turdparty.localhost"
    echo ""
}

# Function to show logs
show_logs() {
    if [ "$1" = "--logs" ]; then
        print_colored $PURPLE "📋 Showing Service Logs..."
        echo ""
        
        print_colored $BLUE "🔍 Core service logs (last 20 lines):"
        $DOCKER_COMPOSE -f compose/docker-compose.yml logs --tail=20

        echo ""
        print_colored $BLUE "🔍 Worker service logs (last 20 lines):"
        $DOCKER_COMPOSE -f compose/docker-compose.workers.yml logs --tail=20
        
        echo ""
    fi
}

# Main function
main() {
    print_banner

    # Parse arguments
    local show_logs_flag=""
    if [ "$1" = "--logs" ]; then
        show_logs_flag="--logs"
    fi

    # CRITICAL: Check Traefik dependency FIRST
    print_colored $PURPLE "🔍 Checking Critical Dependencies..."
    echo ""

    # Source and run Traefik dependency check
    if [ -f "scripts/check-traefik-dependency.sh" ]; then
        source scripts/check-traefik-dependency.sh
        check_traefik_dependency true true  # Strict mode, exit on failure
    else
        print_colored $RED "❌ Traefik dependency checker not found!"
        print_colored $RED "🛑 Cannot proceed without dependency validation"
        exit 1
    fi

    # Execute rebuild steps
    check_prerequisites
    create_traefik_network
    stop_services
    cleanup_containers
    start_core_services
    start_worker_services
    start_elk_services
    verify_services
    display_access_info

    # Show logs if requested
    show_logs "$show_logs_flag"

    print_colored $GREEN "🎉 TurdParty services rebuild completed successfully!"
    print_colored $GREEN "🌐 All services registered with servicename.turdparty.localhost domains"
    print_colored $GREEN "🚀 System ready for malware analysis operations!"
    echo ""
}

# Show help
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    print_banner
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --logs    Show service logs after rebuild"
    echo "  --help    Show this help message"
    echo ""
    echo "This script rebuilds all TurdParty services with Traefik domain registration."
    echo "Services will be available at servicename.turdparty.localhost domains."
    echo ""
    exit 0
fi

# Run main function
main "$@"
