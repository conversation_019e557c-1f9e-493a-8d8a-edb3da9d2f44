#!/usr/bin/env python3
"""
🎉💩🥳 TurdParty Enhanced VM Pools - 10 Binary Analysis Pipeline 🥳💩🎉

This script runs the 10 binary analysis pipeline using the new enhanced VM provisioning
queue system with priority-based allocation, real-time monitoring, and comprehensive
telemetry collection.

Key Features:
    - Enhanced VM pool integration with priority allocation
    - Real binary downloads (no simulation)
    - Comprehensive ECS event generation and logging
    - Rich CLI output with real-time progress tracking
    - Integration with existing TurdParty infrastructure
    - Performance metrics and optimization insights

Enhanced VM Pool Features:
    - Priority-based VM allocation (Critical to Background)
    - Multi-template support (Windows 10, Ubuntu, Alpine)
    - Real-time pool status monitoring
    - Automatic pool replenishment and maintenance
    - Health checking and failure recovery
    - Performance analytics and reporting

Integration:
    - Uses ServiceURLManager for Traefik URL management
    - Leverages enhanced VM pool API endpoints
    - ECS-compliant logging to Elasticsearch
    - Real binary downloads via BinaryDownloader
    - Comprehensive telemetry and performance tracking
"""

import asyncio
import json
import time
import uuid
import hashlib
import sys
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional

import httpx
import requests
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.panel import Panel
from rich.text import Text
from rich.layout import Layout
from rich.live import Live
from rich import box

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import existing infrastructure
from utils.service_urls import ServiceURLManager

# Import real binary downloader
sys.path.append(str(Path(__file__).parent))
import importlib.util
spec = importlib.util.spec_from_file_location("download_binaries", Path(__file__).parent / "download-binaries.py")
download_binaries = importlib.util.module_from_spec(spec)
spec.loader.exec_module(download_binaries)
BinaryDownloader = download_binaries.BinaryDownloader


class EnhancedPoolBinaryAnalyzer:
    """Enhanced VM pool analyzer for 10 popular binaries with comprehensive reporting."""

    def __init__(self):
        self.console = Console()
        self.session_id = str(uuid.uuid4())
        
        # Initialize URL manager
        self.url_manager = ServiceURLManager('local')
        self.api_base = self.url_manager.get_service_url('api')
        self.elasticsearch_url = self.url_manager.get_service_url('elasticsearch')
        self.kibana_url = self.url_manager.get_service_url('kibana')
        
        # Initialize real binary downloader
        self.downloader = BinaryDownloader()
        
        # 10 popular binaries for analysis
        self.top_10_binaries = {
            "firefox": {
                "filename": "Firefox-Setup.exe",
                "description": "Mozilla Firefox browser",
                "file_size": 55000000,
                "expected_files": 30,
                "expected_registry": 22,
                "expected_processes": 4,
                "category": "Browser",
                "vm_template": "10Baht/windows10-turdparty",
                "priority": 2  # High priority
            },
            "notepadpp": {
                "filename": "npp.8.5.8.Installer.x64.exe",
                "description": "Notepad++ text editor",
                "file_size": 4796432,
                "expected_files": 18,
                "expected_registry": 8,
                "expected_processes": 2,
                "category": "Editor",
                "vm_template": "10Baht/windows10-turdparty",
                "priority": 3  # Normal priority
            },
            "7zip": {
                "filename": "7z2301-x64.exe",
                "description": "7-Zip file archiver",
                "file_size": 1468416,
                "expected_files": 12,
                "expected_registry": 15,
                "expected_processes": 2,
                "category": "Utility",
                "vm_template": "10Baht/windows10-turdparty",
                "priority": 3  # Normal priority
            },
            "putty": {
                "filename": "putty-64bit-0.81-installer.msi",
                "description": "PuTTY SSH client",
                "file_size": 3200000,
                "expected_files": 8,
                "expected_registry": 6,
                "expected_processes": 2,
                "category": "Network",
                "vm_template": "10Baht/windows10-turdparty",
                "priority": 4  # Low priority
            },
            "vlc": {
                "filename": "vlc-3.0.20-win64.exe",
                "description": "VLC media player",
                "file_size": 42000000,
                "expected_files": 35,
                "expected_registry": 18,
                "expected_processes": 3,
                "category": "Media",
                "vm_template": "10Baht/windows10-turdparty",
                "priority": 3  # Normal priority
            },
            "chrome": {
                "filename": "ChromeSetup.exe",
                "description": "Google Chrome browser",
                "file_size": 1500000,
                "expected_files": 25,
                "expected_registry": 18,
                "expected_processes": 3,
                "category": "Browser",
                "vm_template": "10Baht/windows10-turdparty",
                "priority": 2  # High priority
            },
            "vscode": {
                "filename": "VSCodeUserSetup-x64.exe",
                "description": "Visual Studio Code editor",
                "file_size": 95000000,
                "expected_files": 45,
                "expected_registry": 25,
                "expected_processes": 4,
                "category": "Development",
                "vm_template": "10Baht/windows10-turdparty",
                "priority": 1  # Critical priority
            },
            "git": {
                "filename": "Git-2.43.0-64-bit.exe",
                "description": "Git version control system",
                "file_size": 48000000,
                "expected_files": 65,
                "expected_registry": 12,
                "expected_processes": 3,
                "category": "Development",
                "vm_template": "ubuntu:20.04",
                "priority": 3  # Normal priority
            },
            "nodejs": {
                "filename": "node-v20.10.0-x64.msi",
                "description": "Node.js JavaScript runtime",
                "file_size": 28000000,
                "expected_files": 35,
                "expected_registry": 15,
                "expected_processes": 3,
                "category": "Development",
                "vm_template": "ubuntu:22.04",
                "priority": 3  # Normal priority
            },
            "python": {
                "filename": "python-3.12.1-amd64.exe",
                "description": "Python programming language",
                "file_size": 25000000,
                "expected_files": 120,
                "expected_registry": 20,
                "expected_processes": 5,
                "category": "Development",
                "vm_template": "alpine:latest",
                "priority": 4  # Low priority
            }
        }
        
        self.results = {}
        self.total_events_sent = 0
        self.total_reports_generated = 0
        self.vm_allocations = {}
        
        # Validate exactly 10 binaries
        if len(self.top_10_binaries) != 10:
            raise ValueError(f"Expected exactly 10 binaries, got {len(self.top_10_binaries)}")
        
        self.console.print(f"[green]✅ Enhanced VM Pool Analyzer Initialized[/green]")
        self.console.print(f"   Session ID: {self.session_id}")
        self.console.print(f"   Binaries: {len(self.top_10_binaries)}")
        self.console.print(f"   API Base: {self.api_base}")
    
    def print_header(self):
        """Print rich header with enhanced pool information."""
        header_text = Text("🎉💩🥳 TurdParty Enhanced VM Pools - 10 Binary Analysis Pipeline 🥳💩🎉", style="bold magenta")
        self.console.print(Panel(header_text, box=box.DOUBLE, padding=(1, 2)))
        
        info_table = Table(show_header=False, box=box.SIMPLE)
        info_table.add_column("Key", style="cyan")
        info_table.add_column("Value", style="white")
        
        info_table.add_row("🎯 Target", "10 Popular Development/Productivity Binaries")
        info_table.add_row("🏗️ Infrastructure", "Enhanced VM Provisioning Queues")
        info_table.add_row("📊 Pipeline", "Download → VM Allocate → Analyze → ECS Events → Reports")
        info_table.add_row("🔍 Monitoring", "Real-time telemetry and pool status")
        info_table.add_row("📋 Reports", "Comprehensive analysis with VM metrics")
        info_table.add_row("⏰ Started", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        self.console.print(info_table)
        self.console.print()
    
    async def check_enhanced_pool_status(self):
        """Check enhanced VM pool status before starting analysis."""
        self.console.print("[bold cyan]🔍 Checking Enhanced VM Pool Status...[/bold cyan]")
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(f"{self.api_base}/api/v1/pools/status")
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if "data" in data:
                        pool_data = data["data"]
                        
                        # Create pool status table
                        table = Table(title="📊 Enhanced VM Pool Status", box=box.ROUNDED)
                        table.add_column("Template", style="cyan")
                        table.add_column("Ready", style="green", justify="center")
                        table.add_column("Creating", style="yellow", justify="center")
                        table.add_column("Total", style="blue", justify="center")
                        table.add_column("Status", style="white")
                        
                        if "templates" in pool_data:
                            for template, status in pool_data["templates"].items():
                                pool_status = status.get("pool_status", "unknown")
                                status_color = "green" if pool_status == "healthy" else "yellow" if pool_status == "degraded" else "red"
                                
                                table.add_row(
                                    template,
                                    str(status.get("ready", 0)),
                                    str(status.get("creating", 0)),
                                    str(status.get("total", 0)),
                                    f"[{status_color}]{pool_status}[/{status_color}]"
                                )
                        
                        self.console.print(table)
                        
                        # Show overall status
                        if "overall" in pool_data:
                            overall = pool_data["overall"]
                            self.console.print(f"[green]✅ Overall Pool Status:[/green]")
                            self.console.print(f"   Ready VMs: {overall.get('ready', 0)}")
                            self.console.print(f"   Creating VMs: {overall.get('creating', 0)}")
                            self.console.print(f"   Total VMs: {overall.get('total', 0)}")
                            self.console.print(f"   Templates: {overall.get('templates', 0)}")
                        
                        return True
                else:
                    self.console.print(f"[red]❌ Pool status check failed: {response.status_code}[/red]")
                    return False
                    
        except Exception as e:
            self.console.print(f"[red]❌ Pool status check error: {e}[/red]")
            return False
    
    async def allocate_vm_from_enhanced_pool(self, binary_name: str, binary_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Allocate VM from enhanced pool with priority support."""
        template = binary_info["vm_template"]
        priority = binary_info["priority"]
        
        self.console.print(f"[bold yellow]🎯 Allocating VM for {binary_name}...[/bold yellow]")
        self.console.print(f"   Template: {template}")
        self.console.print(f"   Priority: {priority} ({'Critical' if priority == 1 else 'High' if priority == 2 else 'Normal' if priority == 3 else 'Low' if priority == 4 else 'Background'})")
        
        try:
            allocation_request = {
                "priority": priority,
                "requester_id": f"enhanced-binary-analyzer-{self.session_id}",
                "timeout_seconds": 300,
                "metadata": {
                    "session_id": self.session_id,
                    "binary_name": binary_name,
                    "analysis_type": "10_binary_pipeline",
                    "template": template
                }
            }
            
            allocation_start = time.time()
            
            async with httpx.AsyncClient(timeout=320.0) as client:
                response = await client.post(
                    f"{self.api_base}/api/v1/pools/{template}/allocate",
                    json=allocation_request
                )
                
                allocation_duration = time.time() - allocation_start
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if data.get("vm_id"):
                        # Immediate allocation successful
                        vm_info = {
                            "vm_id": data["vm_id"],
                            "template": template,
                            "status": "allocated",
                            "allocation_duration": allocation_duration,
                            "allocated_at": data.get("allocated_at", datetime.utcnow().isoformat())
                        }
                        
                        self.console.print(f"[green]✅ VM allocated immediately: {data['vm_id']}[/green]")
                        self.console.print(f"   Duration: {allocation_duration:.2f}s")
                        
                        return vm_info
                    elif data.get("request_id"):
                        # Queued for allocation
                        self.console.print(f"[yellow]⏳ VM allocation queued: {data['request_id']}[/yellow]")
                        self.console.print(f"   Queue position: {data.get('queue_position', 'unknown')}")
                        self.console.print(f"   Estimated wait: {data.get('estimated_wait_seconds', 'unknown')}s")
                        
                        # For now, return queue info (in production, would poll for completion)
                        return {
                            "request_id": data["request_id"],
                            "template": template,
                            "status": "queued",
                            "queue_position": data.get("queue_position"),
                            "estimated_wait": data.get("estimated_wait_seconds")
                        }
                    else:
                        self.console.print(f"[red]❌ Unexpected allocation response format[/red]")
                        return None
                else:
                    self.console.print(f"[red]❌ VM allocation failed: {response.status_code}[/red]")
                    self.console.print(f"   Response: {response.text[:100]}...")
                    return None
                    
        except Exception as e:
            self.console.print(f"[red]❌ VM allocation error: {e}[/red]")
            return None

    async def analyze_binary_with_enhanced_pools(self, binary_name: str) -> Dict[str, Any]:
        """Analyze a single binary using enhanced VM pools."""
        binary_info = self.top_10_binaries[binary_name]

        # Create analysis panel
        analysis_panel = Panel(
            f"[bold cyan]Analyzing: {binary_name}[/bold cyan]\n"
            f"Description: {binary_info['description']}\n"
            f"Category: {binary_info['category']}\n"
            f"VM Template: {binary_info['vm_template']}\n"
            f"Priority: {binary_info['priority']}",
            title=f"🔍 Binary Analysis: {binary_name}",
            box=box.ROUNDED
        )
        self.console.print(analysis_panel)

        analysis_start = time.time()
        result = {"binary_name": binary_name, "success": False}

        try:
            # Step 1: Allocate VM from enhanced pool
            vm_info = await self.allocate_vm_from_enhanced_pool(binary_name, binary_info)
            if not vm_info:
                result["error"] = "VM allocation failed"
                return result

            result["vm_info"] = vm_info

            # Step 2: Download binary
            download_result = await self.download_binary(binary_name, binary_info)
            if not download_result.get("success", False):
                result["error"] = f"Download failed: {download_result.get('error', 'unknown')}"
                return result

            result["download_result"] = download_result

            # Step 3: Generate enhanced ECS events
            ecs_result = await self.generate_enhanced_ecs_events(binary_name, binary_info, download_result, vm_info)
            result["ecs_result"] = ecs_result

            # Step 4: Send events to Elasticsearch
            events_sent = await self.send_events_to_elasticsearch(ecs_result["events"])
            ecs_result["events_sent"] = events_sent
            result["events_sent"] = events_sent

            # Step 5: Generate enhanced report
            report_result = await self.generate_enhanced_report(binary_name, ecs_result, vm_info, download_result)
            result["report_result"] = report_result

            # Calculate analysis duration
            analysis_duration = time.time() - analysis_start
            result["analysis_duration"] = analysis_duration
            result["success"] = True

            self.console.print(f"[green]✅ Analysis completed for {binary_name} in {analysis_duration:.2f}s[/green]")
            self.console.print(f"   Events sent: {events_sent}/{ecs_result['event_count']}")

            return result

        except Exception as e:
            result["error"] = str(e)
            self.console.print(f"[red]❌ Analysis failed for {binary_name}: {e}[/red]")
            return result

    async def run_enhanced_10_binary_analysis(self):
        """Run the complete 10 binary analysis pipeline with enhanced VM pools."""
        self.print_header()

        # Check enhanced pool status first
        pool_status_ok = await self.check_enhanced_pool_status()
        if not pool_status_ok:
            self.console.print("[red]❌ Enhanced pool status check failed. Continuing anyway...[/red]")

        self.console.print()

        # Create binary overview table
        self.create_binary_overview_table()

        # Run analysis for each binary
        pipeline_start = time.time()

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=self.console
        ) as progress:

            main_task = progress.add_task("Enhanced 10 Binary Analysis Pipeline", total=len(self.top_10_binaries))

            for binary_name in self.top_10_binaries.keys():
                progress.update(main_task, description=f"Analyzing {binary_name}...")

                result = await self.analyze_binary_with_enhanced_pools(binary_name)
                self.results[binary_name] = result

                if result["success"]:
                    self.total_events_sent += result.get("events_sent", 0)
                    if result.get("report_result", {}).get("success", False):
                        self.total_reports_generated += 1

                progress.advance(main_task)
                self.console.print()

        # Generate final summary
        pipeline_duration = time.time() - pipeline_start
        await self.generate_final_summary(pipeline_duration)

    def create_binary_overview_table(self):
        """Create overview table of all binaries with enhanced pool information."""
        table = Table(title="📋 Enhanced VM Pool Binary Analysis Overview", box=box.ROUNDED)

        table.add_column("Binary", style="cyan", no_wrap=True)
        table.add_column("Description", style="white")
        table.add_column("VM Template", style="yellow")
        table.add_column("Priority", style="magenta", justify="center")
        table.add_column("Expected Events", style="blue", justify="center")

        for name, info in self.top_10_binaries.items():
            expected_events = info["expected_files"] + info["expected_registry"] + info["expected_processes"]
            priority_text = {1: "Critical", 2: "High", 3: "Normal", 4: "Low", 5: "Background"}[info["priority"]]

            table.add_row(
                name,
                info["description"],
                info["vm_template"],
                priority_text,
                str(expected_events)
            )

        self.console.print(table)
        self.console.print()

    async def generate_final_summary(self, pipeline_duration: float):
        """Generate final summary of the enhanced analysis pipeline."""
        successful_analyses = sum(1 for result in self.results.values() if result["success"])
        failed_analyses = len(self.results) - successful_analyses

        # Create summary table
        summary_table = Table(title="📊 Enhanced VM Pool Analysis Summary", box=box.DOUBLE)
        summary_table.add_column("Metric", style="cyan")
        summary_table.add_column("Value", style="white", justify="right")

        summary_table.add_row("Total Binaries", str(len(self.top_10_binaries)))
        summary_table.add_row("Successful Analyses", f"[green]{successful_analyses}[/green]")
        summary_table.add_row("Failed Analyses", f"[red]{failed_analyses}[/red]")
        summary_table.add_row("Total Events Sent", f"[blue]{self.total_events_sent}[/blue]")
        summary_table.add_row("Reports Generated", f"[yellow]{self.total_reports_generated}[/yellow]")
        summary_table.add_row("Pipeline Duration", f"{pipeline_duration:.2f}s")
        summary_table.add_row("Average per Binary", f"{pipeline_duration / len(self.top_10_binaries):.2f}s")

        self.console.print(summary_table)

        # Show VM allocation summary
        vm_allocations_summary = {}
        for result in self.results.values():
            if "vm_info" in result:
                template = result["vm_info"].get("template", "unknown")
                status = result["vm_info"].get("status", "unknown")
                key = f"{template}_{status}"
                vm_allocations_summary[key] = vm_allocations_summary.get(key, 0) + 1

        if vm_allocations_summary:
            vm_table = Table(title="🎯 VM Allocation Summary", box=box.ROUNDED)
            vm_table.add_column("Template", style="cyan")
            vm_table.add_column("Status", style="yellow")
            vm_table.add_column("Count", style="green", justify="center")

            for key, count in vm_allocations_summary.items():
                template, status = key.rsplit("_", 1)
                vm_table.add_row(template, status, str(count))

            self.console.print(vm_table)

        # Save final summary report
        summary_data = {
            "session_id": self.session_id,
            "pipeline_duration": pipeline_duration,
            "total_binaries": len(self.top_10_binaries),
            "successful_analyses": successful_analyses,
            "failed_analyses": failed_analyses,
            "total_events_sent": self.total_events_sent,
            "reports_generated": self.total_reports_generated,
            "vm_allocations_summary": vm_allocations_summary,
            "detailed_results": self.results,
            "timestamp": datetime.utcnow().isoformat()
        }

        summary_file = Path("/tmp/turdparty_enhanced_reports") / f"enhanced_10_binary_summary_{self.session_id[:8]}.json"
        summary_file.parent.mkdir(exist_ok=True)

        with open(summary_file, "w") as f:
            json.dump(summary_data, f, indent=2, default=str)

        self.console.print(f"\n[green]✅ Final summary saved: {summary_file}[/green]")
        self.console.print(f"[bold magenta]🎉 Enhanced VM Pool 10 Binary Analysis Pipeline Complete! 🎉[/bold magenta]")


async def main():
    """Main function to run enhanced VM pool 10 binary analysis."""
    analyzer = EnhancedPoolBinaryAnalyzer()
    await analyzer.run_enhanced_10_binary_analysis()


if __name__ == "__main__":
    asyncio.run(main())
