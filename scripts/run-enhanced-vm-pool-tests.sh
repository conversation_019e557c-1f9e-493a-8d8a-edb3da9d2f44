#!/bin/bash
# 🧪 Enhanced VM Pool Integration Test Runner
#
# Comprehensive test runner for the enhanced VM provisioning queue system
# with Elasticsearch logging, priority-based allocation, and multi-template support.
#
# Usage:
#   ./scripts/run-enhanced-vm-pool-tests.sh [test-type]
#
# Test Types:
#   all          - Run all test suites (default)
#   integration  - Run comprehensive integration tests
#   pytest       - Run pytest-compatible tests
#   performance  - Run performance benchmarks
#   quick        - Run quick smoke tests
#   elasticsearch - Test Elasticsearch logging only

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TEST_TYPE="${1:-all}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RESULTS_DIR="${PROJECT_ROOT}/test_results"
LOG_FILE="${RESULTS_DIR}/test_run_${TIMESTAMP}.log"

# Ensure results directory exists
mkdir -p "${RESULTS_DIR}"

echo -e "${PURPLE}🧪 Enhanced VM Pool Integration Test Runner${NC}"
echo -e "${CYAN}   Project Root: ${PROJECT_ROOT}${NC}"
echo -e "${CYAN}   Test Type: ${TEST_TYPE}${NC}"
echo -e "${CYAN}   Timestamp: ${TIMESTAMP}${NC}"
echo -e "${CYAN}   Results Dir: ${RESULTS_DIR}${NC}"
echo "=" | tr ' ' '=' | head -c 80
echo

# Function to log messages
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[${timestamp}] [${level}] ${message}" | tee -a "${LOG_FILE}"
}

# Function to check prerequisites
check_prerequisites() {
    log_message "INFO" "Checking prerequisites..."
    
    # Check if services are running
    local services=("api" "celery_worker" "elasticsearch" "redis" "database")
    local missing_services=()

    for service in "${services[@]}"; do
        if ! docker ps --format "table {{.Names}}" | grep -q "turdpartycollab_${service}"; then
            missing_services+=("$service")
        fi
    done
    
    if [ ${#missing_services[@]} -ne 0 ]; then
        echo -e "${RED}❌ Missing services: ${missing_services[*]}${NC}"
        echo -e "${YELLOW}💡 Start services with: docker-compose up -d${NC}"
        return 1
    fi
    
    # Check Python dependencies
    if ! python3 -c "import httpx, pytest, psutil" 2>/dev/null; then
        echo -e "${RED}❌ Missing Python dependencies${NC}"
        echo -e "${YELLOW}💡 Install with: pip install httpx pytest psutil${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ All prerequisites met${NC}"
    return 0
}

# Function to run comprehensive integration tests
run_integration_tests() {
    log_message "INFO" "Running comprehensive integration tests..."
    
    cd "${PROJECT_ROOT}"
    
    echo -e "${BLUE}🔍 Starting comprehensive integration test suite...${NC}"
    
    if python3 tests/integration/test_enhanced_vm_pools.py > "${RESULTS_DIR}/integration_${TIMESTAMP}.log" 2>&1; then
        echo -e "${GREEN}✅ Integration tests completed successfully${NC}"
        log_message "SUCCESS" "Integration tests passed"
        return 0
    else
        echo -e "${RED}❌ Integration tests failed${NC}"
        log_message "ERROR" "Integration tests failed"
        echo -e "${YELLOW}📄 Check log: ${RESULTS_DIR}/integration_${TIMESTAMP}.log${NC}"
        return 1
    fi
}

# Function to run pytest tests
run_pytest_tests() {
    log_message "INFO" "Running pytest integration tests..."
    
    cd "${PROJECT_ROOT}"
    
    echo -e "${BLUE}🧪 Starting pytest test suite...${NC}"
    
    if python3 -m pytest tests/integration/test_vm_pool_pytest.py -v --tb=short > "${RESULTS_DIR}/pytest_${TIMESTAMP}.log" 2>&1; then
        echo -e "${GREEN}✅ Pytest tests completed successfully${NC}"
        log_message "SUCCESS" "Pytest tests passed"
        return 0
    else
        echo -e "${RED}❌ Pytest tests failed${NC}"
        log_message "ERROR" "Pytest tests failed"
        echo -e "${YELLOW}📄 Check log: ${RESULTS_DIR}/pytest_${TIMESTAMP}.log${NC}"
        return 1
    fi
}

# Function to run performance benchmarks
run_performance_tests() {
    log_message "INFO" "Running performance benchmarks..."
    
    cd "${PROJECT_ROOT}"
    
    echo -e "${BLUE}📈 Starting performance benchmark suite...${NC}"
    echo -e "${YELLOW}⚠️ This may take several minutes...${NC}"
    
    if python3 tests/integration/test_vm_pool_performance.py > "${RESULTS_DIR}/performance_${TIMESTAMP}.log" 2>&1; then
        echo -e "${GREEN}✅ Performance benchmarks completed successfully${NC}"
        log_message "SUCCESS" "Performance benchmarks completed"
        return 0
    else
        echo -e "${RED}❌ Performance benchmarks failed${NC}"
        log_message "ERROR" "Performance benchmarks failed"
        echo -e "${YELLOW}📄 Check log: ${RESULTS_DIR}/performance_${TIMESTAMP}.log${NC}"
        return 1
    fi
}

# Function to run quick smoke tests
run_quick_tests() {
    log_message "INFO" "Running quick smoke tests..."
    
    cd "${PROJECT_ROOT}"
    
    echo -e "${BLUE}⚡ Starting quick smoke tests...${NC}"
    
    # Quick API health check
    if curl -s -f "http://localhost:8000/api/v1/pools/status" > /dev/null; then
        echo -e "${GREEN}✅ API health check passed${NC}"
    else
        echo -e "${RED}❌ API health check failed${NC}"
        return 1
    fi
    
    # Quick Elasticsearch check
    if curl -s -f "http://localhost:9200/_cluster/health" > /dev/null; then
        echo -e "${GREEN}✅ Elasticsearch health check passed${NC}"
    else
        echo -e "${RED}❌ Elasticsearch health check failed${NC}"
        return 1
    fi
    
    # Run subset of pytest tests
    if python3 -m pytest tests/integration/test_vm_pool_pytest.py::test_api_endpoints -v > "${RESULTS_DIR}/quick_${TIMESTAMP}.log" 2>&1; then
        echo -e "${GREEN}✅ Quick tests completed successfully${NC}"
        log_message "SUCCESS" "Quick tests passed"
        return 0
    else
        echo -e "${RED}❌ Quick tests failed${NC}"
        log_message "ERROR" "Quick tests failed"
        return 1
    fi
}

# Function to test Elasticsearch logging specifically
run_elasticsearch_tests() {
    log_message "INFO" "Running Elasticsearch logging tests..."
    
    cd "${PROJECT_ROOT}"
    
    echo -e "${BLUE}📊 Testing Elasticsearch logging integration...${NC}"
    
    # Check if Celery logs are being indexed
    local today=$(date +"%Y.%m.%d")
    local index_name="ecs-turdparty-celery-${today}"
    
    if curl -s -f "http://localhost:9200/${index_name}" > /dev/null; then
        echo -e "${GREEN}✅ Celery logs index exists${NC}"
        
        # Check for recent logs
        local log_count=$(curl -s "http://localhost:9200/${index_name}/_count" | python3 -c "import json, sys; print(json.load(sys.stdin)['count'])" 2>/dev/null || echo "0")
        
        if [ "$log_count" -gt 0 ]; then
            echo -e "${GREEN}✅ Found ${log_count} log entries${NC}"
        else
            echo -e "${YELLOW}⚠️ No log entries found (may be normal for new deployment)${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ Celery logs index not found (may be normal for new deployment)${NC}"
    fi
    
    # Run Elasticsearch-specific pytest tests
    if python3 -m pytest tests/integration/test_vm_pool_pytest.py::test_elasticsearch_logging -v > "${RESULTS_DIR}/elasticsearch_${TIMESTAMP}.log" 2>&1; then
        echo -e "${GREEN}✅ Elasticsearch tests completed successfully${NC}"
        log_message "SUCCESS" "Elasticsearch tests passed"
        return 0
    else
        echo -e "${RED}❌ Elasticsearch tests failed${NC}"
        log_message "ERROR" "Elasticsearch tests failed"
        return 1
    fi
}

# Function to generate test report
generate_report() {
    local exit_code="$1"
    
    echo
    echo "=" | tr ' ' '=' | head -c 80
    echo -e "${PURPLE}📋 Test Run Summary${NC}"
    echo "=" | tr ' ' '=' | head -c 80
    
    echo -e "${CYAN}Test Type: ${TEST_TYPE}${NC}"
    echo -e "${CYAN}Timestamp: ${TIMESTAMP}${NC}"
    echo -e "${CYAN}Results Directory: ${RESULTS_DIR}${NC}"
    echo -e "${CYAN}Log File: ${LOG_FILE}${NC}"
    
    if [ "$exit_code" -eq 0 ]; then
        echo -e "${GREEN}🎉 Overall Result: SUCCESS${NC}"
    else
        echo -e "${RED}❌ Overall Result: FAILURE${NC}"
    fi
    
    echo
    echo -e "${CYAN}📁 Generated Files:${NC}"
    ls -la "${RESULTS_DIR}"/*"${TIMESTAMP}"* 2>/dev/null || echo "   No result files generated"
    
    echo
    echo -e "${CYAN}💡 Next Steps:${NC}"
    if [ "$exit_code" -eq 0 ]; then
        echo -e "   ${GREEN}✅ All tests passed! The enhanced VM pool system is working correctly.${NC}"
        echo -e "   ${BLUE}🚀 Ready for production workloads.${NC}"
    else
        echo -e "   ${RED}❌ Some tests failed. Review the logs for details.${NC}"
        echo -e "   ${YELLOW}🔧 Check service status: docker-compose ps${NC}"
        echo -e "   ${YELLOW}📊 Check service logs: docker-compose logs [service]${NC}"
    fi
}

# Main execution
main() {
    local exit_code=0
    
    # Check prerequisites
    if ! check_prerequisites; then
        exit 1
    fi
    
    # Run tests based on type
    case "$TEST_TYPE" in
        "all")
            echo -e "${PURPLE}🧪 Running all test suites...${NC}"
            run_quick_tests || exit_code=1
            run_integration_tests || exit_code=1
            run_pytest_tests || exit_code=1
            run_elasticsearch_tests || exit_code=1
            run_performance_tests || exit_code=1
            ;;
        "integration")
            run_integration_tests || exit_code=1
            ;;
        "pytest")
            run_pytest_tests || exit_code=1
            ;;
        "performance")
            run_performance_tests || exit_code=1
            ;;
        "quick")
            run_quick_tests || exit_code=1
            ;;
        "elasticsearch")
            run_elasticsearch_tests || exit_code=1
            ;;
        *)
            echo -e "${RED}❌ Unknown test type: $TEST_TYPE${NC}"
            echo -e "${YELLOW}Valid types: all, integration, pytest, performance, quick, elasticsearch${NC}"
            exit 1
            ;;
    esac
    
    # Generate report
    generate_report "$exit_code"
    
    exit "$exit_code"
}

# Run main function
main "$@"
