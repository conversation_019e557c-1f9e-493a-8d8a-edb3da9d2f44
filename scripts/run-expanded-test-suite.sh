#!/bin/bash

# TurdParty Expanded Test Suite Runner
# Executes all expanded integration tests in the correct order with comprehensive reporting

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TEST_DIR="$PROJECT_ROOT/tests/integration"
REPORT_DIR="/tmp/turdparty_expanded_test_reports"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test suite configuration
declare -A TEST_SUITES=(
    ["infrastructure"]="test_infrastructure_ready.py"
    ["fibratus"]="test_fibratus_real_injection.py"
    ["workflow"]="test_complete_workflow.py"
    ["parallel"]="test_parallel_analysis.py"
    ["performance"]="test_performance_load.py"
    ["security"]="test_security_edge_cases.py"
    ["comprehensive"]="test_comprehensive_suite.py"
)

declare -A SUITE_DESCRIPTIONS=(
    ["infrastructure"]="Infrastructure readiness and health checks"
    ["fibratus"]="Real Fibratus integration with telemetry verification"
    ["workflow"]="End-to-end workflow testing with multiple components"
    ["parallel"]="Multi-VM parallel analysis capabilities"
    ["performance"]="Performance and load testing under various conditions"
    ["security"]="Security testing and edge case handling"
    ["comprehensive"]="Comprehensive suite orchestration and reporting"
)

declare -A SUITE_REQUIRED=(
    ["infrastructure"]="true"
    ["fibratus"]="true"
    ["workflow"]="false"
    ["parallel"]="false"
    ["performance"]="false"
    ["security"]="false"
    ["comprehensive"]="false"
)

# Default execution order
DEFAULT_ORDER=("infrastructure" "fibratus" "workflow" "parallel" "performance" "security" "comprehensive")

# Functions
print_header() {
    echo -e "${CYAN}================================================================================================${NC}"
    echo -e "${CYAN}🎉💩🥳 TurdParty Expanded Test Suite Runner 🥳💩🎉${NC}"
    echo -e "${CYAN}================================================================================================${NC}"
    echo -e "${BLUE}Timestamp: $(date)${NC}"
    echo -e "${BLUE}Project Root: $PROJECT_ROOT${NC}"
    echo -e "${BLUE}Report Directory: $REPORT_DIR${NC}"
    echo ""
}

print_usage() {
    echo "Usage: $0 [OPTIONS] [SUITES...]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -l, --list              List available test suites"
    echo "  -a, --all               Run all test suites (default)"
    echo "  -r, --required-only     Run only required test suites"
    echo "  -f, --fast              Run with reduced timeouts for faster execution"
    echo "  -v, --verbose           Enable verbose output"
    echo "  -s, --stop-on-failure   Stop execution on first failure"
    echo "  -c, --continue          Continue execution even if required tests fail"
    echo "  -o, --output DIR        Specify output directory for reports"
    echo ""
    echo "Available Test Suites:"
    for suite in "${DEFAULT_ORDER[@]}"; do
        required_status=""
        if [[ "${SUITE_REQUIRED[$suite]}" == "true" ]]; then
            required_status=" ${RED}(REQUIRED)${NC}"
        else
            required_status=" ${YELLOW}(OPTIONAL)${NC}"
        fi
        echo -e "  ${GREEN}$suite${NC}$required_status - ${SUITE_DESCRIPTIONS[$suite]}"
    done
    echo ""
    echo "Examples:"
    echo "  $0                          # Run all test suites"
    echo "  $0 -r                       # Run only required test suites"
    echo "  $0 infrastructure fibratus  # Run specific test suites"
    echo "  $0 -f -v                    # Fast execution with verbose output"
}

setup_environment() {
    echo -e "${BLUE}🔧 Setting up expanded test environment...${NC}"
    
    # Create report directory
    mkdir -p "$REPORT_DIR"
    
    # Check if we're in nix-shell or need to enter it
    if [[ -z "$IN_NIX_SHELL" ]]; then
        echo -e "${YELLOW}⚠️ Not in nix-shell. Tests will be run with nix-shell wrapper.${NC}"
        NIX_WRAPPER="nix-shell -p python311 -p python311Packages.pytest -p python311Packages.requests -p python311Packages.psutil --run"
    else
        echo -e "${GREEN}✅ Already in nix-shell environment.${NC}"
        NIX_WRAPPER=""
    fi
    
    # Check if project dependencies are available
    cd "$PROJECT_ROOT"
    
    # Verify test files exist
    for suite in "${!TEST_SUITES[@]}"; do
        test_file="$TEST_DIR/${TEST_SUITES[$suite]}"
        if [[ ! -f "$test_file" ]]; then
            echo -e "${RED}❌ Test file not found: $test_file${NC}"
            exit 1
        fi
    done
    
    echo -e "${GREEN}✅ Expanded test environment setup completed.${NC}"
    echo ""
}

run_test_suite() {
    local suite_name="$1"
    local test_file="${TEST_SUITES[$suite_name]}"
    local is_required="${SUITE_REQUIRED[$suite_name]}"
    
    echo -e "${PURPLE}🚀 Running Expanded Test Suite: $suite_name${NC}"
    echo -e "${BLUE}   File: $test_file${NC}"
    echo -e "${BLUE}   Description: ${SUITE_DESCRIPTIONS[$suite_name]}${NC}"
    echo -e "${BLUE}   Required: $is_required${NC}"
    echo ""
    
    local start_time=$(date +%s)
    local test_output_file="$REPORT_DIR/test_output_${suite_name}_${TIMESTAMP}.log"
    local test_result_file="$REPORT_DIR/test_results_${suite_name}_${TIMESTAMP}.json"
    
    # Construct pytest command
    local pytest_cmd="python -m pytest $TEST_DIR/$test_file -v -s --tb=short"
    
    if [[ "$VERBOSE" == "true" ]]; then
        pytest_cmd="$pytest_cmd --capture=no"
    fi
    
    if [[ "$FAST_MODE" == "true" ]]; then
        pytest_cmd="$pytest_cmd -x"  # Stop on first failure for speed
    fi
    
    # Execute test
    local exit_code=0
    
    if [[ -n "$NIX_WRAPPER" ]]; then
        echo -e "${CYAN}🔧 Executing with nix-shell wrapper...${NC}"
        $NIX_WRAPPER "$pytest_cmd" 2>&1 | tee "$test_output_file" || exit_code=$?
    else
        echo -e "${CYAN}🔧 Executing directly...${NC}"
        $pytest_cmd 2>&1 | tee "$test_output_file" || exit_code=$?
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # Analyze results
    if [[ $exit_code -eq 0 ]]; then
        echo -e "${GREEN}✅ Expanded Test Suite '$suite_name' PASSED (${duration}s)${NC}"
        PASSED_SUITES+=("$suite_name")
    else
        echo -e "${RED}❌ Expanded Test Suite '$suite_name' FAILED (${duration}s)${NC}"
        FAILED_SUITES+=("$suite_name")
        
        if [[ "$is_required" == "true" && "$STOP_ON_FAILURE" == "true" ]]; then
            echo -e "${RED}💥 Required test suite failed. Stopping execution.${NC}"
            return 1
        fi
        
        if [[ "$is_required" == "true" && "$CONTINUE_ON_FAILURE" != "true" ]]; then
            echo -e "${RED}💥 Required test suite failed. Use -c to continue anyway.${NC}"
            return 1
        fi
    fi
    
    echo ""
    return 0
}

generate_summary_report() {
    echo -e "${CYAN}📊 Generating Expanded Test Suite Summary Report...${NC}"
    
    local total_suites=$((${#PASSED_SUITES[@]} + ${#FAILED_SUITES[@]}))
    local success_rate=0
    
    if [[ $total_suites -gt 0 ]]; then
        success_rate=$(( ${#PASSED_SUITES[@]} * 100 / total_suites ))
    fi
    
    local summary_file="$REPORT_DIR/expanded_test_summary_${TIMESTAMP}.txt"
    
    cat > "$summary_file" << EOF
TurdParty Expanded Test Suite Summary
====================================

Execution Timestamp: $(date)
Project Root: $PROJECT_ROOT
Report Directory: $REPORT_DIR

Expanded Test Suite Results:
---------------------------
Total Suites: $total_suites
Passed: ${#PASSED_SUITES[@]}
Failed: ${#FAILED_SUITES[@]}
Success Rate: ${success_rate}%

Test Suite Coverage:
- Infrastructure Testing: ✅
- Real Fibratus Integration: ✅
- Complete Workflow Testing: ✅
- Parallel Analysis: ✅
- Performance & Load Testing: ✅
- Security & Edge Cases: ✅
- Comprehensive Orchestration: ✅

Passed Suites:
EOF
    
    for suite in "${PASSED_SUITES[@]}"; do
        echo "  ✅ $suite - ${SUITE_DESCRIPTIONS[$suite]}" >> "$summary_file"
    done
    
    if [[ ${#FAILED_SUITES[@]} -gt 0 ]]; then
        echo "" >> "$summary_file"
        echo "Failed Suites:" >> "$summary_file"
        for suite in "${FAILED_SUITES[@]}"; do
            echo "  ❌ $suite - ${SUITE_DESCRIPTIONS[$suite]}" >> "$summary_file"
        done
    fi
    
    cat >> "$summary_file" << EOF

Execution Order:
---------------
EOF
    
    for i in "${!EXECUTED_SUITES[@]}"; do
        echo "  $((i+1)). ${EXECUTED_SUITES[$i]}" >> "$summary_file"
    done
    
    echo "" >> "$summary_file"
    echo "Report Files:" >> "$summary_file"
    echo "  Summary: $summary_file" >> "$summary_file"
    ls -la "$REPORT_DIR"/*_${TIMESTAMP}.* >> "$summary_file" 2>/dev/null || true
    
    # Display summary
    echo ""
    echo -e "${CYAN}================================================================================================${NC}"
    echo -e "${CYAN}📊 EXPANDED TEST EXECUTION SUMMARY${NC}"
    echo -e "${CYAN}================================================================================================${NC}"
    echo -e "${BLUE}Total Suites: $total_suites${NC}"
    echo -e "${GREEN}Passed: ${#PASSED_SUITES[@]}${NC}"
    echo -e "${RED}Failed: ${#FAILED_SUITES[@]}${NC}"
    echo -e "${YELLOW}Success Rate: ${success_rate}%${NC}"
    echo ""
    
    if [[ ${#PASSED_SUITES[@]} -gt 0 ]]; then
        echo -e "${GREEN}✅ Passed Suites:${NC}"
        for suite in "${PASSED_SUITES[@]}"; do
            echo -e "   ${GREEN}$suite${NC} - ${SUITE_DESCRIPTIONS[$suite]}"
        done
        echo ""
    fi
    
    if [[ ${#FAILED_SUITES[@]} -gt 0 ]]; then
        echo -e "${RED}❌ Failed Suites:${NC}"
        for suite in "${FAILED_SUITES[@]}"; do
            echo -e "   ${RED}$suite${NC} - ${SUITE_DESCRIPTIONS[$suite]}"
        done
        echo ""
    fi
    
    echo -e "${BLUE}📄 Summary report saved: $summary_file${NC}"
    echo -e "${CYAN}================================================================================================${NC}"
    
    # Return appropriate exit code
    if [[ ${#FAILED_SUITES[@]} -eq 0 ]]; then
        echo -e "${GREEN}🎉 ALL EXPANDED TESTS PASSED! TurdParty is production-ready with comprehensive coverage! 🎉${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️ Some expanded tests failed. Review the results and fix issues before deployment.${NC}"
        return 1
    fi
}

# Parse command line arguments
SUITES_TO_RUN=()
RUN_ALL="true"
RUN_REQUIRED_ONLY="false"
FAST_MODE="false"
VERBOSE="false"
STOP_ON_FAILURE="false"
CONTINUE_ON_FAILURE="false"
CUSTOM_OUTPUT_DIR=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            print_usage
            exit 0
            ;;
        -l|--list)
            echo -e "${BLUE}Available Expanded Test Suites:${NC}"
            echo ""
            for suite in "${DEFAULT_ORDER[@]}"; do
                required_status=""
                if [[ "${SUITE_REQUIRED[$suite]}" == "true" ]]; then
                    required_status=" ${RED}(REQUIRED)${NC}"
                else
                    required_status=" ${YELLOW}(OPTIONAL)${NC}"
                fi
                echo -e "  ${GREEN}$suite${NC}$required_status"
                echo -e "    File: ${TEST_SUITES[$suite]}"
                echo -e "    Description: ${SUITE_DESCRIPTIONS[$suite]}"
                echo ""
            done
            exit 0
            ;;
        -a|--all)
            RUN_ALL="true"
            shift
            ;;
        -r|--required-only)
            RUN_REQUIRED_ONLY="true"
            RUN_ALL="false"
            shift
            ;;
        -f|--fast)
            FAST_MODE="true"
            shift
            ;;
        -v|--verbose)
            VERBOSE="true"
            shift
            ;;
        -s|--stop-on-failure)
            STOP_ON_FAILURE="true"
            shift
            ;;
        -c|--continue)
            CONTINUE_ON_FAILURE="true"
            shift
            ;;
        -o|--output)
            CUSTOM_OUTPUT_DIR="$2"
            shift 2
            ;;
        -*)
            echo "Unknown option: $1"
            print_usage
            exit 1
            ;;
        *)
            SUITES_TO_RUN+=("$1")
            RUN_ALL="false"
            shift
            ;;
    esac
done

# Override report directory if specified
if [[ -n "$CUSTOM_OUTPUT_DIR" ]]; then
    REPORT_DIR="$CUSTOM_OUTPUT_DIR"
fi

# Determine which suites to run
if [[ "$RUN_ALL" == "true" ]]; then
    SUITES_TO_RUN=("${DEFAULT_ORDER[@]}")
elif [[ "$RUN_REQUIRED_ONLY" == "true" ]]; then
    SUITES_TO_RUN=()
    for suite in "${DEFAULT_ORDER[@]}"; do
        if [[ "${SUITE_REQUIRED[$suite]}" == "true" ]]; then
            SUITES_TO_RUN+=("$suite")
        fi
    done
elif [[ ${#SUITES_TO_RUN[@]} -eq 0 ]]; then
    echo -e "${RED}❌ No test suites specified. Use -a for all or specify suite names.${NC}"
    print_usage
    exit 1
fi

# Validate specified suites
for suite in "${SUITES_TO_RUN[@]}"; do
    if [[ -z "${TEST_SUITES[$suite]}" ]]; then
        echo -e "${RED}❌ Unknown test suite: $suite${NC}"
        echo -e "${YELLOW}Available suites: ${!TEST_SUITES[*]}${NC}"
        exit 1
    fi
done

# Initialize tracking arrays
PASSED_SUITES=()
FAILED_SUITES=()
EXECUTED_SUITES=()

# Main execution
main() {
    print_header
    
    echo -e "${BLUE}📋 Expanded Test Execution Plan:${NC}"
    for suite in "${SUITES_TO_RUN[@]}"; do
        local required_status=""
        if [[ "${SUITE_REQUIRED[$suite]}" == "true" ]]; then
            required_status=" ${RED}(REQUIRED)${NC}"
        else
            required_status=" ${YELLOW}(OPTIONAL)${NC}"
        fi
        echo -e "   ${GREEN}$suite${NC}$required_status - ${SUITE_DESCRIPTIONS[$suite]}"
    done
    echo ""
    
    setup_environment
    
    # Execute test suites
    local overall_success="true"
    
    for suite in "${SUITES_TO_RUN[@]}"; do
        EXECUTED_SUITES+=("$suite")
        
        if ! run_test_suite "$suite"; then
            overall_success="false"
            if [[ "$STOP_ON_FAILURE" == "true" ]]; then
                break
            fi
        fi
    done
    
    # Generate summary report
    if ! generate_summary_report; then
        overall_success="false"
    fi
    
    # Exit with appropriate code
    if [[ "$overall_success" == "true" ]]; then
        exit 0
    else
        exit 1
    fi
}

# Execute main function
main "$@"
