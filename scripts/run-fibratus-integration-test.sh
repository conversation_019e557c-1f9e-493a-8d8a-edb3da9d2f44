#!/bin/bash

# Real Fibratus Integration Test Runner
# Tests actual file injection with real telemetry collection

set -e

echo "🧪 TurdParty Fibratus Integration Test Runner"
echo "=============================================="

# Configuration
TEST_DIR="tests/integration"
TEST_FILE="test_fibratus_real_injection.py"
REPORT_DIR="/tmp/fibratus_test_reports"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Create report directory
mkdir -p "$REPORT_DIR"

echo "📋 Test Configuration:"
echo "  Test File: $TEST_FILE"
echo "  Report Dir: $REPORT_DIR"
echo "  Timestamp: $TIMESTAMP"
echo ""

# Check prerequisites
echo "🔍 Checking prerequisites..."

# Check if Docker is running
if ! docker ps >/dev/null 2>&1; then
    echo "❌ Docker is not running or not accessible"
    exit 1
fi

# Check required containers
REQUIRED_CONTAINERS=(
    "turdpartycollab_api"
    "turdpartycollab_worker_vm"
    "turdpartycollab_elasticsearch"
    "turdpartycollab_database"
)

echo "🐳 Checking Docker containers..."
for container in "${REQUIRED_CONTAINERS[@]}"; do
    if docker ps --format "{{.Names}}" | grep -q "$container"; then
        echo "  ✅ $container: Running"
    else
        echo "  ❌ $container: Not running"
        echo ""
        echo "🚀 Starting required containers..."
        docker-compose up -d
        sleep 30
        break
    fi
done

# Check API health
echo "🔍 Checking API health..."
if curl -s http://localhost:8000/api/v1/health >/dev/null; then
    echo "  ✅ API: Healthy"
else
    echo "  ❌ API: Not responding"
    echo "  ⏳ Waiting for API to be ready..."
    sleep 60
    
    if curl -s http://localhost:8000/api/v1/health >/dev/null; then
        echo "  ✅ API: Now healthy"
    else
        echo "  ❌ API: Still not responding - check logs"
        exit 1
    fi
fi

# Check Elasticsearch
echo "🔍 Checking Elasticsearch..."
if curl -s http://localhost:9200/_cluster/health >/dev/null; then
    echo "  ✅ Elasticsearch: Healthy"
else
    echo "  ❌ Elasticsearch: Not responding"
    exit 1
fi

echo ""
echo "✅ All prerequisites verified"
echo ""

# Install test dependencies
echo "📦 Installing test dependencies..."
if command -v nix-shell >/dev/null 2>&1; then
    echo "  Using nix-shell for dependencies..."
    TEST_COMMAND="nix-shell -p python311 -p python311Packages.pytest -p python311Packages.requests --run"
else
    echo "  Using system Python..."
    TEST_COMMAND=""
    
    # Check if pytest is available
    if ! python3 -c "import pytest" 2>/dev/null; then
        echo "  Installing pytest..."
        pip3 install pytest requests
    fi
fi

echo ""

# Run the integration test
echo "🧪 Starting Fibratus Integration Test..."
echo "⚠️  This test will:"
echo "   - Download real Notepad++ binary (~5MB)"
echo "   - Create a Windows VM (may take 5-10 minutes)"
echo "   - Inject and execute the binary"
echo "   - Monitor with Fibratus for 5 minutes"
echo "   - Verify telemetry collection"
echo "   - Generate comprehensive report"
echo ""
echo "⏱️  Expected duration: 15-20 minutes"
echo ""

read -p "🚀 Continue with integration test? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Test cancelled by user"
    exit 0
fi

echo ""
echo "🚀 Running integration test..."
echo "=============================================="

# Change to project directory
cd "$(dirname "$0")/.."

# Run the test
if [ -n "$TEST_COMMAND" ]; then
    $TEST_COMMAND "python -m pytest $TEST_DIR/$TEST_FILE -v -s --tb=short --junit-xml=$REPORT_DIR/fibratus_test_$TIMESTAMP.xml"
else
    python3 -m pytest "$TEST_DIR/$TEST_FILE" -v -s --tb=short --junit-xml="$REPORT_DIR/fibratus_test_$TIMESTAMP.xml"
fi

TEST_EXIT_CODE=$?

echo ""
echo "=============================================="

if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo "🎉 FIBRATUS INTEGRATION TEST: SUCCESS!"
    echo ""
    echo "📊 Test Results:"
    echo "  ✅ All tests passed"
    echo "  ✅ Real binary injection verified"
    echo "  ✅ Fibratus telemetry collected"
    echo "  ✅ Evidence Box functional"
    echo ""
    echo "📋 Reports generated in: $REPORT_DIR"
    echo "📄 JUnit XML: fibratus_test_$TIMESTAMP.xml"
    echo ""
    echo "🔍 Check /tmp/fibratus_integration_test_report_*.json for detailed results"
    echo ""
    echo "🌐 Verification URLs:"
    echo "  📊 Elasticsearch: http://localhost:9200/turdparty-*/_search"
    echo "  📈 Kibana: http://localhost:5601/app/discover"
    echo "  🖥️ Frontend: http://localhost:3000/vm_status"
    echo ""
else
    echo "❌ FIBRATUS INTEGRATION TEST: FAILED!"
    echo ""
    echo "📊 Test Results:"
    echo "  ❌ Some tests failed (exit code: $TEST_EXIT_CODE)"
    echo ""
    echo "🔍 Troubleshooting:"
    echo "  1. Check container logs: docker logs turdpartycollab_worker_vm"
    echo "  2. Check API logs: docker logs turdpartycollab_api"
    echo "  3. Check Elasticsearch: curl http://localhost:9200/_cluster/health"
    echo "  4. Check test report: $REPORT_DIR/fibratus_test_$TIMESTAMP.xml"
    echo ""
    echo "📋 Reports (may be partial): $REPORT_DIR"
    echo ""
fi

echo "=============================================="
echo "🧪 Fibratus Integration Test Complete"
echo "=============================================="

exit $TEST_EXIT_CODE
