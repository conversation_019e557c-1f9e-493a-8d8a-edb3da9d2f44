#!/usr/bin/env python3
"""
Run VM Pool Migration in Container

This script runs the VM pool schema migration inside a container that has
access to the TurdParty Docker network, allowing proper database connectivity.

Usage:
    python scripts/run_migration_in_container.py
    python scripts/run_migration_in_container.py --dry-run
    python scripts/run_migration_in_container.py --rollback
"""

import argparse
import logging
import subprocess
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def run_migration_in_container(dry_run: bool = False, rollback: bool = False, verify: bool = False) -> bool:
    """Run the migration script inside the API container."""
    
    # Determine the command to run
    migration_cmd = ["python", "/app/scripts/migrate_vm_pool_schema.py"]
    
    if dry_run:
        migration_cmd.append("--dry-run")
    if rollback:
        migration_cmd.append("--rollback")
    if verify:
        migration_cmd.append("--verify")
    
    # Docker exec command to run migration in the API container
    docker_cmd = [
        "docker", "exec", "-it",
        "turdpartycollab_api",
        *migration_cmd
    ]
    
    logger.info(f"Running migration in container: {' '.join(migration_cmd)}")
    logger.info(f"Docker command: {' '.join(docker_cmd)}")
    
    try:
        # Run the migration in the container
        result = subprocess.run(
            docker_cmd,
            cwd=str(project_root),
            capture_output=False,  # Show output in real-time
            text=True
        )
        
        if result.returncode == 0:
            logger.info("✅ Migration completed successfully in container")
            return True
        else:
            logger.error(f"❌ Migration failed in container (exit code: {result.returncode})")
            return False
            
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Migration command failed: {e}")
        return False
    except FileNotFoundError:
        logger.error("❌ Docker command not found. Ensure Docker is installed and running.")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error running migration: {e}")
        return False


def check_container_running() -> bool:
    """Check if the API container is running."""
    try:
        result = subprocess.run(
            ["docker", "ps", "--filter", "name=turdpartycollab_api", "--format", "{{.Names}}"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0 and "turdpartycollab_api" in result.stdout:
            logger.info("✅ API container is running")
            return True
        else:
            logger.error("❌ API container is not running")
            logger.error("Please start the TurdParty stack: docker-compose up -d")
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to check container status: {e}")
        return False


def copy_migration_script_to_container() -> bool:
    """Copy the migration script to the container."""
    try:
        # Copy the migration script to the container
        copy_cmd = [
            "docker", "cp",
            str(project_root / "scripts" / "migrate_vm_pool_schema.py"),
            "turdpartycollab_api:/app/scripts/"
        ]
        
        result = subprocess.run(copy_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Migration script copied to container")
            return True
        else:
            logger.error(f"❌ Failed to copy migration script: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to copy migration script: {e}")
        return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run VM pool migration in container")
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without making changes"
    )
    parser.add_argument(
        "--rollback",
        action="store_true",
        help="Rollback the migration"
    )
    parser.add_argument(
        "--verify",
        action="store_true",
        help="Verify migration was successful"
    )
    
    args = parser.parse_args()
    
    logger.info("🚀 Starting VM Pool Migration in Container")
    logger.info("=" * 60)
    
    # Check if container is running
    if not check_container_running():
        sys.exit(1)
    
    # Copy migration script to container
    if not copy_migration_script_to_container():
        sys.exit(1)
    
    # Run migration in container
    success = run_migration_in_container(
        dry_run=args.dry_run,
        rollback=args.rollback,
        verify=args.verify
    )
    
    if success:
        logger.info("🎉 Migration completed successfully!")
        if not args.dry_run and not args.rollback and not args.verify:
            logger.info("💡 You can now run VM management tests")
    else:
        logger.error("💥 Migration failed!")
        logger.error("Check the container logs for more details")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
