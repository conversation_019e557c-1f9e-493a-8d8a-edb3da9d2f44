#!/usr/bin/env python3
"""
VM Management Test Runner

Comprehensive test runner for VM management functionality including:
- Basic VM availability management tests
- API integration tests via Traefik
- Parallel test suite comparison
- Performance validation

Usage:
    python scripts/run_vm_management_tests.py
    python scripts/run_vm_management_tests.py --include-parallel
    python scripts/run_vm_management_tests.py --performance-only
"""

import argparse
import json
import logging
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class VMManagementTestRunner:
    """Comprehensive test runner for VM management functionality."""
    
    def __init__(self):
        self.test_results = {
            "start_time": datetime.now().isoformat(),
            "test_suites": {},
            "summary": {},
            "performance_comparison": {}
        }
    
    def run_basic_vm_integration_tests(self) -> Dict[str, Any]:
        """Run basic VM integration tests."""
        logger.info("🔧 Running basic VM integration tests...")
        
        try:
            # Import and run the integration tester
            from test_basic_vm_integration import VMIntegrationTester
            
            tester = VMIntegrationTester()
            results = tester.run_comprehensive_test()
            
            return {
                "success": results["summary"]["overall_success"],
                "details": results,
                "test_count": results["summary"]["total_tests"],
                "passed_count": results["summary"]["passed_tests"],
                "failed_count": results["summary"]["failed_tests"]
            }
            
        except Exception as e:
            logger.error(f"Basic VM integration tests failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "test_count": 0,
                "passed_count": 0,
                "failed_count": 1
            }
    
    def run_parallel_tests(self) -> Dict[str, Any]:
        """Run the existing parallel test suite."""
        logger.info("🚀 Running parallel test suite...")
        
        try:
            # Run the parallel test script
            result = subprocess.run(
                ["bash", "scripts/run-parallel-tests.sh"],
                cwd=project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            # Try to find and parse the latest test results
            test_result_files = list(project_root.glob("test_results_test-*.json"))
            
            if test_result_files:
                latest_file = max(test_result_files, key=lambda f: f.stat().st_mtime)
                
                with open(latest_file, 'r') as f:
                    test_data = json.load(f)
                
                # Analyze results
                test_results = test_data.get("test_results", {})
                allocation_results = []
                
                for template, result in test_results.items():
                    if isinstance(result, dict) and "allocation_result" in result:
                        allocation_results.append({
                            "template": template,
                            "success": result["allocation_result"].get("success", False),
                            "vm_id": result["allocation_result"].get("vm_id"),
                            "status": result["allocation_result"].get("status"),
                            "wait_seconds": result["allocation_result"].get("estimated_wait_seconds", 0)
                        })
                
                success_count = sum(1 for r in allocation_results if r["success"])
                total_count = len(allocation_results)
                
                return {
                    "success": success_count > 0,
                    "total_allocations": total_count,
                    "successful_allocations": success_count,
                    "failed_allocations": total_count - success_count,
                    "success_rate": (success_count / total_count * 100) if total_count > 0 else 0,
                    "allocation_details": allocation_results,
                    "test_file": str(latest_file),
                    "script_exit_code": result.returncode
                }
            else:
                return {
                    "success": False,
                    "error": "No test result files found",
                    "script_exit_code": result.returncode,
                    "stdout": result.stdout[-500:],  # Last 500 chars
                    "stderr": result.stderr[-500:]   # Last 500 chars
                }
                
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": "Parallel tests timed out after 5 minutes"
            }
        except Exception as e:
            logger.error(f"Parallel tests failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def run_performance_comparison(self) -> Dict[str, Any]:
        """Run performance comparison between old and new systems."""
        logger.info("📊 Running performance comparison...")
        
        try:
            # Test allocation performance with new system
            from test_basic_vm_integration import VMIntegrationTester
            
            tester = VMIntegrationTester()
            
            # Test multiple templates for performance
            templates = ["alpine:latest", "ubuntu:20.04", "ubuntu:22.04"]
            performance_results = {}
            
            for template in templates:
                logger.info(f"Testing performance for {template}...")
                
                start_time = time.time()
                result = tester.test_vm_allocation_immediate(template)
                allocation_time = time.time() - start_time
                
                performance_results[template] = {
                    "allocation_time_seconds": allocation_time,
                    "success": result.get("success", False),
                    "vm_allocated": result.get("vm_id") is not None,
                    "status": result.get("status", "unknown")
                }
            
            # Calculate averages
            successful_allocations = [r for r in performance_results.values() if r["success"]]
            
            if successful_allocations:
                avg_allocation_time = sum(r["allocation_time_seconds"] for r in successful_allocations) / len(successful_allocations)
                success_rate = len(successful_allocations) / len(performance_results) * 100
            else:
                avg_allocation_time = 0
                success_rate = 0
            
            return {
                "success": len(successful_allocations) > 0,
                "avg_allocation_time_seconds": avg_allocation_time,
                "success_rate_percent": success_rate,
                "template_results": performance_results,
                "improvement_vs_baseline": {
                    "baseline_wait_seconds": 2760,  # From test results
                    "new_allocation_seconds": avg_allocation_time,
                    "improvement_factor": 2760 / max(avg_allocation_time, 0.1),
                    "improvement_percent": ((2760 - avg_allocation_time) / 2760 * 100) if avg_allocation_time < 2760 else 0
                }
            }
            
        except Exception as e:
            logger.error(f"Performance comparison failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def run_comprehensive_test_suite(self, include_parallel: bool = True, 
                                   performance_only: bool = False) -> Dict[str, Any]:
        """Run comprehensive test suite."""
        logger.info("🎯 Starting comprehensive VM management test suite...")
        
        if performance_only:
            # Run only performance tests
            performance_results = self.run_performance_comparison()
            self.test_results["test_suites"]["performance"] = performance_results
            
            self.test_results["summary"] = {
                "total_suites": 1,
                "passed_suites": 1 if performance_results["success"] else 0,
                "failed_suites": 0 if performance_results["success"] else 1,
                "overall_success": performance_results["success"]
            }
            
            return self.test_results
        
        # Run basic VM integration tests
        basic_results = self.run_basic_vm_integration_tests()
        self.test_results["test_suites"]["basic_vm_integration"] = basic_results
        
        # Run performance comparison
        performance_results = self.run_performance_comparison()
        self.test_results["test_suites"]["performance"] = performance_results
        
        # Optionally run parallel tests
        if include_parallel:
            parallel_results = self.run_parallel_tests()
            self.test_results["test_suites"]["parallel_tests"] = parallel_results
        
        # Calculate summary
        suites = self.test_results["test_suites"]
        total_suites = len(suites)
        passed_suites = sum(1 for suite in suites.values() if suite.get("success", False))
        failed_suites = total_suites - passed_suites
        
        self.test_results["summary"] = {
            "total_suites": total_suites,
            "passed_suites": passed_suites,
            "failed_suites": failed_suites,
            "overall_success": failed_suites == 0
        }
        
        self.test_results["end_time"] = datetime.now().isoformat()
        
        return self.test_results
    
    def save_results(self, filename: str = None):
        """Save test results to file."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"vm_management_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        logger.info(f"Test results saved to: {filename}")
        return filename


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run VM management test suite")
    parser.add_argument(
        "--include-parallel",
        action="store_true",
        help="Include parallel test suite (slower)"
    )
    parser.add_argument(
        "--performance-only",
        action="store_true",
        help="Run only performance tests"
    )
    parser.add_argument(
        "--save-results",
        action="store_true",
        help="Save test results to file"
    )
    
    args = parser.parse_args()
    
    runner = VMManagementTestRunner()
    
    logger.info("🚀 Starting VM Management Test Suite")
    logger.info("=" * 60)
    
    results = runner.run_comprehensive_test_suite(
        include_parallel=args.include_parallel,
        performance_only=args.performance_only
    )
    
    # Print summary
    summary = results["summary"]
    logger.info("\n" + "=" * 60)
    logger.info("VM MANAGEMENT TEST RESULTS")
    logger.info("=" * 60)
    logger.info(f"Total Test Suites: {summary['total_suites']}")
    logger.info(f"Passed: {summary['passed_suites']}")
    logger.info(f"Failed: {summary['failed_suites']}")
    logger.info(f"Overall Success: {'✅ PASS' if summary['overall_success'] else '❌ FAIL'}")
    
    # Print detailed results
    for suite_name, suite_results in results["test_suites"].items():
        status = "✅ PASS" if suite_results.get("success", False) else "❌ FAIL"
        logger.info(f"\n{suite_name}: {status}")
        
        if suite_name == "performance" and "improvement_vs_baseline" in suite_results:
            improvement = suite_results["improvement_vs_baseline"]
            logger.info(f"  Allocation Time: {suite_results.get('avg_allocation_time_seconds', 0):.3f}s")
            logger.info(f"  Success Rate: {suite_results.get('success_rate_percent', 0):.1f}%")
            logger.info(f"  Improvement: {improvement.get('improvement_factor', 0):.1f}x faster")
            logger.info(f"  Time Reduction: {improvement.get('improvement_percent', 0):.1f}%")
    
    logger.info("=" * 60)
    
    if args.save_results:
        filename = runner.save_results()
        logger.info(f"📄 Results saved to: {filename}")
    
    sys.exit(0 if summary['overall_success'] else 1)


if __name__ == "__main__":
    main()
