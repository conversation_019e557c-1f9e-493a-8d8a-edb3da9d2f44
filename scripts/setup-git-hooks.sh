#!/bin/bash

# TurdParty Git Hooks Setup Script
# Sets up custom git hooks for the project

set -e

# ANSI color codes
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print banner
print_banner() {
    echo ""
    print_colored $CYAN "╔══════════════════════════════════════════════════════════════╗"
    print_colored $CYAN "║                🔧 TurdParty Git Hooks Setup                  ║"
    print_colored $CYAN "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to check if we're in a git repository
check_git_repo() {
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        print_colored $RED "❌ Error: Not in a git repository!"
        print_colored $YELLOW "Please run this script from the root of your git repository."
        exit 1
    fi
}

# Function to backup existing hooks
backup_existing_hooks() {
    local git_hooks_dir=".git/hooks"
    local backup_dir=".git/hooks.backup.$(date +%Y%m%d_%H%M%S)"
    
    if [ -d "$git_hooks_dir" ] && [ "$(ls -A $git_hooks_dir 2>/dev/null)" ]; then
        print_colored $YELLOW "📦 Backing up existing git hooks to: $backup_dir"
        cp -r "$git_hooks_dir" "$backup_dir"
        print_colored $GREEN "✅ Backup created successfully"
    fi
}

# Function to install hooks
install_hooks() {
    local source_dir=".githooks"
    local target_dir=".git/hooks"
    
    if [ ! -d "$source_dir" ]; then
        print_colored $RED "❌ Error: Source hooks directory '$source_dir' not found!"
        exit 1
    fi
    
    print_colored $BLUE "🔧 Installing git hooks..."
    
    # Create hooks directory if it doesn't exist
    mkdir -p "$target_dir"
    
    # Install each hook
    for hook_file in "$source_dir"/*; do
        if [ -f "$hook_file" ]; then
            local hook_name=$(basename "$hook_file")
            local target_file="$target_dir/$hook_name"
            
            print_colored $BLUE "   Installing: $hook_name"
            cp "$hook_file" "$target_file"
            chmod +x "$target_file"
            print_colored $GREEN "   ✅ $hook_name installed and made executable"
        fi
    done
}

# Function to test hooks
test_hooks() {
    print_colored $BLUE "🧪 Testing installed hooks..."
    
    local hooks_dir=".git/hooks"
    
    # Test pre-push hook
    if [ -f "$hooks_dir/pre-push" ]; then
        print_colored $BLUE "   Testing pre-push hook..."
        if "$hooks_dir/pre-push" "origin" "https://github.com/test/test.git" <<< "n" > /dev/null 2>&1; then
            print_colored $GREEN "   ✅ pre-push hook test passed (correctly blocked business hours push)"
        else
            print_colored $YELLOW "   ⚠️  pre-push hook test completed (expected behavior during business hours)"
        fi
    fi
}

# Function to show hook information
show_hook_info() {
    print_colored $PURPLE "📋 Installed Git Hooks Information:"
    echo ""
    
    print_colored $CYAN "🚫 Pre-Push Hook (Business Hours Protection):"
    print_colored $CYAN "   • Discourages pushing to GitHub during 7am-18:00 CET (Mon-Fri)"
    print_colored $CYAN "   • Local commits are always allowed"
    print_colored $CYAN "   • Weekend pushes are allowed"
    print_colored $CYAN "   • Can be bypassed with: git push --no-verify"
    echo ""
    
    print_colored $YELLOW "⏰ Current Status:"
    local current_hour=$(TZ='Europe/Berlin' date '+%H' 2>/dev/null || date '+%H')
    local day_of_week=$(date '+%u')
    
    print_colored $YELLOW "   • Current time: ${current_hour}:00 CET"
    
    if [ $day_of_week -eq 6 ] || [ $day_of_week -eq 7 ]; then
        print_colored $GREEN "   • Today: Weekend - GitHub pushes allowed"
    elif [ $((10#$current_hour)) -ge 7 ] && [ $((10#$current_hour)) -lt 18 ]; then
        print_colored $RED "   • Status: Business hours - GitHub pushes discouraged"
    else
        print_colored $GREEN "   • Status: Outside business hours - GitHub pushes allowed"
    fi
    echo ""
}

# Function to show usage examples
show_usage_examples() {
    print_colored $PURPLE "💡 Usage Examples:"
    echo ""
    
    print_colored $CYAN "Normal workflow (always works):"
    print_colored $CYAN "   git add ."
    print_colored $CYAN "   git commit -m \"your changes\""
    print_colored $CYAN "   # Local commits work anytime"
    echo ""
    
    print_colored $CYAN "Pushing outside business hours:"
    print_colored $CYAN "   git push origin main"
    print_colored $CYAN "   # Works normally after 18:00 CET or before 07:00 CET"
    echo ""
    
    print_colored $CYAN "Emergency push during business hours:"
    print_colored $CYAN "   git push --no-verify origin main"
    print_colored $CYAN "   # Bypasses the hook for urgent changes"
    echo ""
    
    print_colored $CYAN "Interactive push during business hours:"
    print_colored $CYAN "   git push origin main"
    print_colored $CYAN "   # Will prompt for confirmation"
    echo ""
}

# Main function
main() {
    print_banner
    
    # Check if we're in a git repository
    check_git_repo
    
    # Show current directory
    print_colored $BLUE "📁 Working in: $(pwd)"
    echo ""
    
    # Backup existing hooks
    backup_existing_hooks
    echo ""
    
    # Install hooks
    install_hooks
    echo ""
    
    # Test hooks
    test_hooks
    echo ""
    
    # Show information
    show_hook_info
    
    # Show usage examples
    show_usage_examples
    
    print_colored $GREEN "🎉 Git hooks setup completed successfully!"
    print_colored $GREEN "Your repository is now protected against accidental business hours pushes."
    echo ""
}

# Run main function
main "$@"
