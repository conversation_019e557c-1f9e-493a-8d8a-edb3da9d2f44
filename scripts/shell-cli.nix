# Nix shell configuration for TurdParty CLI
# Includes all dependencies needed for the beautiful CLI interface

{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Python and CLI dependencies
    python312
    python312Packages.click
    python312Packages.rich
    python312Packages.requests
    
    # System tools
    curl
    jq
    docker
    docker-compose
    
    # Development tools
    git
    bash
    coreutils
  ];

  shellHook = ''
    echo "🎉 TurdParty CLI Environment Ready!"
    echo "💩 All dependencies loaded for the sexy CLI interface"
    echo ""
    echo "Available commands:"
    echo "  ./scripts/turdparty --help    # Show CLI help"
    echo "  ./scripts/turdparty status    # Show service status"
    echo "  ./scripts/turdparty monitor   # Real-time monitoring"
    echo ""
  '';
}
