#!/bin/bash
# Start Test Infrastructure for Mock Replacement
# This script starts the containerized test infrastructure needed for productive testing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.test.yml"
PROJECT_NAME="turdparty_test"
TIMEOUT=120

print_header() {
    echo -e "${BLUE}================================================================================================${NC}"
    echo -e "${BLUE}🧪 TurdParty Test Infrastructure Startup${NC}"
    echo -e "${BLUE}================================================================================================${NC}"
    echo -e "${YELLOW}Starting containerized services for productive testing...${NC}"
    echo ""
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    
    # Check if compose file exists
    if [ ! -f "$COMPOSE_FILE" ]; then
        print_error "Docker Compose file '$COMPOSE_FILE' not found"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

cleanup_existing() {
    print_info "Cleaning up existing test infrastructure..."
    
    # Stop and remove existing containers
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" down --volumes --remove-orphans 2>/dev/null || true
    
    # Remove any dangling test containers
    docker ps -a --filter "name=turdparty_test_" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    
    # Remove test networks
    docker network ls --filter "name=${PROJECT_NAME}" --format "{{.ID}}" | xargs -r docker network rm 2>/dev/null || true
    
    print_success "Cleanup completed"
}

start_infrastructure() {
    print_info "Starting test infrastructure services..."
    
    # Start services in dependency order
    echo "Starting core services (database, cache, storage)..."
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" up -d test-postgres test-redis test-minio
    
    echo "Waiting for core services to be healthy..."
    wait_for_service "test-postgres" "5433"
    wait_for_service "test-redis" "6380"
    wait_for_service "test-minio" "9001"
    
    echo "Starting search and logging services..."
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" up -d test-elasticsearch test-rabbitmq
    
    echo "Waiting for search and messaging services..."
    wait_for_service "test-elasticsearch" "9201"
    wait_for_service "test-rabbitmq" "5673"
    
    echo "Starting application services..."
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" up -d test-logstash test-celery-worker test-api
    
    echo "Waiting for application services..."
    wait_for_service "test-api" "8001"
    
    print_success "All test infrastructure services started"
}

wait_for_service() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    print_info "Waiting for $service_name on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "http://localhost:$port" > /dev/null 2>&1 || \
           nc -z localhost "$port" > /dev/null 2>&1; then
            print_success "$service_name is ready"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within timeout"
    return 1
}

verify_services() {
    print_info "Verifying service health..."
    
    local services=(
        "test-postgres:5433:PostgreSQL"
        "test-redis:6380:Redis"
        "test-minio:9001:MinIO"
        "test-elasticsearch:9201:Elasticsearch"
        "test-rabbitmq:15673:RabbitMQ"
        "test-api:8001:API"
    )
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r service port name <<< "$service_info"
        
        if curl -f -s "http://localhost:$port" > /dev/null 2>&1 || \
           nc -z localhost "$port" > /dev/null 2>&1; then
            print_success "$name is healthy on port $port"
        else
            print_warning "$name may not be fully ready on port $port"
        fi
    done
}

setup_test_data() {
    print_info "Setting up test data and configurations..."
    
    # Wait a bit for services to fully initialize
    sleep 5
    
    # Create MinIO test buckets
    docker exec turdparty_test_minio mc alias set local http://localhost:9000 test_access_key test_secret_key 2>/dev/null || true
    docker exec turdparty_test_minio mc mb local/test-bucket 2>/dev/null || true
    docker exec turdparty_test_minio mc mb local/turdparty-files 2>/dev/null || true
    
    # Create Elasticsearch test indices
    curl -X PUT "localhost:9201/test-logs" -H 'Content-Type: application/json' -d'
    {
      "mappings": {
        "properties": {
          "timestamp": {"type": "date"},
          "level": {"type": "keyword"},
          "message": {"type": "text"},
          "service": {"type": "keyword"}
        }
      }
    }' 2>/dev/null || true
    
    print_success "Test data setup completed"
}

show_service_info() {
    print_info "Test Infrastructure Service Information:"
    echo ""
    echo "📊 Service Endpoints:"
    echo "   PostgreSQL:     localhost:5433 (user: test_user, db: turdparty_test)"
    echo "   Redis:          localhost:6380"
    echo "   MinIO:          localhost:9001 (console), localhost:9002 (API)"
    echo "   Elasticsearch:  localhost:9201"
    echo "   RabbitMQ:       localhost:5673 (AMQP), localhost:15673 (Management)"
    echo "   API:            localhost:8001"
    echo ""
    echo "🔑 Test Credentials:"
    echo "   PostgreSQL:     test_user / test_pass"
    echo "   MinIO:          test_access_key / test_secret_key"
    echo "   RabbitMQ:       test_user / test_pass"
    echo ""
    echo "🧪 Running Tests:"
    echo "   pytest tests/unit/ --use-real-services"
    echo "   pytest tests/integration/ --use-real-services"
    echo ""
    echo "🛑 Stop Infrastructure:"
    echo "   ./scripts/stop-test-infrastructure.sh"
    echo "   docker-compose -f docker-compose.test.yml -p turdparty_test down"
}

main() {
    print_header
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --cleanup-only)
                cleanup_existing
                exit 0
                ;;
            --no-cleanup)
                SKIP_CLEANUP=true
                shift
                ;;
            --timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            -h|--help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --cleanup-only    Only cleanup existing infrastructure"
                echo "  --no-cleanup      Skip cleanup of existing infrastructure"
                echo "  --timeout SECS    Set timeout for service startup (default: 120)"
                echo "  -h, --help        Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    check_prerequisites
    
    if [ "$SKIP_CLEANUP" != "true" ]; then
        cleanup_existing
    fi
    
    start_infrastructure
    verify_services
    setup_test_data
    show_service_info
    
    print_success "Test infrastructure is ready for productive testing!"
    echo ""
    echo -e "${GREEN}🎉 You can now run tests with real services instead of mocks!${NC}"
}

# Handle script interruption
trap 'echo -e "\n${YELLOW}Interrupted. Cleaning up...${NC}"; cleanup_existing; exit 1' INT TERM

main "$@"
