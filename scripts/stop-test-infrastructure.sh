#!/bin/bash
# Stop Test Infrastructure for Mock Replacement
# This script stops and cleans up the containerized test infrastructure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.test.yml"
PROJECT_NAME="turdparty_test"

print_header() {
    echo -e "${BLUE}================================================================================================${NC}"
    echo -e "${BLUE}🛑 TurdParty Test Infrastructure Shutdown${NC}"
    echo -e "${BLUE}================================================================================================${NC}"
    echo -e "${YELLOW}Stopping and cleaning up test infrastructure...${NC}"
    echo ""
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

stop_services() {
    print_info "Stopping test infrastructure services..."
    
    if [ -f "$COMPOSE_FILE" ]; then
        docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" down --volumes --remove-orphans
        print_success "Docker Compose services stopped"
    else
        print_warning "Docker Compose file '$COMPOSE_FILE' not found, skipping compose down"
    fi
}

cleanup_containers() {
    print_info "Cleaning up test containers..."
    
    # Remove any remaining test containers
    local containers=$(docker ps -a --filter "name=turdparty_test_" --format "{{.ID}}" 2>/dev/null || true)
    if [ -n "$containers" ]; then
        echo "$containers" | xargs docker rm -f
        print_success "Removed remaining test containers"
    else
        print_info "No test containers to remove"
    fi
}

cleanup_networks() {
    print_info "Cleaning up test networks..."
    
    # Remove test networks
    local networks=$(docker network ls --filter "name=${PROJECT_NAME}" --format "{{.ID}}" 2>/dev/null || true)
    if [ -n "$networks" ]; then
        echo "$networks" | xargs docker network rm 2>/dev/null || true
        print_success "Removed test networks"
    else
        print_info "No test networks to remove"
    fi
}

cleanup_volumes() {
    print_info "Cleaning up test volumes..."
    
    # Remove test volumes
    local volumes=$(docker volume ls --filter "name=${PROJECT_NAME}" --format "{{.Name}}" 2>/dev/null || true)
    if [ -n "$volumes" ]; then
        echo "$volumes" | xargs docker volume rm 2>/dev/null || true
        print_success "Removed test volumes"
    else
        print_info "No test volumes to remove"
    fi
}

cleanup_images() {
    print_info "Cleaning up unused test images..."
    
    # Remove dangling images
    docker image prune -f > /dev/null 2>&1 || true
    print_success "Cleaned up dangling images"
}

verify_cleanup() {
    print_info "Verifying cleanup..."
    
    local remaining_containers=$(docker ps -a --filter "name=turdparty_test_" --format "{{.Names}}" 2>/dev/null || true)
    local remaining_networks=$(docker network ls --filter "name=${PROJECT_NAME}" --format "{{.Name}}" 2>/dev/null || true)
    local remaining_volumes=$(docker volume ls --filter "name=${PROJECT_NAME}" --format "{{.Name}}" 2>/dev/null || true)
    
    if [ -z "$remaining_containers" ] && [ -z "$remaining_networks" ] && [ -z "$remaining_volumes" ]; then
        print_success "All test infrastructure cleaned up successfully"
        return 0
    else
        print_warning "Some test infrastructure may still remain:"
        [ -n "$remaining_containers" ] && echo "  Containers: $remaining_containers"
        [ -n "$remaining_networks" ] && echo "  Networks: $remaining_networks"
        [ -n "$remaining_volumes" ] && echo "  Volumes: $remaining_volumes"
        return 1
    fi
}

show_status() {
    print_info "Current test infrastructure status:"
    echo ""
    
    echo "🐳 Test Containers:"
    docker ps -a --filter "name=turdparty_test_" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "  No test containers found"
    echo ""
    
    echo "🌐 Test Networks:"
    docker network ls --filter "name=${PROJECT_NAME}" --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}" 2>/dev/null || echo "  No test networks found"
    echo ""
    
    echo "💾 Test Volumes:"
    docker volume ls --filter "name=${PROJECT_NAME}" --format "table {{.Name}}\t{{.Driver}}" 2>/dev/null || echo "  No test volumes found"
    echo ""
}

main() {
    print_header
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --status-only)
                show_status
                exit 0
                ;;
            --keep-volumes)
                KEEP_VOLUMES=true
                shift
                ;;
            --force)
                FORCE_CLEANUP=true
                shift
                ;;
            -h|--help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --status-only     Only show current infrastructure status"
                echo "  --keep-volumes    Keep test data volumes (preserve test data)"
                echo "  --force           Force cleanup even if containers are running"
                echo "  -h, --help        Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Check if Docker is available
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    
    # Stop services
    stop_services
    
    # Cleanup containers
    cleanup_containers
    
    # Cleanup networks
    cleanup_networks
    
    # Cleanup volumes (unless --keep-volumes is specified)
    if [ "$KEEP_VOLUMES" != "true" ]; then
        cleanup_volumes
    else
        print_info "Keeping test volumes as requested"
    fi
    
    # Cleanup unused images
    cleanup_images
    
    # Verify cleanup
    if verify_cleanup; then
        print_success "Test infrastructure shutdown completed successfully!"
        echo ""
        echo -e "${GREEN}🎉 All test infrastructure has been cleaned up${NC}"
        
        if [ "$KEEP_VOLUMES" = "true" ]; then
            echo -e "${YELLOW}📦 Test data volumes were preserved${NC}"
        fi
    else
        print_warning "Cleanup completed with some remaining resources"
        echo ""
        echo -e "${YELLOW}💡 You may need to manually clean up remaining resources${NC}"
        exit 1
    fi
}

# Handle script interruption
trap 'echo -e "\n${YELLOW}Interrupted during cleanup...${NC}"; exit 1' INT TERM

main "$@"
