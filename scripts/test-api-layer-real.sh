#!/bin/bash
# Test API Layer with Real Service Integration
# This script runs API layer tests using real service integration instead of mocks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
REPORTS_DIR="$PROJECT_ROOT/reports/api-layer"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

print_header() {
    echo -e "${BLUE}================================================================================================${NC}"
    echo -e "${BLUE}🌐 TurdParty API Layer Real Testing${NC}"
    echo -e "${BLUE}================================================================================================${NC}"
    echo -e "${YELLOW}Running API layer tests with real service integration...${NC}"
    echo ""
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_prerequisites() {
    print_info "Checking prerequisites for API layer testing..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    
    # Check pytest
    if ! command -v pytest &> /dev/null; then
        print_error "pytest is not installed or not in PATH"
        exit 1
    fi
    
    # Check Python dependencies
    python -c "import httpx, testcontainers" 2>/dev/null || {
        print_error "Required Python packages not installed (httpx, testcontainers)"
        exit 1
    }
    
    print_success "Prerequisites check passed"
}

setup_test_environment() {
    print_info "Setting up test environment..."
    
    # Create reports directory
    mkdir -p "$REPORTS_DIR"
    
    # Clean up any existing test containers
    print_info "Cleaning up existing test containers..."
    docker ps -a --filter "name=testcontainers" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    docker ps -a --filter "label=org.testcontainers=true" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    docker ps -a --filter "label=turdparty.test=true" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    
    print_success "Test environment setup completed"
}

start_test_infrastructure() {
    print_info "Starting test infrastructure..."
    
    # Start the test infrastructure if the script exists
    if [ -f "$PROJECT_ROOT/scripts/start-test-infrastructure.sh" ]; then
        print_info "Starting comprehensive test infrastructure..."
        "$PROJECT_ROOT/scripts/start-test-infrastructure.sh" --no-cleanup &
        INFRA_PID=$!
        
        # Wait for infrastructure to be ready
        sleep 30
        
        # Check if services are responding
        check_service_health() {
            local service_name=$1
            local port=$2
            local max_attempts=10
            local attempt=1
            
            while [ $attempt -le $max_attempts ]; do
                if curl -f -s "http://localhost:$port" > /dev/null 2>&1 || \
                   nc -z localhost "$port" > /dev/null 2>&1; then
                    print_success "$service_name is ready on port $port"
                    return 0
                fi
                
                echo -n "."
                sleep 3
                attempt=$((attempt + 1))
            done
            
            print_warning "$service_name not ready on port $port"
            return 1
        }
        
        # Check key services
        check_service_health "API" "8001"
        check_service_health "MinIO" "9001"
        check_service_health "PostgreSQL" "5433"
        check_service_health "Redis" "6380"
        
    else
        print_warning "Test infrastructure script not found, tests will use individual containers"
    fi
}

run_api_service_integration_tests() {
    print_info "Running API Service Integration tests..."
    
    pytest tests/unit/test_api_layer_real.py::TestRealAPIServiceIntegration \
        --verbose \
        --tb=short \
        --cov=api.v1.routes \
        --cov=services.api.src \
        --cov-report=html:"$REPORTS_DIR/coverage-api-integration" \
        --cov-report=term-missing \
        --junitxml="$REPORTS_DIR/junit-api-integration.xml" \
        --html="$REPORTS_DIR/api-integration-tests.html" \
        --self-contained-html \
        -m "not slow" \
        "$@"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_success "API Service Integration tests passed"
    else
        print_error "API Service Integration tests failed with exit code $exit_code"
        return $exit_code
    fi
}

run_api_endpoints_tests() {
    print_info "Running API Endpoints tests..."
    
    pytest tests/unit/test_api_layer_real.py::TestRealAPIEndpoints \
        --verbose \
        --tb=short \
        --cov=api.v1.routes \
        --cov-report=html:"$REPORTS_DIR/coverage-api-endpoints" \
        --cov-report=term-missing \
        --junitxml="$REPORTS_DIR/junit-api-endpoints.xml" \
        --html="$REPORTS_DIR/api-endpoints-tests.html" \
        --self-contained-html \
        -m "not slow" \
        "$@"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_success "API Endpoints tests passed"
    else
        print_error "API Endpoints tests failed with exit code $exit_code"
        return $exit_code
    fi
}

run_api_routes_tests() {
    print_info "Running API Routes tests..."
    
    pytest tests/unit/test_api_routes_real.py \
        --verbose \
        --tb=short \
        --cov=api.v1.routes \
        --cov=services.api.src.routes \
        --cov-report=html:"$REPORTS_DIR/coverage-api-routes" \
        --cov-report=term-missing \
        --junitxml="$REPORTS_DIR/junit-api-routes.xml" \
        --html="$REPORTS_DIR/api-routes-tests.html" \
        --self-contained-html \
        -m "not slow" \
        "$@"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_success "API Routes tests passed"
    else
        print_error "API Routes tests failed with exit code $exit_code"
        return $exit_code
    fi
}

run_api_workflows_tests() {
    print_info "Running API Workflows tests..."
    
    pytest tests/unit/test_api_layer_real.py::TestRealAPIWorkflows \
        --verbose \
        --tb=short \
        --cov=api.v1.routes \
        --cov-report=html:"$REPORTS_DIR/coverage-api-workflows" \
        --cov-report=term-missing \
        --junitxml="$REPORTS_DIR/junit-api-workflows.xml" \
        --html="$REPORTS_DIR/api-workflows-tests.html" \
        --self-contained-html \
        -m "not slow" \
        "$@"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_success "API Workflows tests passed"
    else
        print_error "API Workflows tests failed with exit code $exit_code"
        return $exit_code
    fi
}

run_performance_tests() {
    print_info "Running API Performance tests..."
    
    pytest tests/unit/test_api_layer_real.py::TestRealAPIPerformance \
        --verbose \
        --tb=short \
        --junitxml="$REPORTS_DIR/junit-api-performance.xml" \
        --html="$REPORTS_DIR/api-performance-tests.html" \
        --self-contained-html \
        --benchmark-only \
        --benchmark-json="$REPORTS_DIR/api-benchmark-results.json" \
        "$@"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_success "API Performance tests passed"
    else
        print_warning "API Performance tests had issues (exit code $exit_code)"
        # Don't fail the entire suite for performance tests
    fi
}

cleanup_test_environment() {
    print_info "Cleaning up test environment..."
    
    # Stop test infrastructure if we started it
    if [ -n "$INFRA_PID" ]; then
        print_info "Stopping test infrastructure..."
        kill $INFRA_PID 2>/dev/null || true
        wait $INFRA_PID 2>/dev/null || true
        
        # Run cleanup script if available
        if [ -f "$PROJECT_ROOT/scripts/stop-test-infrastructure.sh" ]; then
            "$PROJECT_ROOT/scripts/stop-test-infrastructure.sh" --force
        fi
    fi
    
    # Remove test containers
    docker ps -a --filter "name=testcontainers" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    docker ps -a --filter "label=org.testcontainers=true" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    docker ps -a --filter "label=turdparty.test=true" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    
    # Remove dangling images
    docker image prune -f &> /dev/null || true
    
    print_success "Test environment cleanup completed"
}

generate_report() {
    print_info "Generating test report..."
    
    local report_file="$REPORTS_DIR/api-layer-test-report-$TIMESTAMP.md"
    
    cat > "$report_file" << EOF
# API Layer Real Testing Report

Generated: $(date)

## Test Summary

This report covers API layer testing using real service integration with central service URL management instead of mocks.

## Test Categories

### API Service Integration Tests
- **File**: tests/unit/test_api_layer_real.py::TestRealAPIServiceIntegration
- **Coverage**: Service health, URL resolution, OpenAPI schema, CORS headers

### API Endpoints Tests  
- **File**: tests/unit/test_api_layer_real.py::TestRealAPIEndpoints
- **Coverage**: Files API, VMs API, health endpoints, error handling

### API Routes Tests
- **File**: tests/unit/test_api_routes_real.py
- **Coverage**: Route registration, middleware, WebSockets, authentication

### API Workflows Tests
- **File**: tests/unit/test_api_layer_real.py::TestRealAPIWorkflows
- **Coverage**: File upload workflow, VM creation workflow, service integration

### Performance Tests
- **File**: tests/unit/test_api_layer_real.py::TestRealAPIPerformance
- **Coverage**: Response times, concurrent requests, load handling

## Test Results

EOF

    # Add test results if available
    if [ -f "$REPORTS_DIR/junit-api-integration.xml" ]; then
        echo "### API Integration Test Results" >> "$report_file"
        echo "\`\`\`" >> "$report_file"
        grep -E "(tests|failures|errors)" "$REPORTS_DIR/junit-api-integration.xml" | head -5 >> "$report_file" || true
        echo "\`\`\`" >> "$report_file"
        echo "" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

## Coverage Reports

- API Integration Coverage: reports/api-layer/coverage-api-integration/index.html
- API Endpoints Coverage: reports/api-layer/coverage-api-endpoints/index.html  
- API Routes Coverage: reports/api-layer/coverage-api-routes/index.html
- API Workflows Coverage: reports/api-layer/coverage-api-workflows/index.html

## Next Steps

1. Review any test failures and fix issues
2. Update mock-based tests to use real service integration
3. Add more edge case testing
4. Optimize performance for larger request volumes

EOF

    print_success "Test report generated: $report_file"
}

main() {
    print_header
    
    # Parse command line arguments
    RUN_PERFORMANCE=false
    SKIP_CLEANUP=false
    START_INFRASTRUCTURE=true
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --performance)
                RUN_PERFORMANCE=true
                shift
                ;;
            --skip-cleanup)
                SKIP_CLEANUP=true
                shift
                ;;
            --no-infrastructure)
                START_INFRASTRUCTURE=false
                shift
                ;;
            --help|-h)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --performance        Run performance tests"
                echo "  --skip-cleanup       Skip cleanup after tests"
                echo "  --no-infrastructure  Don't start test infrastructure"
                echo "  --help, -h           Show this help message"
                exit 0
                ;;
            *)
                # Pass unknown arguments to pytest
                break
                ;;
        esac
    done
    
    # Run test suite
    local overall_exit_code=0
    
    check_prerequisites
    setup_test_environment
    
    if [ "$START_INFRASTRUCTURE" = true ]; then
        start_test_infrastructure
    fi
    
    # Run test suites
    run_api_service_integration_tests "$@" || overall_exit_code=$?
    run_api_endpoints_tests "$@" || overall_exit_code=$?
    run_api_routes_tests "$@" || overall_exit_code=$?
    run_api_workflows_tests "$@" || overall_exit_code=$?
    
    if [ "$RUN_PERFORMANCE" = true ]; then
        run_performance_tests "$@"
    fi
    
    # Cleanup unless skipped
    if [ "$SKIP_CLEANUP" != true ]; then
        cleanup_test_environment
    fi
    
    generate_report
    
    if [ $overall_exit_code -eq 0 ]; then
        print_success "All API layer tests completed successfully!"
        echo ""
        echo -e "${GREEN}🎉 API Layer migration to real service integration is working!${NC}"
    else
        print_error "Some API layer tests failed"
        echo ""
        echo -e "${RED}💥 Check the reports for details: $REPORTS_DIR${NC}"
        exit $overall_exit_code
    fi
}

# Handle script interruption
trap 'echo -e "\n${YELLOW}Interrupted. Cleaning up...${NC}"; cleanup_test_environment; exit 1' INT TERM

main "$@"
