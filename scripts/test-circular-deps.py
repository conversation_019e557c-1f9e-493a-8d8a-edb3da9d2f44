#!/usr/bin/env python3
"""
Test script for circular dependency detection
"""

import sys
import importlib.util
from pathlib import Path

# Import the health check manager module
spec = importlib.util.spec_from_file_location("health_check_manager", Path(__file__).parent / "health-check-manager.py")
health_check_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(health_check_module)

HealthCheckManager = health_check_module.HealthCheckManager

def test_circular_dependencies():
    """Test circular dependency detection with various scenarios"""
    
    print("🧪 Testing Circular Dependency Detection")
    print("=" * 50)
    
    # Test 1: No circular dependencies (current config)
    print("\n1️⃣ Testing current configuration (should be clean):")
    manager = HealthCheckManager(silent=True)
    cycles = manager._detect_circular_dependencies()
    if cycles:
        print(f"   ❌ Found cycles: {cycles}")
    else:
        print("   ✅ No cycles detected")
    
    # Test 2: Simple circular dependency (A -> B -> A)
    print("\n2️⃣ Testing simple circular dependency (api ↔ frontend):")
    manager.dependency_graph["api"] = ["database", "redis", "storage", "frontend"]  # Add frontend to api deps
    cycles = manager._detect_circular_dependencies()
    if cycles:
        print(f"   ✅ Correctly detected cycles: {cycles}")
    else:
        print("   ❌ Failed to detect expected cycle")
    
    # Test 3: Complex circular dependency (A -> B -> C -> A)
    print("\n3️⃣ Testing complex circular dependency (api -> frontend -> status -> api):")
    manager.dependency_graph["status"] = ["api"]  # status depends on api
    cycles = manager._detect_circular_dependencies()
    if cycles:
        print(f"   ✅ Correctly detected cycles: {cycles}")
    else:
        print("   ❌ Failed to detect expected cycle")
    
    # Test 4: Self-dependency (A -> A)
    print("\n4️⃣ Testing self-dependency (redis -> redis):")
    manager.dependency_graph["redis"] = ["redis"]  # redis depends on itself
    cycles = manager._detect_circular_dependencies()
    if cycles:
        print(f"   ✅ Correctly detected self-dependency: {cycles}")
    else:
        print("   ❌ Failed to detect self-dependency")
    
    print("\n🏁 Circular dependency testing complete!")

if __name__ == "__main__":
    test_circular_dependencies()
