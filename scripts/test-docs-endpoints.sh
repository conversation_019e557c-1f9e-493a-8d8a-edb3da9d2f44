#!/bin/bash

# Quick test script for documentation endpoints
# Tests all TurdParty documentation URLs

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✅]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠️]${NC} $1"
}

print_error() {
    echo -e "${RED}[❌]${NC} $1"
}

# Test function
test_endpoint() {
    local name="$1"
    local url="$2"
    local expected_code="${3:-200}"
    local timeout="${4:-10}"
    
    print_status "Testing $name: $url"
    
    # Test with curl
    local response
    local http_code
    
    response=$(curl -s -w "\n%{http_code}" --max-time "$timeout" "$url" 2>/dev/null || echo -e "\nERROR")
    http_code=$(echo "$response" | tail -n1)
    content=$(echo "$response" | head -n -1)
    
    if [ "$http_code" = "ERROR" ]; then
        print_error "$name: Connection failed"
        return 1
    elif [ "$http_code" = "$expected_code" ]; then
        print_success "$name: HTTP $http_code"
        
        # Check content length
        local content_length=${#content}
        if [ "$content_length" -gt 100 ]; then
            print_success "$name: Content loaded ($content_length bytes)"
        else
            print_warning "$name: Content seems short ($content_length bytes)"
        fi
        
        return 0
    else
        print_error "$name: HTTP $http_code (expected $expected_code)"
        return 1
    fi
}

# Test content for specific terms
test_content() {
    local name="$1"
    local url="$2"
    local search_term="$3"
    
    print_status "Testing $name content for '$search_term'"
    
    local content
    content=$(curl -s --max-time 10 "$url" 2>/dev/null || echo "ERROR")
    
    if [ "$content" = "ERROR" ]; then
        print_error "$name: Failed to fetch content"
        return 1
    fi
    
    if echo "$content" | grep -qi "$search_term"; then
        print_success "$name: Contains '$search_term'"
        return 0
    else
        print_warning "$name: Does not contain '$search_term'"
        return 1
    fi
}

print_status "🧪 Testing TurdParty Documentation Endpoints"
echo "=============================================="

# Test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0

# Core API endpoints
print_status "\n📡 Testing Core API Endpoints"
echo "------------------------------"

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if test_endpoint "API Health" "http://localhost:8000/health"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if test_endpoint "API Status" "http://localhost:8000/api/v1/status"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if test_endpoint "API Docs (Swagger)" "http://localhost:8000/docs"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if test_endpoint "OpenAPI Spec" "http://localhost:8000/openapi.json"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Documentation endpoints via Traefik
print_status "\n📚 Testing Documentation Endpoints (Traefik)"
echo "----------------------------------------------"

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if test_endpoint "Main Documentation" "http://docs.turdparty.localhost"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if test_endpoint "API Documentation" "http://docs.turdparty.localhost/api/"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if test_endpoint "Documentation Health" "http://docs.turdparty.localhost/health"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Frontend endpoints
print_status "\n🌐 Testing Frontend Endpoints"
echo "------------------------------"

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if test_endpoint "Frontend" "http://frontend.turdparty.localhost"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Optional services (might not be running)
print_status "\n🔧 Testing Optional Services"
echo "-----------------------------"

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if test_endpoint "Kibana" "http://kibana.turdparty.localhost" 200 5; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "Kibana not accessible (optional service)"
fi

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if test_endpoint "Status Dashboard" "http://status.turdparty.localhost" 200 5; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "Status dashboard not accessible (optional service)"
fi

# Content tests
print_status "\n📝 Testing Content Quality"
echo "---------------------------"

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if test_content "Main Docs Content" "http://docs.turdparty.localhost" "TurdParty"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if test_content "API Docs Content" "http://localhost:8000/docs" "swagger"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if test_content "Dark Mode Check" "http://docs.turdparty.localhost" "data-theme"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Error handling tests
print_status "\n🚨 Testing Error Handling"
echo "--------------------------"

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if test_endpoint "404 Error Page" "http://docs.turdparty.localhost/nonexistent" 404; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Performance tests
print_status "\n⚡ Testing Performance"
echo "----------------------"

print_status "Testing response times..."

# Test API response time
start_time=$(date +%s.%N)
curl -s http://localhost:8000/health > /dev/null
end_time=$(date +%s.%N)
api_time=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "0.1")

if (( $(echo "$api_time < 1.0" | bc -l 2>/dev/null || echo "1") )); then
    print_success "API response time: ${api_time}s (< 1.0s)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "API response time: ${api_time}s (slow)"
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test docs response time
start_time=$(date +%s.%N)
curl -s http://docs.turdparty.localhost > /dev/null 2>&1
end_time=$(date +%s.%N)
docs_time=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "0.5")

if (( $(echo "$docs_time < 2.0" | bc -l 2>/dev/null || echo "1") )); then
    print_success "Docs response time: ${docs_time}s (< 2.0s)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "Docs response time: ${docs_time}s (slow)"
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Summary
echo ""
echo "=============================================="
print_status "📊 Test Results Summary"
echo "=============================================="

if [ "$PASSED_TESTS" -eq "$TOTAL_TESTS" ]; then
    print_success "All tests passed! ($PASSED_TESTS/$TOTAL_TESTS)"
    echo ""
    print_success "🎉 Documentation endpoints are working correctly!"
    echo ""
    echo "📚 Access your documentation:"
    echo "   • Main Docs: http://docs.turdparty.localhost"
    echo "   • API Docs:  http://localhost:8000/docs"
    echo "   • Frontend:  http://frontend.turdparty.localhost"
    echo ""
    exit 0
else
    print_warning "Some tests failed: $PASSED_TESTS/$TOTAL_TESTS passed"
    
    failed_tests=$((TOTAL_TESTS - PASSED_TESTS))
    print_error "$failed_tests tests failed"
    
    echo ""
    print_status "🔧 Troubleshooting:"
    echo "   • Check if all services are running: docker-compose ps"
    echo "   • Restart services: docker-compose restart"
    echo "   • Check logs: docker-compose logs docs"
    echo "   • Rebuild docs: ./scripts/build-docs-auto.sh build"
    echo ""
    exit 1
fi
