#!/bin/bash

# TurdParty Git Hooks Test Script
# Tests the pre-push hook under different scenarios

set -e

# ANSI color codes
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print banner
print_banner() {
    echo ""
    print_colored $CYAN "╔══════════════════════════════════════════════════════════════╗"
    print_colored $CYAN "║                🧪 TurdParty Git Hooks Test                   ║"
    print_colored $CYAN "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to test hook with different scenarios
test_scenario() {
    local description="$1"
    local remote_url="$2"
    local expected_result="$3"
    local input="$4"
    
    print_colored $BLUE "🔬 Testing: $description"
    print_colored $BLUE "   Remote: $remote_url"
    print_colored $BLUE "   Expected: $expected_result"
    echo ""
    
    # Run the hook and capture result
    local result=0
    if [ -n "$input" ]; then
        echo "$input" | .git/hooks/pre-push origin "$remote_url" > /tmp/hook_output 2>&1 || result=$?
    else
        .git/hooks/pre-push origin "$remote_url" > /tmp/hook_output 2>&1 || result=$?
    fi
    
    # Check result
    if [ "$expected_result" = "allow" ] && [ $result -eq 0 ]; then
        print_colored $GREEN "   ✅ PASS: Hook allowed push as expected"
    elif [ "$expected_result" = "block" ] && [ $result -ne 0 ]; then
        print_colored $GREEN "   ✅ PASS: Hook blocked push as expected"
    elif [ "$expected_result" = "prompt" ] && [ $result -ne 0 ]; then
        print_colored $GREEN "   ✅ PASS: Hook prompted and blocked as expected"
    else
        print_colored $RED "   ❌ FAIL: Unexpected result (exit code: $result)"
        print_colored $YELLOW "   Output:"
        cat /tmp/hook_output | sed 's/^/      /'
    fi
    
    echo ""
    echo "   Hook output:"
    cat /tmp/hook_output | sed 's/^/      /' | head -10
    echo ""
    print_colored $CYAN "   ────────────────────────────────────────────────────────────"
    echo ""
}

# Function to check if hook is installed
check_hook_installation() {
    if [ ! -f ".git/hooks/pre-push" ]; then
        print_colored $RED "❌ Error: pre-push hook not found!"
        print_colored $YELLOW "Please run: ./scripts/setup-git-hooks.sh"
        exit 1
    fi
    
    if [ ! -x ".git/hooks/pre-push" ]; then
        print_colored $RED "❌ Error: pre-push hook not executable!"
        print_colored $YELLOW "Please run: chmod +x .git/hooks/pre-push"
        exit 1
    fi
    
    print_colored $GREEN "✅ Pre-push hook found and executable"
    echo ""
}

# Function to show current time info
show_time_info() {
    local current_hour=$(TZ='Europe/Berlin' date '+%H' 2>/dev/null || date '+%H')
    local day_of_week=$(date '+%u')
    local day_name=$(date '+%A')
    
    print_colored $PURPLE "⏰ Current Time Information:"
    print_colored $PURPLE "   • Current time: ${current_hour}:00 CET"
    print_colored $PURPLE "   • Day: $day_name (day $day_of_week of week)"
    
    if [ $day_of_week -eq 6 ] || [ $day_of_week -eq 7 ]; then
        print_colored $GREEN "   • Status: Weekend - GitHub pushes allowed"
    elif [ $((10#$current_hour)) -ge 7 ] && [ $((10#$current_hour)) -lt 18 ]; then
        print_colored $RED "   • Status: Business hours - GitHub pushes discouraged"
    else
        print_colored $GREEN "   • Status: Outside business hours - GitHub pushes allowed"
    fi
    echo ""
}

# Main test function
main() {
    print_banner
    
    # Check if we're in the right directory
    if [ ! -f ".git/config" ]; then
        print_colored $RED "❌ Error: Not in a git repository root!"
        print_colored $YELLOW "Please run this script from the repository root."
        exit 1
    fi
    
    # Check hook installation
    check_hook_installation
    
    # Show current time info
    show_time_info
    
    print_colored $BLUE "🧪 Running Git Hook Tests..."
    echo ""
    
    # Test 1: Non-GitHub remote (should always allow)
    test_scenario \
        "Non-GitHub remote (GitLab)" \
        "https://gitlab.example.com/user/repo.git" \
        "allow"
    
    # Test 2: Non-GitHub remote (should always allow)
    test_scenario \
        "Non-GitHub remote (Bitbucket)" \
        "https://bitbucket.org/user/repo.git" \
        "allow"
    
    # Test 3: GitHub remote with 'n' response (should block)
    test_scenario \
        "GitHub remote during business hours (decline)" \
        "https://github.com/user/repo.git" \
        "block" \
        "n"
    
    # Test 4: GitHub remote with 'y' response (should allow)
    test_scenario \
        "GitHub remote during business hours (accept)" \
        "https://github.com/user/repo.git" \
        "allow" \
        "y"
    
    # Test 5: GitHub SSH remote (should behave same as HTTPS)
    test_scenario \
        "GitHub SSH remote (decline)" \
        "**************:user/repo.git" \
        "block" \
        "n"
    
    # Test 6: Local/internal remote (should always allow)
    test_scenario \
        "Local remote" \
        "/path/to/local/repo.git" \
        "allow"
    
    print_colored $GREEN "🎉 Git hook testing completed!"
    echo ""
    
    print_colored $PURPLE "📋 Test Summary:"
    print_colored $PURPLE "   • Non-GitHub remotes: Always allowed"
    print_colored $PURPLE "   • GitHub remotes: Depends on time and user response"
    print_colored $PURPLE "   • Business hours: 07:00-18:00 CET (Mon-Fri)"
    print_colored $PURPLE "   • Override: Use 'git push --no-verify' to bypass"
    echo ""
    
    # Clean up
    rm -f /tmp/hook_output
}

# Run main function
main "$@"
