#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 Health Check Test Suite

Comprehensive testing suite for the TurdParty dependency-aware health check system.
Validates health check functionality, dependency graph integrity, Docker configuration,
and CLI integration to ensure reliable service monitoring.

Key Features:
    - Dependency graph validation with circular dependency detection
    - Health check manager functionality testing
    - Docker Compose health check configuration validation
    - CLI integration testing for health check commands
    - Service dependency logic verification
    - Health check timing and configuration validation

Test Categories:
    1. **Dependency Graph Validation**: Circular dependency detection
    2. **Health Check Manager**: JSON output and basic functionality
    3. **Docker Compose Health Checks**: Service health check configuration
    4. **CLI Integration**: Command-line health check integration
    5. **Service Dependency Logic**: Dependency relationship validation
    6. **Health Check Timing**: Timing configuration validation

Test Modes:
    - Default: Standard test suite with static validation
    - --live: Live testing against running services (requires services up)
    - --quick: Quick tests only (excludes live service tests)

Validation Checks:
    - No circular dependencies in service graph
    - All services have proper health check configuration
    - Health check timing is reasonable (not too fast/slow)
    - CLI commands execute successfully
    - Service dependencies match expected configuration

Usage:
    python scripts/test-health-checks.py              # Standard test suite
    python scripts/test-health-checks.py --live       # Live service testing
    python scripts/test-health-checks.py --quick      # Quick validation only

Output:
    - Detailed test results with pass/fail status
    - Performance timing for each test
    - Comprehensive error details for failed tests
    - Overall success rate and summary statistics

Integration:
    - Imports and tests health-check-manager.py functionality
    - Validates docker-compose.yml health check configuration
    - Tests CLI integration with turdparty-cli.py commands
    - Ensures proper service dependency configuration
"""

import json
import subprocess
import time
import sys
import importlib.util
from pathlib import Path
from typing import Dict, List, Set, Optional
from dataclasses import dataclass
from enum import Enum


class TestResult(Enum):
    """
    Enumeration of possible test result values.

    Provides standardized result values for consistent test reporting
    across all health check test cases.
    """
    PASS = "PASS"
    FAIL = "FAIL"
    SKIP = "SKIP"


@dataclass
class TestCase:
    """
    Data class representing a single test case result.

    Contains comprehensive test information including name, description,
    result status, error details, and execution timing.

    Attributes:
        name: Test case name identifier
        description: Human-readable test description
        result: Test result from TestResult enum
        details: Optional error details or additional information
        duration: Test execution time in seconds
    """
    name: str
    description: str
    result: TestResult
    details: Optional[str] = None
    duration: float = 0.0


class HealthCheckTestSuite:
    """
    Comprehensive test suite for TurdParty health checks.

    Provides complete validation of the health check system including
    dependency graph validation, Docker configuration testing, and
    CLI integration verification.
    """
    
    def __init__(self):
        """
        Initialize the health check test suite.

        Sets up project paths and imports the health check manager
        for comprehensive testing.
        """
        self.project_root = Path(__file__).parent.parent.absolute()
        self.test_results: List[TestCase] = []
        
        # Import health check manager
        spec = importlib.util.spec_from_file_location(
            "health_check_manager", 
            self.project_root / "scripts" / "health-check-manager.py"
        )
        self.health_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(self.health_module)
        
    def run_all_tests(self) -> bool:
        """
        Run all health check tests.

        Executes the complete test suite including dependency validation,
        health check manager testing, Docker configuration validation,
        and CLI integration testing.

        Returns:
            bool: True if all tests pass, False if any test fails
        """
        print("🧪 TurdParty Health Check Test Suite")
        print("=" * 60)
        
        # Test 1: Dependency Graph Validation
        self._test_dependency_graph_validation()
        
        # Test 2: Health Check Manager Functionality
        self._test_health_check_manager()
        
        # Test 3: Docker Compose Health Check Configuration
        self._test_docker_compose_health_checks()
        
        # Test 4: CLI Integration
        self._test_cli_integration()
        
        # Test 5: Service Dependency Logic
        self._test_service_dependency_logic()
        
        # Test 6: Health Check Timing and Retries
        self._test_health_check_timing()
        
        # Print results
        self._print_test_results()
        
        # Return overall success
        failed_tests = [t for t in self.test_results if t.result == TestResult.FAIL]
        return len(failed_tests) == 0
    
    def _test_dependency_graph_validation(self):
        """Test 1: Validate dependency graph has no circular dependencies"""
        start_time = time.time()
        
        try:
            manager = self.health_module.HealthCheckManager(silent=True)
            circular_deps = manager._detect_circular_dependencies()
            
            if not circular_deps:
                self.test_results.append(TestCase(
                    name="Dependency Graph Validation",
                    description="No circular dependencies detected",
                    result=TestResult.PASS,
                    duration=time.time() - start_time
                ))
            else:
                self.test_results.append(TestCase(
                    name="Dependency Graph Validation",
                    description="Circular dependencies found",
                    result=TestResult.FAIL,
                    details=f"Circular dependencies: {circular_deps}",
                    duration=time.time() - start_time
                ))
                
        except Exception as e:
            self.test_results.append(TestCase(
                name="Dependency Graph Validation",
                description="Exception during validation",
                result=TestResult.FAIL,
                details=str(e),
                duration=time.time() - start_time
            ))
    
    def _test_health_check_manager(self):
        """Test 2: Health check manager basic functionality"""
        start_time = time.time()
        
        try:
            # Test JSON output
            result = subprocess.run([
                sys.executable, 
                str(self.project_root / "scripts" / "health-check-manager.py"),
                "--json"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                # Try to parse JSON
                health_data = json.loads(result.stdout)
                
                # Validate structure
                required_keys = ["timestamp", "services", "summary"]
                if all(key in health_data for key in required_keys):
                    self.test_results.append(TestCase(
                        name="Health Check Manager JSON",
                        description="JSON output format valid",
                        result=TestResult.PASS,
                        duration=time.time() - start_time
                    ))
                else:
                    self.test_results.append(TestCase(
                        name="Health Check Manager JSON",
                        description="JSON output missing required keys",
                        result=TestResult.FAIL,
                        details=f"Missing keys: {set(required_keys) - set(health_data.keys())}",
                        duration=time.time() - start_time
                    ))
            else:
                self.test_results.append(TestCase(
                    name="Health Check Manager JSON",
                    description="Health check manager failed",
                    result=TestResult.FAIL,
                    details=f"Exit code: {result.returncode}, stderr: {result.stderr}",
                    duration=time.time() - start_time
                ))
                
        except Exception as e:
            self.test_results.append(TestCase(
                name="Health Check Manager JSON",
                description="Exception during test",
                result=TestResult.FAIL,
                details=str(e),
                duration=time.time() - start_time
            ))
    
    def _test_docker_compose_health_checks(self):
        """Test 3: Verify all services in docker-compose have health checks"""
        start_time = time.time()
        
        try:
            # Parse docker-compose.yml
            compose_file = self.project_root / "docker-compose.yml"
            
            if not compose_file.exists():
                self.test_results.append(TestCase(
                    name="Docker Compose Health Checks",
                    description="docker-compose.yml not found",
                    result=TestResult.FAIL,
                    duration=time.time() - start_time
                ))
                return
            
            # Count services and health checks
            with open(compose_file, 'r') as f:
                content = f.read()
            
            # Simple parsing - count service definitions and healthcheck blocks
            total_services = content.count('container_name: turdpartycollab_')
            commented_services = content.count('#   container_name: turdpartycollab_')
            service_count = total_services - commented_services

            healthcheck_count = content.count('healthcheck:')

            # Account for commented out services (like reports)
            commented_healthchecks = content.count('#   healthcheck:')
            active_healthchecks = healthcheck_count - commented_healthchecks
            
            if service_count == active_healthchecks:
                self.test_results.append(TestCase(
                    name="Docker Compose Health Checks",
                    description=f"All {service_count} services have health checks",
                    result=TestResult.PASS,
                    duration=time.time() - start_time
                ))
            else:
                self.test_results.append(TestCase(
                    name="Docker Compose Health Checks",
                    description="Not all services have health checks",
                    result=TestResult.FAIL,
                    details=f"Services: {service_count}, Health checks: {active_healthchecks}",
                    duration=time.time() - start_time
                ))
                
        except Exception as e:
            self.test_results.append(TestCase(
                name="Docker Compose Health Checks",
                description="Exception during test",
                result=TestResult.FAIL,
                details=str(e),
                duration=time.time() - start_time
            ))
    
    def _test_cli_integration(self):
        """Test 4: CLI integration with health checks"""
        start_time = time.time()
        
        try:
            # Test CLI dependency validation
            result = subprocess.run([
                sys.executable,
                str(self.project_root / "scripts" / "turdparty-cli.py"),
                "check", "--validate-deps"
            ], capture_output=True, text=True, timeout=30)
            
            # CLI should run without errors (exit code 0)
            if result.returncode == 0:
                self.test_results.append(TestCase(
                    name="CLI Health Check Integration",
                    description="CLI dependency validation runs successfully",
                    result=TestResult.PASS,
                    duration=time.time() - start_time
                ))
            else:
                self.test_results.append(TestCase(
                    name="CLI Health Check Integration",
                    description="CLI dependency validation failed",
                    result=TestResult.FAIL,
                    details=f"Exit code: {result.returncode}, stderr: {result.stderr}",
                    duration=time.time() - start_time
                ))
                
        except Exception as e:
            self.test_results.append(TestCase(
                name="CLI Health Check Integration",
                description="Exception during CLI test",
                result=TestResult.FAIL,
                details=str(e),
                duration=time.time() - start_time
            ))
    
    def _test_service_dependency_logic(self):
        """Test 5: Service dependency logic"""
        start_time = time.time()
        
        try:
            manager = self.health_module.HealthCheckManager(silent=True)
            
            # Test dependency checking logic
            test_cases = [
                ("database", []),  # No dependencies
                ("api", ["database", "redis", "storage"]),  # Has dependencies
                ("frontend", ["api"]),  # Depends on api
                ("filebeat", ["elasticsearch", "logstash"])  # Multiple dependencies
            ]
            
            all_passed = True
            failed_details = []
            
            for service, expected_deps in test_cases:
                actual_deps = manager.dependency_graph.get(service, [])
                if set(actual_deps) != set(expected_deps):
                    all_passed = False
                    failed_details.append(f"{service}: expected {expected_deps}, got {actual_deps}")
            
            if all_passed:
                self.test_results.append(TestCase(
                    name="Service Dependency Logic",
                    description="All service dependencies correctly defined",
                    result=TestResult.PASS,
                    duration=time.time() - start_time
                ))
            else:
                self.test_results.append(TestCase(
                    name="Service Dependency Logic",
                    description="Service dependency mismatches found",
                    result=TestResult.FAIL,
                    details="; ".join(failed_details),
                    duration=time.time() - start_time
                ))
                
        except Exception as e:
            self.test_results.append(TestCase(
                name="Service Dependency Logic",
                description="Exception during dependency logic test",
                result=TestResult.FAIL,
                details=str(e),
                duration=time.time() - start_time
            ))
    
    def _test_health_check_timing(self):
        """Test 6: Health check timing and configuration"""
        start_time = time.time()
        
        try:
            # Parse docker-compose.yml for health check timing
            compose_file = self.project_root / "docker-compose.yml"
            
            with open(compose_file, 'r') as f:
                content = f.read()
            
            # Check for reasonable timing values
            issues = []
            
            # Check for very short intervals (< 10s)
            if 'interval: 5s' in content or 'interval: 1s' in content:
                issues.append("Very short health check intervals detected")
            
            # Check for very long timeouts (> 30s)
            if 'timeout: 60s' in content or 'timeout: 120s' in content:
                issues.append("Very long health check timeouts detected")
            
            # Check for reasonable retry counts
            if 'retries: 10' in content or 'retries: 20' in content:
                issues.append("Excessive retry counts detected")
            
            if not issues:
                self.test_results.append(TestCase(
                    name="Health Check Timing",
                    description="Health check timing configuration is reasonable",
                    result=TestResult.PASS,
                    duration=time.time() - start_time
                ))
            else:
                self.test_results.append(TestCase(
                    name="Health Check Timing",
                    description="Health check timing issues found",
                    result=TestResult.FAIL,
                    details="; ".join(issues),
                    duration=time.time() - start_time
                ))
                
        except Exception as e:
            self.test_results.append(TestCase(
                name="Health Check Timing",
                description="Exception during timing test",
                result=TestResult.FAIL,
                details=str(e),
                duration=time.time() - start_time
            ))
    
    def _print_test_results(self):
        """Print formatted test results"""
        print(f"\n📊 Test Results Summary")
        print("=" * 60)
        
        passed = sum(1 for t in self.test_results if t.result == TestResult.PASS)
        failed = sum(1 for t in self.test_results if t.result == TestResult.FAIL)
        skipped = sum(1 for t in self.test_results if t.result == TestResult.SKIP)
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"⏭️  Skipped: {skipped}")
        print(f"Success Rate: {(passed/total*100):.1f}%")
        
        print(f"\n📋 Detailed Results:")
        print("-" * 60)
        
        for test in self.test_results:
            status_icon = "✅" if test.result == TestResult.PASS else "❌" if test.result == TestResult.FAIL else "⏭️"
            print(f"{status_icon} {test.name}")
            print(f"   {test.description}")
            if test.details:
                print(f"   Details: {test.details}")
            print(f"   Duration: {test.duration:.2f}s")
            print()


def main():
    """
    Main entry point for the health check test suite.

    Supports multiple test modes including live testing against running
    services and quick validation for CI/CD pipelines.
    """
    import argparse

    parser = argparse.ArgumentParser(description="TurdParty Health Check Test Suite")
    parser.add_argument("--live", action="store_true",
                       help="Run live tests against running services (requires services to be up)")
    parser.add_argument("--quick", action="store_true",
                       help="Run only quick tests (no live service tests)")

    args = parser.parse_args()

    test_suite = HealthCheckTestSuite()

    if args.live:
        print("🔴 Live testing mode - requires running services!")
        print("Make sure TurdParty services are running first.\n")
        # Add live tests here in the future

    success = test_suite.run_all_tests()

    if success:
        print("🎉 All health check tests passed!")
        sys.exit(0)
    else:
        print("💥 Some health check tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
