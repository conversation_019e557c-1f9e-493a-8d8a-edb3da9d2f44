#!/bin/bash

# 💩🎉TurdParty🎉💩 32-Core Parallel Test Demonstration
# Shows the power of 32 concurrent test workers with comprehensive output

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
RED='\033[0;31m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
TIMESTAMP=$(date '+%Y-%m-%d_%H:%M:%S')
LOG_DIR="test-results/parallel-32"
mkdir -p "$LOG_DIR"

echo -e "${PURPLE}================================${NC}"
echo -e "${PURPLE}💩🎉TurdParty🎉💩 32-Core Parallel Testing Demo${NC}"
echo -e "${PURPLE}================================${NC}"
echo -e "${CYAN}Timestamp: ${TIMESTAMP}${NC}"
echo ""

# Set environment variables for maximum parallelism
export MAX_PARALLEL_JOBS=32
export PARALLEL_WORKERS=32

echo -e "${BLUE}🚀 System Configuration:${NC}"
echo -e "   Max Parallel Jobs: ${MAX_PARALLEL_JOBS}"
echo -e "   Parallel Workers: ${PARALLEL_WORKERS}"
echo -e "   CPU Cores Available: $(nproc)"
echo -e "   Memory Available: $(free -h | grep '^Mem:' | awk '{print $2}')"
echo -e "   Python Version: $(python --version 2>&1)"
echo -e "   Pytest Version: $(pytest --version | head -1)"
echo -e "   Working Directory: $(pwd)"
echo ""

echo -e "${YELLOW}🔍 Dependency Check:${NC}"
echo -e "   ✅ pytest-xdist: $(python -c 'import xdist; print(xdist.__version__)' 2>/dev/null || echo 'Not found')"
echo -e "   ✅ pytest-cov: $(python -c 'import pytest_cov; print(pytest_cov.__version__)' 2>/dev/null || echo 'Not found')"
echo -e "   ✅ pytest-html: $(python -c 'import pytest_html; print(pytest_html.__version__)' 2>/dev/null || echo 'Not found')"
echo -e "   ✅ bc calculator: $(which bc >/dev/null && echo 'Available' || echo 'Not found')"
echo ""

echo -e "${YELLOW}📊 Performance Scaling Analysis:${NC}"
echo -e "${YELLOW}Testing with different worker counts to demonstrate scaling...${NC}"
echo ""

# Test with different worker counts to show scaling
declare -A timing_results
for workers in 1 4 8 16 32; do
    echo -e "${BLUE}🔄 Testing with $workers workers...${NC}"

    start_time=$(date +%s%3N)

    # Run with more verbose output for demonstration
    if [ "$workers" -eq 32 ]; then
        echo -e "${CYAN}   Running comprehensive test with maximum parallelism...${NC}"
        pytest tests/unit/test_basic.py -n $workers --tb=short -v --maxfail=3 2>&1 | head -20
    else
        pytest tests/unit/test_basic.py -n $workers --tb=no -q 2>/dev/null || true
    fi

    end_time=$(date +%s%3N)
    duration=$((end_time - start_time))

    if command -v bc >/dev/null 2>&1; then
        duration_sec=$(echo "scale=2; $duration/1000" | bc)
    else
        duration_sec=$(echo "$duration / 1000" | awk '{printf "%.2f", $1}')
    fi

    timing_results[$workers]=$duration_sec
    echo -e "   ${GREEN}✅ $workers workers: ${duration_sec}s${NC}"

    # Calculate efficiency
    if [ "$workers" -gt 1 ]; then
        baseline=${timing_results[1]}
        if command -v bc >/dev/null 2>&1; then
            efficiency=$(echo "scale=1; ($baseline / $duration_sec) * 100 / $workers" | bc)
            speedup=$(echo "scale=2; $baseline / $duration_sec" | bc)
        else
            efficiency=$(echo "$baseline $duration_sec $workers" | awk '{printf "%.1f", ($1 / $2) * 100 / $3}')
            speedup=$(echo "$baseline $duration_sec" | awk '{printf "%.2f", $1 / $2}')
        fi
        echo -e "   ${CYAN}   Speedup: ${speedup}x, Efficiency: ${efficiency}%${NC}"
    fi
    echo ""
done

echo -e "${YELLOW}================================${NC}"
echo -e "${YELLOW}🔥 Comprehensive Parallel Test Suite (32 Workers)${NC}"
echo -e "${YELLOW}================================${NC}"
echo ""

# Run the comprehensive parallel test suite like tp-test but with enhanced output
echo -e "${BLUE}📋 Test Suite Overview:${NC}"
echo -e "   • Unit Tests: $(find tests/unit -name '*.py' | wc -l) files"
echo -e "   • Integration Tests: $(find tests/integration -name '*.py' 2>/dev/null | wc -l) files"
echo -e "   • Total Test Files: $(find tests -name 'test_*.py' | wc -l) files"
echo -e "   • Max Parallel Jobs: ${MAX_PARALLEL_JOBS}"
echo -e "   • Test Timeout: 600s"
echo ""

echo -e "${BLUE}🚀 Starting comprehensive parallel test execution...${NC}"
echo -e "${CYAN}Running all test suites in parallel with real-time monitoring...${NC}"
echo ""

start_time=$(date +%s)
start_time_ms=$(date +%s%3N)

# Use the same comprehensive test approach as tp-test but with enhanced output
echo -e "${BLUE}Launching parallel test suites:${NC}"
echo -e "   • ${GREEN}unit_basic${NC} - Core functionality tests"
echo -e "   • ${GREEN}unit_api${NC} - API layer tests"
echo -e "   • ${GREEN}unit_file_injection${NC} - File injection service tests"
echo -e "   • ${GREEN}vm_metrics${NC} - VM monitoring tests"
echo -e "   • ${GREEN}unit_api_routes_real${NC} - Real API route tests"
echo -e "   • ${GREEN}api_endpoints${NC} - Endpoint integration tests"
echo -e "   • ${GREEN}api_real_integration${NC} - Real service integration"
echo -e "   • ${GREEN}edge_cases_performance${NC} - Performance edge cases"
echo -e "   • ${GREEN}e2e_tests${NC} - End-to-end workflow tests"
echo -e "   • ${GREEN}websocket_integration${NC} - WebSocket functionality"
echo -e "   • ${GREEN}real_services${NC} - Real service connectivity"
echo -e "   • ${GREEN}traefik_routing${NC} - Traefik routing tests"
echo -e "   • ${GREEN}integration_tests_light${NC} - Light integration tests"
echo -e "   • ${GREEN}behave_tests${NC} - BDD scenario tests"
echo -e "   • ${GREEN}security_tests${NC} - Security validation"
echo -e "   • ${GREEN}grpc_connectivity${NC} - gRPC communication tests"
echo -e "   • ${GREEN}performance_benchmarks${NC} - Performance benchmarks"
echo -e "   • ${GREEN}ecs_logging${NC} - ECS logging integration"
echo ""

# Run the comprehensive parallel test suite (similar to tp-test logic)
./scripts/run-parallel-tests.sh 2>&1 | tee "$LOG_DIR/parallel-32-output-${TIMESTAMP}.log"

end_time=$(date +%s)
end_time_ms=$(date +%s%3N)
total_time=$((end_time - start_time))
total_time_ms=$((end_time_ms - start_time_ms))

if command -v bc >/dev/null 2>&1; then
    total_time_sec=$(echo "scale=2; $total_time_ms/1000" | bc)
else
    total_time_sec=$(echo "$total_time_ms" | awk '{printf "%.2f", $1/1000}')
fi

echo ""
echo -e "${YELLOW}================================${NC}"
echo -e "${YELLOW}📊 Comprehensive Test Suite Results${NC}"
echo -e "${YELLOW}================================${NC}"
echo -e "${GREEN}🎉 32-worker comprehensive parallel test suite completed!${NC}"
echo -e "${BLUE}⏱️  Total execution time: ${total_time_sec}s${NC}"
echo -e "${BLUE}📁 Results saved to: $LOG_DIR/${NC}"
echo -e "${BLUE}📝 Full Log: parallel-32-output-${TIMESTAMP}.log${NC}"
echo ""

echo -e "${CYAN}📈 Test Suite Performance Analysis:${NC}"
echo -e "   • ${GREEN}Parallel Test Suites${NC}: 18 different test categories"
echo -e "   • ${GREEN}Maximum Concurrency${NC}: Up to 32 parallel jobs"
echo -e "   • ${GREEN}Load Balancing${NC}: Intelligent test distribution"
echo -e "   • ${GREEN}Real-time Monitoring${NC}: Live progress tracking"
echo ""

echo -e "${PURPLE}💩🎉TurdParty🎉💩 32-Core Parallel Testing Power!${NC}"
echo ""

echo -e "${BLUE}🎯 Key Achievements:${NC}"
echo -e "   • ${GREEN}32x parallelism${NC} - Maximum CPU utilization"
echo -e "   • ${GREEN}Intelligent load balancing${NC} - Optimal test distribution"
echo -e "   • ${GREEN}Real-time monitoring${NC} - Live progress tracking"
echo -e "   • ${GREEN}Comprehensive reporting${NC} - Detailed HTML reports"
echo -e "   • ${GREEN}Nix environment${NC} - Perfect dependency management"
echo ""

echo -e "${YELLOW}⚡ Performance Metrics:${NC}"
for workers in 1 4 8 16 32; do
    if [ -n "${timing_results[$workers]}" ]; then
        echo -e "   • ${workers} workers: ${timing_results[$workers]}s"
    fi
done
echo ""

echo -e "${CYAN}💡 Pro Tips for Maximum Performance:${NC}"
echo -e "   • ${YELLOW}pytest -n auto${NC} - Automatic worker detection"
echo -e "   • ${YELLOW}pytest -n 32${NC} - Maximum 32-worker parallelism"
echo -e "   • ${YELLOW}tp-test${NC} - Full TurdParty test suite"
echo -e "   • ${YELLOW}htop${NC} - Monitor all 32 cores in action"
echo -e "   • ${YELLOW}watch -n 1 'ps aux | grep pytest'${NC} - Watch worker processes"
echo ""

echo -e "${GREEN}✅ 32-core parallel testing demonstration complete!${NC}"
echo -e "${PURPLE}================================${NC}"
