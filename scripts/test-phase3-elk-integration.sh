#!/bin/bash

# Test Phase 3 ELK Integration - Complete End-to-End Validation
# This script validates the complete Phase 3 ELK integration implementation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
API_BASE_URL="http://localhost:8000"
KIBANA_URL="http://kibana.turdparty.localhost"
ELASTICSEARCH_URL="http://elasticsearch.turdparty.localhost"
FLOWER_URL="http://flower.turdparty.localhost"

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Test 1: Verify ELK Stack is running
test_elk_stack() {
    print_header "Testing ELK Stack Services"
    
    local services=(
        "elasticsearch:$ELASTICSEARCH_URL/_cluster/health"
        "kibana:$KIBANA_URL/api/status"
        "logstash:http://logstash:8080"
    )
    
    for service_info in "${services[@]}"; do
        local service_name=$(echo "$service_info" | cut -d':' -f1)
        local service_url=$(echo "$service_info" | cut -d':' -f2-)
        
        print_info "Testing $service_name..."
        
        local response=$(curl -s -o /dev/null -w "%{http_code}" "$service_url" 2>/dev/null || echo "000")
        
        if [[ "$response" == "200" ]]; then
            print_success "$service_name is healthy"
        else
            print_error "$service_name is not responding (HTTP $response)"
            return 1
        fi
    done
    
    print_success "ELK stack is running and healthy"
}

# Test 2: Verify ELK worker is running
test_elk_worker() {
    print_header "Testing ELK Integration Worker"
    
    if docker ps --format "table {{.Names}}" | grep -q "turdpartycollab_worker_elk"; then
        print_success "ELK worker container is running"
        
        # Test worker health
        local worker_health=$(docker exec turdpartycollab_worker_elk celery -A celery_app inspect ping 2>/dev/null || echo "failed")
        
        if echo "$worker_health" | grep -q "pong"; then
            print_success "ELK worker is responding to health checks"
        else
            print_warning "ELK worker may not be healthy"
        fi
    else
        print_error "ELK worker container is not running"
        return 1
    fi
}

# Test 3: Test ELK health check task
test_elk_health_task() {
    print_header "Testing ELK Health Check Task"
    
    print_info "Triggering ELK health check task..."
    
    local task_result=$(docker exec turdpartycollab_worker_elk celery -A celery_app call tasks.elk_integration.check_elk_health 2>/dev/null || echo "failed")
    
    if [[ "$task_result" != "failed" ]]; then
        print_success "ELK health check task executed successfully"
    else
        print_warning "ELK health check task may have failed"
    fi
}

# Test 4: Test workflow event streaming
test_workflow_streaming() {
    print_header "Testing Workflow Event Streaming"
    
    # Create a test file
    local test_file="/tmp/test_elk_integration.txt"
    echo "This is a test file for ELK integration validation" > "$test_file"
    
    print_info "Uploading test file for workflow streaming test..."
    
    # Upload file via API
    local upload_response=$(curl -s -X POST \
        -F "file=@$test_file" \
        -F "filename=test_elk_integration.txt" \
        "$API_BASE_URL/api/v1/files/upload" 2>/dev/null || echo "failed")
    
    if [[ "$upload_response" != "failed" ]] && echo "$upload_response" | grep -q "uuid"; then
        print_success "File uploaded successfully"
        
        # Extract UUID from response
        local file_uuid=$(echo "$upload_response" | grep -o '"uuid":"[^"]*"' | cut -d'"' -f4)
        print_info "File UUID: $file_uuid"
        
        # Start workflow
        print_info "Starting workflow with ELK streaming..."
        local workflow_response=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -d "{\"file_id\":\"$file_uuid\",\"vm_config\":{\"template\":\"ubuntu:20.04\"}}" \
            "$API_BASE_URL/api/v1/workflow/start" 2>/dev/null || echo "failed")
        
        if [[ "$workflow_response" != "failed" ]] && echo "$workflow_response" | grep -q "workflow_job_id"; then
            print_success "Workflow started with ELK streaming enabled"
            local workflow_id=$(echo "$workflow_response" | grep -o '"workflow_job_id":"[^"]*"' | cut -d'"' -f4)
            print_info "Workflow ID: $workflow_id"
            
            # Wait a bit for events to be processed
            print_info "Waiting for workflow events to be streamed to ELK..."
            sleep 30
            
            # Check if events appear in Elasticsearch
            print_info "Checking for workflow events in Elasticsearch..."
            local es_query="{\"query\":{\"match\":{\"turdparty.workflow_id\":\"$workflow_id\"}}}"
            local es_response=$(curl -s -X POST \
                "$ELASTICSEARCH_URL/turdparty-workflow-events-*/_search" \
                -H "Content-Type: application/json" \
                -d "$es_query" 2>/dev/null || echo "failed")
            
            if echo "$es_response" | grep -q "\"total\":{\"value\":[1-9]"; then
                print_success "Workflow events found in Elasticsearch"
            else
                print_warning "Workflow events may not have reached Elasticsearch yet"
            fi
        else
            print_warning "Workflow start may have failed"
        fi
    else
        print_warning "File upload may have failed"
    fi
    
    # Cleanup
    rm -f "$test_file"
}

# Test 5: Test VM monitoring agent injection
test_vm_agent_injection() {
    print_header "Testing VM Monitoring Agent Injection"
    
    print_info "Checking if VM agent injection is working..."
    
    # Check if agent files exist
    local agent_files=(
        "services/monitoring/vm-agent/agent.py"
        "services/monitoring/vm-agent/requirements.txt"
        "services/monitoring/vm-agent/Dockerfile"
        "services/monitoring/vm-agent/config/agent.yml"
    )
    
    for file in "${agent_files[@]}"; do
        if [ -f "$PROJECT_ROOT/$file" ]; then
            print_success "Agent file exists: $file"
        else
            print_error "Agent file missing: $file"
            return 1
        fi
    done
    
    # Check if injection task is available
    local injection_task=$(docker exec turdpartycollab_worker_injection celery -A celery_app inspect registered 2>/dev/null | grep "vm_agent_injector" || echo "not_found")
    
    if [[ "$injection_task" != "not_found" ]]; then
        print_success "VM agent injection task is registered"
    else
        print_warning "VM agent injection task may not be registered"
    fi
}

# Test 6: Test Kibana dashboards
test_kibana_dashboards() {
    print_header "Testing Kibana Dashboards"
    
    print_info "Checking Kibana dashboard availability..."
    
    # Import dashboards
    print_info "Importing TurdParty dashboards..."
    if [ -f "$PROJECT_ROOT/services/monitoring/elk/kibana/import-dashboards.sh" ]; then
        local import_result=$("$PROJECT_ROOT/services/monitoring/elk/kibana/import-dashboards.sh" 2>/dev/null || echo "failed")
        
        if echo "$import_result" | grep -q "successfully"; then
            print_success "Dashboards imported successfully"
        else
            print_warning "Dashboard import may have failed"
        fi
    else
        print_warning "Dashboard import script not found"
    fi
    
    # Check if dashboards are accessible
    local dashboard_response=$(curl -s -o /dev/null -w "%{http_code}" "$KIBANA_URL/app/dashboards" 2>/dev/null || echo "000")
    
    if [[ "$dashboard_response" == "200" ]]; then
        print_success "Kibana dashboards are accessible"
    else
        print_warning "Kibana dashboards may not be accessible (HTTP $dashboard_response)"
    fi
}

# Test 7: Test data flow end-to-end
test_data_flow() {
    print_header "Testing End-to-End Data Flow"
    
    print_info "Testing complete data flow: Workflow → ELK → Kibana"
    
    # Check Elasticsearch indices
    print_info "Checking Elasticsearch indices..."
    local indices_response=$(curl -s "$ELASTICSEARCH_URL/_cat/indices/turdparty-*?v" 2>/dev/null || echo "failed")
    
    if [[ "$indices_response" != "failed" ]] && echo "$indices_response" | grep -q "turdparty"; then
        print_success "TurdParty indices found in Elasticsearch"
        
        # Count documents in indices
        local doc_count=$(echo "$indices_response" | grep "turdparty" | awk '{sum += $7} END {print sum}')
        print_info "Total documents in TurdParty indices: ${doc_count:-0}"
    else
        print_warning "TurdParty indices may not exist in Elasticsearch"
    fi
    
    # Test Logstash pipeline
    print_info "Testing Logstash pipeline..."
    local test_event="{\"@timestamp\":\"$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)\",\"event\":{\"action\":\"test_event\"},\"turdparty\":{\"phase\":\"testing\"},\"message\":\"ELK integration test\"}"
    
    local logstash_response=$(curl -s -X POST \
        "http://logstash:8080" \
        -H "Content-Type: application/json" \
        -d "$test_event" 2>/dev/null || echo "failed")
    
    if [[ "$logstash_response" != "failed" ]]; then
        print_success "Logstash pipeline is accepting data"
    else
        print_warning "Logstash pipeline may not be working"
    fi
}

# Test 8: Performance validation
test_performance() {
    print_header "Testing Performance and Resource Usage"
    
    print_info "Checking ELK worker resource usage..."
    
    # Check worker memory usage
    local worker_stats=$(docker stats turdpartycollab_worker_elk --no-stream --format "table {{.CPUPerc}}\t{{.MemUsage}}" 2>/dev/null || echo "failed")
    
    if [[ "$worker_stats" != "failed" ]]; then
        print_success "ELK worker resource usage: $worker_stats"
    else
        print_warning "Could not get ELK worker resource usage"
    fi
    
    # Check Elasticsearch cluster health
    local es_health=$(curl -s "$ELASTICSEARCH_URL/_cluster/health" 2>/dev/null || echo "failed")
    
    if echo "$es_health" | grep -q '"status":"green"'; then
        print_success "Elasticsearch cluster health: GREEN"
    elif echo "$es_health" | grep -q '"status":"yellow"'; then
        print_warning "Elasticsearch cluster health: YELLOW"
    else
        print_warning "Elasticsearch cluster health may be RED or unknown"
    fi
}

# Main test execution
main() {
    print_header "Phase 3 ELK Integration Validation"
    print_info "Testing complete ELK integration and VM monitoring capabilities"
    echo
    
    local failed_tests=0
    
    # Run all tests
    test_elk_stack || ((failed_tests++))
    echo
    
    test_elk_worker || ((failed_tests++))
    echo
    
    test_elk_health_task || ((failed_tests++))
    echo
    
    test_workflow_streaming || ((failed_tests++))
    echo
    
    test_vm_agent_injection || ((failed_tests++))
    echo
    
    test_kibana_dashboards || ((failed_tests++))
    echo
    
    test_data_flow || ((failed_tests++))
    echo
    
    test_performance || ((failed_tests++))
    echo
    
    # Summary
    print_header "Test Summary"
    
    if [[ $failed_tests -eq 0 ]]; then
        print_success "All tests passed! Phase 3 ELK integration is working correctly."
        print_info "🎉 PHASE 3 COMPLETE! 🎉"
        echo
        print_info "TurdParty now has complete end-to-end malware analysis capabilities:"
        echo "  • File upload and processing"
        echo "  • VM pool management"
        echo "  • Real-time VM monitoring"
        echo "  • ELK data pipeline"
        echo "  • Kibana visualization dashboards"
        echo "  • Threat detection and analysis"
    else
        print_warning "$failed_tests test(s) had issues. Please review the output above."
    fi
    
    echo
    print_info "Phase 3 Features Validated:"
    echo "  • ELK Stack Integration"
    echo "  • Workflow Event Streaming"
    echo "  • VM Monitoring Agent"
    echo "  • Kibana Dashboards"
    echo "  • End-to-End Data Pipeline"
    echo "  • Performance Monitoring"
    
    return $failed_tests
}

# Run main function
main "$@"
