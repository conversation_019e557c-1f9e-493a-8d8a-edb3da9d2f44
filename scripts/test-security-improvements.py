#!/usr/bin/env python3
"""
Quick security test validation script.
Tests the improved security test suite for TurdParty.
"""

import subprocess
import sys
import time
from pathlib import Path


def run_command(cmd: list, timeout: int = 30) -> tuple[int, str, str]:
    """Run a command with timeout."""
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd=Path(__file__).parent.parent
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"
    except Exception as e:
        return -1, "", str(e)


def test_security_syntax():
    """Test that security test files have valid syntax."""
    print("🔍 Testing security test file syntax...")
    
    security_files = [
        "tests/security/test_api_security.py",
        "tests/security/test_container_security.py",
        "tests/security/test_security_validation.py"
    ]
    
    for file_path in security_files:
        print(f"  Checking {file_path}...")
        returncode, stdout, stderr = run_command(["python", "-m", "py_compile", file_path])
        
        if returncode == 0:
            print(f"  ✅ {file_path} - Syntax OK")
        else:
            print(f"  ❌ {file_path} - Syntax Error: {stderr}")
            return False
    
    return True


def test_security_imports():
    """Test that security tests can import required modules."""
    print("\n📦 Testing security test imports...")
    
    test_import = """
import sys
sys.path.append('.')
try:
    from tests.security.test_api_security import TestInputValidation
    from tests.security.test_container_security import TestContainerIsolation
    print("✅ All security test imports successful")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
"""
    
    returncode, stdout, stderr = run_command(["python", "-c", test_import])
    
    if returncode == 0:
        print("  ✅ All imports successful")
        return True
    else:
        print(f"  ❌ Import failed: {stderr}")
        return False


def test_security_collection():
    """Test that pytest can collect security tests."""
    print("\n🔍 Testing security test collection...")
    
    returncode, stdout, stderr = run_command([
        "python", "-m", "pytest", 
        "tests/security/", 
        "--collect-only", 
        "-q"
    ], timeout=15)
    
    if returncode == 0:
        # Count collected tests
        lines = stdout.split('\n')
        test_count = len([line for line in lines if '::' in line])
        print(f"  ✅ Successfully collected {test_count} security tests")
        return True
    else:
        print(f"  ❌ Test collection failed: {stderr}")
        return False


def run_quick_security_tests():
    """Run a subset of security tests quickly."""
    print("\n🚀 Running quick security tests...")
    
    # Run just a few fast tests
    quick_tests = [
        "tests/security/test_api_security.py::TestAuthenticationSecurity::test_cors_security_headers",
        "tests/security/test_api_security.py::TestDataSecurity::test_sensitive_data_exposure",
        "tests/security/test_api_security.py::TestVulnerabilityScanning::test_directory_listing_prevention",
        "tests/security/test_container_security.py::TestContainerIsolation::test_environment_variable_security",
    ]
    
    for test in quick_tests:
        print(f"  Running {test.split('::')[-1]}...")
        returncode, stdout, stderr = run_command([
            "python", "-m", "pytest", 
            test, 
            "-v", 
            "--tb=short"
        ], timeout=10)
        
        if returncode == 0:
            print(f"    ✅ PASSED")
        else:
            print(f"    ⚠️  Test result: {returncode} (may be expected)")
    
    return True


def main():
    """Main test runner."""
    print("🛡️  TurdParty Security Test Validation")
    print("=" * 50)
    
    start_time = time.time()
    
    # Run validation steps
    steps = [
        ("Syntax Validation", test_security_syntax),
        ("Import Testing", test_security_imports),
        ("Test Collection", test_security_collection),
        ("Quick Test Run", run_quick_security_tests),
    ]
    
    passed = 0
    total = len(steps)
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}")
        print("-" * 30)
        
        try:
            if step_func():
                passed += 1
                print(f"✅ {step_name} - PASSED")
            else:
                print(f"❌ {step_name} - FAILED")
        except Exception as e:
            print(f"❌ {step_name} - ERROR: {e}")
    
    # Summary
    duration = time.time() - start_time
    print("\n" + "=" * 50)
    print(f"🏁 Security Test Validation Complete")
    print(f"📊 Results: {passed}/{total} steps passed")
    print(f"⏱️  Duration: {duration:.1f}s")
    
    if passed == total:
        print("🎉 All security test validations passed!")
        return 0
    else:
        print("⚠️  Some validations failed - check output above")
        return 1


if __name__ == "__main__":
    sys.exit(main())
