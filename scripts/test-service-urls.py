#!/usr/bin/env python3
"""
TurdParty Service URL Manager Test Suite

Comprehensive testing suite for the TurdParty Service URL Manager and Traefik
routing configuration. Validates service URL generation, endpoint accessibility,
and proper routing configuration across different deployment environments.

Key Features:
    - Service URL Manager functionality validation
    - Traefik routing configuration verification
    - Service availability checking with health status
    - API endpoint generation and validation
    - Environment-specific configuration testing
    - Comprehensive routing and accessibility reporting

Test Categories:
    1. **Service URL Manager**: URL generation and configuration validation
    2. **Service Availability**: Live service accessibility checking
    3. **API Endpoints**: REST API endpoint generation and validation
    4. **Traefik Routing**: Reverse proxy configuration verification
    5. **Health Check URLs**: Service health endpoint validation

Service Categories Tested:
    - **Core Services**: API, frontend, database, storage
    - **ELK Stack**: Elasticsearch, Logstash, Kibana, Filebeat
    - **Monitoring**: Status dashboard, Celery Flower, documentation
    - **Infrastructure**: Redis, MinIO, VM management services

Validation Checks:
    - URL format and structure validation
    - Service accessibility via HTTP requests
    - Traefik service registration verification
    - Health check endpoint availability
    - Environment-specific configuration accuracy

Usage:
    python scripts/test-service-urls.py

    Tests all configured services in the development environment
    and validates Traefik routing configuration.

Output:
    - Service URL listings with accessibility status
    - Health check endpoint validation
    - API endpoint generation verification
    - Traefik routing service registration status
    - Comprehensive test results summary

Integration:
    - Uses ServiceURLManager for dynamic URL generation
    - Integrates with Traefik API for routing validation
    - Supports multiple deployment environments
    - Compatible with Docker Compose service discovery
"""

import sys
import os
import requests
from pathlib import Path

# Add utils to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'utils'))

from service_urls import ServiceURLManager

def test_service_url_manager():
    """
    Test the ServiceURLManager functionality.

    Validates URL generation, environment configuration, API endpoint
    creation, and service availability checking with comprehensive
    reporting of all service URLs and their accessibility status.

    Returns:
        bool: True if all URL manager tests pass, False otherwise
    """
    print("🔧 Testing TurdParty Service URL Manager")
    print("=" * 60)
    
    try:
        # Test development environment (our current setup)
        manager = ServiceURLManager('development')
        
        print(f"📍 Environment: {manager.environment}")
        info = manager.get_environment_info()
        print(f"   Domain: {info['domain']}")
        print(f"   Protocol: {info['protocol']}")
        print(f"   Services: {len(info['services'])}")
        print()
        
        # Test all service URLs
        print("🌐 Service URLs:")
        print("-" * 40)
        
        all_urls = manager.get_all_service_urls()
        for service_name, url in all_urls.items():
            service_config = manager.env_config['services'][service_name]
            if not service_config.get('internal_only', False):  # Only show web-accessible services
                print(f"   {service_name:15} -> {url}")
        
        print()
        
        # Test health check URLs
        print("🏥 Health Check URLs:")
        print("-" * 40)
        
        health_urls = manager.health_check_urls()
        for service_name, url in health_urls.items():
            if url and not manager.env_config['services'][service_name].get('internal_only', False):
                print(f"   {service_name:15} -> {url}")
        
        print()
        
        # Test API endpoints
        print("🚀 API Endpoints:")
        print("-" * 40)
        
        try:
            upload_url = manager.get_api_endpoint('files', 'upload')
            health_url = manager.get_api_endpoint('health', 'system')
            vm_create_url = manager.get_api_endpoint('vms', 'create')
            
            print(f"   File Upload     -> {upload_url}")
            print(f"   Health Check    -> {health_url}")
            print(f"   VM Create       -> {vm_create_url}")
        except Exception as e:
            print(f"   ❌ Error testing API endpoints: {e}")
        
        print()
        
        # Test service availability (if services are running)
        print("🔍 Service Availability Check:")
        print("-" * 40)
        
        web_services = ['api', 'frontend', 'status', 'docs', 'elasticsearch', 'kibana', 'storage', 'celery_flower']
        
        for service in web_services:
            try:
                url = manager.get_service_url(service)
                response = requests.get(url, timeout=5)
                status = "✅ UP" if response.status_code < 400 else f"⚠️  {response.status_code}"
                print(f"   {service:15} -> {status}")
            except requests.exceptions.RequestException as e:
                print(f"   {service:15} -> ❌ DOWN ({type(e).__name__})")
            except Exception as e:
                print(f"   {service:15} -> ❓ ERROR ({e})")
        
        print()
        print("✅ ServiceURLManager test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing ServiceURLManager: {e}")
        return False

def validate_traefik_routing():
    """
    Validate that Traefik is properly routing to our services.

    Checks Traefik API accessibility and validates that all expected
    services are properly registered with the reverse proxy for
    correct routing and load balancing.

    Returns:
        bool: True if Traefik routing is properly configured, False otherwise
    """
    print("\n🔀 Traefik Routing Validation")
    print("=" * 60)
    
    try:
        # Check Traefik API
        traefik_api = "http://localhost:8080/api/http/services"
        response = requests.get(traefik_api, timeout=5)
        
        if response.status_code == 200:
            services = response.json()
            print(f"✅ Traefik API accessible - {len(services)} services registered")

            # List registered services
            print("\n📋 Registered Services:")
            print("-" * 30)
            if isinstance(services, dict):
                for service_name, service_info in services.items():
                    if '@docker' in service_name:
                        clean_name = service_name.replace('@docker', '')
                        status = "UP" if service_info.get('serverStatus', {}) else "UNKNOWN"
                        print(f"   {clean_name:20} -> {status}")
            else:
                # Handle case where services is a list
                for service in services:
                    if isinstance(service, str) and '@docker' in service:
                        clean_name = service.replace('@docker', '')
                        print(f"   {clean_name:20} -> REGISTERED")

            return True
        else:
            print(f"❌ Traefik API not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error validating Traefik routing: {e}")
        return False

def main():
    """
    Main test function.

    Orchestrates the complete service URL and routing test suite,
    including ServiceURLManager validation and Traefik routing
    verification with comprehensive result reporting.

    Returns:
        int: Exit code (0 for success, 1 for failure)
    """
    print("💩🎉TurdParty🎉💩 Service URL & Routing Test")
    print("=" * 70)
    print()
    
    # Test ServiceURLManager
    url_test_passed = test_service_url_manager()
    
    # Test Traefik routing
    traefik_test_passed = validate_traefik_routing()
    
    print("\n" + "=" * 70)
    print("📊 Test Results Summary:")
    print(f"   ServiceURLManager: {'✅ PASSED' if url_test_passed else '❌ FAILED'}")
    print(f"   Traefik Routing:   {'✅ PASSED' if traefik_test_passed else '❌ FAILED'}")
    
    if url_test_passed and traefik_test_passed:
        print("\n🎉 All tests passed! Service URL management is working correctly.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
