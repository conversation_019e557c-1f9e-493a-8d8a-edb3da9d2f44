#!/bin/bash

# TurdParty Status System Robustness Test
# Tests the enhanced status monitoring with mixed service states

set -e

# ANSI color codes
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print banner
print_banner() {
    echo ""
    print_colored $CYAN "╔══════════════════════════════════════════════════════════════╗"
    print_colored $CYAN "║              🧪 TurdParty Status Robustness Test             ║"
    print_colored $CYAN "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to test service endpoint
test_endpoint() {
    local name="$1"
    local url="$2"
    local expected_status="$3"
    
    print_colored $BLUE "🔬 Testing: $name"
    print_colored $BLUE "   URL: $url"
    
    local status_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
    local result=""
    
    if [ "$status_code" = "200" ]; then
        result="🟢 Operational"
    elif [ "$status_code" = "000" ]; then
        result="🔴 Outage (Connection Failed)"
    elif [ "$status_code" -ge 500 ]; then
        result="🔴 Outage (Server Error)"
    elif [ "$status_code" -ge 400 ]; then
        result="🟡 Degraded (Client Error)"
    else
        result="🟡 Degraded (Unexpected Response)"
    fi
    
    print_colored $YELLOW "   Status Code: $status_code"
    print_colored $YELLOW "   Result: $result"
    echo ""
}

# Function to test status page API
test_status_page() {
    print_colored $PURPLE "📊 Testing Status Page Functionality:"
    echo ""
    
    # Test main page
    test_endpoint "Status Dashboard Main Page" "http://localhost:8090/" "operational"
    
    # Test health endpoint
    test_endpoint "Status Dashboard Health" "http://localhost:8090/health" "operational"
    
    # Test if JavaScript is loading
    local js_check=$(curl -s http://localhost:8090/ | grep -c "status.js" || echo "0")
    if [ "$js_check" -gt 0 ]; then
        print_colored $GREEN "   ✅ JavaScript assets loading correctly"
    else
        print_colored $RED "   ❌ JavaScript assets not found"
    fi
    echo ""
}

# Function to test real service endpoints
test_real_services() {
    print_colored $PURPLE "🔍 Testing Real Service Endpoints:"
    echo ""
    
    # Test Elasticsearch
    test_endpoint "Elasticsearch Cluster Health" "http://localhost:9200/_cluster/health" "operational"
    
    # Test MinIO
    test_endpoint "MinIO Health Check" "http://localhost:9000/minio/health/live" "operational"
    
    # Test Status Dashboard
    test_endpoint "Status Dashboard" "http://localhost:8090/health" "operational"
    
    # Test non-existent services (should show outage)
    test_endpoint "API Service (Expected Down)" "http://localhost:8000/health" "outage"
    test_endpoint "Kibana (Expected Down)" "http://localhost:5601/api/status" "outage"
}

# Function to simulate mixed status scenario
simulate_mixed_status() {
    print_colored $PURPLE "🎭 Simulating Mixed Status Scenario:"
    echo ""
    
    print_colored $YELLOW "This demonstrates how the status page handles:"
    print_colored $YELLOW "• 🟢 Operational services (Elasticsearch, MinIO, Status Dashboard)"
    print_colored $YELLOW "• 🔴 Down services (API, Kibana, Logstash)"
    print_colored $YELLOW "• 🟡 Degraded services (simulated in JavaScript)"
    echo ""
    
    print_colored $CYAN "The status page JavaScript will:"
    print_colored $CYAN "• Attempt real health checks first"
    print_colored $CYAN "• Fall back to simulation for unreachable services"
    print_colored $CYAN "• Update Mermaid diagram with real-time status lights"
    print_colored $CYAN "• Track service history and detect flapping"
    print_colored $CYAN "• Calculate uptime metrics and dependency impacts"
    echo ""
}

# Function to show status page features
show_status_features() {
    print_colored $PURPLE "🚀 Enhanced Status Page Features:"
    echo ""
    
    print_colored $GREEN "✅ Real Health Checks:"
    print_colored $GREEN "   • Elasticsearch cluster health API"
    print_colored $GREEN "   • MinIO health endpoint"
    print_colored $GREEN "   • Status dashboard self-monitoring"
    print_colored $GREEN "   • Timeout handling (5 seconds)"
    print_colored $GREEN "   • CORS support for cross-origin requests"
    echo ""
    
    print_colored $GREEN "✅ Robust Error Handling:"
    print_colored $GREEN "   • Retry logic with exponential backoff"
    print_colored $GREEN "   • Graceful fallback to simulation"
    print_colored $GREEN "   • Service dependency tracking"
    print_colored $GREEN "   • Flapping detection"
    print_colored $GREEN "   • Recovery detection"
    echo ""
    
    print_colored $GREEN "✅ Advanced Monitoring:"
    print_colored $GREEN "   • Service uptime calculation"
    print_colored $GREEN "   • Mean time between failures (MTBF)"
    print_colored $GREEN "   • Current status streaks"
    print_colored $GREEN "   • Critical service prioritization"
    print_colored $GREEN "   • Weighted overall status calculation"
    echo ""
    
    print_colored $GREEN "✅ Visual Enhancements:"
    print_colored $GREEN "   • Dynamic Mermaid diagram updates"
    print_colored $GREEN "   • Animated status lights (🟢🟡🔴)"
    print_colored $GREEN "   • Service metrics display"
    print_colored $GREEN "   • Real-time activity feed"
    print_colored $GREEN "   • Professional CSS animations"
    echo ""
}

# Function to show access information
show_access_info() {
    print_colored $PURPLE "🌐 Access Information:"
    echo ""
    
    print_colored $CYAN "Status Dashboard: http://localhost:8090"
    print_colored $CYAN "• Interactive architecture diagram with live status lights"
    print_colored $CYAN "• Real-time service monitoring grid"
    print_colored $CYAN "• Performance metrics and uptime statistics"
    print_colored $CYAN "• Activity feed with service events"
    echo ""
    
    print_colored $CYAN "Working Services:"
    print_colored $CYAN "• Elasticsearch: http://localhost:9200/_cluster/health"
    print_colored $CYAN "• MinIO: http://localhost:9000/minio/health/live"
    print_colored $CYAN "• Status Dashboard: http://localhost:8090/health"
    echo ""
    
    print_colored $CYAN "Expected Down Services (for mixed status demo):"
    print_colored $CYAN "• API Service: http://localhost:8000/health"
    print_colored $CYAN "• Kibana: http://localhost:5601/api/status"
    print_colored $CYAN "• Logstash: Various ports (5044, 5001, 8081)"
    echo ""
}

# Main test function
main() {
    print_banner
    
    # Test status page functionality
    test_status_page
    
    # Test real services
    test_real_services
    
    # Simulate mixed status
    simulate_mixed_status
    
    # Show features
    show_status_features
    
    # Show access info
    show_access_info
    
    print_colored $GREEN "🎉 Status System Robustness Test Completed!"
    print_colored $GREEN "Visit http://localhost:8090 to see the enhanced monitoring in action."
    echo ""
}

# Run main function
main "$@"
