#!/bin/bash
# Test Storage Layer with Real MinIO Containers
# This script runs storage layer tests using real MinIO containers instead of mocks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
REPORTS_DIR="$PROJECT_ROOT/reports/storage-layer"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

print_header() {
    echo -e "${BLUE}================================================================================================${NC}"
    echo -e "${BLUE}🗄️  TurdParty Storage Layer Real Testing${NC}"
    echo -e "${BLUE}================================================================================================${NC}"
    echo -e "${YELLOW}Running storage layer tests with real MinIO containers...${NC}"
    echo ""
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_prerequisites() {
    print_info "Checking prerequisites for storage layer testing..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    
    # Check pytest
    if ! command -v pytest &> /dev/null; then
        print_error "pytest is not installed or not in PATH"
        exit 1
    fi
    
    # Check if we can pull MinIO image
    print_info "Pulling MinIO test image..."
    if ! docker pull minio/minio:latest &> /dev/null; then
        print_warning "Failed to pull minio/minio:latest image, tests may fail"
    fi
    
    print_success "Prerequisites check passed"
}

setup_test_environment() {
    print_info "Setting up test environment..."
    
    # Create reports directory
    mkdir -p "$REPORTS_DIR"
    
    # Clean up any existing test containers
    print_info "Cleaning up existing test containers..."
    docker ps -a --filter "name=testcontainers" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    docker ps -a --filter "label=org.testcontainers=true" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    
    print_success "Test environment setup completed"
}

run_minio_operations_tests() {
    print_info "Running MinIO operations tests with real containers..."
    
    pytest tests/unit/test_storage_layer_real.py::TestRealMinIOOperations \
        --verbose \
        --tb=short \
        --cov=api.services \
        --cov-report=html:"$REPORTS_DIR/coverage-minio-ops" \
        --cov-report=term-missing \
        --junitxml="$REPORTS_DIR/junit-minio-ops.xml" \
        --html="$REPORTS_DIR/minio-ops-tests.html" \
        --self-contained-html \
        -m "not slow" \
        "$@"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_success "MinIO operations tests passed"
    else
        print_error "MinIO operations tests failed with exit code $exit_code"
        return $exit_code
    fi
}

run_storage_service_tests() {
    print_info "Running Storage Service tests with real MinIO..."
    
    pytest tests/unit/test_storage_layer_real.py::TestRealStorageService \
        --verbose \
        --tb=short \
        --cov=api.services \
        --cov-report=html:"$REPORTS_DIR/coverage-storage-service" \
        --cov-report=term-missing \
        --junitxml="$REPORTS_DIR/junit-storage-service.xml" \
        --html="$REPORTS_DIR/storage-service-tests.html" \
        --self-contained-html \
        -m "not slow" \
        "$@"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_success "Storage Service tests passed"
    else
        print_error "Storage Service tests failed with exit code $exit_code"
        return $exit_code
    fi
}

run_file_upload_routes_tests() {
    print_info "Running File Upload Routes tests with real MinIO..."
    
    pytest tests/unit/test_file_upload_routes_real.py::TestRealFileUploadRoutes \
        --verbose \
        --tb=short \
        --cov=api.v1.routes \
        --cov-report=html:"$REPORTS_DIR/coverage-upload-routes" \
        --cov-report=term-missing \
        --junitxml="$REPORTS_DIR/junit-upload-routes.xml" \
        --html="$REPORTS_DIR/upload-routes-tests.html" \
        --self-contained-html \
        -m "not slow" \
        "$@"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_success "File Upload Routes tests passed"
    else
        print_error "File Upload Routes tests failed with exit code $exit_code"
        return $exit_code
    fi
}

run_performance_tests() {
    print_info "Running Storage Layer performance tests..."
    
    pytest tests/unit/test_file_upload_routes_real.py::TestRealFileUploadPerformance \
        --verbose \
        --tb=short \
        --junitxml="$REPORTS_DIR/junit-storage-performance.xml" \
        --html="$REPORTS_DIR/storage-performance-tests.html" \
        --self-contained-html \
        --benchmark-only \
        --benchmark-json="$REPORTS_DIR/storage-benchmark-results.json" \
        "$@"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_success "Storage Performance tests passed"
    else
        print_warning "Storage Performance tests had issues (exit code $exit_code)"
        # Don't fail the entire suite for performance tests
    fi
}

run_integration_tests() {
    print_info "Running existing MinIO integration tests..."
    
    # Run the existing integration tests if they exist
    if [ -f "tests/integration/test_minio_integration.py" ]; then
        pytest tests/integration/test_minio_integration.py \
            --verbose \
            --tb=short \
            --junitxml="$REPORTS_DIR/junit-minio-integration.xml" \
            --html="$REPORTS_DIR/minio-integration-tests.html" \
            --self-contained-html \
            "$@"
        
        local exit_code=$?
        if [ $exit_code -eq 0 ]; then
            print_success "MinIO integration tests passed"
        else
            print_warning "MinIO integration tests had issues (exit code $exit_code)"
        fi
    else
        print_info "No existing MinIO integration tests found, skipping"
    fi
}

cleanup_test_environment() {
    print_info "Cleaning up test environment..."
    
    # Remove test containers
    docker ps -a --filter "name=testcontainers" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    docker ps -a --filter "label=org.testcontainers=true" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    
    # Remove dangling images
    docker image prune -f &> /dev/null || true
    
    print_success "Test environment cleanup completed"
}

generate_report() {
    print_info "Generating test report..."
    
    local report_file="$REPORTS_DIR/storage-layer-test-report-$TIMESTAMP.md"
    
    cat > "$report_file" << EOF
# Storage Layer Real Testing Report

Generated: $(date)

## Test Summary

This report covers storage layer testing using real MinIO containers instead of mocks.

## Test Categories

### MinIO Operations Tests
- **File**: tests/unit/test_storage_layer_real.py::TestRealMinIOOperations
- **Coverage**: File upload, download, existence checks, deletion, listing, large files, presigned URLs

### Storage Service Tests  
- **File**: tests/unit/test_storage_layer_real.py::TestRealStorageService
- **Coverage**: Service-level operations, concurrent operations, error handling

### File Upload Routes Tests
- **File**: tests/unit/test_file_upload_routes_real.py::TestRealFileUploadRoutes
- **Coverage**: API endpoints, file validation, upload/download cycles, multiple files

### Performance Tests
- **File**: tests/unit/test_file_upload_routes_real.py::TestRealFileUploadPerformance
- **Coverage**: Upload performance, concurrent uploads, large file handling

## Test Results

EOF

    # Add test results if available
    if [ -f "$REPORTS_DIR/junit-minio-ops.xml" ]; then
        echo "### MinIO Operations Test Results" >> "$report_file"
        echo "\`\`\`" >> "$report_file"
        grep -E "(tests|failures|errors)" "$REPORTS_DIR/junit-minio-ops.xml" | head -5 >> "$report_file" || true
        echo "\`\`\`" >> "$report_file"
        echo "" >> "$report_file"
    fi
    
    if [ -f "$REPORTS_DIR/junit-storage-service.xml" ]; then
        echo "### Storage Service Test Results" >> "$report_file"
        echo "\`\`\`" >> "$report_file"
        grep -E "(tests|failures|errors)" "$REPORTS_DIR/junit-storage-service.xml" | head -5 >> "$report_file" || true
        echo "\`\`\`" >> "$report_file"
        echo "" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

## Coverage Reports

- MinIO Operations Coverage: reports/storage-layer/coverage-minio-ops/index.html
- Storage Service Coverage: reports/storage-layer/coverage-storage-service/index.html  
- Upload Routes Coverage: reports/storage-layer/coverage-upload-routes/index.html

## Next Steps

1. Review any test failures and fix issues
2. Update mock-based tests to use real MinIO
3. Add more edge case testing
4. Optimize performance for larger file uploads

EOF

    print_success "Test report generated: $report_file"
}

main() {
    print_header
    
    # Parse command line arguments
    RUN_PERFORMANCE=false
    RUN_INTEGRATION=false
    SKIP_CLEANUP=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --performance)
                RUN_PERFORMANCE=true
                shift
                ;;
            --integration)
                RUN_INTEGRATION=true
                shift
                ;;
            --skip-cleanup)
                SKIP_CLEANUP=true
                shift
                ;;
            --help|-h)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --performance     Run performance tests"
                echo "  --integration     Run integration tests"
                echo "  --skip-cleanup    Skip cleanup after tests"
                echo "  --help, -h        Show this help message"
                exit 0
                ;;
            *)
                # Pass unknown arguments to pytest
                break
                ;;
        esac
    done
    
    # Run test suite
    local overall_exit_code=0
    
    check_prerequisites
    setup_test_environment
    
    # Run test suites
    run_minio_operations_tests "$@" || overall_exit_code=$?
    run_storage_service_tests "$@" || overall_exit_code=$?
    run_file_upload_routes_tests "$@" || overall_exit_code=$?
    
    if [ "$RUN_PERFORMANCE" = true ]; then
        run_performance_tests "$@"
    fi
    
    if [ "$RUN_INTEGRATION" = true ]; then
        run_integration_tests "$@"
    fi
    
    # Cleanup unless skipped
    if [ "$SKIP_CLEANUP" != true ]; then
        cleanup_test_environment
    fi
    
    generate_report
    
    if [ $overall_exit_code -eq 0 ]; then
        print_success "All storage layer tests completed successfully!"
        echo ""
        echo -e "${GREEN}🎉 Storage Layer migration to real MinIO is working!${NC}"
    else
        print_error "Some storage layer tests failed"
        echo ""
        echo -e "${RED}💥 Check the reports for details: $REPORTS_DIR${NC}"
        exit $overall_exit_code
    fi
}

# Handle script interruption
trap 'echo -e "\n${YELLOW}Interrupted. Cleaning up...${NC}"; cleanup_test_environment; exit 1' INT TERM

main "$@"
