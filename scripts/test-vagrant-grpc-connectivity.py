#!/usr/bin/env python3
"""
Test script to verify Vagrant gRPC connectivity from containers
"""
import asyncio
import socket
import time
import logging
from typing import Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_port_connectivity(host: str, port: int, timeout: float = 5.0) -> bool:
    """Test if a port is reachable"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        logger.error(f"Error testing connectivity to {host}:{port}: {e}")
        return False


def scan_vagrant_ports(host: str = "localhost") -> dict:
    """Scan common Vagrant/gRPC ports"""
    ports_to_check = [
        40000,  # Our configured gRPC port
        50051,  # Default gRPC port
        22,     # SSH (for fallback)
        2222,   # Common Vagrant SSH port
        3389,   # RDP (Windows VMs)
        5985,   # WinRM HTTP
        5986,   # WinRM HTTPS
    ]
    
    results = {}
    for port in ports_to_check:
        logger.info(f"Testing connectivity to {host}:{port}")
        is_open = test_port_connectivity(host, port, timeout=2.0)
        results[port] = is_open
        logger.info(f"  {host}:{port} - {'✅ OPEN' if is_open else '❌ CLOSED'}")
    
    return results


def test_grpc_connection(host: str, port: int) -> bool:
    """Test gRPC connection specifically"""
    try:
        import grpc
        
        # Create a channel
        channel = grpc.insecure_channel(f"{host}:{port}")
        
        # Test connection with a timeout
        try:
            grpc.channel_ready_future(channel).result(timeout=5.0)
            logger.info(f"✅ gRPC channel to {host}:{port} is ready")
            channel.close()
            return True
        except grpc.FutureTimeoutError:
            logger.warning(f"⏰ gRPC channel to {host}:{port} timed out")
            channel.close()
            return False
        except Exception as e:
            logger.error(f"❌ gRPC channel to {host}:{port} failed: {e}")
            channel.close()
            return False
            
    except ImportError:
        logger.warning("gRPC not available, skipping gRPC-specific test")
        return False


def test_from_container_perspective():
    """Test connectivity from container perspective"""
    logger.info("🐳 Testing from container perspective...")
    
    # Test various host resolution methods
    hosts_to_test = [
        "localhost",
        "127.0.0.1",
        "host.docker.internal",
        "**********",  # Common Docker gateway
        "**********",  # Default Docker bridge gateway
    ]
    
    for host in hosts_to_test:
        logger.info(f"\n📡 Testing host: {host}")
        try:
            # Test basic connectivity
            results = scan_vagrant_ports(host)
            
            # Test gRPC specifically on port 40000 if it's open
            if results.get(40000, False):
                logger.info(f"🔌 Testing gRPC connection to {host}:40000")
                grpc_result = test_grpc_connection(host, 40000)
                if grpc_result:
                    logger.info(f"✅ gRPC connection successful to {host}:40000")
                    return True
                    
        except Exception as e:
            logger.error(f"Error testing {host}: {e}")
    
    return False


def test_vagrant_vm_detection():
    """Test if we can detect running Vagrant VMs"""
    logger.info("🔍 Testing Vagrant VM detection...")
    
    try:
        import subprocess
        
        # Try to run vagrant status
        result = subprocess.run(
            ["vagrant", "status"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            logger.info("✅ Vagrant is accessible")
            logger.info(f"Vagrant status output:\n{result.stdout}")
            
            # Check for running VMs
            if "running" in result.stdout.lower():
                logger.info("🟢 Found running Vagrant VMs")
                return True
            else:
                logger.info("🟡 Vagrant accessible but no running VMs found")
                return False
        else:
            logger.warning(f"⚠️ Vagrant command failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("⏰ Vagrant command timed out")
        return False
    except FileNotFoundError:
        logger.warning("❌ Vagrant command not found")
        return False
    except Exception as e:
        logger.error(f"❌ Error running vagrant: {e}")
        return False


def main():
    """Main test function"""
    logger.info("🚀 Starting Vagrant gRPC connectivity tests...")
    logger.info("=" * 60)
    
    # Test 1: Basic port scanning
    logger.info("📊 Test 1: Basic port connectivity")
    host_results = scan_vagrant_ports("localhost")
    
    # Test 2: Container perspective
    logger.info("\n📊 Test 2: Container network perspective")
    container_success = test_from_container_perspective()
    
    # Test 3: Vagrant VM detection
    logger.info("\n📊 Test 3: Vagrant VM detection")
    vagrant_success = test_vagrant_vm_detection()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📋 CONNECTIVITY TEST SUMMARY")
    logger.info("=" * 60)
    
    logger.info(f"🌐 Host port 40000: {'✅ OPEN' if host_results.get(40000, False) else '❌ CLOSED'}")
    logger.info(f"🐳 Container access: {'✅ SUCCESS' if container_success else '❌ FAILED'}")
    logger.info(f"📦 Vagrant VMs: {'✅ DETECTED' if vagrant_success else '❌ NOT FOUND'}")
    
    # Recommendations
    logger.info("\n💡 RECOMMENDATIONS:")
    
    if not host_results.get(40000, False):
        logger.info("❗ Port 40000 is not accessible. Check if Vagrant gRPC plugin is running.")
        logger.info("   Try: vagrant plugin install vagrant-grpc")
        logger.info("   Or start a VM with gRPC enabled")
    
    if not container_success:
        logger.info("❗ Containers cannot access host port 40000.")
        logger.info("   Consider adding port mapping: '40000:40000' to docker-compose.yml")
        logger.info("   Or use host networking mode for VM management containers")
    
    if not vagrant_success:
        logger.info("❗ No running Vagrant VMs detected.")
        logger.info("   Start a Vagrant VM: vagrant up")
        logger.info("   Ensure VM has gRPC communication enabled")
    
    # Overall status
    overall_success = host_results.get(40000, False) and (container_success or vagrant_success)
    logger.info(f"\n🎯 OVERALL STATUS: {'✅ READY FOR GRPC' if overall_success else '❌ NEEDS CONFIGURATION'}")
    
    return overall_success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
