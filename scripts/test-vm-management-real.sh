#!/bin/bash
# Test VM Management with Real Docker Containers
# This script runs VM management tests using real Docker containers instead of mocks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
REPORTS_DIR="$PROJECT_ROOT/reports/vm-management"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

print_header() {
    echo -e "${BLUE}================================================================================================${NC}"
    echo -e "${BLUE}🐳 TurdParty VM Management Real Testing${NC}"
    echo -e "${BLUE}================================================================================================${NC}"
    echo -e "${YELLOW}Running VM management tests with real Docker containers...${NC}"
    echo ""
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_prerequisites() {
    print_info "Checking prerequisites for VM management testing..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    
    # Check pytest
    if ! command -v pytest &> /dev/null; then
        print_error "pytest is not installed or not in PATH"
        exit 1
    fi
    
    # Check if we can pull Ubuntu image
    print_info "Pulling Ubuntu test image..."
    if ! docker pull ubuntu:20.04 &> /dev/null; then
        print_warning "Failed to pull ubuntu:20.04 image, tests may fail"
    fi
    
    print_success "Prerequisites check passed"
}

setup_test_environment() {
    print_info "Setting up test environment..."
    
    # Create reports directory
    mkdir -p "$REPORTS_DIR"
    
    # Clean up any existing test containers
    print_info "Cleaning up existing test containers..."
    docker ps -a --filter "label=turdparty.test=true" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    docker ps -a --filter "name=turdparty_test-" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    
    # Create test network if it doesn't exist
    if ! docker network ls | grep -q "turdpartycollab_net"; then
        print_info "Creating test network..."
        docker network create turdpartycollab_net 2>/dev/null || true
    fi
    
    print_success "Test environment setup completed"
}

run_vm_service_tests() {
    print_info "Running VM Service tests with real Docker containers..."
    
    pytest tests/unit/test_vm_management_real.py \
        --verbose \
        --tb=short \
        --cov=api.services.vm_service \
        --cov=api.services.vm_metrics_service \
        --cov-report=html:"$REPORTS_DIR/coverage-vm-service" \
        --cov-report=term-missing \
        --junitxml="$REPORTS_DIR/junit-vm-service.xml" \
        --html="$REPORTS_DIR/vm-service-tests.html" \
        --self-contained-html \
        -m "not slow" \
        "$@"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_success "VM Service tests passed"
    else
        print_error "VM Service tests failed with exit code $exit_code"
        return $exit_code
    fi
}

run_vm_routes_tests() {
    print_info "Running VM Routes tests with real Docker containers..."
    
    pytest tests/unit/test_vm_management_routes_real.py \
        --verbose \
        --tb=short \
        --cov=api.v1.routes.vm_management \
        --cov-report=html:"$REPORTS_DIR/coverage-vm-routes" \
        --cov-report=term-missing \
        --junitxml="$REPORTS_DIR/junit-vm-routes.xml" \
        --html="$REPORTS_DIR/vm-routes-tests.html" \
        --self-contained-html \
        -m "not slow" \
        "$@"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_success "VM Routes tests passed"
    else
        print_error "VM Routes tests failed with exit code $exit_code"
        return $exit_code
    fi
}

run_vm_metrics_tests() {
    print_info "Running VM Metrics tests with real Docker containers..."
    
    pytest tests/unit/test_vm_metrics_service.py::TestRealVMMetricsService \
        --verbose \
        --tb=short \
        --cov=api.services.vm_metrics_service \
        --cov-report=html:"$REPORTS_DIR/coverage-vm-metrics" \
        --cov-report=term-missing \
        --junitxml="$REPORTS_DIR/junit-vm-metrics.xml" \
        --html="$REPORTS_DIR/vm-metrics-tests.html" \
        --self-contained-html \
        -m "not slow" \
        "$@"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_success "VM Metrics tests passed"
    else
        print_error "VM Metrics tests failed with exit code $exit_code"
        return $exit_code
    fi
}

run_performance_tests() {
    print_info "Running VM Management performance tests..."
    
    pytest tests/unit/test_vm_management_routes_real.py::TestRealVMManagementPerformance \
        --verbose \
        --tb=short \
        --junitxml="$REPORTS_DIR/junit-vm-performance.xml" \
        --html="$REPORTS_DIR/vm-performance-tests.html" \
        --self-contained-html \
        --benchmark-only \
        --benchmark-json="$REPORTS_DIR/benchmark-results.json" \
        "$@"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        print_success "VM Performance tests passed"
    else
        print_warning "VM Performance tests had issues (exit code $exit_code)"
        # Don't fail the entire suite for performance tests
    fi
}

cleanup_test_environment() {
    print_info "Cleaning up test environment..."
    
    # Remove test containers
    docker ps -a --filter "label=turdparty.test=true" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    docker ps -a --filter "name=turdparty_test-" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    docker ps -a --filter "name=test-vm-" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    docker ps -a --filter "name=lifecycle-test-" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    docker ps -a --filter "name=metrics-test-" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    
    # Remove dangling images
    docker image prune -f &> /dev/null || true
    
    print_success "Test environment cleanup completed"
}

generate_report() {
    print_info "Generating test report..."
    
    local report_file="$REPORTS_DIR/vm-management-test-report-$TIMESTAMP.md"
    
    cat > "$report_file" << EOF
# VM Management Real Testing Report

Generated: $(date)

## Test Summary

This report covers VM management testing using real Docker containers instead of mocks.

## Test Categories

### VM Service Tests
- **File**: tests/unit/test_vm_management_real.py
- **Coverage**: VM lifecycle, resource limits, network connectivity, command execution

### VM Routes Tests  
- **File**: tests/unit/test_vm_management_routes_real.py
- **Coverage**: API endpoints, WebSocket connections, error handling

### VM Metrics Tests
- **File**: tests/unit/test_vm_metrics_service.py (Real tests only)
- **Coverage**: Real metrics collection, streaming, process monitoring

## Test Results

EOF

    # Add test results if available
    if [ -f "$REPORTS_DIR/junit-vm-service.xml" ]; then
        echo "### VM Service Test Results" >> "$report_file"
        echo "\`\`\`" >> "$report_file"
        grep -E "(tests|failures|errors)" "$REPORTS_DIR/junit-vm-service.xml" | head -5 >> "$report_file" || true
        echo "\`\`\`" >> "$report_file"
        echo "" >> "$report_file"
    fi
    
    if [ -f "$REPORTS_DIR/junit-vm-routes.xml" ]; then
        echo "### VM Routes Test Results" >> "$report_file"
        echo "\`\`\`" >> "$report_file"
        grep -E "(tests|failures|errors)" "$REPORTS_DIR/junit-vm-routes.xml" | head -5 >> "$report_file" || true
        echo "\`\`\`" >> "$report_file"
        echo "" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

## Coverage Reports

- VM Service Coverage: reports/vm-management/coverage-vm-service/index.html
- VM Routes Coverage: reports/vm-management/coverage-vm-routes/index.html  
- VM Metrics Coverage: reports/vm-management/coverage-vm-metrics/index.html

## Next Steps

1. Review any test failures and fix issues
2. Update mock-based tests to use real containers
3. Add more edge case testing
4. Optimize performance for larger container counts

EOF

    print_success "Test report generated: $report_file"
}

main() {
    print_header
    
    # Parse command line arguments
    RUN_PERFORMANCE=false
    SKIP_CLEANUP=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --performance)
                RUN_PERFORMANCE=true
                shift
                ;;
            --skip-cleanup)
                SKIP_CLEANUP=true
                shift
                ;;
            --help|-h)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --performance     Run performance tests"
                echo "  --skip-cleanup    Skip cleanup after tests"
                echo "  --help, -h        Show this help message"
                exit 0
                ;;
            *)
                # Pass unknown arguments to pytest
                break
                ;;
        esac
    done
    
    # Run test suite
    local overall_exit_code=0
    
    check_prerequisites
    setup_test_environment
    
    # Run test suites
    run_vm_service_tests "$@" || overall_exit_code=$?
    run_vm_routes_tests "$@" || overall_exit_code=$?
    run_vm_metrics_tests "$@" || overall_exit_code=$?
    
    if [ "$RUN_PERFORMANCE" = true ]; then
        run_performance_tests "$@"
    fi
    
    # Cleanup unless skipped
    if [ "$SKIP_CLEANUP" != true ]; then
        cleanup_test_environment
    fi
    
    generate_report
    
    if [ $overall_exit_code -eq 0 ]; then
        print_success "All VM management tests completed successfully!"
        echo ""
        echo -e "${GREEN}🎉 VM Management migration to real containers is working!${NC}"
    else
        print_error "Some VM management tests failed"
        echo ""
        echo -e "${RED}💥 Check the reports for details: $REPORTS_DIR${NC}"
        exit $overall_exit_code
    fi
}

# Handle script interruption
trap 'echo -e "\n${YELLOW}Interrupted. Cleaning up...${NC}"; cleanup_test_environment; exit 1' INT TERM

main "$@"
