#!/usr/bin/env python3
"""
Simple TurdParty Workflow Test
Tests key components: ECS Data → Mock Reports → Sphinx Generation
"""

import json
import time
import uuid
import hashlib
from datetime import datetime, timedelta
import subprocess
import requests

def test_infrastructure():
    """Test that key services are available."""
    print("🔧 Testing Infrastructure...")
    
    services = {
        "API": "http://api.turdparty.localhost/health",
        "Elasticsearch": "http://elasticsearch.turdparty.localhost/_cluster/health"
    }
    
    results = {}
    for service, url in services.items():
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"   ✅ {service}: Available")
                results[service] = True
            else:
                print(f"   ⚠️ {service}: Status {response.status_code}")
                results[service] = False
        except Exception as e:
            print(f"   ❌ {service}: Failed - {e}")
            results[service] = False
    
    return results

def test_ecs_data():
    """Test existing ECS data."""
    print("\n📊 Testing ECS Data...")
    
    # Test Notepad++ data
    notepadpp_uuid = "d5cc1f03-3041-46f5-8ad9-7af2b270ddbb"
    
    query = {
        "query": {
            "term": {"file_uuid.keyword": notepadpp_uuid}
        },
        "size": 5
    }
    
    try:
        response = requests.post(
            "http://elasticsearch.turdparty.localhost/turdparty-*/_search",
            json=query,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            total_events = result.get("hits", {}).get("total", {}).get("value", 0)
            hits = result.get("hits", {}).get("hits", [])
            
            print(f"   ✅ Notepad++ ECS Data: {total_events} events found")
            
            if hits:
                sample_event = hits[0]["_source"]
                print(f"   📋 Sample Event: {sample_event.get('event', {}).get('action', 'unknown')}")
            
            return {"notepadpp": {"total_events": total_events, "available": True}}
        else:
            print(f"   ❌ ECS query failed: {response.status_code}")
            return {"notepadpp": {"available": False}}
            
    except Exception as e:
        print(f"   ❌ ECS test failed: {e}")
        return {"notepadpp": {"available": False}}

def generate_git_ecs_data():
    """Generate mock ECS data for Git installer."""
    print("\n🔄 Generating Git ECS Data...")
    
    git_uuid = "a1b2c3d4-e5f6-7890-1234-************"
    base_time = datetime.utcnow()
    vm_id = str(uuid.uuid4())
    events = []
    
    # Generate Git-specific events
    git_files = [
        "C:\\Program Files\\Git\\bin\\git.exe",
        "C:\\Program Files\\Git\\bin\\bash.exe", 
        "C:\\Program Files\\Git\\bin\\sh.exe",
        "C:\\Program Files\\Git\\libexec\\git-core\\git-add.exe",
        "C:\\Program Files\\Git\\libexec\\git-core\\git-commit.exe",
        "C:\\Program Files\\Git\\etc\\gitconfig",
        "C:\\Program Files\\Git\\etc\\bash.bashrc",
        "C:\\Users\\<USER>\\Desktop\\Git Bash.lnk",
        "C:\\Users\\<USER>\\Desktop\\Git CMD.lnk"
    ]
    
    git_registry_keys = [
        "HKEY_LOCAL_MACHINE\\SOFTWARE\\GitForWindows",
        "HKEY_LOCAL_MACHINE\\SOFTWARE\\GitForWindows\\InstallPath",
        "HKEY_LOCAL_MACHINE\\SOFTWARE\\GitForWindows\\Version",
        "HKEY_CURRENT_USER\\SOFTWARE\\Classes\\Directory\\shell\\git_shell",
        "HKEY_CURRENT_USER\\SOFTWARE\\Classes\\Directory\\Background\\shell\\git_shell",
        "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Environment\\Path"
    ]
    
    # File creation events
    for i, file_path in enumerate(git_files):
        event = {
            "@timestamp": (base_time + timedelta(seconds=i * 3)).isoformat() + "Z",
            "ecs": {"version": "8.11.0"},
            "event": {
                "kind": "event",
                "category": ["file"],
                "type": ["creation"],
                "action": "file_created",
                "outcome": "success"
            },
            "service": {
                "name": "turdparty-vm-agent",
                "type": "monitoring"
            },
            "file": {
                "path": file_path,
                "size": 1024 * (i + 10),
                "type": "file"
            },
            "file_uuid": git_uuid,
            "vm_id": vm_id,
            "host": {
                "name": f"turdparty-vm-{vm_id[:8]}",
                "id": vm_id
            },
            "tags": ["installation", "file-creation", "git"]
        }
        events.append(event)
    
    # Registry events
    for i, reg_key in enumerate(git_registry_keys):
        event = {
            "@timestamp": (base_time + timedelta(seconds=(len(git_files) + i) * 3)).isoformat() + "Z",
            "ecs": {"version": "8.11.0"},
            "event": {
                "kind": "event",
                "category": ["configuration"],
                "type": ["change"],
                "action": "registry_key_created",
                "outcome": "success"
            },
            "service": {
                "name": "turdparty-vm-agent",
                "type": "monitoring"
            },
            "registry": {
                "key": reg_key,
                "value": f"git_value_{i}"
            },
            "file_uuid": git_uuid,
            "vm_id": vm_id,
            "host": {
                "name": f"turdparty-vm-{vm_id[:8]}",
                "id": vm_id
            },
            "tags": ["installation", "registry-change", "git"]
        }
        events.append(event)
    
    # Process events
    git_processes = ["Git-2.42.0-64-bit.exe", "msiexec.exe", "git.exe"]
    for i, process_name in enumerate(git_processes):
        event = {
            "@timestamp": (base_time + timedelta(seconds=(len(git_files) + len(git_registry_keys) + i) * 3)).isoformat() + "Z",
            "ecs": {"version": "8.11.0"},
            "event": {
                "kind": "event",
                "category": ["process"],
                "type": ["start"],
                "action": "process_start",
                "outcome": "success"
            },
            "service": {
                "name": "turdparty-vm-agent",
                "type": "monitoring"
            },
            "process": {
                "name": process_name,
                "pid": 2000 + i,
                "command_line": f"{process_name} /SILENT"
            },
            "file_uuid": git_uuid,
            "vm_id": vm_id,
            "host": {
                "name": f"turdparty-vm-{vm_id[:8]}",
                "id": vm_id
            },
            "tags": ["installation", "process-execution", "git"]
        }
        events.append(event)
    
    # Send events to Elasticsearch
    sent_count = 0
    for event in events:
        try:
            index = f"turdparty-install-ecs-{datetime.now().strftime('%Y.%m.%d')}"
            response = requests.post(
                f"http://elasticsearch.turdparty.localhost/{index}/_doc",
                json=event,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code in [200, 201]:
                sent_count += 1
                
        except Exception as e:
            print(f"   ⚠️ Failed to send event: {e}")
    
    print(f"   ✅ Git ECS Data: {sent_count}/{len(events)} events sent")
    return {"git_uuid": git_uuid, "events_sent": sent_count, "total_events": len(events)}

def test_sphinx_platform():
    """Test Sphinx reports platform."""
    print("\n📚 Testing Sphinx Platform...")
    
    try:
        response = requests.get("http://localhost:8081", timeout=10)
        if response.status_code == 200:
            print("   ✅ Sphinx Platform: Available")
            
            # Check if Notepad++ report exists
            notepadpp_response = requests.get("http://localhost:8081/reports/notepadpp-analysis.html", timeout=10)
            if notepadpp_response.status_code == 200:
                print("   ✅ Notepad++ Report: Available")
            else:
                print("   ⚠️ Notepad++ Report: Not found")
            
            return {"platform_available": True, "notepadpp_report": notepadpp_response.status_code == 200}
        else:
            print(f"   ⚠️ Sphinx Platform: Status {response.status_code}")
            return {"platform_available": False}
            
    except Exception as e:
        print(f"   ❌ Sphinx Platform: Failed - {e}")
        return {"platform_available": False}

def generate_workflow_summary(results):
    """Generate workflow test summary."""
    print(f"\n{'='*60}")
    print("📊 TURDPARTY WORKFLOW TEST SUMMARY")
    print(f"{'='*60}")
    
    # Infrastructure Status
    print(f"\n🔧 INFRASTRUCTURE:")
    infra = results.get("infrastructure", {})
    for service, status in infra.items():
        status_icon = "✅" if status else "❌"
        print(f"   {status_icon} {service}")
    
    # ECS Data Status
    print(f"\n📊 ECS DATA:")
    ecs = results.get("ecs_data", {})
    for binary, data in ecs.items():
        if data.get("available"):
            events = data.get("total_events", 0)
            print(f"   ✅ {binary}: {events} events")
        else:
            print(f"   ❌ {binary}: No data")
    
    # Git Data Generation
    git_gen = results.get("git_generation", {})
    if git_gen:
        events_sent = git_gen.get("events_sent", 0)
        total_events = git_gen.get("total_events", 0)
        print(f"   ✅ Git (Generated): {events_sent}/{total_events} events")
    
    # Sphinx Platform
    print(f"\n📚 SPHINX PLATFORM:")
    sphinx = results.get("sphinx", {})
    platform_status = "✅" if sphinx.get("platform_available") else "❌"
    print(f"   {platform_status} Platform Available")
    
    notepadpp_status = "✅" if sphinx.get("notepadpp_report") else "❌"
    print(f"   {notepadpp_status} Notepad++ Report")
    
    # Access URLs
    print(f"\n🌐 ACCESS URLS:")
    print(f"   📊 Kibana: http://kibana.turdparty.localhost/app/discover")
    print(f"   🔍 Elasticsearch: http://elasticsearch.turdparty.localhost/_cat/indices")
    print(f"   📋 API: http://api.turdparty.localhost/health")
    print(f"   📚 Sphinx Reports: http://localhost:8081")
    
    # Test Queries
    print(f"\n🔍 TEST QUERIES:")
    print(f"   # Search Notepad++ events")
    print(f"   curl \"http://elasticsearch.turdparty.localhost/turdparty-*/_search\" \\")
    print(f"     -H \"Content-Type: application/json\" \\")
    print(f"     -d '{{\"query\": {{\"term\": {{\"file_uuid.keyword\": \"d5cc1f03-3041-46f5-8ad9-7af2b270ddbb\"}}}}}}'")
    
    if git_gen:
        print(f"\n   # Search Git events")
        print(f"   curl \"http://elasticsearch.turdparty.localhost/turdparty-*/_search\" \\")
        print(f"     -H \"Content-Type: application/json\" \\")
        print(f"     -d '{{\"query\": {{\"term\": {{\"file_uuid.keyword\": \"{git_gen.get('git_uuid', '')}\"}}}}}}'")
    
    # Next Steps
    print(f"\n🎯 WORKFLOW STATUS:")
    
    all_infra_ok = all(infra.values())
    ecs_data_ok = any(data.get("available", False) for data in ecs.values())
    sphinx_ok = sphinx.get("platform_available", False)
    
    if all_infra_ok and ecs_data_ok and sphinx_ok:
        print(f"   🎉 WORKFLOW READY: All components operational!")
        print(f"   ✅ Infrastructure: Healthy")
        print(f"   ✅ ECS Data: Available")
        print(f"   ✅ Sphinx Platform: Running")
        print(f"\n   🚀 Ready for real binary uploads and VM execution!")
    else:
        print(f"   ⚠️ WORKFLOW ISSUES: Some components need attention")
        if not all_infra_ok:
            print(f"   ❌ Infrastructure: Check failed services")
        if not ecs_data_ok:
            print(f"   ❌ ECS Data: No data available")
        if not sphinx_ok:
            print(f"   ❌ Sphinx Platform: Not running")

def main():
    """Run the simplified workflow test."""
    print("🚀 TurdParty Simplified Workflow Test")
    print("=" * 50)
    
    start_time = time.time()
    results = {}
    
    # Test infrastructure
    results["infrastructure"] = test_infrastructure()
    
    # Test existing ECS data
    results["ecs_data"] = test_ecs_data()
    
    # Generate new Git ECS data
    results["git_generation"] = generate_git_ecs_data()
    
    # Wait for indexing
    print("\n⏳ Waiting for Elasticsearch indexing...")
    time.sleep(10)
    
    # Test Sphinx platform
    results["sphinx"] = test_sphinx_platform()
    
    # Generate summary
    generate_workflow_summary(results)
    
    total_time = time.time() - start_time
    print(f"\n⏱️ Total Test Time: {total_time:.1f} seconds")
    
    # Save results
    results_file = f"/tmp/turdparty-workflow-test-{int(time.time())}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"📄 Results saved: {results_file}")

if __name__ == "__main__":
    main()
