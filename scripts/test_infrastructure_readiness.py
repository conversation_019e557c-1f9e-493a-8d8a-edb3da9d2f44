#!/usr/bin/env python3
"""
Infrastructure Readiness Test

Tests all infrastructure prerequisites before running VM management tests:
- Vagrant service availability
- Docker daemon status
- Database connectivity
- Redis connectivity
- Elasticsearch availability
- MinIO availability
- Traefik routing

Usage:
    python scripts/test_infrastructure_readiness.py
    python scripts/test_infrastructure_readiness.py --verbose
    python scripts/test_infrastructure_readiness.py --service vagrant
"""

import argparse
import json
import logging
import subprocess
import sys
import time
import requests
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class InfrastructureReadinessTester:
    """Test infrastructure readiness for VM management."""
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.test_results = {
            "start_time": datetime.now().isoformat(),
            "tests": {},
            "summary": {}
        }
        
        # Initialize ServiceURLManager for Traefik URLs
        self.service_urls = {}
        self._load_service_urls()
    
    def _load_service_urls(self):
        """Load service URLs from central configuration."""
        try:
            from utils.service_urls import ServiceURLManager
            
            manager = ServiceURLManager('local')
            
            # Load all service URLs
            services = ['api', 'frontend', 'elasticsearch', 'kibana', 'minio', 'redis']
            for service in services:
                try:
                    self.service_urls[service] = manager.get_service_url(service)
                except Exception as e:
                    logger.warning(f"Could not get URL for {service}: {e}")
                    # Fallback URLs
                    fallback_urls = {
                        'api': 'http://api.turdparty.localhost',
                        'frontend': 'http://frontend.turdparty.localhost',
                        'elasticsearch': 'http://elasticsearch.turdparty.localhost:9200',
                        'kibana': 'http://kibana.turdparty.localhost:5601',
                        'minio': 'http://minio.turdparty.localhost:9000',
                        'redis': 'redis://redis.turdparty.localhost:6379'
                    }
                    self.service_urls[service] = fallback_urls.get(service, f'http://{service}.turdparty.localhost')
            
            logger.info(f"Loaded service URLs: {list(self.service_urls.keys())}")
            
        except ImportError:
            logger.warning("ServiceURLManager not available, using fallback URLs")
            self.service_urls = {
                'api': 'http://api.turdparty.localhost',
                'frontend': 'http://frontend.turdparty.localhost',
                'elasticsearch': 'http://elasticsearch.turdparty.localhost:9200',
                'kibana': 'http://kibana.turdparty.localhost:5601',
                'minio': 'http://minio.turdparty.localhost:9000',
                'redis': 'redis://redis.turdparty.localhost:6379'
            }
    
    def test_vagrant_service(self) -> Dict[str, Any]:
        """Test Vagrant service availability."""
        logger.info("Testing Vagrant service availability...")
        
        try:
            # Check if vagrant command is available
            result = subprocess.run(
                ['vagrant', '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0:
                return {
                    "success": False,
                    "error": "Vagrant command not available",
                    "suggestion": "Install Vagrant or ensure it's in PATH"
                }
            
            vagrant_version = result.stdout.strip()
            
            # Check if any Vagrant VMs are running
            result = subprocess.run(
                ['vagrant', 'global-status'],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode != 0:
                return {
                    "success": False,
                    "error": f"Vagrant global-status failed: {result.stderr}",
                    "vagrant_version": vagrant_version
                }
            
            # Parse vagrant status
            lines = result.stdout.split('\n')
            running_vms = []
            
            for line in lines:
                if 'running' in line.lower():
                    parts = line.split()
                    if len(parts) >= 4:
                        vm_id = parts[0]
                        vm_name = parts[1]
                        vm_provider = parts[2]
                        vm_state = parts[3]
                        running_vms.append({
                            "id": vm_id,
                            "name": vm_name,
                            "provider": vm_provider,
                            "state": vm_state
                        })
            
            # Check for TurdParty-specific VMs
            turdparty_vms = [vm for vm in running_vms if 'turdparty' in vm['name'].lower()]
            
            return {
                "success": True,
                "vagrant_version": vagrant_version,
                "total_running_vms": len(running_vms),
                "turdparty_vms": len(turdparty_vms),
                "running_vms": running_vms,
                "vagrant_available": True,
                "message": f"Vagrant available with {len(running_vms)} running VMs"
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": "Vagrant command timed out",
                "suggestion": "Check if Vagrant is responsive"
            }
        except FileNotFoundError:
            return {
                "success": False,
                "error": "Vagrant not found in PATH",
                "suggestion": "Install Vagrant: https://www.vagrantup.com/downloads"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Vagrant test failed: {e}"
            }
    
    def test_docker_service(self) -> Dict[str, Any]:
        """Test Docker daemon availability."""
        logger.info("Testing Docker service availability...")
        
        try:
            # Check if docker command is available
            result = subprocess.run(
                ['docker', '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0:
                return {
                    "success": False,
                    "error": "Docker command not available"
                }
            
            docker_version = result.stdout.strip()
            
            # Check if Docker daemon is running
            result = subprocess.run(
                ['docker', 'info'],
                capture_output=True,
                text=True,
                timeout=15
            )
            
            if result.returncode != 0:
                return {
                    "success": False,
                    "error": "Docker daemon not running",
                    "docker_version": docker_version,
                    "suggestion": "Start Docker daemon: sudo systemctl start docker"
                }
            
            # Check running containers
            result = subprocess.run(
                ['docker', 'ps', '--format', 'table {{.Names}}\t{{.Status}}'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            running_containers = []
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                for line in lines:
                    if line.strip():
                        parts = line.split('\t')
                        if len(parts) >= 2:
                            running_containers.append({
                                "name": parts[0],
                                "status": parts[1]
                            })
            
            # Check for TurdParty containers
            turdparty_containers = [c for c in running_containers if 'turdparty' in c['name'].lower()]
            
            return {
                "success": True,
                "docker_version": docker_version,
                "daemon_running": True,
                "total_containers": len(running_containers),
                "turdparty_containers": len(turdparty_containers),
                "running_containers": running_containers,
                "message": f"Docker available with {len(running_containers)} running containers"
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": "Docker command timed out"
            }
        except FileNotFoundError:
            return {
                "success": False,
                "error": "Docker not found in PATH",
                "suggestion": "Install Docker: https://docs.docker.com/get-docker/"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Docker test failed: {e}"
            }
    
    def test_service_connectivity(self, service_name: str, url: str, timeout: int = 10) -> Dict[str, Any]:
        """Test connectivity to a service via Traefik."""
        logger.info(f"Testing {service_name} connectivity at {url}...")
        
        try:
            # Set up session with appropriate headers for Traefik
            session = requests.Session()
            session.headers.update({
                'Host': url.split('//')[1].split(':')[0] if '://' in url else url.split(':')[0],
                'User-Agent': 'TurdParty-Infrastructure-Test/1.0'
            })
            
            # For Traefik routing, use localhost with Host header
            if '.turdparty.localhost' in url:
                test_url = url.replace(url.split('//')[1].split(':')[0], 'localhost')
            else:
                test_url = url
            
            response = session.get(test_url, timeout=timeout)
            
            return {
                "success": response.status_code < 400,
                "status_code": response.status_code,
                "response_time_ms": int(response.elapsed.total_seconds() * 1000),
                "url": url,
                "test_url": test_url,
                "headers": dict(response.headers),
                "accessible": True
            }
            
        except requests.exceptions.ConnectTimeout:
            return {
                "success": False,
                "error": "Connection timeout",
                "url": url,
                "accessible": False
            }
        except requests.exceptions.ConnectionError:
            return {
                "success": False,
                "error": "Connection refused",
                "url": url,
                "accessible": False,
                "suggestion": f"Check if {service_name} service is running"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Service test failed: {e}",
                "url": url,
                "accessible": False
            }
    
    def test_all_services(self) -> Dict[str, Any]:
        """Test connectivity to all configured services."""
        logger.info("Testing all service connectivity...")
        
        service_results = {}
        
        for service_name, url in self.service_urls.items():
            service_results[service_name] = self.test_service_connectivity(service_name, url)
        
        # Calculate summary
        total_services = len(service_results)
        accessible_services = sum(1 for result in service_results.values() if result.get("accessible", False))
        
        return {
            "success": accessible_services > 0,
            "total_services": total_services,
            "accessible_services": accessible_services,
            "inaccessible_services": total_services - accessible_services,
            "service_results": service_results,
            "traefik_routing": accessible_services > 0,
            "message": f"{accessible_services}/{total_services} services accessible via Traefik"
        }
    
    def test_database_connectivity(self) -> Dict[str, Any]:
        """Test database connectivity."""
        logger.info("Testing database connectivity...")
        
        try:
            # Try to import database modules
            import psycopg2
            
            # Try to connect to database
            # This would normally use environment variables or config
            conn_params = {
                'host': 'localhost',
                'port': 5432,
                'database': 'turdparty',
                'user': 'turdparty',
                'password': 'turdparty'
            }
            
            conn = psycopg2.connect(**conn_params)
            cursor = conn.cursor()
            cursor.execute('SELECT version();')
            db_version = cursor.fetchone()[0]
            cursor.close()
            conn.close()
            
            return {
                "success": True,
                "database_version": db_version,
                "connection_params": {k: v for k, v in conn_params.items() if k != 'password'},
                "accessible": True
            }
            
        except ImportError:
            return {
                "success": False,
                "error": "psycopg2 not available",
                "suggestion": "Install psycopg2: pip install psycopg2-binary"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Database connection failed: {e}",
                "accessible": False,
                "suggestion": "Check if PostgreSQL is running and accessible"
            }
    
    def run_comprehensive_test(self, specific_service: Optional[str] = None) -> Dict[str, Any]:
        """Run comprehensive infrastructure readiness tests."""
        logger.info("🚀 Running comprehensive infrastructure readiness tests...")
        
        if specific_service:
            # Test only specific service
            if specific_service == 'vagrant':
                tests = [("vagrant_service", self.test_vagrant_service)]
            elif specific_service == 'docker':
                tests = [("docker_service", self.test_docker_service)]
            elif specific_service == 'database':
                tests = [("database_connectivity", self.test_database_connectivity)]
            elif specific_service == 'services':
                tests = [("service_connectivity", self.test_all_services)]
            else:
                logger.error(f"Unknown service: {specific_service}")
                return {"error": f"Unknown service: {specific_service}"}
        else:
            # Run all tests
            tests = [
                ("vagrant_service", self.test_vagrant_service),
                ("docker_service", self.test_docker_service),
                ("database_connectivity", self.test_database_connectivity),
                ("service_connectivity", self.test_all_services)
            ]
        
        results = {}
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"Running test: {test_name}")
            
            try:
                result = test_func()
                results[test_name] = result
                
                if result.get("success", False):
                    passed_tests += 1
                    logger.info(f"✅ {test_name}: PASSED")
                    if self.verbose and "message" in result:
                        logger.info(f"   {result['message']}")
                else:
                    logger.error(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
                    if "suggestion" in result:
                        logger.info(f"   💡 Suggestion: {result['suggestion']}")
                    
            except Exception as e:
                logger.error(f"❌ {test_name}: EXCEPTION - {e}")
                results[test_name] = {
                    "success": False,
                    "error": f"Test exception: {e}"
                }
        
        success_rate = (passed_tests / total_tests) * 100
        overall_success = passed_tests == total_tests
        
        summary = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": success_rate,
            "overall_success": overall_success,
            "infrastructure_ready": overall_success
        }
        
        self.test_results.update({
            "tests": results,
            "summary": summary,
            "end_time": datetime.now().isoformat()
        })
        
        return self.test_results
    
    def save_results(self, filename: str = None):
        """Save test results to file."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"infrastructure_readiness_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        logger.info(f"Test results saved to: {filename}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test infrastructure readiness")
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose output"
    )
    parser.add_argument(
        "--service",
        choices=["vagrant", "docker", "database", "services"],
        help="Test only specific service"
    )
    parser.add_argument(
        "--save-results",
        action="store_true",
        help="Save test results to file"
    )
    
    args = parser.parse_args()
    
    tester = InfrastructureReadinessTester(verbose=args.verbose)
    
    logger.info("🔧 Starting Infrastructure Readiness Tests")
    logger.info("=" * 60)
    
    results = tester.run_comprehensive_test(specific_service=args.service)
    
    if "error" in results:
        logger.error(f"Test execution failed: {results['error']}")
        sys.exit(1)
    
    # Print summary
    summary = results["summary"]
    logger.info("\n" + "=" * 60)
    logger.info("INFRASTRUCTURE READINESS TEST RESULTS")
    logger.info("=" * 60)
    logger.info(f"Total Tests: {summary['total_tests']}")
    logger.info(f"Passed: {summary['passed_tests']}")
    logger.info(f"Failed: {summary['failed_tests']}")
    logger.info(f"Success Rate: {summary['success_rate']:.1f}%")
    logger.info(f"Infrastructure Ready: {'✅ YES' if summary['infrastructure_ready'] else '❌ NO'}")
    logger.info("=" * 60)
    
    if args.save_results:
        tester.save_results()
    
    sys.exit(0 if summary['infrastructure_ready'] else 1)


if __name__ == "__main__":
    main()
