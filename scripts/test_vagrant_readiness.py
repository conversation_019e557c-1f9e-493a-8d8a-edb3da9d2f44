#!/usr/bin/env python3
"""
Quick Vagrant Readiness Test

Tests if Vagrant is available and running before VM management tests.

Usage:
    python scripts/test_vagrant_readiness.py
    nix-shell --run "python scripts/test_vagrant_readiness.py"
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.test_infrastructure_readiness import InfrastructureReadinessTester
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Test Vagrant readiness."""
    logger.info("🔧 Testing Vagrant Readiness for VM Management")
    logger.info("=" * 50)
    
    tester = InfrastructureReadinessTester(verbose=True)
    
    # Test only Vagrant
    results = tester.run_comprehensive_test(specific_service='vagrant')
    
    if "error" in results:
        logger.error(f"❌ Test execution failed: {results['error']}")
        sys.exit(1)
    
    vagrant_result = results["tests"]["vagrant_service"]
    
    logger.info("\n" + "=" * 50)
    logger.info("VAGRANT READINESS RESULTS")
    logger.info("=" * 50)
    
    if vagrant_result["success"]:
        logger.info("✅ VAGRANT: READY")
        logger.info(f"   Version: {vagrant_result.get('vagrant_version', 'Unknown')}")
        logger.info(f"   Running VMs: {vagrant_result.get('total_running_vms', 0)}")
        logger.info(f"   TurdParty VMs: {vagrant_result.get('turdparty_vms', 0)}")
        
        if vagrant_result.get('running_vms'):
            logger.info("   Active VMs:")
            for vm in vagrant_result['running_vms']:
                logger.info(f"     - {vm['name']} ({vm['provider']}) - {vm['state']}")
        
        logger.info("\n🎉 Vagrant is ready for VM management!")
        
    else:
        logger.error("❌ VAGRANT: NOT READY")
        logger.error(f"   Error: {vagrant_result.get('error', 'Unknown error')}")
        if 'suggestion' in vagrant_result:
            logger.info(f"   💡 Suggestion: {vagrant_result['suggestion']}")
        
        logger.error("\n🚨 Vagrant must be available before running VM management tests!")
        
    logger.info("=" * 50)
    
    sys.exit(0 if vagrant_result["success"] else 1)


if __name__ == "__main__":
    main()
