#!/usr/bin/env python3
"""
Core VM Management Test

Simple test script that validates the core VM management functionality
without external dependencies. Tests the basic components and logic.

Usage:
    python scripts/test_vm_management_core.py
"""

import json
import logging
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CoreVMManagementTester:
    """Test core VM management functionality."""
    
    def __init__(self):
        self.test_results = {
            "start_time": datetime.now().isoformat(),
            "tests": {},
            "summary": {}
        }
    
    def test_service_url_manager(self) -> Dict[str, Any]:
        """Test ServiceURLManager functionality."""
        logger.info("Testing ServiceURLManager...")
        
        try:
            from utils.service_urls import ServiceURLManager
            
            # Test local environment
            manager = ServiceURLManager('local')
            
            # Test getting API URL
            api_url = manager.get_service_url('api')
            
            # Validate URL format
            if not api_url or not api_url.startswith('http'):
                return {
                    "success": False,
                    "error": f"Invalid API URL: {api_url}"
                }
            
            # Test getting other service URLs
            services_tested = {}
            for service in ['api', 'frontend', 'elasticsearch', 'kibana']:
                try:
                    url = manager.get_service_url(service)
                    services_tested[service] = {
                        "url": url,
                        "valid": url and url.startswith('http')
                    }
                except Exception as e:
                    services_tested[service] = {
                        "url": None,
                        "valid": False,
                        "error": str(e)
                    }
            
            return {
                "success": True,
                "api_url": api_url,
                "services_tested": services_tested,
                "environment": 'local'
            }
            
        except ImportError as e:
            return {
                "success": False,
                "error": f"ServiceURLManager import failed: {e}",
                "suggestion": "Ensure utils.service_urls module is available"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"ServiceURLManager test failed: {e}"
            }
    
    def test_basic_vm_availability_manager_import(self) -> Dict[str, Any]:
        """Test BasicVMAvailabilityManager import and initialization."""
        logger.info("Testing BasicVMAvailabilityManager import...")
        
        try:
            from services.workers.tasks.basic_vm_availability_manager import (
                BasicVMAvailabilityManager, VMTemplate, PoolConfig
            )
            
            # Test manager initialization
            manager = BasicVMAvailabilityManager()
            
            # Test pool configurations
            configs = manager.pool_configs
            
            if not configs:
                return {
                    "success": False,
                    "error": "No pool configurations found"
                }
            
            # Validate configurations
            expected_templates = [
                "ubuntu:20.04",
                "ubuntu:22.04", 
                "alpine:latest",
                "10Baht/windows10-turdparty"
            ]
            
            missing_templates = []
            for template in expected_templates:
                if template not in configs:
                    missing_templates.append(template)
            
            if missing_templates:
                return {
                    "success": False,
                    "error": f"Missing template configurations: {missing_templates}"
                }
            
            # Test configuration structure
            config_details = {}
            for template, config in configs.items():
                config_details[template] = {
                    "min_ready": config.min_ready,
                    "max_total": config.max_total,
                    "vm_type": config.vm_type,
                    "memory_mb": config.memory_mb,
                    "cpus": config.cpus
                }
            
            return {
                "success": True,
                "manager_initialized": True,
                "template_count": len(configs),
                "templates": list(configs.keys()),
                "config_details": config_details
            }
            
        except ImportError as e:
            return {
                "success": False,
                "error": f"BasicVMAvailabilityManager import failed: {e}",
                "suggestion": "Ensure basic VM management modules are deployed"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"BasicVMAvailabilityManager test failed: {e}"
            }
    
    def test_database_models(self) -> Dict[str, Any]:
        """Test database models import and structure."""
        logger.info("Testing database models...")
        
        try:
            from services.database.models import VMInstance, VMStatus
            
            # Test VMStatus enum
            statuses = [status.value for status in VMStatus]
            expected_statuses = ["creating", "ready", "running", "terminated", "failed"]
            
            missing_statuses = [s for s in expected_statuses if s not in statuses]
            
            if missing_statuses:
                return {
                    "success": False,
                    "error": f"Missing VM statuses: {missing_statuses}"
                }
            
            # Test VMInstance model structure
            vm_instance = VMInstance()
            
            # Check for required attributes
            required_attrs = ["name", "template", "memory_mb", "cpus", "status"]
            missing_attrs = []
            
            for attr in required_attrs:
                if not hasattr(vm_instance, attr):
                    missing_attrs.append(attr)
            
            if missing_attrs:
                return {
                    "success": False,
                    "error": f"VMInstance missing attributes: {missing_attrs}"
                }
            
            return {
                "success": True,
                "vm_statuses": statuses,
                "vm_instance_attrs": [attr for attr in dir(vm_instance) if not attr.startswith('_')],
                "models_imported": True
            }
            
        except ImportError as e:
            return {
                "success": False,
                "error": f"Database models import failed: {e}",
                "suggestion": "Ensure database models are available"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Database models test failed: {e}"
            }
    
    def test_api_routes_import(self) -> Dict[str, Any]:
        """Test API routes import and structure."""
        logger.info("Testing API routes import...")
        
        try:
            from services.api.src.routes.v1.basic_vm_allocation import router
            
            # Get route information
            routes = []
            for route in router.routes:
                routes.append({
                    "path": route.path,
                    "methods": list(route.methods) if hasattr(route, 'methods') else [],
                    "name": getattr(route, 'name', 'unknown')
                })
            
            # Check for expected endpoints
            expected_paths = ["/allocate", "/pools/status", "/pools/maintain"]
            found_paths = [route["path"] for route in routes]
            
            missing_paths = [path for path in expected_paths if path not in found_paths]
            
            if missing_paths:
                return {
                    "success": False,
                    "error": f"Missing API endpoints: {missing_paths}",
                    "found_paths": found_paths
                }
            
            return {
                "success": True,
                "routes_count": len(routes),
                "routes": routes,
                "expected_endpoints_found": True
            }
            
        except ImportError as e:
            return {
                "success": False,
                "error": f"API routes import failed: {e}",
                "suggestion": "Ensure API routes are deployed"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"API routes test failed: {e}"
            }
    
    def test_configuration_files(self) -> Dict[str, Any]:
        """Test configuration files exist and are valid."""
        logger.info("Testing configuration files...")
        
        try:
            # Test service URLs configuration
            service_urls_file = project_root / "config" / "service-urls.json"
            
            if not service_urls_file.exists():
                return {
                    "success": False,
                    "error": "service-urls.json not found",
                    "expected_path": str(service_urls_file)
                }
            
            with open(service_urls_file, 'r') as f:
                service_urls_config = json.load(f)
            
            # Validate structure
            if "environments" not in service_urls_config:
                return {
                    "success": False,
                    "error": "service-urls.json missing 'environments' key"
                }
            
            environments = service_urls_config["environments"]
            
            if "local" not in environments:
                return {
                    "success": False,
                    "error": "service-urls.json missing 'local' environment"
                }
            
            local_config = environments["local"]
            
            if "services" not in local_config:
                return {
                    "success": False,
                    "error": "local environment missing 'services' configuration"
                }
            
            services = local_config["services"]
            expected_services = ["api", "frontend", "elasticsearch"]
            
            missing_services = [svc for svc in expected_services if svc not in services]
            
            return {
                "success": len(missing_services) == 0,
                "service_urls_file": str(service_urls_file),
                "environments": list(environments.keys()),
                "local_services": list(services.keys()),
                "missing_services": missing_services,
                "config_valid": len(missing_services) == 0
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Configuration files test failed: {e}"
            }
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive core tests."""
        logger.info("🚀 Running comprehensive core VM management tests...")
        
        tests = [
            ("service_url_manager", self.test_service_url_manager),
            ("basic_vm_manager", self.test_basic_vm_availability_manager_import),
            ("database_models", self.test_database_models),
            ("api_routes", self.test_api_routes_import),
            ("configuration_files", self.test_configuration_files)
        ]
        
        results = {}
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"Running test: {test_name}")
            
            try:
                result = test_func()
                results[test_name] = result
                
                if result.get("success", False):
                    passed_tests += 1
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                logger.error(f"❌ {test_name}: EXCEPTION - {e}")
                results[test_name] = {
                    "success": False,
                    "error": f"Test exception: {e}"
                }
        
        success_rate = (passed_tests / total_tests) * 100
        overall_success = passed_tests == total_tests
        
        summary = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": success_rate,
            "overall_success": overall_success
        }
        
        self.test_results.update({
            "tests": results,
            "summary": summary,
            "end_time": datetime.now().isoformat()
        })
        
        return self.test_results


def main():
    """Main function."""
    logger.info("🔧 Starting Core VM Management Tests")
    logger.info("=" * 50)
    
    tester = CoreVMManagementTester()
    results = tester.run_comprehensive_test()
    
    # Print summary
    summary = results["summary"]
    logger.info("\n" + "=" * 50)
    logger.info("CORE VM MANAGEMENT TEST RESULTS")
    logger.info("=" * 50)
    logger.info(f"Total Tests: {summary['total_tests']}")
    logger.info(f"Passed: {summary['passed_tests']}")
    logger.info(f"Failed: {summary['failed_tests']}")
    logger.info(f"Success Rate: {summary['success_rate']:.1f}%")
    logger.info(f"Overall Success: {'✅ PASS' if summary['overall_success'] else '❌ FAIL'}")
    logger.info("=" * 50)
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"core_vm_test_results_{timestamp}.json"
    
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"📄 Results saved to: {filename}")
    
    sys.exit(0 if summary['overall_success'] else 1)


if __name__ == "__main__":
    main()
