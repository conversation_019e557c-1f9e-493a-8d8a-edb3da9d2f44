#!/bin/bash
# TurdParty Documentation Update Script
# Automatically rebuilds documentation when source files change

set -e

# Configuration
DOCS_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/../docs" && pwd)"
WATCH_DIRS=("$DOCS_DIR" "$(dirname "$DOCS_DIR")/services" "$(dirname "$DOCS_DIR")/frontend")
BUILD_HTML=true
BUILD_PDF=false
SERVE_DOCS=false
WATCH_MODE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
TurdParty Documentation Update Script

Usage: $0 [OPTIONS]

Options:
    -h, --help          Show this help message
    -w, --watch         Watch for changes and rebuild automatically
    -s, --serve         Serve documentation locally after building
    -p, --pdf           Generate PDF documentation
    --html-only         Build only HTML documentation (default)
    --clean             Clean build directory before building

Examples:
    $0                  # Build HTML documentation once
    $0 -w               # Watch for changes and rebuild
    $0 -s               # Build and serve documentation
    $0 -p               # Build HTML and PDF documentation
    $0 -w -s            # Watch, rebuild, and serve documentation

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -w|--watch)
            WATCH_MODE=true
            shift
            ;;
        -s|--serve)
            SERVE_DOCS=true
            shift
            ;;
        -p|--pdf)
            BUILD_PDF=true
            shift
            ;;
        --html-only)
            BUILD_PDF=false
            shift
            ;;
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Check dependencies
check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is required but not installed"
        exit 1
    fi
    
    if [ ! -d "$DOCS_DIR/venv" ]; then
        log_warning "Virtual environment not found, creating..."
        cd "$DOCS_DIR"
        python3 -m venv venv
        source venv/bin/activate
        pip install --upgrade pip
        pip install -r requirements.txt
        log_success "Virtual environment created and dependencies installed"
    fi
    
    if [ "$BUILD_PDF" = true ] && ! command -v wkhtmltopdf &> /dev/null; then
        if command -v nix-shell &> /dev/null; then
            log_info "wkhtmltopdf not found, will use nix-shell"
        else
            log_warning "wkhtmltopdf not found and nix not available, PDF generation will be skipped"
            BUILD_PDF=false
        fi
    fi
    
    if [ "$WATCH_MODE" = true ] && ! command -v inotifywait &> /dev/null; then
        log_warning "inotifywait not found, watch mode not available"
        WATCH_MODE=false
    fi
}

# Clean build directory
clean_build() {
    if [ "$CLEAN_BUILD" = true ]; then
        log_info "Cleaning build directory..."
        rm -rf "$DOCS_DIR/_build"
        log_success "Build directory cleaned"
    fi
}

# Build documentation
build_docs() {
    log_info "Building documentation..."
    cd "$DOCS_DIR"
    source venv/bin/activate
    
    # Build HTML documentation
    if [ "$BUILD_HTML" = true ]; then
        log_info "Building HTML documentation..."
        if sphinx-build -b html . _build/html -q; then
            log_success "HTML documentation built successfully"
        else
            log_error "HTML documentation build failed"
            return 1
        fi
        
        # Build single HTML for PDF generation
        if sphinx-build -b singlehtml . _build/singlehtml -q; then
            log_success "Single HTML documentation built successfully"
        else
            log_warning "Single HTML documentation build failed"
        fi
    fi
    
    # Build PDF documentation
    if [ "$BUILD_PDF" = true ]; then
        log_info "Building PDF documentation..."
        if command -v wkhtmltopdf &> /dev/null; then
            wkhtmltopdf --enable-local-file-access _build/singlehtml/index.html _build/TurdParty-Documentation.pdf 2>/dev/null
        elif command -v nix-shell &> /dev/null; then
            nix-shell -p wkhtmltopdf --run "wkhtmltopdf --enable-local-file-access _build/singlehtml/index.html _build/TurdParty-Documentation.pdf" 2>/dev/null
        fi
        
        if [ -f "_build/TurdParty-Documentation.pdf" ]; then
            log_success "PDF documentation built successfully"
        else
            log_warning "PDF documentation build failed"
        fi
    fi
    
    return 0
}

# Serve documentation
serve_docs() {
    if [ "$SERVE_DOCS" = true ]; then
        log_info "Starting documentation server..."
        cd "$DOCS_DIR/_build/html"
        log_success "Documentation server started at http://localhost:8080"
        log_info "Press Ctrl+C to stop the server"
        python3 -m http.server 8080
    fi
}

# Watch for changes
watch_changes() {
    if [ "$WATCH_MODE" = true ]; then
        log_info "Watching for changes in documentation files..."
        log_info "Press Ctrl+C to stop watching"
        
        while true; do
            inotifywait -r -e modify,create,delete,move "${WATCH_DIRS[@]}" --include '\.(rst|md|py|tsx?|jsx?)$' -q
            log_info "Changes detected, rebuilding documentation..."
            if build_docs; then
                log_success "Documentation rebuilt at $(date)"
            else
                log_error "Documentation rebuild failed at $(date)"
            fi
            sleep 1
        done
    fi
}

# Main execution
main() {
    log_info "TurdParty Documentation Update Script"
    log_info "======================================"
    
    check_dependencies
    clean_build
    
    if build_docs; then
        if [ "$WATCH_MODE" = true ]; then
            if [ "$SERVE_DOCS" = true ]; then
                # Start server in background for watch mode
                (serve_docs) &
                SERVER_PID=$!
                trap "kill $SERVER_PID 2>/dev/null" EXIT
            fi
            watch_changes
        else
            serve_docs
        fi
    else
        log_error "Documentation build failed"
        exit 1
    fi
}

# Run main function
main "$@"
