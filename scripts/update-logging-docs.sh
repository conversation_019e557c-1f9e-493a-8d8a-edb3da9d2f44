#!/bin/bash
# Update TurdParty documentation with latest ELK logging information

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Print functions
print_header() {
    echo -e "\n${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║${NC} ${CYAN}$1${NC} ${PURPLE}║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}\n"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if documentation files exist
check_documentation_files() {
    print_status "Checking documentation files..."
    
    local files=(
        "docs/getting-started/components/monitoring-stack.rst"
        "docs/getting-started/operations/logging-operations.rst"
        "docs/getting-started/index.rst"
        "docs/getting-started/quickstart/installation.rst"
    )
    
    local missing_files=()
    
    for file in "${files[@]}"; do
        if [ ! -f "$PROJECT_ROOT/$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        print_success "All documentation files present"
    else
        print_error "Missing documentation files:"
        for file in "${missing_files[@]}"; do
            echo -e "  ${RED}✗${NC} $file"
        done
        return 1
    fi
}

# Function to rebuild documentation
rebuild_documentation() {
    print_status "Rebuilding Sphinx documentation..."
    
    cd "$PROJECT_ROOT"
    
    if [ -x "./scripts/build-sphinx-docs.sh" ]; then
        ./scripts/build-sphinx-docs.sh --clean
        print_success "Documentation rebuilt successfully"
    else
        print_error "Build script not found or not executable"
        return 1
    fi
}

# Function to verify documentation accessibility
verify_documentation() {
    print_status "Verifying documentation accessibility..."
    
    local urls=(
        "http://docs.turdparty.localhost/getting-started/"
        "http://docs.turdparty.localhost/getting-started/components/monitoring-stack.html"
        "http://docs.turdparty.localhost/getting-started/operations/logging-operations.html"
        "http://docs.turdparty.localhost/getting-started/quickstart/installation.html"
    )
    
    local failed_urls=()
    
    for url in "${urls[@]}"; do
        if ! curl -s --fail "$url" > /dev/null; then
            failed_urls+=("$url")
        fi
    done
    
    if [ ${#failed_urls[@]} -eq 0 ]; then
        print_success "All documentation URLs accessible"
    else
        print_warning "Some documentation URLs not accessible:"
        for url in "${failed_urls[@]}"; do
            echo -e "  ${YELLOW}⚠${NC} $url"
        done
    fi
}

# Function to show documentation structure
show_documentation_structure() {
    print_header "📚 Updated Documentation Structure"
    
    echo -e "${GREEN}🏗️ System Components:${NC}"
    echo -e "  • API Layer: ${CYAN}http://docs.turdparty.localhost/getting-started/components/api-layer.html${NC}"
    echo -e "  • Storage Systems: ${CYAN}http://docs.turdparty.localhost/getting-started/components/storage-systems.html${NC}"
    echo -e "  • VM Management: ${CYAN}http://docs.turdparty.localhost/getting-started/components/vm-management.html${NC}"
    echo -e "  • ${YELLOW}Monitoring Stack: ${CYAN}http://docs.turdparty.localhost/getting-started/components/monitoring-stack.html${NC} ${YELLOW}[NEW]${NC}"
    
    echo -e "\n${GREEN}🚀 Quick Start:${NC}"
    echo -e "  • Prerequisites: ${CYAN}http://docs.turdparty.localhost/getting-started/quickstart/prerequisites.html${NC}"
    echo -e "  • Installation: ${CYAN}http://docs.turdparty.localhost/getting-started/quickstart/installation.html${NC} ${YELLOW}[UPDATED]${NC}"
    
    echo -e "\n${GREEN}🔧 Operations:${NC}"
    echo -e "  • ${YELLOW}Logging Operations: ${CYAN}http://docs.turdparty.localhost/getting-started/operations/logging-operations.html${NC} ${YELLOW}[NEW]${NC}"
    
    echo -e "\n${GREEN}📊 ELK Stack Coverage:${NC}"
    echo -e "  • Complete logging pipeline documentation"
    echo -e "  • Service-specific log configuration"
    echo -e "  • Troubleshooting and maintenance guides"
    echo -e "  • Performance monitoring and optimization"
    echo -e "  • Kibana dashboard setup and usage"
}

# Function to show logging-specific information
show_logging_information() {
    print_header "📊 ELK Logging Documentation Added"
    
    echo -e "${GREEN}📋 New Content Added:${NC}"
    echo -e "  ✅ Complete ELK stack architecture diagrams"
    echo -e "  ✅ Service logging configuration matrix"
    echo -e "  ✅ Filebeat Docker log collection setup"
    echo -e "  ✅ Logstash processing pipeline documentation"
    echo -e "  ✅ Elasticsearch indexing strategy"
    echo -e "  ✅ Kibana dashboard access and setup"
    echo -e "  ✅ Troubleshooting common logging issues"
    echo -e "  ✅ Performance monitoring and optimization"
    echo -e "  ✅ Log maintenance and cleanup procedures"
    echo -e "  ✅ Alerting and health check automation"
    
    echo -e "\n${GREEN}🔧 Management Scripts:${NC}"
    echo -e "  • Restart with logging: ${CYAN}./scripts/restart-with-logging.sh${NC}"
    echo -e "  • Build documentation: ${CYAN}./scripts/build-sphinx-docs.sh${NC}"
    echo -e "  • Update logging docs: ${CYAN}./scripts/update-logging-docs.sh${NC}"
    
    echo -e "\n${GREEN}🌐 Key Access Points:${NC}"
    echo -e "  • Kibana Dashboard: ${CYAN}http://kibana.turdparty.localhost${NC}"
    echo -e "  • Elasticsearch API: ${CYAN}http://localhost:9200${NC}"
    echo -e "  • Log Indices: ${CYAN}http://localhost:9200/_cat/indices/turdparty-*${NC}"
    echo -e "  • Documentation: ${CYAN}http://docs.turdparty.localhost/getting-started/components/monitoring-stack.html${NC}"
}

# Function to show usage
show_usage() {
    echo -e "${CYAN}TurdParty Logging Documentation Updater${NC}"
    echo -e "${CYAN}=======================================${NC}"
    echo ""
    echo -e "${GREEN}Usage:${NC} $0 [OPTIONS]"
    echo ""
    echo -e "${GREEN}Options:${NC}"
    echo -e "  ${YELLOW}--check-only${NC}    Only check files, don't rebuild"
    echo -e "  ${YELLOW}--no-verify${NC}     Skip URL verification"
    echo -e "  ${YELLOW}--help${NC}          Show this help message"
    echo ""
    echo -e "${GREEN}Description:${NC}"
    echo -e "  Updates and rebuilds TurdParty documentation with comprehensive"
    echo -e "  ELK logging stack information, including monitoring, operations,"
    echo -e "  and troubleshooting guides."
}

# Main function
main() {
    local check_only=false
    local verify_urls=true
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --check-only)
                check_only=true
                shift
                ;;
            --no-verify)
                verify_urls=false
                shift
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    print_header "📚 TurdParty Logging Documentation Update"
    
    # Execute update steps
    check_documentation_files
    
    if [ "$check_only" = false ]; then
        rebuild_documentation
        
        if [ "$verify_urls" = true ]; then
            verify_documentation
        fi
    fi
    
    show_documentation_structure
    show_logging_information
    
    print_success "Documentation update complete!"
    print_status "Access the updated documentation at: http://docs.turdparty.localhost/getting-started/"
}

# Run main function with all arguments
main "$@"
