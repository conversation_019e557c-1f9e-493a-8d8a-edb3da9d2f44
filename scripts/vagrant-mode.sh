#!/bin/bash
# TurdParty Vagrant Mode Management Script
# Switches between host-based and container-based Vagrant

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[VAGRANT-MODE]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[VAGRANT-MODE]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[VAGRANT-MODE]${NC} $1"
}

log_error() {
    echo -e "${RED}[VAGRANT-MODE]${NC} $1"
}

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENV_FILE="$PROJECT_ROOT/.env"
ENV_EXAMPLE="$PROJECT_ROOT/.env.example"

# Create .env file if it doesn't exist
create_env_file() {
    if [ ! -f "$ENV_FILE" ]; then
        log_info "Creating .env file from .env.example"
        cp "$ENV_EXAMPLE" "$ENV_FILE"
    fi
}

# Get current Vagrant mode
get_current_mode() {
    if [ -f "$ENV_FILE" ]; then
        grep "^VAGRANT_MODE=" "$ENV_FILE" | cut -d'=' -f2 || echo "host"
    else
        echo "host"
    fi
}

# Set Vagrant mode in .env file
set_vagrant_mode() {
    local mode="$1"
    create_env_file
    
    if grep -q "^VAGRANT_MODE=" "$ENV_FILE"; then
        # Update existing line
        sed -i "s/^VAGRANT_MODE=.*/VAGRANT_MODE=$mode/" "$ENV_FILE"
    else
        # Add new line
        echo "VAGRANT_MODE=$mode" >> "$ENV_FILE"
    fi
    
    log_success "Set VAGRANT_MODE=$mode in $ENV_FILE"
}

# Check if host Vagrant is available
check_host_vagrant() {
    if command -v vagrant >/dev/null 2>&1; then
        log_success "Vagrant found on host: $(vagrant --version)"
        return 0
    else
        log_warning "Vagrant not found on host system"
        return 1
    fi
}

# Check if Vagrant serve is running on host
check_vagrant_serve() {
    local port="${VAGRANT_GRPC_PORT:-40000}"
    if nc -z localhost "$port" 2>/dev/null; then
        log_success "Vagrant serve detected on port $port"
        return 0
    else
        log_warning "Vagrant serve not running on port $port"
        return 1
    fi
}

# Start Vagrant container
start_vagrant_container() {
    log_info "Starting Vagrant container..."
    cd "$PROJECT_ROOT"
    
    # Use docker-compose with vagrant-container profile
    if command -v docker-compose >/dev/null 2>&1; then
        COMPOSE_PROFILES=vagrant-container docker-compose -f compose/docker-compose.yml up -d vagrant
    else
        log_error "docker-compose not found"
        return 1
    fi
    
    # Wait for container to be healthy
    log_info "Waiting for Vagrant container to be ready..."
    for i in {1..30}; do
        if docker exec turdpartycollab_vagrant /usr/local/bin/entrypoint.sh health >/dev/null 2>&1; then
            log_success "Vagrant container is ready"
            return 0
        fi
        sleep 2
    done
    
    log_error "Vagrant container failed to start properly"
    return 1
}

# Stop Vagrant container
stop_vagrant_container() {
    log_info "Stopping Vagrant container..."
    cd "$PROJECT_ROOT"
    
    if docker ps | grep -q turdpartycollab_vagrant; then
        docker stop turdpartycollab_vagrant >/dev/null 2>&1 || true
        docker rm turdpartycollab_vagrant >/dev/null 2>&1 || true
        log_success "Vagrant container stopped"
    else
        log_info "Vagrant container not running"
    fi
}

# Show current status
show_status() {
    local current_mode
    current_mode=$(get_current_mode)
    
    log_info "Current Vagrant Mode: $current_mode"
    
    case "$current_mode" in
        "host")
            if check_host_vagrant; then
                if check_vagrant_serve; then
                    log_success "Host Vagrant is ready and serving"
                else
                    log_warning "Host Vagrant available but not serving"
                    log_info "Run: vagrant serve --host 0.0.0.0 --port 40000"
                fi
            else
                log_error "Host Vagrant not available"
                log_info "Consider switching to container mode: $0 container"
            fi
            ;;
        "container")
            if docker ps | grep -q turdpartycollab_vagrant; then
                log_success "Vagrant container is running"
            else
                log_warning "Vagrant container not running"
                log_info "Run: $0 start-container"
            fi
            ;;
        *)
            log_error "Unknown Vagrant mode: $current_mode"
            ;;
    esac
}

# Show usage information
show_usage() {
    cat << EOF
TurdParty Vagrant Mode Management

Usage: $0 <command>

Commands:
    host                Set Vagrant mode to host (default)
    container           Set Vagrant mode to container
    start-container     Start Vagrant container service
    stop-container      Stop Vagrant container service
    status              Show current Vagrant mode and status
    help                Show this help message

Vagrant Modes:
    host        Use vagrant serve running on the host system
                Requires: vagrant serve --host 0.0.0.0 --port 40000
    
    container   Use Docker container with Vagrant service
                Automatically managed via Docker Compose

Examples:
    $0 host                 # Switch to host mode
    $0 container            # Switch to container mode
    $0 start-container      # Start container service
    $0 status               # Check current status

Environment Variables:
    VAGRANT_MODE            Current mode (host/container)
    VAGRANT_GRPC_PORT       gRPC port (default: 40000)
    VAGRANT_HOST            Host address (default: localhost)

EOF
}

# Main execution
main() {
    case "${1:-status}" in
        "host")
            set_vagrant_mode "host"
            stop_vagrant_container
            log_info "Switched to host mode"
            log_info "Make sure to run: vagrant serve --host 0.0.0.0 --port 40000"
            ;;
        "container")
            set_vagrant_mode "container"
            if start_vagrant_container; then
                log_success "Switched to container mode"
            else
                log_error "Failed to start container mode"
                exit 1
            fi
            ;;
        "start-container")
            if start_vagrant_container; then
                log_success "Vagrant container started"
            else
                log_error "Failed to start Vagrant container"
                exit 1
            fi
            ;;
        "stop-container")
            stop_vagrant_container
            ;;
        "status")
            show_status
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            log_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
