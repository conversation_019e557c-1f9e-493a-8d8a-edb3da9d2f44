#!/bin/bash

# TurdParty Analysis Reports Viewer
# Quick access to all generated analysis reports

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${CYAN}================================================================================================${NC}"
    echo -e "${CYAN}🎉💩🥳 TurdParty Analysis Reports Viewer 🥳💩🎉${NC}"
    echo -e "${CYAN}================================================================================================${NC}"
    echo -e "${BLUE}Generated Reports from 10-Binary Rich CLI Analysis${NC}"
    echo -e "${BLUE}Timestamp: $(date)${NC}"
    echo ""
}

check_reports() {
    echo -e "${BLUE}📋 Checking for generated reports...${NC}"
    
    REPORT_DIR="/tmp/turdparty_reports"
    
    if [[ ! -d "$REPORT_DIR" ]]; then
        echo -e "${RED}❌ Report directory not found: $REPORT_DIR${NC}"
        echo -e "${YELLOW}💡 Run the rich CLI analysis first: ./scripts/run-10-binaries-rich-cli.py${NC}"
        exit 1
    fi
    
    # Count reports
    HTML_REPORTS=$(find "$REPORT_DIR" -name "*.html" | wc -l)
    JSON_REPORTS=$(find "$REPORT_DIR" -name "*.json" | wc -l)
    
    echo -e "${GREEN}✅ Found $HTML_REPORTS HTML reports and $JSON_REPORTS JSON reports${NC}"
    echo ""
}

show_report_summary() {
    echo -e "${PURPLE}📊 Report Summary${NC}"
    echo "=" * 60
    
    REPORT_DIR="/tmp/turdparty_reports"
    
    # Create summary table
    printf "%-12s %-10s %-10s %-15s\n" "Binary" "HTML" "JSON" "Size"
    echo "------------------------------------------------------------"
    
    for binary in vscode nodejs python chrome firefox notepadpp 7zip putty vlc git; do
        html_file="$REPORT_DIR/${binary}_analysis_report.html"
        json_file="$REPORT_DIR/${binary}_analysis_report.json"
        
        html_status="❌"
        json_status="❌"
        size="N/A"
        
        if [[ -f "$html_file" ]]; then
            html_status="✅"
            size=$(du -h "$html_file" | cut -f1)
        fi
        
        if [[ -f "$json_file" ]]; then
            json_status="✅"
        fi
        
        printf "%-12s %-10s %-10s %-15s\n" "$binary" "$html_status" "$json_status" "$size"
    done
    
    echo ""
}

show_elasticsearch_info() {
    echo -e "${BLUE}📊 Elasticsearch Data${NC}"
    echo "=" * 40
    
    # Check Elasticsearch connection
    if curl -s "http://localhost:9200/_cluster/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Elasticsearch is accessible${NC}"
        
        # Get event count
        EVENT_COUNT=$(curl -s "http://localhost:9200/turdparty-rich-cli-ecs-*/_count" | grep -o '"count":[0-9]*' | cut -d':' -f2)
        
        if [[ -n "$EVENT_COUNT" ]]; then
            echo -e "${GREEN}📊 Total Events: $EVENT_COUNT${NC}"
        else
            echo -e "${YELLOW}⚠️ No events found in turdparty-rich-cli-ecs-* indices${NC}"
        fi
        
        # Show index info
        echo -e "${CYAN}🔍 Query URL: http://localhost:9200/turdparty-rich-cli-ecs-*/_search${NC}"
        echo -e "${CYAN}📈 Kibana: http://localhost:5601/app/discover#/${NC}"
        
    else
        echo -e "${RED}❌ Elasticsearch not accessible at http://localhost:9200${NC}"
    fi
    
    echo ""
}

open_reports() {
    echo -e "${PURPLE}🌐 Opening Reports${NC}"
    echo "=" * 30
    
    REPORT_DIR="/tmp/turdparty_reports"
    
    # Check if we have a browser available
    if command -v firefox > /dev/null 2>&1; then
        BROWSER="firefox"
    elif command -v google-chrome > /dev/null 2>&1; then
        BROWSER="google-chrome"
    elif command -v chromium > /dev/null 2>&1; then
        BROWSER="chromium"
    else
        echo -e "${YELLOW}⚠️ No browser found. Reports available at: $REPORT_DIR${NC}"
        return
    fi
    
    echo -e "${GREEN}🌐 Opening reports in $BROWSER...${NC}"
    
    # Open a few key reports
    for binary in vscode python firefox; do
        html_file="$REPORT_DIR/${binary}_analysis_report.html"
        if [[ -f "$html_file" ]]; then
            echo -e "${BLUE}📄 Opening $binary report...${NC}"
            $BROWSER "$html_file" > /dev/null 2>&1 &
            sleep 1
        fi
    done
    
    echo -e "${GREEN}✅ Reports opened in browser${NC}"
    echo ""
}

show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -s, --summary       Show report summary only"
    echo "  -e, --elasticsearch Show Elasticsearch info only"
    echo "  -o, --open          Open reports in browser"
    echo "  -l, --list          List all report files"
    echo ""
    echo "Examples:"
    echo "  $0                  # Show full report overview"
    echo "  $0 -s               # Show summary only"
    echo "  $0 -o               # Open reports in browser"
}

list_reports() {
    echo -e "${BLUE}📋 All Report Files${NC}"
    echo "=" * 40
    
    REPORT_DIR="/tmp/turdparty_reports"
    
    if [[ -d "$REPORT_DIR" ]]; then
        ls -la "$REPORT_DIR"
    else
        echo -e "${RED}❌ Report directory not found${NC}"
    fi
    
    echo ""
}

# Parse command line arguments
SHOW_SUMMARY_ONLY=false
SHOW_ES_ONLY=false
OPEN_REPORTS=false
LIST_REPORTS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -s|--summary)
            SHOW_SUMMARY_ONLY=true
            shift
            ;;
        -e|--elasticsearch)
            SHOW_ES_ONLY=true
            shift
            ;;
        -o|--open)
            OPEN_REPORTS=true
            shift
            ;;
        -l|--list)
            LIST_REPORTS=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    if [[ "$SHOW_SUMMARY_ONLY" == "true" ]]; then
        print_header
        check_reports
        show_report_summary
    elif [[ "$SHOW_ES_ONLY" == "true" ]]; then
        print_header
        show_elasticsearch_info
    elif [[ "$LIST_REPORTS" == "true" ]]; then
        print_header
        list_reports
    elif [[ "$OPEN_REPORTS" == "true" ]]; then
        print_header
        check_reports
        open_reports
    else
        # Full overview
        print_header
        check_reports
        show_report_summary
        show_elasticsearch_info
        
        echo -e "${CYAN}💡 Quick Actions:${NC}"
        echo -e "   📄 View reports: $0 --list"
        echo -e "   🌐 Open in browser: $0 --open"
        echo -e "   📊 ES info only: $0 --elasticsearch"
        echo ""
        
        echo -e "${GREEN}🎉 Analysis complete! All reports and data are ready for review.${NC}"
    fi
}

# Execute main function
main "$@"
