"""Add missing workflow status enum values

Revision ID: 1f4086798405
Revises: 
Create Date: 2025-06-13 23:58:40.349447

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1f4086798405'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add missing enum values to workflowstatus enum (using UPPERCASE to match existing pattern)
    op.execute("ALTER TYPE workflowstatus ADD VALUE IF NOT EXISTS 'VM_ALLOCATING'")
    op.execute("ALTER TYPE workflowstatus ADD VALUE IF NOT EXISTS 'FILE_DOWNLOADING'")
    op.execute("ALTER TYPE workflowstatus ADD VALUE IF NOT EXISTS 'VM_EXECUTING'")
    op.execute("ALTER TYPE workflowstatus ADD VALUE IF NOT EXISTS 'TERMINATED'")


def downgrade() -> None:
    # Note: PostgreSQL doesn't support removing enum values
    # This would require recreating the enum type and updating all references
    # For now, we'll leave the enum values in place
    pass
