#!/bin/bash

# 💩🎉TurdParty🎉💩 API Service Startup Dependency Check
# This script runs before the API service starts to validate critical dependencies

set -euo pipefail

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print red warning banner
print_red_warning() {
    local message=$1
    echo ""
    print_colored $RED "╔══════════════════════════════════════════════════════════════════════════════╗"
    print_colored $RED "║                                                                              ║"
    print_colored $RED "║                            🚨 CRITICAL ERROR 🚨                             ║"
    print_colored $RED "║                                                                              ║"
    print_colored $RED "╠══════════════════════════════════════════════════════════════════════════════╣"
    print_colored $RED "║                                                                              ║"
    printf "${RED}║  %-76s  ║${NC}\n" "$message"
    print_colored $RED "║                                                                              ║"
    print_colored $RED "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to check if Traefik is accessible
check_traefik_dependency() {
    print_colored $BLUE "🔍 Checking Traefik dependency for API service..."
    
    # Check if Traefik container is running
    if docker ps --filter "name=traefik" --format "{{.Names}}" | grep -q "traefik" 2>/dev/null; then
        print_colored $GREEN "✅ Traefik container is running"
        
        # Check if Traefik API is accessible
        if curl -s --connect-timeout 5 http://traefik:8080/api/overview >/dev/null 2>&1 || \
           curl -s --connect-timeout 5 http://localhost:8080/api/overview >/dev/null 2>&1; then
            print_colored $GREEN "✅ Traefik API is accessible"
            return 0
        else
            print_colored $YELLOW "⚠️  Traefik container running but API not accessible"
            print_colored $YELLOW "   This may be normal during startup - continuing..."
            return 0
        fi
    else
        print_red_warning "TRAEFIK IS NOT RUNNING - API SERVICE WILL FAIL"
        print_colored $RED "🛑 TurdParty heavily relies on Traefik for:"
        print_colored $RED "   • Authentication and authorization"
        print_colored $RED "   • WebSocket connection handling"
        print_colored $RED "   • Service discovery and routing"
        print_colored $RED "   • SSL termination and security headers"
        echo ""
        print_colored $CYAN "📋 To start Traefik, run:"
        print_colored $WHITE "   docker-compose -f traefik/docker-compose.yml up -d"
        print_colored $WHITE "   OR"
        print_colored $WHITE "   ./scripts/setup-traefik.sh"
        echo ""
        print_colored $RED "🛑 API service startup BLOCKED due to missing critical dependency"
        exit 1
    fi
}

# Function to check other dependencies
check_other_dependencies() {
    print_colored $BLUE "🔍 Checking other critical dependencies..."
    
    local deps_ok=true
    
    # Check if we can resolve required hostnames
    local required_hosts=("database" "elasticsearch" "logstash" "redis")
    
    for host in "${required_hosts[@]}"; do
        if getent hosts "$host" >/dev/null 2>&1; then
            print_colored $GREEN "✅ $host hostname resolves"
        else
            print_colored $YELLOW "⚠️  $host hostname not resolvable (may start later)"
        fi
    done
    
    # Check if required environment variables are set
    local required_vars=("DATABASE_URL" "ELASTICSEARCH_HOST" "LOGSTASH_HOST")
    
    for var in "${required_vars[@]}"; do
        if [ -n "${!var:-}" ]; then
            print_colored $GREEN "✅ $var environment variable set"
        else
            print_colored $RED "❌ $var environment variable not set"
            deps_ok=false
        fi
    done
    
    if [ "$deps_ok" = false ]; then
        print_colored $RED "🛑 Critical environment variables missing"
        exit 1
    fi
}

# Main function
main() {
    print_colored $PURPLE "🚀 💩🎉TurdParty🎉💩 API Service Startup Check"
    print_colored $PURPLE "═══════════════════════════════════════════════"
    echo ""
    
    # Run dependency checks
    check_traefik_dependency
    check_other_dependencies
    
    echo ""
    print_colored $GREEN "✅ All dependency checks passed - starting API service..."
    echo ""
}

# Run main function
main "$@"
