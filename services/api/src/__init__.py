"""
TurdParty API Service Source Package

This package contains the source code for the TurdParty API service.
It implements the core API functionality as a microservice within
the TurdParty malware analysis platform architecture.

Package Structure:
    - main: FastAPI application entry point and configuration
    - models: SQLAlchemy database models and schemas
    - routes: API route handlers organized by version and domain
    - services: Business logic services and external integrations
    - utils: Utility functions and common functionality
    - config: Configuration management and settings

Service Architecture:
    - FastAPI-based REST API service
    - SQLAlchemy ORM for database operations
    - Celery integration for background task processing
    - MinIO integration for file storage
    - ELK stack integration for logging and monitoring
    - WebSocket support for real-time communications

Key Features:
    - File upload and storage management
    - VM lifecycle management (create, start, stop, destroy)
    - File injection into analysis VMs
    - Workflow orchestration and monitoring
    - ECS-compliant structured logging
    - Real-time status updates via WebSocket
    - Comprehensive error handling and validation

Database Models:
    - FileUpload: File metadata and storage information
    - VMInstance: VM configuration and status tracking
    - WorkflowJob: Analysis workflow state and results

External Integrations:
    - Vagrant/Docker for VM management
    - MinIO for object storage
    - Elasticsearch for log aggregation
    - Celery/Redis for task queuing
    - PostgreSQL for persistent data storage

Usage:
    This package is deployed as a containerized microservice
    and provides the core API functionality for the TurdParty platform.
"""
