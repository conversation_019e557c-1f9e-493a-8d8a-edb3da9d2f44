"""
TurdParty Vagrant Configuration
Handles switching between host-based and container-based Vagrant
"""

import os
import logging
from typing import Optional

logger = logging.getLogger(__name__)


class VagrantConfig:
    """Configuration for Vagrant integration"""
    
    def __init__(self):
        self.mode = os.getenv("VAGRANT_MODE", "host").lower()
        self.grpc_port = int(os.getenv("VAGRANT_GRPC_PORT", "40000"))
        self.host = os.getenv("VAGRANT_HOST", "localhost")
        
        # Validate mode
        if self.mode not in ["host", "container"]:
            logger.warning(f"Invalid VAGRANT_MODE '{self.mode}', defaulting to 'host'")
            self.mode = "host"
    
    @property
    def grpc_endpoint(self) -> str:
        """Get the gRPC endpoint for Vagrant communication"""
        if self.mode == "container":
            # Use container name for internal Docker communication
            return f"turdpartycollab_vagrant:{self.grpc_port}"
        else:
            # Use host for external Vagrant serve
            return f"{self.host}:{self.grpc_port}"
    
    @property
    def is_container_mode(self) -> bool:
        """Check if using container mode"""
        return self.mode == "container"
    
    @property
    def is_host_mode(self) -> bool:
        """Check if using host mode"""
        return self.mode == "host"
    
    def get_connection_info(self) -> dict:
        """Get connection information for debugging"""
        return {
            "mode": self.mode,
            "grpc_endpoint": self.grpc_endpoint,
            "grpc_port": self.grpc_port,
            "host": self.host,
            "container_mode": self.is_container_mode,
            "host_mode": self.is_host_mode
        }
    
    def validate_connection(self) -> bool:
        """Validate that Vagrant is accessible"""
        import socket
        
        try:
            if self.mode == "container":
                # For container mode, check if container is running
                import subprocess
                result = subprocess.run(
                    ["docker", "ps", "--filter", "name=turdpartycollab_vagrant", "--format", "{{.Names}}"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                return "turdpartycollab_vagrant" in result.stdout
            else:
                # For host mode, check if port is accessible
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex((self.host, self.grpc_port))
                sock.close()
                return result == 0
        except Exception as e:
            logger.warning(f"Vagrant connection validation failed: {e}")
            return False


# Global configuration instance
vagrant_config = VagrantConfig()


def get_vagrant_config() -> VagrantConfig:
    """Get the global Vagrant configuration"""
    return vagrant_config


def reload_vagrant_config() -> VagrantConfig:
    """Reload Vagrant configuration from environment"""
    global vagrant_config
    vagrant_config = VagrantConfig()
    logger.info(f"Vagrant configuration reloaded: {vagrant_config.get_connection_info()}")
    return vagrant_config


# Log current configuration on import
logger.info(f"Vagrant configuration loaded: {vagrant_config.get_connection_info()}")
