"""
TurdParty API Service - Main Application

Core API service for file upload, MinIO storage, and workflow orchestration.
Focused on the essential workflow: File Upload → MinIO → VM Injection → ELK Data Exfiltration
"""

import logging
import os
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import <PERSON><PERSON><PERSON>, Request, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles

from .utils.logging import (
    setup_logging,
    CorrelationIdMiddleware,
    performance_logger,
    get_correlation_id
)

from .routes import health
from .routes.v1 import v1_router
from .services.database import init_database
from .services.minio_client import init_minio
from .services.celery_app import init_celery

# Configure enhanced logging
setup_logging(
    service_name="turdparty-api",
    log_level=os.getenv("LOG_LEVEL", "INFO"),
    enable_json=os.getenv("LOG_FORMAT", "json").lower() == "json"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(_app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager for TurdParty API.

    Handles startup and shutdown events for the FastAPI application,
    including service initialization and cleanup.

    Args:
        _app: The FastAPI application instance

    Yields:
        None: Control to the application during its lifetime

    Note:
        This function initializes database connections, MinIO client,
        and Celery workers during startup, with graceful error handling
        to allow degraded functionality if services are unavailable.
    """
    logger.info("Starting TurdParty API service...")

    # Initialize services with error handling
    try:
        try:
            await init_database()
            logger.info("Database initialized")
        except Exception as e:
            logger.warning(f"Database initialization failed: {e}")

        try:
            await init_minio()
            logger.info("MinIO client initialized")
        except Exception as e:
            logger.warning(f"MinIO initialization failed: {e}")

        try:
            init_celery()
            logger.info("Celery app initialized")
        except Exception as e:
            logger.warning(f"Celery initialization failed: {e}")

        logger.info("TurdParty API service started successfully")

    except Exception as e:
        logger.error(f"Critical failure during startup: {e}")
        # Don't raise - allow service to start with degraded functionality

    yield

    logger.info("Shutting down TurdParty API service...")


def create_application() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Sets up the complete TurdParty API application with all middleware,
    exception handlers, route includes, and static file mounts.

    Returns:
        FastAPI: Fully configured FastAPI application instance

    Note:
        The application includes:
        - Correlation ID middleware for request tracing
        - CORS middleware for cross-origin requests
        - Global exception handler with structured logging
        - Health and v1 API route includes
        - Static documentation mounting (if available)
    """

    # Check environment configuration
    development_mode = os.getenv("DEVELOPMENT_MODE", "true").lower() == "true"
    traefik_enabled = os.getenv("TRAEFIK_ENABLED", "false").lower() == "true"

    logger.info(f"Creating TurdParty API - Development mode: {development_mode}, Traefik: {traefik_enabled}")

    app = FastAPI(
        title="TurdParty API",
        description="File analysis workflow API - Upload → MinIO → VM Injection → ELK",
        version="1.0.0",
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc"
    )

    # CORS middleware with WebSocket support
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        # Explicitly allow WebSocket headers
        expose_headers=["*"],
    )

    # Add correlation ID middleware with WebSocket support
    app.add_middleware(CorrelationIdMiddleware)
    
    # Exception handler with correlation ID
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
        """
        Global exception handler for unhandled exceptions.

        Logs the exception with correlation ID and request context,
        then returns a standardized error response to the client.

        Args:
            request: The FastAPI request object
            exc: The unhandled exception

        Returns:
            JSONResponse: Standardized error response with correlation ID
        """
        correlation_id = get_correlation_id()
        logger.error(
            f"Global exception: {exc}",
            exc_info=True,
            extra={
                "correlation_id": correlation_id,
                "method": request.method,
                "url": str(request.url),
                "exception_type": type(exc).__name__
            }
        )
        return JSONResponse(
            status_code=500,
            content={
                "detail": "Internal server error",
                "correlation_id": correlation_id
            }
        )
    
    # Add development mode info endpoint
    @app.get("/dev/info")
    async def dev_info():
        """Development mode information"""
        dev_mode = os.getenv("DEVELOPMENT_MODE", "true").lower() == "true"
        traefik_on = os.getenv("TRAEFIK_ENABLED", "false").lower() == "true"
        return {
            "mode": "development" if dev_mode else "production",
            "traefik_enabled": traefik_on,
            "development_mode": dev_mode,
            "websocket_enabled": True,
            "environment": os.getenv("ENVIRONMENT", "local"),
            "message": f"API running in {'development' if dev_mode else 'production'} mode"
        }

    # Include routers
    from .routes import celery_health
    app.include_router(health.router, prefix="/health", tags=["health"])
    app.include_router(celery_health.router, prefix="/api/v1")
    app.include_router(v1_router)

    logger.info("🚀 ABOUT TO REGISTER WEBSOCKET ENDPOINTS - THIS SHOULD APPEAR IN LOGS!")

    # Add WebSocket endpoints AFTER routers are included
    logger.info("Registering WebSocket endpoints...")

    try:
        @app.websocket("/test-ws")
        async def test_websocket(websocket: WebSocket):
            """
            Basic WebSocket test endpoint.

            Accepts a WebSocket connection, sends a greeting message,
            and immediately closes the connection. Used for testing
            WebSocket functionality and connectivity.

            Args:
                websocket: The WebSocket connection object

            Raises:
                Exception: Any WebSocket communication errors are logged
            """
            try:
                await websocket.accept()
                await websocket.send_text("Hello WebSocket!")
                await websocket.close()
            except Exception as e:
                logger.error(f"WebSocket error: {e}")
        logger.info("✅ Registered /test-ws")
    except Exception as e:
        logger.error(f"❌ Failed to register /test-ws: {e}")

    try:
        @app.websocket("/ws/test")
        async def test_websocket_traefik(websocket: WebSocket):
            """
            WebSocket test endpoint for Traefik routing.

            Tests WebSocket connectivity through Traefik reverse proxy.
            Uses the /ws/ path prefix which is typically configured
            for WebSocket routing in Traefik.

            Args:
                websocket: The WebSocket connection object

            Raises:
                Exception: Any WebSocket communication errors are logged
            """
            try:
                await websocket.accept()
                await websocket.send_text("Hello WebSocket via Traefik!")
                await websocket.close()
            except Exception as e:
                logger.error(f"WebSocket error: {e}")
        logger.info("✅ Registered /ws/test")
    except Exception as e:
        logger.error(f"❌ Failed to register /ws/test: {e}")

    try:
        @app.websocket("/minimal-ws")
        async def minimal_websocket(websocket: WebSocket):
            """
            Minimal WebSocket endpoint for basic connectivity testing.

            Provides the simplest possible WebSocket interaction:
            accept connection, send minimal response, close connection.
            Used for basic connectivity and performance testing.

            Args:
                websocket: The WebSocket connection object
            """
            await websocket.accept()
            await websocket.send_text("minimal")
            await websocket.close()
        logger.info("✅ Registered /minimal-ws")
    except Exception as e:
        logger.error(f"❌ Failed to register /minimal-ws: {e}")

    try:
        # Test WebSocket with path parameter at app level
        @app.websocket("/test-with-param/{test_id}")
        async def test_websocket_with_param(websocket: WebSocket, test_id: str):
            """
            WebSocket endpoint with path parameter testing.

            Tests WebSocket functionality with URL path parameters.
            Echoes the provided test_id back to the client to verify
            parameter parsing and WebSocket communication.

            Args:
                websocket: The WebSocket connection object
                test_id: Path parameter for testing parameter handling
            """
            await websocket.accept()
            await websocket.send_text(f"Hello with param: {test_id}")
            await websocket.close()
        logger.info("✅ Registered /test-with-param/{test_id}")
    except Exception as e:
        logger.error(f"❌ Failed to register /test-with-param/{{test_id}}: {e}")

    try:
        # Test WebSocket using ConnectionManager pattern
        @app.websocket("/test-with-manager/{test_id}")
        async def test_websocket_with_manager(websocket: WebSocket, test_id: str):
            """
            WebSocket endpoint using ConnectionManager pattern.

            Tests WebSocket functionality with the ConnectionManager
            pattern used for managing multiple WebSocket connections.
            Demonstrates proper connection lifecycle management.

            Args:
                websocket: The WebSocket connection object
                test_id: Identifier for the WebSocket connection

            Note:
                Uses the ConnectionManager from the VMs router to handle
                connection state and cleanup.
            """
            # Import the ConnectionManager from VMs router
            from .routes.v1.vms import manager
            await manager.connect(websocket, test_id)
            try:
                await websocket.send_text(f"Hello with manager: {test_id}")
            finally:
                manager.disconnect(websocket, test_id)
        logger.info("✅ Registered /test-with-manager/{test_id}")
    except Exception as e:
        logger.error(f"❌ Failed to register /test-with-manager/{{test_id}}: {e}")

    # Log final WebSocket route count
    websocket_count = len([r for r in app.routes if 'websocket' in type(r).__name__.lower()])
    logger.info(f"Total WebSocket routes registered: {websocket_count}")

    # Mount static documentation files
    docs_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "docs", "_build", "html")
    if os.path.exists(docs_path):
        app.mount("/docs", StaticFiles(directory=docs_path, html=True), name="docs")
        logger.info(f"Documentation mounted at /docs from {docs_path}")
    else:
        logger.warning(f"Documentation directory not found: {docs_path}")

    return app


# Create the application instance
app = create_application()


if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("PORT", "8000"))
    host = os.getenv("HOST", "0.0.0.0")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )
