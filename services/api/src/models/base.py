"""
Base Database Model

This module defines the base SQLAlchemy model class that provides common functionality
for all database models in the TurdParty API service. It establishes consistent
patterns for primary keys, timestamps, and table naming conventions.

Key Features:
    - UUID-based primary keys for all models
    - Automatic timestamp tracking (created_at, updated_at)
    - Consistent table naming conventions
    - PostgreSQL-optimized configuration
    - Timezone-aware datetime handling

Base Model Attributes:
    - id: UUID primary key (automatically generated)
    - created_at: Record creation timestamp (UTC)
    - updated_at: Last modification timestamp (UTC, auto-updated)

Table Naming:
    - Automatic table name generation from class name (lowercase)
    - Consistent naming across all models
    - PostgreSQL-compatible naming conventions

Usage:
    All database models should inherit from this Base class:

    class MyModel(Base):
        name: Mapped[str] = mapped_column(String(100))

    This automatically provides:
    - UUID primary key
    - Creation and update timestamps
    - Table name 'mymodel'

PostgreSQL Integration:
    - UUID columns use PostgreSQL native UUID type
    - Timezone-naive datetimes for PostgreSQL compatibility
    - Optimized for PostgreSQL performance and features
"""

import uuid
from datetime import datetime, timezone

from sqlalchemy import DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, declared_attr


def utc_now() -> datetime:
    """
    Get current UTC datetime (timezone-naive for PostgreSQL compatibility).

    Returns timezone-naive datetime in UTC for PostgreSQL compatibility.
    PostgreSQL TIMESTAMP columns work best with timezone-naive datetimes
    when the application handles timezone conversion.

    Returns:
        datetime: Current UTC time as timezone-naive datetime
    """
    return datetime.now(timezone.utc).replace(tzinfo=None)


class Base(DeclarativeBase):
    """
    Base class for all database models in the TurdParty API service.

    Provides common functionality including UUID primary keys, automatic
    timestamps, and consistent table naming. All models should inherit
    from this class to ensure consistency across the database schema.

    Attributes:
        id: UUID primary key (automatically generated)
        created_at: Record creation timestamp (UTC, auto-set)
        updated_at: Last modification timestamp (UTC, auto-updated)
    """

    # Generate __tablename__ automatically
    @declared_attr.directive
    def __tablename__(cls) -> str:
        """
        Generate table name automatically from class name.

        Converts class name to lowercase for PostgreSQL compatibility.
        For example: UserAccount -> useraccount

        Returns:
            str: Lowercase table name derived from class name
        """
        return cls.__name__.lower()

    # Common columns
    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now, onupdate=utc_now, nullable=False)
