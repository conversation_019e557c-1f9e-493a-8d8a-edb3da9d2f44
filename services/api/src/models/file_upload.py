"""
File Upload Database Model

This module defines the SQLAlchemy model for file uploads in the TurdParty system.
It tracks uploaded files through their lifecycle from initial upload to analysis completion,
including storage metadata, processing status, and workflow integration.

The FileUpload model serves as the central record for all files processed by the platform,
providing traceability and status tracking throughout the malware analysis workflow.

Key Features:
    - File metadata tracking (name, size, type, hash)
    - MinIO storage integration with bucket and object key tracking
    - Processing status enumeration for workflow state management
    - Workflow job association for end-to-end traceability
    - Error tracking for failed operations

Database Schema:
    - Primary key: UUID-based unique identifier
    - Indexes: filename, file_hash, status for efficient querying
    - Foreign keys: workflow_job_id for workflow integration
    - Constraints: Non-null requirements for essential metadata
"""

import enum
import uuid
from typing import Optional

from sqlalchemy import String, Integer, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column

from .base import Base


class FileStatus(enum.Enum):
    """File processing status."""
    UPLOADED = "uploaded"
    STORED = "stored"
    PROCESSING = "processing"
    INJECTED = "injected"
    COMPLETED = "completed"
    FAILED = "failed"


class FileUpload(Base):
    """Model for uploaded files."""

    __tablename__ = "file_uploads"

    # File metadata
    filename: Mapped[str] = mapped_column(String(255), nullable=False)
    original_filename: Mapped[str] = mapped_column(String(255), nullable=False)
    content_type: Mapped[str] = mapped_column(String(100), nullable=False)
    file_size: Mapped[int] = mapped_column(Integer, nullable=False)
    file_hash: Mapped[str] = mapped_column(String(64), nullable=False)  # SHA256 hash

    # MinIO storage
    minio_bucket: Mapped[str] = mapped_column(String(100), nullable=False)
    minio_object_key: Mapped[str] = mapped_column(String(255), nullable=False)

    # Processing status
    status: Mapped[FileStatus] = mapped_column(default=FileStatus.UPLOADED, nullable=False)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Workflow tracking
    workflow_job_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), nullable=True)

    def __repr__(self) -> str:
        """
        Return string representation of the FileUpload instance.

        Returns:
            str: Human-readable representation showing key identifying information
        """
        return f"<FileUpload(id={self.id}, filename={self.filename}, status={self.status})>"
