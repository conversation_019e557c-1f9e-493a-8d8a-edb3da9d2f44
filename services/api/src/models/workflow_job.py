"""
Workflow Job Database Model

This module defines the SQLAlchemy model for end-to-end workflow jobs in the TurdParty
malware analysis platform. Workflow jobs orchestrate the complete analysis pipeline
from file upload through VM execution to result collection.

The WorkflowJob model serves as the central coordination point for complex multi-step
analysis workflows, tracking progress, managing resources, and collecting results
across all stages of the malware analysis process.

Key Features:
    - Complete workflow lifecycle tracking
    - Multi-step progress monitoring with percentage completion
    - Resource association (files, VMs, configurations)
    - Celery task coordination and tracking
    - ELK index management for log aggregation
    - Error handling and recovery information
    - JSON-based configuration and result storage

Workflow Stages:
    1. PENDING - Initial job creation
    2. FILE_UPLOADED - File received and validated
    3. FILE_STORED - File stored in MinIO
    4. VM_ALLOCATING - VM resource allocation
    5. VM_CREATING - VM provisioning in progress
    6. VM_RUNNING - VM active and ready
    7. FILE_DOWNLOADING - File transfer to VM
    8. FILE_INJECTING - File injection into VM
    9. VM_EXECUTING - Malware execution and monitoring
    10. MONITORING - Behavioral analysis collection
    11. COMPLETED - Successful workflow completion
    12. TERMINATED - Manual termination
    13. FAILED - Workflow failure

Database Schema:
    - Primary key: UUID-based unique identifier
    - Foreign keys: file_upload_id, vm_instance_id
    - JSON fields: vm_config, injection_config, monitoring_config, results
    - Progress tracking: status, current_step, progress_percentage
    - Task coordination: celery_task_ids array
    - Logging: elk_indices array for log correlation
"""

import enum
import uuid
from typing import Optional, Any, Dict, List

from sqlalchemy import String, Text, JSON, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base


class WorkflowStatus(enum.Enum):
    """
    Workflow job status enumeration.

    Defines all possible states in the malware analysis workflow lifecycle.
    Each status represents a specific stage in the end-to-end analysis process.
    """
    PENDING = "PENDING"
    FILE_UPLOADED = "FILE_UPLOADED"
    FILE_STORED = "FILE_STORED"
    VM_ALLOCATING = "VM_ALLOCATING"
    VM_CREATING = "VM_CREATING"
    VM_RUNNING = "VM_RUNNING"
    FILE_DOWNLOADING = "FILE_DOWNLOADING"
    FILE_INJECTING = "FILE_INJECTING"
    VM_EXECUTING = "VM_EXECUTING"
    MONITORING = "MONITORING"
    COMPLETED = "COMPLETED"
    TERMINATED = "TERMINATED"
    FAILED = "FAILED"


class WorkflowJob(Base):
    """
    Database model for end-to-end workflow jobs.

    Represents a complete malware analysis workflow from file upload
    through execution to result collection. Coordinates multiple services
    and tracks progress through all stages of the analysis pipeline.

    Attributes:
        name: Human-readable workflow name
        description: Optional workflow description
        status: Current workflow status from WorkflowStatus enum
        current_step: Current processing step description
        progress_percentage: Completion percentage (0-100)
        file_upload_id: Associated uploaded file UUID
        vm_instance_id: Associated VM instance UUID
        vm_config: VM configuration parameters (JSON)
        injection_config: File injection configuration (JSON)
        monitoring_config: Monitoring and analysis configuration (JSON)
        results: Workflow results and collected data (JSON)
        error_message: Error details if workflow failed
        elk_indices: List of ELK indices created for this workflow
        celery_task_ids: List of Celery task IDs for tracking
    """

    __tablename__ = "workflow_jobs"

    # Job identification
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Workflow status
    status: Mapped[WorkflowStatus] = mapped_column(default=WorkflowStatus.PENDING, nullable=False)
    current_step: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    progress_percentage: Mapped[str] = mapped_column(String(3), default="0", nullable=False)

    # Associated resources
    file_upload_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), ForeignKey("file_uploads.id"), nullable=True)
    vm_instance_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), ForeignKey("vm_instances.id"), nullable=True)

    # Configuration
    vm_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    injection_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    monitoring_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)

    # Results and logs
    results: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    elk_indices: Mapped[Optional[List[str]]] = mapped_column(JSON, nullable=True)  # List of ELK indices created

    # Celery task tracking
    celery_task_ids: Mapped[Optional[List[Dict[str, Any]]]] = mapped_column(JSON, nullable=True)  # List of Celery task IDs
    
    # Relationships
    file_upload = relationship("FileUpload", foreign_keys=[file_upload_id])
    vm_instance = relationship("VMInstance", foreign_keys=[vm_instance_id])
    
    def __repr__(self) -> str:
        """
        Return string representation of the WorkflowJob instance.

        Returns:
            str: Human-readable representation showing key identifying information
        """
        return f"<WorkflowJob(id={self.id}, name={self.name}, status={self.status})>"
    
    def add_celery_task(self, task_id: str, task_name: str) -> None:
        """
        Add a Celery task ID to the workflow tracking list.

        Maintains a list of all Celery tasks associated with this workflow
        for monitoring, debugging, and cleanup purposes.

        Args:
            task_id: Unique Celery task identifier
            task_name: Human-readable task name for identification
        """
        if not self.celery_task_ids:
            self.celery_task_ids = []
        
        self.celery_task_ids.append({
            "task_id": task_id,
            "task_name": task_name,
            "created_at": str(self.updated_at)
        })
    
    def update_progress(self, step: str, percentage: int, message: str = None) -> None:
        """
        Update workflow progress tracking.

        Updates the current step, progress percentage, and optionally logs
        a progress message with timestamp for audit and monitoring purposes.

        Args:
            step: Current workflow step description
            percentage: Completion percentage (0-100, will be clamped)
            message: Optional progress message for logging
        """
        self.current_step = step
        self.progress_percentage = str(min(100, max(0, percentage)))
        
        if message and self.results:
            if "progress_log" not in self.results:
                self.results["progress_log"] = []
            
            self.results["progress_log"].append({
                "step": step,
                "percentage": percentage,
                "message": message,
                "timestamp": str(self.updated_at)
            })
