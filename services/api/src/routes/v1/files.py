"""File upload and management endpoints."""

import logging
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ...models.file_upload import FileUpload, FileStatus
from ...services.database import get_db
from ...services.minio_client import upload_file, get_file_url, download_file
from ...services.celery_app import get_celery_app

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/upload")
async def upload_file_endpoint(
    file: UploadFile = File(...),
    description: Optional[str] = Form(None),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    Upload a file and store it in MinIO with UUID.
    
    This is the entry point for the workflow:
    File Upload → MinIO Storage → UUID Generation
    """
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        if file.size == 0:
            raise HTTPException(status_code=400, detail="Empty file not allowed")
        
        # Upload to MinIO
        object_key, file_hash, bucket = await upload_file(
            file.file,
            file.filename,
            file.content_type or "application/octet-stream"
        )
        
        # Create database record
        file_upload = FileUpload(
            filename=file.filename,
            original_filename=file.filename,
            content_type=file.content_type or "application/octet-stream",
            file_size=file.size,
            file_hash=file_hash,
            minio_bucket=bucket,
            minio_object_key=object_key,
            status=FileStatus.STORED
        )
        
        db.add(file_upload)
        await db.commit()
        await db.refresh(file_upload)
        
        logger.info(f"File uploaded successfully: {file_upload.id}")
        
        return {
            "file_id": str(file_upload.id),
            "filename": file_upload.filename,
            "file_size": file_upload.file_size,
            "file_hash": file_upload.file_hash,
            "status": file_upload.status.value,
            "minio_object_key": object_key,
            "created_at": file_upload.created_at.isoformat(),
            "message": "File uploaded and stored successfully with UUID"
        }
        
    except Exception as e:
        logger.error(f"File upload failed: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


@router.get("/")
async def list_files(
    skip: int = 0,
    limit: int = 100,
    status: Optional[FileStatus] = None,
    db: AsyncSession = Depends(get_db)
) -> dict:
    """List uploaded files with optional filtering."""
    try:
        query = select(FileUpload)
        
        if status:
            query = query.where(FileUpload.status == status)
        
        query = query.offset(skip).limit(limit).order_by(FileUpload.created_at.desc())
        
        result = await db.execute(query)
        files = result.scalars().all()
        
        return {
            "files": [
                {
                    "file_id": str(f.id),
                    "filename": f.filename,
                    "file_size": f.file_size,
                    "status": f.status.value,
                    "created_at": f.created_at.isoformat(),
                    "workflow_job_id": str(f.workflow_job_id) if f.workflow_job_id else None
                }
                for f in files
            ],
            "total": len(files),
            "skip": skip,
            "limit": limit
        }
        
    except Exception as e:
        logger.error(f"Failed to list files: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve files")


@router.get("/{file_id}")
async def get_file_details(
    file_id: UUID,
    db: AsyncSession = Depends(get_db)
) -> dict:
    """Get detailed information about a specific file."""
    try:
        result = await db.execute(
            select(FileUpload).where(FileUpload.id == file_id)
        )
        file_upload = result.scalar_one_or_none()
        
        if not file_upload:
            raise HTTPException(status_code=404, detail="File not found")
        
        return {
            "file_id": str(file_upload.id),
            "filename": file_upload.filename,
            "original_filename": file_upload.original_filename,
            "content_type": file_upload.content_type,
            "file_size": file_upload.file_size,
            "file_hash": file_upload.file_hash,
            "status": file_upload.status.value,
            "minio_bucket": file_upload.minio_bucket,
            "minio_object_key": file_upload.minio_object_key,
            "workflow_job_id": str(file_upload.workflow_job_id) if file_upload.workflow_job_id else None,
            "error_message": file_upload.error_message,
            "created_at": file_upload.created_at.isoformat(),
            "updated_at": file_upload.updated_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get file details: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve file details")


@router.get("/{file_id}/download-url")
async def get_file_download_url(
    file_id: UUID,
    db: AsyncSession = Depends(get_db)
) -> dict:
    """Get a presigned URL for downloading the file."""
    try:
        result = await db.execute(
            select(FileUpload).where(FileUpload.id == file_id)
        )
        file_upload = result.scalar_one_or_none()
        
        if not file_upload:
            raise HTTPException(status_code=404, detail="File not found")
        
        # Generate presigned URL
        download_url = await get_file_url(
            file_upload.minio_object_key,
            file_upload.minio_bucket
        )
        
        return {
            "file_id": str(file_upload.id),
            "filename": file_upload.filename,
            "download_url": download_url,
            "expires_in": "1 hour"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to generate download URL: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate download URL")
