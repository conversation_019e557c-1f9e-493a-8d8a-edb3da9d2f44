"""UUID Run Tracking API endpoints."""

import logging
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel, Field

from ...models.file_upload import FileUpload
from ...services.database import get_db
import sys
from pathlib import Path

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent.parent))

# Import run tracking utilities
from utils.run_tracker import (
    get_run_tracker, 
    get_file_runs, 
    compare_runs,
    start_file_run,
    complete_file_run
)

logger = logging.getLogger(__name__)
router = APIRouter()


class RunStartRequest(BaseModel):
    """Request to start a new run for a file UUID."""
    file_id: str = Field(..., description="File UUID to start a new run for")
    workflow_id: str = Field(..., description="Workflow ID for this run")
    vm_id: str = Field(..., description="VM ID for this run")


class RunCompleteRequest(BaseModel):
    """Request to complete a run."""
    file_id: str = Field(..., description="File UUID")
    run_number: int = Field(..., description="Run number to complete")
    install_events: int = Field(default=0, description="Number of install events")
    runtime_events: int = Field(default=0, description="Number of runtime events")
    install_uuid: Optional[str] = Field(None, description="Install UUID for correlation")
    runtime_uuids: Optional[List[str]] = Field(None, description="Runtime UUIDs for correlation")


class RunCompareRequest(BaseModel):
    """Request to compare two runs of the same file."""
    file_id: str = Field(..., description="File UUID")
    run1: int = Field(..., description="First run number")
    run2: int = Field(..., description="Second run number")


@router.get("/files/{file_id}/runs")
async def get_file_runs_endpoint(
    file_id: str,
    run_number: Optional[int] = Query(None, description="Specific run number to retrieve"),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get run information for a file UUID.
    
    Returns all runs for a file or a specific run if run_number is provided.
    """
    try:
        # Validate file exists
        result = await db.execute(
            select(FileUpload).where(FileUpload.id == UUID(file_id))
        )
        file_upload = result.scalar_one_or_none()
        
        if not file_upload:
            raise HTTPException(status_code=404, detail="File not found")
        
        # Get run information
        run_info = get_file_runs(file_id, run_number)
        
        if not run_info:
            if run_number:
                raise HTTPException(status_code=404, detail=f"Run {run_number} not found for file {file_id}")
            else:
                # No runs yet, return empty structure
                run_info = {
                    "file_id": file_id,
                    "total_runs": 0,
                    "runs": []
                }
        
        # Add file metadata
        run_info["file_metadata"] = {
            "filename": file_upload.filename,
            "file_size": file_upload.file_size,
            "file_hash": file_upload.file_hash,
            "created_at": file_upload.created_at.isoformat()
        }
        
        return run_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get runs for file {file_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve runs: {str(e)}")


@router.post("/files/{file_id}/runs/start")
async def start_run_endpoint(
    file_id: str,
    request: RunStartRequest,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Start a new run for a file UUID.
    
    Creates a new run tracking entry and returns the run number and ID.
    """
    try:
        # Validate file exists
        result = await db.execute(
            select(FileUpload).where(FileUpload.id == UUID(file_id))
        )
        file_upload = result.scalar_one_or_none()
        
        if not file_upload:
            raise HTTPException(status_code=404, detail="File not found")
        
        # Ensure file_id matches request
        if request.file_id != file_id:
            raise HTTPException(status_code=400, detail="File ID mismatch")
        
        # Start new run
        run_number, run_id = start_file_run(
            request.file_id,
            request.workflow_id,
            request.vm_id
        )
        
        logger.info(f"Started run {run_number} for file {file_id}")
        
        return {
            "file_id": file_id,
            "run_number": run_number,
            "run_id": run_id,
            "workflow_id": request.workflow_id,
            "vm_id": request.vm_id,
            "status": "started",
            "message": f"Run {run_number} started successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start run for file {file_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start run: {str(e)}")


@router.post("/files/{file_id}/runs/complete")
async def complete_run_endpoint(
    file_id: str,
    request: RunCompleteRequest,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Complete a run for a file UUID.
    
    Updates the run with final event counts and completion status.
    """
    try:
        # Validate file exists
        result = await db.execute(
            select(FileUpload).where(FileUpload.id == UUID(file_id))
        )
        file_upload = result.scalar_one_or_none()
        
        if not file_upload:
            raise HTTPException(status_code=404, detail="File not found")
        
        # Ensure file_id matches request
        if request.file_id != file_id:
            raise HTTPException(status_code=400, detail="File ID mismatch")
        
        # Complete the run
        complete_file_run(
            request.file_id,
            request.run_number,
            install_events=request.install_events,
            runtime_events=request.runtime_events,
            install_uuid=request.install_uuid,
            runtime_uuids=request.runtime_uuids or []
        )
        
        logger.info(f"Completed run {request.run_number} for file {file_id}")
        
        return {
            "file_id": file_id,
            "run_number": request.run_number,
            "install_events": request.install_events,
            "runtime_events": request.runtime_events,
            "status": "completed",
            "message": f"Run {request.run_number} completed successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to complete run for file {file_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to complete run: {str(e)}")


@router.post("/files/{file_id}/runs/compare")
async def compare_runs_endpoint(
    file_id: str,
    request: RunCompareRequest,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Compare two runs of the same file UUID.
    
    Returns detailed comparison including event deltas and behavioral analysis.
    """
    try:
        # Validate file exists
        result = await db.execute(
            select(FileUpload).where(FileUpload.id == UUID(file_id))
        )
        file_upload = result.scalar_one_or_none()
        
        if not file_upload:
            raise HTTPException(status_code=404, detail="File not found")
        
        # Ensure file_id matches request
        if request.file_id != file_id:
            raise HTTPException(status_code=400, detail="File ID mismatch")
        
        # Compare runs
        comparison = compare_runs(request.file_id, request.run1, request.run2)
        
        if "error" in comparison:
            raise HTTPException(status_code=404, detail=comparison["error"])
        
        # Add file metadata
        comparison["file_metadata"] = {
            "filename": file_upload.filename,
            "file_size": file_upload.file_size,
            "file_hash": file_upload.file_hash
        }
        
        logger.info(f"Compared runs {request.run1} and {request.run2} for file {file_id}")
        
        return comparison
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to compare runs for file {file_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to compare runs: {str(e)}")


@router.get("/runs/summary")
async def get_runs_summary() -> Dict[str, Any]:
    """
    Get summary of all tracked runs across all files.
    
    Returns overview statistics and recent activity.
    """
    try:
        tracker = get_run_tracker()
        summary = tracker.get_all_files_summary()
        
        return {
            "summary": summary,
            "message": "Run tracking summary retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to get runs summary: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get summary: {str(e)}")


@router.delete("/runs/cleanup")
async def cleanup_old_runs(
    days_old: int = Query(30, description="Delete runs older than this many days")
) -> Dict[str, Any]:
    """
    Clean up old run tracking data.
    
    Removes runs older than the specified number of days.
    """
    try:
        if days_old < 1:
            raise HTTPException(status_code=400, detail="days_old must be at least 1")
        
        tracker = get_run_tracker()
        cleanup_result = tracker.cleanup_old_runs(days_old)
        
        logger.info(f"Cleaned up {cleanup_result['cleaned_files']} old run files")
        
        return {
            "cleaned_files": cleanup_result["cleaned_files"],
            "remaining_files": cleanup_result["remaining_files"],
            "days_old": days_old,
            "message": f"Cleaned up runs older than {days_old} days"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cleanup old runs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to cleanup: {str(e)}")
