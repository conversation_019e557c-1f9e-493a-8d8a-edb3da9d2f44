"""MinIO client service for file storage."""

import os
import uuid
import hashlib
import logging
from typing import Binary<PERSON>, Tuple
from datetime import timed<PERSON><PERSON>

from minio import Minio
from minio.error import S3Error

logger = logging.getLogger(__name__)

# MinIO configuration
MINIO_HOST = os.getenv("MINIO_HOST", "storage")
MINIO_PORT = int(os.getenv("MINIO_PORT", "9000"))
MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY", "minioadmin")
MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY", "minioadmin")
MINIO_SECURE = os.getenv("MINIO_SECURE", "false").lower() == "true"

# Default bucket for file uploads
DEFAULT_BUCKET = "turdparty-files"

# Global MinIO client instance
minio_client: Minio = None


async def init_minio() -> None:
    """Initialize MinIO client and create default bucket."""
    global minio_client
    
    try:
        # Create MinIO client
        minio_client = Minio(
            f"{MINIO_HOST}:{MINIO_PORT}",
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=MINIO_SECURE
        )
        
        # Create default bucket if it doesn't exist
        if not minio_client.bucket_exists(DEFAULT_BUCKET):
            minio_client.make_bucket(DEFAULT_BUCKET)
            logger.info(f"Created MinIO bucket: {DEFAULT_BUCKET}")
        
        logger.info("MinIO client initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize MinIO client: {e}")
        raise


def get_minio_client() -> Minio:
    """Get the MinIO client instance."""
    if minio_client is None:
        raise RuntimeError("MinIO client not initialized")
    return minio_client


async def upload_file(
    file_data: BinaryIO, 
    filename: str, 
    content_type: str = "application/octet-stream",
    bucket: str = DEFAULT_BUCKET
) -> Tuple[str, str, str]:
    """
    Upload a file to MinIO storage.
    
    Args:
        file_data: File data stream
        filename: Original filename
        content_type: MIME type of the file
        bucket: MinIO bucket name
        
    Returns:
        Tuple of (object_key, file_hash, bucket_name)
    """
    client = get_minio_client()
    
    # Generate unique object key
    file_uuid = str(uuid.uuid4())
    file_extension = filename.split('.')[-1] if '.' in filename else ''
    object_key = f"{file_uuid}.{file_extension}" if file_extension else file_uuid
    
    # Read file data and calculate hash
    file_data.seek(0)
    file_content = file_data.read()
    file_hash = hashlib.sha256(file_content).hexdigest()
    
    # Reset file pointer for upload
    file_data.seek(0)
    
    try:
        # Upload to MinIO
        result = client.put_object(
            bucket,
            object_key,
            file_data,
            length=len(file_content),
            content_type=content_type,
            metadata={
                "original-filename": filename,
                "file-hash": file_hash,
                "upload-timestamp": str(uuid.uuid1().time)
            }
        )
        
        logger.info(f"File uploaded to MinIO: {bucket}/{object_key}")
        return object_key, file_hash, bucket
        
    except S3Error as e:
        logger.error(f"MinIO upload failed: {e}")
        raise


async def download_file(object_key: str, bucket: str = DEFAULT_BUCKET) -> bytes:
    """
    Download a file from MinIO storage.
    
    Args:
        object_key: MinIO object key
        bucket: MinIO bucket name
        
    Returns:
        File content as bytes
    """
    client = get_minio_client()
    
    try:
        response = client.get_object(bucket, object_key)
        file_content = response.read()
        response.close()
        response.release_conn()
        
        logger.info(f"File downloaded from MinIO: {bucket}/{object_key}")
        return file_content
        
    except S3Error as e:
        logger.error(f"MinIO download failed: {e}")
        raise


async def get_file_url(
    object_key: str, 
    bucket: str = DEFAULT_BUCKET,
    expires: timedelta = timedelta(hours=1)
) -> str:
    """
    Get a presigned URL for file access.
    
    Args:
        object_key: MinIO object key
        bucket: MinIO bucket name
        expires: URL expiration time
        
    Returns:
        Presigned URL
    """
    client = get_minio_client()
    
    try:
        url = client.presigned_get_object(bucket, object_key, expires=expires)
        logger.info(f"Generated presigned URL for: {bucket}/{object_key}")
        return url
        
    except S3Error as e:
        logger.error(f"Failed to generate presigned URL: {e}")
        raise


async def delete_file(object_key: str, bucket: str = DEFAULT_BUCKET) -> None:
    """
    Delete a file from MinIO storage.
    
    Args:
        object_key: MinIO object key
        bucket: MinIO bucket name
    """
    client = get_minio_client()
    
    try:
        client.remove_object(bucket, object_key)
        logger.info(f"File deleted from MinIO: {bucket}/{object_key}")
        
    except S3Error as e:
        logger.error(f"MinIO delete failed: {e}")
        raise
