"""VM management service for Vagrant and Docker VMs."""

import logging
import subprocess
import json
import os
import tempfile
from typing import Dict, Any, Optional, List
from pathlib import Path
import docker
import yaml

logger = logging.getLogger(__name__)


class VMManager:
    """Manages both Vagrant and Docker VM instances."""
    
    def __init__(self):
        self.docker_client = None
        self.vagrant_base_dir = Path("/tmp/turdparty_vms")
        self.vagrant_base_dir.mkdir(exist_ok=True)
        
    def get_docker_client(self):
        """Get Docker client with lazy initialization."""
        if self.docker_client is None:
            try:
                self.docker_client = docker.from_env()
                # Test connection
                self.docker_client.ping()
            except Exception as e:
                logger.error(f"Failed to connect to Docker: {e}")
                raise
        return self.docker_client
    
    async def create_vagrant_vm(self, vm_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create a Vagrant VM instance."""
        try:
            vm_name = vm_config["name"]
            template = vm_config.get("template", "ubuntu/focal64")
            memory_mb = vm_config.get("memory_mb", 1024)
            cpus = vm_config.get("cpus", 1)
            
            # Create VM directory
            vm_dir = self.vagrant_base_dir / vm_name
            vm_dir.mkdir(exist_ok=True)
            
            # Generate Vagrantfile
            vagrantfile_content = self._generate_vagrantfile(
                template, memory_mb, cpus, vm_name
            )
            
            vagrantfile_path = vm_dir / "Vagrantfile"
            with open(vagrantfile_path, "w") as f:
                f.write(vagrantfile_content)
            
            # Initialize and start Vagrant VM
            result = await self._run_vagrant_command(vm_dir, ["up"])
            
            if result["returncode"] == 0:
                # Get VM info
                vm_info = await self._get_vagrant_vm_info(vm_dir)
                
                return {
                    "success": True,
                    "vm_id": vm_name,
                    "vm_dir": str(vm_dir),
                    "ip_address": vm_info.get("ip_address"),
                    "ssh_port": vm_info.get("ssh_port"),
                    "status": "running",
                    "message": "Vagrant VM created successfully"
                }
            else:
                return {
                    "success": False,
                    "error": result["stderr"],
                    "message": "Failed to create Vagrant VM"
                }
                
        except Exception as e:
            logger.error(f"Failed to create Vagrant VM: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Vagrant VM creation failed"
            }
    
    async def create_docker_vm(self, vm_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create a Docker container VM instance."""
        try:
            client = self.get_docker_client()
            
            vm_name = vm_config["name"]
            template = vm_config.get("template", "ubuntu:20.04")
            memory_mb = vm_config.get("memory_mb", 1024)
            cpus = vm_config.get("cpus", 1)
            
            # Map template to Docker image
            docker_image = self._map_template_to_docker_image(template)
            
            # Create container configuration
            container_config = {
                "image": docker_image,
                "name": f"turdparty_vm_{vm_name}",
                "detach": True,
                "tty": True,
                "stdin_open": True,
                "mem_limit": f"{memory_mb}m",
                "cpu_count": cpus,
                "network_mode": "turdpartycollab_net",
                "labels": {
                    "turdparty.vm": "true",
                    "turdparty.vm.name": vm_name,
                    "turdparty.vm.template": template
                },
                "environment": {
                    "TURDPARTY_VM": "true",
                    "TURDPARTY_VM_NAME": vm_name
                },
                "volumes": {
                    "/tmp": {"bind": "/tmp", "mode": "rw"}
                },
                "command": "/bin/bash -c 'while true; do sleep 30; done'"
            }
            
            # Pull image if needed
            try:
                client.images.get(docker_image)
            except docker.errors.ImageNotFound:
                logger.info(f"Pulling Docker image: {docker_image}")
                client.images.pull(docker_image)
            
            # Create and start container
            container = client.containers.run(**container_config)
            
            # Get container info
            container.reload()
            
            return {
                "success": True,
                "vm_id": container.id,
                "container_name": container.name,
                "ip_address": self._get_container_ip(container),
                "status": container.status,
                "image": docker_image,
                "message": "Docker VM created successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to create Docker VM: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Docker VM creation failed"
            }
    
    async def start_vm(self, vm_id: str, vm_type: str) -> Dict[str, Any]:
        """Start a VM instance."""
        try:
            if vm_type == "vagrant":
                return await self._start_vagrant_vm(vm_id)
            elif vm_type == "docker":
                return await self._start_docker_vm(vm_id)
            else:
                return {
                    "success": False,
                    "error": f"Unknown VM type: {vm_type}",
                    "message": "VM start failed"
                }
        except Exception as e:
            logger.error(f"Failed to start VM {vm_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "VM start failed"
            }
    
    async def stop_vm(self, vm_id: str, vm_type: str, force: bool = False) -> Dict[str, Any]:
        """Stop a VM instance."""
        try:
            if vm_type == "vagrant":
                return await self._stop_vagrant_vm(vm_id, force)
            elif vm_type == "docker":
                return await self._stop_docker_vm(vm_id, force)
            else:
                return {
                    "success": False,
                    "error": f"Unknown VM type: {vm_type}",
                    "message": "VM stop failed"
                }
        except Exception as e:
            logger.error(f"Failed to stop VM {vm_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "VM stop failed"
            }
    
    async def delete_vm(self, vm_id: str, vm_type: str, force: bool = False) -> Dict[str, Any]:
        """Delete a VM instance and clean up resources."""
        try:
            if vm_type == "vagrant":
                return await self._delete_vagrant_vm(vm_id, force)
            elif vm_type == "docker":
                return await self._delete_docker_vm(vm_id, force)
            else:
                return {
                    "success": False,
                    "error": f"Unknown VM type: {vm_type}",
                    "message": "VM deletion failed"
                }
        except Exception as e:
            logger.error(f"Failed to delete VM {vm_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "VM deletion failed"
            }
    
    def _generate_vagrantfile(self, template: str, memory_mb: int, cpus: int, vm_name: str) -> str:
        """Generate Vagrantfile content."""
        return f'''# -*- mode: ruby -*-
# vi: set ft=ruby :

Vagrant.configure("2") do |config|
  config.vm.box = "{template}"
  config.vm.hostname = "{vm_name}"
  
  # Network configuration
  config.vm.network "private_network", type: "dhcp"
  config.vm.network "forwarded_port", guest: 22, host_auto_correct: true
  
  # Provider configuration
  config.vm.provider "virtualbox" do |vb|
    vb.memory = {memory_mb}
    vb.cpus = {cpus}
    vb.name = "turdparty_{vm_name}"
  end
  
  # Provisioning
  config.vm.provision "shell", inline: <<-SHELL
    # Update system
    apt-get update
    
    # Install basic tools
    apt-get install -y curl wget htop
    
    # Create turdparty directory
    mkdir -p /opt/turdparty
    chmod 755 /opt/turdparty
    
    # Set up environment
    echo "export TURDPARTY_VM=true" >> /etc/environment
    echo "export TURDPARTY_VM_NAME={vm_name}" >> /etc/environment
    
    echo "TurdParty VM setup completed"
  SHELL
end
'''
    
    def _map_template_to_docker_image(self, template: str) -> str:
        """Map Vagrant template to Docker image."""
        template_map = {
            "ubuntu/focal64": "ubuntu:20.04",
            "ubuntu/jammy64": "ubuntu:22.04",
            "ubuntu/bionic64": "ubuntu:18.04",
            "centos/7": "centos:7",
            "centos/8": "centos:8",
            "debian/bullseye64": "debian:bullseye",
            "alpine/alpine64": "alpine:latest"
        }
        
        return template_map.get(template, template)
    
    def _get_container_ip(self, container) -> Optional[str]:
        """Get container IP address."""
        try:
            networks = container.attrs["NetworkSettings"]["Networks"]
            for network_name, network_info in networks.items():
                if network_info.get("IPAddress"):
                    return network_info["IPAddress"]
            return None
        except Exception:
            return None
    
    async def _run_vagrant_command(self, vm_dir: Path, command: List[str]) -> Dict[str, Any]:
        """Run a Vagrant command in the VM directory."""
        try:
            full_command = ["vagrant"] + command
            result = subprocess.run(
                full_command,
                cwd=vm_dir,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            return {
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
        except subprocess.TimeoutExpired:
            return {
                "returncode": -1,
                "stdout": "",
                "stderr": "Command timed out"
            }
        except Exception as e:
            return {
                "returncode": -1,
                "stdout": "",
                "stderr": str(e)
            }
    
    async def _get_vagrant_vm_info(self, vm_dir: Path) -> Dict[str, Any]:
        """Get Vagrant VM information."""
        try:
            # Get SSH config
            result = await self._run_vagrant_command(vm_dir, ["ssh-config"])
            
            if result["returncode"] == 0:
                ssh_config = result["stdout"]
                
                # Parse SSH config for IP and port
                ip_address = None
                ssh_port = None
                
                for line in ssh_config.split("\n"):
                    line = line.strip()
                    if line.startswith("HostName"):
                        ip_address = line.split()[1]
                    elif line.startswith("Port"):
                        ssh_port = int(line.split()[1])
                
                return {
                    "ip_address": ip_address,
                    "ssh_port": ssh_port
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"Failed to get Vagrant VM info: {e}")
            return {}
    
    async def _start_vagrant_vm(self, vm_name: str) -> Dict[str, Any]:
        """Start a Vagrant VM."""
        vm_dir = self.vagrant_base_dir / vm_name
        result = await self._run_vagrant_command(vm_dir, ["up"])
        
        return {
            "success": result["returncode"] == 0,
            "message": "Vagrant VM started" if result["returncode"] == 0 else "Failed to start Vagrant VM",
            "error": result["stderr"] if result["returncode"] != 0 else None
        }
    
    async def _stop_vagrant_vm(self, vm_name: str, force: bool = False) -> Dict[str, Any]:
        """Stop a Vagrant VM."""
        vm_dir = self.vagrant_base_dir / vm_name
        command = ["halt"]
        if force:
            command.append("--force")
        
        result = await self._run_vagrant_command(vm_dir, command)
        
        return {
            "success": result["returncode"] == 0,
            "message": "Vagrant VM stopped" if result["returncode"] == 0 else "Failed to stop Vagrant VM",
            "error": result["stderr"] if result["returncode"] != 0 else None
        }
    
    async def _delete_vagrant_vm(self, vm_name: str, force: bool = False) -> Dict[str, Any]:
        """Delete a Vagrant VM."""
        vm_dir = self.vagrant_base_dir / vm_name
        command = ["destroy"]
        if force:
            command.append("--force")
        
        result = await self._run_vagrant_command(vm_dir, command)
        
        # Clean up VM directory
        if result["returncode"] == 0:
            try:
                import shutil
                shutil.rmtree(vm_dir)
            except Exception as e:
                logger.warning(f"Failed to clean up VM directory {vm_dir}: {e}")
        
        return {
            "success": result["returncode"] == 0,
            "message": "Vagrant VM deleted" if result["returncode"] == 0 else "Failed to delete Vagrant VM",
            "error": result["stderr"] if result["returncode"] != 0 else None
        }
    
    async def _start_docker_vm(self, container_id: str) -> Dict[str, Any]:
        """Start a Docker container VM."""
        try:
            client = self.get_docker_client()
            container = client.containers.get(container_id)
            container.start()
            
            return {
                "success": True,
                "message": "Docker VM started"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to start Docker VM"
            }
    
    async def _stop_docker_vm(self, container_id: str, force: bool = False) -> Dict[str, Any]:
        """Stop a Docker container VM."""
        try:
            client = self.get_docker_client()
            container = client.containers.get(container_id)
            
            if force:
                container.kill()
            else:
                container.stop()
            
            return {
                "success": True,
                "message": "Docker VM stopped"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to stop Docker VM"
            }
    
    async def _delete_docker_vm(self, container_id: str, force: bool = False) -> Dict[str, Any]:
        """Delete a Docker container VM."""
        try:
            client = self.get_docker_client()
            container = client.containers.get(container_id)
            
            # Stop container if running
            if container.status == "running":
                if force:
                    container.kill()
                else:
                    container.stop()
            
            # Remove container
            container.remove()
            
            return {
                "success": True,
                "message": "Docker VM deleted"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to delete Docker VM"
            }
