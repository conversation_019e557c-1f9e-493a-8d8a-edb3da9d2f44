#!/usr/bin/env python3
"""
Quick test to verify API structure and v1 versioning.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_api_structure():
    """Test that the API structure is correct."""
    try:
        from src.main import app
        
        # Get all routes
        routes = []
        for route in app.routes:
            if hasattr(route, 'path'):
                routes.append(route.path)
        
        print("✅ API Routes discovered:")
        for route in sorted(routes):
            print(f"   {route}")
        
        # Check for v1 versioning
        v1_routes = [r for r in routes if '/api/v1/' in r]
        health_routes = [r for r in routes if '/health' in r]
        
        print(f"\n✅ V1 API routes: {len(v1_routes)}")
        print(f"✅ Health routes: {len(health_routes)}")
        
        # Expected routes
        expected_v1_routes = [
            '/api/v1/files/',
            '/api/v1/files/upload',
            '/api/v1/workflow/',
            '/api/v1/workflow/start'
        ]
        
        print(f"\n✅ API structure verification complete!")
        print(f"   - Health endpoint: /health/")
        print(f"   - V1 API prefix: /api/v1/")
        print(f"   - Files endpoints: /api/v1/files/*")
        print(f"   - Workflow endpoints: /api/v1/workflow/*")
        
        return True
        
    except Exception as e:
        print(f"❌ API structure test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_api_structure()
    sys.exit(0 if success else 1)
