/* Analysis Report Styles for TurdParty */

.analysis-report {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.analysis-report.loading {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  font-size: 48px;
  animation: spin 2s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.analysis-report.error {
  text-align: center;
  padding: 60px 20px;
  color: #dc3545;
}

/* Report Header */
.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  flex-wrap: wrap;
  gap: 20px;
}

.header-main h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 700;
}

.vm-info {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.vm-id {
  font-family: 'Monaco', 'Menlo', monospace;
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.running {
  background: #28a745;
  color: white;
}

.status-badge.completed {
  background: #17a2b8;
  color: white;
}

.status-badge.failed {
  background: #dc3545;
  color: white;
}

.status-badge.creating {
  background: #ffc107;
  color: #212529;
}

/* Threat Assessment */
.threat-assessment {
  display: flex;
  align-items: center;
}

.threat-level {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.15);
  padding: 15px 20px;
  border-radius: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.threat-icon {
  font-size: 24px;
}

.threat-details {
  text-align: left;
}

.threat-label {
  font-size: 12px;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.threat-value {
  font-size: 18px;
  font-weight: 700;
  margin-top: 2px;
}

/* Report Sections */
.report-section {
  margin-bottom: 30px;
  padding: 25px;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #667eea;
}

.report-section h2 {
  margin: 0 0 20px 0;
  color: #495057;
  font-size: 22px;
  font-weight: 600;
}

/* Summary Grid */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.summary-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.summary-card h3 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 8px;
}

.summary-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.summary-card li {
  padding: 6px 0;
  border-bottom: 1px solid #f8f9fa;
  font-size: 14px;
}

.summary-card li:last-child {
  border-bottom: none;
}

.summary-card strong {
  color: #495057;
  font-weight: 600;
}

/* Events Grid */
.events-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

@media (max-width: 768px) {
  .events-grid {
    grid-template-columns: 1fr;
  }
}

.events-chart {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.events-chart h3 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.event-list {
  max-height: 300px;
  overflow-y: auto;
}

.event-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f8f9fa;
  font-size: 14px;
}

.event-item:last-child {
  border-bottom: none;
}

.event-name {
  color: #495057;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 13px;
}

.event-count {
  background: #e9ecef;
  color: #495057;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* Timeline */
.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #dee2e6;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.timeline-marker {
  position: absolute;
  left: -22px;
  width: 30px;
  height: 30px;
  background: white;
  border: 2px solid #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  z-index: 1;
}

.timeline-content {
  background: white;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  font-size: 14px;
  flex: 1;
}

/* Recommendations */
.recommendations {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recommendation {
  padding: 15px 20px;
  border-radius: 8px;
  border-left: 4px solid;
  font-size: 14px;
  line-height: 1.5;
}

.recommendation.high {
  background: #f8d7da;
  border-color: #dc3545;
  color: #721c24;
}

.recommendation.medium {
  background: #fff3cd;
  border-color: #fd7e14;
  color: #856404;
}

.recommendation.low {
  background: #d4edda;
  border-color: #28a745;
  color: #155724;
}

.recommendation.general {
  background: #d1ecf1;
  border-color: #17a2b8;
  color: #0c5460;
}

/* Report Footer */
.report-footer {
  margin-top: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-top: 3px solid #667eea;
  text-align: center;
}

.footer-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
  font-family: 'Monaco', 'Menlo', monospace;
}

.footer-info strong {
  color: #495057;
}

/* Responsive Design */
@media (max-width: 768px) {
  .analysis-report {
    padding: 15px;
  }
  
  .report-header {
    flex-direction: column;
    text-align: center;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
  }
  
  .threat-assessment {
    width: 100%;
    justify-content: center;
  }
  
  .vm-info {
    justify-content: center;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .analysis-report {
    background: #1a202c;
    color: #e2e8f0;
  }
  
  .report-section {
    background: #2d3748;
  }
  
  .summary-card, .events-chart, .timeline-content {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  
  .report-footer {
    background: #2d3748;
    border-color: #4a5568;
  }
}
