/**
 * Analysis Report Component with Evidence Box Integration
 * Displays comprehensive malware analysis results with verification data
 */

import React, { useState, useEffect } from 'react';
import EvidenceBox from './EvidenceBox';
import './AnalysisReport.css';

const AnalysisReport = ({ vmId, reportData }) => {
  const [reportDetails, setReportDetails] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (vmId) {
      fetchReportDetails();
    }
  }, [vmId]);

  const fetchReportDetails = async () => {
    try {
      setLoading(true);
      
      // Fetch VM details
      const vmResponse = await fetch(`/api/v1/vms/${vmId}`);
      const vmData = await vmResponse.json();

      // Fetch ECS events summary
      const ecsResponse = await fetch(`/api/v1/ecs/events/${vmId}/summary`);
      const ecsData = await ecsResponse.json();

      setReportDetails({
        vm: vmData,
        ecs: ecsData,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to fetch report details:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (startTime, endTime) => {
    if (!startTime || !endTime) return 'Unknown';
    const duration = new Date(endTime) - new Date(startTime);
    return `${Math.round(duration / 1000)}s`;
  };

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'running': return '✅';
      case 'completed': return '🎯';
      case 'failed': return '❌';
      case 'creating': return '⏳';
      default: return '❓';
    }
  };

  const getThreatLevel = (eventsCount) => {
    if (eventsCount > 50) return { level: 'HIGH', color: '#dc3545', icon: '🔴' };
    if (eventsCount > 20) return { level: 'MEDIUM', color: '#fd7e14', icon: '🟡' };
    if (eventsCount > 0) return { level: 'LOW', color: '#28a745', icon: '🟢' };
    return { level: 'NONE', color: '#6c757d', icon: '⚪' };
  };

  if (loading) {
    return (
      <div className="analysis-report loading">
        <div className="loading-spinner">🔄</div>
        <p>Loading analysis report...</p>
      </div>
    );
  }

  if (!reportDetails) {
    return (
      <div className="analysis-report error">
        <h2>❌ Report Not Available</h2>
        <p>Unable to load analysis report for VM {vmId}</p>
      </div>
    );
  }

  const { vm, ecs } = reportDetails;
  const threat = getThreatLevel(ecs.total_events);

  return (
    <div className="analysis-report">
      {/* Report Header */}
      <div className="report-header">
        <div className="header-main">
          <h1>💩🎉 TurdParty Analysis Report 🎉💩</h1>
          <div className="vm-info">
            <span className="vm-id">VM: {vmId.slice(0, 8)}...</span>
            <span className={`status-badge ${vm.status}`}>
              {getStatusIcon(vm.status)} {vm.status?.toUpperCase()}
            </span>
          </div>
        </div>
        
        <div className="threat-assessment">
          <div className="threat-level" style={{ borderColor: threat.color }}>
            <span className="threat-icon">{threat.icon}</span>
            <div className="threat-details">
              <div className="threat-label">Threat Level</div>
              <div className="threat-value" style={{ color: threat.color }}>
                {threat.level}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Executive Summary */}
      <div className="report-section">
        <h2>📊 Executive Summary</h2>
        <div className="summary-grid">
          <div className="summary-card">
            <h3>🖥️ VM Environment</h3>
            <ul>
              <li><strong>Name:</strong> {vm.name}</li>
              <li><strong>Template:</strong> {vm.template}</li>
              <li><strong>IP Address:</strong> {vm.ip_address || 'N/A'}</li>
              <li><strong>Runtime:</strong> {vm.runtime_minutes?.toFixed(1) || 0} minutes</li>
            </ul>
          </div>

          <div className="summary-card">
            <h3>📁 File Analysis</h3>
            <ul>
              <li><strong>Injection Path:</strong> {vm.injection_path || 'N/A'}</li>
              <li><strong>Injection Status:</strong> {vm.injection_completed ? '✅ Completed' : '❌ Pending'}</li>
              <li><strong>Monitoring:</strong> {vm.monitoring_active ? '🔍 Active' : '⏸️ Inactive'}</li>
            </ul>
          </div>

          <div className="summary-card">
            <h3>📈 Event Analysis</h3>
            <ul>
              <li><strong>Total Events:</strong> {ecs.total_events}</li>
              <li><strong>Event Types:</strong> {ecs.event_types?.length || 0}</li>
              <li><strong>Categories:</strong> {ecs.event_categories?.length || 0}</li>
              <li><strong>ELK Index:</strong> {vm.elk_index || 'N/A'}</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Event Breakdown */}
      {ecs.event_types && ecs.event_types.length > 0 && (
        <div className="report-section">
          <h2>🔍 Event Breakdown</h2>
          <div className="events-grid">
            <div className="events-chart">
              <h3>Event Types</h3>
              <div className="event-list">
                {ecs.event_types.slice(0, 10).map((event, index) => (
                  <div key={index} className="event-item">
                    <span className="event-name">{event.key}</span>
                    <span className="event-count">{event.doc_count}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="events-chart">
              <h3>Event Categories</h3>
              <div className="event-list">
                {ecs.event_categories.slice(0, 10).map((category, index) => (
                  <div key={index} className="event-item">
                    <span className="event-name">{category.key}</span>
                    <span className="event-count">{category.doc_count}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Timeline */}
      <div className="report-section">
        <h2>⏱️ Analysis Timeline</h2>
        <div className="timeline">
          <div className="timeline-item">
            <div className="timeline-marker">🚀</div>
            <div className="timeline-content">
              <strong>VM Created:</strong> {new Date(vm.created_at).toLocaleString()}
            </div>
          </div>
          
          {vm.started_at && (
            <div className="timeline-item">
              <div className="timeline-marker">▶️</div>
              <div className="timeline-content">
                <strong>VM Started:</strong> {new Date(vm.started_at).toLocaleString()}
              </div>
            </div>
          )}
          
          {vm.injection_completed && (
            <div className="timeline-item">
              <div className="timeline-marker">💉</div>
              <div className="timeline-content">
                <strong>File Injected:</strong> Analysis completed successfully
              </div>
            </div>
          )}
          
          {vm.scheduled_termination && (
            <div className="timeline-item">
              <div className="timeline-marker">⏰</div>
              <div className="timeline-content">
                <strong>Scheduled Termination:</strong> {new Date(vm.scheduled_termination).toLocaleString()}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Evidence Box */}
      <EvidenceBox
        vmId={vmId}
        fileId={vm.injected_file_id}
        taskId={vm.workflow_job_id}
        injectionPath={vm.injection_path}
        ecsIndex={`turdparty-vm-ecs-${new Date().toISOString().slice(0, 10)}`}
        eventsCount={ecs.total_events}
        timestamp={reportDetails.timestamp}
      />

      {/* Recommendations */}
      <div className="report-section">
        <h2>💡 Recommendations</h2>
        <div className="recommendations">
          {threat.level === 'HIGH' && (
            <div className="recommendation high">
              <strong>🔴 High Activity Detected:</strong> This file generated significant system activity. 
              Review the ECS events for potential malicious behavior patterns.
            </div>
          )}
          
          {threat.level === 'MEDIUM' && (
            <div className="recommendation medium">
              <strong>🟡 Moderate Activity:</strong> File shows normal to elevated activity levels. 
              Monitor for any suspicious network connections or file modifications.
            </div>
          )}
          
          {threat.level === 'LOW' && (
            <div className="recommendation low">
              <strong>🟢 Low Activity:</strong> File appears to have minimal system impact. 
              Continue monitoring for any delayed or triggered behaviors.
            </div>
          )}
          
          <div className="recommendation general">
            <strong>📋 General:</strong> Always verify results with multiple analysis tools and 
            cross-reference with threat intelligence databases.
          </div>
        </div>
      </div>

      {/* Report Footer */}
      <div className="report-footer">
        <div className="footer-info">
          <p>
            <strong>Generated:</strong> {new Date(reportDetails.timestamp).toLocaleString()} | 
            <strong> Platform:</strong> 💩🎉TurdParty🎉💩 v1.0 | 
            <strong> VM ID:</strong> {vmId}
          </p>
        </div>
      </div>
    </div>
  );
};

export default AnalysisReport;
