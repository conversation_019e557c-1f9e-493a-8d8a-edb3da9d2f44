/**
 * Evidence Box Component for TurdParty Malware Analysis Reports
 * Displays verification queries, database records, and ECS event links
 */

import React, { useState, useEffect } from 'react';
import './EvidenceBox.css';

const EvidenceBox = ({ 
  vmId, 
  fileId, 
  taskId, 
  injectionPath, 
  ecsIndex, 
  eventsCount,
  timestamp 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [evidenceData, setEvidenceData] = useState(null);
  const [loading, setLoading] = useState(false);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    if (!isExpanded && !evidenceData) {
      fetchEvidenceData();
    }
  };

  const fetchEvidenceData = async () => {
    setLoading(true);
    try {
      // Fetch VM data
      const vmResponse = await fetch(`/api/v1/vms/${vmId}`);
      const vmData = await vmResponse.json();

      // Fetch ECS events
      const ecsResponse = await fetch(`/api/v1/ecs/events/${vmId}`);
      const ecsData = await ecsResponse.json();

      setEvidenceData({ vm: vmData, ecs: ecsData });
    } catch (error) {
      console.error('Failed to fetch evidence data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTimestamp = (ts) => {
    return new Date(ts).toISOString().replace('T', ' ').slice(0, 19) + ' UTC';
  };

  const getElasticsearchUrl = (index, query = '') => {
    const baseUrl = 'http://elasticsearch.turdparty.localhost:9200';
    if (query) {
      return `${baseUrl}/${index}/_search?q=${encodeURIComponent(query)}&size=100&sort=@timestamp:desc`;
    }
    return `${baseUrl}/${index}/_search?size=100&sort=@timestamp:desc`;
  };

  const getKibanaUrl = (index, vmId) => {
    const kibanaUrl = 'http://kibana.turdparty.localhost:5601';
    const timeFrom = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
    const timeTo = new Date().toISOString();
    
    return `${kibanaUrl}/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:'${timeFrom}',to:'${timeTo}'))&_a=(columns:!(_source),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'${index}',key:host.name,negate:!f,params:(query:'${vmId}'),type:phrase),query:(match_phrase:(host.name:'${vmId}')))),index:'${index}',interval:auto,query:(language:kuery,query:''),sort:!(!('@timestamp',desc)))`;
  };

  return (
    <div className="evidence-box">
      <div className="evidence-header" onClick={toggleExpanded}>
        <h3>🔍 Evidence & Verification</h3>
        <div className="evidence-summary">
          <span className="evidence-badge">VM: {vmId?.slice(0, 8)}...</span>
          <span className="evidence-badge">Events: {eventsCount}</span>
          <span className="evidence-badge">Index: {ecsIndex}</span>
        </div>
        <span className={`expand-icon ${isExpanded ? 'expanded' : ''}`}>▼</span>
      </div>

      {isExpanded && (
        <div className="evidence-content">
          {loading && <div className="loading">Loading evidence data...</div>}
          
          <div className="evidence-section">
            <h4>📋 Database Verification Queries</h4>
            <div className="query-block">
              <h5>VM Instance Status</h5>
              <pre className="sql-query">
{`-- PostgreSQL Query
SELECT 
  id, name, status, injection_completed, 
  injection_path, elk_index, ip_address,
  started_at, created_at
FROM vm_instances 
WHERE id = '${vmId}';`}
              </pre>
              {evidenceData?.vm && (
                <div className="query-result">
                  <strong>Result:</strong>
                  <pre>{JSON.stringify(evidenceData.vm, null, 2)}</pre>
                </div>
              )}
            </div>

            <div className="query-block">
              <h5>File Processing History</h5>
              <pre className="sql-query">
{`-- File Upload & Processing
SELECT 
  id, original_filename, file_size, 
  blake3_hash, sha256_hash, md5_hash,
  upload_timestamp, processing_status
FROM uploaded_files 
WHERE id = '${fileId}';`}
              </pre>
            </div>

            <div className="query-block">
              <h5>Task Execution Log</h5>
              <pre className="sql-query">
{`-- Celery Task Verification
SELECT 
  task_id, task_name, status, 
  result, traceback, timestamp
FROM celery_taskmeta 
WHERE task_id = '${taskId}';`}
              </pre>
            </div>
          </div>

          <div className="evidence-section">
            <h4>🔗 Elasticsearch & ECS Data Links</h4>
            
            <div className="ecs-links">
              <div className="link-group">
                <h5>Direct Elasticsearch Queries</h5>
                <a 
                  href={getElasticsearchUrl(ecsIndex, `host.name:${vmId}`)}
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="ecs-link"
                >
                  📊 All Events for VM {vmId?.slice(0, 8)}...
                </a>
                
                <a 
                  href={getElasticsearchUrl(ecsIndex, 'event.category:file')}
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="ecs-link"
                >
                  📁 File System Events
                </a>
                
                <a 
                  href={getElasticsearchUrl(ecsIndex, 'event.action:file_create')}
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="ecs-link"
                >
                  ➕ File Creation Events
                </a>
                
                <a 
                  href={getElasticsearchUrl(ecsIndex, 'event.action:file_modify')}
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="ecs-link"
                >
                  ✏️ File Modification Events
                </a>
              </div>

              <div className="link-group">
                <h5>Kibana Dashboard</h5>
                <a 
                  href={getKibanaUrl(ecsIndex, vmId)}
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="kibana-link"
                >
                  📈 Open in Kibana Discover
                </a>
              </div>
            </div>
          </div>

          <div className="evidence-section">
            <h4>🧪 Manual Verification Commands</h4>
            <div className="command-block">
              <h5>Docker Container Logs</h5>
              <pre className="bash-command">
{`# Check worker logs for this task
docker logs turdpartycollab_worker_vm | grep "${taskId}"

# Check API logs for injection request
docker logs turdpartycollab_api | grep "${vmId}"

# Check database connection
docker exec -it turdpartycollab_database psql -U postgres -d turdparty \\
  -c "SELECT * FROM vm_instances WHERE id = '${vmId}';"
`}
              </pre>
            </div>

            <div className="command-block">
              <h5>Elasticsearch Verification</h5>
              <pre className="bash-command">
{`# Check index status
curl "http://localhost:9200/_cat/indices/${ecsIndex}?v"

# Count events for this VM
curl "http://localhost:9200/${ecsIndex}/_count" \\
  -H "Content-Type: application/json" \\
  -d '{"query": {"term": {"host.name": "${vmId}"}}}'

# Get latest events
curl "http://localhost:9200/${ecsIndex}/_search" \\
  -H "Content-Type: application/json" \\
  -d '{"query": {"term": {"host.name": "${vmId}"}}, "sort": [{"@timestamp": "desc"}], "size": 10}'
`}
              </pre>
            </div>
          </div>

          <div className="evidence-section">
            <h4>📊 ECS Event Sample</h4>
            {evidenceData?.ecs?.events && evidenceData.ecs.events.length > 0 && (
              <div className="ecs-sample">
                <pre>{JSON.stringify(evidenceData.ecs.events[0], null, 2)}</pre>
              </div>
            )}
          </div>

          <div className="evidence-footer">
            <small>
              Generated: {formatTimestamp(timestamp || new Date().toISOString())} | 
              VM: {vmId} | 
              Task: {taskId} | 
              Index: {ecsIndex}
            </small>
          </div>
        </div>
      )}
    </div>
  );
};

export default EvidenceBox;
