syntax = "proto3";

package turdparty.vm;

// VM Management Service for TurdParty
service VMManagement {
    // VM Lifecycle Operations
    rpc CreateVM(CreateVMRequest) returns (CreateVMResponse);
    rpc GetVMStatus(GetVMStatusRequest) returns (GetVMStatusResponse);
    rpc StartVM(StartVMRequest) returns (StartVMResponse);
    rpc StopVM(StopVMRequest) returns (StopVMResponse);
    rpc DestroyVM(DestroyVMRequest) returns (DestroyVMResponse);
    
    // File Operations
    rpc InjectFile(InjectFileRequest) returns (InjectFileResponse);
    rpc ExecuteCommand(ExecuteCommandRequest) returns (stream ExecuteCommandResponse);
    
    // Monitoring
    rpc GetVMMetrics(GetVMMetricsRequest) returns (GetVMMetricsResponse);
    rpc StreamVMMetrics(StreamVMMetricsRequest) returns (stream VMMetricsUpdate);
}

// VM Creation Request
message CreateVMRequest {
    string name = 1;
    string template = 2;  // e.g., "10Baht/windows10-turdparty"
    int32 memory_mb = 3;
    int32 cpus = 4;
    int32 disk_gb = 5;
    string description = 6;
    map<string, string> metadata = 7;
}

message CreateVMResponse {
    bool success = 1;
    string vm_id = 2;
    string message = 3;
    string error = 4;
    VMInfo vm_info = 5;
}

// VM Status Request
message GetVMStatusRequest {
    string vm_id = 1;
}

message GetVMStatusResponse {
    bool success = 1;
    VMInfo vm_info = 2;
    string error = 3;
}

// VM Control Requests
message StartVMRequest {
    string vm_id = 1;
}

message StartVMResponse {
    bool success = 1;
    string message = 2;
    string error = 3;
}

message StopVMRequest {
    string vm_id = 1;
    bool force = 2;
}

message StopVMResponse {
    bool success = 1;
    string message = 2;
    string error = 3;
}

message DestroyVMRequest {
    string vm_id = 1;
    bool force = 2;
}

message DestroyVMResponse {
    bool success = 1;
    string message = 2;
    string error = 3;
}

// File Injection Request
message InjectFileRequest {
    string vm_id = 1;
    string file_path = 2;  // Path on host/container
    string target_path = 3;  // Path in VM
    string permissions = 4;  // e.g., "0755"
    bool execute_after_injection = 5;
    map<string, string> metadata = 6;
}

message InjectFileResponse {
    bool success = 1;
    string injection_id = 2;
    string message = 3;
    string error = 4;
    InjectionInfo injection_info = 5;
}

// Command Execution
message ExecuteCommandRequest {
    string vm_id = 1;
    string command = 2;
    repeated string args = 3;
    string working_directory = 4;
    map<string, string> environment = 5;
    int32 timeout_seconds = 6;
}

message ExecuteCommandResponse {
    string output = 1;
    string error = 2;
    int32 exit_code = 3;
    bool finished = 4;
}

// VM Metrics
message GetVMMetricsRequest {
    string vm_id = 1;
}

message GetVMMetricsResponse {
    bool success = 1;
    VMMetrics metrics = 2;
    string error = 3;
}

message StreamVMMetricsRequest {
    string vm_id = 1;
    int32 interval_seconds = 2;
}

message VMMetricsUpdate {
    string vm_id = 1;
    VMMetrics metrics = 2;
    int64 timestamp = 3;
}

// Data Structures
message VMInfo {
    string vm_id = 1;
    string name = 2;
    string template = 3;
    VMStatus status = 4;
    string ip_address = 5;
    int32 ssh_port = 6;
    int32 memory_mb = 7;
    int32 cpus = 8;
    int32 disk_gb = 9;
    string created_at = 10;
    string started_at = 11;
    string description = 12;
    map<string, string> metadata = 13;
}

message InjectionInfo {
    string injection_id = 1;
    string vm_id = 2;
    string file_path = 3;
    string target_path = 4;
    string permissions = 5;
    bool executed = 6;
    string injected_at = 7;
    int64 file_size = 8;
}

message VMMetrics {
    double cpu_percent = 1;
    double memory_percent = 2;
    int64 memory_used_mb = 3;
    int64 memory_total_mb = 4;
    double disk_percent = 5;
    int64 disk_used_gb = 6;
    int64 disk_total_gb = 7;
    int64 network_bytes_sent = 8;
    int64 network_bytes_recv = 9;
    int32 process_count = 10;
    repeated ProcessInfo processes = 11;
}

message ProcessInfo {
    int32 pid = 1;
    string name = 2;
    string command_line = 3;
    double cpu_percent = 4;
    int64 memory_mb = 5;
    string status = 6;
    string started_at = 7;
}

enum VMStatus {
    VM_STATUS_UNKNOWN = 0;
    VM_STATUS_CREATING = 1;
    VM_STATUS_STARTING = 2;
    VM_STATUS_RUNNING = 3;
    VM_STATUS_STOPPING = 4;
    VM_STATUS_STOPPED = 5;
    VM_STATUS_DESTROYING = 6;
    VM_STATUS_DESTROYED = 7;
    VM_STATUS_ERROR = 8;
    VM_STATUS_SUSPENDED = 9;
}
