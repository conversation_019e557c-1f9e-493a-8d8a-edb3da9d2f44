{"index_patterns": ["turdparty-api-logs-*"], "template": {"settings": {"number_of_shards": 1, "number_of_replicas": 0, "index.refresh_interval": "5s", "index.codec": "best_compression"}, "mappings": {"properties": {"@timestamp": {"type": "date"}, "ecs": {"properties": {"version": {"type": "keyword"}}}, "event": {"properties": {"kind": {"type": "keyword"}, "category": {"type": "keyword"}, "type": {"type": "keyword"}, "outcome": {"type": "keyword"}, "duration": {"type": "long"}}}, "service": {"properties": {"name": {"type": "keyword"}, "type": {"type": "keyword"}, "version": {"type": "keyword"}}}, "http": {"properties": {"request": {"properties": {"method": {"type": "keyword"}, "body": {"properties": {"bytes": {"type": "long"}}}, "headers": {"type": "object", "enabled": false}}}, "response": {"properties": {"status_code": {"type": "short"}, "body": {"properties": {"bytes": {"type": "long"}}}}}, "version": {"type": "keyword"}}}, "url": {"properties": {"path": {"type": "keyword"}, "query": {"type": "text"}, "full": {"type": "keyword"}, "scheme": {"type": "keyword"}, "domain": {"type": "keyword"}, "port": {"type": "integer"}}}, "user_agent": {"properties": {"original": {"type": "text", "fields": {"keyword": {"type": "keyword"}}}}}, "client": {"properties": {"ip": {"type": "ip"}, "port": {"type": "integer"}, "geo": {"properties": {"country_name": {"type": "keyword"}, "city_name": {"type": "keyword"}, "location": {"type": "geo_point"}}}}}, "labels": {"properties": {"request_id": {"type": "keyword"}, "api_version": {"type": "keyword"}, "environment": {"type": "keyword"}, "vm_id": {"type": "keyword"}, "connection_type": {"type": "keyword"}, "connection_id": {"type": "keyword"}}}, "performance": {"properties": {"response_time_ms": {"type": "float"}, "slow_request": {"type": "boolean"}, "status_class": {"type": "keyword"}}}, "error": {"properties": {"message": {"type": "text"}, "type": {"type": "keyword"}, "stack_trace": {"type": "text"}}}, "api": {"properties": {"category": {"type": "keyword"}, "subcategory": {"type": "keyword"}}}, "alert": {"properties": {"type": {"type": "keyword"}, "severity": {"type": "keyword"}}}, "tags": {"type": "keyword"}, "host": {"properties": {"name": {"type": "keyword"}}}, "log": {"properties": {"level": {"type": "keyword"}}}}}}, "priority": 200, "version": 1, "_meta": {"description": "Template for TurdParty API logs in ECS format"}}