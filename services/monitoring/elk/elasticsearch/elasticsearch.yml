# Elasticsearch configuration for TurdParty
# Optimized for single-node development environment

cluster.name: "turdparty-cluster"
node.name: "turdparty-node-1"

# Network settings
network.host: 0.0.0.0
http.port: 9200
transport.port: 9300

# Discovery settings for single node
discovery.type: single-node

# Security settings (disabled for development)
xpack.security.enabled: false
xpack.security.enrollment.enabled: false
xpack.security.http.ssl.enabled: false
xpack.security.transport.ssl.enabled: false

# Index settings
action.auto_create_index: "+turdparty-*,+.monitoring-*,+.watches,+.triggered_watches,+.watcher-history-*,+.ml-*"

# Memory settings
bootstrap.memory_lock: false

# Logging
logger.org.elasticsearch.deprecation: warn

# Note: Index-level settings (shards, replicas, refresh_interval)
# are configured via index templates, not node configuration
