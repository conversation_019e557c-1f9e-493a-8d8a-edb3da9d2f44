{"version": "8.11.0", "objects": [{"id": "turdparty-install-index-pattern", "type": "index-pattern", "attributes": {"title": "turdparty-install-ecs-*", "timeFieldName": "@timestamp", "fields": "[{\"name\":\"@timestamp\",\"type\":\"date\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"turdparty.install_uuid\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"turdparty.workflow_id\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"event.action\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"file.name\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true},{\"name\":\"file.path\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":false},{\"name\":\"host.name\",\"type\":\"string\",\"searchable\":true,\"aggregatable\":true}]"}}, {"id": "turdparty-install-dashboard", "type": "dashboard", "attributes": {"title": "TurdParty Install-Time Analysis", "description": "Dashboard for monitoring file injection and install-time events", "panelsJSON": "[{\"version\":\"8.11.0\",\"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":15,\"i\":\"1\"},\"panelIndex\":\"1\",\"embeddableConfig\":{},\"panelRefName\":\"panel_1\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":24,\"y\":0,\"w\":24,\"h\":15,\"i\":\"2\"},\"panelIndex\":\"2\",\"embeddableConfig\":{},\"panelRefName\":\"panel_2\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":0,\"y\":15,\"w\":48,\"h\":15,\"i\":\"3\"},\"panelIndex\":\"3\",\"embeddableConfig\":{},\"panelRefName\":\"panel_3\"}]", "timeRestore": false, "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"query\":{\"match_all\":{}},\"filter\":[]}"}}, "references": [{"name": "panel_1", "type": "visualization", "id": "install-events-timeline"}, {"name": "panel_2", "type": "visualization", "id": "install-files-table"}, {"name": "panel_3", "type": "visualization", "id": "install-workflow-status"}]}, {"id": "install-events-timeline", "type": "visualization", "attributes": {"title": "Install Events Timeline", "visState": "{\"title\":\"Install Events Timeline\",\"type\":\"histogram\",\"params\":{\"grid\":{\"categoryLines\":false,\"style\":{\"color\":\"#eee\"}},\"categoryAxes\":[{\"id\":\"CategoryAxis-1\",\"type\":\"category\",\"position\":\"bottom\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\"},\"labels\":{\"show\":true,\"truncate\":100},\"title\":{}}],\"valueAxes\":[{\"id\":\"ValueAxis-1\",\"name\":\"LeftAxis-1\",\"type\":\"value\",\"position\":\"left\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\",\"mode\":\"normal\"},\"labels\":{\"show\":true,\"rotate\":0,\"filter\":false,\"truncate\":100},\"title\":{\"text\":\"Count\"}}],\"seriesParams\":[{\"show\":true,\"type\":\"histogram\",\"mode\":\"stacked\",\"data\":{\"label\":\"Count\",\"id\":\"1\"},\"valueAxis\":\"ValueAxis-1\",\"drawLinesBetweenPoints\":true,\"showCircles\":true}],\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"right\",\"times\":[],\"addTimeMarker\":false},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"enabled\":true,\"type\":\"date_histogram\",\"schema\":\"segment\",\"params\":{\"field\":\"@timestamp\",\"interval\":\"auto\",\"customInterval\":\"2h\",\"min_doc_count\":1,\"extended_bounds\":{}}}]}", "uiStateJSON": "{}", "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"turdparty-install-index-pattern\",\"query\":{\"match_all\":{}},\"filter\":[]}"}}}, {"id": "install-files-table", "type": "visualization", "attributes": {"title": "Injected Files", "visState": "{\"title\":\"Injected Files\",\"type\":\"table\",\"params\":{\"perPage\":10,\"showPartialRows\":false,\"showMeticsAtAllLevels\":false,\"sort\":{\"columnIndex\":null,\"direction\":null},\"showTotal\":false,\"totalFunc\":\"sum\"},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"bucket\",\"params\":{\"field\":\"file.name\",\"size\":20,\"order\":\"desc\",\"orderBy\":\"1\"}},{\"id\":\"3\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"bucket\",\"params\":{\"field\":\"turdparty.workflow_id\",\"size\":10,\"order\":\"desc\",\"orderBy\":\"1\"}},{\"id\":\"4\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"bucket\",\"params\":{\"field\":\"host.name\",\"size\":10,\"order\":\"desc\",\"orderBy\":\"1\"}}]}", "uiStateJSON": "{}", "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"turdparty-install-index-pattern\",\"query\":{\"match_all\":{}},\"filter\":[]}"}}}, {"id": "install-workflow-status", "type": "visualization", "attributes": {"title": "Workflow Install Status", "visState": "{\"title\":\"Workflow Install Status\",\"type\":\"pie\",\"params\":{\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"right\",\"isDonut\":true},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"segment\",\"params\":{\"field\":\"event.outcome\",\"size\":5,\"order\":\"desc\",\"orderBy\":\"1\"}}]}", "uiStateJSON": "{}", "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"turdparty-install-index-pattern\",\"query\":{\"match_all\":{}},\"filter\":[]}"}}}]}