{"version": "8.11.0", "objects": [{"id": "turdparty-system-performance", "type": "dashboard", "attributes": {"title": "TurdParty System Performance", "description": "System performance monitoring and resource utilization", "panelsJSON": "[{\"version\":\"8.11.0\",\"gridData\":{\"x\":0,\"y\":0,\"w\":12,\"h\":15,\"i\":\"1\"},\"panelIndex\":\"1\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_1\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":12,\"y\":0,\"w\":12,\"h\":15,\"i\":\"2\"},\"panelIndex\":\"2\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_2\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":24,\"y\":0,\"w\":12,\"h\":15,\"i\":\"3\"},\"panelIndex\":\"3\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_3\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":36,\"y\":0,\"w\":12,\"h\":15,\"i\":\"4\"},\"panelIndex\":\"4\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_4\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":0,\"y\":15,\"w\":24,\"h\":15,\"i\":\"5\"},\"panelIndex\":\"5\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_5\"},{\"version\":\"8.11.0\",\"gridData\":{\"x\":24,\"y\":15,\"w\":24,\"h\":15,\"i\":\"6\"},\"panelIndex\":\"6\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_6\"}]", "timeRestore": false, "timeTo": "now", "timeFrom": "now-4h", "refreshInterval": {"pause": false, "value": 30000}, "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"query\":{\"match_all\":{}},\"filter\":[]}"}}, "references": [{"name": "panel_1", "type": "visualization", "id": "elk-stack-health"}, {"name": "panel_2", "type": "visualization", "id": "worker-queue-status"}, {"name": "panel_3", "type": "visualization", "id": "vm-pool-metrics"}, {"name": "panel_4", "type": "visualization", "id": "data-ingestion-rate"}, {"name": "panel_5", "type": "visualization", "id": "workflow-performance"}, {"name": "panel_6", "type": "visualization", "id": "error-rate-monitoring"}]}, {"id": "elk-stack-health", "type": "visualization", "attributes": {"title": "ELK Stack Health", "visState": "{\"title\":\"ELK Stack Health\",\"type\":\"metric\",\"aggs\":[{\"id\":\"1\",\"type\":\"count\",\"params\":{}},{\"id\":\"2\",\"type\":\"terms\",\"params\":{\"field\":\"service.name.keyword\",\"size\":5,\"order\":\"desc\",\"orderBy\":\"1\"}}]}", "uiStateJSON": "{}", "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"turdparty-*\",\"query\":{\"bool\":{\"should\":[{\"match\":{\"event.action\":\"health_check\"}},{\"match\":{\"service.name\":\"turdparty\"}}]}},\"filter\":[]}"}}}, {"id": "worker-queue-status", "type": "visualization", "attributes": {"title": "Worker Queue Status", "visState": "{\"title\":\"Worker Queue Status\",\"type\":\"histogram\",\"aggs\":[{\"id\":\"1\",\"type\":\"count\",\"params\":{}},{\"id\":\"2\",\"type\":\"date_histogram\",\"params\":{\"field\":\"@timestamp\",\"interval\":\"auto\",\"min_doc_count\":1}},{\"id\":\"3\",\"type\":\"terms\",\"params\":{\"field\":\"event.action.keyword\",\"size\":10,\"order\":\"desc\",\"orderBy\":\"1\"}}]}", "uiStateJSON": "{}", "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"turdparty-workflow-events-*\",\"query\":{\"bool\":{\"should\":[{\"match\":{\"event.action\":\"workflow_started\"}},{\"match\":{\"event.action\":\"workflow_completed\"}},{\"match\":{\"event.action\":\"workflow_failed\"}}]}},\"filter\":[]}"}}}, {"id": "vm-pool-metrics", "type": "visualization", "attributes": {"title": "VM Pool Metrics", "visState": "{\"title\":\"VM Pool Metrics\",\"type\":\"line\",\"aggs\":[{\"id\":\"1\",\"type\":\"cardinality\",\"params\":{\"field\":\"turdparty.vm_id.keyword\"}},{\"id\":\"2\",\"type\":\"date_histogram\",\"params\":{\"field\":\"@timestamp\",\"interval\":\"auto\",\"min_doc_count\":1}},{\"id\":\"3\",\"type\":\"terms\",\"params\":{\"field\":\"turdparty.vm_status.keyword\",\"size\":5,\"order\":\"desc\",\"orderBy\":\"1\"}}]}", "uiStateJSON": "{}", "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"turdparty-vm-monitoring-*\",\"query\":{\"match_all\":{}},\"filter\":[]}"}}}, {"id": "data-ingestion-rate", "type": "visualization", "attributes": {"title": "Data Ingestion Rate", "visState": "{\"title\":\"Data Ingestion Rate\",\"type\":\"line\",\"aggs\":[{\"id\":\"1\",\"type\":\"count\",\"params\":{}},{\"id\":\"2\",\"type\":\"date_histogram\",\"params\":{\"field\":\"@timestamp\",\"interval\":\"auto\",\"min_doc_count\":1}},{\"id\":\"3\",\"type\":\"terms\",\"params\":{\"field\":\"event.dataset.keyword\",\"size\":5,\"order\":\"desc\",\"orderBy\":\"1\"}}]}", "uiStateJSON": "{}", "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"turdparty-*\",\"query\":{\"match_all\":{}},\"filter\":[]}"}}}, {"id": "workflow-performance", "type": "visualization", "attributes": {"title": "Workflow Performance Metrics", "visState": "{\"title\":\"Workflow Performance Metrics\",\"type\":\"table\",\"aggs\":[{\"id\":\"1\",\"type\":\"count\",\"params\":{}},{\"id\":\"2\",\"type\":\"terms\",\"params\":{\"field\":\"turdparty.workflow_id.keyword\",\"size\":20,\"order\":\"desc\",\"orderBy\":\"1\"}},{\"id\":\"3\",\"type\":\"terms\",\"params\":{\"field\":\"event.outcome.keyword\",\"size\":3,\"order\":\"desc\",\"orderBy\":\"1\"}},{\"id\":\"4\",\"type\":\"avg\",\"params\":{\"field\":\"event.duration\"}},{\"id\":\"5\",\"type\":\"max\",\"params\":{\"field\":\"@timestamp\"}}]}", "uiStateJSON": "{\"vis\":{\"params\":{\"sort\":{\"columnIndex\":4,\"direction\":\"desc\"}}}}", "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"turdparty-workflow-events-*\",\"query\":{\"match_all\":{}},\"filter\":[]}"}}}, {"id": "error-rate-monitoring", "type": "visualization", "attributes": {"title": "Error Rate Monitoring", "visState": "{\"title\":\"Error Rate Monitoring\",\"type\":\"line\",\"aggs\":[{\"id\":\"1\",\"type\":\"count\",\"params\":{}},{\"id\":\"2\",\"type\":\"date_histogram\",\"params\":{\"field\":\"@timestamp\",\"interval\":\"auto\",\"min_doc_count\":1}},{\"id\":\"3\",\"type\":\"terms\",\"params\":{\"field\":\"event.outcome.keyword\",\"size\":3,\"order\":\"desc\",\"orderBy\":\"1\"}}]}", "uiStateJSON": "{}", "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\":\"turdparty-*\",\"query\":{\"bool\":{\"should\":[{\"match\":{\"event.outcome\":\"failure\"}},{\"match\":{\"event.outcome\":\"success\"}}]}},\"filter\":[]}"}}}]}