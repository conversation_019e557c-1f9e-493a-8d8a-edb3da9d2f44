#!/bin/bash

# TurdParty Kibana Dashboard Import Script
# Automatically imports all TurdParty dashboards into Kibana

set -e

# Configuration
KIBANA_HOST="${KIBANA_HOST:-kibana}"
KIBANA_PORT="${KIBANA_PORT:-5601}"
KIBANA_URL="http://${KIBANA_HOST}:${KIBANA_PORT}"
DASHBOARD_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)/dashboards"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Wait for Kibana to be ready
wait_for_kibana() {
    print_info "Waiting for Kibana to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "${KIBANA_URL}/api/status" > /dev/null 2>&1; then
            print_success "Kibana is ready!"
            return 0
        fi
        
        print_info "Attempt $attempt/$max_attempts - Kibana not ready yet..."
        sleep 10
        ((attempt++))
    done
    
    print_error "Kibana failed to become ready after $max_attempts attempts"
    return 1
}

# Create index patterns
create_index_patterns() {
    print_header "Creating Index Patterns"
    
    local patterns=(
        "turdparty-workflow-events-*"
        "turdparty-vm-monitoring-*"
        "turdparty-runtime-ecs-*"
        "turdparty-install-ecs-*"
    )
    
    for pattern in "${patterns[@]}"; do
        print_info "Creating index pattern: $pattern"
        
        local payload=$(cat <<EOF
{
  "attributes": {
    "title": "$pattern",
    "timeFieldName": "@timestamp"
  }
}
EOF
)
        
        local response=$(curl -s -X POST \
            "${KIBANA_URL}/api/saved_objects/index-pattern" \
            -H "Content-Type: application/json" \
            -H "kbn-xsrf: true" \
            -d "$payload" 2>/dev/null || echo "failed")
        
        if echo "$response" | grep -q '"id"'; then
            print_success "Index pattern created: $pattern"
        elif echo "$response" | grep -q "conflict"; then
            print_warning "Index pattern already exists: $pattern"
        else
            print_error "Failed to create index pattern: $pattern"
        fi
    done
}

# Import dashboard
import_dashboard() {
    local dashboard_file="$1"
    local dashboard_name=$(basename "$dashboard_file" .json)
    
    print_info "Importing dashboard: $dashboard_name"
    
    if [ ! -f "$dashboard_file" ]; then
        print_error "Dashboard file not found: $dashboard_file"
        return 1
    fi
    
    # Import the dashboard
    local response=$(curl -s -X POST \
        "${KIBANA_URL}/api/saved_objects/_import" \
        -H "kbn-xsrf: true" \
        -F "file=@$dashboard_file" 2>/dev/null || echo "failed")
    
    if echo "$response" | grep -q '"success":true'; then
        print_success "Dashboard imported: $dashboard_name"
        return 0
    elif echo "$response" | grep -q "conflict"; then
        print_warning "Dashboard already exists: $dashboard_name"
        
        # Try to overwrite
        print_info "Attempting to overwrite existing dashboard..."
        local overwrite_response=$(curl -s -X POST \
            "${KIBANA_URL}/api/saved_objects/_import?overwrite=true" \
            -H "kbn-xsrf: true" \
            -F "file=@$dashboard_file" 2>/dev/null || echo "failed")
        
        if echo "$overwrite_response" | grep -q '"success":true'; then
            print_success "Dashboard overwritten: $dashboard_name"
            return 0
        else
            print_error "Failed to overwrite dashboard: $dashboard_name"
            return 1
        fi
    else
        print_error "Failed to import dashboard: $dashboard_name"
        echo "Response: $response"
        return 1
    fi
}

# Import all dashboards
import_all_dashboards() {
    print_header "Importing TurdParty Dashboards"
    
    local dashboard_files=(
        "$DASHBOARD_DIR/turdparty-workflow-overview.json"
        "$DASHBOARD_DIR/turdparty-vm-monitoring.json"
        "$DASHBOARD_DIR/turdparty-threat-detection.json"
        "$DASHBOARD_DIR/turdparty-system-performance.json"
    )
    
    local success_count=0
    local total_count=${#dashboard_files[@]}
    
    for dashboard_file in "${dashboard_files[@]}"; do
        if import_dashboard "$dashboard_file"; then
            ((success_count++))
        fi
        echo
    done
    
    print_header "Import Summary"
    print_info "Successfully imported: $success_count/$total_count dashboards"
    
    if [ $success_count -eq $total_count ]; then
        print_success "All dashboards imported successfully!"
        return 0
    else
        print_warning "Some dashboards failed to import"
        return 1
    fi
}

# Set default index pattern
set_default_index_pattern() {
    print_header "Setting Default Index Pattern"
    
    local default_pattern="turdparty-workflow-events-*"
    
    print_info "Setting default index pattern: $default_pattern"
    
    # Get the index pattern ID
    local pattern_response=$(curl -s -X GET \
        "${KIBANA_URL}/api/saved_objects/_find?type=index-pattern&search_fields=title&search=${default_pattern}" \
        -H "Content-Type: application/json" 2>/dev/null || echo "failed")
    
    local pattern_id=$(echo "$pattern_response" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
    
    if [ -n "$pattern_id" ]; then
        # Set as default
        local settings_payload=$(cat <<EOF
{
  "changes": {
    "defaultIndex": "$pattern_id"
  }
}
EOF
)
        
        local settings_response=$(curl -s -X POST \
            "${KIBANA_URL}/api/kibana/settings" \
            -H "Content-Type: application/json" \
            -H "kbn-xsrf: true" \
            -d "$settings_payload" 2>/dev/null || echo "failed")
        
        if echo "$settings_response" | grep -q "settings"; then
            print_success "Default index pattern set: $default_pattern"
        else
            print_warning "Failed to set default index pattern"
        fi
    else
        print_warning "Could not find index pattern ID for: $default_pattern"
    fi
}

# Create sample data (if needed)
create_sample_data() {
    print_header "Creating Sample Data"
    
    print_info "Sample data creation would go here..."
    print_info "In production, this would be populated by actual workflow data"
    
    # For now, just create a simple test document
    local sample_doc=$(cat <<EOF
{
  "@timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)",
  "event": {
    "action": "dashboard_import",
    "category": ["process"],
    "type": ["info"],
    "outcome": "success"
  },
  "service": {
    "name": "turdparty",
    "type": "malware_analysis"
  },
  "turdparty": {
    "phase": "setup",
    "workflow_id": "dashboard-import-$(date +%s)"
  },
  "message": "TurdParty dashboards imported successfully"
}
EOF
)
    
    print_info "Sample document created for testing"
}

# Main execution
main() {
    print_header "TurdParty Kibana Dashboard Import"
    print_info "Importing dashboards for TurdParty malware analysis platform"
    echo
    
    # Check if dashboard directory exists
    if [ ! -d "$DASHBOARD_DIR" ]; then
        print_error "Dashboard directory not found: $DASHBOARD_DIR"
        exit 1
    fi
    
    # Wait for Kibana
    if ! wait_for_kibana; then
        print_error "Cannot connect to Kibana at $KIBANA_URL"
        exit 1
    fi
    
    echo
    
    # Create index patterns
    create_index_patterns
    echo
    
    # Import dashboards
    if import_all_dashboards; then
        echo
        
        # Set default index pattern
        set_default_index_pattern
        echo
        
        # Create sample data
        create_sample_data
        echo
        
        print_header "Dashboard Import Complete!"
        print_success "TurdParty dashboards are now available in Kibana"
        print_info "Access Kibana at: $KIBANA_URL"
        print_info "Available dashboards:"
        echo "  • TurdParty Workflow Overview"
        echo "  • TurdParty VM Runtime Monitoring"
        echo "  • TurdParty Threat Detection & Analysis"
        echo "  • TurdParty System Performance"
        
    else
        print_error "Dashboard import failed"
        exit 1
    fi
}

# Run main function
main "$@"
