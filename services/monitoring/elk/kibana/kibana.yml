# Kibana configuration for TurdParty

server.name: "turdparty-kibana"
server.host: "0.0.0.0"
server.port: 5601

# Elasticsearch connection
elasticsearch.hosts: ["http://elasticsearch:9200"]

# Security (disabled for development)
xpack.security.enabled: false
xpack.encryptedSavedObjects.encryptionKey: "turdparty-kibana-encryption-key-32-chars"

# Monitoring
monitoring.ui.container.elasticsearch.enabled: true
monitoring.ui.container.logstash.enabled: true

# Logging
logging.appenders.file.type: file
logging.appenders.file.fileName: /usr/share/kibana/logs/kibana.log
logging.appenders.file.layout.type: json

logging.root.level: info
logging.root.appenders: [default, file]

# Index patterns
kibana.index: ".kibana"
kibana.defaultAppId: "dashboard"

# Default index pattern
kibana.defaultRoute: "/app/dashboards"

# UI settings
map.includeElasticMapsService: false
telemetry.enabled: false
newsfeed.enabled: false

# Advanced settings
server.maxPayload: 1048576
elasticsearch.requestTimeout: 30000
elasticsearch.shardTimeout: 30000
