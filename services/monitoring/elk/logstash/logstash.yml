# Logstash configuration for TurdParty
# Process VM runtime data and forward to Elasticsearch

node.name: "turdparty-logstash"

# Pipeline settings
pipeline.workers: 2
pipeline.batch.size: 125
pipeline.batch.delay: 50

# Monitoring
xpack.monitoring.enabled: false

# Logging
log.level: info
path.logs: /usr/share/logstash/logs

# Configuration reload
config.reload.automatic: true
config.reload.interval: 3s

# Dead letter queue
dead_letter_queue.enable: true
path.dead_letter_queue: /usr/share/logstash/data/dead_letter_queue
