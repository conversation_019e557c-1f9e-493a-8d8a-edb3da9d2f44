{"index_patterns": ["turdparty-install-ecs-*"], "settings": {"number_of_shards": 1, "number_of_replicas": 0, "refresh_interval": "5s", "index.lifecycle.name": "turdparty-install-policy"}, "mappings": {"properties": {"@timestamp": {"type": "date"}, "ecs": {"properties": {"version": {"type": "keyword"}}}, "event": {"properties": {"kind": {"type": "keyword"}, "category": {"type": "keyword"}, "type": {"type": "keyword"}, "action": {"type": "keyword"}, "outcome": {"type": "keyword"}, "duration": {"type": "long"}}}, "turdparty": {"properties": {"workflow_id": {"type": "keyword"}, "file_id": {"type": "keyword"}, "vm_id": {"type": "keyword"}, "phase": {"type": "keyword"}, "install_uuid": {"type": "keyword"}}}, "file": {"properties": {"name": {"type": "keyword"}, "path": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 512}}}, "size": {"type": "long"}, "hash": {"properties": {"sha256": {"type": "keyword"}}}}}, "process": {"properties": {"pid": {"type": "long"}, "name": {"type": "keyword"}, "command_line": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 1024}}}, "executable": {"type": "keyword"}}}, "host": {"properties": {"name": {"type": "keyword"}, "id": {"type": "keyword"}, "ip": {"type": "ip"}}}, "message": {"type": "text"}, "level": {"type": "keyword"}, "tags": {"type": "keyword"}}}}