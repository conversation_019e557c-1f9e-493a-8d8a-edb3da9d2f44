{"index_patterns": ["turdparty-runtime-ecs-*"], "settings": {"number_of_shards": 1, "number_of_replicas": 0, "refresh_interval": "5s", "index.lifecycle.name": "turdparty-runtime-policy"}, "mappings": {"properties": {"@timestamp": {"type": "date"}, "ecs": {"properties": {"version": {"type": "keyword"}}}, "event": {"properties": {"kind": {"type": "keyword"}, "category": {"type": "keyword"}, "type": {"type": "keyword"}, "action": {"type": "keyword"}, "outcome": {"type": "keyword"}, "duration": {"type": "long"}}}, "turdparty": {"properties": {"workflow_id": {"type": "keyword"}, "file_id": {"type": "keyword"}, "vm_id": {"type": "keyword"}, "phase": {"type": "keyword"}, "runtime_uuid": {"type": "keyword"}}}, "file": {"properties": {"name": {"type": "keyword"}, "path": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 512}}}, "accessed": {"type": "date"}, "created": {"type": "date"}, "modified": {"type": "date"}}}, "process": {"properties": {"pid": {"type": "long"}, "ppid": {"type": "long"}, "name": {"type": "keyword"}, "command_line": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 1024}}}, "executable": {"type": "keyword"}, "start": {"type": "date"}, "working_directory": {"type": "keyword"}}}, "network": {"properties": {"protocol": {"type": "keyword"}, "direction": {"type": "keyword"}, "bytes": {"type": "long"}, "packets": {"type": "long"}}}, "source": {"properties": {"ip": {"type": "ip"}, "port": {"type": "long"}}}, "destination": {"properties": {"ip": {"type": "ip"}, "port": {"type": "long"}}}, "host": {"properties": {"name": {"type": "keyword"}, "id": {"type": "keyword"}, "ip": {"type": "ip"}, "cpu": {"properties": {"usage": {"type": "float"}}}, "memory": {"properties": {"usage": {"type": "float"}, "total": {"type": "long"}, "available": {"type": "long"}}}}}, "message": {"type": "text"}, "level": {"type": "keyword"}, "tags": {"type": "keyword"}}}}