{"index_patterns": ["turdparty-*"], "settings": {"number_of_shards": 1, "number_of_replicas": 0, "refresh_interval": "5s", "index.lifecycle.name": "turdparty-policy", "index.lifecycle.rollover_alias": "turdparty"}, "mappings": {"properties": {"@timestamp": {"type": "date"}, "event_source": {"type": "keyword"}, "workflow_id": {"type": "keyword"}, "vm_id": {"type": "keyword"}, "process_name": {"type": "keyword"}, "process_pid": {"type": "long"}, "process_command": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "file_path": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 512}}}, "file_operation": {"type": "keyword"}, "network_protocol": {"type": "keyword"}, "network_src_ip": {"type": "ip"}, "network_dst_ip": {"type": "ip"}, "network_src_port": {"type": "long"}, "network_dst_port": {"type": "long"}, "cpu_usage": {"type": "float"}, "memory_usage": {"type": "float"}, "disk_usage": {"type": "float"}, "src_geoip": {"properties": {"location": {"type": "geo_point"}, "country_name": {"type": "keyword"}, "city_name": {"type": "keyword"}, "region_name": {"type": "keyword"}}}, "dst_geoip": {"properties": {"location": {"type": "geo_point"}, "country_name": {"type": "keyword"}, "city_name": {"type": "keyword"}, "region_name": {"type": "keyword"}}}, "message": {"type": "text"}, "level": {"type": "keyword"}, "tags": {"type": "keyword"}}}}