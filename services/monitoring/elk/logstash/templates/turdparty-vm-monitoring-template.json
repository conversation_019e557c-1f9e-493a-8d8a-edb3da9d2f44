{"index_patterns": ["turdparty-vm-monitoring-*"], "settings": {"number_of_shards": 1, "number_of_replicas": 0, "refresh_interval": "5s", "index.lifecycle.name": "turdparty-vm-monitoring-policy", "index.mapping.total_fields.limit": 2000, "index.max_result_window": 50000}, "mappings": {"properties": {"@timestamp": {"type": "date", "format": "strict_date_optional_time||epoch_millis"}, "ecs": {"properties": {"version": {"type": "keyword", "ignore_above": 256}}}, "event": {"properties": {"kind": {"type": "keyword", "ignore_above": 256}, "category": {"type": "keyword", "ignore_above": 256}, "type": {"type": "keyword", "ignore_above": 256}, "action": {"type": "keyword", "ignore_above": 256}, "outcome": {"type": "keyword", "ignore_above": 256}, "duration": {"type": "long"}, "start": {"type": "date"}, "end": {"type": "date"}, "dataset": {"type": "keyword", "ignore_above": 256}}}, "service": {"properties": {"name": {"type": "keyword", "ignore_above": 256}, "type": {"type": "keyword", "ignore_above": 256}, "version": {"type": "keyword", "ignore_above": 256}}}, "host": {"properties": {"name": {"type": "keyword", "ignore_above": 256}, "hostname": {"type": "keyword", "ignore_above": 256}, "ip": {"type": "ip"}, "mac": {"type": "keyword", "ignore_above": 256}, "os": {"properties": {"name": {"type": "keyword", "ignore_above": 256}, "version": {"type": "keyword", "ignore_above": 256}, "platform": {"type": "keyword", "ignore_above": 256}}}, "cpu": {"properties": {"usage": {"type": "scaled_float", "scaling_factor": 1000}, "cores": {"type": "integer"}}}, "memory": {"properties": {"usage": {"type": "scaled_float", "scaling_factor": 1000}, "total": {"type": "long"}, "available": {"type": "long"}, "used": {"type": "long"}}}, "disk": {"properties": {"usage": {"type": "scaled_float", "scaling_factor": 1000}, "total": {"type": "long"}, "available": {"type": "long"}, "used": {"type": "long"}}}, "network": {"properties": {"in": {"properties": {"bytes": {"type": "long"}, "packets": {"type": "long"}}}, "out": {"properties": {"bytes": {"type": "long"}, "packets": {"type": "long"}}}}}}}, "process": {"properties": {"pid": {"type": "long"}, "name": {"type": "keyword", "ignore_above": 256}, "title": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "command_line": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 1024}}}, "executable": {"type": "keyword", "ignore_above": 1024}, "args": {"type": "keyword", "ignore_above": 256}, "working_directory": {"type": "keyword", "ignore_above": 1024}, "start": {"type": "date"}, "cpu": {"properties": {"usage": {"type": "scaled_float", "scaling_factor": 1000}}}, "memory": {"properties": {"usage": {"type": "long"}, "rss": {"type": "long"}, "vms": {"type": "long"}}}}}, "file": {"properties": {"path": {"type": "keyword", "ignore_above": 1024}, "name": {"type": "keyword", "ignore_above": 256}, "extension": {"type": "keyword", "ignore_above": 256}, "size": {"type": "long"}, "hash": {"properties": {"md5": {"type": "keyword", "ignore_above": 256}, "sha1": {"type": "keyword", "ignore_above": 256}, "sha256": {"type": "keyword", "ignore_above": 256}, "blake3": {"type": "keyword", "ignore_above": 256}}}, "operation": {"type": "keyword", "ignore_above": 256}}}, "network": {"properties": {"protocol": {"type": "keyword", "ignore_above": 256}, "direction": {"type": "keyword", "ignore_above": 256}, "bytes": {"type": "long"}, "packets": {"type": "long"}, "community_id": {"type": "keyword", "ignore_above": 256}}}, "source": {"properties": {"ip": {"type": "ip"}, "port": {"type": "long"}, "geo": {"properties": {"country_name": {"type": "keyword", "ignore_above": 256}, "city_name": {"type": "keyword", "ignore_above": 256}, "location": {"type": "geo_point"}}}}}, "destination": {"properties": {"ip": {"type": "ip"}, "port": {"type": "long"}, "geo": {"properties": {"country_name": {"type": "keyword", "ignore_above": 256}, "city_name": {"type": "keyword", "ignore_above": 256}, "location": {"type": "geo_point"}}}}}, "turdparty": {"properties": {"workflow_id": {"type": "keyword", "ignore_above": 256}, "file_id": {"type": "keyword", "ignore_above": 256}, "vm_id": {"type": "keyword", "ignore_above": 256}, "phase": {"type": "keyword", "ignore_above": 256}, "vm_name": {"type": "keyword", "ignore_above": 256}, "vm_template": {"type": "keyword", "ignore_above": 256}, "vm_status": {"type": "keyword", "ignore_above": 256}, "analysis_type": {"type": "keyword", "ignore_above": 256}, "threat_level": {"type": "keyword", "ignore_above": 256}, "indicators": {"type": "nested", "properties": {"type": {"type": "keyword", "ignore_above": 256}, "value": {"type": "keyword", "ignore_above": 1024}, "confidence": {"type": "integer"}}}}}, "vm": {"properties": {"name": {"type": "keyword", "ignore_above": 256}, "template": {"type": "keyword", "ignore_above": 256}, "status": {"type": "keyword", "ignore_above": 256}, "container_id": {"type": "keyword", "ignore_above": 256}, "uptime": {"type": "long"}}}, "message": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "tags": {"type": "keyword", "ignore_above": 256}}}}