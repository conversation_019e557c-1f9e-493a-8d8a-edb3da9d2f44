"""
Comprehensive TurdParty Monitoring Solution
Integrates file tree, registry, and network monitoring for complete malware analysis
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Any
from file_tree_monitor import FileTreeMonitor
from registry_monitor import RegistryMonitor
from monitor import NetworkTrafficMonitor, TurdPartyVMMonitor


class ComprehensiveMalwareMonitor:
    """Complete monitoring solution for TurdParty malware analysis."""
    
    def __init__(self):
        self.file_monitor = FileTreeMonitor()
        self.registry_monitor = RegistryMonitor()
        self.network_monitor = NetworkTrafficMonitor()
        self.vm_monitor = TurdPartyVMMonitor()
        
    def start_comprehensive_monitoring(self, vm_connection, vm_id: str, file_uuid: str, monitoring_duration: int = 300) -> Dict[str, Any]:
        """Start comprehensive monitoring for malware installation."""
        try:
            print(f"🔍 Starting comprehensive monitoring for VM {vm_id}")
            
            # Step 1: Capture baselines
            print("📊 Capturing baseline snapshots...")
            file_baseline = self.file_monitor.capture_baseline_snapshot(vm_connection)
            registry_baseline = self.registry_monitor.capture_baseline_registry(vm_connection)
            network_baseline = self.network_monitor.capture_network_baseline(vm_connection)
            
            print(f"✅ Baselines captured - Files: {len(file_baseline)} dirs, Registry: {len(registry_baseline)} hives, Network: {len(network_baseline)} connections")
            
            # Step 2: Start network monitoring (runs in background)
            print(f"🌐 Starting network traffic monitoring for {monitoring_duration} seconds...")
            network_connections = self.network_monitor.monitor_network_traffic(vm_connection, monitoring_duration)
            
            # Step 3: Wait for installation to complete
            print("⏳ Waiting for installation to complete...")
            time.sleep(monitoring_duration)
            
            # Step 4: Capture post-installation snapshots
            print("📊 Capturing post-installation snapshots...")
            file_post_install = self.file_monitor.capture_post_install_snapshot(vm_connection)
            registry_post_install = self.registry_monitor.capture_post_install_registry(vm_connection)
            
            # Step 5: Generate analysis
            print("🔍 Analyzing changes...")
            file_footprint = self.file_monitor.generate_installation_footprint()
            registry_changes = self.registry_monitor.generate_registry_changes()
            
            # Step 6: Generate ECS events
            print("📤 Generating ECS events...")
            file_events = self.file_monitor.generate_ecs_file_events(file_footprint, vm_id, file_uuid)
            registry_events = self.registry_monitor.generate_ecs_registry_events(registry_changes, vm_id, file_uuid)
            network_events = self.network_monitor.generate_ecs_network_events(network_connections, vm_id, file_uuid)
            
            # Step 7: Send to Elasticsearch
            all_events = file_events + registry_events + network_events
            print(f"📤 Sending {len(all_events)} events to Elasticsearch...")
            
            es_result = self._send_events_to_elasticsearch(all_events, vm_id)
            
            # Step 8: Generate comprehensive report
            comprehensive_report = {
                "monitoring_summary": {
                    "vm_id": vm_id,
                    "file_uuid": file_uuid,
                    "monitoring_duration": monitoring_duration,
                    "timestamp": datetime.now(datetime.UTC).isoformat(),
                    "total_events_generated": len(all_events)
                },
                "file_footprint": {
                    "files_created": len(file_footprint.get('files_created', [])),
                    "files_modified": len(file_footprint.get('files_modified', [])),
                    "files_deleted": len(file_footprint.get('files_deleted', [])),
                    "total_size_added": file_footprint.get('total_size_added', 0),
                    "installation_paths": file_footprint.get('installation_paths', []),
                    "detailed_files": file_footprint.get('files_created', [])[:10]  # First 10 for summary
                },
                "registry_changes": {
                    "keys_created": len(registry_changes.get('keys_created', [])),
                    "keys_modified": len(registry_changes.get('keys_modified', [])),
                    "values_created": len(registry_changes.get('values_created', [])),
                    "values_modified": len(registry_changes.get('values_modified', [])),
                    "total_registry_changes": registry_changes.get('total_keys_added', 0) + registry_changes.get('total_values_added', 0),
                    "detailed_keys": registry_changes.get('keys_created', [])[:5]  # First 5 for summary
                },
                "network_activity": {
                    "new_connections": len(network_connections),
                    "unique_destinations": len(set([f"{conn.get('RemoteAddress')}:{conn.get('RemotePort')}" for conn in network_connections])),
                    "detailed_connections": network_connections[:10]  # First 10 for summary
                },
                "elasticsearch_result": es_result
            }
            
            print("✅ Comprehensive monitoring complete!")
            print(f"📊 Summary: {comprehensive_report['file_footprint']['files_created']} files created, {comprehensive_report['registry_changes']['total_registry_changes']} registry changes, {comprehensive_report['network_activity']['new_connections']} network connections")
            
            return comprehensive_report
            
        except Exception as e:
            print(f"❌ Comprehensive monitoring failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "vm_id": vm_id,
                "file_uuid": file_uuid
            }
    
    def _send_events_to_elasticsearch(self, events: List[Dict[str, Any]], vm_id: str) -> Dict[str, Any]:
        """Send all events to Elasticsearch."""
        try:
            import requests
            
            es_url = "http://elasticsearch:9200"
            index_name = f"turdparty-comprehensive-ecs-{datetime.now(datetime.UTC).strftime('%Y.%m.%d')}"
            
            sent_count = 0
            failed_count = 0
            
            for event in events:
                try:
                    response = requests.post(
                        f"{es_url}/{index_name}/_doc",
                        json=event,
                        headers={"Content-Type": "application/json"},
                        timeout=10
                    )
                    
                    if response.status_code in [200, 201]:
                        sent_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    failed_count += 1
                    print(f"Failed to send event: {e}")
            
            return {
                "success": True,
                "total_events": len(events),
                "sent_count": sent_count,
                "failed_count": failed_count,
                "index_name": index_name,
                "vm_id": vm_id
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "total_events": len(events),
                "sent_count": 0,
                "failed_count": len(events)
            }
    
    def generate_evidence_box_data(self, comprehensive_report: Dict[str, Any]) -> Dict[str, Any]:
        """Generate Evidence Box data from comprehensive monitoring results."""
        try:
            vm_id = comprehensive_report['monitoring_summary']['vm_id']
            file_uuid = comprehensive_report['monitoring_summary']['file_uuid']
            index_name = comprehensive_report['elasticsearch_result']['index_name']
            
            evidence_data = {
                "vm_id": vm_id,
                "file_uuid": file_uuid,
                "index_name": index_name,
                "timestamp": comprehensive_report['monitoring_summary']['timestamp'],
                "events_count": comprehensive_report['monitoring_summary']['total_events_generated'],
                
                # Database verification queries
                "database_queries": {
                    "vm_verification": f"SELECT id, name, status, injection_completed, injection_path FROM vm_instances WHERE id = '{vm_id}';",
                    "file_verification": f"SELECT id, original_filename, file_size, blake3_hash FROM uploaded_files WHERE id = '{file_uuid}';",
                    "installation_footprint": f"SELECT * FROM installation_footprints WHERE vm_id = '{vm_id}' AND file_uuid = '{file_uuid}';"
                },
                
                # Elasticsearch verification links
                "elasticsearch_links": {
                    "all_events": f"http://elasticsearch.turdparty.localhost:9200/{index_name}/_search?q=turdparty.vm_id:{vm_id}",
                    "file_events": f"http://elasticsearch.turdparty.localhost:9200/{index_name}/_search?q=event.category:file AND turdparty.vm_id:{vm_id}",
                    "registry_events": f"http://elasticsearch.turdparty.localhost:9200/{index_name}/_search?q=event.category:configuration AND turdparty.vm_id:{vm_id}",
                    "network_events": f"http://elasticsearch.turdparty.localhost:9200/{index_name}/_search?q=event.category:network AND turdparty.vm_id:{vm_id}"
                },
                
                # Manual verification commands
                "verification_commands": {
                    "check_vm_logs": f"docker logs turdpartycollab_worker_vm | grep '{vm_id}'",
                    "check_elasticsearch": f"curl 'http://localhost:9200/{index_name}/_count' -H 'Content-Type: application/json' -d '{{\"query\": {{\"term\": {{\"turdparty.vm_id\": \"{vm_id}\"}}}}}}'",
                    "check_file_footprint": f"docker exec -it turdpartycollab_database psql -U postgres -d turdparty -c \"SELECT COUNT(*) FROM installation_footprints WHERE vm_id = '{vm_id}';\""
                },
                
                # Summary statistics
                "summary_stats": {
                    "files_created": comprehensive_report['file_footprint']['files_created'],
                    "registry_changes": comprehensive_report['registry_changes']['total_registry_changes'],
                    "network_connections": comprehensive_report['network_activity']['new_connections'],
                    "total_disk_usage_mb": round(comprehensive_report['file_footprint']['total_size_added'] / (1024 * 1024), 2),
                    "installation_paths": comprehensive_report['file_footprint']['installation_paths']
                }
            }
            
            return evidence_data
            
        except Exception as e:
            print(f"Failed to generate evidence box data: {e}")
            return {}


def main():
    """Example usage of comprehensive monitoring."""
    print("🔍 TurdParty Comprehensive Malware Monitor")
    print("==========================================")
    
    monitor = ComprehensiveMalwareMonitor()
    
    # This would be called from the VM injection workflow
    # vm_connection = establish_vm_connection(vm_id)
    # result = monitor.start_comprehensive_monitoring(vm_connection, vm_id, file_uuid, 300)
    # evidence_data = monitor.generate_evidence_box_data(result)
    
    print("✅ Comprehensive monitoring system ready")


if __name__ == "__main__":
    main()
