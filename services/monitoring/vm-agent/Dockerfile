# TurdParty VM Monitoring Agent
# Lightweight agent for collecting runtime data from VMs

FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    procps \
    net-tools \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy agent code
COPY agent.py .

# Create non-root user for security
RUN useradd -r -s /bin/false turdparty-agent

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV LOGSTASH_HOST=logstash
ENV LOGSTASH_PORT=8080
ENV COLLECTION_INTERVAL=5
ENV MONITORED_PATHS=/tmp,/home

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import psutil; psutil.cpu_percent()" || exit 1

# Switch to non-root user
USER turdparty-agent

# Run the agent
CMD ["python", "agent.py"]
