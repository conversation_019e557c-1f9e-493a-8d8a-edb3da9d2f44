# TurdParty VM Monitoring Agent Configuration

# ELK Stack Configuration
elk:
  logstash:
    host: "logstash"
    port: 8080
    timeout: 5
  elasticsearch:
    host: "elasticsearch"
    port: 9200

# Collection Settings
collection:
  interval: 5  # seconds
  batch_size: 10  # events per batch
  
# Monitoring Configuration
monitoring:
  # System metrics
  system:
    enabled: true
    cpu: true
    memory: true
    disk: true
    network: true
  
  # Process monitoring
  processes:
    enabled: true
    suspicious_detection: true
    max_processes: 100  # limit for performance
    
  # File system monitoring
  filesystem:
    enabled: true
    paths:
      - "/tmp"
      - "/home"
      - "/var/log"
    recursive: true
    events:
      - "created"
      - "modified"
      - "deleted"
      - "moved"

# Suspicious Activity Detection
detection:
  # Process-based detection
  processes:
    suspicious_names:
      - "nc"
      - "netcat"
      - "wget"
      - "curl"
      - "python"
      - "perl"
      - "ruby"
      - "bash"
      - "sh"
      - "powershell"
      - "cmd"
    
    network_tools:
      - "wget"
      - "curl"
      - "nc "
      - "netcat"
      - "nmap"
      - "telnet"
    
    thresholds:
      high_cpu: 80.0  # percent
      high_memory: 50.0  # percent
  
  # File system detection
  filesystem:
    suspicious_extensions:
      - ".exe"
      - ".bat"
      - ".cmd"
      - ".ps1"
      - ".vbs"
      - ".scr"
    
    suspicious_paths:
      - "/tmp"
      - "/var/tmp"
      - "/dev/shm"
    
    size_threshold: 10485760  # 10MB

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
# Performance Settings
performance:
  max_memory_usage: 100  # MB
  max_cpu_usage: 10  # percent
  buffer_size: 1000  # events
  
# Security Settings
security:
  # Run as non-root user
  user: "turdparty-agent"
  
  # Restrict network access
  allowed_hosts:
    - "logstash"
    - "elasticsearch"
  
  # File permissions
  file_permissions: "0644"
