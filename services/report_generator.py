"""
TurdParty Sphinx Report Generator Service
Generates comprehensive Sphinx documentation from binary analysis data.
"""

import os
import json
import asyncio
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
import jinja2
import httpx
from elasticsearch import AsyncElasticsearch

class SphinxReportGenerator:
    """Generate Sphinx reports from TurdParty analysis data."""
    
    def __init__(self, reports_dir: str = "docs/reports"):
        self.reports_dir = Path(reports_dir)
        self.templates_dir = self.reports_dir / "_templates"
        self.reports_output_dir = self.reports_dir / "reports"
        self.build_dir = self.reports_dir / "_build"
        
        # Initialize Jinja2 environment
        self.jinja_env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(str(self.templates_dir)),
            autoescape=jinja2.select_autoescape(['html', 'xml'])
        )
        
        # API client
        self.api_client = httpx.AsyncClient(timeout=60.0)
        
        # Elasticsearch client
        self.es_client = AsyncElasticsearch(
            hosts=["http://elasticsearch.turdparty.localhost:9200"],
            timeout=30
        )
    
    async def generate_report_from_uuid(self, file_uuid: str) -> Dict[str, Any]:
        """Generate a complete Sphinx report from a file UUID."""
        try:
            print(f"📊 Generating Sphinx report for UUID: {file_uuid}")
            
            # Step 1: Fetch report data from API
            report_data = await self._fetch_report_data(file_uuid)
            if not report_data:
                raise Exception("Failed to fetch report data from API")
            
            # Step 2: Fetch ECS data from Elasticsearch
            ecs_data = await self._fetch_ecs_data(file_uuid)
            
            # Step 3: Generate RST content
            rst_content = await self._generate_rst_content(report_data, ecs_data)
            
            # Step 4: Write RST file
            report_filename = self._get_report_filename(report_data)
            rst_file_path = self.reports_output_dir / f"{report_filename}.rst"
            
            with open(rst_file_path, 'w', encoding='utf-8') as f:
                f.write(rst_content)
            
            # Step 5: Update index
            await self._update_reports_index(report_data, report_filename)
            
            # Step 6: Build HTML
            html_path = await self._build_html()
            
            print(f"✅ Report generated successfully: {rst_file_path}")
            
            return {
                "success": True,
                "file_uuid": file_uuid,
                "rst_file": str(rst_file_path),
                "html_path": html_path,
                "report_url": f"http://reports.turdparty.localhost/{report_filename}.html",
                "report_data": report_data
            }
            
        except Exception as e:
            print(f"❌ Report generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "file_uuid": file_uuid
            }
    
    async def _fetch_report_data(self, file_uuid: str) -> Optional[Dict[str, Any]]:
        """Fetch report data from the TurdParty API."""
        try:
            url = f"http://api.turdparty.localhost/api/v1/reports/binary/{file_uuid}"
            response = await self.api_client.get(url)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"⚠️ API request failed: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"⚠️ Failed to fetch report data: {e}")
            return None
    
    async def _fetch_ecs_data(self, file_uuid: str) -> List[Dict[str, Any]]:
        """Fetch ECS data from Elasticsearch."""
        try:
            query = {
                "query": {
                    "term": {"file_uuid.keyword": file_uuid}
                },
                "size": 1000,
                "sort": [{"@timestamp": {"order": "asc"}}]
            }
            
            result = await self.es_client.search(
                index="turdparty-*",
                body=query
            )
            
            return [hit["_source"] for hit in result["hits"]["hits"]]
            
        except Exception as e:
            print(f"⚠️ Failed to fetch ECS data: {e}")
            return []
    
    async def _generate_rst_content(self, report_data: Dict[str, Any], ecs_data: List[Dict[str, Any]]) -> str:
        """Generate RST content from report data."""
        
        # Prepare template context
        context = {
            "report": report_data,
            "ecs_data": ecs_data,
            "generated_at": datetime.now(datetime.UTC),
            "file_uuid": report_data.get("metadata", {}).get("file_uuid", ""),
            "filename": report_data.get("file_info", {}).get("filename", "Unknown"),
            "risk_level": report_data.get("security_analysis", {}).get("threat_indicators", {}).get("risk_level", "unknown"),
            "total_events": len(ecs_data),
            "event_categories": self._categorize_events(ecs_data),
            "installation_summary": self._summarize_installation(ecs_data),
            "process_timeline": self._create_process_timeline(ecs_data),
            "file_changes": self._summarize_file_changes(ecs_data),
            "registry_changes": self._summarize_registry_changes(ecs_data)
        }
        
        # Load and render template
        template = self.jinja_env.get_template("binary_analysis_report.rst.j2")
        return template.render(**context)
    
    def _categorize_events(self, ecs_data: List[Dict[str, Any]]) -> Dict[str, int]:
        """Categorize ECS events by type."""
        categories = {}
        for event in ecs_data:
            action = event.get("event", {}).get("action", "unknown")
            categories[action] = categories.get(action, 0) + 1
        return categories
    
    def _summarize_installation(self, ecs_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Summarize installation activities."""
        files_created = []
        registry_keys = []
        processes = []
        
        for event in ecs_data:
            action = event.get("event", {}).get("action", "")
            
            if action == "file_created":
                files_created.append({
                    "path": event.get("file", {}).get("path", ""),
                    "size": event.get("file", {}).get("size", 0),
                    "timestamp": event.get("@timestamp", "")
                })
            elif action == "registry_key_created":
                registry_keys.append({
                    "key": event.get("registry", {}).get("key", ""),
                    "value": event.get("registry", {}).get("value", ""),
                    "timestamp": event.get("@timestamp", "")
                })
            elif action == "process_start":
                processes.append({
                    "name": event.get("process", {}).get("name", ""),
                    "pid": event.get("process", {}).get("pid", 0),
                    "command": event.get("process", {}).get("command_line", ""),
                    "timestamp": event.get("@timestamp", "")
                })
        
        return {
            "files_created": files_created,
            "registry_keys": registry_keys,
            "processes": processes,
            "total_files": len(files_created),
            "total_registry_keys": len(registry_keys),
            "total_processes": len(processes)
        }
    
    def _create_process_timeline(self, ecs_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create a timeline of process events."""
        timeline = []
        
        for event in ecs_data:
            if event.get("event", {}).get("action") == "process_start":
                timeline.append({
                    "timestamp": event.get("@timestamp", ""),
                    "process": event.get("process", {}).get("name", ""),
                    "pid": event.get("process", {}).get("pid", 0),
                    "command": event.get("process", {}).get("command_line", "")
                })
        
        return sorted(timeline, key=lambda x: x["timestamp"])
    
    def _summarize_file_changes(self, ecs_data: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """Summarize file system changes by directory."""
        directories = {}
        
        for event in ecs_data:
            if event.get("event", {}).get("action") == "file_created":
                file_path = event.get("file", {}).get("path", "")
                if file_path:
                    directory = str(Path(file_path).parent)
                    if directory not in directories:
                        directories[directory] = []
                    directories[directory].append(Path(file_path).name)
        
        return directories
    
    def _summarize_registry_changes(self, ecs_data: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, str]]]:
        """Summarize registry changes by hive."""
        hives = {}
        
        for event in ecs_data:
            if event.get("event", {}).get("action") == "registry_key_created":
                key = event.get("registry", {}).get("key", "")
                value = event.get("registry", {}).get("value", "")
                
                if key:
                    hive = key.split("\\")[0] if "\\" in key else "Unknown"
                    if hive not in hives:
                        hives[hive] = []
                    hives[hive].append({
                        "key": key,
                        "value": value
                    })
        
        return hives
    
    def _get_report_filename(self, report_data: Dict[str, Any]) -> str:
        """Generate a filename for the report."""
        filename = report_data.get("file_info", {}).get("filename", "unknown")
        # Sanitize filename for filesystem
        safe_filename = "".join(c for c in filename if c.isalnum() or c in ('-', '_', '.')).lower()
        return f"{safe_filename}-analysis"
    
    async def _update_reports_index(self, report_data: Dict[str, Any], report_filename: str):
        """Update the reports index with the new report."""
        index_file = self.reports_dir / "index.rst"
        
        # Read current index
        if index_file.exists():
            with open(index_file, 'r', encoding='utf-8') as f:
                content = f.read()
        else:
            content = ""
        
        # Add new report to toctree if not already present
        report_entry = f"   reports/{report_filename}"
        if report_entry not in content:
            # Find the toctree section and add the new report
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "reports/notepadpp-analysis" in line:
                    lines.insert(i + 1, report_entry)
                    break
            
            # Write updated index
            with open(index_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
    
    async def _build_html(self) -> str:
        """Build HTML documentation using Sphinx."""
        try:
            print("🔨 Building Sphinx HTML documentation...")
            
            # Run sphinx-build
            cmd = [
                "sphinx-build",
                "-b", "html",
                str(self.reports_dir),
                str(self.build_dir / "html")
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=str(self.reports_dir.parent)
            )
            
            if result.returncode == 0:
                html_path = self.build_dir / "html" / "index.html"
                print(f"✅ HTML documentation built: {html_path}")
                return str(html_path)
            else:
                print(f"❌ Sphinx build failed: {result.stderr}")
                return ""
                
        except Exception as e:
            print(f"❌ HTML build exception: {e}")
            return ""
    
    async def generate_all_reports(self) -> Dict[str, Any]:
        """Generate reports for all available UUIDs."""
        try:
            print("📊 Generating all available reports...")
            
            # Get all UUIDs from Elasticsearch
            query = {
                "aggs": {
                    "unique_uuids": {
                        "terms": {
                            "field": "file_uuid.keyword",
                            "size": 1000
                        }
                    }
                },
                "size": 0
            }
            
            result = await self.es_client.search(
                index="turdparty-*",
                body=query
            )
            
            uuids = [bucket["key"] for bucket in result["aggregations"]["unique_uuids"]["buckets"]]
            
            print(f"📋 Found {len(uuids)} unique UUIDs to process")
            
            # Generate reports for each UUID
            results = []
            for uuid in uuids:
                result = await self.generate_report_from_uuid(uuid)
                results.append(result)
            
            successful = len([r for r in results if r.get("success")])
            
            print(f"✅ Generated {successful}/{len(uuids)} reports successfully")
            
            return {
                "success": True,
                "total_uuids": len(uuids),
                "successful_reports": successful,
                "results": results
            }
            
        except Exception as e:
            print(f"❌ Batch report generation failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def serve_reports(self, host: str = "0.0.0.0", port: int = 8080):
        """Serve the generated reports via HTTP."""
        try:
            print(f"🌐 Starting report server on http://{host}:{port}")
            
            # Use Python's built-in HTTP server
            import http.server
            import socketserver
            import os
            
            os.chdir(self.build_dir / "html")
            
            handler = http.server.SimpleHTTPRequestHandler
            with socketserver.TCPServer((host, port), handler) as httpd:
                print(f"✅ Report server running at http://{host}:{port}")
                httpd.serve_forever()
                
        except Exception as e:
            print(f"❌ Failed to start report server: {e}")
    
    async def cleanup(self):
        """Clean up resources."""
        await self.api_client.aclose()
        await self.es_client.close()

# CLI interface
async def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="TurdParty Sphinx Report Generator")
    parser.add_argument("--uuid", help="Generate report for specific UUID")
    parser.add_argument("--all", action="store_true", help="Generate all reports")
    parser.add_argument("--serve", action="store_true", help="Serve reports via HTTP")
    parser.add_argument("--port", type=int, default=8080, help="Server port")
    
    args = parser.parse_args()
    
    generator = SphinxReportGenerator()
    
    try:
        if args.uuid:
            result = await generator.generate_report_from_uuid(args.uuid)
            print(json.dumps(result, indent=2))
        elif args.all:
            result = await generator.generate_all_reports()
            print(json.dumps(result, indent=2))
        elif args.serve:
            await generator.serve_reports(port=args.port)
        else:
            print("Please specify --uuid, --all, or --serve")
    
    finally:
        await generator.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
