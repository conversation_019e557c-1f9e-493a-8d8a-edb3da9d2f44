/* Dark Mode Error Pages */
:root {
    --bg-primary: #0d1117;
    --bg-secondary: #161b22;
    --bg-tertiary: #21262d;
    --text-primary: #f0f6fc;
    --text-secondary: #8b949e;
    --accent-color: #7c4dff;
    --accent-hover: #9575ff;
    --border-color: #30363d;
    --error-color: #da3633;
    --warning-color: #d29922;
    --success-color: #238636;
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    --card-bg: linear-gradient(135deg, #161b22 0%, #21262d 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.error-container {
    max-width: 800px;
    width: 100%;
}

.error-content {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 3rem;
    box-shadow: var(--shadow);
    text-align: center;
}

.error-header {
    margin-bottom: 2rem;
}

.error-code {
    font-size: 4rem;
    font-weight: 700;
    color: var(--error-color);
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(218, 54, 51, 0.3);
}

.error-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--accent-color), #bb86fc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.error-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 400;
}

.error-description {
    margin-bottom: 2rem;
    text-align: left;
}

.error-description h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.error-message {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    color: var(--text-secondary);
    line-height: 1.7;
}

.error-details {
    margin-bottom: 2rem;
    padding: 1rem;
    background: rgba(124, 77, 255, 0.1);
    border: 1px solid rgba(124, 77, 255, 0.3);
    border-radius: 8px;
}

.error-details code {
    background: var(--bg-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    color: var(--accent-color);
    font-size: 0.9rem;
}

.error-actions {
    margin-bottom: 2rem;
}

.home-link {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.home-link:hover {
    color: var(--accent-hover);
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-color), #bb86fc);
    color: white;
    box-shadow: 0 4px 12px rgba(124, 77, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(124, 77, 255, 0.4);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    border-color: var(--accent-color);
    transform: translateY(-2px);
}

.error-footer {
    border-top: 1px solid var(--border-color);
    padding-top: 1.5rem;
    color: var(--text-secondary);
}

.error-footer p {
    margin-bottom: 0.5rem;
}

.error-joke {
    font-style: italic;
    opacity: 0.8;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 1rem;
    }
    
    .error-content {
        padding: 2rem;
    }
    
    .error-code {
        font-size: 3rem;
    }
    
    .error-title {
        font-size: 1.5rem;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 250px;
    }
}

/* Animation for error code */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.error-code {
    animation: pulse 2s infinite ease-in-out;
}
