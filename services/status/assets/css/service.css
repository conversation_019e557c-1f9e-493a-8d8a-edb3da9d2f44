/* Dark Mode Service Status Page */
:root {
    --bg-primary: #0d1117;
    --bg-secondary: #161b22;
    --bg-tertiary: #21262d;
    --text-primary: #f0f6fc;
    --text-secondary: #8b949e;
    --accent-color: #7c4dff;
    --accent-hover: #9575ff;
    --border-color: #30363d;
    --success-color: #238636;
    --warning-color: #d29922;
    --error-color: #da3633;
    --operational-color: #238636;
    --degraded-color: #d29922;
    --outage-color: #da3633;
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    --card-bg: linear-gradient(135deg, #161b22 0%, #21262d 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

/* Header */
.service-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
}

.service-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--accent-color), #bb86fc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 400;
}

/* Section Styling */
.service-status-section,
.metrics-section,
.activity-section {
    margin-bottom: 3rem;
    padding: 2rem;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
}

.service-status-section h2,
.metrics-section h2,
.activity-section h2 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
}

/* Service Grid */
.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.service-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.service-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(124, 77, 255, 0.2);
    border-color: var(--accent-color);
}

.service-header-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.service-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.service-status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.service-status-badge.operational {
    background: rgba(35, 134, 54, 0.2);
    color: var(--operational-color);
    border: 1px solid var(--operational-color);
}

.service-status-badge.degraded {
    background: rgba(210, 153, 34, 0.2);
    color: var(--degraded-color);
    border: 1px solid var(--degraded-color);
}

.service-status-badge.outage {
    background: rgba(218, 54, 51, 0.2);
    color: var(--outage-color);
    border: 1px solid var(--outage-color);
}

.service-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.service-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.service-detail {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
}

.service-detail-label {
    color: var(--text-secondary);
}

.service-detail-value {
    color: var(--text-primary);
    font-weight: 500;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.metric-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(124, 77, 255, 0.2);
}

.metric-card:hover::before {
    opacity: 1;
}

.task-metric::before {
    background: linear-gradient(90deg, #238636, #2ea043);
}

.performance-metric::before {
    background: linear-gradient(90deg, #d29922, #fb8500);
}

.metric-header {
    margin-bottom: 1rem;
}

.metric-title {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--accent-color), #bb86fc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.metric-label,
.metric-subtitle {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
}

/* Activity Feed */
.activity-feed {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: rgba(124, 77, 255, 0.1);
    border-color: var(--accent-color);
}

.activity-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.activity-icon.success {
    background: rgba(35, 134, 54, 0.2);
    color: var(--success-color);
}

.activity-icon.warning {
    background: rgba(210, 153, 34, 0.2);
    color: var(--warning-color);
}

.activity-icon.error {
    background: rgba(218, 54, 51, 0.2);
    color: var(--error-color);
}

.activity-content {
    flex: 1;
}

.activity-service {
    font-weight: 600;
    color: var(--accent-color);
    font-size: 0.9rem;
}

.activity-message {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin-top: 0.25rem;
}

.activity-time {
    color: var(--text-secondary);
    font-size: 0.8rem;
    opacity: 0.7;
    flex-shrink: 0;
}

/* Footer */
.service-footer {
    margin-top: 3rem;
    padding: 2rem 0;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.footer-links {
    display: flex;
    gap: 1.5rem;
}

.footer-links a {
    color: var(--accent-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--accent-hover);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .service-grid,
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .metric-value {
        font-size: 2rem;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
}
