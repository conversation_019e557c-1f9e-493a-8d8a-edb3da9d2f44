// Celery Integration for TurdParty Status Dashboard
// Extends the existing status page with Celery worker and task metrics

class CeleryStatusIntegration {
    constructor() {
        this.celeryServices = [
            {
                name: 'Celery Workers',
                component: 'celery_workers',
                description: 'Background Task Workers',
                icon: '👷',
                endpoint: 'http://api.turdparty.localhost/api/v1/health/celery'
            },
            {
                name: 'Scheduled Tasks',
                component: 'scheduled_tasks',
                description: 'Automated Maintenance Tasks',
                icon: '⏰',
                endpoint: 'http://api.turdparty.localhost/api/v1/health/tasks'
            }
        ];
        
        this.taskMetrics = {
            success_rate: 0,
            total_executions: 0,
            failed_executions: 0,
            active_workers: 0
        };
        
        this.updateInterval = null;
    }

    async initialize() {
        console.log('Initializing Celery status integration...');
        
        // Add Celery components to the existing status page
        this.addCeleryComponents();
        
        // Start monitoring
        await this.updateCeleryStatus();
        this.startPeriodicUpdates();
        
        console.log('Celery status integration initialized');
    }

    addCeleryComponents() {
        const servicesGrid = document.querySelector('.services-grid');
        if (!servicesGrid) {
            console.warn('Services grid not found, cannot add Celery components');
            return;
        }

        // Add Celery services to the grid
        this.celeryServices.forEach(service => {
            const serviceCard = this.createServiceCard(service);
            servicesGrid.appendChild(serviceCard);
        });

        // Add task metrics section
        this.addTaskMetricsSection();
    }

    createServiceCard(service) {
        const card = document.createElement('div');
        card.className = 'service-card';
        card.id = `service-${service.component}`;
        
        card.innerHTML = `
            <div class="service-header">
                <span class="service-icon">${service.icon}</span>
                <div class="service-info">
                    <h3 class="service-name">${service.name}</h3>
                    <p class="service-description">${service.description}</p>
                </div>
                <div class="service-status">
                    <span class="status-indicator checking" id="status-${service.component}"></span>
                    <span class="status-text" id="status-text-${service.component}">Checking...</span>
                </div>
            </div>
            <div class="service-details" id="details-${service.component}">
                <div class="detail-item">
                    <span class="detail-label">Status:</span>
                    <span class="detail-value" id="detail-status-${service.component}">Checking...</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Last Check:</span>
                    <span class="detail-value" id="detail-time-${service.component}">-</span>
                </div>
            </div>
        `;
        
        return card;
    }

    addTaskMetricsSection() {
        const container = document.querySelector('.container');
        if (!container) return;

        // Find the services section and add metrics after it
        const servicesSection = document.querySelector('.services-section');
        if (!servicesSection) return;

        const metricsSection = document.createElement('section');
        metricsSection.className = 'metrics-section';
        metricsSection.innerHTML = `
            <h2>📊 Task Metrics</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="metric-success-rate">-</div>
                    <div class="metric-label">Success Rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="metric-total-tasks">-</div>
                    <div class="metric-label">Total Tasks (24h)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="metric-failed-tasks">-</div>
                    <div class="metric-label">Failed Tasks</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="metric-active-workers">-</div>
                    <div class="metric-label">Active Workers</div>
                </div>
            </div>
        `;

        // Insert after services section
        servicesSection.parentNode.insertBefore(metricsSection, servicesSection.nextSibling);
    }

    async updateCeleryStatus() {
        console.log('Updating Celery status...');

        for (const service of this.celeryServices) {
            await this.checkCeleryService(service);
        }

        await this.updateTaskMetrics();
    }

    async checkCeleryService(service) {
        const statusIndicator = document.getElementById(`status-${service.component}`);
        const statusText = document.getElementById(`status-text-${service.component}`);
        const detailStatus = document.getElementById(`detail-status-${service.component}`);
        const detailTime = document.getElementById(`detail-time-${service.component}`);

        if (!statusIndicator || !statusText) return;

        try {
            // Set checking state
            statusIndicator.className = 'status-indicator checking';
            statusText.textContent = 'Checking...';

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch(service.endpoint, {
                signal: controller.signal,
                mode: 'cors',
                headers: { 'Accept': 'application/json' }
            });

            clearTimeout(timeoutId);

            let status = 'outage';
            let message = 'Service unavailable';
            let details = {};

            if (response.ok) {
                const data = await response.json();
                
                if (service.component === 'celery_workers') {
                    const workerCount = data.dependencies?.celery === 'healthy' ? 
                        (data.worker_count || 1) : 0;
                    
                    status = workerCount > 0 ? 'operational' : 'outage';
                    message = workerCount > 0 ? 
                        `${workerCount} workers active` : 
                        'No workers available';
                    
                    this.taskMetrics.active_workers = workerCount;
                    
                } else if (service.component === 'scheduled_tasks') {
                    // This would come from our Celery task metrics
                    status = 'operational'; // Default, will be updated by task metrics
                    message = 'Tasks running';
                }

                details = data;
            }

            // Update UI
            statusIndicator.className = `status-indicator ${status}`;
            statusText.textContent = message;
            
            if (detailStatus) detailStatus.textContent = status;
            if (detailTime) detailTime.textContent = new Date().toLocaleTimeString();

            console.log(`${service.name}: ${status} - ${message}`);

        } catch (error) {
            console.error(`Error checking ${service.name}:`, error);
            
            statusIndicator.className = 'status-indicator outage';
            statusText.textContent = 'Connection failed';
            
            if (detailStatus) detailStatus.textContent = 'error';
            if (detailTime) detailTime.textContent = new Date().toLocaleTimeString();
        }
    }

    async updateTaskMetrics() {
        try {
            // This would typically fetch from a Celery metrics endpoint
            // For now, we'll simulate or use logged data
            
            const successRateEl = document.getElementById('success-rate');
            const totalTasksEl = document.getElementById('total-tasks');
            const failedTasksEl = document.getElementById('failed-tasks');
            const activeWorkersEl = document.getElementById('active-workers');

            if (successRateEl) {
                successRateEl.textContent = `${this.taskMetrics.success_rate.toFixed(1)}%`;
                successRateEl.className = `metric-value ${this.getSuccessRateClass(this.taskMetrics.success_rate)}`;
            }
            
            if (totalTasksEl) {
                totalTasksEl.textContent = this.taskMetrics.total_executions.toString();
            }
            
            if (failedTasksEl) {
                failedTasksEl.textContent = this.taskMetrics.failed_executions.toString();
                failedTasksEl.className = `metric-value ${this.taskMetrics.failed_executions > 5 ? 'warning' : 'good'}`;
            }
            
            if (activeWorkersEl) {
                activeWorkersEl.textContent = this.taskMetrics.active_workers.toString();
                activeWorkersEl.className = `metric-value ${this.taskMetrics.active_workers > 0 ? 'good' : 'error'}`;
            }

        } catch (error) {
            console.error('Error updating task metrics:', error);
        }
    }

    getSuccessRateClass(rate) {
        if (rate >= 95) return 'good';
        if (rate >= 80) return 'warning';
        return 'error';
    }

    startPeriodicUpdates() {
        // Update every 30 seconds
        this.updateInterval = setInterval(() => {
            this.updateCeleryStatus();
        }, 30000);
    }

    stop() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    // Method to update metrics from external sources (e.g., WebSocket, polling)
    updateMetrics(metrics) {
        this.taskMetrics = { ...this.taskMetrics, ...metrics };
        this.updateTaskMetrics();
    }
}

// Initialize Celery integration when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit for the main status page to initialize
    setTimeout(() => {
        window.celeryIntegration = new CeleryStatusIntegration();
        window.celeryIntegration.initialize();
    }, 1000);
});

// Export for external use
window.CeleryStatusIntegration = CeleryStatusIntegration;
