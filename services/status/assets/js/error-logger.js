// TurdParty Status Dashboard - JavaScript Error Logger
// Comprehensive error logging to Elasticsearch via API

class ErrorLogger {
    constructor() {
        this.apiEndpoint = '/api/v1/logs/ui-error';
        this.sessionId = this.generateSessionId();
        this.errorQueue = [];
        this.isOnline = navigator.onLine;
        this.maxRetries = 3;
        this.retryDelay = 1000; // 1 second
        
        this.setupEventListeners();
        this.setupPeriodicFlush();
        
        console.log('🔍 TurdParty Error Logger initialized');
    }
    
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    setupEventListeners() {
        // Global error handler for uncaught exceptions
        window.addEventListener('error', (event) => {
            this.logError({
                type: 'javascript_error',
                message: event.message || 'Unknown error',
                source: event.filename || 'unknown',
                line: event.lineno || 0,
                column: event.colno || 0,
                stack: event.error ? event.error.stack : null,
                timestamp: new Date().toISOString(),
                url: window.location.href,
                userAgent: navigator.userAgent,
                sessionId: this.sessionId,
                severity: 'error'
            });
        });
        
        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));
            
            this.logError({
                type: 'unhandled_promise_rejection',
                message: error.message || 'Unhandled promise rejection',
                source: 'promise',
                stack: error.stack || null,
                reason: event.reason,
                timestamp: new Date().toISOString(),
                url: window.location.href,
                userAgent: navigator.userAgent,
                sessionId: this.sessionId,
                severity: 'error'
            });
        });
        
        // Network status monitoring
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.flushErrorQueue();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
        });
        
        // Page visibility change (for cleanup)
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'hidden') {
                this.flushErrorQueue();
            }
        });
        
        // Before page unload
        window.addEventListener('beforeunload', () => {
            this.flushErrorQueue();
        });
    }
    
    setupPeriodicFlush() {
        // Flush error queue every 30 seconds
        setInterval(() => {
            if (this.errorQueue.length > 0) {
                this.flushErrorQueue();
            }
        }, 30000);
    }
    
    logError(errorData) {
        // Add additional context
        const enrichedError = {
            ...errorData,
            component: 'status-dashboard',
            service: 'turdparty-status',
            environment: window.location.hostname.includes('localhost') ? 'development' : 'production',
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            performance: this.getPerformanceMetrics(),
            memoryUsage: this.getMemoryUsage()
        };
        
        // Add to queue
        this.errorQueue.push(enrichedError);
        
        // Log to console in development
        if (window.location.hostname.includes('localhost')) {
            console.error('🚨 Error logged:', enrichedError);
        }
        
        // Try to send immediately if online and queue is getting large
        if (this.isOnline && this.errorQueue.length >= 5) {
            this.flushErrorQueue();
        }
    }
    
    getPerformanceMetrics() {
        if (!window.performance) return null;
        
        const navigation = performance.getEntriesByType('navigation')[0];
        return {
            loadTime: navigation ? navigation.loadEventEnd - navigation.loadEventStart : null,
            domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : null,
            timeOrigin: performance.timeOrigin,
            now: performance.now()
        };
    }
    
    getMemoryUsage() {
        if (!window.performance || !window.performance.memory) return null;
        
        return {
            usedJSHeapSize: performance.memory.usedJSHeapSize,
            totalJSHeapSize: performance.memory.totalJSHeapSize,
            jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
        };
    }
    
    async flushErrorQueue() {
        if (this.errorQueue.length === 0 || !this.isOnline) {
            return;
        }
        
        const errorsToSend = [...this.errorQueue];
        this.errorQueue = [];
        
        try {
            await this.sendErrors(errorsToSend);
        } catch (error) {
            // If sending fails, put errors back in queue for retry
            this.errorQueue.unshift(...errorsToSend);
            console.warn('Failed to send errors to backend:', error);
        }
    }
    
    async sendErrors(errors, retryCount = 0) {
        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Component': 'status-dashboard'
                },
                body: JSON.stringify({
                    errors: errors,
                    metadata: {
                        sessionId: this.sessionId,
                        timestamp: new Date().toISOString(),
                        count: errors.length
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            console.log(`✅ Successfully sent ${errors.length} errors to backend`);

        } catch (error) {
            // Silently fail if API endpoint is not available (development mode)
            if (window.location.hostname.includes('localhost')) {
                console.warn(`⚠️ Error logging API not available (${error.message}). Errors will be logged to console only.`);
                return; // Don't retry in development if API is not available
            }

            if (retryCount < this.maxRetries) {
                console.warn(`Retrying error send (attempt ${retryCount + 1}/${this.maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, this.retryDelay * (retryCount + 1)));
                return this.sendErrors(errors, retryCount + 1);
            } else {
                console.warn(`Failed to send errors after ${this.maxRetries} attempts:`, error.message);
                // Don't throw error to prevent blocking other JavaScript
            }
        }
    }
    
    // Public method to manually log custom errors
    logCustomError(message, source, additionalData = {}) {
        this.logError({
            type: 'custom_error',
            message: message,
            source: source,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            sessionId: this.sessionId,
            severity: 'warning',
            ...additionalData
        });
    }
    
    // Public method to log warnings
    logWarning(message, source, additionalData = {}) {
        this.logError({
            type: 'warning',
            message: message,
            source: source,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            sessionId: this.sessionId,
            severity: 'warning',
            ...additionalData
        });
    }
    
    // Public method to log info events
    logInfo(message, source, additionalData = {}) {
        this.logError({
            type: 'info',
            message: message,
            source: source,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            sessionId: this.sessionId,
            severity: 'info',
            ...additionalData
        });
    }
}

// Initialize global error logger with error handling
try {
    window.TurdPartyErrorLogger = new ErrorLogger();
} catch (error) {
    console.warn('⚠️ Failed to initialize error logger:', error.message);
    // Create a minimal fallback logger that doesn't break other scripts
    window.TurdPartyErrorLogger = {
        logError: () => {},
        logCustomError: () => {},
        logWarning: () => {},
        logInfo: () => {}
    };
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ErrorLogger;
}
