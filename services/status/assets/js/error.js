class ErrorPageHandler {
    constructor() {
        this.errorMessages = [
            // Technical Messages
            "Something went wrong on our servers while we were processing your request. The requested resource could not be found but may be available again in the future. This occurrence has been logged, and a highly trained team of monkeys has been dispatched to deal with your problem. We're really sorry about this, and will work hard to get this resolved as soon as possible.",
            
            "Our servers are currently experiencing some technical difficulties. It's like trying to flush a toilet that's already clogged - sometimes you just need to wait for the plumber. Our engineering team has been notified and is working to resolve this issue.",
            
            "The page you're looking for seems to have vanished into the digital void. It's probably hanging out with all those missing socks from your laundry. Don't worry, our team is on the case and will have everything back to normal soon.",
            
            "We've encountered an unexpected error in our system. Think of it as digital indigestion - sometimes the servers just need a moment to process everything properly. Please try again in a few minutes.",
            
            "Our database is currently taking a coffee break. Even the most sophisticated systems need their caffeine fix. We'll have everything running smoothly again shortly.",
            
            "The requested resource has gone on an unscheduled vacation. We're trying to convince it to come back to work, but you know how these digital entities can be. Please check back later.",
            
            "We're experiencing some turbulence in our data streams. It's like trying to navigate through a digital storm - sometimes you just have to wait for the weather to clear up.",
            
            "Our servers are currently playing hide and seek with your request. Unfortunately, they're really good at hiding. Our team is working to convince them to come out and play nicely.",
            
            "The page you're looking for is currently in witness protection. For security reasons, we can't reveal its location, but we're working on a safe relocation program.",
            
            "We've hit a snag in our digital pipeline. It's like having a hairball in your drain - messy, inconvenient, but totally fixable. Our maintenance crew is on it.",
            
            "Our system is currently experiencing what we like to call 'digital constipation.' Everything is backed up, but we're working on getting things flowing smoothly again.",
            
            "The requested page seems to have been abducted by aliens. We've contacted the appropriate authorities (NASA and our IT department) to negotiate its safe return.",
            
            "We're currently debugging a particularly stubborn gremlin in our system. These digital creatures are notoriously difficult to catch, but our team has experience in gremlin wrangling.",
            
            // Humorous Public Defecation Stories
            "Our servers are currently experiencing what happened to Dave at the company picnic last summer. He thought he could make it to the restroom, but nature had other plans. Right there by the potato salad table, Dave learned that sometimes you just can't hold it. Our servers learned the same lesson today - sometimes you just have to let it all out, regardless of timing or location. We're cleaning up the mess and should be back online shortly.",
            
            "This error reminds us of the legendary incident at Central Park when Margaret, a distinguished 67-year-old librarian, was feeding pigeons on her lunch break. A sudden bout of food poisoning from questionable sushi hit her like a freight train. With no public facilities in sight and a growing crowd of tourists, Margaret made a executive decision that would become park folklore. She discreetly positioned herself behind a large oak tree and... well, let's just say the pigeons got more than breadcrumbs that day. Like Margaret, our servers sometimes have to make emergency decisions. We're working on better contingency planning."
        ];
        
        this.init();
    }
    
    init() {
        this.displayRandomMessage();
        this.generateRandomErrorCode();
    }
    
    displayRandomMessage() {
        const messageElement = document.getElementById('error-message');
        if (messageElement) {
            const randomIndex = Math.floor(Math.random() * this.errorMessages.length);
            const selectedMessage = this.errorMessages[randomIndex];
            
            messageElement.innerHTML = `<div class="error-message">${selectedMessage}</div>`;
        }
    }
    
    generateRandomErrorCode() {
        const codeElement = document.getElementById('error-code');
        if (codeElement) {
            const errorCode = this.generateUUID();
            codeElement.textContent = errorCode;
        }
    }
    
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
}

// Initialize error page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ErrorPageHandler();
});
