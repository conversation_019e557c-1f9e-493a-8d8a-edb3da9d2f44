<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💩🎉 TurdParty Service Status</title>
    <link rel="stylesheet" href="assets/css/service.css?v=1.0">
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="service-header">
            <div class="header-content">
                <h1>💩🎉 TurdParty Service Status</h1>
                <p class="header-subtitle">Real-time monitoring of all system components</p>
            </div>
        </header>

        <!-- Service Status Grid -->
        <section class="service-status-section">
            <h2>🚀 Service Status</h2>
            <div class="service-grid" id="service-grid">
                <!-- Services will be populated by JavaScript -->
            </div>
        </section>

        <!-- Task Metrics -->
        <section class="metrics-section">
            <h2>📊 Task Metrics</h2>
            <div class="metrics-grid">
                <div class="metric-card task-metric">
                    <div class="metric-value" id="success-rate">0.0%</div>
                    <div class="metric-label">Success Rate</div>
                </div>
                <div class="metric-card task-metric">
                    <div class="metric-value" id="total-tasks">0</div>
                    <div class="metric-label">Total Tasks (24h)</div>
                </div>
                <div class="metric-card task-metric">
                    <div class="metric-value" id="failed-tasks">0</div>
                    <div class="metric-label">Failed Tasks</div>
                </div>
                <div class="metric-card task-metric">
                    <div class="metric-value" id="active-workers">0</div>
                    <div class="metric-label">Active Workers</div>
                </div>
            </div>
        </section>

        <!-- Performance Metrics -->
        <section class="metrics-section">
            <h2>⚡ Performance Metrics</h2>
            <div class="metrics-grid">
                <div class="metric-card performance-metric">
                    <div class="metric-header">
                        <span class="metric-title">Throughput</span>
                    </div>
                    <div class="metric-value" id="throughput">1.35M ops/s</div>
                    <div class="metric-subtitle">Model Operations</div>
                </div>
                <div class="metric-card performance-metric">
                    <div class="metric-header">
                        <span class="metric-title">VM Boot Time</span>
                    </div>
                    <div class="metric-value" id="vm-boot">12s</div>
                    <div class="metric-subtitle">Container Startup</div>
                </div>
                <div class="metric-card performance-metric">
                    <div class="metric-header">
                        <span class="metric-title">File Operations</span>
                    </div>
                    <div class="metric-value" id="file-ops">0.8s</div>
                    <div class="metric-subtitle">Upload/Download</div>
                </div>
                <div class="metric-card performance-metric">
                    <div class="metric-header">
                        <span class="metric-title">Test Success</span>
                    </div>
                    <div class="metric-value" id="test-success">63/63</div>
                    <div class="metric-subtitle">Tests Passing</div>
                </div>
            </div>
        </section>

        <!-- Recent Activity -->
        <section class="activity-section">
            <h2>📈 Recent Activity</h2>
            <div class="activity-feed" id="activity-feed">
                <!-- Activity will be populated by JavaScript -->
            </div>
        </section>

        <!-- Footer -->
        <footer class="service-footer">
            <div class="footer-content">
                <p>&copy; 2024 💩🎉 TurdParty Binary Analysis Platform</p>
                <div class="footer-links">
                    <a href="/">Main Dashboard</a>
                    <a href="/docs">Documentation</a>
                    <a href="/api/v1/health">API Health</a>
                    <a href="http://kibana.turdparty.localhost">Kibana Dashboard</a>
                </div>
            </div>
        </footer>
    </div>

    <script src="assets/js/service.js?v=1.0"></script>
</body>
</html>
