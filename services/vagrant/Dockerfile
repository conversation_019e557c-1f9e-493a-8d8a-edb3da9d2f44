# Latest LTS Ubuntu
ARG BASE_IMAGE=ubuntu:24.04
FROM $BASE_IMAGE

# [FOR DOCKERFILE ONLY] Skip interactive prompt questions, like selecting your geographic area
ENV DEBIAN_FRONTEND noninteractive
ENV DOCKER_CLI_HINTS false

# 1. --- Setup environment ---

# Install system requirements
RUN apt update -y
RUN apt upgrade -y
RUN apt install software-properties-common keychain curl wget -y
RUN apt update -y

# Install packer & vagrant
RUN wget -O - https://apt.releases.hashicorp.com/gpg | gpg --dearmor -o /usr/share/keyrings/hashicorp-archive-keyring.gpg
RUN echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(lsb_release -cs) main" | tee /etc/apt/sources.list.d/hashicorp.list
RUN apt update && apt install vagrant packer -y

# Dependencies for vagrant-libvirt plugin
RUN apt install -y qemu-system libvirt-daemon-system libvirt-dev ebtables libguestfs-tools
RUN apt install -y ruby-fog-libvirt make build-essential
# Install vagrant-libvirt plugin
RUN vagrant plugin install vagrant-libvirt

# Install rsync (needed for vagrant file sync)
RUN apt install -y rsync

# Install additional tools for VM management
RUN apt install -y openssh-client sshpass netcat-openbsd

# Create vagrant user and setup environment
RUN useradd -m -s /bin/bash vagrant
RUN usermod -aG libvirt vagrant
RUN usermod -aG kvm vagrant

# Setup working directory
WORKDIR /vagrant
RUN chown vagrant:vagrant /vagrant

# Copy entrypoint script
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# Expose gRPC port for VM communication
EXPOSE 40000

# Switch to vagrant user
USER vagrant

# Set default command
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["vagrant", "serve", "--host", "0.0.0.0", "--port", "40000"]
