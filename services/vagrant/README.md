# TurdParty Vagrant Integration

This directory contains the Docker container implementation for Vagrant integration with TurdParty. It provides an alternative to running `vagrant serve` on the host system.

## Overview

TurdParty supports two modes for Vagrant integration:

1. **Host Mode** (default): Uses `vagrant serve` running on the host system
2. **Container Mode**: Uses a Docker container with Vagrant and libvirt

## Quick Start

### Host Mode (Default)

```bash
# Install Vagrant on your host system
# Start Vagrant serve
vagrant serve --host 0.0.0.0 --port 40000

# Set environment (optional, this is the default)
echo "VAGRANT_MODE=host" >> .env
```

### Container Mode

```bash
# Switch to container mode
./scripts/vagrant-mode.sh container

# Or manually
echo "VAGRANT_MODE=container" >> .env
COMPOSE_PROFILES=vagrant-container docker-compose up -d vagrant
```

## Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `VAGRANT_MODE` | `host` | Mode: `host` or `container` |
| `VAGRANT_GRPC_PORT` | `40000` | gRPC port for VM communication |
| `VAGRANT_HOST` | `localhost` | Host address (host mode only) |

## Container Details

### Base Image
- **Ubuntu 24.04 LTS** - Latest stable Ubuntu
- **Vagrant** - Latest version from HashiCorp repository
- **Packer** - For VM template building
- **libvirt** - KVM/QEMU virtualization
- **vagrant-libvirt** - Vagrant libvirt provider plugin

### Features
- **Privileged container** - Required for VM management
- **libvirt integration** - Full KVM/QEMU support
- **Automatic setup** - Ready-to-use Vagrant environment
- **Health checks** - Container health monitoring
- **Volume persistence** - Vagrant data persisted across restarts

### Ports
- **40000** - Vagrant gRPC service
- **50051** - Alternative gRPC port

### Volumes
- `/vagrant` - Vagrant working directory
- `/vagrant/.vagrant` - Vagrant state (persistent volume)
- `/var/run/libvirt` - libvirt socket (host sharing)

## Usage Examples

### Check Current Mode
```bash
./scripts/vagrant-mode.sh status
```

### Switch to Container Mode
```bash
./scripts/vagrant-mode.sh container
```

### Switch to Host Mode
```bash
./scripts/vagrant-mode.sh host
# Then run: vagrant serve --host 0.0.0.0 --port 40000
```

### Start Container Manually
```bash
COMPOSE_PROFILES=vagrant-container docker-compose up -d vagrant
```

### Container Health Check
```bash
docker exec turdpartycollab_vagrant /usr/local/bin/entrypoint.sh health
```

### Access Container Shell
```bash
docker exec -it turdpartycollab_vagrant bash
```

## Vagrant Configuration

The container includes a default `Vagrantfile` optimized for TurdParty:

### VM Configuration
- **Memory**: 2GB RAM
- **CPUs**: 2 cores
- **Provider**: libvirt (KVM/QEMU)
- **Box**: generic/ubuntu2004

### Network Ports
- **22** → **2222** - SSH access
- **40000** → **40000** - TurdParty gRPC
- **50051** → **50051** - Alternative gRPC
- **80** → **8080** - HTTP
- **443** → **8443** - HTTPS
- **3389** → **3389** - RDP (Windows VMs)

### Shared Folders
- `./shared` → `/vagrant/shared` - File exchange
- `./samples` → `/vagrant/samples` - Malware samples

### TurdParty Agent
The VM is automatically provisioned with:
- **Python 3** and monitoring packages
- **TurdParty monitoring agent** (`/opt/turdparty/agent/monitor.py`)
- **Systemd service** (`turdparty-agent.service`)
- **Log files** (`/var/log/turdparty/agent.log`)

## Troubleshooting

### Container Won't Start
```bash
# Check Docker daemon
sudo systemctl status docker

# Check libvirt
sudo systemctl status libvirtd

# Check container logs
docker logs turdpartycollab_vagrant
```

### VM Creation Fails
```bash
# Check libvirt connection
docker exec turdpartycollab_vagrant virsh list

# Check Vagrant status
docker exec turdpartycollab_vagrant vagrant status

# Check available boxes
docker exec turdpartycollab_vagrant vagrant box list
```

### gRPC Connection Issues
```bash
# Test port connectivity
nc -zv localhost 40000

# Check Vagrant serve status
docker exec turdpartycollab_vagrant ps aux | grep vagrant

# Check container networking
docker network inspect turdpartycollab_network
```

### Performance Issues
```bash
# Check container resources
docker stats turdpartycollab_vagrant

# Check host resources
free -h
df -h

# Check libvirt performance
sudo virsh list --all
```

## Security Considerations

### Container Security
- **Privileged mode** required for VM management
- **Host libvirt access** for VM creation
- **Network isolation** via Docker networks
- **Volume restrictions** to necessary paths only

### VM Security
- **Isolated networking** via libvirt
- **Snapshot support** for clean VM states
- **Automatic cleanup** after analysis
- **Monitoring agent** with restricted permissions

### Host Security
- **libvirt daemon** properly configured
- **User permissions** for Docker and libvirt groups
- **Firewall rules** for exposed ports
- **Regular updates** for base images

## Development

### Building the Container
```bash
cd services/vagrant
docker build -t turdparty-vagrant .
```

### Testing
```bash
# Test container startup
docker run --rm --privileged turdparty-vagrant health

# Test Vagrant functionality
docker run --rm --privileged -v /var/run/libvirt:/var/run/libvirt turdparty-vagrant vagrant --version
```

### Customization
- Modify `Dockerfile` for additional packages
- Update `entrypoint.sh` for custom initialization
- Customize `Vagrantfile` for different VM configurations

## Integration with TurdParty

The Vagrant service integrates with TurdParty through:

1. **gRPC API** - VM lifecycle management
2. **File sharing** - Sample injection and result extraction
3. **Monitoring** - Real-time VM metrics via agent
4. **ELK integration** - Log forwarding to Elasticsearch

### API Endpoints
- `POST /api/v1/vms/` - Create VM
- `GET /api/v1/vms/{vm_id}` - Get VM status
- `DELETE /api/v1/vms/{vm_id}` - Destroy VM
- `WebSocket /api/v1/vms/{vm_id}/metrics` - Real-time metrics

### Configuration in API
The API automatically detects the Vagrant mode and adjusts endpoints accordingly:

```python
from config.vagrant import get_vagrant_config

config = get_vagrant_config()
grpc_endpoint = config.grpc_endpoint  # Automatically selects host or container
```

This provides seamless switching between host and container modes without code changes.
