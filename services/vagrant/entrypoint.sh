#!/bin/bash
set -e

# TurdParty Vagrant Container Entrypoint
# Provides Vagrant gRPC service for VM management

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[VAGRANT]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[VAGRANT]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[VAGRANT]${NC} $1"
}

log_error() {
    echo -e "${RED}[VAGRANT]${NC} $1"
}

# Initialize libvirt if needed
init_libvirt() {
    log_info "Initializing libvirt service..."
    
    # Start libvirt daemon if not running
    if ! pgrep -x "libvirtd" > /dev/null; then
        log_info "Starting libvirt daemon..."
        sudo service libvirtd start || log_warning "Could not start libvirtd (may already be running)"
    fi
    
    # Check libvirt connection
    if virsh list >/dev/null 2>&1; then
        log_success "Libvirt connection established"
    else
        log_warning "Libvirt connection failed - VMs may not work properly"
    fi
}

# Setup Vagrant environment
setup_vagrant() {
    log_info "Setting up Vagrant environment..."
    
    # Create .vagrant directory if it doesn't exist
    mkdir -p /vagrant/.vagrant
    
    # Set Vagrant home
    export VAGRANT_HOME=/vagrant/.vagrant
    
    # Check if Vagrantfile exists
    if [ ! -f "/vagrant/Vagrantfile" ]; then
        log_warning "No Vagrantfile found - creating default configuration"
        create_default_vagrantfile
    fi
    
    log_success "Vagrant environment ready"
}

# Create a default Vagrantfile for TurdParty
create_default_vagrantfile() {
    cat > /vagrant/Vagrantfile << 'EOF'
# TurdParty Default Vagrantfile
# Configured for malware analysis VMs

Vagrant.configure("2") do |config|
  # Default box - Ubuntu 20.04 LTS
  config.vm.box = "generic/ubuntu2004"
  
  # VM configuration
  config.vm.provider "libvirt" do |libvirt|
    libvirt.memory = 2048
    libvirt.cpus = 2
    libvirt.driver = "kvm"
    libvirt.nested = true
  end
  
  # Network configuration for TurdParty integration
  config.vm.network "forwarded_port", guest: 22, host: 2222, auto_correct: true
  config.vm.network "forwarded_port", guest: 40000, host: 40000, auto_correct: true
  config.vm.network "forwarded_port", guest: 50051, host: 50051, auto_correct: true
  
  # Shared folder for file injection
  config.vm.synced_folder "/vagrant/shared", "/vagrant/shared", create: true
  
  # Provisioning script for TurdParty agent
  config.vm.provision "shell", inline: <<-SHELL
    # Update system
    apt-get update
    apt-get install -y python3 python3-pip curl wget
    
    # Install monitoring dependencies
    pip3 install psutil requests websockets
    
    # Create TurdParty agent directory
    mkdir -p /opt/turdparty
    
    # Setup agent service (placeholder)
    echo "TurdParty VM ready for analysis" > /opt/turdparty/status
  SHELL
end
EOF
    log_info "Created default Vagrantfile for TurdParty"
}

# Health check function
health_check() {
    log_info "Performing health check..."
    
    # Check Vagrant installation
    if vagrant --version >/dev/null 2>&1; then
        log_success "Vagrant: $(vagrant --version)"
    else
        log_error "Vagrant not found or not working"
        return 1
    fi
    
    # Check libvirt plugin
    if vagrant plugin list | grep -q vagrant-libvirt; then
        log_success "vagrant-libvirt plugin installed"
    else
        log_error "vagrant-libvirt plugin not found"
        return 1
    fi
    
    # Check libvirt connection
    if virsh list >/dev/null 2>&1; then
        log_success "Libvirt connection working"
    else
        log_warning "Libvirt connection issues detected"
    fi
    
    return 0
}

# Start Vagrant serve with proper configuration
start_vagrant_serve() {
    local host=${VAGRANT_SERVE_HOST:-"0.0.0.0"}
    local port=${VAGRANT_SERVE_PORT:-"40000"}
    
    log_info "Starting Vagrant serve on ${host}:${port}"
    log_info "Command: vagrant serve --host ${host} --port ${port}"
    
    # Change to vagrant directory
    cd /vagrant
    
    # Start vagrant serve
    exec vagrant serve --host "${host}" --port "${port}"
}

# Main execution
main() {
    log_info "TurdParty Vagrant Container Starting..."
    log_info "Container provides Vagrant gRPC service for VM management"
    
    # Initialize services
    init_libvirt
    setup_vagrant
    
    # Perform health check
    if ! health_check; then
        log_error "Health check failed - some features may not work"
    fi
    
    # Handle different commands
    case "${1:-serve}" in
        "serve")
            start_vagrant_serve
            ;;
        "health")
            health_check && log_success "Health check passed" || log_error "Health check failed"
            ;;
        "init")
            log_success "Vagrant environment initialized"
            ;;
        "bash"|"shell")
            log_info "Starting interactive shell"
            exec /bin/bash
            ;;
        *)
            log_info "Executing command: $*"
            exec "$@"
            ;;
    esac
}

# Run main function
main "$@"
