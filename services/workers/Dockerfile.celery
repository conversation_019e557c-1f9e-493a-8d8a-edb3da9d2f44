# TurdParty Celery Workers Dockerfile
# For file operations, VM management, and injection tasks

FROM python:3.11-slim as builder

# Set build arguments
ARG DEBIAN_FRONTEND=noninteractive

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Production stage
FROM python:3.11-slim as production

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/opt/venv/bin:$PATH" \
    PYTHONPATH="/app"

# Create non-root user
RUN groupadd -r celery && useradd -r -g celery celery

# Install runtime dependencies for VM operations including Vagrant
RUN apt-get update && apt-get install -y \
    curl \
    openssh-client \
    sshpass \
    wget \
    unzip \
    gnupg \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# Install Vagrant
RUN wget -O- https://apt.releases.hashicorp.com/gpg | gpg --dearmor | tee /usr/share/keyrings/hashicorp-archive-keyring.gpg && \
    echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(lsb_release -cs) main" | tee /etc/apt/sources.list.d/hashicorp.list && \
    apt-get update && apt-get install -y vagrant && \
    rm -rf /var/lib/apt/lists/*

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv

# Create application directory
WORKDIR /app

# Copy worker code
COPY tasks/ ./tasks/
COPY celery_app.py ./
COPY elasticsearch_logging.py ./

# Create necessary directories including Vagrant home
RUN mkdir -p /app/logs /app/tmp /home/<USER>/.vagrant.d /tmp/turdparty_vms && \
    chown -R celery:celery /app /home/<USER>/tmp/turdparty_vms

# Switch to non-root user
USER celery

# Health check for Celery worker
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD celery -A celery_app inspect ping -d celery@$HOSTNAME || exit 1

# Default command (will be overridden by docker-compose)
CMD ["celery", "-A", "celery_app", "worker", "--loglevel=info"]
