#!/usr/bin/env python3
"""
🔍 Elasticsearch Logging Configuration for TurdParty Celery Workers

This module provides comprehensive Elasticsearch logging integration for Celery workers,
streaming all logs in ECS (Elastic Common Schema) format for better observability.

Features:
- Real-time log streaming to Elasticsearch
- ECS-compliant log format
- Task execution tracking
- Error and exception logging
- Performance metrics integration
- Structured logging with context
"""

import json
import logging
import logging.handlers
import socket
import sys
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import traceback
import os

import httpx
from celery.signals import (
    task_prerun, task_postrun, task_failure, task_retry,
    worker_ready, worker_shutdown, task_received
)


class ElasticsearchHandler(logging.Handler):
    """Custom logging handler that sends logs to Elasticsearch in ECS format."""
    
    def __init__(self, elasticsearch_url: str, index_prefix: str = "ecs-turdparty-celery"):
        super().__init__()
        self.elasticsearch_url = elasticsearch_url
        self.index_prefix = index_prefix
        self.hostname = socket.gethostname()
        self.worker_id = os.environ.get('CELERY_WORKER_ID', self.hostname)
        
    def emit(self, record):
        """Emit a log record to Elasticsearch."""
        try:
            # Create ECS-compliant log entry
            log_entry = self._create_ecs_log_entry(record)
            
            # Send to Elasticsearch asynchronously
            self._send_to_elasticsearch(log_entry)
            
        except Exception as e:
            # Fallback to stderr if Elasticsearch fails
            print(f"Failed to send log to Elasticsearch: {e}", file=sys.stderr)
    
    def _create_ecs_log_entry(self, record) -> Dict[str, Any]:
        """Create ECS-compliant log entry from logging record."""
        timestamp = datetime.fromtimestamp(record.created, timezone.utc).isoformat().replace('+00:00', 'Z')
        
        # Base ECS structure
        log_entry = {
            "@timestamp": timestamp,
            "ecs": {"version": "8.11.0"},
            "event": {
                "dataset": "turdparty.celery_worker",
                "category": ["process"],
                "type": ["info"],
                "action": "log_message"
            },
            "service": {
                "name": "turdparty-celery-worker",
                "type": "worker",
                "version": "2.0.0"
            },
            "host": {
                "name": self.hostname,
                "id": self.worker_id
            },
            "log": {
                "level": record.levelname,
                "logger": record.name
            },
            "message": record.getMessage(),
            "labels": {
                "environment": "local",
                "component": "celery_worker"
            }
        }
        
        # Add error information if present
        if record.levelno >= logging.ERROR:
            log_entry["event"]["type"] = ["error"]
            log_entry["event"]["outcome"] = "failure"
            
            if record.exc_info:
                log_entry["error"] = {
                    "message": str(record.exc_info[1]) if record.exc_info[1] else "Unknown error",
                    "type": record.exc_info[0].__name__ if record.exc_info[0] else "Exception",
                    "stack_trace": traceback.format_exception(*record.exc_info)
                }
        
        # Add task context if available
        if hasattr(record, 'task_id'):
            log_entry["turdparty"] = {
                "task_id": record.task_id,
                "task_name": getattr(record, 'task_name', 'unknown'),
                "worker_id": self.worker_id
            }
        
        # Add custom fields
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        return log_entry
    
    def _send_to_elasticsearch(self, log_entry: Dict[str, Any]):
        """Send log entry to Elasticsearch."""
        try:
            # Create daily index
            date_suffix = datetime.now(timezone.utc).strftime('%Y.%m.%d')
            index_name = f"{self.index_prefix}-{date_suffix}"
            
            # Use httpx for async HTTP client
            import asyncio
            
            async def _async_send():
                async with httpx.AsyncClient(timeout=5.0) as client:
                    await client.post(
                        f"{self.elasticsearch_url}/{index_name}/_doc",
                        json=log_entry,
                        headers={"Content-Type": "application/json"}
                    )
            
            # Run in event loop if available, otherwise create new one
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # Create task for running loop
                    asyncio.create_task(_async_send())
                else:
                    loop.run_until_complete(_async_send())
            except RuntimeError:
                # No event loop, create new one
                asyncio.run(_async_send())
                
        except Exception as e:
            # Silent fail to avoid logging loops
            pass


class CeleryTaskLogger:
    """Enhanced task logging with Elasticsearch integration."""
    
    def __init__(self, elasticsearch_url: str):
        self.elasticsearch_url = elasticsearch_url
        self.logger = logging.getLogger('celery.task')
        
    def log_task_event(self, event_type: str, task_id: str, task_name: str, 
                      message: str, details: Optional[Dict[str, Any]] = None,
                      error: Optional[str] = None):
        """Log task event with structured data."""
        
        # Create log record with extra context
        extra = {
            'task_id': task_id,
            'task_name': task_name,
            'extra_fields': {
                'turdparty': {
                    'task_id': task_id,
                    'task_name': task_name,
                    'event_type': event_type,
                    'worker_id': socket.gethostname()
                }
            }
        }
        
        if details:
            extra['extra_fields']['details'] = details
        
        if error:
            extra['extra_fields']['error'] = {'message': error}
            self.logger.error(message, extra=extra)
        else:
            self.logger.info(message, extra=extra)


# Global task logger instance
task_logger = None


def setup_elasticsearch_logging(elasticsearch_url: str = "http://elasticsearch:9200"):
    """Set up Elasticsearch logging for Celery workers."""
    global task_logger
    
    # Create Elasticsearch handler
    es_handler = ElasticsearchHandler(elasticsearch_url)
    es_handler.setLevel(logging.INFO)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(es_handler)
    
    # Configure Celery loggers
    celery_loggers = [
        'celery',
        'celery.worker',
        'celery.task',
        'celery.beat',
        'services.workers'
    ]
    
    for logger_name in celery_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.INFO)
        logger.addHandler(es_handler)
    
    # Initialize task logger
    task_logger = CeleryTaskLogger(elasticsearch_url)
    
    print(f"✅ Elasticsearch logging configured for Celery workers")
    print(f"   Elasticsearch URL: {elasticsearch_url}")
    print(f"   Index prefix: ecs-turdparty-celery")


# Celery signal handlers for comprehensive task tracking
@task_received.connect
def task_received_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
    """Log when a task is received."""
    if task_logger:
        task_logger.log_task_event(
            'received', task_id, task.name,
            f"Task received: {task.name}",
            details={
                'args': str(args)[:200] if args else None,
                'kwargs': str(kwargs)[:200] if kwargs else None,
                'sender': str(sender)
            }
        )


@task_prerun.connect
def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
    """Log when a task starts execution."""
    if task_logger:
        task_logger.log_task_event(
            'start', task_id, task.name,
            f"Task started: {task.name}",
            details={
                'args': str(args)[:200] if args else None,
                'kwargs': str(kwargs)[:200] if kwargs else None,
                'start_time': datetime.now(timezone.utc).isoformat()
            }
        )


@task_postrun.connect
def task_postrun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, 
                        retval=None, state=None, **kwds):
    """Log when a task completes successfully."""
    if task_logger:
        task_logger.log_task_event(
            'success', task_id, task.name,
            f"Task completed successfully: {task.name}",
            details={
                'state': state,
                'return_value': str(retval)[:200] if retval else None,
                'end_time': datetime.now(timezone.utc).isoformat()
            }
        )


@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, **kwds):
    """Log when a task fails."""
    if task_logger:
        task_logger.log_task_event(
            'failure', task_id, sender.name if sender else 'unknown',
            f"Task failed: {sender.name if sender else 'unknown'}",
            details={
                'exception_type': type(exception).__name__ if exception else 'Unknown',
                'traceback': str(traceback)[:500] if traceback else None,
                'error_info': str(einfo)[:500] if einfo else None
            },
            error=str(exception) if exception else 'Unknown error'
        )


@task_retry.connect
def task_retry_handler(sender=None, task_id=None, reason=None, einfo=None, **kwds):
    """Log when a task is retried."""
    if task_logger:
        task_logger.log_task_event(
            'retry', task_id, sender.name if sender else 'unknown',
            f"Task retry: {sender.name if sender else 'unknown'}",
            details={
                'reason': str(reason) if reason else None,
                'error_info': str(einfo)[:500] if einfo else None,
                'retry_time': datetime.now(timezone.utc).isoformat()
            }
        )


@worker_ready.connect
def worker_ready_handler(sender=None, **kwds):
    """Log when worker is ready."""
    logging.info(f"🚀 Celery worker ready: {sender.hostname}")


@worker_shutdown.connect
def worker_shutdown_handler(sender=None, **kwds):
    """Log when worker shuts down."""
    logging.info(f"🛑 Celery worker shutting down: {sender.hostname}")
