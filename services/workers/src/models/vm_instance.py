"""VM instance model for workers."""

import enum
import uuid
from datetime import datetime, timedelta, timezone
from typing import Optional

from sqlalchemy import String, Integer, Text, DateTime, Boolean, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship, DeclarativeBase


class Base(DeclarativeBase):
    """Base class for all database models."""
    pass


class VMStatus(enum.Enum):
    """VM lifecycle status."""
    CREATING = "creating"
    RUNNING = "running"
    TERMINATING = "terminating"
    TERMINATED = "terminated"
    FAILED = "failed"


class VMInstance(Base):
    """Model for VM instances."""
    
    __tablename__ = "vm_instances"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # VM identification
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    vm_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)  # External VM ID (Vagrant, etc.)
    
    # VM configuration
    template: Mapped[str] = mapped_column(String(100), default="ubuntu/focal64", nullable=False)
    memory_mb: Mapped[int] = mapped_column(Integer, default=1024, nullable=False)
    cpus: Mapped[int] = mapped_column(Integer, default=1, nullable=False)
    disk_gb: Mapped[int] = mapped_column(Integer, default=20, nullable=False)
    
    # VM lifecycle
    status: Mapped[VMStatus] = mapped_column(default=VMStatus.CREATING, nullable=False)
    ip_address: Mapped[Optional[str]] = mapped_column(String(15), nullable=True)
    ssh_port: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Timing
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    scheduled_termination: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    terminated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # File injection
    injected_file_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), nullable=True)
    injection_path: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    injection_completed: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # Monitoring
    monitoring_active: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    elk_index: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    
    # Error handling
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Workflow tracking
    workflow_job_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), nullable=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc).replace(tzinfo=None), nullable=False)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc).replace(tzinfo=None), onupdate=lambda: datetime.now(timezone.utc).replace(tzinfo=None), nullable=False)
    
    @property
    def is_expired(self) -> bool:
        """Check if VM has exceeded its 30-minute runtime."""
        if not self.started_at:
            return False
        return datetime.now(timezone.utc) > self.started_at + timedelta(minutes=30)
    
    @property
    def runtime_minutes(self) -> float:
        """Get current runtime in minutes."""
        if not self.started_at:
            return 0.0
        end_time = self.terminated_at or datetime.now(timezone.utc)
        return (end_time - self.started_at).total_seconds() / 60
    
    def __repr__(self) -> str:
        return f"<VMInstance(id={self.id}, name={self.name}, status={self.status})>"
