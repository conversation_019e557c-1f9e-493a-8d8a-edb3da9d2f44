"""
Basic VM Availability Manager

A practical implementation to improve the current TurdParty VM management pipeline
by maintaining a pool of ready-to-use VMs for immediate allocation.

This addresses the current issue where VMs are queued but never actually allocated
because there are no pre-available VMs in the pool.

Key Features:
- Maintains minimum ready VMs per template
- Fast allocation from pre-created VM pool
- Automatic pool replenishment
- Integration with existing API endpoints
- Elasticsearch logging for all operations
"""

import asyncio
import logging
import os
import time
import uuid
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from celery import shared_task
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker

# Import Elasticsearch logging
try:
    import sys
    sys.path.append('/app')
    from utils.elasticsearch_logger import VMLogger, vm_logger
    ELASTICSEARCH_AVAILABLE = True
except ImportError:
    ELASTICSEARCH_AVAILABLE = False
    # Fallback logger
    class VMLogger:
        def __init__(self):
            self.correlation_id = str(uuid.uuid4())
        def new_correlation_id(self): return str(uuid.uuid4())
        def log_vm_allocation(self, *args, **kwargs): pass
        def log_vm_provisioning(self, *args, **kwargs): pass
        def log_vm_termination(self, *args, **kwargs): pass
        def log_pool_maintenance(self, *args, **kwargs): pass
        def log_error(self, *args, **kwargs): pass
        def log_request(self, *args, **kwargs): return str(uuid.uuid4())
    vm_logger = VMLogger()

# Import database models - adjust path based on container environment
try:
    from services.api.src.models.vm_instance import VMInstance, VMStatus
except ImportError:
    try:
        # Alternative import path for different container setups
        import sys
        sys.path.append('/app/services/api/src')
        from models.vm_instance import VMInstance, VMStatus
    except ImportError:
        # Fallback - create minimal models for basic functionality
        from enum import Enum

        class VMStatus(Enum):
            CREATING = "creating"
            READY = "ready"
            ALLOCATED = "allocated"
            RUNNING = "running"
            TERMINATING = "terminating"
            TERMINATED = "terminated"
            FAILED = "failed"

        # Minimal VMInstance class for basic operations
        class VMInstance:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

# Import Celery for task management
try:
    from services.workers.tasks.vm_management import create_vm
except ImportError:
    # Fallback for missing vm_management module
    def create_vm(*args, **kwargs):
        """Fallback VM creation function."""
        return {"status": "error", "message": "VM creation not available"}

try:
    from services.workers.celery_app import celery_app
except ImportError:
    # Fallback for missing celery app
    celery_app = None

logger = logging.getLogger(__name__)

# Database session with environment variable support
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import os

def get_database_url():
    """Get database URL from environment or use default."""
    # Use container-to-container communication for database
    default_url = "********************************************/turdparty"
    return os.getenv('DATABASE_URL', default_url)

# Use container-appropriate database URL
DATABASE_URL = get_database_url()
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_elasticsearch_url():
    """Get Elasticsearch URL from environment or use default."""
    return os.getenv('ELASTICSEARCH_URL', 'http://elasticsearch:9200')


class VMTemplate(Enum):
    """Supported VM templates."""
    UBUNTU_20 = "ubuntu:20.04"
    UBUNTU_22 = "ubuntu:22.04"
    ALPINE = "alpine:latest"
    WINDOWS_10 = "10Baht/windows10-turdparty"


@dataclass
class PoolConfig:
    """Pool configuration for each template."""
    template: str
    min_ready: int = 3
    max_total: int = 10
    vm_type: str = "docker"
    memory_mb: int = 1024
    cpus: int = 1
    disk_gb: int = 20


class BasicVMAvailabilityManager:
    """Basic VM availability manager for immediate allocation."""
    
    def __init__(self):
        self.pool_configs = {
            VMTemplate.UBUNTU_20.value: PoolConfig(
                template=VMTemplate.UBUNTU_20.value,
                min_ready=1,  # Minimal Ubuntu pool for Linux analysis
                max_total=3,
                vm_type="docker",
                memory_mb=1024,
                cpus=1
            ),
            VMTemplate.WINDOWS_10.value: PoolConfig(
                template=VMTemplate.WINDOWS_10.value,
                min_ready=2,  # Keep 2 Windows VMs ready for immediate malware analysis
                max_total=5,  # Allow up to 5 total Windows VMs
                vm_type="vagrant",
                memory_mb=4096,
                cpus=2
            )
        }
    
    def get_pool_status(self) -> Dict[str, Any]:
        """Get current pool status for all templates."""
        start_time = time.time()

        try:
            with SessionLocal() as db:
                status = {}

                for template, config in self.pool_configs.items():
                    # Count VMs by status
                    result = db.execute(
                        text("""
                            SELECT status, COUNT(*) as count
                            FROM vm_instances
                            WHERE template = :template
                            GROUP BY status
                        """),
                        {"template": template}
                    ).fetchall()

                    status_counts = {row.status: row.count for row in result}

                    ready_count = status_counts.get(VMStatus.READY.value, 0)
                    total_count = sum(status_counts.values())

                    pool_status = {
                        "ready": ready_count,
                        "total": total_count,
                        "creating": status_counts.get(VMStatus.CREATING.value, 0),
                        "running": status_counts.get(VMStatus.RUNNING.value, 0),
                        "terminated": status_counts.get(VMStatus.TERMINATED.value, 0),
                        "min_ready": config.min_ready,
                        "max_total": config.max_total,
                        "needs_provisioning": ready_count < config.min_ready,
                        "can_provision": total_count < config.max_total
                    }

                    status[template] = pool_status

                    # Log pool status to Elasticsearch
                    if ELASTICSEARCH_AVAILABLE:
                        vm_logger.es_logger.log_pool_status(template, pool_status)

                # Log performance metric
                duration_ms = int((time.time() - start_time) * 1000)
                if ELASTICSEARCH_AVAILABLE:
                    vm_logger.es_logger.log_performance_metric(
                        "pool.status.query_time", duration_ms, "ms",
                        {"templates_checked": len(self.pool_configs)}
                    )

                return status

        except Exception as e:
            error_msg = f"Error getting pool status: {e}"
            logger.error(error_msg)

            # Log error to Elasticsearch
            if ELASTICSEARCH_AVAILABLE:
                vm_logger.log_error(error_msg, {"operation": "get_pool_status"}, e)

            return {}
    
    def get_available_vm(self, template: str) -> Optional[Dict[str, Any]]:
        """Get an available VM for immediate allocation."""
        start_time = time.time()
        vm_id = None

        try:
            with SessionLocal() as db:
                # Find a ready VM for this template
                result = db.execute(
                    text("""
                        SELECT id, name, vm_id, template, memory_mb, cpus, ip_address, ssh_port
                        FROM vm_instances
                        WHERE status = :status AND template = :template
                        ORDER BY created_at ASC
                        LIMIT 1
                    """),
                    {"status": VMStatus.READY.value, "template": template}
                ).fetchone()

                if result:
                    vm_id = str(result.id)

                    # Mark VM as allocated
                    db.execute(
                        text("""
                            UPDATE vm_instances
                            SET status = :new_status, allocated_at = :allocated_at
                            WHERE id = :vm_id
                        """),
                        {
                            "new_status": VMStatus.RUNNING.value,
                            "allocated_at": datetime.now(timezone.utc),
                            "vm_id": result.id
                        }
                    )
                    db.commit()

                    vm_data = {
                        "vm_id": vm_id,
                        "name": result.name,
                        "template": result.template,
                        "memory_mb": result.memory_mb,
                        "cpus": result.cpus,
                        "ip_address": result.ip_address,
                        "ssh_port": result.ssh_port,
                        "status": VMStatus.RUNNING.value
                    }

                    # Log successful allocation
                    duration_ms = int((time.time() - start_time) * 1000)
                    if ELASTICSEARCH_AVAILABLE:
                        vm_logger.log_vm_allocation(
                            vm_id, template, True, duration_ms,
                            {"allocation_method": "pool", "vm_name": result.name}
                        )

                    logger.info(f"✅ VM allocated from pool: {vm_id} ({template}) in {duration_ms}ms")
                    return vm_data

                # No VMs available
                if ELASTICSEARCH_AVAILABLE:
                    vm_logger.log_vm_allocation(
                        "none", template, False, int((time.time() - start_time) * 1000),
                        {"allocation_method": "pool", "reason": "no_ready_vms"}
                    )

                logger.warning(f"⚠️ No ready VMs available for template: {template}")
                return None

        except Exception as e:
            error_msg = f"Error getting available VM: {e}"
            logger.error(error_msg)

            # Log error to Elasticsearch
            duration_ms = int((time.time() - start_time) * 1000)
            if ELASTICSEARCH_AVAILABLE:
                vm_logger.log_vm_allocation(
                    vm_id or "unknown", template, False, duration_ms,
                    {"allocation_method": "pool", "error": str(e)}
                )
                vm_logger.log_error(error_msg, {
                    "operation": "get_available_vm",
                    "template": template,
                    "vm_id": vm_id
                }, e)

            return None
    
    def provision_vm(self, template: str) -> Optional[str]:
        """Provision a new VM for the pool."""
        try:
            config = self.pool_configs.get(template)
            if not config:
                logger.error(f"No configuration found for template: {template}")
                return None
            
            vm_name = f"pool_{template.replace(':', '_').replace('/', '_')}_{uuid.uuid4().hex[:8]}"
            
            # Create VM record
            with SessionLocal() as db:
                vm_instance = VMInstance(
                    name=vm_name,
                    template=template,
                    memory_mb=config.memory_mb,
                    cpus=config.cpus,
                    disk_gb=config.disk_gb,
                    status=VMStatus.CREATING
                )
                db.add(vm_instance)
                db.commit()
                db.refresh(vm_instance)
                
                vm_id = str(vm_instance.id)
            
            # Queue VM creation task
            vm_config = {
                "name": vm_name,
                "template": template,
                "memory_mb": config.memory_mb,
                "cpus": config.cpus,
                "disk_gb": config.disk_gb
            }
            
            create_vm.delay(vm_id, config.vm_type, vm_config)
            
            logger.info(f"Provisioned new VM for pool: {vm_name} ({vm_id})")
            return vm_id
            
        except Exception as e:
            logger.error(f"Error provisioning VM: {e}")
            return None


# Global manager instance
availability_manager = BasicVMAvailabilityManager()


@shared_task(bind=True, name="services.workers.tasks.basic_vm_availability_manager.maintain_vm_pools")
def maintain_vm_pools(self) -> Dict[str, Any]:
    """Maintain VM pools by ensuring minimum ready VMs."""
    start_time = time.time()
    correlation_id = vm_logger.new_correlation_id() if ELASTICSEARCH_AVAILABLE else str(uuid.uuid4())

    try:
        logger.info(f"🔧 Starting VM pool maintenance (correlation: {correlation_id})")

        # Log maintenance request
        if ELASTICSEARCH_AVAILABLE:
            vm_logger.log_request("pool_maintenance", {"correlation_id": correlation_id})

        status = availability_manager.get_pool_status()
        actions = []

        for template, pool_status in status.items():
            if pool_status["needs_provisioning"] and pool_status["can_provision"]:
                needed = pool_status["min_ready"] - pool_status["ready"]
                logger.info(f"📊 Template {template} needs {needed} VMs (ready: {pool_status['ready']}, min: {pool_status['min_ready']})")

                for i in range(needed):
                    vm_start_time = time.time()
                    vm_id = availability_manager.provision_vm(template)
                    vm_duration_ms = int((time.time() - vm_start_time) * 1000)

                    if vm_id:
                        actions.append({
                            "action": "provision",
                            "template": template,
                            "vm_id": vm_id,
                            "duration_ms": vm_duration_ms
                        })

                        # Log VM provisioning
                        if ELASTICSEARCH_AVAILABLE:
                            vm_logger.log_vm_provisioning(
                                vm_id, template, True, vm_duration_ms,
                                {"trigger": "scheduled_maintenance", "correlation_id": correlation_id}
                            )

                        logger.info(f"✅ Provisioned new VM: {vm_id} for template {template} in {vm_duration_ms}ms")
                    else:
                        # Log failed provisioning
                        if ELASTICSEARCH_AVAILABLE:
                            vm_logger.log_vm_provisioning(
                                "failed", template, False, vm_duration_ms,
                                {"trigger": "scheduled_maintenance", "correlation_id": correlation_id}
                            )

                        logger.error(f"❌ Failed to provision VM for template {template}")
            else:
                logger.debug(f"📊 Template {template}: no action needed (ready: {pool_status['ready']}, min: {pool_status['min_ready']})")

        # Log maintenance completion
        total_duration_ms = int((time.time() - start_time) * 1000)

        if ELASTICSEARCH_AVAILABLE:
            vm_logger.log_pool_maintenance(actions, status)
            vm_logger.es_logger.log_performance_metric(
                "pool.maintenance.scheduled_duration", total_duration_ms, "ms",
                {"actions_count": len(actions), "correlation_id": correlation_id}
            )

        logger.info(f"🎉 Pool maintenance completed in {total_duration_ms}ms: {len(actions)} actions taken")

        return {
            "maintenance_completed": True,
            "actions_taken": actions,
            "pool_status": status,
            "duration_ms": total_duration_ms,
            "correlation_id": correlation_id
        }

    except Exception as e:
        error_msg = f"Pool maintenance failed: {e}"
        logger.error(error_msg)

        # Log error to Elasticsearch
        if ELASTICSEARCH_AVAILABLE:
            vm_logger.log_error(error_msg, {
                "operation": "maintain_vm_pools",
                "correlation_id": correlation_id
            }, e)

        raise


@shared_task(bind=True, name="services.workers.tasks.basic_vm_availability_manager.allocate_vm_immediately")
def allocate_vm_immediately(self, template: str, requester_id: str = "api") -> Dict[str, Any]:
    """Allocate a VM immediately from the available pool."""
    start_time = time.time()
    correlation_id = vm_logger.new_correlation_id() if ELASTICSEARCH_AVAILABLE else str(uuid.uuid4())

    try:
        logger.info(f"🚀 Immediate VM allocation requested for template: {template} (correlation: {correlation_id})")

        # Log allocation request
        if ELASTICSEARCH_AVAILABLE:
            vm_logger.log_request("vm_allocation", {
                "template": template,
                "requester_id": requester_id,
                "correlation_id": correlation_id
            })

        # Try to get an available VM
        vm = availability_manager.get_available_vm(template)
        duration_ms = int((time.time() - start_time) * 1000)

        if vm:
            # Trigger pool maintenance to replace the allocated VM
            maintain_vm_pools.delay()

            # Log successful allocation
            if ELASTICSEARCH_AVAILABLE:
                vm_logger.log_vm_allocation(
                    vm["vm_id"], template, True, duration_ms,
                    {
                        "allocation_method": "immediate_pool",
                        "requester_id": requester_id,
                        "correlation_id": correlation_id,
                        "vm_name": vm.get('name'),
                        "pool_maintenance_triggered": True
                    }
                )

            logger.info(f"✅ VM allocated successfully: {vm['vm_id']} in {duration_ms}ms")

            return {
                "success": True,
                "vm_id": vm["vm_id"],
                "vm": vm,
                "allocated_at": datetime.now(timezone.utc).isoformat(),
                "message": f"VM allocated immediately from {template} pool",
                "duration_ms": duration_ms,
                "correlation_id": correlation_id
            }
        else:
            # No VMs available, provision one and queue the request
            provision_start_time = time.time()
            vm_id = availability_manager.provision_vm(template)
            provision_duration_ms = int((time.time() - provision_start_time) * 1000)

            # Log provisioning attempt
            if ELASTICSEARCH_AVAILABLE:
                vm_logger.log_vm_allocation(
                    "none", template, False, duration_ms,
                    {
                        "allocation_method": "immediate_pool",
                        "requester_id": requester_id,
                        "correlation_id": correlation_id,
                        "reason": "no_available_vms",
                        "fallback_provisioning": True,
                        "provisioning_vm_id": vm_id
                    }
                )

                if vm_id:
                    vm_logger.log_vm_provisioning(
                        vm_id, template, True, provision_duration_ms,
                        {
                            "trigger": "immediate_allocation_fallback",
                            "correlation_id": correlation_id
                        }
                    )

            logger.warning(f"⚠️ No ready VMs available for {template}, provisioned new VM: {vm_id}")

            return {
                "success": False,
                "status": "provisioning",
                "provisioning_vm_id": vm_id,
                "message": f"No ready VMs available for {template}, provisioning new VM",
                "estimated_wait_minutes": 3,
                "duration_ms": duration_ms,
                "correlation_id": correlation_id
            }

    except Exception as e:
        error_msg = f"Immediate VM allocation failed: {e}"
        logger.error(error_msg)

        # Log error to Elasticsearch
        duration_ms = int((time.time() - start_time) * 1000)
        if ELASTICSEARCH_AVAILABLE:
            vm_logger.log_vm_allocation(
                "error", template, False, duration_ms,
                {
                    "allocation_method": "immediate_pool",
                    "requester_id": requester_id,
                    "correlation_id": correlation_id,
                    "error": str(e)
                }
            )
            vm_logger.log_error(error_msg, {
                "operation": "allocate_vm_immediately",
                "template": template,
                "requester_id": requester_id,
                "correlation_id": correlation_id
            }, e)

        raise


@shared_task(bind=True, name="services.workers.tasks.basic_vm_availability_manager.get_pool_status")
def get_pool_status(self) -> Dict[str, Any]:
    """Get current pool status for monitoring."""
    try:
        return availability_manager.get_pool_status()
    except Exception as e:
        logger.error(f"Error getting pool status: {e}")
        raise
