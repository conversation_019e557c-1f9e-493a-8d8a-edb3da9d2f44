"""ELK Integration tasks for TurdParty workflow data streaming."""

import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone
from uuid import UUID

import requests
from celery import shared_task
from celery.utils.log import get_task_logger
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker

# Database setup for workers
from sqlalchemy import create_engine
DATABASE_URL = "********************************************/turdparty"
engine = create_engine(DATABASE_URL)

logger = get_task_logger(__name__)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# ELK Configuration
LOGSTASH_HOST = "logstash"
LOGSTASH_HTTP_PORT = 8080
ELASTICSEARCH_HOST = "elasticsearch"
ELASTICSEARCH_PORT = 9200


class ELKStreamer:
    """Handles streaming data to ELK stack."""
    
    def __init__(self):
        self.logstash_url = f"http://{LOGSTASH_HOST}:{LOGSTASH_HTTP_PORT}"
        self.elasticsearch_url = f"http://{ELASTICSEARCH_HOST}:{ELASTICSEARCH_PORT}"
    
    def send_to_logstash(self, data: Dict[str, Any]) -> bool:
        """Send data to Logstash HTTP input."""
        try:
            # Add ECS-compliant fields
            ecs_data = self._add_ecs_fields(data)
            
            response = requests.post(
                self.logstash_url,
                json=ecs_data,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"Successfully sent data to Logstash: {data.get('event', {}).get('action', 'unknown')}")
                return True
            else:
                logger.error(f"Failed to send data to Logstash: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending data to Logstash: {e}")
            return False
    
    def _add_ecs_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Add ECS (Elastic Common Schema) compliant fields."""
        ecs_data = {
            "@timestamp": datetime.now(timezone.utc).isoformat(),
            "ecs": {"version": "8.11.0"},
            "event": {
                "kind": "event",
                "category": ["process"],
                "type": ["info"],
                "dataset": "turdparty.workflow"
            },
            "service": {
                "name": "turdparty",
                "type": "malware_analysis",
                "version": "1.0.0"
            },
            "host": {
                "name": "turdparty-worker"
            }
        }
        
        # Merge with provided data
        ecs_data.update(data)
        
        return ecs_data
    
    def check_elasticsearch_health(self) -> bool:
        """Check if Elasticsearch is healthy."""
        try:
            response = requests.get(f"{self.elasticsearch_url}/_health", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Elasticsearch health check failed: {e}")
            return False


# Global ELK streamer instance
elk_streamer = ELKStreamer()


@shared_task(bind=True, name="services.workers.tasks.elk_integration.stream_workflow_event")
def stream_workflow_event(self, workflow_job_id: str, event_type: str, event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Stream workflow events to ELK stack."""
    try:
        logger.info(f"Streaming workflow event: {event_type} for workflow {workflow_job_id}")
        
        # Get workflow details from database
        with SessionLocal() as db:
            workflow_result = db.execute(
                text("""
                    SELECT wj.*, fu.filename, fu.file_size, fu.blake3_hash, vm.name as vm_name, vm.template
                    FROM workflow_jobs wj
                    LEFT JOIN file_uploads fu ON wj.file_upload_id = fu.id
                    LEFT JOIN vm_instances vm ON wj.vm_instance_id = vm.id
                    WHERE wj.id = :workflow_id
                """),
                {"workflow_id": workflow_job_id}
            ).fetchone()
            
            if not workflow_result:
                raise ValueError(f"Workflow job not found: {workflow_job_id}")
        
        # Prepare ELK event data
        elk_event = {
            "event": {
                "action": event_type,
                "category": ["process"],
                "type": ["info"],
                "outcome": event_data.get("outcome", "unknown")
            },
            "turdparty": {
                "workflow_id": workflow_job_id,
                "file_id": str(workflow_result.file_upload_id) if workflow_result.file_upload_id else None,
                "vm_id": str(workflow_result.vm_instance_id) if workflow_result.vm_instance_id else None,
                "phase": "workflow",
                "status": workflow_result.status
            },
            "file": {
                "name": workflow_result.filename,
                "size": workflow_result.file_size,
                "hash": {
                    "blake3": workflow_result.blake3_hash
                }
            } if workflow_result.filename else {},
            "vm": {
                "name": workflow_result.vm_name,
                "template": workflow_result.template
            } if workflow_result.vm_name else {},
            "process": {
                "title": f"TurdParty Workflow - {event_type}"
            }
        }
        
        # Add event-specific data
        elk_event.update(event_data)
        
        # Stream to ELK
        success = elk_streamer.send_to_logstash(elk_event)
        
        if success:
            logger.info(f"Successfully streamed workflow event: {event_type}")
            return {"success": True, "event_type": event_type, "workflow_id": workflow_job_id}
        else:
            logger.error(f"Failed to stream workflow event: {event_type}")
            return {"success": False, "error": "Failed to send to Logstash"}
    
    except Exception as e:
        logger.error(f"Error streaming workflow event: {e}")
        raise


@shared_task(bind=True, name="services.workers.tasks.elk_integration.stream_vm_metrics")
def stream_vm_metrics(self, vm_id: str, metrics: Dict[str, Any]) -> Dict[str, Any]:
    """Stream VM performance metrics to ELK stack."""
    try:
        logger.info(f"Streaming VM metrics for VM: {vm_id}")
        
        # Get VM details from database
        with SessionLocal() as db:
            vm_result = db.execute(
                text("SELECT * FROM vm_instances WHERE id = :vm_id"),
                {"vm_id": vm_id}
            ).fetchone()
            
            if not vm_result:
                raise ValueError(f"VM not found: {vm_id}")
        
        # Prepare ELK metrics data
        elk_metrics = {
            "event": {
                "action": "vm_metrics",
                "category": ["host"],
                "type": ["info"],
                "dataset": "turdparty.vm_metrics"
            },
            "turdparty": {
                "vm_id": vm_id,
                "phase": "runtime"
            },
            "vm": {
                "name": vm_result.name,
                "template": vm_result.template,
                "status": vm_result.status
            },
            "host": {
                "cpu": {
                    "usage": metrics.get("cpu_usage", 0)
                },
                "memory": {
                    "usage": metrics.get("memory_usage", 0),
                    "total": metrics.get("memory_total", 0)
                },
                "disk": {
                    "usage": metrics.get("disk_usage", 0),
                    "total": metrics.get("disk_total", 0)
                }
            },
            "process": {
                "title": "TurdParty VM Metrics"
            }
        }
        
        # Add additional metrics
        if "network" in metrics:
            elk_metrics["network"] = metrics["network"]
        
        if "processes" in metrics:
            elk_metrics["process"]["count"] = len(metrics["processes"])
        
        # Stream to ELK
        success = elk_streamer.send_to_logstash(elk_metrics)
        
        if success:
            logger.info(f"Successfully streamed VM metrics for: {vm_id}")
            return {"success": True, "vm_id": vm_id}
        else:
            logger.error(f"Failed to stream VM metrics for: {vm_id}")
            return {"success": False, "error": "Failed to send to Logstash"}
    
    except Exception as e:
        logger.error(f"Error streaming VM metrics: {e}")
        raise


@shared_task(bind=True, name="services.workers.tasks.elk_integration.stream_file_analysis")
def stream_file_analysis(self, workflow_job_id: str, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
    """Stream file analysis results to ELK stack."""
    try:
        logger.info(f"Streaming file analysis results for workflow: {workflow_job_id}")
        
        # Prepare ELK analysis data
        elk_analysis = {
            "event": {
                "action": "file_analysis_complete",
                "category": ["malware"],
                "type": ["info"],
                "outcome": "success" if analysis_results.get("success") else "failure"
            },
            "turdparty": {
                "workflow_id": workflow_job_id,
                "phase": "analysis_complete"
            },
            "file": {
                "analysis": analysis_results
            },
            "process": {
                "title": "TurdParty File Analysis Complete"
            }
        }
        
        # Stream to ELK
        success = elk_streamer.send_to_logstash(elk_analysis)
        
        if success:
            logger.info(f"Successfully streamed analysis results for: {workflow_job_id}")
            return {"success": True, "workflow_id": workflow_job_id}
        else:
            logger.error(f"Failed to stream analysis results for: {workflow_job_id}")
            return {"success": False, "error": "Failed to send to Logstash"}
    
    except Exception as e:
        logger.error(f"Error streaming file analysis: {e}")
        raise


@shared_task(bind=True, name="services.workers.tasks.elk_integration.check_elk_health")
def check_elk_health(self) -> Dict[str, Any]:
    """Check ELK stack health and connectivity."""
    try:
        logger.info("Checking ELK stack health")
        
        health_status = {
            "elasticsearch": elk_streamer.check_elasticsearch_health(),
            "logstash": False,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        # Check Logstash connectivity
        try:
            test_data = {
                "event": {"action": "health_check"},
                "message": "ELK health check",
                "turdparty": {"phase": "health_check"}
            }
            health_status["logstash"] = elk_streamer.send_to_logstash(test_data)
        except Exception as e:
            logger.error(f"Logstash health check failed: {e}")
            health_status["logstash"] = False
        
        overall_health = all(health_status[service] for service in ["elasticsearch", "logstash"])
        health_status["overall"] = overall_health
        
        if overall_health:
            logger.info("ELK stack is healthy")
        else:
            logger.warning(f"ELK stack health issues: {health_status}")
        
        return health_status
    
    except Exception as e:
        logger.error(f"Error checking ELK health: {e}")
        raise
