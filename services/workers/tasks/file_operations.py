"""File operation tasks for TurdParty workflow."""

import logging
import os
from typing import Dict, Any
from uuid import UUID

from celery import shared_task
from celery.utils.log import get_task_logger

logger = get_task_logger(__name__)


@shared_task(bind=True, max_retries=3, retry_backoff=True)
def download_file_from_minio(self, workflow_job_id: str, file_upload_id: str) -> Dict[str, Any]:
    """
    Download file from MinIO storage for processing.
    
    Args:
        workflow_job_id: ID of the workflow job
        file_upload_id: ID of the file upload record
        
    Returns:
        Dictionary with download details
    """
    try:
        # Import here to avoid circular imports
        from services.api.src.services.database import SessionLocal
        from services.api.src.services.minio_client import download_file
        from services.api.src.models.file_upload import FileUpload
        from services.api.src.models.workflow_job import WorkflowJob, WorkflowStatus
        
        db = SessionLocal()
        
        try:
            # Get file upload record
            file_upload = db.query(FileUpload).filter(
                FileUpload.id == UUID(file_upload_id)
            ).first()
            
            if not file_upload:
                raise ValueError(f"File upload not found: {file_upload_id}")
            
            # Get workflow job
            workflow_job = db.query(WorkflowJob).filter(
                WorkflowJob.id == UUID(workflow_job_id)
            ).first()
            
            if not workflow_job:
                raise ValueError(f"Workflow job not found: {workflow_job_id}")
            
            # Download file from MinIO
            logger.info(f"Downloading file from MinIO: {file_upload.minio_object_key}")
            
            file_content = download_file(
                file_upload.minio_object_key,
                file_upload.minio_bucket
            )
            
            # Save to temporary location for VM injection
            temp_dir = "/app/tmp"
            os.makedirs(temp_dir, exist_ok=True)
            
            temp_file_path = os.path.join(temp_dir, f"{workflow_job_id}_{file_upload.filename}")
            
            with open(temp_file_path, "wb") as f:
                f.write(file_content)
            
            # Update workflow status
            workflow_job.status = WorkflowStatus.FILE_STORED
            workflow_job.update_progress("file_download", 20, "File downloaded from MinIO")
            
            if not workflow_job.results:
                workflow_job.results = {}
            
            workflow_job.results["temp_file_path"] = temp_file_path
            workflow_job.results["file_size"] = len(file_content)
            
            db.commit()
            
            logger.info(f"File downloaded successfully: {temp_file_path}")
            
            return {
                "status": "success",
                "temp_file_path": temp_file_path,
                "file_size": len(file_content),
                "workflow_job_id": workflow_job_id,
                "file_upload_id": file_upload_id
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"File download failed: {e}")
        
        # Update workflow job with error
        try:
            db = SessionLocal()
            workflow_job = db.query(WorkflowJob).filter(
                WorkflowJob.id == UUID(workflow_job_id)
            ).first()
            
            if workflow_job:
                workflow_job.status = WorkflowStatus.FAILED
                workflow_job.error_message = f"File download failed: {str(e)}"
                db.commit()
            
            db.close()
        except Exception:
            pass
        
        # Retry if possible
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying file download (attempt {self.request.retries + 1})")
            raise self.retry(countdown=60, exc=e)
        
        raise


@shared_task(bind=True, max_retries=2, retry_backoff=True)
def validate_file_for_injection(self, workflow_job_id: str, temp_file_path: str) -> Dict[str, Any]:
    """
    Validate file before injection into VM.
    
    Args:
        workflow_job_id: ID of the workflow job
        temp_file_path: Path to the temporary file
        
    Returns:
        Dictionary with validation results
    """
    try:
        # Import here to avoid circular imports
        from services.api.src.services.database import SessionLocal
        from services.api.src.models.workflow_job import WorkflowJob, WorkflowStatus
        import hashlib
        import magic
        
        db = SessionLocal()
        
        try:
            # Get workflow job
            workflow_job = db.query(WorkflowJob).filter(
                WorkflowJob.id == UUID(workflow_job_id)
            ).first()
            
            if not workflow_job:
                raise ValueError(f"Workflow job not found: {workflow_job_id}")
            
            # Validate file exists
            if not os.path.exists(temp_file_path):
                raise FileNotFoundError(f"Temporary file not found: {temp_file_path}")
            
            # Get file info
            file_size = os.path.getsize(temp_file_path)
            
            # Calculate hash
            with open(temp_file_path, "rb") as f:
                file_hash = hashlib.sha256(f.read()).hexdigest()
            
            # Detect file type
            file_type = magic.from_file(temp_file_path)
            mime_type = magic.from_file(temp_file_path, mime=True)
            
            # Update workflow status
            workflow_job.update_progress("file_validation", 30, "File validated for injection")
            
            if not workflow_job.results:
                workflow_job.results = {}
            
            workflow_job.results.update({
                "validated_file_path": temp_file_path,
                "validated_file_size": file_size,
                "validated_file_hash": file_hash,
                "file_type": file_type,
                "mime_type": mime_type
            })
            
            db.commit()
            
            logger.info(f"File validated successfully: {temp_file_path}")
            
            return {
                "status": "success",
                "file_path": temp_file_path,
                "file_size": file_size,
                "file_hash": file_hash,
                "file_type": file_type,
                "mime_type": mime_type,
                "workflow_job_id": workflow_job_id
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"File validation failed: {e}")
        
        # Update workflow job with error
        try:
            db = SessionLocal()
            workflow_job = db.query(WorkflowJob).filter(
                WorkflowJob.id == UUID(workflow_job_id)
            ).first()
            
            if workflow_job:
                workflow_job.status = WorkflowStatus.FAILED
                workflow_job.error_message = f"File validation failed: {str(e)}"
                db.commit()
            
            db.close()
        except Exception:
            pass
        
        raise
