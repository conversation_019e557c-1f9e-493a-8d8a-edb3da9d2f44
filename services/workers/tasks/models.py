"""Simple models for workers."""

import enum


class VMStatus(enum.Enum):
    """VM lifecycle status."""
    CREATING = "CREATING"
    RUNNING = "RUNNING"
    INJECTING = "INJECTING"
    MONITORING = "MONITORING"
    TERMINATING = "TERMINATING"
    TERMINATED = "TERMINATED"
    FAILED = "FAILED"


# Simple class to represent VM instance for database operations
class VMInstance:
    """Simple VM instance representation for workers."""
    pass
