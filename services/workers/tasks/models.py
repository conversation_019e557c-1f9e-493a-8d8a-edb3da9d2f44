"""Simple models for workers."""

import enum


class VMStatus(enum.Enum):
    """VM lifecycle status."""

    CREATING = "creating"
    RUNNING = "running"
    INJECTING = "injecting"
    MONITORING = "monitoring"
    TERMINATING = "terminating"
    TERMINATED = "terminated"
    FAILED = "failed"


# Simple class to represent VM instance for database operations
class VMInstance:
    """Simple VM instance representation for workers."""

    pass
