"""
Simplified ELK integration for TurdParty workflow.
Generates mock ECS data for demo purposes.
"""

import logging
import json
import time
import uuid
import requests
from datetime import datetime
from typing import Dict, Any, List
from celery import shared_task
from celery.utils.log import get_task_logger

logger = get_task_logger(__name__)

@shared_task(bind=True, name="services.workers.tasks.simple_elk_ops.stream_workflow_event")
def stream_workflow_event(self, workflow_job_id: str, event_action: str, event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Stream workflow event to Elasticsearch.
    """
    try:
        logger.info(f"Streaming workflow event: {event_action} for {workflow_job_id}")
        
        # Create ECS-compliant event
        ecs_event = {
            "@timestamp": datetime.now(datetime.UTC).isoformat() + "Z",
            "ecs": {"version": "8.11.0"},
            "event": {
                "kind": "event",
                "category": ["process"],
                "type": ["start"],
                "action": event_action,
                "outcome": event_data.get("outcome", "success"),
                "duration": int(time.time() * 1000000)  # microseconds
            },
            "service": {
                "name": "turdparty-workflow",
                "type": "orchestrator",
                "version": "1.0.0"
            },
            "workflow": {
                "job_id": workflow_job_id,
                "action": event_action,
                "data": event_data
            },
            "file_uuid": event_data.get("file_uuid", ""),
            "vm_id": event_data.get("vm_id", ""),
            "labels": {
                "workflow_id": workflow_job_id,
                "environment": "development"
            },
            "tags": ["workflow", "turdparty", "vm-processing"]
        }
        
        # Send to Elasticsearch
        es_url = f"http://elasticsearch:9200/turdparty-workflow-ecs-{datetime.now().strftime('%Y.%m.%d')}/_doc"
        
        try:
            response = requests.post(
                es_url,
                json=ecs_event,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code in [200, 201]:
                logger.info(f"Workflow event sent successfully: {event_action}")
            else:
                logger.warning(f"Failed to send workflow event: {response.status_code}")
                
        except Exception as e:
            logger.warning(f"Failed to send event to Elasticsearch: {e}")
        
        return {
            "success": True,
            "event_action": event_action,
            "workflow_job_id": workflow_job_id
        }
        
    except Exception as e:
        logger.error(f"Failed to stream workflow event: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@shared_task(bind=True, name="services.workers.tasks.simple_elk_ops.generate_installation_events")
def generate_installation_events(self, file_uuid: str, vm_id: str, workflow_job_id: str) -> Dict[str, Any]:
    """
    Collect and process installation events from VM monitoring.
    Production implementation using monitoring data.
    """
    try:
        from services.workers.tasks.vm_management import collect_vm_monitoring_data, send_monitoring_data_to_elasticsearch
        from utils.service_urls import ServiceURLManager

        logger.info(f"Collecting installation events for {file_uuid} from VM {vm_id}")

        # Get VM directory for data collection
        vm_dir = f"/tmp/turdparty_vms/turdparty-vm-{vm_id[:8]}"

        # Collect monitoring data from VM
        monitoring_result = collect_vm_monitoring_data(f"turdparty-vm-{vm_id[:8]}", vm_dir=vm_dir)

        if not monitoring_result.get("success"):
            logger.warning(f"Failed to collect monitoring data: {monitoring_result.get('error')}")
            return {
                "success": False,
                "error": monitoring_result.get("error", "Monitoring data collection failed"),
                "events_generated": 0
            }

        collected_events = monitoring_result.get("events", [])
        base_time = datetime.now(datetime.UTC)

        # Process collected monitoring events and enrich with workflow metadata
        processed_events = []

        for event in collected_events:
            # Enrich event with workflow metadata
            enriched_event = event.copy()
            enriched_event.update({
                "file_uuid": file_uuid,
                "vm_id": vm_id,
                "workflow_job_id": workflow_job_id,
                "labels": {
                    "workflow_id": workflow_job_id,
                    "environment": "development"
                }
            })

            # Add workflow-specific tags
            existing_tags = enriched_event.get("tags", [])
            workflow_tags = ["installation", "workflow-processing", "malware-analysis"]
            enriched_event["tags"] = list(set(existing_tags + workflow_tags))

            processed_events.append(enriched_event)

        logger.info(f"Processed {len(processed_events)} monitoring events for workflow {workflow_job_id}")

        # If no events were collected, create a minimal event set for tracking
        if not processed_events:
            logger.warning(f"No monitoring events collected, creating minimal event set for {file_uuid}")

            minimal_event = {
                "@timestamp": base_time.isoformat() + "Z",
                "ecs": {"version": "8.11.0"},
                "event": {
                    "kind": "event",
                    "category": ["process"],
                    "type": ["info"],
                    "action": "workflow_processing",
                    "outcome": "success"
                },
                "service": {
                    "name": "turdparty-workflow",
                    "type": "orchestrator",
                    "version": "1.0.0"
                },
                "file_uuid": file_uuid,
                "vm_id": vm_id,
                "workflow_job_id": workflow_job_id,
                "host": {
                    "name": f"turdparty-vm-{vm_id[:8]}",
                    "id": vm_id
                },
                "labels": {
                    "workflow_id": workflow_job_id,
                    "environment": "development"
                },
                "tags": ["workflow", "processing", "minimal-event"],
                "message": "Workflow processing completed with minimal monitoring data"
            }
            processed_events = [minimal_event]

        # Send processed events to Elasticsearch using centralized URL management
        try:
            url_manager = ServiceURLManager('local')
            es_base_url = url_manager.get_service_url('elasticsearch')
        except Exception:
            # Fallback to Traefik URL
            es_base_url = "http://elasticsearch.turdparty.localhost"

        # Create index names based on event types
        install_index = f"turdparty-install-ecs-{datetime.now().strftime('%Y.%m.%d')}"
        runtime_index = f"turdparty-runtime-ecs-{datetime.now().strftime('%Y.%m.%d')}"

        sent_count = 0
        for event in processed_events:
            try:
                # Determine appropriate index based on event category
                event_categories = event.get("event", {}).get("category", [])

                if any(cat in ["file", "configuration", "registry"] for cat in event_categories):
                    index_name = install_index
                else:
                    index_name = runtime_index

                url = f"{es_base_url}/{index_name}/_doc"

                response = requests.post(
                    url,
                    json=event,
                    headers={"Content-Type": "application/json"},
                    timeout=10
                )

                if response.status_code in [200, 201]:
                    sent_count += 1
                else:
                    logger.warning(f"Elasticsearch returned status {response.status_code}: {response.text}")

            except Exception as e:
                logger.warning(f"Failed to send event to Elasticsearch: {e}")

        logger.info(f"Processed and sent {sent_count}/{len(processed_events)} installation events for {file_uuid}")

        return {
            "success": True,
            "events_generated": len(processed_events),
            "events_sent": sent_count,
            "file_uuid": file_uuid,
            "elasticsearch_url": es_base_url,
            "install_index": install_index,
            "runtime_index": runtime_index
        }
        
    except Exception as e:
        logger.error(f"Failed to generate installation events: {e}")
        return {
            "success": False,
            "error": str(e)
        }
