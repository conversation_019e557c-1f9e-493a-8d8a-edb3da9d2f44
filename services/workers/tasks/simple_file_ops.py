"""
Simplified file operations for TurdParty workflow.
Works without database dependencies for demo purposes.
"""

import logging
import os
import tempfile
import hashlib
import requests
from typing import Dict, Any
from celery import shared_task
from celery.utils.log import get_task_logger

logger = get_task_logger(__name__)

@shared_task(bind=True, name="services.workers.tasks.simple_file_ops.download_file_from_minio")
def download_file_from_minio(self, file_uuid: str, file_metadata: Dict[str, Any]) -> Dict[str, Any]:
    """
    Download file from MinIO storage for processing.
    Simplified version that works without database.
    """
    try:
        logger.info(f"Downloading file for UUID: {file_uuid}")
        
        # For demo purposes, we'll download from the original URL
        # In production, this would download from MinIO
        file_url = file_metadata.get("download_url", "")
        filename = file_metadata.get("filename", f"{file_uuid}.exe")
        
        if not file_url:
            # Mock successful download for demo
            temp_dir = "/tmp/turdparty"
            os.makedirs(temp_dir, exist_ok=True)
            temp_file_path = os.path.join(temp_dir, filename)
            
            # Create a mock file
            with open(temp_file_path, "wb") as f:
                f.write(b"Mock file content for demo")
            
            logger.info(f"Mock file created: {temp_file_path}")
            
            return {
                "success": True,
                "file_path": temp_file_path,
                "file_size": os.path.getsize(temp_file_path),
                "file_uuid": file_uuid
            }
        
        # Download the actual file
        response = requests.get(file_url, stream=True)
        response.raise_for_status()
        
        # Save to temporary location
        temp_dir = "/tmp/turdparty"
        os.makedirs(temp_dir, exist_ok=True)
        temp_file_path = os.path.join(temp_dir, filename)
        
        with open(temp_file_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        file_size = os.path.getsize(temp_file_path)
        logger.info(f"File downloaded successfully: {temp_file_path} ({file_size} bytes)")
        
        return {
            "success": True,
            "file_path": temp_file_path,
            "file_size": file_size,
            "file_uuid": file_uuid
        }
        
    except Exception as e:
        logger.error(f"File download failed for {file_uuid}: {e}")
        return {
            "success": False,
            "error": str(e),
            "file_uuid": file_uuid
        }

@shared_task(bind=True, name="services.workers.tasks.simple_file_ops.validate_file")
def validate_file(self, file_path: str, file_metadata: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate file before injection into VM.
    """
    try:
        logger.info(f"Validating file: {file_path}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Get file info
        file_size = os.path.getsize(file_path)
        
        # Calculate hash
        with open(file_path, "rb") as f:
            file_hash = hashlib.sha256(f.read()).hexdigest()
        
        # Basic file type detection
        file_type = "unknown"
        if file_path.endswith(".exe"):
            file_type = "Windows executable"
        elif file_path.endswith(".msi"):
            file_type = "Windows installer"
        
        logger.info(f"File validated: {file_path} ({file_size} bytes, {file_type})")
        
        return {
            "valid": True,
            "file_path": file_path,
            "file_size": file_size,
            "file_hash": file_hash,
            "file_type": file_type
        }
        
    except Exception as e:
        logger.error(f"File validation failed for {file_path}: {e}")
        return {
            "valid": False,
            "error": str(e)
        }
