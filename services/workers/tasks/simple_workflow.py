"""
Simplified workflow orchestrator for TurdParty.
Uses mock tasks to demonstrate the complete workflow.
"""

import logging
import time
import uuid
from datetime import datetime
from typing import Dict, Any
from celery import shared_task, chain
from celery.utils.log import get_task_logger

logger = get_task_logger(__name__)

@shared_task(bind=True, name="services.workers.tasks.simple_workflow.process_notepadpp_workflow")
def process_notepadpp_workflow(self, file_uuid: str = "d5cc1f03-3041-46f5-8ad9-7af2b270ddbb") -> Dict[str, Any]:
    """
    Complete Notepad++ processing workflow.
    Simplified version that generates mock data for demo.
    """
    try:
        logger.info(f"Starting Notepad++ workflow for UUID: {file_uuid}")
        
        workflow_job_id = str(uuid.uuid4())
        start_time = time.time()
        
        # Step 1: Mock file download
        logger.info("Step 1: Downloading file from MinIO...")
        file_metadata = {
            "filename": "npp.8.5.8.Installer.x64.exe",
            "file_size": 4796432,
            "file_type": "Windows executable",
            "application_name": "Notepad++",
            "description": "Notepad++ Text Editor"
        }
        
        from services.workers.tasks.simple_file_ops import download_file_from_minio
        download_result = download_file_from_minio(file_uuid, file_metadata)
        
        if not download_result.get("success"):
            raise Exception(f"File download failed: {download_result.get('error')}")
        
        # Step 2: Get VM from pool
        logger.info("Step 2: Allocating VM from pool...")
        from services.workers.tasks.simple_vm_ops import get_vm_for_processing
        vm_result = get_vm_for_processing("gusztavvargadr/windows-10")
        
        if not vm_result.get("success"):
            raise Exception(f"VM allocation failed: {vm_result.get('error')}")
        
        vm_info = vm_result["vm"]
        vm_id = vm_info["id"]
        
        # Step 3: Inject file into VM
        logger.info("Step 3: Injecting file into VM...")
        from services.workers.tasks.simple_vm_ops import inject_file
        injection_result = inject_file(
            workflow_job_id,
            file_uuid,
            download_result["file_path"],
            vm_id,
            "C:\\temp\\npp.8.5.8.Installer.x64.exe"
        )
        
        if not injection_result.get("success"):
            raise Exception(f"File injection failed: {injection_result.get('error')}")
        
        # Step 4: Execute installation
        logger.info("Step 4: Executing installation...")
        from services.workers.tasks.simple_vm_ops import execute_command_in_vm
        execution_result = execute_command_in_vm(
            vm_id,
            "C:\\temp\\npp.8.5.8.Installer.x64.exe /S",
            "C:\\temp",
            300
        )
        
        if not execution_result.get("success"):
            raise Exception(f"Installation failed: {execution_result.get('error')}")
        
        # Step 5: Generate ECS data
        logger.info("Step 5: Generating ECS installation data...")
        from services.workers.tasks.simple_elk_ops import generate_installation_events
        ecs_result = generate_installation_events(file_uuid, vm_id, workflow_job_id)
        
        # Step 6: Start monitoring
        logger.info("Step 6: Starting VM monitoring...")
        from services.workers.tasks.simple_vm_ops import start_vm_monitoring
        monitoring_result = start_vm_monitoring(vm_id, workflow_job_id, 30)
        
        # Step 7: Stream workflow events
        logger.info("Step 7: Streaming workflow events...")
        from services.workers.tasks.simple_elk_ops import stream_workflow_event
        
        # Workflow started event
        stream_workflow_event(workflow_job_id, "workflow_started", {
            "file_uuid": file_uuid,
            "vm_id": vm_id,
            "outcome": "success"
        })
        
        # File downloaded event
        stream_workflow_event(workflow_job_id, "file_downloaded", {
            "file_uuid": file_uuid,
            "file_size": file_metadata["file_size"],
            "outcome": "success"
        })
        
        # File injected event
        stream_workflow_event(workflow_job_id, "file_injected", {
            "file_uuid": file_uuid,
            "vm_id": vm_id,
            "injection_path": injection_result["injection_path"],
            "outcome": "success"
        })
        
        # Installation completed event
        stream_workflow_event(workflow_job_id, "installation_completed", {
            "file_uuid": file_uuid,
            "vm_id": vm_id,
            "exit_code": execution_result["exit_code"],
            "execution_time": execution_result["execution_time"],
            "outcome": "success"
        })
        
        # Monitoring started event
        stream_workflow_event(workflow_job_id, "monitoring_started", {
            "file_uuid": file_uuid,
            "vm_id": vm_id,
            "duration_minutes": 30,
            "outcome": "success"
        })
        
        # Step 8: Schedule VM termination (in 30 minutes)
        logger.info("Step 8: Scheduling VM termination...")
        from services.workers.tasks.simple_vm_ops import terminate_vm
        terminate_vm.apply_async(args=[vm_id], countdown=30 * 60)  # 30 minutes
        
        # Calculate workflow duration
        workflow_duration = time.time() - start_time
        
        # Final workflow completion event
        stream_workflow_event(workflow_job_id, "workflow_completed", {
            "file_uuid": file_uuid,
            "vm_id": vm_id,
            "workflow_duration": workflow_duration,
            "outcome": "success"
        })
        
        logger.info(f"Notepad++ workflow completed successfully in {workflow_duration:.2f} seconds")
        
        # Return comprehensive workflow result
        return {
            "success": True,
            "workflow_job_id": workflow_job_id,
            "file_uuid": file_uuid,
            "vm_id": vm_id,
            "vm_name": vm_info["name"],
            "workflow_duration": workflow_duration,
            "steps_completed": {
                "file_download": download_result,
                "vm_allocation": vm_result,
                "file_injection": injection_result,
                "installation_execution": execution_result,
                "ecs_data_generation": ecs_result,
                "monitoring_started": monitoring_result
            },
            "ecs_events_generated": ecs_result.get("events_generated", 0),
            "installation_summary": {
                "application": "Notepad++",
                "version": "8.5.8",
                "install_path": "C:\\Program Files\\Notepad++",
                "files_created": 15,
                "registry_keys_created": 10,
                "processes_spawned": 3,
                "exit_code": 0,
                "installation_time": execution_result["execution_time"]
            },
            "next_steps": {
                "vm_monitoring": "Active for 30 minutes",
                "vm_termination": "Scheduled in 30 minutes",
                "report_generation": "Available via API"
            }
        }
        
    except Exception as e:
        logger.error(f"Notepad++ workflow failed: {e}")
        
        # Stream failure event
        try:
            from services.workers.tasks.simple_elk_ops import stream_workflow_event
            stream_workflow_event(workflow_job_id, "workflow_failed", {
                "file_uuid": file_uuid,
                "error": str(e),
                "outcome": "failure"
            })
        except:
            pass
        
        return {
            "success": False,
            "error": str(e),
            "file_uuid": file_uuid,
            "workflow_job_id": workflow_job_id
        }

@shared_task(bind=True, name="services.workers.tasks.simple_workflow.trigger_notepadpp_report_generation")
def trigger_notepadpp_report_generation(self, file_uuid: str = "d5cc1f03-3041-46f5-8ad9-7af2b270ddbb") -> Dict[str, Any]:
    """
    Trigger report generation after workflow completion.
    """
    try:
        logger.info(f"Triggering report generation for UUID: {file_uuid}")
        
        # Wait a bit for ECS data to be indexed
        time.sleep(10)
        
        # This would normally call the reporting API
        # For now, we'll just log that the report is ready
        
        logger.info(f"Report generation triggered for {file_uuid}")
        
        return {
            "success": True,
            "file_uuid": file_uuid,
            "report_urls": {
                "complete_report": f"http://api.turdparty.localhost/api/v1/reports/binary/{file_uuid}",
                "summary": f"http://api.turdparty.localhost/api/v1/reports/binary/{file_uuid}/summary",
                "footprint": f"http://api.turdparty.localhost/api/v1/reports/binary/{file_uuid}/footprint",
                "runtime": f"http://api.turdparty.localhost/api/v1/reports/binary/{file_uuid}/runtime"
            },
            "message": "Report generation completed - data available via API"
        }
        
    except Exception as e:
        logger.error(f"Report generation failed: {e}")
        return {
            "success": False,
            "error": str(e)
        }
