"""VM Agent Injection for TurdParty workflow."""

import logging
import os
import subprocess
import tempfile
import tarfile
from typing import Dict, Any, Optional
from pathlib import Path

from celery import shared_task
from celery.utils.log import get_task_logger
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker

# Database setup for workers
from sqlalchemy import create_engine
DATABASE_URL = "********************************************/turdparty"
engine = create_engine(DATABASE_URL)
from tasks.elk_integration import stream_workflow_event

logger = get_task_logger(__name__)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


class VMAgentInjector:
    """Handles injection of monitoring agent into VMs."""
    
    def __init__(self):
        self.agent_path = Path(__file__).parent.parent.parent / "monitoring" / "vm-agent"
        self.temp_dir = Path("/tmp/turdparty-agents")
        self.temp_dir.mkdir(exist_ok=True)
    
    def create_agent_package(self, vm_id: str, workflow_id: str, vm_name: str) -> str:
        """Create a deployable agent package."""
        try:
            # Create temporary package directory
            package_dir = self.temp_dir / f"agent-{vm_id}"
            package_dir.mkdir(exist_ok=True)
            
            # Copy agent files
            agent_files = [
                "agent.py",
                "requirements.txt",
                "config/agent.yml"
            ]
            
            for file_name in agent_files:
                src_file = self.agent_path / file_name
                if src_file.exists():
                    dest_file = package_dir / file_name
                    dest_file.parent.mkdir(exist_ok=True)
                    
                    # Copy and customize for this VM
                    if file_name == "agent.py":
                        self._customize_agent_script(src_file, dest_file, vm_id, workflow_id, vm_name)
                    else:
                        dest_file.write_bytes(src_file.read_bytes())
            
            # Create installation script
            install_script = package_dir / "install.sh"
            self._create_install_script(install_script, vm_id, workflow_id, vm_name)
            
            # Create tar package
            package_file = self.temp_dir / f"agent-{vm_id}.tar.gz"
            with tarfile.open(package_file, "w:gz") as tar:
                tar.add(package_dir, arcname="turdparty-agent")
            
            logger.info(f"Created agent package: {package_file}")
            return str(package_file)
            
        except Exception as e:
            logger.error(f"Error creating agent package: {e}")
            raise
    
    def _customize_agent_script(self, src_file: Path, dest_file: Path, vm_id: str, workflow_id: str, vm_name: str):
        """Customize agent script with VM-specific configuration."""
        content = src_file.read_text()
        
        # Add VM-specific environment variables at the top
        vm_config = f'''
# VM-specific configuration (auto-generated)
import os
os.environ["TURDPARTY_VM_ID"] = "{vm_id}"
os.environ["TURDPARTY_VM_NAME"] = "{vm_name}"
os.environ["TURDPARTY_WORKFLOW_ID"] = "{workflow_id}"
os.environ["LOGSTASH_HOST"] = "host.docker.internal"  # Access host from container

'''
        
        # Insert configuration after the shebang and docstring
        lines = content.split('\n')
        insert_index = 0
        
        # Find insertion point (after docstring)
        for i, line in enumerate(lines):
            if line.strip().startswith('"""') and i > 0:
                # Find end of docstring
                for j in range(i + 1, len(lines)):
                    if lines[j].strip().endswith('"""'):
                        insert_index = j + 1
                        break
                break
        
        lines.insert(insert_index, vm_config)
        dest_file.write_text('\n'.join(lines))
    
    def _create_install_script(self, script_path: Path, vm_id: str, workflow_id: str, vm_name: str):
        """Create installation script for the agent."""
        script_content = f'''#!/bin/bash
# TurdParty VM Agent Installation Script
# Auto-generated for VM: {vm_name} ({vm_id})

set -e

echo "Installing TurdParty VM Monitoring Agent..."

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Python3 not found. Installing..."
    if command -v apt-get &> /dev/null; then
        apt-get update && apt-get install -y python3 python3-pip
    elif command -v yum &> /dev/null; then
        yum install -y python3 python3-pip
    elif command -v apk &> /dev/null; then
        apk add python3 py3-pip
    else
        echo "Cannot install Python3. Unsupported package manager."
        exit 1
    fi
fi

# Install Python dependencies
echo "Installing Python dependencies..."
pip3 install -r requirements.txt

# Set up environment
export TURDPARTY_VM_ID="{vm_id}"
export TURDPARTY_VM_NAME="{vm_name}"
export TURDPARTY_WORKFLOW_ID="{workflow_id}"
export LOGSTASH_HOST="host.docker.internal"
export COLLECTION_INTERVAL="5"
export MONITORED_PATHS="/tmp,/home,/var/log"

# Create systemd service (if systemd is available)
if command -v systemctl &> /dev/null; then
    cat > /etc/systemd/system/turdparty-agent.service << EOF
[Unit]
Description=TurdParty VM Monitoring Agent
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$(pwd)
Environment=TURDPARTY_VM_ID={vm_id}
Environment=TURDPARTY_VM_NAME={vm_name}
Environment=TURDPARTY_WORKFLOW_ID={workflow_id}
Environment=LOGSTASH_HOST=host.docker.internal
Environment=COLLECTION_INTERVAL=5
Environment=MONITORED_PATHS=/tmp,/home,/var/log
ExecStart=/usr/bin/python3 agent.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable turdparty-agent
    systemctl start turdparty-agent
    echo "Agent installed as systemd service"
else
    # Run in background
    echo "Starting agent in background..."
    nohup python3 agent.py > agent.log 2>&1 &
    echo $! > agent.pid
    echo "Agent started with PID: $(cat agent.pid)"
fi

echo "TurdParty VM Agent installation completed successfully!"
'''
        
        script_path.write_text(script_content)
        script_path.chmod(0o755)
    
    def inject_into_docker_vm(self, container_id: str, package_file: str) -> bool:
        """Inject agent into Docker container VM."""
        try:
            logger.info(f"Injecting agent into Docker container: {container_id}")
            
            # Copy package to container
            copy_cmd = [
                "docker", "cp", package_file, f"{container_id}:/tmp/agent.tar.gz"
            ]
            subprocess.run(copy_cmd, check=True, capture_output=True)
            
            # Extract and install in container
            install_commands = [
                "cd /tmp",
                "tar -xzf agent.tar.gz",
                "cd turdparty-agent",
                "chmod +x install.sh",
                "./install.sh"
            ]
            
            for cmd in install_commands:
                exec_cmd = ["docker", "exec", container_id, "bash", "-c", cmd]
                result = subprocess.run(exec_cmd, capture_output=True, text=True)
                
                if result.returncode != 0:
                    logger.error(f"Command failed: {cmd}")
                    logger.error(f"Error: {result.stderr}")
                    return False
            
            logger.info(f"Agent successfully injected into container: {container_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error injecting agent into Docker VM: {e}")
            return False
    
    def inject_into_vagrant_vm(self, vm_dir: str, package_file: str) -> bool:
        """Inject agent into Vagrant VM."""
        try:
            logger.info(f"Injecting agent into Vagrant VM: {vm_dir}")
            
            # Copy package to VM via Vagrant
            copy_cmd = [
                "vagrant", "upload", package_file, "/tmp/agent.tar.gz"
            ]
            subprocess.run(copy_cmd, cwd=vm_dir, check=True, capture_output=True)
            
            # Install in VM
            install_commands = [
                "cd /tmp && tar -xzf agent.tar.gz",
                "cd /tmp/turdparty-agent && chmod +x install.sh && sudo ./install.sh"
            ]
            
            for cmd in install_commands:
                exec_cmd = ["vagrant", "ssh", "-c", cmd]
                result = subprocess.run(exec_cmd, cwd=vm_dir, capture_output=True, text=True)
                
                if result.returncode != 0:
                    logger.error(f"Command failed: {cmd}")
                    logger.error(f"Error: {result.stderr}")
                    return False
            
            logger.info(f"Agent successfully injected into Vagrant VM: {vm_dir}")
            return True
            
        except Exception as e:
            logger.error(f"Error injecting agent into Vagrant VM: {e}")
            return False


# Global injector instance
vm_agent_injector = VMAgentInjector()


@shared_task(bind=True, name="services.workers.tasks.vm_agent_injector.inject_monitoring_agent")
def inject_monitoring_agent(self, vm_id: str, workflow_id: str) -> Dict[str, Any]:
    """Inject monitoring agent into a VM."""
    try:
        logger.info(f"Injecting monitoring agent into VM: {vm_id}")
        
        # Get VM details from database
        with SessionLocal() as db:
            vm_result = db.execute(
                text("SELECT * FROM vm_instances WHERE id = :vm_id"),
                {"vm_id": vm_id}
            ).fetchone()
            
            if not vm_result:
                raise ValueError(f"VM not found: {vm_id}")
        
        # Create agent package
        package_file = vm_agent_injector.create_agent_package(
            vm_id, workflow_id, vm_result.name
        )
        
        # Inject based on VM type
        success = False
        if vm_result.vm_id and vm_result.vm_id.startswith("turdparty_vm_"):
            # Docker container
            success = vm_agent_injector.inject_into_docker_vm(vm_result.vm_id, package_file)
        elif hasattr(vm_result, 'vm_dir') and vm_result.vm_dir:
            # Vagrant VM
            success = vm_agent_injector.inject_into_vagrant_vm(vm_result.vm_dir, package_file)
        else:
            logger.error(f"Unknown VM type for: {vm_id}")
            return {"success": False, "error": "Unknown VM type"}
        
        # Stream injection event to ELK
        stream_workflow_event.delay(
            workflow_id,
            "agent_injected",
            {
                "outcome": "success" if success else "failure",
                "vm_id": vm_id,
                "vm_name": vm_result.name,
                "agent_package": package_file
            }
        )
        
        # Cleanup package file
        try:
            os.unlink(package_file)
        except Exception:
            pass
        
        if success:
            logger.info(f"Monitoring agent successfully injected into VM: {vm_id}")
            return {"success": True, "vm_id": vm_id, "vm_name": vm_result.name}
        else:
            logger.error(f"Failed to inject monitoring agent into VM: {vm_id}")
            return {"success": False, "error": "Injection failed"}
    
    except Exception as e:
        logger.error(f"Error injecting monitoring agent: {e}")
        raise
