<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>TurdParty 10-Binary Analysis Report &#8212; TurdParty 10-Binary Analysis Report</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="_static/classic.css?v=def86cc0" />
    <link rel="stylesheet" type="text/css" href="_static/custom.css?v=f7ada4e2" />
    
    <script src="_static/documentation_options.js?v=8d563738"></script>
    <script src="_static/doctools.js?v=9a2dae69"></script>
    <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Vscode Analysis Report" href="vscode.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="vscode.html" title="Vscode Analysis Report"
             accesskey="N">next</a> |</li>
        <li class="nav-item nav-item-0"><a href="#">TurdParty Analysis</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">TurdParty 10-Binary Analysis Report</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="turdparty-10-binary-analysis-report">
<h1>TurdParty 10-Binary Analysis Report<a class="headerlink" href="#turdparty-10-binary-analysis-report" title="Link to this heading">¶</a></h1>
<img alt="TurdParty Analysis Report" src="https://img.shields.io/badge/TurdParty-Analysis%20Report-blue" />
<p><strong>Generated on:</strong> 2025-06-13 21:22:58 UTC</p>
<p><strong>Analysis Summary:</strong></p>
<ul class="simple">
<li><p><strong>Total Binaries Analyzed:</strong> 10</p></li>
<li><p><strong>Total ECS Events:</strong> 583</p></li>
<li><p><strong>Analysis Duration:</strong> 25.3 seconds</p></li>
<li><p><strong>Success Rate:</strong> 100%</p></li>
</ul>
<section id="binary-analysis-reports">
<h2>Binary Analysis Reports<a class="headerlink" href="#binary-analysis-reports" title="Link to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Individual Binary Reports</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="vscode.html">Vscode Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="vscode.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="vscode.html#quick-stats-summary">Quick Stats Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="vscode.html#installation-tree-structure">Installation Tree Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="vscode.html#quick-navigation">Quick Navigation</a></li>
<li class="toctree-l2"><a class="reference internal" href="vscode.html#id1">Binary Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="vscode.html#id2">Analysis Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="vscode.html#id3">Installation Footprint Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="vscode.html#id4">Behavioral Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="vscode.html#id5">Security Assessment</a></li>
<li class="toctree-l2"><a class="reference internal" href="vscode.html#id6">ECS Data Collection Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="vscode.html#id7">Evidence Box</a></li>
<li class="toctree-l2"><a class="reference internal" href="vscode.html#id22">Technical Implementation Details</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="nodejs.html">Nodejs Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="nodejs.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="nodejs.html#quick-stats-summary">Quick Stats Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="nodejs.html#installation-tree-structure">Installation Tree Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="nodejs.html#quick-navigation">Quick Navigation</a></li>
<li class="toctree-l2"><a class="reference internal" href="nodejs.html#id1">Binary Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="nodejs.html#id2">Analysis Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="nodejs.html#id3">Installation Footprint Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="nodejs.html#id4">Behavioral Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="nodejs.html#id5">Security Assessment</a></li>
<li class="toctree-l2"><a class="reference internal" href="nodejs.html#id6">ECS Data Collection Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="nodejs.html#id7">Evidence Box</a></li>
<li class="toctree-l2"><a class="reference internal" href="nodejs.html#id22">Technical Implementation Details</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="python.html">Python Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="python.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="python.html#quick-stats-summary">Quick Stats Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="python.html#installation-tree-structure">Installation Tree Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="python.html#quick-navigation">Quick Navigation</a></li>
<li class="toctree-l2"><a class="reference internal" href="python.html#id1">Binary Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="python.html#id2">Analysis Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="python.html#id3">Installation Footprint Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="python.html#id4">Behavioral Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="python.html#id5">Security Assessment</a></li>
<li class="toctree-l2"><a class="reference internal" href="python.html#id6">ECS Data Collection Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="python.html#id7">Evidence Box</a></li>
<li class="toctree-l2"><a class="reference internal" href="python.html#id22">Technical Implementation Details</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="chrome.html">Chrome Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="chrome.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="chrome.html#quick-stats-summary">Quick Stats Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="chrome.html#installation-tree-structure">Installation Tree Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="chrome.html#quick-navigation">Quick Navigation</a></li>
<li class="toctree-l2"><a class="reference internal" href="chrome.html#id1">Binary Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="chrome.html#id2">Analysis Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="chrome.html#id3">Installation Footprint Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="chrome.html#id4">Behavioral Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="chrome.html#id5">Security Assessment</a></li>
<li class="toctree-l2"><a class="reference internal" href="chrome.html#id6">ECS Data Collection Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="chrome.html#id7">Evidence Box</a></li>
<li class="toctree-l2"><a class="reference internal" href="chrome.html#id22">Technical Implementation Details</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="firefox.html">Firefox Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="firefox.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="firefox.html#quick-stats-summary">Quick Stats Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="firefox.html#installation-tree-structure">Installation Tree Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="firefox.html#quick-navigation">Quick Navigation</a></li>
<li class="toctree-l2"><a class="reference internal" href="firefox.html#id1">Binary Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="firefox.html#id2">Analysis Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="firefox.html#id3">Installation Footprint Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="firefox.html#id4">Behavioral Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="firefox.html#id5">Security Assessment</a></li>
<li class="toctree-l2"><a class="reference internal" href="firefox.html#id6">ECS Data Collection Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="firefox.html#id7">Evidence Box</a></li>
<li class="toctree-l2"><a class="reference internal" href="firefox.html#id22">Technical Implementation Details</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="notepadpp.html">Notepadpp Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="notepadpp.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp.html#quick-stats-summary">Quick Stats Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp.html#installation-tree-structure">Installation Tree Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp.html#quick-navigation">Quick Navigation</a></li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp.html#id1">Binary Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp.html#id2">Analysis Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp.html#id3">Installation Footprint Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp.html#id4">Behavioral Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp.html#id5">Security Assessment</a></li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp.html#id6">ECS Data Collection Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp.html#id7">Evidence Box</a></li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp.html#id22">Technical Implementation Details</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="7zip.html">7Zip Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="7zip.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="7zip.html#quick-stats-summary">Quick Stats Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="7zip.html#installation-tree-structure">Installation Tree Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="7zip.html#quick-navigation">Quick Navigation</a></li>
<li class="toctree-l2"><a class="reference internal" href="7zip.html#id1">Binary Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="7zip.html#id2">Analysis Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="7zip.html#id3">Installation Footprint Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="7zip.html#id4">Behavioral Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="7zip.html#id5">Security Assessment</a></li>
<li class="toctree-l2"><a class="reference internal" href="7zip.html#id6">ECS Data Collection Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="7zip.html#id7">Evidence Box</a></li>
<li class="toctree-l2"><a class="reference internal" href="7zip.html#id22">Technical Implementation Details</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="putty.html">Putty Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="putty.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="putty.html#quick-stats-summary">Quick Stats Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="putty.html#installation-tree-structure">Installation Tree Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="putty.html#quick-navigation">Quick Navigation</a></li>
<li class="toctree-l2"><a class="reference internal" href="putty.html#id1">Binary Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="putty.html#id2">Analysis Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="putty.html#id3">Installation Footprint Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="putty.html#id4">Behavioral Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="putty.html#id5">Security Assessment</a></li>
<li class="toctree-l2"><a class="reference internal" href="putty.html#id6">ECS Data Collection Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="putty.html#id7">Evidence Box</a></li>
<li class="toctree-l2"><a class="reference internal" href="putty.html#id22">Technical Implementation Details</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="vlc.html">Vlc Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="vlc.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="vlc.html#quick-stats-summary">Quick Stats Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="vlc.html#installation-tree-structure">Installation Tree Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="vlc.html#quick-navigation">Quick Navigation</a></li>
<li class="toctree-l2"><a class="reference internal" href="vlc.html#id1">Binary Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="vlc.html#id2">Analysis Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="vlc.html#id3">Installation Footprint Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="vlc.html#id4">Behavioral Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="vlc.html#id5">Security Assessment</a></li>
<li class="toctree-l2"><a class="reference internal" href="vlc.html#id6">ECS Data Collection Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="vlc.html#id7">Evidence Box</a></li>
<li class="toctree-l2"><a class="reference internal" href="vlc.html#id22">Technical Implementation Details</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="git.html">Git Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="git.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="git.html#quick-stats-summary">Quick Stats Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="git.html#installation-tree-structure">Installation Tree Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="git.html#quick-navigation">Quick Navigation</a></li>
<li class="toctree-l2"><a class="reference internal" href="git.html#id1">Binary Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="git.html#id2">Analysis Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="git.html#id3">Installation Footprint Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="git.html#id4">Behavioral Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="git.html#id5">Security Assessment</a></li>
<li class="toctree-l2"><a class="reference internal" href="git.html#id6">ECS Data Collection Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="git.html#id7">Evidence Box</a></li>
<li class="toctree-l2"><a class="reference internal" href="git.html#id22">Technical Implementation Details</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="analysis-overview">
<h2>Analysis Overview<a class="headerlink" href="#analysis-overview" title="Link to this heading">¶</a></h2>
<p>This comprehensive analysis covers 10 popular development and productivity binaries using the TurdParty malware analysis platform.</p>
<p><strong>Categories Analyzed:</strong></p>
<ul class="simple">
<li><p><strong>Development Tools:</strong> VSCode, Node.js, Python, Git</p></li>
<li><p><strong>Browsers:</strong> Chrome, Firefox</p></li>
<li><p><strong>Editors &amp; Utilities:</strong> Notepad++, 7-Zip, PuTTY, VLC</p></li>
</ul>
<p><strong>Methodology:</strong></p>
<ol class="arabic simple">
<li><p>File upload and metadata extraction</p></li>
<li><p>VM environment creation</p></li>
<li><p>Controlled execution and monitoring</p></li>
<li><p>ECS event collection (583 total events)</p></li>
<li><p>Behavioral analysis and security assessment</p></li>
<li><p>Comprehensive report generation</p></li>
</ol>
<p><strong>Key Findings:</strong></p>
<ul class="simple">
<li><p>All 10 binaries executed successfully</p></li>
<li><p>No malicious behavior detected</p></li>
<li><p>Complete installation footprint captured</p></li>
<li><p>Comprehensive telemetry collected</p></li>
</ul>
</section>
<section id="data-sources">
<h2>Data Sources<a class="headerlink" href="#data-sources" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p><strong>Elasticsearch Index:</strong> turdparty-rich-cli-ecs-2025.06.13</p></li>
<li><p><strong>Total Events:</strong> 583 verified ECS events</p></li>
<li><p><strong>Collection Method:</strong> Real-time VM monitoring</p></li>
<li><p><strong>Analysis Platform:</strong> TurdParty v1.0</p></li>
</ul>
</section>
<section id="quick-access">
<h2>Quick Access<a class="headerlink" href="#quick-access" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p><a class="reference external" href="/tmp/turdparty_reports/">View Raw Reports</a></p></li>
<li><p><a class="reference external" href="http://localhost:9200/turdparty-rich-cli-ecs-*/_search">Elasticsearch Data</a></p></li>
<li><p><a class="reference external" href="http://localhost:5601/app/discover#/">Kibana Dashboard</a></p></li>
</ul>
</section>
<section id="summary-statistics">
<h2>Summary Statistics<a class="headerlink" href="#summary-statistics" title="Link to this heading">¶</a></h2>
<table class="docutils align-default" id="id1">
<caption><span class="caption-text">Binary Analysis Summary</span><a class="headerlink" href="#id1" title="Link to this table">¶</a></caption>
<colgroup>
<col style="width: 25.0%" />
<col style="width: 25.0%" />
<col style="width: 25.0%" />
<col style="width: 25.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Binary</p></th>
<th class="head"><p>Category</p></th>
<th class="head"><p>Events Generated</p></th>
<th class="head"><p>Status</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Vscode</p></td>
<td><p>Development</p></td>
<td><p>74</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-odd"><td><p>Nodejs</p></td>
<td><p>Development</p></td>
<td><p>53</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-even"><td><p>Python</p></td>
<td><p>Development</p></td>
<td><p>145</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-odd"><td><p>Chrome</p></td>
<td><p>Browser</p></td>
<td><p>46</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-even"><td><p>Firefox</p></td>
<td><p>Browser</p></td>
<td><p>56</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-odd"><td><p>Notepadpp</p></td>
<td><p>Editor</p></td>
<td><p>28</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-even"><td><p>7Zip</p></td>
<td><p>Utility</p></td>
<td><p>29</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-odd"><td><p>Putty</p></td>
<td><p>Network</p></td>
<td><p>16</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-even"><td><p>Vlc</p></td>
<td><p>Media</p></td>
<td><p>56</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-odd"><td><p>Git</p></td>
<td><p>Development</p></td>
<td><p>80</p></td>
<td><p>✅ Success</p></td>
</tr>
</tbody>
</table>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This documentation was automatically generated from the TurdParty 10-binary analysis.
All data represents real execution in controlled VM environments.</p>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="#">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">TurdParty 10-Binary Analysis Report</a><ul>
<li><a class="reference internal" href="#binary-analysis-reports">Binary Analysis Reports</a></li>
<li><a class="reference internal" href="#analysis-overview">Analysis Overview</a></li>
<li><a class="reference internal" href="#data-sources">Data Sources</a></li>
<li><a class="reference internal" href="#quick-access">Quick Access</a></li>
<li><a class="reference internal" href="#summary-statistics">Summary Statistics</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="vscode.html"
                          title="next chapter">Vscode Analysis Report</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/index.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="vscode.html" title="Vscode Analysis Report"
             >next</a> |</li>
        <li class="nav-item nav-item-0"><a href="#">TurdParty Analysis</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">TurdParty 10-Binary Analysis Report</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 1980, TurdParty Security.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.4.7.
    </div>
  </body>
</html>