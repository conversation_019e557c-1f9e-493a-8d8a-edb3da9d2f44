Search.setIndex({"alltitles": {"7Zip Analysis Report": [[0, null]], "Analysis Overview": [[4, "analysis-overview"]], "Analysis Results": [[0, "id2"], [1, "id2"], [2, "id2"], [3, "id2"], [5, "id2"], [6, "id2"], [7, "id2"], [8, "id2"], [9, "id2"], [10, "id2"]], "Behavioral Analysis": [[0, "id4"], [1, "id4"], [2, "id4"], [3, "id4"], [5, "id4"], [6, "id4"], [7, "id4"], [8, "id4"], [9, "id4"], [10, "id4"]], "Binary Analysis Reports": [[4, "binary-analysis-reports"]], "Binary Analysis Summary": [[4, "id1"]], "Binary Information": [[0, "id1"], [1, "id1"], [2, "id1"], [3, "id1"], [5, "id1"], [6, "id1"], [7, "id1"], [8, "id1"], [9, "id1"], [10, "id1"]], "Chrome Analysis Report": [[1, null]], "Data Sources": [[4, "data-sources"]], "ECS Data Collection Details": [[0, "id6"], [1, "id6"], [2, "id6"], [3, "id6"], [5, "id6"], [6, "id6"], [7, "id6"], [8, "id6"], [9, "id6"], [10, "id6"]], "Evidence Box": [[0, "id7"], [1, "id7"], [2, "id7"], [3, "id7"], [5, "id7"], [6, "id7"], [7, "id7"], [8, "id7"], [9, "id7"], [10, "id7"]], "Executive Summary": [[0, "executive-summary"], [1, "executive-summary"], [2, "executive-summary"], [3, "executive-summary"], [5, "executive-summary"], [6, "executive-summary"], [7, "executive-summary"], [8, "executive-summary"], [9, "executive-summary"], [10, "executive-summary"]], "Firefox Analysis Report": [[2, null]], "Git Analysis Report": [[3, null]], "Individual Binary Reports": [[4, null]], "Installation Footprint Analysis": [[0, "id3"], [1, "id3"], [2, "id3"], [3, "id3"], [5, "id3"], [6, "id3"], [7, "id3"], [8, "id3"], [9, "id3"], [10, "id3"]], "Installation Tree Structure": [[0, "installation-tree-structure"], [1, "installation-tree-structure"], [2, "installation-tree-structure"], [3, "installation-tree-structure"], [5, "installation-tree-structure"], [6, "installation-tree-structure"], [7, "installation-tree-structure"], [8, "installation-tree-structure"], [9, "installation-tree-structure"], [10, "installation-tree-structure"]], "Nodejs Analysis Report": [[5, null]], "Notepadpp Analysis Report": [[6, null]], "Putty Analysis Report": [[7, null]], "Python Analysis Report": [[8, null]], "Quick Access": [[4, "quick-access"]], "Quick Navigation": [[0, "quick-navigation"], [1, "quick-navigation"], [2, "quick-navigation"], [3, "quick-navigation"], [5, "quick-navigation"], [6, "quick-navigation"], [7, "quick-navigation"], [8, "quick-navigation"], [9, "quick-navigation"], [10, "quick-navigation"]], "Quick Stats Summary": [[0, "quick-stats-summary"], [1, "quick-stats-summary"], [2, "quick-stats-summary"], [3, "quick-stats-summary"], [5, "quick-stats-summary"], [6, "quick-stats-summary"], [7, "quick-stats-summary"], [8, "quick-stats-summary"], [9, "quick-stats-summary"], [10, "quick-stats-summary"]], "Security Assessment": [[0, "id5"], [1, "id5"], [2, "id5"], [3, "id5"], [5, "id5"], [6, "id5"], [7, "id5"], [8, "id5"], [9, "id5"], [10, "id5"]], "Summary Statistics": [[4, "summary-statistics"]], "Technical Implementation Details": [[0, "id22"], [1, "id22"], [2, "id22"], [3, "id22"], [5, "id22"], [6, "id22"], [7, "id22"], [8, "id22"], [9, "id22"], [10, "id22"]], "TurdParty 10-Binary Analysis Report": [[4, null]], "Vlc Analysis Report": [[9, null]], "Vscode Analysis Report": [[10, null]]}, "docnames": ["7zip", "chrome", "firefox", "git", "index", "nodejs", "notepadpp", "putty", "python", "vlc", "vscode"], "envversion": {"sphinx": 62, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.viewcode": 1}, "filenames": ["7zip.rst", "chrome.rst", "firefox.rst", "git.rst", "index.rst", "nodejs.rst", "notepadpp.rst", "putty.rst", "python.rst", "vlc.rst", "vscode.rst"], "indexentries": {}, "objects": {}, "objnames": {}, "objtypes": {}, "terms": {"": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "0": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "00": 2, "01": 0, "02": 6, "04": 0, "057365": 3, "06": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "09": 9, "1": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "10": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "100": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "100m": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "100mb": [1, 2, 3, 9, 10], "1065": 7, "11": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "12": [0, 3, 8], "120": [8, 9], "13": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "13t20": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "145": [4, 8], "15": [0, 5], "150": 8, "16": [4, 7], "1749847728": 10, "1749847731": 5, "1749847734": 8, "1749847737": 1, "1749847739": 2, "1749847742": 6, "1749847744": 0, "1749847746": 7, "1749847748": 9, "1749847751": 3, "18": [1, 6, 9], "1d86ef59": 6, "1ed128a1": 3, "2": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "20": [5, 6, 8, 9], "200": [1, 2, 10], "2025": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "20719e24c2d6": 5, "20d39800": 8, "20gb": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "21": 4, "22": [2, 4], "23": 0, "230": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "2305": 0, "232189": 5, "24": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "2453d276": 10, "25": [1, 4, 8, 10], "250": [1, 2, 3], "256": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "27001": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "28": [4, 5, 6], "29": [0, 4], "2b25": 10, "2c931d2513c6": 3, "2fd29274c60f": 2, "2gb": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "3": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "30": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "300": [3, 10], "338427": 2, "35": [5, 9], "3b3bac16dc50": 10, "3c67": 2, "4": [0, 2, 6, 10], "4017": 1, "402c0087a7e2": 6, "404903": 8, "409a": 6, "42": 9, "43": 3, "43ebc661": 5, "440a": 10, "447175": 9, "45": 10, "457f1f0b": 1, "45b8": 8, "45c8": 3, "46": [1, 4], "48": [1, 3, 5, 8, 10], "4891": 5, "489a": 0, "49": [0, 2, 3, 6, 7, 9, 10], "4bcf": 7, "4cf4": 2, "4d15": 9, "4db1": 10, "5": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "50": [3, 5, 9], "50mb": [0, 5, 6, 7, 8], "52": 5, "53": [4, 5], "55": [2, 8], "558222": 6, "56": [2, 4, 9], "5601": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "57": 1, "58": 4, "583": 4, "5996": 6, "5bbcaf83": 7, "6": [6, 7], "60": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "63a5d7f4de11": 10, "64": [3, 7], "64bit": 7, "65": 3, "67be": 1, "6ac6": 9, "7": [0, 4], "70": 5, "74": [4, 10], "771fd6b0": 10, "79": 7, "794700": 0, "798150": 1, "7b3232ce6537": 1, "7z": 0, "7z2301": 0, "7zfm": 0, "7zg": 0, "7zip": 4, "7zip_ev": 0, "8": [6, 7], "80": [3, 4], "808539": 10, "85": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "853d": 7, "8779e6eb98fb": 9, "89f39e8f": 0, "8d36": 0, "9": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "9200": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "922956": 7, "9340": 8, "95": 10, "962c": 5, "9894": 10, "99": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "9b32": 6, "No": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "Not": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "The": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "_asyncio": 8, "_blank": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "_bz2": 8, "_cat": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "_count": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "_ctype": 8, "_decim": 8, "_is1": 10, "_search": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "a002": 10, "a0b7": 8, "a268": 9, "a594": 1, "a739": 5, "aaf9": 2, "access": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "accord": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "accuraci": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "action": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "activ": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "add": 3, "administr": [0, 1, 2, 3, 5, 8, 9, 10], "advanc": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "ae": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "af89": 3, "after": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "agent": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "agg": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "alert": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "algorithm": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "all": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "amd64": 8, "analyt": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "analyz": 4, "anti": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "api": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "app": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "appdata": [1, 2, 10], "applic": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "appropri": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "ar": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "architectur": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "archiv": 0, "asc": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "assess": 4, "associ": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "assur": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "asyncio": 8, "attack": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "audio_filt": 9, "audio_output": 9, "audit": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "authent": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "author": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "auto": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "autom": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "automat": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "avail": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "averag": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "avi": 9, "b0a7bb56": 9, "b499aab146ec": 8, "backend": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "backoff": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "backup": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "base": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "bash": 3, "basic": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "batch": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "be7a7fc375f0": 0, "behavior": 4, "bin": [3, 5], "binary_nam": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "bit": [3, 7], "bool": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "box": 4, "breakdown": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "brows": 1, "browser": [1, 2, 4], "bsd": 0, "bulk": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "c": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "c1a4d374": 2, "c3b334f6c2d5": 7, "calendar_interv": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "capabl": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "captur": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "categori": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "certif": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "cfg": 6, "chain": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "chang": 6, "channel": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "characterist": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "check": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "checksum": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "chines": 6, "chm": 7, "chrome": 4, "chrome_ev": 1, "chrome_proxi": 1, "chrome_pwa_launch": 1, "chromesetup": 1, "class": [6, 8, 10], "classif": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "cli": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "click": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "client": 7, "cluster": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "cmd": [3, 5], "cn": 10, "code": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "codec": 9, "collect": 4, "com": [1, 3, 10], "commit": 3, "common": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "commun": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "complet": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "complianc": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "compliant": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "compon": 2, "comprehens": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "compress": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "compromis": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "conduct": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "config": 6, "configur": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "connect": [1, 2, 5, 7, 8, 10], "contain": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "control": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "cooki": [1, 2], "core": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "corepack": 5, "corpor": [2, 10], "correl": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "count": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "cover": 4, "coverag": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "cpu": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "crc": 0, "creat": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "creation": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "cryptograph": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "curl": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "currentcontrolset": [3, 5], "currentvers": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "custom": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "cybersecur": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "dai": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "daili": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "darkmodedefault": 6, "dashboard": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "date_histogram": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "de": [0, 1, 9], "de91": 3, "dedupl": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "default": [0, 1, 2, 3, 5, 6, 7, 8, 9], "default_app": 1, "defaulticon": 0, "demand": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "demux": 9, "depend": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "desc": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "descript": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "detail": 4, "detect": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "develop": [3, 4, 5, 8, 10], "dictionari": 2, "digit": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "disabl": 6, "disclaim": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "disk": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "distribut": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "div": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "dll": [0, 1, 6, 8, 9], "document": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "don": 6, "download": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "duplic": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "durat": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "dure": [1, 2, 10], "ec": 4, "edit": 6, "editor": [4, 6, 10], "educ": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "effici": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "elast": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "elasticsearch": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "electron": 10, "email": 8, "en": [0, 1, 9, 10], "enabl": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "encrypt": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "english": 6, "enhanc": 2, "ensur": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "entri": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "environ": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "error": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "esr": 2, "estim": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "event": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "evid": 4, "ex": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "exampl": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "execut": 4, "exfiltr": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "expand": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "expect": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "explor": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "exponenti": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "export": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "extens": [1, 2, 10], "extract": 4, "fa20": 10, "famili": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "fast": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "featur": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "fidel": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "field": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "file": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "file_cr": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "filenam": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "filesystem": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "find": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "firefox": 4, "firefox_ev": 2, "flac": 9, "font": 2, "footprint": 4, "format": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "foundat": [5, 8], "fr": [0, 1, 9], "framework": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "from": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "full": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "gdpr": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "gener": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "get": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "git": 4, "git_ev": 3, "git_is1": 3, "gitforwindow": 3, "gnu": 0, "googl": 1, "googlechromeautolaunch": 1, "gpg": 3, "gpl": [3, 6, 9], "guest": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "gui": 3, "gz": 0, "h5": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "handl": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "health": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "high": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "histori": 1, "hkcr": [0, 9], "hkcu": [1, 2, 6, 7, 8, 10], "hklm": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "ho": 6, "hook": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "host": 7, "href": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "htm": [1, 2], "html": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "http": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "hypervisor": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "i": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "id": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "identifi": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "igor": 0, "ii": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "impact": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "implement": 4, "incid": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "index": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "indexpattern": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "indic": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "industri": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "inform": 4, "infrastructur": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "ini": 6, "inject": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "instal": 4, "integr": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "interact": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "internet": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "iso": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "isol": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "j": [2, 4, 5, 10], "ja": 2, "javascript": 5, "json": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "kei": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "keyword": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "kibana": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "known": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "laboratori": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "lang": 0, "languag": 8, "latest": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "law": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "legitim": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "level": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "lgpl": 0, "li": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "lib": [3, 5, 8], "libexec": 3, "libvlc": 9, "libvlccor": 9, "licens": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "limit": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "link": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "llc": 1, "local": [1, 6, 9, 10], "localhost": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "log": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "low": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "lua": 9, "m": 10, "machin": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "maintenanceservic": 2, "malici": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "malwar": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "manag": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "marketplac": 10, "mb": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "md": 10, "mechan": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "media": [4, 9], "medium": [0, 1, 2, 3, 5, 6, 7, 9, 10], "memori": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "messag": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "metadata": 4, "method": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "methodologi": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "metric": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "microsecond": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "microsoft": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "mingw64": 3, "minut": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "miss": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "mit": [5, 7, 10], "mj": 5, "mkv": 9, "mode": 6, "modif": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "modifi": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "monitor": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "monokai": 6, "mozilla": 2, "mp3": 9, "mp4": 9, "msi": [5, 7, 8], "must": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "navig": 4, "network": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "next": 10, "nist": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "node": [4, 5], "node_modul": [5, 10], "nodej": 4, "nodejs_ev": 5, "none": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "notepad": [4, 6], "notepadpp": 4, "notepadpp_ev": 6, "notif": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "npm": 5, "npp": 6, "npx": 5, "nsi": [3, 6, 9], "ntp": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "o": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "oauth": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "obfusc": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "observ": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "offici": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "omni": 2, "onli": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "onlin": 1, "oper": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "option": [1, 2, 5, 8, 9, 10], "org": [0, 2, 5, 6, 7, 8, 9], "organ": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "packag": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "pageant": 7, "pak": [1, 10], "path": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "pattern": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "pavlov": 0, "payload": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "pdf": [1, 2], "per": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "perform": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "persist": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "pip": 8, "pip3": 8, "pipelin": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "place": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "platform": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "player": 9, "plink": 7, "plu": 6, "plugin": [6, 9], "point": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "polici": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "popular": [4, 10], "ppk": 7, "pre": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "precis": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "pref": 2, "prefer": 1, "primari": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "privaci": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "privileg": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "pro": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "process": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "product": [4, 10], "profil": 2, "program": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "project": 3, "protect": 2, "protocol": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "provid": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "pscp": 7, "psftp": 7, "public": 2, "publish": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "pull": 3, "purpos": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "push": 3, "putti": 4, "putty_ev": 7, "putty_is1": 7, "puttygen": 7, "py": [8, 10], "pyc": 8, "pyd": 8, "pyo": 8, "python": 4, "python312": 8, "python_ev": 8, "pythoncor": 8, "pythonw": 8, "pyw": 8, "q": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "qualiti": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "queri": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "queue": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "ram": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "rar": 0, "rate": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "ratio": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "raw": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "rbac": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "readabl": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "real": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "reboot": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "redistribut": [1, 2, 8, 10], "registri": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "registry_set": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "regul": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "releas": 2, "remot": 3, "repositori": 3, "repres": 4, "reput": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "requir": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "research": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "reset": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "resourc": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "respons": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "rest": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "result": 4, "retent": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "retri": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "rich": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "ripgrep": 10, "risk": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "roam": 2, "role": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "root": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "rsa": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "runtim": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "safe": [1, 6], "sampl": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "sandbox": [1, 2], "schedul": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "schema": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "scilex": 6, "scm": 3, "script": 8, "second": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "secur": 4, "sensit": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "sent": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "servic": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "session": [3, 5, 7], "setup": 2, "setuptool": 8, "sh": 3, "sha": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "share": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "shell": [0, 6], "should": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "sign": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "signatur": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "simon": 7, "simontatham": 7, "simul": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "site": 8, "size": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "skins2": 9, "soc": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "softwar": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "sort": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "sourc": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "spawn": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "sqlite": 2, "ssh": [3, 7], "ssl": 8, "stabl": [1, 2], "standard": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "startup": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "stat": 4, "statist": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "statu": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "storag": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "strategi": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "stream": 9, "strong": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "structur": 4, "studio": 10, "success": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "successfulli": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "support": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "suspici": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "synchron": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "system": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "t": 10, "tamper": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "tar": 0, "target": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "task": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "tatham": 7, "team": 6, "technic": 4, "techniqu": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "telemetri": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "templat": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "term": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "text": 6, "theme": 6, "thi": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "threat": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "throughput": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "time": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "timelin": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "timestamp": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "tl": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "tool": [4, 8], "total": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "track": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "trail": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "transit": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "transmit": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "transport": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "tree": 4, "trust": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "ttt": 0, "turdparti": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "txt": 6, "type": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "typescript": 10, "typic": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "u": [1, 10], "ul": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "unauthor": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "unins000": 10, "uninstal": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "updat": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "upload": 4, "uptim": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "urllib": 8, "us": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "usag": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "user": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "usernam": [1, 2, 10], "usr": 3, "utc": 4, "util": [0, 4], "uuid": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "v": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "v1": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "v2": [3, 9], "v20": 5, "v3": 6, "v8": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "valid": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "vector": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "vendor": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "verif": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "verifi": 4, "version": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "video_filt": 9, "video_output": 9, "videolan": 9, "view": 4, "virtualbox": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "visual": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "visualstudio": 10, "vlc": 4, "vlc_event": 9, "vm": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "vmware": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "vscode": 4, "vscode_ev": 10, "vscodeusersetup": 10, "wa": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "web": [1, 2], "webhook": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "websit": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "when": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "wide": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "widevinecdm": 1, "win64": 9, "window": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "wizard": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "www": [0, 1, 2, 7, 8, 9], "x64": [0, 1, 2, 5, 6, 9, 10], "xml": 6, "xxxxxxxx": 2, "ye": [1, 2, 5, 8, 10], "your": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "zh": 10, "zip": [0, 4]}, "titles": ["7Zip Analysis Report", "Chrome Analysis Report", "Firefox Analysis Report", "Git Analysis Report", "TurdParty 10-Binary Analysis Report", "Nodejs Analysis Report", "Notepadpp Analysis Report", "Putty Analysis Report", "Python Analysis Report", "Vlc Analysis Report", "Vscode Analysis Report"], "titleterms": {"10": 4, "7zip": 0, "access": 4, "analysi": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "assess": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "behavior": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "binari": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "box": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "chrome": 1, "collect": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "data": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "detail": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "ec": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "evid": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "execut": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "firefox": 2, "footprint": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "git": 3, "implement": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "individu": 4, "inform": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "instal": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "navig": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "nodej": 5, "notepadpp": 6, "overview": 4, "putti": 7, "python": 8, "quick": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "report": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "result": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "secur": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "sourc": 4, "stat": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "statist": 4, "structur": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "summari": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "technic": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "tree": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "turdparti": 4, "vlc": 9, "vscode": 10}})