# Simple Sphinx configuration for TurdParty Analysis Reports

project = 'TurdParty 10-Binary Analysis Report'
copyright = '2025, TurdParty Security'
author = 'TurdParty Security Team'
release = '1.0.0'

extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.viewcode',
]

templates_path = ['_templates']
exclude_patterns = ['_build', 'Thumbs.db', '.DS_Store']

html_theme = 'default'
html_static_path = ['_static']

html_title = 'TurdParty 10-Binary Analysis Report'
html_short_title = 'TurdParty Analysis'

# Custom CSS
html_css_files = ['custom.css']

# Source file parsers
source_suffix = '.rst'
master_doc = 'index'
language = 'en'
