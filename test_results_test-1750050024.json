{"start_time": "2025-06-16T07:00:24.250829", "test_session": "test-**********", "tests": {"api_endpoints": {"results": {"pool_status": true, "pool_allocation": true, "pool_deallocation": false, "pool_health": false}, "duration": 6.896245002746582, "success": false}, "celery_integration": {"results": {"task_registration": false, "task_execution": false, "queue_processing": true, "elasticsearch_logging": false}, "duration": 19.241673707962036, "success": false}, "database_operations": {"results": {"pool_configurations": false, "vm_instances": false, "allocation_tracking": false, "cleanup": false}, "duration": 0.0040242671966552734, "success": false}, "elasticsearch_logging": {"results": {"index_exists": true, "log_ingestion": true, "log_structure": true, "search_functionality": true}, "duration": 0.0230104923248291, "success": true}, "priority_allocation": {"results": {"critical_priority": true, "high_priority": true, "normal_priority": true, "low_priority": true, "priority_ordering": true}, "duration": 71.69281673431396, "success": true}, "multi_template_support": {"results": {"ubuntu_20_template": true, "ubuntu_22_template": true, "alpine_template": true, "template_isolation": true}, "duration": 70.7939932346344, "success": true}, "error_handling": {"results": {"invalid_template": true, "invalid_priority": true, "timeout_handling": true, "resource_exhaustion": true}, "duration": 0.014405965805053711, "success": true}}, "summary": {"total_test_categories": 7, "total_individual_tests": 29, "passed_tests": 20, "success_rate": 68.96551724137932, "overall_success": false}, "performance_metrics": {"allocation_times": {"critical": 11.032123804092407, "high": 20.216233253479004, "normal": 20.193679809570312, "low": 20.243149518966675}}, "end_time": "2025-06-16T07:03:12.917120", "test_results": {"allocation_result": {"success": false, "vm_id": null, "request_id": "9316ecb8-125e-4eec-8b53-88bd3217f49f", "status": "queued", "message": "VM allocation queued for template ubuntu:20.04", "queue_position": 7, "estimated_wait_seconds": 420, "allocated_at": null}, "template_results": {"ubuntu:20.04": {"success": false, "vm_id": null, "request_id": "8d79252c-e1b6-4ef8-9195-90a58d49f2f9", "status": "queued", "message": "VM allocation queued for template ubuntu:20.04", "queue_position": 4, "estimated_wait_seconds": 240, "allocated_at": null}, "ubuntu:22.04": {"success": false, "vm_id": null, "request_id": "0e3517d9-c716-41bb-a529-9ee870334774", "status": "queued", "message": "VM allocation queued for template ubuntu:22.04", "queue_position": 5, "estimated_wait_seconds": 300, "allocated_at": null}, "alpine:latest": {"success": false, "vm_id": null, "request_id": "ea4ebfda-1621-45a1-95d3-7461f788b59e", "status": "queued", "message": "VM allocation queued for template alpine:latest", "queue_position": 11, "estimated_wait_seconds": 660, "allocated_at": null}}}}