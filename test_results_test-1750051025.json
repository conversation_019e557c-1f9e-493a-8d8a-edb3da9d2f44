{"start_time": "2025-06-16T07:17:05.439882", "test_session": "test-1750051025", "tests": {"api_endpoints": {"results": {"pool_status": false, "pool_allocation": true}, "duration": 5.0750908851623535, "success": false}, "celery_integration": {"results": {"task_registration": true, "task_execution": true, "queue_processing": true, "elasticsearch_logging": false}, "duration": 19.226799964904785, "success": false}, "database_operations": {"results": {"pool_configurations": true, "vm_instances": true, "allocation_tracking": true, "cleanup": true}, "duration": 0.003489971160888672, "success": true}, "elasticsearch_logging": {"results": {"index_exists": true, "log_ingestion": true, "log_structure": true, "search_functionality": true}, "duration": 0.019488096237182617, "success": true}, "priority_allocation": {"results": {"critical_priority": true, "high_priority": true, "normal_priority": true, "low_priority": true, "priority_ordering": true}, "duration": 35.27347779273987, "success": true}, "multi_template_support": {"results": {"ubuntu_20_template": true, "ubuntu_22_template": true, "alpine_template": true, "template_isolation": true}, "duration": 30.206828117370605, "success": true}, "error_handling": {"results": {"invalid_template": true, "invalid_priority": true, "timeout_handling": true, "resource_exhaustion": true}, "duration": 0.01376032829284668, "success": true}}, "summary": {"total_test_categories": 7, "total_individual_tests": 27, "passed_tests": 25, "success_rate": 92.5925925925926, "overall_success": false}, "performance_metrics": {"allocation_times": {"critical": 5.054535388946533, "high": 10.068470239639282, "normal": 10.070703029632568, "low": 10.0706627368927}}, "end_time": "2025-06-16T07:18:35.258931", "test_results": {"allocation_result": {"success": false, "vm_id": null, "request_id": "f01db1a9-a44b-48c5-bf45-1a18e750bfd4", "status": "queued", "message": "VM allocation queued for template ubuntu:20.04", "queue_position": 1, "estimated_wait_seconds": 60, "allocated_at": null}, "template_results": {"ubuntu:20.04": {"success": false, "vm_id": null, "request_id": "e7d4272e-a6a7-4bb5-8809-ca7356d4b438", "status": "queued", "message": "VM allocation queued for template ubuntu:20.04", "queue_position": 5, "estimated_wait_seconds": 300, "allocated_at": null}, "ubuntu:22.04": {"success": false, "vm_id": null, "request_id": "788ffaf3-27f8-48ad-be10-d6cd12b039c8", "status": "queued", "message": "VM allocation queued for template ubuntu:22.04", "queue_position": 6, "estimated_wait_seconds": 360, "allocated_at": null}, "alpine:latest": {"success": false, "vm_id": null, "request_id": "da054d1f-ba8d-4715-98d9-a27a08f05b6b", "status": "queued", "message": "VM allocation queued for template alpine:latest", "queue_position": 7, "estimated_wait_seconds": 420, "allocated_at": null}}}}