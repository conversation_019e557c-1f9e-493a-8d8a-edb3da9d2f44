{"start_time": "2025-06-16T07:39:32.622173", "test_session": "test-1750052372", "tests": {"api_endpoints": {"results": {"pool_status": true, "pool_allocation": true}, "duration": 5.072460651397705, "success": true}, "celery_integration": {"results": {"task_registration": true, "task_execution": true, "queue_processing": true, "elasticsearch_logging": true}, "duration": 19.235457181930542, "success": true}, "database_operations": {"results": {"pool_configurations": true, "vm_instances": true, "allocation_tracking": true, "cleanup": true}, "duration": 0.003542661666870117, "success": true}, "elasticsearch_logging": {"results": {"index_exists": true, "log_ingestion": true, "log_structure": true, "search_functionality": true}, "duration": 0.022133588790893555, "success": true}, "priority_allocation": {"results": {"critical_priority": true, "high_priority": true, "normal_priority": true, "low_priority": true, "priority_ordering": true}, "duration": 35.282747745513916, "success": true}, "multi_template_support": {"results": {"ubuntu_20_template": true, "ubuntu_22_template": true, "alpine_template": true, "template_isolation": true}, "duration": 30.229570865631104, "success": true}, "error_handling": {"results": {"invalid_template": true, "invalid_priority": true, "timeout_handling": true, "resource_exhaustion": true}, "duration": 0.015796661376953125, "success": true}}, "summary": {"total_test_categories": 7, "total_individual_tests": 27, "passed_tests": 27, "success_rate": 100.0, "overall_success": true}, "performance_metrics": {"allocation_times": {"critical": 5.057419300079346, "high": 10.076235294342041, "normal": 10.072837591171265, "low": 10.066723823547363}}, "end_time": "2025-06-16T07:41:02.483992", "test_results": {"allocation_result": {"success": false, "vm_id": null, "request_id": "6d0e1437-f479-460d-84eb-8040d21f2080", "status": "queued", "message": "VM allocation queued for template ubuntu:20.04", "queue_position": 7, "estimated_wait_seconds": 420, "allocated_at": null}, "template_results": {"ubuntu:20.04": {"success": false, "vm_id": null, "request_id": "aa3d7958-5195-42b2-8f2e-33871ec152ab", "status": "queued", "message": "VM allocation queued for template ubuntu:20.04", "queue_position": 11, "estimated_wait_seconds": 660, "allocated_at": null}, "ubuntu:22.04": {"success": false, "vm_id": null, "request_id": "c1fb378f-f098-4f52-8f24-26b630e3f937", "status": "queued", "message": "VM allocation queued for template ubuntu:22.04", "queue_position": 12, "estimated_wait_seconds": 720, "allocated_at": null}, "alpine:latest": {"success": false, "vm_id": null, "request_id": "ec7bb141-cee7-4eb2-9f3c-43c23a8e45e5", "status": "queued", "message": "VM allocation queued for template alpine:latest", "queue_position": 13, "estimated_wait_seconds": 780, "allocated_at": null}}}}