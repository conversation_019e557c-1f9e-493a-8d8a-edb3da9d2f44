{"start_time": "2025-06-16T20:35:46.075370", "test_session": "test-1750098946", "tests": {"api_endpoints": {"results": {"pool_status": true, "pool_allocation": true}, "duration": 5.071500778198242, "success": true}, "celery_integration": {"results": {"task_registration": true, "task_execution": true, "queue_processing": true, "elasticsearch_logging": true}, "duration": 19.227485418319702, "success": true}, "database_operations": {"results": {"pool_configurations": true, "vm_instances": true, "allocation_tracking": true, "cleanup": true}, "duration": 0.0055201053619384766, "success": true}, "elasticsearch_logging": {"results": {"index_exists": true, "log_ingestion": false, "log_structure": false, "search_functionality": false}, "duration": 0.014542579650878906, "success": false}, "priority_allocation": {"results": {"critical_priority": true, "high_priority": true, "normal_priority": true, "low_priority": true, "priority_ordering": true}, "duration": 35.29068970680237, "success": true}, "multi_template_support": {"results": {"ubuntu_20_template": true, "ubuntu_22_template": true, "alpine_template": true, "template_isolation": true}, "duration": 30.219362020492554, "success": true}, "error_handling": {"results": {"invalid_template": true, "invalid_priority": true, "timeout_handling": true, "resource_exhaustion": true}, "duration": 0.021404027938842773, "success": true}}, "summary": {"total_test_categories": 7, "total_individual_tests": 27, "passed_tests": 24, "success_rate": 88.88888888888889, "overall_success": false}, "performance_metrics": {"allocation_times": {"critical": 5.055057764053345, "high": 10.076651096343994, "normal": 10.07437515258789, "low": 10.076366662979126}}, "end_time": "2025-06-16T20:37:15.925989", "test_results": {"allocation_result": {"success": false, "vm_id": null, "request_id": "6b7229e3-4782-4052-b39a-1c1efb66e697", "status": "queued", "message": "VM allocation queued for template ubuntu:20.04", "queue_position": 14, "estimated_wait_seconds": 840, "allocated_at": null}, "template_results": {"ubuntu:20.04": {"success": false, "vm_id": null, "request_id": "3d9d791c-363d-47f8-a987-58fad83a9864", "status": "queued", "message": "VM allocation queued for template ubuntu:20.04", "queue_position": 19, "estimated_wait_seconds": 1140, "allocated_at": null}, "ubuntu:22.04": {"success": false, "vm_id": null, "request_id": "0ff9dbf1-571f-46de-a4d0-a8238030cfba", "status": "queued", "message": "VM allocation queued for template ubuntu:22.04", "queue_position": 20, "estimated_wait_seconds": 1200, "allocated_at": null}, "alpine:latest": {"success": false, "vm_id": null, "request_id": "78f98720-0e1e-4dc3-aed4-9941f3e0a0d7", "status": "queued", "message": "VM allocation queued for template alpine:latest", "queue_position": 21, "estimated_wait_seconds": 1260, "allocated_at": null}}}}