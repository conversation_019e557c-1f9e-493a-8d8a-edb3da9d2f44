{"start_time": "2025-06-16T20:40:02.551165", "test_session": "test-1750099202", "tests": {"api_endpoints": {"results": {"pool_status": true, "pool_allocation": true}, "duration": 5.0674028396606445, "success": true}, "celery_integration": {"results": {"task_registration": true, "task_execution": true, "queue_processing": true, "elasticsearch_logging": true}, "duration": 19.255070209503174, "success": true}, "database_operations": {"results": {"pool_configurations": true, "vm_instances": true, "allocation_tracking": true, "cleanup": true}, "duration": 0.003895998001098633, "success": true}, "elasticsearch_logging": {"results": {"index_exists": true, "log_ingestion": false, "log_structure": false, "search_functionality": false}, "duration": 0.013987302780151367, "success": false}, "priority_allocation": {"results": {"critical_priority": true, "high_priority": true, "normal_priority": true, "low_priority": true, "priority_ordering": true}, "duration": 35.29128170013428, "success": true}, "multi_template_support": {"results": {"ubuntu_20_template": true, "ubuntu_22_template": true, "alpine_template": true, "template_isolation": true}, "duration": 30.204342126846313, "success": true}, "error_handling": {"results": {"invalid_template": true, "invalid_priority": true, "timeout_handling": true, "resource_exhaustion": true}, "duration": 0.015563011169433594, "success": true}}, "summary": {"total_test_categories": 7, "total_individual_tests": 27, "passed_tests": 24, "success_rate": 88.88888888888889, "overall_success": false}, "performance_metrics": {"allocation_times": {"critical": 5.067359209060669, "high": 10.07312273979187, "normal": 10.068790435791016, "low": 10.071910858154297}}, "end_time": "2025-06-16T20:41:32.402831", "test_results": {"allocation_result": {"success": false, "vm_id": null, "request_id": "6032b5ad-6aeb-4fd4-aedd-fec21b4ce464", "status": "queued", "message": "VM allocation queued for template ubuntu:20.04", "queue_position": 31, "estimated_wait_seconds": 1860, "allocated_at": null}, "template_results": {"ubuntu:20.04": {"success": false, "vm_id": null, "request_id": "82c9cdca-362b-4106-9b67-96737e86f562", "status": "queued", "message": "VM allocation queued for template ubuntu:20.04", "queue_position": 9, "estimated_wait_seconds": 540, "allocated_at": null}, "ubuntu:22.04": {"success": false, "vm_id": null, "request_id": "8c3cf96c-413e-4de6-801f-3aad9a6b76b1", "status": "queued", "message": "VM allocation queued for template ubuntu:22.04", "queue_position": 36, "estimated_wait_seconds": 2160, "allocated_at": null}, "alpine:latest": {"success": false, "vm_id": null, "request_id": "7c55eb31-7bcc-4eee-9ff6-022dd793a50f", "status": "queued", "message": "VM allocation queued for template alpine:latest", "queue_position": 37, "estimated_wait_seconds": 2220, "allocated_at": null}}}}