{"start_time": "2025-06-16T20:54:58.742646", "test_session": "test-1750100098", "tests": {"api_endpoints": {"results": {"pool_status": true, "pool_allocation": true}, "duration": 5.066725730895996, "success": true}, "celery_integration": {"results": {"task_registration": true, "task_execution": true, "queue_processing": true, "elasticsearch_logging": true}, "duration": 19.18786859512329, "success": true}, "database_operations": {"results": {"pool_configurations": true, "vm_instances": true, "allocation_tracking": true, "cleanup": true}, "duration": 8.106231689453125e-06, "success": true}, "elasticsearch_logging": {"results": {"index_exists": true, "log_ingestion": true, "log_structure": true, "search_functionality": true}, "duration": 0.014358282089233398, "success": true}, "priority_allocation": {"results": {"critical_priority": true, "high_priority": true, "normal_priority": true, "low_priority": true, "priority_ordering": true}, "duration": 35.279451847076416, "success": true}, "multi_template_support": {"results": {"ubuntu_20_template": true, "ubuntu_22_template": true, "alpine_template": true, "template_isolation": true}, "duration": 30.205348253250122, "success": true}, "error_handling": {"results": {"invalid_template": true, "invalid_priority": true, "timeout_handling": true, "resource_exhaustion": true}, "duration": 0.012213468551635742, "success": true}}, "summary": {"total_test_categories": 7, "total_individual_tests": 27, "passed_tests": 27, "success_rate": 100.0, "overall_success": true}, "performance_metrics": {"allocation_times": {"critical": 5.055071115493774, "high": 10.070996522903442, "normal": 10.072349786758423, "low": 10.073572158813477}}, "end_time": "2025-06-16T20:56:28.508737", "test_results": {"allocation_result": {"success": false, "vm_id": null, "request_id": "6c96872f-cbf8-454b-8d2e-26ca5e2dd7d7", "status": "queued", "message": "VM allocation queued for template ubuntu:20.04", "queue_position": 46, "estimated_wait_seconds": 2760, "allocated_at": null}, "template_results": {"ubuntu:20.04": {"success": false, "vm_id": null, "request_id": "64b3855f-7e1e-4351-a30f-25cec358374a", "status": "queued", "message": "VM allocation queued for template ubuntu:20.04", "queue_position": 51, "estimated_wait_seconds": 3060, "allocated_at": null}, "ubuntu:22.04": {"success": false, "vm_id": null, "request_id": "7660c1b1-b15e-45d6-80ef-e852edb1c77e", "status": "queued", "message": "VM allocation queued for template ubuntu:22.04", "queue_position": 52, "estimated_wait_seconds": 3120, "allocated_at": null}, "alpine:latest": {"success": false, "vm_id": null, "request_id": "a4b7b056-53ba-4ea0-9106-2ece1d63cc3f", "status": "queued", "message": "VM allocation queued for template alpine:latest", "queue_position": 10, "estimated_wait_seconds": 600, "allocated_at": null}}}}