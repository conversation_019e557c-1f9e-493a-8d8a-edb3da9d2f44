#!/bin/bash

# TurdParty API Testing Suite
# Comprehensive testing of all API endpoints and workflows

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 TurdParty API Testing Suite${NC}"
echo "=================================="

# Check if we're in the correct directory
if [ ! -f "test_api_endpoints.py" ]; then
    echo -e "${RED}❌ Error: This script must be run from the tests/api directory${NC}"
    exit 1
fi

# Check if API is running
echo -e "${YELLOW}🔍 Checking if TurdParty API is running...${NC}"

API_URL="http://localhost:8000"
if ! curl -f "$API_URL/health" >/dev/null 2>&1; then
    echo -e "${RED}❌ TurdParty API is not accessible at $API_URL${NC}"
    echo -e "${YELLOW}💡 Please start the API service first:${NC}"
    echo "   cd ../../compose && docker compose up -d"
    exit 1
fi

echo -e "${GREEN}✅ API is running and accessible${NC}"

# Check API health details
echo -e "${YELLOW}🔍 Checking API health details...${NC}"
HEALTH_RESPONSE=$(curl -s "$API_URL/health")
echo -e "${BLUE}API Health: $HEALTH_RESPONSE${NC}"

# Parse command line arguments
RUN_ENDPOINTS=true
RUN_FLOW=true
VERBOSE=false
PYTHON_CMD="python3"

while [[ $# -gt 0 ]]; do
    case $1 in
        --endpoints-only)
            RUN_FLOW=false
            shift
            ;;
        --flow-only)
            RUN_ENDPOINTS=false
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --python)
            PYTHON_CMD="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --endpoints-only    Run only endpoint tests"
            echo "  --flow-only         Run only flow tests"
            echo "  --verbose           Enable verbose output"
            echo "  --python <cmd>      Python command to use (default: python3)"
            echo "  --help              Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                          # Run all tests"
            echo "  $0 --endpoints-only         # Run only endpoint tests"
            echo "  $0 --flow-only --verbose    # Run only flow tests with verbose output"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ Unknown option: $1${NC}"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check Python and required packages
echo -e "${YELLOW}🐍 Checking Python environment...${NC}"

if ! command -v $PYTHON_CMD &> /dev/null; then
    echo -e "${RED}❌ Python command '$PYTHON_CMD' not found${NC}"
    exit 1
fi

# Check if requests is available
if ! $PYTHON_CMD -c "import requests" 2>/dev/null; then
    echo -e "${YELLOW}⚠️  Installing required Python packages...${NC}"
    $PYTHON_CMD -m pip install requests pytest --user
fi

echo -e "${GREEN}✅ Python environment ready${NC}"

# Function to run tests with proper error handling
run_test() {
    local test_file=$1
    local test_name=$2
    
    echo ""
    echo -e "${PURPLE}🧪 Running $test_name...${NC}"
    echo "----------------------------------------"
    
    if [ "$VERBOSE" = true ]; then
        $PYTHON_CMD "$test_file"
    else
        $PYTHON_CMD "$test_file" 2>&1 | grep -E "(✅|❌|🎉|Step [0-9]+:|Testing|Error|Failed|Passed)"
    fi
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        echo -e "${GREEN}✅ $test_name completed successfully${NC}"
        return 0
    else
        echo -e "${RED}❌ $test_name failed${NC}"
        return 1
    fi
}

# Track test results
TOTAL_TESTS=0
PASSED_TESTS=0

# Run endpoint tests
if [ "$RUN_ENDPOINTS" = true ]; then
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test "test_api_endpoints.py" "API Endpoints Tests"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    fi
fi

# Run flow tests
if [ "$RUN_FLOW" = true ]; then
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test "test_api_flow.py" "API Flow Tests"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    fi
fi

# Summary
echo ""
echo "=========================================="
echo -e "${BLUE}📊 Test Results Summary${NC}"
echo "=========================================="

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo -e "${GREEN}🎉 All tests passed! ($PASSED_TESTS/$TOTAL_TESTS)${NC}"
    echo ""
    echo -e "${GREEN}✅ API Endpoints: Working correctly${NC}"
    echo -e "${GREEN}✅ API Workflows: Functioning properly${NC}"
    echo -e "${GREEN}✅ Error Handling: Robust and reliable${NC}"
    echo ""
    echo -e "${BLUE}🚀 TurdParty API is ready for production use!${NC}"
    
    # Show API documentation link
    echo ""
    echo -e "${YELLOW}📚 API Documentation:${NC}"
    echo "  • Interactive docs: $API_URL/docs"
    echo "  • ReDoc: $API_URL/redoc"
    echo "  • OpenAPI JSON: $API_URL/openapi.json"
    
    exit 0
else
    echo -e "${RED}❌ Some tests failed ($PASSED_TESTS/$TOTAL_TESTS passed)${NC}"
    echo ""
    echo -e "${YELLOW}🔧 Troubleshooting tips:${NC}"
    echo "  • Check API logs: docker logs turdpartycollab_api"
    echo "  • Verify all services are running: docker compose ps"
    echo "  • Check API health: curl $API_URL/health"
    echo "  • Run with --verbose for detailed output"
    echo ""
    echo -e "${BLUE}📚 For more information, check the API documentation at $API_URL/docs${NC}"
    
    exit 1
fi
