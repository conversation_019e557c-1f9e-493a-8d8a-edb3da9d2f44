"""
Comprehensive API endpoint tests for TurdParty
"""
import requests
import json
import time
from typing import Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test configuration
API_BASE_URL = "http://localhost:8000"
API_V1_BASE = f"{API_BASE_URL}/api/v1"

class TestAPIEndpoints:
    """Test suite for all API endpoints"""
    
    def setup_method(self):
        """Setup method run before each test"""
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

        # Test data storage
        self.test_file_id = None
        self.test_workflow_id = None
        
    def test_health_endpoint(self):
        """Test the health check endpoint"""
        logger.info("Testing health endpoint...")

        response = self.session.get(f"{API_BASE_URL}/health/")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "turdparty-api"
        assert "timestamp" in data

        logger.info("✅ Health endpoint test passed")
    
    def test_files_list_endpoint(self):
        """Test the files listing endpoint"""
        logger.info("Testing files list endpoint...")
        
        response = self.session.get(f"{API_V1_BASE}/files/")
        
        assert response.status_code == 200
        data = response.json()
        assert "files" in data
        assert "total" in data
        assert "skip" in data
        assert "limit" in data
        assert isinstance(data["files"], list)
        
        logger.info(f"✅ Files list endpoint test passed - found {data['total']} files")
    
    def test_files_list_pagination(self):
        """Test files listing with pagination"""
        logger.info("Testing files list pagination...")
        
        # Test with skip and limit parameters
        response = self.session.get(f"{API_V1_BASE}/files/?skip=0&limit=5")
        
        assert response.status_code == 200
        data = response.json()
        assert data["skip"] == 0
        assert data["limit"] == 5
        assert len(data["files"]) <= 5
        
        logger.info("✅ Files pagination test passed")
    
    def test_file_upload_endpoint(self):
        """Test file upload endpoint"""
        logger.info("Testing file upload endpoint...")

        # Create a test file
        test_file_content = b"This is a test file for API testing"
        files = {
            'file': ('test_api_file.txt', test_file_content, 'text/plain')
        }
        data = {
            'description': 'Test file uploaded via API test'
        }

        # Remove Content-Type header for multipart upload
        headers = {k: v for k, v in self.session.headers.items() if k.lower() != 'content-type'}

        response = requests.post(
            f"{API_V1_BASE}/files/upload",
            files=files,
            data=data,
            headers=headers
        )

        assert response.status_code == 200  # Real API returns 200, not 201
        upload_data = response.json()
        assert "file_id" in upload_data
        assert upload_data["filename"] == "test_api_file.txt"
        assert upload_data["status"] == "stored"
        assert "created_at" in upload_data

        # Store file ID for later tests
        self.test_file_id = upload_data["file_id"]

        logger.info(f"✅ File upload test passed - file ID: {self.test_file_id}")
    
    def test_file_get_by_id(self):
        """Test getting a file by ID"""
        if not self.test_file_id:
            self.test_file_upload_endpoint()
        
        logger.info(f"Testing file get by ID: {self.test_file_id}")
        
        response = self.session.get(f"{API_V1_BASE}/files/{self.test_file_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["file_id"] == self.test_file_id
        assert data["filename"] == "test_api_file.txt"
        assert data["status"] == "stored"
        
        logger.info("✅ File get by ID test passed")
    
    def test_file_get_nonexistent(self):
        """Test getting a non-existent file"""
        logger.info("Testing file get for non-existent file...")
        
        fake_id = "00000000-0000-0000-0000-000000000000"
        response = self.session.get(f"{API_V1_BASE}/files/{fake_id}")
        
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"].lower()
        
        logger.info("✅ File not found test passed")
    
    def test_workflow_list_endpoint(self):
        """Test workflow listing endpoint"""
        logger.info("Testing workflow list endpoint...")

        response = self.session.get(f"{API_V1_BASE}/workflow/")

        assert response.status_code == 200
        data = response.json()
        assert "workflows" in data
        assert "total" in data
        assert "skip" in data
        assert "limit" in data
        assert isinstance(data["workflows"], list)

        logger.info(f"✅ Workflow list test passed - found {data['total']} workflows")
    
    def test_workflow_create(self):
        """Test creating a workflow"""
        if not self.test_file_id:
            self.test_file_upload_endpoint()

        logger.info("Testing workflow creation...")

        workflow_data = {
            "file_id": self.test_file_id,
            "vm_template": "ubuntu/focal64",
            "vm_memory_mb": 1024,
            "vm_cpus": 1,
            "injection_path": "/tmp/test_injection.txt",
            "description": "Test workflow via API test"
        }

        # Remove Content-Type header for form data
        headers = {k: v for k, v in self.session.headers.items() if k.lower() != 'content-type'}

        response = requests.post(
            f"{API_V1_BASE}/workflow/start",
            data=workflow_data,
            headers=headers
        )

        if response.status_code != 200:
            logger.error(f"Workflow creation failed: {response.status_code} - {response.text}")
        assert response.status_code == 200
        data = response.json()
        assert "workflow_job_id" in data
        assert data["file_id"] == self.test_file_id
        assert "status" in data  # Status can be "pending", "vm_creating", etc.
        assert "created_at" in data

        # Store workflow ID for later tests
        self.test_workflow_id = data["workflow_job_id"]

        logger.info(f"✅ Workflow creation test passed - workflow ID: {self.test_workflow_id}")
    
    def test_workflow_get_by_id(self):
        """Test getting a workflow by ID"""
        if not self.test_workflow_id:
            self.test_workflow_create()

        logger.info(f"Testing workflow get by ID: {self.test_workflow_id}")

        response = self.session.get(f"{API_V1_BASE}/workflow/{self.test_workflow_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["workflow_job_id"] == self.test_workflow_id
        assert "status" in data

        logger.info("✅ Workflow get by ID test passed")
    
    def test_vm_templates_endpoint(self):
        """Test VM templates endpoint"""
        logger.info("Testing VM templates endpoint...")

        response = self.session.get(f"{API_V1_BASE}/vms/templates")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0

        # Check that templates have required fields
        for template in data:
            assert "name" in template
            assert "description" in template

        logger.info(f"✅ VM templates test passed - found {len(data)} templates")
    
    def test_vm_management_endpoints(self):
        """Test VM management endpoints"""
        logger.info("Testing VM management endpoints...")

        # Test list VMs endpoint
        response = self.session.get(f"{API_V1_BASE}/vms/")
        assert response.status_code == 200
        data = response.json()
        assert "vms" in data
        assert "total" in data
        assert isinstance(data["vms"], list)

        logger.info(f"✅ VM list test passed - found {data['total']} VMs")

        # Test create VM endpoint (optional - might be resource intensive)
        vm_data = {
            "name": f"test-vm-api-{int(__import__('time').time())}",
            "template": "ubuntu/focal64",
            "vm_type": "docker",
            "memory_mb": 512,
            "cpus": 1,
            "description": "Test VM created via API test"
        }

        try:
            response = self.session.post(f"{API_V1_BASE}/vms/", json=vm_data)
            if response.status_code == 201:
                vm_response = response.json()
                vm_id = vm_response["vm_id"]
                logger.info(f"✅ VM creation test passed - VM ID: {vm_id}")

                # Clean up - delete the test VM
                delete_response = self.session.delete(f"{API_V1_BASE}/vms/{vm_id}")
                if delete_response.status_code == 200:
                    logger.info("✅ VM cleanup successful")
            else:
                logger.info("⚠️ VM creation skipped (may require additional resources)")
        except Exception as e:
            logger.info(f"⚠️ VM creation test skipped: {e}")

        logger.info("✅ VM management endpoints test completed")
    
    def test_health_detailed_endpoints(self):
        """Test detailed health endpoints"""
        logger.info("Testing detailed health endpoints...")

        # Test detailed health endpoint
        response = self.session.get(f"{API_BASE_URL}/health/detailed")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "timestamp" in data

        # Test readiness endpoint
        response = self.session.get(f"{API_BASE_URL}/health/ready")
        assert response.status_code == 200
        ready_data = response.json()
        assert isinstance(ready_data, dict)

        logger.info("✅ Health detailed endpoints test passed")
    
    def test_api_error_handling(self):
        """Test API error handling"""
        logger.info("Testing API error handling...")
        
        # Test invalid endpoint
        response = self.session.get(f"{API_V1_BASE}/nonexistent/")
        assert response.status_code == 404
        
        # Test invalid JSON to workflow endpoint
        response = requests.post(
            f"{API_V1_BASE}/workflow/start",
            data="invalid json",
            headers={'Content-Type': 'application/json'}
        )
        # Should return 422 for validation error or 400 for bad request
        assert response.status_code in [400, 422, 415]  # 415 = Unsupported Media Type
        
        logger.info("✅ API error handling test passed")
    
    def test_api_documentation(self):
        """Test API documentation endpoints"""
        logger.info("Testing API documentation...")
        
        # Test OpenAPI docs
        response = self.session.get(f"{API_BASE_URL}/docs")
        assert response.status_code == 200
        
        # Test OpenAPI JSON
        response = self.session.get(f"{API_BASE_URL}/openapi.json")
        assert response.status_code == 200
        data = response.json()
        assert "openapi" in data
        assert "info" in data
        
        logger.info("✅ API documentation test passed")

if __name__ == "__main__":
    # Run tests directly
    test_suite = TestAPIEndpoints()
    test_suite.setup_method()
    
    try:
        test_suite.test_health_endpoint()
        test_suite.test_files_list_endpoint()
        test_suite.test_files_list_pagination()
        test_suite.test_file_upload_endpoint()
        test_suite.test_file_get_by_id()
        test_suite.test_file_get_nonexistent()
        test_suite.test_workflow_list_endpoint()
        test_suite.test_workflow_create()
        test_suite.test_workflow_get_by_id()
        test_suite.test_vm_templates_endpoint()
        test_suite.test_vm_management_endpoints()
        test_suite.test_health_detailed_endpoints()
        test_suite.test_api_error_handling()
        test_suite.test_api_documentation()
        
        print("\n🎉 All API endpoint tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
