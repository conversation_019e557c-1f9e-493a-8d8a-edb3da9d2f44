"""
Extended API endpoint tests for TurdParty
Tests advanced endpoints: Admin, ECS, Health, VM Actions, File Downloads
Uses centralized ServiceURLManager and Traefik routing
"""
import requests
import json
import time
import sys
import os
from typing import Dict, Any
import logging

# Add utils to path for ServiceURLManager
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'utils'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get API URL from centralized ServiceURLManager
try:
    from service_urls import ServiceURLManager
    url_manager = ServiceURLManager('development')
    traefik_url = url_manager.get_service_url('api')

    # Use Traefik routing for proper service integration
    if traefik_url and 'api.turdparty.localhost' in traefik_url:
        API_BASE_URL = traefik_url  # Use Traefik URL directly
        logger.info(f"Using Traefik routing: {traefik_url}")
    else:
        API_BASE_URL = "http://localhost:8000"  # Fallback only if ServiceURLManager fails
        logger.warning("ServiceURLManager not working, using fallback")
except ImportError:
    # Fallback if ServiceURLManager not available
    API_BASE_URL = "http://localhost:8000"
    logger.warning("ServiceURLManager not available, using fallback")

API_V1_BASE = f"{API_BASE_URL}/api/v1"

logger.info(f"Using API Base URL: {API_BASE_URL}")

class TestExtendedAPIEndpoints:
    """Extended test suite for advanced API endpoints"""
    
    def setup_method(self):
        """Setup method run before each test"""
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

        # Test data storage
        self.test_file_id = None
        self.test_vm_id = None
        
    def test_comprehensive_health_endpoints(self):
        """Test all individual health check endpoints"""
        logger.info("Testing comprehensive health endpoints...")

        health_endpoints = [
            "celery", "database", "elasticsearch", "kibana", 
            "minio", "redis", "vm-monitor"
        ]

        for endpoint in health_endpoints:
            logger.info(f"Testing health endpoint: {endpoint}")
            response = self.session.get(f"{API_V1_BASE}/health/{endpoint}")
            
            # Health endpoints should return 200 or 503 (service unavailable)
            assert response.status_code in [200, 503], f"Health endpoint {endpoint} failed: {response.status_code}"
            
            if response.status_code == 200:
                data = response.json()
                assert "status" in data
                logger.info(f"✅ Health endpoint {endpoint}: {data.get('status', 'unknown')}")
            else:
                logger.info(f"⚠️ Health endpoint {endpoint}: Service unavailable")

        logger.info("✅ Comprehensive health endpoints test completed")
    
    def test_ecs_endpoints(self):
        """Test ECS/Logging endpoints"""
        logger.info("Testing ECS endpoints...")

        # Test ECS health
        response = self.session.get(f"{API_V1_BASE}/ecs/ecs/health")
        assert response.status_code in [200, 503]
        logger.info(f"✅ ECS health endpoint: {response.status_code}")

        # Test ECS indices
        response = self.session.get(f"{API_V1_BASE}/ecs/ecs/indices")
        assert response.status_code in [200, 503]
        
        if response.status_code == 200:
            data = response.json()
            assert isinstance(data, (list, dict))
            logger.info(f"✅ ECS indices endpoint: Found {len(data) if isinstance(data, list) else 'data'}")
        else:
            logger.info("⚠️ ECS indices endpoint: Service unavailable")

        logger.info("✅ ECS endpoints test completed")
    
    def test_admin_endpoints(self):
        """Test Admin endpoints"""
        logger.info("Testing Admin endpoints...")

        # Test admin health
        response = self.session.get(f"{API_V1_BASE}/admin/admin/health")
        assert response.status_code in [200, 403, 404]  # May require auth or not exist
        logger.info(f"✅ Admin health endpoint: {response.status_code}")

        # Test docs rebuild (should be safe to test)
        response = self.session.post(f"{API_V1_BASE}/admin/admin/docs/rebuild")
        assert response.status_code in [200, 202, 403, 404, 422]  # Various acceptable responses
        logger.info(f"✅ Admin docs rebuild endpoint: {response.status_code}")

        logger.info("✅ Admin endpoints test completed")
    
    def test_file_download_url_endpoint(self):
        """Test file download URL generation"""
        logger.info("Testing file download URL endpoint...")

        # First upload a test file
        test_file_content = b"Test file for download URL testing"
        files = {
            'file': ('download_test.txt', test_file_content, 'text/plain')
        }
        data = {
            'description': 'Test file for download URL endpoint'
        }

        # Remove Content-Type header for multipart upload
        headers = {k: v for k, v in self.session.headers.items() if k.lower() != 'content-type'}

        response = requests.post(
            f"{API_V1_BASE}/files/upload",
            files=files,
            data=data,
            headers=headers
        )

        assert response.status_code == 200
        upload_data = response.json()
        file_id = upload_data["file_id"]
        self.test_file_id = file_id

        # Test download URL generation
        response = self.session.get(f"{API_V1_BASE}/files/{file_id}/download-url")
        assert response.status_code in [200, 404]  # May not be implemented
        
        if response.status_code == 200:
            data = response.json()
            assert "download_url" in data or "url" in data
            logger.info("✅ File download URL endpoint working")
        else:
            logger.info("⚠️ File download URL endpoint not implemented")

        logger.info("✅ File download URL test completed")
    
    def test_vm_action_endpoints(self):
        """Test VM action endpoints"""
        logger.info("Testing VM action endpoints...")

        # First create a test VM
        vm_data = {
            "name": f"test-vm-actions-{int(time.time())}",
            "template": "ubuntu/focal64",
            "vm_type": "docker",
            "memory_mb": 256,
            "cpus": 1,
            "description": "Test VM for action endpoints"
        }

        try:
            response = self.session.post(f"{API_V1_BASE}/vms/", json=vm_data)
            if response.status_code == 201:
                vm_response = response.json()
                vm_id = vm_response["vm_id"]
                self.test_vm_id = vm_id
                logger.info(f"✅ Test VM created: {vm_id}")

                # Test VM actions
                actions = ["start", "stop", "pause", "resume"]
                for action in actions:
                    action_data = {"action": action}
                    response = self.session.post(f"{API_V1_BASE}/vms/{vm_id}/action", json=action_data)
                    
                    # Actions may fail depending on VM state, so accept various codes
                    assert response.status_code in [200, 400, 409, 500]
                    logger.info(f"✅ VM action '{action}': {response.status_code}")

                # Clean up - delete the test VM
                delete_response = self.session.delete(f"{API_V1_BASE}/vms/{vm_id}")
                if delete_response.status_code == 200:
                    logger.info("✅ Test VM cleanup successful")

            else:
                logger.info("⚠️ VM action tests skipped (VM creation failed)")

        except Exception as e:
            logger.info(f"⚠️ VM action tests skipped: {e}")

        logger.info("✅ VM action endpoints test completed")
    
    def test_dev_info_endpoint(self):
        """Test development info endpoint"""
        logger.info("Testing dev info endpoint...")

        response = self.session.get(f"{API_BASE_URL}/dev/info")
        assert response.status_code in [200, 404]  # May not exist in production
        
        if response.status_code == 200:
            data = response.json()
            assert isinstance(data, dict)
            logger.info("✅ Dev info endpoint working")
        else:
            logger.info("⚠️ Dev info endpoint not available")

        logger.info("✅ Dev info endpoint test completed")
    
    def test_ecs_vm_events(self):
        """Test ECS VM event endpoints"""
        logger.info("Testing ECS VM event endpoints...")

        # Use a fake VM ID for testing
        fake_vm_id = "test-vm-12345"
        
        # Test VM events endpoint
        response = self.session.get(f"{API_V1_BASE}/ecs/ecs/events/{fake_vm_id}")
        assert response.status_code in [200, 404, 422, 503]  # Various acceptable responses
        logger.info(f"✅ ECS VM events endpoint: {response.status_code}")

        # Test VM events summary
        response = self.session.get(f"{API_V1_BASE}/ecs/ecs/events/{fake_vm_id}/summary")
        assert response.status_code in [200, 404, 422, 503]
        logger.info(f"✅ ECS VM events summary endpoint: {response.status_code}")

        logger.info("✅ ECS VM events test completed")

if __name__ == "__main__":
    # Run tests directly
    test_suite = TestExtendedAPIEndpoints()
    test_suite.setup_method()
    
    try:
        test_suite.test_comprehensive_health_endpoints()
        test_suite.test_ecs_endpoints()
        test_suite.test_admin_endpoints()
        test_suite.test_file_download_url_endpoint()
        test_suite.test_vm_action_endpoints()
        test_suite.test_dev_info_endpoint()
        test_suite.test_ecs_vm_events()
        
        print("\n🎉 All extended API endpoint tests completed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
