"""
Tests for Standardized Error Handling in TurdParty API

This test suite verifies that all API endpoints return consistent error responses
with proper error codes, status codes, and structured error information.
"""

import pytest
import json
from datetime import datetime
from unittest.mock import Mock, patch

from api.models.error_responses import (
    ErrorResponse, ErrorCode, ValidationErrorDetail,
    create_error_response, create_success_response
)
from api.exceptions.api_exceptions import (
    TurdPartyAPIException, ResourceNotFoundException,
    ValidationException, FileInjectionException
)


class TestErrorResponseModels:
    """Test error response model creation and validation."""
    
    def test_error_response_creation(self):
        """Test creating a standardized error response."""
        error_response = create_error_response(
            error_code=ErrorCode.VALIDATION_ERROR,
            message="Test validation error",
            details="Field 'name' is required",
            request_id="test-123"
        )
        
        assert error_response.error is True
        assert error_response.error_code == ErrorCode.VALIDATION_ERROR
        assert error_response.message == "Test validation error"
        assert error_response.details == "Field 'name' is required"
        assert error_response.request_id == "test-123"
        assert isinstance(error_response.timestamp, datetime)
    
    def test_success_response_creation(self):
        """Test creating a standardized success response."""
        success_response = create_success_response(
            message="Operation completed successfully",
            data={"id": "123", "status": "completed"},
            request_id="test-456"
        )
        
        assert success_response.success is True
        assert success_response.message == "Operation completed successfully"
        assert success_response.data == {"id": "123", "status": "completed"}
        assert success_response.request_id == "test-456"
        assert isinstance(success_response.timestamp, datetime)
    
    def test_validation_error_details(self):
        """Test validation error details structure."""
        validation_errors = [
            ValidationErrorDetail(
                field="filename",
                message="Field is required",
                code="missing",
                value=None
            ),
            ValidationErrorDetail(
                field="file_size",
                message="Value must be positive",
                code="value_error",
                value=-1
            )
        ]
        
        error_response = create_error_response(
            error_code=ErrorCode.VALIDATION_ERROR,
            message="Validation failed",
            validation_errors=validation_errors
        )
        
        assert len(error_response.validation_errors) == 2
        assert error_response.validation_errors[0].field == "filename"
        assert error_response.validation_errors[1].field == "file_size"


class TestCustomExceptions:
    """Test custom API exception classes."""
    
    def test_resource_not_found_exception(self):
        """Test ResourceNotFoundException creation."""
        exc = ResourceNotFoundException(
            resource_type="File",
            resource_id="123"
        )
        
        assert exc.status_code == 404
        assert exc.error_code == ErrorCode.RESOURCE_NOT_FOUND
        assert "File with ID '123' not found" in exc.message
        assert exc.details == "Resource type: File, ID: 123"
    
    def test_validation_exception(self):
        """Test ValidationException creation."""
        validation_errors = [
            ValidationErrorDetail(
                field="email",
                message="Invalid email format",
                code="format_error",
                value="invalid-email"
            )
        ]
        
        exc = ValidationException(
            message="Input validation failed",
            validation_errors=validation_errors
        )
        
        assert exc.status_code == 422
        assert exc.error_code == ErrorCode.VALIDATION_ERROR
        assert exc.message == "Input validation failed"
        assert len(exc.validation_errors) == 1
    
    def test_file_injection_exception(self):
        """Test FileInjectionException creation."""
        exc = FileInjectionException(
            message="File injection failed",
            details="VM not accessible"
        )
        
        assert exc.status_code == 500
        assert exc.error_code == ErrorCode.FILE_INJECTION_FAILED
        assert exc.message == "File injection failed"
        assert exc.details == "VM not accessible"


class TestErrorCodeMappings:
    """Test error code to HTTP status code mappings."""
    
    def test_status_code_mappings(self):
        """Test that error codes map to correct HTTP status codes."""
        from api.models.error_responses import STATUS_CODE_MAPPINGS
        
        # Test key mappings
        assert STATUS_CODE_MAPPINGS[400] == ErrorCode.INVALID_INPUT
        assert STATUS_CODE_MAPPINGS[401] == ErrorCode.AUTHENTICATION_REQUIRED
        assert STATUS_CODE_MAPPINGS[403] == ErrorCode.ACCESS_DENIED
        assert STATUS_CODE_MAPPINGS[404] == ErrorCode.RESOURCE_NOT_FOUND
        assert STATUS_CODE_MAPPINGS[422] == ErrorCode.VALIDATION_ERROR
        assert STATUS_CODE_MAPPINGS[429] == ErrorCode.RATE_LIMIT_EXCEEDED
        assert STATUS_CODE_MAPPINGS[500] == ErrorCode.INTERNAL_SERVER_ERROR
        assert STATUS_CODE_MAPPINGS[503] == ErrorCode.SERVICE_UNAVAILABLE


class TestErrorResponseSerialization:
    """Test error response JSON serialization."""
    
    def test_error_response_json_serialization(self):
        """Test that error responses serialize correctly to JSON."""
        error_response = create_error_response(
            error_code=ErrorCode.RESOURCE_NOT_FOUND,
            message="Resource not found",
            details="The requested file does not exist",
            request_id="req-123"
        )
        
        # Convert to dict (simulates FastAPI JSON response)
        response_dict = error_response.model_dump()
        
        # Verify structure
        assert response_dict["error"] is True
        assert response_dict["error_code"] == "RESOURCE_NOT_FOUND"
        assert response_dict["message"] == "Resource not found"
        assert response_dict["details"] == "The requested file does not exist"
        assert response_dict["request_id"] == "req-123"
        assert "timestamp" in response_dict
        
        # Verify JSON serialization works
        json_str = json.dumps(response_dict, default=str)
        assert "RESOURCE_NOT_FOUND" in json_str
    
    def test_validation_error_json_serialization(self):
        """Test validation error response JSON serialization."""
        validation_errors = [
            ValidationErrorDetail(
                field="filename",
                message="Required field missing",
                code="missing",
                value=None
            )
        ]
        
        error_response = create_error_response(
            error_code=ErrorCode.VALIDATION_ERROR,
            message="Validation failed",
            validation_errors=validation_errors
        )
        
        response_dict = error_response.model_dump()
        
        # Verify validation errors are included
        assert "validation_errors" in response_dict
        assert len(response_dict["validation_errors"]) == 1
        assert response_dict["validation_errors"][0]["field"] == "filename"
        assert response_dict["validation_errors"][0]["code"] == "missing"


class TestExceptionHandlerIntegration:
    """Test exception handler integration (requires FastAPI app)."""

    def test_exception_handler_setup(self):
        """Test that exception handlers can be set up without errors."""
        from fastapi import FastAPI
        from api.middleware.exception_handlers import setup_exception_handlers
        
        app = FastAPI()
        
        # Should not raise any exceptions
        setup_exception_handlers(app)
        
        # Verify handlers are registered
        assert len(app.exception_handlers) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
