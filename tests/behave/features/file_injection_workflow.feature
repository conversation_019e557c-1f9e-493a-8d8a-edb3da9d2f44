Feature: File Injection Workflow
  As a malware analyst
  I want to inject files into VMs through the complete TurdParty workflow
  So that I can analyze malware behavior in isolated environments

  Background:
    Given the TurdParty system is running
    And all required services are available
    And I have access to the file injection API

  @file_injection @workflow @production
  Scenario: Complete File Injection Lifecycle
    Given I have a test executable file "test_binary.exe"
    And the file is approximately 2 MB in size
    When I upload the file to the TurdParty API
    Then the upload should succeed
    And I should receive a valid file UUID
    And the file should be stored in MinIO
    And a database record should be created

    When I create a VM for file injection
    Then the VM should be provisioned successfully
    And the VM should be running within 3 minutes
    And the VM should have SSH connectivity

    When I inject the file into the VM at path "C:\temp\test_binary.exe"
    Then the file injection should succeed
    And the file should be present in the VM
    And the file should have correct permissions
    And the injection should be logged to Elasticsearch

  @file_injection @multiple_files @concurrent
  Scenario: Multiple File Injection Handling
    Given I have multiple test files:
      | filename        | size_mb | target_path              |
      | malware1.exe    | 1.5     | C:\temp\malware1.exe     |
      | malware2.dll    | 0.8     | C:\temp\malware2.dll     |
      | config.xml      | 0.1     | C:\temp\config.xml       |
    When I upload all files simultaneously
    Then all uploads should succeed
    And each file should receive a unique UUID

    When I create VMs for each file injection
    Then all VMs should be provisioned successfully
    And all VMs should be running within 5 minutes

    When I inject all files into their respective VMs
    Then all file injections should succeed
    And all files should be present in their VMs
    And all injections should be logged separately

  @file_injection @error_handling @security
  Scenario: File Injection Error Recovery
    Given I have a test file "test.exe"
    When I attempt to inject the file to an invalid VM
    Then the injection should fail gracefully
    And an appropriate error should be returned
    And no partial injection should occur

    When I attempt to inject to an invalid path
    Then the injection should fail with path validation error
    And the VM should remain unaffected

    When I attempt to inject a non-existent file
    Then the injection should fail with file not found error
    And appropriate logging should occur

  @file_injection @permissions @validation
  Scenario: Permission Handling Validation
    Given I have a test script "test_script.ps1"
    When I inject the file with execute permissions
    Then the file should be executable in the VM
    And the permissions should be correctly set

    When I inject the file with read-only permissions
    Then the file should not be executable
    And the permissions should be correctly restricted

  @file_injection @security @malicious
  Scenario: Security Validation During Injection
    Given I have a potentially malicious file "suspicious.exe"
    When I upload the file for injection
    Then security scanning should be performed
    And the file should be flagged if malicious
    And appropriate warnings should be generated

    When I inject the flagged file into a VM
    Then additional security measures should be applied
    And the VM should be properly isolated
    And enhanced monitoring should be enabled

  @file_injection @ssh @vagrant @methods
  Scenario: File Injection Method Validation
    Given I have a test file "method_test.exe"
    And I have VMs configured for different injection methods

    When I inject the file using Vagrant method
    Then the injection should succeed via Vagrant upload
    And the transfer should be logged with method "vagrant"

    When I inject the file using SSH method
    Then the injection should succeed via SSH/SCP
    And the transfer should be logged with method "ssh"

  @file_injection @performance @benchmarks
  Scenario: File Injection Performance Validation
    Given I have test files of various sizes:
      | filename     | size_mb |
      | small.exe    | 0.1     |
      | medium.exe   | 5.0     |
      | large.exe    | 50.0    |

    When I inject the small file
    Then the injection should complete within 30 seconds

    When I inject the medium file
    Then the injection should complete within 2 minutes

    When I inject the large file
    Then the injection should complete within 10 minutes
    And progress should be reported during transfer

  @file_injection @cleanup @resources
  Scenario: Resource Cleanup After Injection
    Given I have completed a file injection workflow
    When the injection process finishes
    Then temporary files should be cleaned up
    And VM resources should be properly managed
    And only the injected file should remain in the VM

    When the VM is terminated
    Then all VM resources should be freed
    And the VM directory should be cleaned up
    And no orphaned processes should remain

  @file_injection @monitoring @elasticsearch
  Scenario: File Injection Monitoring and Logging
    Given I have a test file for monitoring "monitor_test.exe"
    When I inject the file into a VM
    Then injection events should be sent to Elasticsearch
    And the events should follow ECS format
    And the events should include:
      | field           | description                    |
      | @timestamp      | Injection timestamp           |
      | file.path       | Source file path              |
      | file.size       | File size in bytes            |
      | vm.id           | Target VM identifier          |
      | injection.path  | Target path in VM             |
      | injection.method| Transfer method used          |
      | event.outcome   | Success or failure            |

  @file_injection @integration @end_to_end
  Scenario: End-to-End File Injection Integration
    Given I have a complete malware sample "real_malware.exe"
    When I start the complete injection workflow
    Then the file should be uploaded to MinIO
    And a workflow job should be created
    And a VM should be provisioned
    And the file should be injected successfully
    And the file should be executed in the VM
    And runtime data should be collected
    And analysis results should be available
    And the VM should be cleaned up after analysis

  @file_injection @api_validation @endpoints
  Scenario: File Injection API Endpoint Validation
    Given I have a test file "api_test.exe"
    When I call the file upload API endpoint
    Then the response should be HTTP 201 Created
    And the response should contain file metadata

    When I call the VM creation API endpoint
    Then the response should be HTTP 201 Created
    And the response should contain VM details

    When I call the file injection API endpoint
    Then the response should be HTTP 202 Accepted
    And the response should contain injection job ID

    When I call the injection status API endpoint
    Then the response should be HTTP 200 OK
    And the response should contain current status
