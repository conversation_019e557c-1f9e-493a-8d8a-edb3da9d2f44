Feature: TurdParty Workflow - VLC Binary Analysis
  As a malware analyst
  I want to analyze the VLC media player binary through the complete TurdParty workflow
  So that I can verify the end-to-end functionality

  Background:
    Given the TurdParty system is running
    And all required services are available

  @workflow @vlc_binary
  Scenario: Complete VLC Binary Analysis Workflow
    Given I want to analyze the VLC media player binary
    When I download the VLC binary from the official source
    Then the VLC binary should be downloaded successfully
    And the file size should be approximately 44 MB
    And the file should have a valid executable format

    When I upload the VLC binary to the TurdParty API
    Then the upload should succeed
    And I should receive a valid file UUID
    And the binary should be stored in MinIO
    And a database record should be created

    When I create a workflow for the uploaded VLC binary
    Then the workflow should be created successfully
    And I should receive a valid workflow UUID
    And the workflow status should be "pending" or "running"
    And the workflow should be queued for processing

    When I monitor the workflow progress
    Then the workflow should progress through the following stages:
      | Stage           | Expected Status |
      | VM Creation     | running         |
      | File Injection  | running         |
      | Binary Execution| running         |
      | Event Collection| running         |
      | Analysis        | running         |
      | Cleanup         | running         |
      | Completed       | completed       |

    When the workflow completes
    Then the final status should be "completed"
    And events should be collected in Elasticsearch
    And the events should include both install and runtime phases
    And the VM should be properly cleaned up

  @real_workflow @vlc_binary @event_validation
  Scenario: VLC Binary Event Collection Validation
    Given a completed VLC binary analysis workflow
    When I query the Elasticsearch indices for VLC events
    Then I should find events in the install ECS index
    And I should find events in the runtime ECS index
    And the events should have proper UUID run tracking
    And the events should contain real VLC execution data

    When I examine the install events
    Then they should include file system changes
    And they should include registry modifications
    And they should have valid timestamps
    And they should have the correct file UUID correlation

    When I examine the runtime events
    Then they should include process execution events
    And they should include network activity (if any)
    And they should have valid timestamps
    And they should have the correct file UUID correlation

  @real_workflow @vlc_binary @api_validation
  Scenario: API Endpoint Validation for VLC Workflow
    Given I have the VLC binary ready for analysis
    When I call the file upload API endpoint
    Then the response should be HTTP 201 Created
    And the response should contain a valid file UUID
    And the response should contain correct file metadata

    When I call the workflow creation API endpoint
    Then the response should be HTTP 201 Created
    And the response should contain a valid workflow UUID
    And the response should contain workflow configuration

    When I call the workflow status API endpoint
    Then the response should be HTTP 200 OK
    And the response should contain current workflow status
    And the response should contain progress information

  @real_workflow @vlc_binary @error_handling
  Scenario: Error Handling in VLC Workflow
    Given the TurdParty system is running
    When I attempt to upload an invalid file
    Then the API should return an appropriate error
    And no workflow should be created

    When I attempt to create a workflow with an invalid file UUID
    Then the API should return HTTP 404 Not Found
    And no workflow should be created

    When I attempt to query a non-existent workflow
    Then the API should return HTTP 404 Not Found

  @real_workflow @vlc_binary @performance
  Scenario: VLC Workflow Performance Validation
    Given I start the VLC binary analysis workflow
    When I measure the workflow execution time
    Then the download phase should complete within 5 minutes
    And the upload phase should complete within 2 minutes
    And the workflow creation should complete within 30 seconds
    And the total workflow should complete within 35 minutes

  @real_workflow @vlc_binary @cleanup
  Scenario: Resource Cleanup After VLC Analysis
    Given a completed VLC binary analysis workflow
    When the workflow finishes
    Then the temporary VM should be destroyed
    And the temporary files should be cleaned up
    And the network resources should be released
    And only the analysis results should remain

  @real_workflow @vlc_binary @integration
  Scenario: VLC Workflow Integration with ELK Stack
    Given a running VLC binary analysis workflow
    When events are generated during execution
    Then they should be sent to Elasticsearch in real-time
    And they should be visible in Kibana dashboards
    And they should follow the ECS format specification
    And they should include UUID run tracking fields

  @real_workflow @vlc_binary @security
  Scenario: Security Isolation During VLC Analysis
    Given the VLC binary is being analyzed
    When the binary executes in the VM
    Then the VM should be properly isolated
    And network access should be controlled
    And the host system should remain secure
    And no malicious activity should escape the VM

  @real_workflow @vlc_binary @reporting
  Scenario: VLC Analysis Report Generation
    Given a completed VLC binary analysis
    When I request the analysis report
    Then a comprehensive report should be generated
    And it should include install footprint analysis
    And it should include runtime behavior analysis
    And it should include file system changes
    And it should include registry modifications
    And it should include network activity summary
    And the report should be available in multiple formats
