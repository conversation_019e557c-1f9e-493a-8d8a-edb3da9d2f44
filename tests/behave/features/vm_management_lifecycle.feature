Feature: VM Management Lifecycle
  As a system administrator
  I want to manage VM lifecycles through the TurdParty platform
  So that I can efficiently provision, monitor, and clean up analysis environments

  Background:
    Given the TurdParty system is running
    And all required services are available
    And I have access to the VM management API

  @vm_management @lifecycle @creation
  Scenario: VM Creation and Provisioning
    Given I want to create a Windows 10 VM for analysis
    When I request VM creation with the following specifications:
      | parameter    | value                        |
      | name         | test-analysis-vm             |
      | template     | gusztavvargadr/windows-10    |
      | vm_type      | vagrant                      |
      | memory_mb    | 4096                         |
      | cpus         | 2                            |
      | auto_start   | true                         |
    Then the VM creation request should be accepted
    And I should receive a valid VM ID
    And the VM should be in "creating" status initially

    When I wait for VM provisioning to complete
    Then the VM should be in "running" status within 10 minutes
    And the VM should have an assigned IP address
    And the VM should be accessible via SSH
    And monitoring agents should be installed and running

  @vm_management @pool @allocation
  Scenario: VM Pool Management
    Given I have a VM pool configuration:
      | pool_name    | min_vms | max_vms | template                  |
      | windows10    | 2       | 5       | gusztavvargadr/windows-10 |
      | ubuntu20     | 1       | 3       | ubuntu/focal64            |
    When I initialize the VM pools
    Then the minimum number of VMs should be provisioned for each pool
    And all VMs should be in "ready" status
    And the pools should be available for allocation

    When I request a VM from the windows10 pool
    Then a VM should be allocated within 30 seconds
    And the VM should be moved to "allocated" status
    And a replacement VM should be queued for provisioning

  @vm_management @monitoring @health
  Scenario: VM Resource Monitoring and Health Checks
    Given I have a running VM "test-vm-001"
    When I query the VM health status
    Then I should receive current resource utilization:
      | metric       | expected_range |
      | cpu_percent  | 0-100         |
      | memory_mb    | 0-4096        |
      | disk_gb      | 0-50          |
    And the VM should report "healthy" status
    And monitoring agents should be active

    When the VM experiences high resource usage
    Then alerts should be generated
    And the VM status should reflect the condition
    And appropriate scaling actions should be considered

  @vm_management @lifecycle @termination
  Scenario: VM Cleanup and Termination
    Given I have completed analysis on VM "analysis-vm-123"
    When I request VM termination
    Then the VM should be gracefully shut down
    And all VM processes should be stopped
    And VM resources should be freed
    And the VM directory should be cleaned up
    And no orphaned processes should remain

    When I verify the cleanup
    Then the VM should not appear in active VM lists
    And all associated files should be removed
    And network resources should be released
    And the VM slot should be available for reuse

  @vm_management @scaling @performance
  Scenario: VM Pool Scaling and Performance
    Given I have a VM pool with current capacity:
      | pool_name | current_vms | allocated_vms | available_vms |
      | analysis  | 3           | 2             | 1             |
    When demand increases and all VMs are allocated
    Then additional VMs should be provisioned automatically
    And the pool should scale up to the maximum limit
    And provisioning should complete within acceptable time

    When demand decreases
    Then excess VMs should be terminated gracefully
    And the pool should scale down to the minimum limit
    And resources should be freed appropriately

  @vm_management @failure @recovery
  Scenario: VM Failure Recovery
    Given I have a running VM that encounters a failure
    When the VM becomes unresponsive
    Then the failure should be detected within 2 minutes
    And the VM should be marked as "failed"
    And automatic recovery should be attempted

    When automatic recovery fails
    Then the VM should be terminated
    And a replacement VM should be provisioned
    And any ongoing analysis should be rescheduled
    And appropriate notifications should be sent

  @vm_management @security @isolation
  Scenario: VM Security and Isolation
    Given I have multiple VMs running simultaneously
    When I verify VM isolation
    Then each VM should be in its own network namespace
    And VMs should not be able to communicate with each other
    And VMs should have limited host system access
    And security policies should be enforced

    When I check VM security configurations
    Then firewalls should be properly configured
    And unnecessary services should be disabled
    And security monitoring should be active
    And compliance requirements should be met

  @vm_management @templates @customization
  Scenario: VM Template Management and Customization
    Given I have base VM templates available
    When I create a customized template with:
      | component           | configuration                    |
      | monitoring_agents   | fibratus, sysmon, custom_logger |
      | analysis_tools      | procmon, wireshark, volatility  |
      | security_settings   | restricted_network, logging     |
    Then the template should be built successfully
    And the template should be available for VM creation
    And VMs created from the template should include customizations

  @vm_management @networking @connectivity
  Scenario: VM Networking and Connectivity
    Given I have a VM with network requirements
    When I configure VM networking
    Then the VM should have proper network connectivity
    And DNS resolution should work correctly
    And required ports should be accessible
    And network isolation should be maintained

    When I test network connectivity
    Then the VM should be able to reach required services
    And network traffic should be properly monitored
    And security policies should be enforced
    And bandwidth limits should be respected

  @vm_management @backup @snapshots
  Scenario: VM Backup and Snapshot Management
    Given I have a VM in a specific state
    When I create a VM snapshot
    Then the snapshot should be created successfully
    And the snapshot should capture the complete VM state
    And the snapshot should be stored securely
    And metadata should be recorded

    When I restore from a snapshot
    Then the VM should be restored to the exact previous state
    And all data should be intact
    And the restoration should complete within acceptable time
    And the VM should be fully functional

  @vm_management @api @integration
  Scenario: VM Management API Integration
    Given I have access to the VM management API
    When I call the VM creation endpoint
    Then the response should be HTTP 201 Created
    And the response should contain VM details

    When I call the VM status endpoint
    Then the response should be HTTP 200 OK
    And the response should contain current VM status

    When I call the VM action endpoint with "restart"
    Then the response should be HTTP 202 Accepted
    And the VM should restart successfully

    When I call the VM termination endpoint
    Then the response should be HTTP 202 Accepted
    And the VM should be terminated properly

  @vm_management @performance @benchmarks
  Scenario: VM Performance Benchmarks
    Given I want to measure VM performance
    When I create VMs with different configurations:
      | config_name | memory_mb | cpus | expected_boot_time |
      | minimal     | 2048      | 1    | 3 minutes         |
      | standard    | 4096      | 2    | 4 minutes         |
      | enhanced    | 8192      | 4    | 5 minutes         |
    Then each VM should boot within the expected time
    And performance metrics should be within acceptable ranges
    And resource utilization should be optimized

  @vm_management @concurrent @stress
  Scenario: Concurrent VM Management Stress Test
    Given I need to test concurrent VM operations
    When I create 10 VMs simultaneously
    Then all VMs should be created successfully
    And creation times should be within acceptable limits
    And system resources should not be exhausted
    And no VM creation should fail due to resource conflicts

    When I terminate all VMs simultaneously
    Then all VMs should be terminated successfully
    And cleanup should complete within acceptable time
    And no resources should be leaked
    And the system should return to baseline state
