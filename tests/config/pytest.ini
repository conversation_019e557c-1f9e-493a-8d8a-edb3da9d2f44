[tool:pytest]
# Pytest configuration for TurdParty
minversion = 6.0
addopts = 
    -ra
    --strict-markers
    --strict-config
    --cov=api
    --cov-report=term-missing
    --cov-report=html:test-results/htmlcov
    --cov-report=xml:test-results/coverage.xml
    --junit-xml=test-results/pytest-results.xml
    --html=test-results/pytest-report.html
    --self-contained-html
    -v
testpaths = 
    tests/unit
    tests/integration
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    api: API tests
    database: Database tests
    elk: ELK stack tests
    vm: Virtual machine tests
    file_injection: File injection tests
    security: Security tests
    performance: Performance tests
    smoke: Smoke tests
    regression: Regression tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*
    ignore::UserWarning:urllib3.*
    ignore::UserWarning:aiohttp.*
    ignore::ResourceWarning
    ignore::pytest.PytestUnraisableExceptionWarning
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S
log_file = test-results/pytest.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s (%(filename)s:%(lineno)d)
log_file_date_format = %Y-%m-%d %H:%M:%S
asyncio_mode = auto
timeout = 300
timeout_method = thread
# Distributed testing
dist = worksteal
# Parallel execution
addopts =
    -ra
    --strict-markers
    --strict-config
    --tb=short
    --maxfail=10
    --durations=10
    --durations-min=1.0
# Test discovery
collect_ignore = [
    "setup.py",
    "conftest.py",
    "node_modules",
    ".git",
    ".tox",
    "dist",
    "build",
    "*.egg"
]
# Custom test markers for better organization
markers =
    unit: Unit tests - fast, isolated tests
    integration: Integration tests - test component interactions
    e2e: End-to-end tests - full workflow tests
    slow: Slow running tests (> 5 seconds)
    fast: Fast running tests (< 1 second)
    api: API endpoint tests
    database: Database interaction tests
    elk: ELK stack integration tests
    minio: MinIO storage tests
    vm: Virtual machine tests
    file_injection: File injection workflow tests
    worker: Worker service tests
    security: Security-related tests
    performance: Performance and load tests
    smoke: Smoke tests for basic functionality
    regression: Regression tests
    critical: Critical path tests
    flaky: Tests that may be flaky
    docker: Tests that require Docker
    network: Tests that require network access
    external: Tests that depend on external services
