"""
Pytest configuration and shared fixtures for TurdParty tests.

This module provides common test fixtures and configuration for all test types.
Ensures PEP8, PEP257, and PEP484 compliance.
"""

from collections.abc import Generator
from datetime import UTC, datetime
import os
from pathlib import Path
import tempfile
from typing import Any
from unittest.mock import AsyncMock, MagicMock, patch
import uuid

import pytest
import asyncio
import time
import logging
import statistics
import threading
from typing import AsyncGenerator

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Try to import optional dependencies
try:
    from fastapi.testclient import TestClient
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    TestClient = None

try:
    from minio import Minio
    MINIO_AVAILABLE = True
except ImportError:
    MINIO_AVAILABLE = False
    Minio = None

try:
    from elasticsearch import AsyncElasticsearch
    ELASTICSEARCH_AVAILABLE = True
except ImportError:
    ELASTICSEARCH_AVAILABLE = False
    AsyncElasticsearch = None

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None

try:
    from api.v1.application import get_application
    APP_AVAILABLE = True
except ImportError:
    try:
        # Fallback to main module
        from api.main import app as get_application
        APP_AVAILABLE = True
    except ImportError:
        APP_AVAILABLE = False
        get_application = None

try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False
    httpx = None

try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False
    websockets = None

try:
    import docker
    DOCKER_AVAILABLE = True
except ImportError:
    DOCKER_AVAILABLE = False
    docker = None

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None

try:
    from testcontainers.minio import MinioContainer
    from testcontainers.elasticsearch import ElasticsearchContainer
    from testcontainers.redis import RedisContainer
    TESTCONTAINERS_AVAILABLE = True
except ImportError:
    TESTCONTAINERS_AVAILABLE = False
    MinioContainer = None
    ElasticsearchContainer = None
    RedisContainer = None


def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "websocket: WebSocket tests")
    config.addinivalue_line("markers", "performance: Performance tests")
    config.addinivalue_line("markers", "benchmark: Benchmark tests")
    config.addinivalue_line("markers", "scalability: Scalability tests")
    config.addinivalue_line("markers", "resource: Resource utilization tests")
    config.addinivalue_line("markers", "slow: Slow running tests")
    config.addinivalue_line("markers", "docker: Tests requiring Docker")
    config.addinivalue_line("markers", "vagrant: Tests requiring Vagrant")


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically"""
    for item in items:
        # Add markers based on test file location
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)

        # Add markers based on test name patterns
        if "websocket" in item.name.lower():
            item.add_marker(pytest.mark.websocket)
        if "benchmark" in item.name.lower():
            item.add_marker(pytest.mark.benchmark)
        if "docker" in item.name.lower():
            item.add_marker(pytest.mark.docker)
        if "vagrant" in item.name.lower():
            item.add_marker(pytest.mark.vagrant)


def pytest_addoption(parser):
    """Add custom command line options"""
    parser.addoption(
        "--runslow", action="store_true", default=False,
        help="run slow tests"
    )
    parser.addoption(
        "--runperformance", action="store_true", default=False,
        help="run performance tests"
    )
    parser.addoption(
        "--runintegration", action="store_true", default=False,
        help="run integration tests"
    )


def pytest_runtest_setup(item):
    """Setup for each test"""
    # Skip slow tests unless explicitly requested
    if "slow" in item.keywords and not item.config.getoption("--runslow", default=False):
        pytest.skip("need --runslow option to run")


# Note: Removed deprecated event_loop fixture - pytest-asyncio handles this automatically


@pytest.fixture
def app():
    """Create a FastAPI application instance for testing."""
    if not APP_AVAILABLE:
        pytest.skip("FastAPI application not available")
    return get_application()


@pytest.fixture
def client(app):
    """Create a test client for the FastAPI application."""
    if not FASTAPI_AVAILABLE:
        pytest.skip("FastAPI TestClient not available")
    return TestClient(app)


@pytest.fixture
def client_with_mocks(app, mock_minio_client, mock_elasticsearch_client, mock_redis_client):
    """Create a test client with all dependencies mocked."""
    if not FASTAPI_AVAILABLE:
        pytest.skip("FastAPI TestClient not available")

    # Override dependencies with mocks
    from api.dependencies import get_minio_client, get_elasticsearch, get_redis

    app.dependency_overrides[get_minio_client] = lambda: mock_minio_client
    app.dependency_overrides[get_elasticsearch] = lambda: mock_elasticsearch_client
    app.dependency_overrides[get_redis] = lambda: mock_redis_client

    client = TestClient(app)

    yield client

    # Clean up dependency overrides
    app.dependency_overrides.clear()


@pytest.fixture
async def async_client_with_mocks(app, mock_minio_client, mock_elasticsearch_client, mock_redis_client):
    """Create an async client with all dependencies mocked."""
    if not HTTPX_AVAILABLE:
        pytest.skip("httpx not available")

    # Override dependencies with mocks
    from api.dependencies import get_minio_client, get_elasticsearch, get_redis

    app.dependency_overrides[get_minio_client] = lambda: mock_minio_client
    app.dependency_overrides[get_elasticsearch] = lambda: mock_elasticsearch_client
    app.dependency_overrides[get_redis] = lambda: mock_redis_client

    async with httpx.AsyncClient(app=app, base_url="http://test") as client:
        yield client

    # Clean up dependency overrides
    app.dependency_overrides.clear()


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def sample_file_content() -> bytes:
    """Provide sample file content for testing."""
    return b"#!/bin/bash\necho 'Hello from test file'\n"


@pytest.fixture
def sample_script_file(temp_dir: Path, sample_file_content: bytes) -> Path:
    """Create a sample script file for testing."""
    script_file = temp_dir / "test_script.sh"
    script_file.write_bytes(sample_file_content)
    script_file.chmod(0o755)
    return script_file


@pytest.fixture
def mock_elk_logger() -> MagicMock:
    """Create a mock ELK logger for testing."""
    mock_logger = MagicMock()
    mock_logger.log_file_injection_event = AsyncMock()
    mock_logger.log_installation_base = AsyncMock()
    mock_logger.log_system_event = AsyncMock()
    return mock_logger


@pytest.fixture
def mock_file_injection_service() -> MagicMock:
    """Create a mock file injection service for testing."""
    mock_service = MagicMock()
    mock_service.create_injection = AsyncMock()
    mock_service.get_by_id = AsyncMock()
    mock_service.get_all = AsyncMock()
    mock_service.get_status = AsyncMock()
    mock_service.process_injection = AsyncMock()
    mock_service.delete = AsyncMock()
    return mock_service


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch, temp_dir: Path) -> None:
    """Set up test environment variables."""
    monkeypatch.setenv("TEST_MODE", "true")
    monkeypatch.setenv("DEBUG", "true")
    monkeypatch.setenv("FILE_UPLOAD_DIR", str(temp_dir))
    monkeypatch.setenv("ELASTICSEARCH_HOST", "localhost")
    monkeypatch.setenv("ELASTICSEARCH_PORT", "9200")
    monkeypatch.setenv("LOGSTASH_HOST", "localhost")
    monkeypatch.setenv("LOGSTASH_PORT", "5000")


@pytest.fixture
def injection_data() -> dict:
    """Provide sample injection data for testing."""
    return {
        "filename": "test_script.sh",
        "target_path": "/app/scripts/test_script.sh",
        "permissions": "0755",
        "description": "Test script for unit testing",
    }


@pytest.fixture
def injection_response_data() -> dict:
    """Provide sample injection response data for testing."""
    return {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "filename": "test_script.sh",
        "target_path": "/app/scripts/test_script.sh",
        "permissions": "0755",
        "status": "pending",
        "description": "Test script for unit testing",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "file_size": 1024,
        "file_hash": "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3",
    }


@pytest.fixture
def injection_status_data() -> dict:
    """Provide sample injection status data for testing."""
    return {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "status": "completed",
        "progress": 100,
        "message": "File injection completed successfully",
        "details": {
            "file_path": "/tmp/test_script.sh",
            "target_path": "/app/scripts/test_script.sh",
            "permissions": "0755",
        },
        "updated_at": "2024-01-15T10:35:00Z",
    }


class AsyncContextManager:
    """Helper class for testing async context managers."""

    def __init__(self, return_value=None):
        """Initialize with optional return value."""
        self.return_value = return_value

    async def __aenter__(self):
        """Async enter method."""
        return self.return_value

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async exit method."""
        # Suppress unused parameter warnings
        _ = exc_type, exc_val, exc_tb


@pytest.fixture
def async_context_manager():
    """Provide an async context manager for testing."""
    return AsyncContextManager


@pytest.fixture
def mock_minio_client() -> MagicMock:
    """Create a mock MinIO client for testing."""
    mock_client = MagicMock(spec=Minio) if MINIO_AVAILABLE else MagicMock()
    mock_client.bucket_exists.return_value = True
    mock_client.put_object.return_value = MagicMock()
    mock_client.get_object.return_value = MagicMock()
    mock_client.remove_object.return_value = None
    mock_client.list_objects.return_value = []
    return mock_client


@pytest.fixture
def mock_elasticsearch_client() -> AsyncMock:
    """Create a mock Elasticsearch client for testing."""
    if ELASTICSEARCH_AVAILABLE:
        mock_client = AsyncMock(spec=AsyncElasticsearch)
    else:
        mock_client = AsyncMock()
    mock_client.index.return_value = {"_id": "test-doc-id", "result": "created"}
    mock_client.search.return_value = {
        "hits": {"total": {"value": 0}, "hits": []}
    }
    mock_client.indices.exists.return_value = True
    return mock_client


@pytest.fixture
def mock_redis_client() -> MagicMock:
    """Create a mock Redis client for testing."""
    mock_client = MagicMock(spec=redis.Redis) if REDIS_AVAILABLE else MagicMock()
    mock_client.ping.return_value = True
    mock_client.set.return_value = True
    mock_client.get.return_value = None
    mock_client.delete.return_value = 1
    return mock_client


@pytest.fixture
def mock_celery_app() -> MagicMock:
    """Create a mock Celery application for testing."""
    mock_app = MagicMock()
    mock_task = MagicMock()
    mock_task.delay.return_value = MagicMock(id="test-task-id")
    mock_app.send_task.return_value = mock_task.delay.return_value
    return mock_app


@pytest.fixture
def sample_file_data() -> dict[str, Any]:
    """Provide sample file data for testing."""
    return {
        "filename": "test_script.sh",
        "content": b"#!/bin/bash\necho 'Hello World'\n",
        "size": 28,
        "content_type": "application/x-sh",
        "hash": "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"
    }


@pytest.fixture
def sample_vm_data() -> dict[str, Any]:
    """Provide sample VM data for testing."""
    return {
        "vm_id": "test-vm-123",
        "name": "test-vm",
        "status": "running",
        "ip_address": "*************",
        "memory": 1024,
        "cpus": 2,
        "created_at": datetime.now(UTC).isoformat()
    }


@pytest.fixture
def sample_workflow_data() -> dict[str, Any]:
    """Provide sample workflow data for testing."""
    return {
        "workflow_id": str(uuid.uuid4()),
        "file_id": str(uuid.uuid4()),
        "vm_id": "test-vm-123",
        "status": "pending",
        "steps": ["upload", "inject", "execute", "monitor"],
        "current_step": "upload"
    }


def load_test_config() -> dict[str, Any]:
    """Load test configuration from environment and .env files."""
    config = {}

    # Load from integration test .env file if it exists
    integration_env = Path(__file__).parent / 'integration' / '.env.test'
    if integration_env.exists():
        try:
            from dotenv import load_dotenv
            load_dotenv(integration_env)
        except ImportError:
            # Parse .env file manually if python-dotenv not available
            with integration_env.open() as f:
                for raw_line in f:
                    line = raw_line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()

    # Load from main .env file if it exists
    env_file = Path(__file__).parent / '.env'
    if env_file.exists():
        try:
            from dotenv import load_dotenv
            load_dotenv(env_file)
        except ImportError:
            pass

    # Load configuration from environment
    config.update({
        'TEST_MODE': os.getenv('TEST_MODE', 'true').lower() == 'true',
        'DEBUG': os.getenv('DEBUG', 'false').lower() == 'true',
        'LOG_LEVEL': os.getenv('LOG_LEVEL', 'INFO'),
        'DATABASE_URL': os.getenv('DATABASE_URL', 'sqlite:///test.db'),
        'REDIS_URL': os.getenv('REDIS_URL', 'redis://localhost:6379/1'),
        'API_BASE_URL': os.getenv('API_BASE_URL', 'http://localhost:8000'),
        'API_VERSION': os.getenv('API_VERSION', 'v1'),
        'API_TIMEOUT': int(os.getenv('API_TIMEOUT', '300')),

        # MinIO Configuration
        'MINIO_ENDPOINT': os.getenv('MINIO_ENDPOINT', 'localhost:9000'),
        'MINIO_ACCESS_KEY': os.getenv('MINIO_ACCESS_KEY', 'minioadmin'),
        'MINIO_SECRET_KEY': os.getenv('MINIO_SECRET_KEY', 'minioadmin'),
        'MINIO_SECURE': os.getenv('MINIO_SECURE', 'false').lower() == 'true',
        'MINIO_TEST_BUCKET_PREFIX': os.getenv('MINIO_TEST_BUCKET_PREFIX', 'turdparty-test'),
        'MINIO_REGION': os.getenv('MINIO_REGION', 'us-east-1'),

        # ELK Configuration
        'ELASTICSEARCH_URL': os.getenv('ELASTICSEARCH_URL', 'http://localhost:9200'),
        'ELASTICSEARCH_INDEX_PREFIX': os.getenv('ELASTICSEARCH_INDEX_PREFIX', 'turdparty-test'),
        'ELASTICSEARCH_TIMEOUT': int(os.getenv('ELASTICSEARCH_TIMEOUT', '30')),
        'KIBANA_URL': os.getenv('KIBANA_URL', 'http://localhost:5601'),
        'LOGSTASH_HOST': os.getenv('LOGSTASH_HOST', 'localhost'),
        'LOGSTASH_PORT': int(os.getenv('LOGSTASH_PORT', '5044')),

        # VM Configuration
        'VAGRANT_GRPC_PORT': int(os.getenv('VAGRANT_GRPC_PORT', '40000')),
        'VM_CAPTURE_TIMEOUT_MINUTES': int(os.getenv('VM_CAPTURE_TIMEOUT_MINUTES', '5')),
        'VM_BOOT_TIMEOUT_MINUTES': int(os.getenv('VM_BOOT_TIMEOUT_MINUTES', '10')),
        'VM_TEARDOWN_TIMEOUT_MINUTES': int(os.getenv('VM_TEARDOWN_TIMEOUT_MINUTES', '5')),
        'VM_NETWORK_DISABLED': os.getenv('VM_NETWORK_DISABLED', 'true').lower() == 'true',
        'VM_MOUNTS_DISABLED': os.getenv('VM_MOUNTS_DISABLED', 'true').lower() == 'true',
        'USE_DOCKER_FALLBACK': os.getenv('USE_DOCKER_FALLBACK', 'true').lower() == 'true',

        # VirtualBox Configuration
        'VBOX_HEADLESS': os.getenv('VBOX_HEADLESS', 'true').lower() == 'true',
        'VBOX_MEMORY_MB': int(os.getenv('VBOX_MEMORY_MB', '2048')),
        'VBOX_CPUS': int(os.getenv('VBOX_CPUS', '2')),
        'VBOX_DISK_SIZE_GB': int(os.getenv('VBOX_DISK_SIZE_GB', '20')),

        # Test Data Configuration
        'TEST_DOWNLOAD_DIR': os.getenv('TEST_DOWNLOAD_DIR', '/tmp/turdparty-test-downloads'),
        'TEST_APPS_JSON': os.getenv('TEST_APPS_JSON', 'tests/data/test_applications.json'),
        'TEST_BENCHMARK_FILE': os.getenv('TEST_BENCHMARK_FILE', 'tests/data/benchmarks.json'),
        'TEST_CLEANUP_ENABLED': os.getenv('TEST_CLEANUP_ENABLED', 'true').lower() == 'true',

        # File Processing Configuration
        'BLAKE3_HASH_ENABLED': os.getenv('BLAKE3_HASH_ENABLED', 'true').lower() == 'true',
        'FILE_CHUNK_SIZE': int(os.getenv('FILE_CHUNK_SIZE', '8192')),
        'MAX_FILE_SIZE_MB': int(os.getenv('MAX_FILE_SIZE_MB', '2048')),
        'DOWNLOAD_TIMEOUT_SECONDS': int(os.getenv('DOWNLOAD_TIMEOUT_SECONDS', '300')),
        'UPLOAD_TIMEOUT_SECONDS': int(os.getenv('UPLOAD_TIMEOUT_SECONDS', '300')),

        # VM Test Configuration
        'UBUNTU_VM_COUNT': int(os.getenv('UBUNTU_VM_COUNT', '2')),
        'WINDOWS_VM_COUNT': int(os.getenv('WINDOWS_VM_COUNT', '2')),
        'UBUNTU_VM_IMAGE': os.getenv('UBUNTU_VM_IMAGE', 'ubuntu/focal64'),
        'WINDOWS_VM_IMAGE': os.getenv('WINDOWS_VM_IMAGE', 'gusztavvargadr/windows-10'),

        # Cleanup Configuration
        'AUTO_CLEANUP_DOWNLOADS': os.getenv('AUTO_CLEANUP_DOWNLOADS', 'true').lower() == 'true',
        'AUTO_CLEANUP_VMS': os.getenv('AUTO_CLEANUP_VMS', 'true').lower() == 'true',
        'AUTO_CLEANUP_MINIO': os.getenv('AUTO_CLEANUP_MINIO', 'true').lower() == 'true',
        'KEEP_FAILED_TEST_DATA': os.getenv('KEEP_FAILED_TEST_DATA', 'true').lower() == 'true',

        # Security Configuration
        'DISABLE_VM_NETWORKING': os.getenv('DISABLE_VM_NETWORKING', 'true').lower() == 'true',
        'DISABLE_VM_SHARED_FOLDERS': os.getenv('DISABLE_VM_SHARED_FOLDERS', 'true').lower() == 'true',
        'ENABLE_VM_ISOLATION': os.getenv('ENABLE_VM_ISOLATION', 'true').lower() == 'true',
        'SANDBOX_MODE': os.getenv('SANDBOX_MODE', 'true').lower() == 'true',
    })

    return config


# Enhanced VM Testing Fixtures

@pytest.fixture(scope="session")
def api_server_url():
    """API server URL for testing"""
    return os.getenv("TEST_API_URL", "http://localhost:8000")


@pytest.fixture(scope="session")
def websocket_server_url():
    """WebSocket server URL for testing"""
    return os.getenv("TEST_WS_URL", "ws://localhost:8000")


@pytest.fixture
async def api_client(api_server_url) -> AsyncGenerator:
    """Async HTTP client for API testing"""
    if not HTTPX_AVAILABLE:
        pytest.skip("httpx not available")

    async with httpx.AsyncClient(base_url=api_server_url, timeout=30.0) as client:
        yield client


@pytest.fixture
async def test_vm_data():
    """Standard test VM data"""
    return {
        "name": f"test-vm-{int(time.time() * 1000)}",
        "template": "ubuntu:20.04",
        "vm_type": "docker",
        "memory_mb": 512,
        "cpus": 1,
        "domain": "TurdParty",
        "description": "Test VM for automated testing"
    }


@pytest.fixture
async def created_test_vm(api_client, test_vm_data):
    """Create a test VM and clean it up after test"""
    if not HTTPX_AVAILABLE:
        pytest.skip("httpx not available")

    # Create VM
    response = await api_client.post("/api/v1/vms/", json=test_vm_data)
    if response.status_code != 201:
        pytest.skip(f"Failed to create test VM: {response.status_code}")

    vm_info = response.json()
    vm_id = vm_info["vm_id"]

    yield vm_info

    # Cleanup
    try:
        await api_client.delete(f"/api/v1/vms/{vm_id}?force=true")
    except Exception as e:
        logger.warning(f"Failed to cleanup test VM {vm_id}: {e}")


@pytest.fixture
def mock_docker_client():
    """Mock Docker client for testing"""
    if not DOCKER_AVAILABLE:
        pytest.skip("docker not available")

    mock_client = MagicMock()

    # Mock container
    mock_container = MagicMock()
    mock_container.id = "test_container_123"
    mock_container.status = "running"
    mock_container.attrs = {
        'State': {
            'StartedAt': '2023-11-04T10:30:00.000000000Z'
        },
        'NetworkSettings': {
            'Networks': {
                'bridge': {
                    'IPAddress': '**********'
                }
            }
        }
    }

    # Mock stats
    mock_stats = {
        'cpu_stats': {
            'cpu_usage': {
                'total_usage': 1000000000,
                'percpu_usage': [500000000, 500000000]
            },
            'system_cpu_usage': 2000000000
        },
        'precpu_stats': {
            'cpu_usage': {
                'total_usage': 500000000
            },
            'system_cpu_usage': 1000000000
        },
        'memory_stats': {
            'usage': 1073741824,  # 1GB
            'limit': 2147483648   # 2GB
        },
        'networks': {
            'eth0': {
                'rx_bytes': 1048576,
                'tx_bytes': 524288,
                'rx_packets': 1000,
                'tx_packets': 800
            }
        }
    }

    mock_container.stats.return_value = mock_stats
    mock_container.exec_run.return_value = MagicMock(
        exit_code=0,
        output=(
            b"root      1234  15.2  25.6  123456  789012 ?        R    10:30   0:01 python app.py\n",
            None
        )
    )
    mock_container.put_archive.return_value = True

    mock_client.containers.get.return_value = mock_container
    mock_client.containers.list.return_value = [mock_container]
    mock_client.containers.run.return_value = mock_container

    return mock_client


# Real VM Management Test Fixtures (replacing mocks)

@pytest.fixture(scope="session")
def docker_client():
    """Real Docker client for testing"""
    try:
        import docker
        client = docker.from_env()
        # Test Docker connection
        client.ping()
        return client
    except Exception as e:
        pytest.skip(f"Docker not available: {e}")

@pytest.fixture
def real_vm_service(docker_client):
    """Real VM service with Docker client for testing"""
    from api.services.vm_service import VMService

    service = VMService()
    service.docker_client = docker_client

    yield service

    # Cleanup: Remove any test containers created during testing
    try:
        containers = docker_client.containers.list(
            all=True,
            filters={"label": "turdparty.test=true"}
        )
        for container in containers:
            try:
                if container.status == "running":
                    container.kill()
                container.remove(force=True)
            except Exception as e:
                logger.warning(f"Failed to cleanup container {container.id}: {e}")
    except Exception as e:
        logger.warning(f"Failed to cleanup test containers: {e}")

@pytest.fixture
def real_vm_metrics_service(docker_client):
    """Real VM metrics service for testing"""
    from api.services.vm_metrics_service import VMMetricsService

    service = VMMetricsService()
    service.docker_client = docker_client

    yield service


# Real Storage Layer Test Fixtures (replacing mocks)

@pytest.fixture(scope="session")
def minio_container():
    """Real MinIO container for testing"""
    try:
        from testcontainers.minio import MinioContainer

        with MinioContainer() as minio:
            yield minio
    except Exception as e:
        pytest.skip(f"MinIO container not available: {e}")

@pytest.fixture
def real_minio_client(minio_container):
    """Real MinIO client for testing"""
    try:
        from minio import Minio

        client = Minio(
            f"{minio_container.get_container_host_ip()}:{minio_container.get_exposed_port(9000)}",
            access_key=minio_container.access_key,
            secret_key=minio_container.secret_key,
            secure=False
        )

        # Create test bucket
        test_bucket = "turdparty-test"
        if not client.bucket_exists(test_bucket):
            client.make_bucket(test_bucket)

        yield client, test_bucket

        # Cleanup: Remove all test objects
        try:
            objects = client.list_objects(test_bucket, recursive=True)
            for obj in objects:
                client.remove_object(test_bucket, obj.object_name)
        except Exception as e:
            logger.warning(f"Failed to cleanup MinIO objects: {e}")

    except Exception as e:
        pytest.skip(f"MinIO client not available: {e}")

@pytest.fixture
def real_storage_service(real_minio_client):
    """Real storage service for testing"""
    try:
        # Import the actual storage service implementation
        # Note: We'll need to create this if it doesn't exist
        from api.services.storage_service import StorageService

        client, bucket = real_minio_client
        service = StorageService()
        service.minio_client = client
        service.default_bucket = bucket

        yield service

    except ImportError:
        # If storage service doesn't exist, create a simple one for testing
        class SimpleStorageService:
            def __init__(self, minio_client, bucket):
                self.minio_client = minio_client
                self.default_bucket = bucket

            async def upload_file(self, file_id, filename, file_content, content_type="application/octet-stream", bucket_name=None):
                import io
                import hashlib

                bucket = bucket_name or self.default_bucket
                object_key = f"{file_id}_{filename}"

                # Calculate hash
                file_hash = hashlib.sha256(file_content).hexdigest()

                # Upload to MinIO
                self.minio_client.put_object(
                    bucket,
                    object_key,
                    io.BytesIO(file_content),
                    len(file_content),
                    content_type=content_type,
                    metadata={
                        "original-filename": filename,
                        "file-hash": file_hash
                    }
                )

                return {
                    "success": True,
                    "file_id": file_id,
                    "object_key": object_key,
                    "bucket": bucket,
                    "file_size": len(file_content),
                    "file_hash": file_hash
                }

            async def download_file(self, bucket_name, object_key):
                try:
                    response = self.minio_client.get_object(bucket_name, object_key)
                    content = response.read()
                    response.close()
                    response.release_conn()

                    return {
                        "success": True,
                        "content": content,
                        "file_size": len(content)
                    }
                except Exception as e:
                    return {
                        "success": False,
                        "error": str(e)
                    }

            def file_exists(self, bucket_name, object_key):
                try:
                    self.minio_client.stat_object(bucket_name, object_key)
                    return True
                except:
                    return False

            async def delete_file(self, bucket_name, object_key):
                try:
                    self.minio_client.remove_object(bucket_name, object_key)
                    return {"success": True}
                except Exception as e:
                    return {"success": False, "error": str(e)}

        client, bucket = real_minio_client
        service = SimpleStorageService(client, bucket)
        yield service


# Real API Layer Test Fixtures (replacing mocks)

@pytest.fixture(scope="session")
def test_service_urls():
    """Real service URL configuration for testing"""
    try:
        from utils.service_urls import ServiceURLManager

        # Use test environment configuration
        manager = ServiceURLManager(environment="local")

        # Override with test-specific URLs for containerized services
        test_urls = {
            "api": "http://localhost:8001",  # Test API service
            "minio": "http://localhost:9001",  # Test MinIO service
            "elasticsearch": "http://localhost:9201",  # Test Elasticsearch
            "redis": "http://localhost:6380",  # Test Redis
            "postgres": "http://localhost:5433",  # Test PostgreSQL
        }

        yield manager, test_urls

    except ImportError:
        # Fallback if service URL manager not available
        test_urls = {
            "api": "http://localhost:8001",
            "minio": "http://localhost:9001",
            "elasticsearch": "http://localhost:9201",
            "redis": "http://localhost:6380",
            "postgres": "http://localhost:5433",
        }
        yield None, test_urls

@pytest.fixture
def real_api_client(test_service_urls):
    """Real API client for testing with actual service URLs"""
    try:
        import httpx

        url_manager, test_urls = test_service_urls
        api_base_url = test_urls["api"]

        # Create real HTTP client
        client = httpx.AsyncClient(
            base_url=api_base_url,
            timeout=30.0,
            follow_redirects=True
        )

        yield client, api_base_url, test_urls

    except ImportError:
        pytest.skip("httpx not available for real API testing")

@pytest.fixture
def real_service_integration(real_api_client, real_minio_client, real_vm_service):
    """Real service integration fixture combining all real services"""
    api_client, api_base_url, test_urls = real_api_client
    minio_client, minio_bucket = real_minio_client

    integration = {
        "api_client": api_client,
        "api_base_url": api_base_url,
        "service_urls": test_urls,
        "minio_client": minio_client,
        "minio_bucket": minio_bucket,
        "vm_service": real_vm_service,
    }

    yield integration


# Real Worker Services Test Fixtures (replacing mocks)

@pytest.fixture(scope="session")
def redis_container():
    """Real Redis container for Celery broker"""
    try:
        from testcontainers.redis import RedisContainer

        with RedisContainer("redis:7-alpine") as redis:
            yield redis
    except Exception as e:
        pytest.skip(f"Redis container not available: {e}")

@pytest.fixture
def real_celery_app(redis_container):
    """Real Celery app for testing with Redis broker"""
    try:
        from celery import Celery

        # Configure Celery with real Redis broker
        broker_url = f"redis://{redis_container.get_container_host_ip()}:{redis_container.get_exposed_port(6379)}/0"

        app = Celery('test_workers')
        app.conf.update(
            broker_url=broker_url,
            result_backend=broker_url,
            task_always_eager=False,  # Use real async execution
            task_eager_propagates=True,
            task_serializer='json',
            accept_content=['json'],
            result_serializer='json',
            timezone='UTC',
            enable_utc=True,
            task_routes={
                'test.*': {'queue': 'test_queue'},
                'file_ops.*': {'queue': 'file_ops'},
                'vm_ops.*': {'queue': 'vm_ops'},
            }
        )

        yield app

    except ImportError:
        pytest.skip("Celery not available for real worker testing")

@pytest.fixture
def real_worker_services(real_celery_app, real_minio_client, real_vm_service):
    """Real worker services integration fixture"""
    celery_app = real_celery_app
    minio_client, minio_bucket = real_minio_client
    vm_service = real_vm_service

    # Create simple worker service implementations
    class RealFileOperationsWorker:
        def __init__(self, celery_app, minio_client, bucket):
            self.celery_app = celery_app
            self.minio_client = minio_client
            self.bucket = bucket

        def download_file_from_minio(self, object_key):
            """Download file from MinIO"""
            try:
                response = self.minio_client.get_object(self.bucket, object_key)
                content = response.read()
                response.close()
                response.release_conn()

                return {
                    "success": True,
                    "content": content,
                    "size": len(content)
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e)
                }

        def validate_file(self, file_content, filename):
            """Validate file content"""
            if not file_content:
                return {"success": False, "error": "Empty file"}

            if len(file_content) > 100 * 1024 * 1024:  # 100MB limit
                return {"success": False, "error": "File too large"}

            return {
                "success": True,
                "file_size": len(file_content),
                "filename": filename
            }

    class RealVMOperationsWorker:
        def __init__(self, celery_app, vm_service):
            self.celery_app = celery_app
            self.vm_service = vm_service

        async def create_vm_for_processing(self, template="ubuntu:20.04"):
            """Create VM for file processing"""
            from api.models.vm_management import VMCreateRequest, VMType
            import time

            request = VMCreateRequest(
                name=f"worker_vm_{int(time.time())}",
                template=template,
                vm_type=VMType.DOCKER,
                memory_mb=256,
                cpus=1,
                domain="TurdParty"
            )

            vm = await self.vm_service.create_vm(request)
            return {
                "success": True,
                "vm_id": vm.vm_id,
                "vm_name": vm.name,
                "status": vm.status
            }

        async def destroy_vm(self, vm_id):
            """Destroy VM after processing"""
            try:
                await self.vm_service.delete_vm(vm_id, force=True)
                return {"success": True}
            except Exception as e:
                return {"success": False, "error": str(e)}

    class RealWorkflowOrchestrator:
        def __init__(self, file_worker, vm_worker):
            self.file_worker = file_worker
            self.vm_worker = vm_worker

        async def execute_file_processing_workflow(self, file_object_key, workflow_id):
            """Execute complete file processing workflow"""
            results = []

            # Step 1: Download file
            download_result = self.file_worker.download_file_from_minio(file_object_key)
            results.append(("download", download_result))

            if not download_result["success"]:
                return {"success": False, "error": "Download failed", "results": results}

            # Step 2: Validate file
            validate_result = self.file_worker.validate_file(
                download_result["content"],
                f"workflow_{workflow_id}.bin"
            )
            results.append(("validate", validate_result))

            if not validate_result["success"]:
                return {"success": False, "error": "Validation failed", "results": results}

            # Step 3: Create VM
            vm_result = await self.vm_worker.create_vm_for_processing()
            results.append(("create_vm", vm_result))

            if not vm_result["success"]:
                return {"success": False, "error": "VM creation failed", "results": results}

            vm_id = vm_result["vm_id"]

            try:
                # Step 4: Simulate file injection (simplified)
                injection_result = {
                    "success": True,
                    "injected_file": f"workflow_{workflow_id}.bin",
                    "vm_id": vm_id
                }
                results.append(("inject", injection_result))

                return {
                    "success": True,
                    "workflow_id": workflow_id,
                    "vm_id": vm_id,
                    "results": results
                }

            finally:
                # Step 5: Cleanup VM
                cleanup_result = await self.vm_worker.destroy_vm(vm_id)
                results.append(("cleanup", cleanup_result))

    # Create worker service instances
    file_worker = RealFileOperationsWorker(celery_app, minio_client, minio_bucket)
    vm_worker = RealVMOperationsWorker(celery_app, vm_service)
    workflow_orchestrator = RealWorkflowOrchestrator(file_worker, vm_worker)

    services = {
        "celery_app": celery_app,
        "file_worker": file_worker,
        "vm_worker": vm_worker,
        "workflow_orchestrator": workflow_orchestrator,
        "minio_client": minio_client,
        "minio_bucket": minio_bucket,
        "vm_service": vm_service
    }

    yield services


# Real Logging & Monitoring Test Fixtures (replacing mocks)

@pytest.fixture(scope="session")
def elasticsearch_container():
    """Real Elasticsearch container for logging"""
    try:
        from testcontainers.compose import DockerCompose
        import tempfile
        import os

        # Create a simple docker-compose for Elasticsearch
        compose_content = """
version: '3.8'
services:
  elasticsearch:
    image: elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
    ports:
      - "9200"
"""

        with tempfile.TemporaryDirectory() as temp_dir:
            compose_file = os.path.join(temp_dir, "docker-compose.yml")
            with open(compose_file, "w") as f:
                f.write(compose_content)

            with DockerCompose(temp_dir) as compose:
                # Get the exposed port
                elasticsearch_port = compose.get_service_port("elasticsearch", 9200)

                # Create a simple container-like object
                class ElasticsearchContainer:
                    def get_container_host_ip(self):
                        return "localhost"

                    def get_exposed_port(self, port):
                        return elasticsearch_port

                yield ElasticsearchContainer()

    except Exception as e:
        pytest.skip(f"Elasticsearch container not available: {e}")

@pytest.fixture
def real_elasticsearch_client(elasticsearch_container):
    """Real Elasticsearch client for testing"""
    try:
        from elasticsearch import Elasticsearch

        # Configure Elasticsearch client
        client = Elasticsearch(
            [f"http://{elasticsearch_container.get_container_host_ip()}:{elasticsearch_container.get_exposed_port(9200)}"],
            verify_certs=False,
            ssl_show_warn=False,
            request_timeout=30,
            retry_on_timeout=True,
            max_retries=3
        )

        # Wait for cluster to be ready
        client.cluster.health(wait_for_status="yellow", timeout="30s")

        yield client

        # Cleanup: Delete test indices
        try:
            test_indices = client.indices.get(index="test-*,turdparty-test-*")
            for index in test_indices.keys():
                client.indices.delete(index=index, ignore=[404])
        except Exception as e:
            logger.warning(f"Failed to cleanup Elasticsearch indices: {e}")

    except Exception as e:
        pytest.skip(f"Elasticsearch client not available: {e}")

@pytest.fixture
def real_logging_monitoring_services(real_elasticsearch_client, real_worker_services):
    """Real logging and monitoring services integration"""
    es_client = real_elasticsearch_client
    worker_services = real_worker_services

    # Create real logging and monitoring service implementations
    class RealElasticsearchLogger:
        def __init__(self, es_client):
            self.es_client = es_client
            self.index_prefix = "turdparty-test"

        def log_event(self, event_type, event_data, index_suffix="events"):
            """Log event to Elasticsearch"""
            import time
            from datetime import datetime

            index_name = f"{self.index_prefix}-{index_suffix}"

            document = {
                "@timestamp": datetime.now(datetime.UTC).isoformat(),
                "event_type": event_type,
                "event_data": event_data,
                "service": "turdparty-test",
                "environment": "test"
            }

            try:
                response = self.es_client.index(
                    index=index_name,
                    document=document
                )

                # Refresh index to make document searchable immediately
                self.es_client.indices.refresh(index=index_name)

                return {
                    "success": True,
                    "document_id": response["_id"],
                    "index": index_name
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e)
                }

        def search_events(self, query, index_suffix="events", size=100):
            """Search events in Elasticsearch"""
            index_name = f"{self.index_prefix}-{index_suffix}"

            try:
                response = self.es_client.search(
                    index=index_name,
                    query=query,
                    size=size,
                    sort=[{"@timestamp": {"order": "desc"}}]
                )

                return {
                    "success": True,
                    "hits": response["hits"]["hits"],
                    "total": response["hits"]["total"]["value"]
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e)
                }

        def get_cluster_health(self):
            """Get Elasticsearch cluster health"""
            try:
                health = self.es_client.cluster.health()
                return {
                    "success": True,
                    "status": health["status"],
                    "cluster_name": health["cluster_name"],
                    "number_of_nodes": health["number_of_nodes"]
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e)
                }

    class RealWorkflowMonitor:
        def __init__(self, es_logger, worker_services):
            self.es_logger = es_logger
            self.worker_services = worker_services

        def monitor_workflow_execution(self, workflow_id, workflow_steps):
            """Monitor workflow execution and log to Elasticsearch"""
            monitoring_data = []

            for step_name, step_result in workflow_steps:
                log_data = {
                    "workflow_id": workflow_id,
                    "step_name": step_name,
                    "step_result": step_result,
                    "success": step_result.get("success", False)
                }

                log_result = self.es_logger.log_event(
                    "workflow_step",
                    log_data,
                    "workflow-monitoring"
                )

                monitoring_data.append({
                    "step": step_name,
                    "logged": log_result["success"],
                    "log_id": log_result.get("document_id")
                })

            return {
                "success": True,
                "workflow_id": workflow_id,
                "monitored_steps": len(monitoring_data),
                "monitoring_data": monitoring_data
            }

        def get_workflow_metrics(self, workflow_id):
            """Get workflow metrics from Elasticsearch"""
            query = {
                "bool": {
                    "must": [
                        {"term": {"event_type": "workflow_step"}},
                        {"term": {"event_data.workflow_id": workflow_id}}
                    ]
                }
            }

            search_result = self.es_logger.search_events(
                query,
                "workflow-monitoring"
            )

            if not search_result["success"]:
                return search_result

            # Analyze workflow metrics
            steps = search_result["hits"]
            successful_steps = sum(1 for hit in steps
                                 if hit["_source"]["event_data"]["success"])

            return {
                "success": True,
                "workflow_id": workflow_id,
                "total_steps": len(steps),
                "successful_steps": successful_steps,
                "success_rate": successful_steps / len(steps) if steps else 0,
                "steps": steps
            }

    class RealSystemMonitor:
        def __init__(self, es_logger):
            self.es_logger = es_logger

        def log_system_metrics(self, metrics_data):
            """Log system metrics to Elasticsearch"""
            return self.es_logger.log_event(
                "system_metrics",
                metrics_data,
                "system-monitoring"
            )

        def get_system_health(self):
            """Get overall system health"""
            # Check Elasticsearch health
            es_health = self.es_logger.get_cluster_health()

            # Aggregate health status
            health_status = {
                "elasticsearch": es_health["success"],
                "overall_status": "healthy" if es_health["success"] else "degraded"
            }

            # Log health check
            self.log_system_metrics({
                "health_check": health_status,
                "timestamp": time.time()
            })

            return {
                "success": True,
                "health_status": health_status
            }

    # Create service instances
    es_logger = RealElasticsearchLogger(es_client)
    workflow_monitor = RealWorkflowMonitor(es_logger, worker_services)
    system_monitor = RealSystemMonitor(es_logger)

    services = {
        "elasticsearch_client": es_client,
        "es_logger": es_logger,
        "workflow_monitor": workflow_monitor,
        "system_monitor": system_monitor,
        "worker_services": worker_services
    }

    yield services

# Legacy mock fixture for backward compatibility
@pytest.fixture
def mock_vm_metrics_service():
    """Mock VM metrics service (legacy - use real_vm_metrics_service instead)"""
    with patch('api.services.vm_metrics_service.vm_metrics_service') as mock_service:
        # Mock initialization
        mock_service.initialize = AsyncMock()

        # Mock metrics collection
        mock_service.get_vm_metrics = AsyncMock(return_value={
            "vm_id": "test_vm",
            "vm_type": "docker",
            "timestamp": int(time.time() * 1000),
            "status": "running",
            "cpu_percent": 25.5,
            "memory_percent": 45.2,
            "memory_used_mb": 1024.0,
            "network_rx_bytes": 1048576,
            "network_tx_bytes": 524288,
            "top_processes": [
                {"pid": 1234, "name": "python", "cpu_percent": 15.2, "memory_mb": 256.5}
            ],
            "uptime_seconds": 3600
        })

        # Mock streaming
        async def mock_stream():
            for i in range(5):
                yield {
                    "vm_id": "test_vm",
                    "timestamp": int(time.time() * 1000) + i * 1000,
                    "cpu_percent": 25.5 + i,
                    "memory_percent": 45.2 + i,
                    "status": "running"
                }
                await asyncio.sleep(0.1)

        mock_service.stream_vm_metrics.return_value = mock_stream()
        mock_service.stop_stream = MagicMock()

        yield mock_service


@pytest.fixture
async def websocket_test_helper():
    """Helper for WebSocket testing"""
    if not WEBSOCKETS_AVAILABLE:
        pytest.skip("websockets not available")

    class WebSocketTestHelper:
        def __init__(self):
            self.connections = []

        async def connect(self, uri, **kwargs):
            """Connect to WebSocket and track connection"""
            websocket = await websockets.connect(uri, **kwargs)
            self.connections.append(websocket)
            return websocket

        async def close_all(self):
            """Close all tracked connections"""
            for ws in self.connections:
                try:
                    await ws.close()
                except:
                    pass
            self.connections.clear()

        async def send_and_receive(self, websocket, message, timeout=5.0):
            """Send message and receive response"""
            await websocket.send(message)
            return await asyncio.wait_for(websocket.recv(), timeout=timeout)

    helper = WebSocketTestHelper()
    yield helper
    await helper.close_all()


@pytest.fixture
def performance_monitor():
    """Monitor performance metrics during test execution"""
    if not PSUTIL_AVAILABLE:
        pytest.skip("psutil not available")

    metrics = {
        'cpu_samples': [],
        'memory_samples': [],
        'start_time': time.time(),
        'monitoring': True
    }

    def monitor():
        process = psutil.Process()
        while metrics['monitoring']:
            try:
                cpu_percent = psutil.cpu_percent()
                memory_info = process.memory_info()

                metrics['cpu_samples'].append(cpu_percent)
                metrics['memory_samples'].append(memory_info.rss)

                time.sleep(0.5)
            except:
                break

    # Start monitoring
    monitor_thread = threading.Thread(target=monitor, daemon=True)
    monitor_thread.start()

    yield metrics

    # Stop monitoring
    metrics['monitoring'] = False
    metrics['end_time'] = time.time()

    # Calculate summary statistics
    if metrics['cpu_samples']:
        metrics['avg_cpu'] = statistics.mean(metrics['cpu_samples'])
        metrics['max_cpu'] = max(metrics['cpu_samples'])
        metrics['avg_memory'] = statistics.mean(metrics['memory_samples'])
        metrics['max_memory'] = max(metrics['memory_samples'])
        metrics['duration'] = metrics['end_time'] - metrics['start_time']


@pytest.fixture(scope="session")
def docker_available():
    """Check if Docker is available for testing"""
    if not DOCKER_AVAILABLE:
        return False
    try:
        client = docker.from_env()
        client.ping()
        return True
    except Exception:
        return False


@pytest.fixture(scope="session")
def vagrant_available():
    """Check if Vagrant is available for testing"""
    import subprocess
    try:
        result = subprocess.run(['vagrant', '--version'],
                              capture_output=True, text=True, timeout=10)
        return result.returncode == 0
    except Exception:
        return False


@pytest.fixture
def skip_if_no_docker(docker_available):
    """Skip test if Docker is not available"""
    if not docker_available:
        pytest.skip("Docker not available")


@pytest.fixture
def skip_if_no_vagrant(vagrant_available):
    """Skip test if Vagrant is not available"""
    if not vagrant_available:
        pytest.skip("Vagrant not available")


@pytest.fixture
def test_data_generator():
    """Generate test data for various scenarios"""
    class TestDataGenerator:
        @staticmethod
        def vm_creation_data(name_suffix="", **overrides):
            """Generate VM creation data"""
            base_data = {
                "name": f"test-vm-{int(time.time() * 1000)}{name_suffix}",
                "template": "ubuntu:20.04",
                "vm_type": "docker",
                "memory_mb": 512,
                "cpus": 1,
                "domain": "TurdParty",
                "description": "Generated test VM"
            }
            base_data.update(overrides)
            return base_data

        @staticmethod
        def command_execution_data(command="echo 'test'", **overrides):
            """Generate command execution data"""
            base_data = {
                "command": command,
                "working_directory": "/tmp",
                "timeout_seconds": 30
            }
            base_data.update(overrides)
            return base_data

        @staticmethod
        def file_upload_data(target_path="/tmp/test_file.txt", **overrides):
            """Generate file upload data"""
            base_data = {
                "target_path": target_path,
                "permissions": "644"
            }
            base_data.update(overrides)
            return base_data

    return TestDataGenerator()


# Container-based service fixtures for integration testing

@pytest.fixture(scope="session")
def minio_container():
    """MinIO container for storage testing"""
    if not TESTCONTAINERS_AVAILABLE:
        pytest.skip("testcontainers not available")

    with MinioContainer() as container:
        yield container


@pytest.fixture(scope="session")
def elasticsearch_container():
    """Elasticsearch container for logging testing"""
    if not TESTCONTAINERS_AVAILABLE:
        pytest.skip("testcontainers not available")

    with ElasticsearchContainer("elasticsearch:8.11.0") as container:
        yield container


@pytest.fixture(scope="session")
def redis_container():
    """Redis container for caching testing"""
    if not TESTCONTAINERS_AVAILABLE:
        pytest.skip("testcontainers not available")

    with RedisContainer() as container:
        yield container


@pytest.fixture
def minio_client(minio_container):
    """MinIO client connected to test container"""
    if not MINIO_AVAILABLE:
        pytest.skip("minio not available")

    client = Minio(
        f"{minio_container.get_container_host_ip()}:{minio_container.get_exposed_port(9000)}",
        access_key=minio_container.access_key,
        secret_key=minio_container.secret_key,
        secure=False
    )

    # Create test bucket
    test_bucket = "turdparty-test"
    if not client.bucket_exists(test_bucket):
        client.make_bucket(test_bucket)

    yield client, test_bucket

    # Cleanup: Remove all test objects
    try:
        objects = client.list_objects(test_bucket, recursive=True)
        for obj in objects:
            client.remove_object(test_bucket, obj.object_name)
    except Exception as e:
        logger.warning(f"Failed to cleanup MinIO objects: {e}")


@pytest.fixture
def elasticsearch_client(elasticsearch_container):
    """Elasticsearch client connected to test container"""
    if not ELASTICSEARCH_AVAILABLE:
        pytest.skip("elasticsearch not available")

    client = AsyncElasticsearch(
        hosts=[f"http://{elasticsearch_container.get_container_host_ip()}:{elasticsearch_container.get_exposed_port(9200)}"],
        verify_certs=False
    )

    yield client

    # Cleanup
    try:
        asyncio.create_task(client.close())
    except Exception as e:
        logger.warning(f"Failed to close Elasticsearch client: {e}")


@pytest.fixture
def redis_client(redis_container):
    """Redis client connected to test container"""
    if not REDIS_AVAILABLE:
        pytest.skip("redis not available")

    client = redis.Redis(
        host=redis_container.get_container_host_ip(),
        port=redis_container.get_exposed_port(6379),
        decode_responses=True
    )

    yield client

    # Cleanup
    try:
        client.flushdb()
        client.close()
    except Exception as e:
        logger.warning(f"Failed to cleanup Redis: {e}")


@pytest.fixture
def docker_client():
    """Docker client for VM testing"""
    if not DOCKER_AVAILABLE:
        pytest.skip("docker not available")

    client = docker.from_env()

    yield client

    # Cleanup: Remove test containers
    try:
        containers = client.containers.list(
            all=True,
            filters={"label": "turdparty.test=true"}
        )
        for container in containers:
            try:
                if container.status == "running":
                    container.kill()
                container.remove(force=True)
            except Exception as e:
                logger.warning(f"Failed to cleanup container {container.id}: {e}")
    except Exception as e:
        logger.warning(f"Failed to cleanup test containers: {e}")


@pytest.fixture
def vm_service(docker_client):
    """VM service with Docker client for testing"""
    from api.services.vm_service import VMService

    service = VMService()
    service.docker_client = docker_client

    yield service


@pytest.fixture
def vm_metrics_service(docker_client):
    """VM metrics service for testing"""
    from api.services.vm_metrics_service import VMMetricsService

    service = VMMetricsService()
    yield service


@pytest.fixture
def storage_service(minio_client):
    """Storage service for testing"""
    try:
        from api.services.storage_service import StorageService

        client, bucket = minio_client
        service = StorageService()
        service.minio_client = client
        service.default_bucket = bucket

        yield service

    except ImportError:
        # Create a simple storage service for testing
        class StorageService:
            def __init__(self, minio_client, bucket):
                self.minio_client = minio_client
                self.default_bucket = bucket

            async def upload_file(self, file_id, filename, file_content, content_type="application/octet-stream", bucket_name=None):
                import io
                import hashlib

                bucket = bucket_name or self.default_bucket
                object_key = f"{file_id}_{filename}"

                # Calculate hash
                file_hash = hashlib.sha256(file_content).hexdigest()

                # Upload to MinIO
                self.minio_client.put_object(
                    bucket,
                    object_key,
                    io.BytesIO(file_content),
                    length=len(file_content),
                    content_type=content_type
                )

                return {
                    "object_key": object_key,
                    "bucket": bucket,
                    "size": len(file_content),
                    "hash": file_hash,
                    "content_type": content_type
                }

            async def download_file(self, object_key, bucket_name=None):
                bucket = bucket_name or self.default_bucket
                response = self.minio_client.get_object(bucket, object_key)
                content = response.read()
                response.close()
                response.release_conn()
                return content

            async def delete_file(self, object_key, bucket_name=None):
                bucket = bucket_name or self.default_bucket
                self.minio_client.remove_object(bucket, object_key)
                return True

            async def file_exists(self, object_key, bucket_name=None):
                bucket = bucket_name or self.default_bucket
                try:
                    self.minio_client.stat_object(bucket, object_key)
                    return True
                except Exception:
                    return False

        client, bucket = minio_client
        service = StorageService(client, bucket)
        yield service


# Enhanced service fixtures with cleanup
@pytest.fixture
def enhanced_vm_service(docker_client):
    """Enhanced VM service with Docker client and cleanup"""
    from api.services.vm_service import VMService

    service = VMService()
    service.docker_client = docker_client

    yield service

    # Cleanup: Remove any test containers created during testing
    try:
        containers = docker_client.containers.list(
            all=True,
            filters={"label": "turdparty.test=true"}
        )
        for container in containers:
            try:
                if container.status == "running":
                    container.kill()
                container.remove(force=True)
            except Exception as e:
                logger.warning(f"Failed to cleanup container {container.id}: {e}")
    except Exception as e:
        logger.warning(f"Failed to cleanup test containers: {e}")


@pytest.fixture
def enhanced_vm_metrics_service(docker_client):
    """Enhanced VM metrics service with Docker client"""
    from api.services.vm_metrics_service import VMMetricsService

    service = VMMetricsService()
    service.docker_client = docker_client

    yield service


@pytest.fixture
def service_integration(api_client, minio_client, enhanced_vm_service):
    """Service integration fixture combining all services"""
    client, bucket = minio_client

    integration = {
        "api_client": api_client,
        "minio_client": client,
        "minio_bucket": bucket,
        "vm_service": enhanced_vm_service,
    }

    yield integration
