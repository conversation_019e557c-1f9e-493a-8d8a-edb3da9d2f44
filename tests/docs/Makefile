# Makefile for Sphinx documentation

# You can set these variables from the command line, and also
# from the environment for the first two.
SPHINXOPTS    ?=
SPHINXBUILD  ?= sphinx-build
SOURCEDIR    = .
BUILDDIR     = _build

# Put it first so that "make" without argument is like "make help".
help:
	@$(SPHINXBUILD) -M help "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

.PHONY: help Makefile

# Custom targets for TurdParty testing documentation

# Build HTML documentation
html:
	@echo "🏗️  Building HTML documentation..."
	@$(SPHINXBUILD) -b html "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS) $(O)
	@echo "✅ HTML documentation built successfully!"
	@echo "📖 Open $(BUILDDIR)/html/index.html to view"

# Build and serve documentation locally
serve: html
	@echo "🚀 Starting local documentation server..."
	@cd $(BUILDDIR)/html && python -m http.server 8080
	@echo "📖 Documentation available at http://localhost:8080"

# Clean build directory
clean:
	@echo "🧹 Cleaning build directory..."
	@rm -rf $(BUILDDIR)/*
	@echo "✅ Build directory cleaned"

# Build PDF documentation
pdf: latexpdf
	@echo "📄 PDF documentation built at $(BUILDDIR)/latex/TurdPartyTesting.pdf"

# Build EPUB documentation
epub:
	@echo "📱 Building EPUB documentation..."
	@$(SPHINXBUILD) -b epub "$(SOURCEDIR)" "$(BUILDDIR)/epub" $(SPHINXOPTS) $(O)
	@echo "✅ EPUB documentation built successfully!"

# Check for broken links
linkcheck:
	@echo "🔗 Checking for broken links..."
	@$(SPHINXBUILD) -b linkcheck "$(SOURCEDIR)" "$(BUILDDIR)/linkcheck" $(SPHINXOPTS) $(O)
	@echo "✅ Link check completed"

# Build documentation with warnings as errors
strict:
	@echo "⚠️  Building documentation with strict warnings..."
	@$(SPHINXBUILD) -W -b html "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS) $(O)
	@echo "✅ Strict build completed successfully!"

# Auto-build documentation on file changes
watch:
	@echo "👀 Watching for file changes..."
	@sphinx-autobuild "$(SOURCEDIR)" "$(BUILDDIR)/html" --host 0.0.0.0 --port 8080

# Build all formats
all: html epub pdf
	@echo "📚 All documentation formats built successfully!"

# Quick development build (faster, less strict)
dev:
	@echo "⚡ Building development documentation..."
	@$(SPHINXBUILD) -b html "$(SOURCEDIR)" "$(BUILDDIR)/html" -E $(SPHINXOPTS) $(O)
	@echo "✅ Development build completed!"

# Production build (strict, all checks)
prod: clean strict linkcheck
	@echo "🚀 Production documentation build completed!"

# Generate API documentation from docstrings
apidoc:
	@echo "📝 Generating API documentation..."
	@sphinx-apidoc -o api ../../api ../../services --force --module-first
	@echo "✅ API documentation generated!"

# Spell check documentation
spellcheck:
	@echo "📝 Running spell check..."
	@$(SPHINXBUILD) -b spelling "$(SOURCEDIR)" "$(BUILDDIR)/spelling" $(SPHINXOPTS) $(O)
	@echo "✅ Spell check completed!"

# Check documentation coverage
coverage:
	@echo "📊 Checking documentation coverage..."
	@$(SPHINXBUILD) -b coverage "$(SOURCEDIR)" "$(BUILDDIR)/coverage" $(SPHINXOPTS) $(O)
	@echo "✅ Coverage check completed!"

# Build documentation statistics
stats:
	@echo "📈 Generating documentation statistics..."
	@find $(SOURCEDIR) -name "*.rst" -o -name "*.md" | xargs wc -l
	@echo "📊 Documentation statistics:"
	@echo "   RST files: $$(find $(SOURCEDIR) -name '*.rst' | wc -l)"
	@echo "   MD files: $$(find $(SOURCEDIR) -name '*.md' | wc -l)"
	@echo "   Total lines: $$(find $(SOURCEDIR) -name '*.rst' -o -name '*.md' | xargs wc -l | tail -1)"

# Validate documentation structure
validate:
	@echo "✅ Validating documentation structure..."
	@python -c "
import os
required_files = ['index.rst', 'quickstart.rst', 'unit-testing.rst', 'property-testing.rst', 'performance-testing.rst']
missing = [f for f in required_files if not os.path.exists(f)]
if missing:
    print('❌ Missing required files:', missing)
    exit(1)
else:
    print('✅ All required files present')
"

# Install documentation dependencies
install-deps:
	@echo "📦 Installing documentation dependencies..."
	@pip install sphinx sphinx-rtd-theme myst-parser sphinx-copybutton sphinx-tabs sphinxcontrib-mermaid
	@echo "✅ Documentation dependencies installed!"

# Update documentation dependencies
update-deps:
	@echo "🔄 Updating documentation dependencies..."
	@pip install --upgrade sphinx sphinx-rtd-theme myst-parser sphinx-copybutton sphinx-tabs sphinxcontrib-mermaid
	@echo "✅ Documentation dependencies updated!"

# Create documentation archive
archive: html
	@echo "📦 Creating documentation archive..."
	@cd $(BUILDDIR) && tar -czf turdparty-testing-docs-$$(date +%Y%m%d).tar.gz html/
	@echo "✅ Documentation archive created: $(BUILDDIR)/turdparty-testing-docs-$$(date +%Y%m%d).tar.gz"

# Deploy documentation (placeholder)
deploy: prod
	@echo "🚀 Deploying documentation..."
	@echo "⚠️  Configure your deployment target in this Makefile"
	@echo "💡 Common targets: GitHub Pages, Netlify, AWS S3, etc."

# Show documentation metrics
metrics:
	@echo "📊 Documentation Metrics:"
	@echo "========================"
	@echo "📄 Files:"
	@echo "   RST files: $$(find $(SOURCEDIR) -name '*.rst' | wc -l)"
	@echo "   MD files: $$(find $(SOURCEDIR) -name '*.md' | wc -l)"
	@echo "   Python files: $$(find $(SOURCEDIR) -name '*.py' | wc -l)"
	@echo ""
	@echo "📏 Size:"
	@echo "   Total lines: $$(find $(SOURCEDIR) -name '*.rst' -o -name '*.md' | xargs wc -l | tail -1 | awk '{print $$1}')"
	@echo "   Total words: $$(find $(SOURCEDIR) -name '*.rst' -o -name '*.md' | xargs wc -w | tail -1 | awk '{print $$1}')"
	@echo "   Total chars: $$(find $(SOURCEDIR) -name '*.rst' -o -name '*.md' | xargs wc -c | tail -1 | awk '{print $$1}')"
	@echo ""
	@echo "🏗️  Build:"
	@if [ -d "$(BUILDDIR)/html" ]; then \
		echo "   HTML size: $$(du -sh $(BUILDDIR)/html | cut -f1)"; \
		echo "   HTML files: $$(find $(BUILDDIR)/html -name '*.html' | wc -l)"; \
	else \
		echo "   HTML: Not built (run 'make html')"; \
	fi

# Development workflow
dev-workflow: clean dev serve

# CI/CD workflow
ci-workflow: install-deps validate strict linkcheck coverage

# Help with custom targets
help-custom:
	@echo "TurdParty Testing Documentation - Custom Targets:"
	@echo "================================================"
	@echo ""
	@echo "Development:"
	@echo "  dev          - Quick development build"
	@echo "  serve        - Build and serve locally"
	@echo "  watch        - Auto-rebuild on changes"
	@echo "  dev-workflow - Clean, build, and serve"
	@echo ""
	@echo "Production:"
	@echo "  prod         - Production build with all checks"
	@echo "  strict       - Build with warnings as errors"
	@echo "  all          - Build all formats (HTML, EPUB, PDF)"
	@echo ""
	@echo "Quality:"
	@echo "  linkcheck    - Check for broken links"
	@echo "  spellcheck   - Run spell checker"
	@echo "  coverage     - Check documentation coverage"
	@echo "  validate     - Validate documentation structure"
	@echo ""
	@echo "Utilities:"
	@echo "  apidoc       - Generate API documentation"
	@echo "  stats        - Show documentation statistics"
	@echo "  metrics      - Show detailed metrics"
	@echo "  archive      - Create documentation archive"
	@echo ""
	@echo "Dependencies:"
	@echo "  install-deps - Install documentation dependencies"
	@echo "  update-deps  - Update documentation dependencies"
	@echo ""
	@echo "CI/CD:"
	@echo "  ci-workflow  - Complete CI/CD validation"
	@echo "  deploy       - Deploy documentation (configure first)"

# Catch-all target: route all unknown targets to Sphinx using the new
# "make mode" option.  $(O) is meant as a shortcut for $(SPHINXOPTS).
%: Makefile
	@$(SPHINXBUILD) -M $@ "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)
