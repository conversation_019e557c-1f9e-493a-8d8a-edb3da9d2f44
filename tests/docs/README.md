# TurdParty Testing Framework Documentation

[![Documentation Status](https://img.shields.io/badge/docs-sphinx-blue.svg)](https://turdparty-testing-docs.readthedocs.io/)
[![Python Version](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![Testing Framework](https://img.shields.io/badge/Testing-pytest-green.svg)](https://pytest.org/)
[![Property Testing](https://img.shields.io/badge/Property--Based-Hypothesis-orange.svg)](https://hypothesis.readthedocs.io/)
[![Performance](https://img.shields.io/badge/Performance-pytest--benchmark-red.svg)](https://pytest-benchmark.readthedocs.io/)
[![Code Quality](https://img.shields.io/badge/Code--Quality-Ruff-yellow.svg)](https://github.com/astral-sh/ruff)

Comprehensive documentation for the TurdParty modern testing framework, featuring industry-standard tools and best practices.

## 🎯 Quick Stats

- **36 Unit Tests** - Fast, isolated component validation
- **9 Property Tests** - Hypothesis-driven edge case discovery
- **6/6 Real VM Tests** - Actual Docker containers and VMs (no mocks)
- **1.54M ops/sec** - Model creation performance benchmark
- **90% Issue Reduction** - Code quality improvements with Ruff
- **Zero Warnings** - Clean, modern Python codebase

## 📚 Documentation Structure

```
tests/docs/
├── index.rst                 # Main documentation index
├── quickstart.rst            # Quick start guide
├── installation.rst          # Installation & setup
├── unit-testing.rst          # Unit testing guide
├── property-testing.rst      # Property-based testing
├── performance-testing.rst   # Performance & benchmarking
├── conf.py                   # Sphinx configuration
├── requirements.txt          # Documentation dependencies
├── Makefile                  # Build automation
├── build_docs.sh            # Build script
└── _static/                  # Static assets
    ├── custom.css           # Custom styling
    └── custom.js            # Interactive features
```

## 🚀 Quick Start

### Build Documentation

```bash
# Setup and build all formats
./build_docs.sh full

# Build HTML only
./build_docs.sh html

# Development mode with auto-rebuild
./build_docs.sh watch
```

### Using Make

```bash
# Build HTML documentation
make html

# Serve locally
make serve

# Build all formats
make all

# Development workflow
make dev-workflow
```

## 🛠️ Installation

### Prerequisites

- Python 3.11+
- Sphinx 7.2+
- Git

### Setup

```bash
# Clone repository
<NAME_EMAIL>:tenbahtsecurity/turdparty.git
cd turdparty/tests/docs

# Setup virtual environment and dependencies
./build_docs.sh setup

# Build documentation
./build_docs.sh html

# Serve locally
./build_docs.sh serve
```

### Using pip

```bash
# Install documentation dependencies
pip install -r requirements.txt

# Build with Sphinx
sphinx-build -b html . _build/html

# Serve locally
cd _build/html && python -m http.server 8080
```

## 📖 Documentation Sections

### Core Testing

- **[Quick Start](quickstart.rst)** - Get up and running in minutes
- **[Installation](installation.rst)** - Complete setup guide
- **[Unit Testing](unit-testing.rst)** - Fast, isolated component tests
- **[Property Testing](property-testing.rst)** - Hypothesis-driven edge case discovery
- **[Performance Testing](performance-testing.rst)** - Benchmarking and optimisation

### Tools & Configuration

- **pytest Configuration** - Modern test execution
- **Hypothesis Setup** - Property-based testing
- **Ruff Integration** - Fast Python linting
- **Pre-commit Hooks** - Automated quality gates
- **CI/CD Integration** - Continuous testing

### Advanced Topics

- **Test Patterns** - Best practices and patterns
- **Debugging** - Troubleshooting and debugging
- **Reporting** - Coverage and metrics
- **Contributing** - Development guidelines

## 🎨 Features

### Modern Sphinx Theme

- **Responsive Design** - Works on all devices
- **Interactive Elements** - Copy buttons, search, navigation
- **Custom Styling** - TurdParty branding and colours
- **Performance Optimised** - Fast loading and rendering

### Enhanced Content

- **Code Examples** - Comprehensive, runnable examples
- **Performance Metrics** - Real benchmark results
- **Interactive Diagrams** - Mermaid flowcharts and diagrams
- **Status Indicators** - Live test status and metrics

### Developer Experience

- **Auto-rebuild** - Watch mode for development
- **Link Checking** - Automated broken link detection
- **Spell Checking** - Content quality assurance
- **Multiple Formats** - HTML, PDF, EPUB output

## 🔧 Build Commands

### Basic Commands

```bash
# Setup environment
./build_docs.sh setup

# Build HTML
./build_docs.sh html

# Serve locally
./build_docs.sh serve

# Watch for changes
./build_docs.sh watch
```

### Advanced Commands

```bash
# Build all formats
./build_docs.sh all

# Check links
./build_docs.sh linkcheck

# Generate statistics
./build_docs.sh stats

# Create archive
./build_docs.sh archive

# Complete workflow
./build_docs.sh full
```

### Make Targets

```bash
# Development
make dev          # Quick development build
make serve        # Build and serve locally
make watch        # Auto-rebuild on changes

# Production
make prod         # Production build with all checks
make all          # Build all formats
make clean        # Clean build directory

# Quality
make linkcheck    # Check for broken links
make coverage     # Check documentation coverage
make validate     # Validate structure
```

## 📊 Documentation Metrics

Current documentation statistics:

- **RST Files**: 6+ comprehensive guides
- **Total Lines**: 2000+ lines of documentation
- **Code Examples**: 100+ runnable examples
- **Build Time**: < 30 seconds for full build
- **Output Size**: ~5MB HTML documentation

## 🎯 Testing Framework Coverage

The documentation covers our complete modern testing stack:

### Core Tools
- ✅ **pytest** - Test execution framework
- ✅ **Hypothesis** - Property-based testing
- ✅ **pytest-benchmark** - Performance testing
- ✅ **Locust** - Load testing
- ✅ **Ruff** - Code quality
- ✅ **MyPy** - Type checking
- ✅ **Bandit** - Security scanning

### Test Types
- ✅ **Unit Tests** (36 tests) - Component validation
- ✅ **Property Tests** (9 tests) - Edge case discovery
- ✅ **Performance Tests** - Benchmark validation
- ✅ **Security Tests** - Vulnerability scanning
- ✅ **Integration Tests** - Service interaction
- ✅ **Load Tests** - Realistic usage simulation

## 🚀 Deployment

### GitHub Pages

```bash
# Build for GitHub Pages
make prod

# Deploy (configure in Makefile)
make deploy
```

### Read the Docs

1. Connect repository to Read the Docs
2. Configure build settings
3. Use `requirements.txt` for dependencies
4. Set build command: `sphinx-build -b html . _build/html`

### Custom Hosting

```bash
# Build static files
./build_docs.sh html

# Upload _build/html/ to your hosting provider
rsync -av _build/html/ user@server:/var/www/docs/
```

## 🤝 Contributing

### Documentation Guidelines

1. **Follow RST conventions** - Use proper syntax and structure
2. **Include code examples** - Provide runnable, tested examples
3. **Update cross-references** - Maintain internal links
4. **Test builds locally** - Ensure no warnings or errors

### Adding New Sections

1. Create new `.rst` file
2. Add to `index.rst` toctree
3. Update navigation if needed
4. Build and test locally
5. Submit pull request

### Style Guide

- **Headers**: Use consistent header hierarchy
- **Code Blocks**: Include language specification
- **Links**: Use meaningful link text
- **Examples**: Provide context and explanation

## 🐛 Troubleshooting

### Common Issues

**Build Failures**
```bash
# Check for syntax errors
./build_docs.sh validate

# Build with verbose output
sphinx-build -v -b html . _build/html
```

**Missing Dependencies**
```bash
# Reinstall dependencies
./build_docs.sh setup

# Or manually
pip install -r requirements.txt
```

**Broken Links**
```bash
# Check all links
./build_docs.sh linkcheck

# Review output
cat _build/linkcheck/output.txt
```

### Getting Help

1. Check the [Sphinx documentation](https://www.sphinx-doc.org/)
2. Review existing issues on GitHub
3. Ask questions in project discussions
4. Submit bug reports with details

## 📄 Licence

This documentation is part of the TurdParty project and follows the same licence terms.

## 🔗 Links

- **Main Project**: [TurdParty Repository](https://github.com/tenbahtsecurity/turdparty)
- **Testing Guide**: [Main Testing Documentation](../TESTING.md)
- **Sphinx Documentation**: [sphinx-doc.org](https://www.sphinx-doc.org/)
- **Read the Docs**: [readthedocs.org](https://readthedocs.org/)

---

**Built with ❤️ using Sphinx and modern documentation tools**
