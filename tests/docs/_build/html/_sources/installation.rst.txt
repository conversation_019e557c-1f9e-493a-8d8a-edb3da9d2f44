Installation & Setup
====================

This guide covers the installation and setup of the TurdParty testing framework, including all dependencies and development tools.

Prerequisites
-------------

System Requirements
~~~~~~~~~~~~~~~~~~

* **Python 3.11+** - Modern Python with latest features
* **Git** - Version control system
* **Docker** (optional) - For integration tests and services
* **Nix** (recommended) - For reproducible development environment

Supported Platforms
~~~~~~~~~~~~~~~~~~

* **Linux** - Primary development platform (Ubuntu 20.04+, Debian 11+)
* **macOS** - Full support (macOS 11+)
* **Windows** - WSL2 recommended for best experience

Installation Methods
--------------------

Method 1: Nix Development Environment (Recommended)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The Nix development environment provides all tools and dependencies in a reproducible manner.

.. code-block:: bash

   # Clone the repository
   <NAME_EMAIL>:tenbahtsecurity/turdparty.git
   cd turdparty

   # Enter Nix development shell
   nix-shell

   # All testing tools are now available:
   python --version    # Python 3.11+
   pytest --version    # pytest 7.4+
   ruff --version      # ruff 0.1.6+
   mypy --version      # mypy 1.7+

**Available Tools in Nix Shell:**

.. list-table:: Nix Environment Tools
   :widths: 25 25 50
   :header-rows: 1

   * - Tool
     - Version
     - Purpose
   * - pytest
     - 7.4+
     - Test execution framework
   * - hypothesis
     - 6.92+
     - Property-based testing
   * - pytest-benchmark
     - 4.0+
     - Performance benchmarking
   * - ruff
     - 0.1.6+
     - Fast Python linting
   * - mypy
     - 1.7+
     - Static type checking
   * - bandit
     - 1.7.5+
     - Security scanning
   * - pre-commit
     - 3.6+
     - Git hooks

Method 2: pip Installation
~~~~~~~~~~~~~~~~~~~~~~~~~

Install using Python's package manager:

.. code-block:: bash

   # Clone the repository
   <NAME_EMAIL>:tenbahtsecurity/turdparty.git
   cd turdparty

   # Create virtual environment
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate

   # Install with development dependencies
   pip install -e ".[dev]"

   # Verify installation
   python -m pytest --version
   ruff --version

Method 3: Development Dependencies Only
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Install just the testing tools without the main application:

.. code-block:: bash

   # Install core testing tools
   pip install pytest pytest-asyncio pytest-cov pytest-benchmark
   pip install hypothesis factory-boy faker
   pip install ruff mypy bandit safety
   pip install locust pre-commit

   # Install documentation tools
   pip install sphinx sphinx-rtd-theme myst-parser

Configuration
-------------

Environment Setup
~~~~~~~~~~~~~~~~

Create a ``.env`` file for local development:

.. code-block:: bash

   # Development environment variables
   export DEVELOPMENT=true
   export DEBUG=true
   export LOG_LEVEL=DEBUG
   export TURDPARTY_ENV=development

   # Testing configuration
   export TEST_MODE=false
   export COVERAGE_THRESHOLD=80
   export PARALLEL_WORKERS=4

   # Service endpoints (for integration tests)
   export API_BASE_URL=http://localhost:8000
   export DATABASE_URL=postgresql://test:test@localhost/test_db
   export REDIS_URL=redis://localhost:6379/1

pytest Configuration
~~~~~~~~~~~~~~~~~~~

The testing framework is configured via ``pyproject.toml``:

.. code-block:: toml

   [tool.pytest.ini_options]
   minversion = "7.0"
   testpaths = ["tests"]
   markers = [
       "unit: Unit tests",
       "integration: Integration tests",
       "performance: Performance tests",
       "security: Security tests",
       "property: Property-based tests",
       "benchmark: Benchmark tests",
   ]
   filterwarnings = [
       "error",
       "ignore::UserWarning",
       "ignore::DeprecationWarning",
   ]

Pre-commit Hooks Setup
~~~~~~~~~~~~~~~~~~~~~

Install and configure pre-commit hooks:

.. code-block:: bash

   # Install pre-commit hooks
   pre-commit install

   # Run hooks manually (optional)
   pre-commit run --all-files

   # Update hooks to latest versions
   pre-commit autoupdate

Verification
-----------

Test Installation
~~~~~~~~~~~~~~~~

Verify your installation by running the test suite:

.. code-block:: bash

   # Run basic unit tests
   python -m pytest tests/unit/test_basic.py -v

   # Expected output:
   # ======== test session starts ========
   # tests/unit/test_basic.py::TestBasicFunctionality::test_python_environment PASSED
   # tests/unit/test_basic.py::TestBasicFunctionality::test_file_operations PASSED
   # ...
   # ======== 12 passed in 0.03s ========

Test All Components
~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Test all unit tests
   python -m pytest tests/unit/ -v

   # Test property-based tests
   python -m pytest tests/property/ -v

   # Test performance benchmarks
   python -m pytest tests/performance/test_benchmarks.py::TestModelPerformance::test_file_injection_create_performance --benchmark-only

   # Test code quality
   ruff check tests/unit/test_basic.py
   mypy api/ services/

Tool Verification
~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Verify all tools are working
   ./scripts/test_runner.sh help

   # Expected output:
   # TurdParty Modern Test Runner
   # 
   # Usage: ./scripts/test_runner.sh [COMMAND] [OPTIONS]
   # 
   # Commands:
   #   unit         Run unit tests with coverage
   #   property     Run property-based tests (Hypothesis)
   #   performance  Run performance benchmarks
   #   ...

Development Environment
----------------------

IDE Configuration
~~~~~~~~~~~~~~~~

**VS Code Settings** (`.vscode/settings.json`):

.. code-block:: json

   {
       "python.defaultInterpreterPath": "./venv/bin/python",
       "python.testing.pytestEnabled": true,
       "python.testing.pytestArgs": ["tests/"],
       "python.linting.enabled": true,
       "python.linting.ruffEnabled": true,
       "python.formatting.provider": "ruff",
       "python.typeChecking": "mypy"
   }

**PyCharm Configuration:**

1. Set Python interpreter to your virtual environment
2. Enable pytest as test runner
3. Configure Ruff as external tool
4. Enable MyPy type checking

Shell Aliases
~~~~~~~~~~~~

Add these aliases to your shell configuration:

.. code-block:: bash

   # Testing aliases
   alias tp-test='python -m pytest tests/unit/ -v'
   alias tp-test-all='python -m pytest tests/ -v'
   alias tp-property='python -m pytest tests/property/ -v'
   alias tp-benchmark='python -m pytest tests/performance/ --benchmark-only'
   alias tp-coverage='python -m pytest tests/unit/ --cov=api --cov=services --cov-report=html'

   # Code quality aliases
   alias tp-lint='ruff check . && ruff format . && mypy api/ services/'
   alias tp-format='ruff format .'
   alias tp-security='bandit -r api/ services/'

   # Development aliases
   alias tp-up='docker-compose up -d'
   alias tp-down='docker-compose down'
   alias tp-logs='docker-compose logs -f'

Docker Services (Optional)
--------------------------

For integration testing, you may need supporting services:

.. code-block:: yaml

   # docker-compose.test.yml
   version: '3.8'
   services:
     postgres:
       image: postgres:15
       environment:
         POSTGRES_DB: test_db
         POSTGRES_USER: test
         POSTGRES_PASSWORD: test
       ports:
         - "5432:5432"

     redis:
       image: redis:7-alpine
       ports:
         - "6379:6379"

     elasticsearch:
       image: elasticsearch:8.11.0
       environment:
         - discovery.type=single-node
         - xpack.security.enabled=false
       ports:
         - "9200:9200"

Start test services:

.. code-block:: bash

   # Start test services
   docker-compose -f docker-compose.test.yml up -d

   # Run integration tests
   python -m pytest tests/integration/ -v

   # Stop test services
   docker-compose -f docker-compose.test.yml down

Troubleshooting
--------------

Common Issues
~~~~~~~~~~~~

**1. Import Errors**

.. code-block:: bash

   # Fix PYTHONPATH
   export PYTHONPATH="$PWD:$PYTHONPATH"

   # Or install in development mode
   pip install -e .

**2. Permission Errors**

.. code-block:: bash

   # Make scripts executable
   chmod +x scripts/test_runner.sh

   # Fix pre-commit permissions
   pre-commit clean
   pre-commit install

**3. Missing Dependencies**

.. code-block:: bash

   # Reinstall all dependencies
   pip install -e ".[dev]" --force-reinstall

   # Or use Nix for guaranteed environment
   nix-shell

**4. Test Failures**

.. code-block:: bash

   # Run with verbose output
   python -m pytest tests/unit/failing_test.py -v -s --tb=long

   # Check for environment issues
   python -c "import sys; print(sys.path)"

**5. Performance Issues**

.. code-block:: bash

   # Run tests in parallel
   python -m pytest tests/unit/ -n auto

   # Skip slow tests
   python -m pytest tests/unit/ -m "not slow"

Platform-Specific Notes
~~~~~~~~~~~~~~~~~~~~~~

**macOS:**

.. code-block:: bash

   # Install Homebrew dependencies
   brew install python@3.11 git docker

   # Use pyenv for Python version management
   brew install pyenv
   pyenv install 3.11.6
   pyenv global 3.11.6

**Windows (WSL2):**

.. code-block:: bash

   # Install Python 3.11
   sudo apt update
   sudo apt install python3.11 python3.11-venv python3.11-dev

   # Install Docker Desktop for Windows
   # Enable WSL2 integration

**Linux (Ubuntu/Debian):**

.. code-block:: bash

   # Install system dependencies
   sudo apt update
   sudo apt install python3.11 python3.11-venv python3.11-dev git

   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh

Getting Help
-----------

If you encounter issues:

1. **Check the documentation** - Most common issues are covered
2. **Review the logs** - Use verbose output for debugging
3. **Check GitHub issues** - Search for similar problems
4. **Ask for help** - Create a new issue with details

**Useful Commands for Debugging:**

.. code-block:: bash

   # Show environment information
   python -c "import sys; print(f'Python: {sys.version}'); print(f'Path: {sys.path}')"

   # Show installed packages
   pip list | grep -E "(pytest|hypothesis|ruff|mypy)"

   # Test basic functionality
   python -c "import pytest, hypothesis, ruff; print('✅ All imports successful')"

   # Show test discovery
   python -m pytest --collect-only tests/unit/

Next Steps
----------

After successful installation:

1. **Run the Quick Start** - :doc:`quickstart`
2. **Explore Unit Testing** - :doc:`unit-testing`
3. **Try Property-Based Testing** - :doc:`property-testing`
4. **Set up your IDE** - Configure your development environment
5. **Join the community** - Contribute to the project
