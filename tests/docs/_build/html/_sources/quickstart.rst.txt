Quick Start Guide
=================

This guide will get you up and running with the TurdParty testing framework in minutes.

Prerequisites
-------------

Before you begin, ensure you have:

* Python 3.11 or higher
* Git for version control
* Docker (optional, for integration tests)
* <PERSON> (recommended for development environment)

Installation
------------

Development Environment Setup
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Option 1: Using Nix (Recommended)**

.. code-block:: bash

   # Enter the Nix development shell
   cd turdparty-collab
   nix-shell

   # All testing tools are now available:
   # pytest, hypothesis, ruff, mypy, bandit, locust, etc.

**Option 2: Using pip**

.. code-block:: bash

   # Install testing dependencies
   pip install -r requirements-dev.txt

   # Or install specific tools
   pip install pytest pytest-benchmark hypothesis locust ruff mypy bandit

**Option 3: Using the project's pyproject.toml**

.. code-block:: bash

   # Install with development dependencies
   pip install -e ".[dev]"

Running Your First Tests
------------------------

Unit Tests
~~~~~~~~~~

.. code-block:: bash

   # Run all unit tests
   python -m pytest tests/unit/ -v

   # Expected output:
   # ======== test session starts ========
   # tests/unit/test_basic.py ............ [ 33%]
   # tests/unit/test_models_validation.py ........................ [100%]
   # ======== 36 passed in 0.06s ========

Property-Based Tests
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run property-based tests with Hypothesis
   python -m pytest tests/property/ -v

   # Expected output:
   # ======== test session starts ========
   # tests/property/test_property_based.py ......... [100%]
   # ======== 9 passed in 1.02s ========

Performance Benchmarks
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run performance benchmarks
   python -m pytest tests/performance/test_benchmarks.py::TestModelPerformance::test_file_injection_create_performance --benchmark-only -v

   # Expected output:
   # Name (time in us)                              Min      Max     Mean
   # test_file_injection_create_performance      0.6400   2.1000   0.6500

Using the Modern Test Runner
----------------------------

Our custom test runner provides a unified interface for all testing operations:

.. code-block:: bash

   # Show all available commands
   ./scripts/test_runner.sh help

   # Run complete test suite
   ./scripts/test_runner.sh all

   # Run specific test types
   ./scripts/test_runner.sh unit         # Unit tests only
   ./scripts/test_runner.sh property    # Property-based tests
   ./scripts/test_runner.sh performance # Performance benchmarks
   ./scripts/test_runner.sh security    # Security tests
   ./scripts/test_runner.sh lint        # Code quality checks

Test Coverage
~~~~~~~~~~~~~

.. code-block:: bash

   # Run tests with coverage reporting
   python -m pytest tests/unit/ --cov=api --cov=services --cov-report=html --cov-report=term-missing

   # View HTML coverage report
   open htmlcov/index.html

Code Quality Checks
-------------------

Linting and Formatting
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Check code quality with Ruff
   ruff check .

   # Auto-fix issues
   ruff check . --fix

   # Format code
   ruff format .

Type Checking
~~~~~~~~~~~~

.. code-block:: bash

   # Run static type checking
   mypy api/ services/

Security Scanning
~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Scan for security vulnerabilities
   bandit -r api/ services/

   # Check dependencies for known vulnerabilities
   safety check

Pre-commit Hooks
~~~~~~~~~~~~~~~

.. code-block:: bash

   # Install pre-commit hooks
   pre-commit install

   # Run hooks manually
   pre-commit run --all-files

Common Workflows
---------------

Development Workflow
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # 1. Make code changes
   vim api/models/file_injection.py

   # 2. Run relevant tests
   python -m pytest tests/unit/test_models_validation.py -v

   # 3. Check code quality
   ruff check . --fix
   ruff format .

   # 4. Run full test suite
   ./scripts/test_runner.sh unit

   # 5. Commit changes (pre-commit hooks run automatically)
   git add .
   git commit -m "feat: improve file injection model validation"

Testing New Features
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # 1. Write failing test first (TDD)
   vim tests/unit/test_new_feature.py

   # 2. Run the failing test
   python -m pytest tests/unit/test_new_feature.py -v

   # 3. Implement the feature
   vim api/services/new_feature.py

   # 4. Run test until it passes
   python -m pytest tests/unit/test_new_feature.py -v

   # 5. Add property-based tests for edge cases
   vim tests/property/test_new_feature_properties.py

   # 6. Run comprehensive test suite
   ./scripts/test_runner.sh all

Performance Testing
~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # 1. Write performance test
   vim tests/performance/test_new_feature_performance.py

   # 2. Run benchmark
   python -m pytest tests/performance/test_new_feature_performance.py --benchmark-only

   # 3. Compare with baseline
   python -m pytest tests/performance/ --benchmark-compare=baseline.json

Load Testing
~~~~~~~~~~~

.. code-block:: bash

   # 1. Start the application
   docker-compose up -d

   # 2. Run load tests
   locust -f tests/load/locustfile.py --headless -u 10 -r 2 -t 30s --host=http://localhost:8000

   # 3. View results
   open load-test-report.html

Configuration Examples
---------------------

pytest Configuration
~~~~~~~~~~~~~~~~~~~

Create or update ``pyproject.toml``:

.. code-block:: toml

   [tool.pytest.ini_options]
   minversion = "7.0"
   testpaths = ["tests"]
   markers = [
       "unit: Unit tests",
       "property: Property-based tests", 
       "performance: Performance tests",
       "security: Security tests",
   ]

Hypothesis Configuration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # In tests/conftest.py
   from hypothesis import settings, Verbosity

   # Configure Hypothesis globally
   settings.register_profile("dev", max_examples=10, verbosity=Verbosity.verbose)
   settings.register_profile("ci", max_examples=100, verbosity=Verbosity.normal)
   settings.load_profile("dev")

Ruff Configuration
~~~~~~~~~~~~~~~~~

.. code-block:: toml

   # In pyproject.toml
   [tool.ruff]
   target-version = "py311"
   line-length = 88

   [tool.ruff.lint]
   select = ["E", "W", "F", "I", "B", "UP", "PL", "RUF"]
   ignore = ["E501", "B008"]

Next Steps
----------

Now that you have the basics working, explore these advanced topics:

1. **Writing Effective Tests** - :doc:`unit-testing`
2. **Property-Based Testing** - :doc:`property-testing`
3. **Performance Optimization** - :doc:`performance-testing`
4. **Security Validation** - :doc:`security-testing`
5. **CI/CD Integration** - :doc:`ci-cd`

Troubleshooting
--------------

Common Issues
~~~~~~~~~~~~

**Import Errors**

.. code-block:: bash

   # Ensure PYTHONPATH is set correctly
   export PYTHONPATH="$PWD:$PYTHONPATH"

**Permission Errors**

.. code-block:: bash

   # Make scripts executable
   chmod +x scripts/test_runner.sh

**Missing Dependencies**

.. code-block:: bash

   # Reinstall development dependencies
   pip install -e ".[dev]"

**Test Failures**

.. code-block:: bash

   # Run with verbose output for debugging
   python -m pytest tests/unit/failing_test.py -v -s --tb=long

Getting Help
~~~~~~~~~~~~

* 📖 Read the full documentation
* 🐛 Check existing GitHub issues
* 💬 Ask questions in discussions
* 🤝 Contribute improvements

.. tip::
   Use the ``--help`` flag with any command to see available options:
   
   .. code-block:: bash
   
      python -m pytest --help
      ./scripts/test_runner.sh help
      ruff --help
