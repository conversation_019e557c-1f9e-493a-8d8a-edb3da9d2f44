// Custom JavaScript for TurdParty Testing Documentation

document.addEventListener('DOMContentLoaded', function() {
    // Add copy buttons to code blocks
    addCopyButtons();
    
    // Add performance metrics animations
    animateMetrics();
    
    // Add interactive test status indicators
    addTestStatusIndicators();
    
    // Add search enhancements
    enhanceSearch();
    
    // Add responsive table handling
    makeTablesResponsive();
    
    // Add smooth scrolling
    addSmoothScrolling();
});

function addCopyButtons() {
    const codeBlocks = document.querySelectorAll('pre');
    
    codeBlocks.forEach(function(codeBlock) {
        const button = document.createElement('button');
        button.className = 'copy-button';
        button.innerHTML = '📋 Copy';
        button.style.cssText = `
            position: absolute;
            top: 8px;
            right: 8px;
            background: #2980B9;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        const wrapper = document.createElement('div');
        wrapper.style.position = 'relative';
        codeBlock.parentNode.insertBefore(wrapper, codeBlock);
        wrapper.appendChild(codeBlock);
        wrapper.appendChild(button);
        
        wrapper.addEventListener('mouseenter', function() {
            button.style.opacity = '1';
        });
        
        wrapper.addEventListener('mouseleave', function() {
            button.style.opacity = '0';
        });
        
        button.addEventListener('click', function() {
            const text = codeBlock.textContent;
            navigator.clipboard.writeText(text).then(function() {
                button.innerHTML = '✅ Copied!';
                setTimeout(function() {
                    button.innerHTML = '📋 Copy';
                }, 2000);
            });
        });
    });
}

function animateMetrics() {
    const metrics = document.querySelectorAll('.performance-metric, .stat-number');
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(function(entry) {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'countUp 1s ease-out';
                observer.unobserve(entry.target);
            }
        });
    });
    
    metrics.forEach(function(metric) {
        observer.observe(metric);
    });
    
    // Add CSS animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes countUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    document.head.appendChild(style);
}

function addTestStatusIndicators() {
    // Add interactive status badges
    const statusBadges = document.querySelectorAll('.status-badge');
    
    statusBadges.forEach(function(badge) {
        badge.addEventListener('click', function() {
            const status = badge.textContent.toLowerCase();
            showStatusDetails(status, badge);
        });
        
        badge.style.cursor = 'pointer';
        badge.title = 'Click for details';
    });
}

function showStatusDetails(status, element) {
    const details = {
        'passing': {
            icon: '✅',
            message: 'All tests are passing successfully',
            color: '#27AE60'
        },
        'working': {
            icon: '⚙️',
            message: 'Feature is operational and functioning',
            color: '#3498DB'
        },
        'configured': {
            icon: '🔧',
            message: 'Tool is configured and ready to use',
            color: '#F39C12'
        },
        'clean': {
            icon: '🧹',
            message: 'Code quality issues have been resolved',
            color: '#27AE60'
        }
    };
    
    const detail = details[status] || { icon: 'ℹ️', message: 'Status information', color: '#3498DB' };
    
    // Create tooltip
    const tooltip = document.createElement('div');
    tooltip.className = 'status-tooltip';
    tooltip.innerHTML = `${detail.icon} ${detail.message}`;
    tooltip.style.cssText = `
        position: absolute;
        background: ${detail.color};
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 14px;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + 'px';
    tooltip.style.top = (rect.bottom + 8) + 'px';
    
    setTimeout(() => tooltip.style.opacity = '1', 10);
    
    setTimeout(function() {
        tooltip.style.opacity = '0';
        setTimeout(() => document.body.removeChild(tooltip), 300);
    }, 3000);
}

function enhanceSearch() {
    // Add search highlighting
    const searchInput = document.querySelector('input[type="search"]');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            if (query.length > 2) {
                highlightSearchTerms(query);
            } else {
                clearHighlights();
            }
        });
    }
}

function highlightSearchTerms(query) {
    const content = document.querySelector('.document');
    if (!content) return;
    
    const walker = document.createTreeWalker(
        content,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );
    
    const textNodes = [];
    let node;
    while (node = walker.nextNode()) {
        textNodes.push(node);
    }
    
    textNodes.forEach(function(textNode) {
        const text = textNode.textContent;
        const regex = new RegExp(`(${query})`, 'gi');
        if (regex.test(text)) {
            const highlightedText = text.replace(regex, '<mark>$1</mark>');
            const span = document.createElement('span');
            span.innerHTML = highlightedText;
            textNode.parentNode.replaceChild(span, textNode);
        }
    });
}

function clearHighlights() {
    const highlights = document.querySelectorAll('mark');
    highlights.forEach(function(mark) {
        const parent = mark.parentNode;
        parent.replaceChild(document.createTextNode(mark.textContent), mark);
        parent.normalize();
    });
}

function makeTablesResponsive() {
    const tables = document.querySelectorAll('table');
    
    tables.forEach(function(table) {
        const wrapper = document.createElement('div');
        wrapper.className = 'table-responsive';
        wrapper.style.cssText = `
            overflow-x: auto;
            margin: 1em 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        `;
        
        table.parentNode.insertBefore(wrapper, table);
        wrapper.appendChild(table);
        
        // Add mobile-friendly styling
        if (window.innerWidth < 768) {
            table.style.fontSize = '14px';
        }
    });
}

function addSmoothScrolling() {
    // Smooth scrolling for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Add highlight effect
                targetElement.style.transition = 'background-color 0.5s ease';
                targetElement.style.backgroundColor = '#E8F4FD';
                setTimeout(function() {
                    targetElement.style.backgroundColor = '';
                }, 2000);
            }
        });
    });
}

// Add progress indicators for test execution
function addProgressIndicators() {
    const progressBars = document.querySelectorAll('.progress-bar');
    
    progressBars.forEach(function(bar) {
        const fill = bar.querySelector('.progress-fill');
        const percentage = fill.getAttribute('data-percentage') || '0';
        
        // Animate progress bar
        setTimeout(function() {
            fill.style.width = percentage + '%';
        }, 500);
    });
}

// Add interactive command examples
function addInteractiveCommands() {
    const commandExamples = document.querySelectorAll('.command-example');
    
    commandExamples.forEach(function(example) {
        example.addEventListener('click', function() {
            // Copy command to clipboard
            const command = this.textContent.replace('$ ', '');
            navigator.clipboard.writeText(command).then(function() {
                example.style.backgroundColor = '#27AE60';
                setTimeout(function() {
                    example.style.backgroundColor = '';
                }, 1000);
            });
        });
        
        example.style.cursor = 'pointer';
        example.title = 'Click to copy command';
    });
}

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + K for search
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.querySelector('input[type="search"]');
        if (searchInput) {
            searchInput.focus();
        }
    }
    
    // Escape to clear search
    if (e.key === 'Escape') {
        clearHighlights();
        const searchInput = document.querySelector('input[type="search"]');
        if (searchInput) {
            searchInput.value = '';
            searchInput.blur();
        }
    }
});

// Add theme toggle (if dark mode is supported)
function addThemeToggle() {
    const toggle = document.createElement('button');
    toggle.innerHTML = '🌙';
    toggle.className = 'theme-toggle';
    toggle.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #2980B9;
        color: white;
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        cursor: pointer;
        font-size: 20px;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
    `;
    
    toggle.addEventListener('click', function() {
        document.body.classList.toggle('dark-theme');
        this.innerHTML = document.body.classList.contains('dark-theme') ? '☀️' : '🌙';
    });
    
    document.body.appendChild(toggle);
}

// Initialize additional features
document.addEventListener('DOMContentLoaded', function() {
    addProgressIndicators();
    addInteractiveCommands();
    addThemeToggle();
});

// Add performance monitoring
if ('performance' in window) {
    window.addEventListener('load', function() {
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        console.log(`📊 Documentation loaded in ${loadTime}ms`);
        
        // Add load time indicator
        const indicator = document.createElement('div');
        indicator.innerHTML = `⚡ Loaded in ${loadTime}ms`;
        indicator.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #27AE60;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        document.body.appendChild(indicator);
        setTimeout(() => indicator.style.opacity = '1', 100);
        setTimeout(() => indicator.style.opacity = '0', 3000);
        setTimeout(() => document.body.removeChild(indicator), 3300);
    });
}
