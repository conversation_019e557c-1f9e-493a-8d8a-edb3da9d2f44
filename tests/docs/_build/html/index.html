<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>TurdParty Testing Framework Documentation &mdash; TurdParty Testing Framework v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=67647e4a" />
      <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=67647e4a" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=8d563738"></script>
        <script src="_static/doctools.js?v=9a2dae69"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/custom.js?v=3a49a312"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Quick Start Guide" href="quickstart.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="#" class="icon icon-home">
            TurdParty Testing Framework
          </a>
              <div class="version">
                1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Quick Start</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="quickstart.html">Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation &amp; Setup</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Core Testing</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="unit-testing.html">Unit Testing</a></li>
<li class="toctree-l1"><a class="reference internal" href="property-testing.html">Property-Based Testing</a></li>
<li class="toctree-l1"><a class="reference internal" href="performance-testing.html">Performance Testing</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="#">TurdParty Testing Framework</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="#" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">TurdParty Testing Framework Documentation</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/tenbahtsecurity/turdparty/blob/master/tests/docs/index.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="turdparty-testing-framework-documentation">
<h1>TurdParty Testing Framework Documentation<a class="headerlink" href="#turdparty-testing-framework-documentation" title="Link to this heading"></a></h1>
<a class="reference external image-reference" href="https://www.python.org/downloads/"><img alt="Python Version" src="https://img.shields.io/badge/Python-3.11+-blue.svg" />
</a>
<a class="reference external image-reference" href="https://pytest.org/"><img alt="Testing Framework" src="https://img.shields.io/badge/Testing-pytest-green.svg" />
</a>
<a class="reference external image-reference" href="https://hypothesis.readthedocs.io/"><img alt="Property-Based Testing" src="https://img.shields.io/badge/Property--Based-Hypothesis-orange.svg" />
</a>
<a class="reference external image-reference" href="https://pytest-benchmark.readthedocs.io/"><img alt="Performance Testing" src="https://img.shields.io/badge/Performance-pytest--benchmark-red.svg" />
</a>
<a class="reference external image-reference" href="https://locust.io/"><img alt="Load Testing" src="https://img.shields.io/badge/Load--Testing-Locust-purple.svg" />
</a>
<a class="reference external image-reference" href="https://github.com/astral-sh/ruff"><img alt="Code Quality" src="https://img.shields.io/badge/Code--Quality-Ruff-yellow.svg" />
</a>
<p>Welcome to the <strong>TurdParty Testing Framework</strong> documentation! This comprehensive guide covers our modern, industry-standard testing infrastructure designed for reliability, performance, and maintainability.</p>
<dl class="simple">
<dt>🎯 <strong>Quick Stats</strong></dt><dd><ul class="simple">
<li><p><strong>36 Unit Tests</strong> - Fast, isolated component validation</p></li>
<li><p><strong>9 Property Tests</strong> - Hypothesis-driven edge case discovery</p></li>
<li><p><strong>1.54M ops/sec</strong> - Model creation performance benchmark</p></li>
<li><p><strong>90% Issue Reduction</strong> - Code quality improvements with Ruff</p></li>
<li><p><strong>Zero Warnings</strong> - Clean, modern Python codebase</p></li>
</ul>
</dd>
</dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This documentation covers the <strong>modern testing stack</strong> implemented in 2024, featuring industry-standard tools and best practices.</p>
</div>
</section>
<section id="getting-started">
<h1>Getting Started<a class="headerlink" href="#getting-started" title="Link to this heading"></a></h1>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Quick Start</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="quickstart.html">Quick Start Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#installation">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#running-your-first-tests">Running Your First Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#using-the-modern-test-runner">Using the Modern Test Runner</a></li>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#code-quality-checks">Code Quality Checks</a></li>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#common-workflows">Common Workflows</a></li>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#configuration-examples">Configuration Examples</a></li>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#next-steps">Next Steps</a></li>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation &amp; Setup</a><ul>
<li class="toctree-l2"><a class="reference internal" href="installation.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#installation-methods">Installation Methods</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#configuration">Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#verification">Verification</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#development-environment">Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#docker-services-optional">Docker Services (Optional)</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#getting-help">Getting Help</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="testing-framework">
<h1>Testing Framework<a class="headerlink" href="#testing-framework" title="Link to this heading"></a></h1>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Core Testing</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="unit-testing.html">Unit Testing</a><ul>
<li class="toctree-l2"><a class="reference internal" href="unit-testing.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="unit-testing.html#test-structure">Test Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="unit-testing.html#basic-functionality-tests">Basic Functionality Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="unit-testing.html#model-validation-tests">Model Validation Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="unit-testing.html#running-unit-tests">Running Unit Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="unit-testing.html#test-markers">Test Markers</a></li>
<li class="toctree-l2"><a class="reference internal" href="unit-testing.html#fixtures-and-test-data">Fixtures and Test Data</a></li>
<li class="toctree-l2"><a class="reference internal" href="unit-testing.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="unit-testing.html#performance-considerations">Performance Considerations</a></li>
<li class="toctree-l2"><a class="reference internal" href="unit-testing.html#continuous-integration">Continuous Integration</a></li>
<li class="toctree-l2"><a class="reference internal" href="unit-testing.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="property-testing.html">Property-Based Testing</a><ul>
<li class="toctree-l2"><a class="reference internal" href="property-testing.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="property-testing.html#what-is-property-based-testing">What is Property-Based Testing?</a></li>
<li class="toctree-l2"><a class="reference internal" href="property-testing.html#our-property-based-tests">Our Property-Based Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="property-testing.html#hypothesis-strategies">Hypothesis Strategies</a></li>
<li class="toctree-l2"><a class="reference internal" href="property-testing.html#advanced-hypothesis-features">Advanced Hypothesis Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="property-testing.html#running-property-based-tests">Running Property-Based Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="property-testing.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="property-testing.html#integration-with-ci-cd">Integration with CI/CD</a></li>
<li class="toctree-l2"><a class="reference internal" href="property-testing.html#next-steps">Next Steps</a></li>
<li class="toctree-l2"><a class="reference internal" href="property-testing.html#resources">Resources</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="performance-testing.html">Performance Testing</a><ul>
<li class="toctree-l2"><a class="reference internal" href="performance-testing.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance-testing.html#current-performance-metrics">Current Performance Metrics</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance-testing.html#micro-benchmarking-with-pytest-benchmark">Micro-Benchmarking with pytest-benchmark</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance-testing.html#running-performance-tests">Running Performance Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance-testing.html#load-testing-with-locust">Load Testing with Locust</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance-testing.html#memory-profiling">Memory Profiling</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance-testing.html#performance-regression-testing">Performance Regression Testing</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance-testing.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="performance-testing.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="tools-configuration">
<h1>Tools &amp; Configuration<a class="headerlink" href="#tools-configuration" title="Link to this heading"></a></h1>
<div class="toctree-wrapper compound">
</div>
</section>
<section id="advanced-topics">
<h1>Advanced Topics<a class="headerlink" href="#advanced-topics" title="Link to this heading"></a></h1>
<div class="toctree-wrapper compound">
</div>
</section>
<section id="api-reference">
<h1>API Reference<a class="headerlink" href="#api-reference" title="Link to this heading"></a></h1>
<div class="toctree-wrapper compound">
</div>
</section>
<section id="examples-tutorials">
<h1>Examples &amp; Tutorials<a class="headerlink" href="#examples-tutorials" title="Link to this heading"></a></h1>
<div class="toctree-wrapper compound">
</div>
</section>
<section id="development">
<h1>Development<a class="headerlink" href="#development" title="Link to this heading"></a></h1>
<div class="toctree-wrapper compound">
</div>
</section>
<section id="architecture-overview">
<h1>Architecture Overview<a class="headerlink" href="#architecture-overview" title="Link to this heading"></a></h1>
<p>Our testing framework is built on modern Python testing tools:</p>
</section>
<section id="key-features">
<h1>Key Features<a class="headerlink" href="#key-features" title="Link to this heading"></a></h1>
<dl class="simple">
<dt>🚀 <strong>Modern Tools</strong></dt><dd><ul class="simple">
<li><p><strong>pytest</strong> - Industry-standard test runner</p></li>
<li><p><strong>Hypothesis</strong> - Property-based testing for edge cases</p></li>
<li><p><strong>pytest-benchmark</strong> - Performance regression testing</p></li>
<li><p><strong>Locust</strong> - Realistic load testing scenarios</p></li>
<li><p><strong>Ruff</strong> - Fast Python linting and formatting</p></li>
<li><p><strong>MyPy</strong> - Static type checking</p></li>
<li><p><strong>Bandit</strong> - Security vulnerability scanning</p></li>
</ul>
</dd>
<dt>🧪 <strong>Comprehensive Testing</strong></dt><dd><ul class="simple">
<li><p><strong>Unit Tests</strong> - Fast feedback on component functionality</p></li>
<li><p><strong>Property Tests</strong> - Automated edge case discovery</p></li>
<li><p><strong>Performance Tests</strong> - Benchmark and regression validation</p></li>
<li><p><strong>Security Tests</strong> - Vulnerability and attack vector testing</p></li>
<li><p><strong>Integration Tests</strong> - Service interaction validation</p></li>
<li><p><strong>Load Tests</strong> - Real-world usage simulation</p></li>
</ul>
</dd>
<dt>⚡ <strong>Performance Optimized</strong></dt><dd><ul class="simple">
<li><p><strong>Fast Execution</strong> - 36 tests in 0.06 seconds</p></li>
<li><p><strong>Parallel Testing</strong> - Multi-worker test execution</p></li>
<li><p><strong>Efficient Fixtures</strong> - Shared test setup and teardown</p></li>
<li><p><strong>Smart Caching</strong> - Pytest cache for faster reruns</p></li>
</ul>
</dd>
<dt>🛡️ <strong>Quality Assured</strong></dt><dd><ul class="simple">
<li><p><strong>90% Issue Reduction</strong> - Comprehensive code quality improvements</p></li>
<li><p><strong>Zero Warnings</strong> - Clean, modern Python patterns</p></li>
<li><p><strong>Type Safety</strong> - Full PEP 484 compliance</p></li>
<li><p><strong>Security Hardened</strong> - Vulnerability scanning and validation</p></li>
</ul>
</dd>
</dl>
</section>
<section id="test-results-dashboard">
<h1>Test Results Dashboard<a class="headerlink" href="#test-results-dashboard" title="Link to this heading"></a></h1>
<table class="docutils align-default" id="id1">
<caption><span class="caption-text">Current Test Status</span><a class="headerlink" href="#id1" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 25.0%" />
<col style="width: 15.0%" />
<col style="width: 15.0%" />
<col style="width: 25.0%" />
<col style="width: 20.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Test Type</p></th>
<th class="head"><p>Count</p></th>
<th class="head"><p>Status</p></th>
<th class="head"><p>Performance</p></th>
<th class="head"><p>Coverage</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Unit Tests</p></td>
<td><p>36</p></td>
<td><p>✅ PASSING</p></td>
<td><p>0.06s</p></td>
<td><p>95%+</p></td>
</tr>
<tr class="row-odd"><td><p>Property Tests</p></td>
<td><p>9</p></td>
<td><p>✅ PASSING</p></td>
<td><p>1.02s</p></td>
<td><p>Edge Cases</p></td>
</tr>
<tr class="row-even"><td><p>Performance Tests</p></td>
<td><p>Benchmarks</p></td>
<td><p>✅ WORKING</p></td>
<td><p>1.54M ops/s</p></td>
<td><p>Regression</p></td>
</tr>
<tr class="row-odd"><td><p>Security Tests</p></td>
<td><p>Multiple</p></td>
<td><p>✅ CONFIGURED</p></td>
<td><p>Scanning</p></td>
<td><p>Vulnerabilities</p></td>
</tr>
<tr class="row-even"><td><p>Code Quality</p></td>
<td><p>Ruff</p></td>
<td><p>✅ CLEAN</p></td>
<td><p>Fast</p></td>
<td><p>90% Fixed</p></td>
</tr>
</tbody>
</table>
</section>
<section id="quick-commands">
<h1>Quick Commands<a class="headerlink" href="#quick-commands" title="Link to this heading"></a></h1>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run all unit tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/<span class="w"> </span>-v

<span class="c1"># Run property-based tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/property/<span class="w"> </span>-v

<span class="c1"># Run performance benchmarks</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/performance/<span class="w"> </span>--benchmark-only

<span class="c1"># Run with coverage</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/<span class="w"> </span>--cov<span class="o">=</span>api<span class="w"> </span>--cov<span class="o">=</span>services<span class="w"> </span>--cov-report<span class="o">=</span>html

<span class="c1"># Use modern test runner</span>
./scripts/test_runner.sh<span class="w"> </span><span class="nb">help</span>
./scripts/test_runner.sh<span class="w"> </span>all

<span class="c1"># Code quality checks</span>
ruff<span class="w"> </span>check<span class="w"> </span>.<span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>ruff<span class="w"> </span>format<span class="w"> </span>.
pre-commit<span class="w"> </span>run<span class="w"> </span>--all-files
</pre></div>
</div>
</section>
<section id="support-contributing">
<h1>Support &amp; Contributing<a class="headerlink" href="#support-contributing" title="Link to this heading"></a></h1>
<div class="tip admonition">
<p class="admonition-title">Need Help?</p>
<ul class="simple">
<li><p>📖 Check the documentation sections above</p></li>
<li><p>🐛 Report issues on GitHub</p></li>
<li><p>💬 Join our development discussions</p></li>
<li><p>🤝 Contribute improvements and fixes</p></li>
</ul>
</div>
<div class="note admonition">
<p class="admonition-title">Contributing</p>
<p>We welcome contributions! Please see our <span class="xref std std-doc">contributing</span> guide for details on:</p>
<ul class="simple">
<li><p>Setting up the development environment</p></li>
<li><p>Writing and running tests</p></li>
<li><p>Code quality standards</p></li>
<li><p>Submitting pull requests</p></li>
</ul>
</div>
</section>
<section id="indices-and-tables">
<h1>Indices and tables<a class="headerlink" href="#indices-and-tables" title="Link to this heading"></a></h1>
<ul class="simple">
<li><p><a class="reference internal" href="genindex.html"><span class="std std-ref">Index</span></a></p></li>
<li><p><a class="reference internal" href="py-modindex.html"><span class="std std-ref">Module Index</span></a></p></li>
<li><p><a class="reference internal" href="search.html"><span class="std std-ref">Search Page</span></a></p></li>
</ul>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="quickstart.html" class="btn btn-neutral float-right" title="Quick Start Guide" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, TurdParty Team.
      <span class="lastupdated">Last updated on Jan 01, 1980.
      </span></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>