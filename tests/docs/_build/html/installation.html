<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Installation &amp; Setup &mdash; TurdParty Testing Framework v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=67647e4a" />
      <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=67647e4a" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=8d563738"></script>
        <script src="_static/doctools.js?v=9a2dae69"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/custom.js?v=3a49a312"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Unit Testing" href="unit-testing.html" />
    <link rel="prev" title="Quick Start Guide" href="quickstart.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            TurdParty Testing Framework
          </a>
              <div class="version">
                1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Quick Start</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="quickstart.html">Quick Start Guide</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Installation &amp; Setup</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#system-requirements">System Requirements</a></li>
<li class="toctree-l3"><a class="reference internal" href="#supported-platforms">Supported Platforms</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#installation-methods">Installation Methods</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#method-1-nix-development-environment-recommended">Method 1: Nix Development Environment (Recommended)</a></li>
<li class="toctree-l3"><a class="reference internal" href="#method-2-pip-installation">Method 2: pip Installation</a></li>
<li class="toctree-l3"><a class="reference internal" href="#method-3-development-dependencies-only">Method 3: Development Dependencies Only</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#configuration">Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#environment-setup">Environment Setup</a></li>
<li class="toctree-l3"><a class="reference internal" href="#pytest-configuration">pytest Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#pre-commit-hooks-setup">Pre-commit Hooks Setup</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#verification">Verification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#test-installation">Test Installation</a></li>
<li class="toctree-l3"><a class="reference internal" href="#test-all-components">Test All Components</a></li>
<li class="toctree-l3"><a class="reference internal" href="#tool-verification">Tool Verification</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#development-environment">Development Environment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#ide-configuration">IDE Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#shell-aliases">Shell Aliases</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#docker-services-optional">Docker Services (Optional)</a></li>
<li class="toctree-l2"><a class="reference internal" href="#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="#platform-specific-notes">Platform-Specific Notes</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#getting-help">Getting Help</a></li>
<li class="toctree-l2"><a class="reference internal" href="#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Core Testing</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="unit-testing.html">Unit Testing</a></li>
<li class="toctree-l1"><a class="reference internal" href="property-testing.html">Property-Based Testing</a></li>
<li class="toctree-l1"><a class="reference internal" href="performance-testing.html">Performance Testing</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">TurdParty Testing Framework</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Installation &amp; Setup</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/tenbahtsecurity/turdparty/blob/master/tests/docs/installation.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="installation-setup">
<h1>Installation &amp; Setup<a class="headerlink" href="#installation-setup" title="Link to this heading"></a></h1>
<p>This guide covers the installation and setup of the TurdParty testing framework, including all dependencies and development tools.</p>
<section id="prerequisites">
<h2>Prerequisites<a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h2>
<section id="system-requirements">
<h3>System Requirements<a class="headerlink" href="#system-requirements" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Python 3.11+</strong> - Modern Python with latest features</p></li>
<li><p><strong>Git</strong> - Version control system</p></li>
<li><p><strong>Docker</strong> (optional) - For integration tests and services</p></li>
<li><p><strong>Nix</strong> (recommended) - For reproducible development environment</p></li>
</ul>
</section>
<section id="supported-platforms">
<h3>Supported Platforms<a class="headerlink" href="#supported-platforms" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Linux</strong> - Primary development platform (Ubuntu 20.04+, Debian 11+)</p></li>
<li><p><strong>macOS</strong> - Full support (macOS 11+)</p></li>
<li><p><strong>Windows</strong> - WSL2 recommended for best experience</p></li>
</ul>
</section>
</section>
<section id="installation-methods">
<h2>Installation Methods<a class="headerlink" href="#installation-methods" title="Link to this heading"></a></h2>
<section id="method-1-nix-development-environment-recommended">
<h3>Method 1: Nix Development Environment (Recommended)<a class="headerlink" href="#method-1-nix-development-environment-recommended" title="Link to this heading"></a></h3>
<p>The Nix development environment provides all tools and dependencies in a reproducible manner.</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Clone the repository</span>
git<span class="w"> </span>clone<span class="w"> </span>**************:tenbahtsecurity/turdparty.git
<span class="nb">cd</span><span class="w"> </span>turdparty

<span class="c1"># Enter Nix development shell</span>
nix-shell

<span class="c1"># All testing tools are now available:</span>
python<span class="w"> </span>--version<span class="w">    </span><span class="c1"># Python 3.11+</span>
pytest<span class="w"> </span>--version<span class="w">    </span><span class="c1"># pytest 7.4+</span>
ruff<span class="w"> </span>--version<span class="w">      </span><span class="c1"># ruff 0.1.6+</span>
mypy<span class="w"> </span>--version<span class="w">      </span><span class="c1"># mypy 1.7+</span>
</pre></div>
</div>
<p><strong>Available Tools in Nix Shell:</strong></p>
<table class="docutils align-default" id="id1">
<caption><span class="caption-text">Nix Environment Tools</span><a class="headerlink" href="#id1" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 25.0%" />
<col style="width: 25.0%" />
<col style="width: 50.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Tool</p></th>
<th class="head"><p>Version</p></th>
<th class="head"><p>Purpose</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>pytest</p></td>
<td><p>7.4+</p></td>
<td><p>Test execution framework</p></td>
</tr>
<tr class="row-odd"><td><p>hypothesis</p></td>
<td><p>6.92+</p></td>
<td><p>Property-based testing</p></td>
</tr>
<tr class="row-even"><td><p>pytest-benchmark</p></td>
<td><p>4.0+</p></td>
<td><p>Performance benchmarking</p></td>
</tr>
<tr class="row-odd"><td><p>ruff</p></td>
<td><p>0.1.6+</p></td>
<td><p>Fast Python linting</p></td>
</tr>
<tr class="row-even"><td><p>mypy</p></td>
<td><p>1.7+</p></td>
<td><p>Static type checking</p></td>
</tr>
<tr class="row-odd"><td><p>bandit</p></td>
<td><p>1.7.5+</p></td>
<td><p>Security scanning</p></td>
</tr>
<tr class="row-even"><td><p>pre-commit</p></td>
<td><p>3.6+</p></td>
<td><p>Git hooks</p></td>
</tr>
</tbody>
</table>
</section>
<section id="method-2-pip-installation">
<h3>Method 2: pip Installation<a class="headerlink" href="#method-2-pip-installation" title="Link to this heading"></a></h3>
<p>Install using Python’s package manager:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Clone the repository</span>
git<span class="w"> </span>clone<span class="w"> </span>**************:tenbahtsecurity/turdparty.git
<span class="nb">cd</span><span class="w"> </span>turdparty

<span class="c1"># Create virtual environment</span>
python<span class="w"> </span>-m<span class="w"> </span>venv<span class="w"> </span>venv
<span class="nb">source</span><span class="w"> </span>venv/bin/activate<span class="w">  </span><span class="c1"># On Windows: venv\Scripts\activate</span>

<span class="c1"># Install with development dependencies</span>
pip<span class="w"> </span>install<span class="w"> </span>-e<span class="w"> </span><span class="s2">&quot;.[dev]&quot;</span>

<span class="c1"># Verify installation</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>--version
ruff<span class="w"> </span>--version
</pre></div>
</div>
</section>
<section id="method-3-development-dependencies-only">
<h3>Method 3: Development Dependencies Only<a class="headerlink" href="#method-3-development-dependencies-only" title="Link to this heading"></a></h3>
<p>Install just the testing tools without the main application:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Install core testing tools</span>
pip<span class="w"> </span>install<span class="w"> </span>pytest<span class="w"> </span>pytest-asyncio<span class="w"> </span>pytest-cov<span class="w"> </span>pytest-benchmark
pip<span class="w"> </span>install<span class="w"> </span>hypothesis<span class="w"> </span>factory-boy<span class="w"> </span>faker
pip<span class="w"> </span>install<span class="w"> </span>ruff<span class="w"> </span>mypy<span class="w"> </span>bandit<span class="w"> </span>safety
pip<span class="w"> </span>install<span class="w"> </span>locust<span class="w"> </span>pre-commit

<span class="c1"># Install documentation tools</span>
pip<span class="w"> </span>install<span class="w"> </span>sphinx<span class="w"> </span>sphinx-rtd-theme<span class="w"> </span>myst-parser
</pre></div>
</div>
</section>
</section>
<section id="configuration">
<h2>Configuration<a class="headerlink" href="#configuration" title="Link to this heading"></a></h2>
<section id="environment-setup">
<h3>Environment Setup<a class="headerlink" href="#environment-setup" title="Link to this heading"></a></h3>
<p>Create a <code class="docutils literal notranslate"><span class="pre">.env</span></code> file for local development:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Development environment variables</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">DEVELOPMENT</span><span class="o">=</span><span class="nb">true</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">DEBUG</span><span class="o">=</span><span class="nb">true</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">LOG_LEVEL</span><span class="o">=</span>DEBUG
<span class="nb">export</span><span class="w"> </span><span class="nv">TURDPARTY_ENV</span><span class="o">=</span>development

<span class="c1"># Testing configuration</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">TEST_MODE</span><span class="o">=</span><span class="nb">false</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">COVERAGE_THRESHOLD</span><span class="o">=</span><span class="m">80</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">PARALLEL_WORKERS</span><span class="o">=</span><span class="m">4</span>

<span class="c1"># Service endpoints (for integration tests)</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">API_BASE_URL</span><span class="o">=</span>http://localhost:8000
<span class="nb">export</span><span class="w"> </span><span class="nv">DATABASE_URL</span><span class="o">=</span>postgresql://test:test@localhost/test_db
<span class="nb">export</span><span class="w"> </span><span class="nv">REDIS_URL</span><span class="o">=</span>redis://localhost:6379/1
</pre></div>
</div>
</section>
<section id="pytest-configuration">
<h3>pytest Configuration<a class="headerlink" href="#pytest-configuration" title="Link to this heading"></a></h3>
<p>The testing framework is configured via <code class="docutils literal notranslate"><span class="pre">pyproject.toml</span></code>:</p>
<div class="highlight-toml notranslate"><div class="highlight"><pre><span></span><span class="k">[tool.pytest.ini_options]</span>
<span class="n">minversion</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s2">&quot;7.0&quot;</span>
<span class="n">testpaths</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;tests&quot;</span><span class="p">]</span>
<span class="n">markers</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;unit: Unit tests&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;integration: Integration tests&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;performance: Performance tests&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;security: Security tests&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;property: Property-based tests&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;benchmark: Benchmark tests&quot;</span><span class="p">,</span>
<span class="p">]</span>
<span class="n">filterwarnings</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;error&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;ignore::UserWarning&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;ignore::DeprecationWarning&quot;</span><span class="p">,</span>
<span class="p">]</span>
</pre></div>
</div>
</section>
<section id="pre-commit-hooks-setup">
<h3>Pre-commit Hooks Setup<a class="headerlink" href="#pre-commit-hooks-setup" title="Link to this heading"></a></h3>
<p>Install and configure pre-commit hooks:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Install pre-commit hooks</span>
pre-commit<span class="w"> </span>install

<span class="c1"># Run hooks manually (optional)</span>
pre-commit<span class="w"> </span>run<span class="w"> </span>--all-files

<span class="c1"># Update hooks to latest versions</span>
pre-commit<span class="w"> </span>autoupdate
</pre></div>
</div>
</section>
</section>
<section id="verification">
<h2>Verification<a class="headerlink" href="#verification" title="Link to this heading"></a></h2>
<section id="test-installation">
<h3>Test Installation<a class="headerlink" href="#test-installation" title="Link to this heading"></a></h3>
<p>Verify your installation by running the test suite:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run basic unit tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/test_basic.py<span class="w"> </span>-v

<span class="c1"># Expected output:</span>
<span class="c1"># ======== test session starts ========</span>
<span class="c1"># tests/unit/test_basic.py::TestBasicFunctionality::test_python_environment PASSED</span>
<span class="c1"># tests/unit/test_basic.py::TestBasicFunctionality::test_file_operations PASSED</span>
<span class="c1"># ...</span>
<span class="c1"># ======== 12 passed in 0.03s ========</span>
</pre></div>
</div>
</section>
<section id="test-all-components">
<h3>Test All Components<a class="headerlink" href="#test-all-components" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Test all unit tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/<span class="w"> </span>-v

<span class="c1"># Test property-based tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/property/<span class="w"> </span>-v

<span class="c1"># Test performance benchmarks</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/performance/test_benchmarks.py::TestModelPerformance::test_file_injection_create_performance<span class="w"> </span>--benchmark-only

<span class="c1"># Test code quality</span>
ruff<span class="w"> </span>check<span class="w"> </span>tests/unit/test_basic.py
mypy<span class="w"> </span>api/<span class="w"> </span>services/
</pre></div>
</div>
</section>
<section id="tool-verification">
<h3>Tool Verification<a class="headerlink" href="#tool-verification" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Verify all tools are working</span>
./scripts/test_runner.sh<span class="w"> </span><span class="nb">help</span>

<span class="c1"># Expected output:</span>
<span class="c1"># TurdParty Modern Test Runner</span>
<span class="c1">#</span>
<span class="c1"># Usage: ./scripts/test_runner.sh [COMMAND] [OPTIONS]</span>
<span class="c1">#</span>
<span class="c1"># Commands:</span>
<span class="c1">#   unit         Run unit tests with coverage</span>
<span class="c1">#   property     Run property-based tests (Hypothesis)</span>
<span class="c1">#   performance  Run performance benchmarks</span>
<span class="c1">#   ...</span>
</pre></div>
</div>
</section>
</section>
<section id="development-environment">
<h2>Development Environment<a class="headerlink" href="#development-environment" title="Link to this heading"></a></h2>
<section id="ide-configuration">
<h3>IDE Configuration<a class="headerlink" href="#ide-configuration" title="Link to this heading"></a></h3>
<p><strong>VS Code Settings</strong> (<cite>.vscode/settings.json</cite>):</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;python.defaultInterpreterPath&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;./venv/bin/python&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;python.testing.pytestEnabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;python.testing.pytestArgs&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;tests/&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;python.linting.enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;python.linting.ruffEnabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;python.formatting.provider&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ruff&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;python.typeChecking&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;mypy&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>PyCharm Configuration:</strong></p>
<ol class="arabic simple">
<li><p>Set Python interpreter to your virtual environment</p></li>
<li><p>Enable pytest as test runner</p></li>
<li><p>Configure Ruff as external tool</p></li>
<li><p>Enable MyPy type checking</p></li>
</ol>
</section>
<section id="shell-aliases">
<h3>Shell Aliases<a class="headerlink" href="#shell-aliases" title="Link to this heading"></a></h3>
<p>Add these aliases to your shell configuration:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Testing aliases</span>
<span class="nb">alias</span><span class="w"> </span>tp-test<span class="o">=</span><span class="s1">&#39;python -m pytest tests/unit/ -v&#39;</span>
<span class="nb">alias</span><span class="w"> </span>tp-test-all<span class="o">=</span><span class="s1">&#39;python -m pytest tests/ -v&#39;</span>
<span class="nb">alias</span><span class="w"> </span>tp-property<span class="o">=</span><span class="s1">&#39;python -m pytest tests/property/ -v&#39;</span>
<span class="nb">alias</span><span class="w"> </span>tp-benchmark<span class="o">=</span><span class="s1">&#39;python -m pytest tests/performance/ --benchmark-only&#39;</span>
<span class="nb">alias</span><span class="w"> </span>tp-coverage<span class="o">=</span><span class="s1">&#39;python -m pytest tests/unit/ --cov=api --cov=services --cov-report=html&#39;</span>

<span class="c1"># Code quality aliases</span>
<span class="nb">alias</span><span class="w"> </span>tp-lint<span class="o">=</span><span class="s1">&#39;ruff check . &amp;&amp; ruff format . &amp;&amp; mypy api/ services/&#39;</span>
<span class="nb">alias</span><span class="w"> </span>tp-format<span class="o">=</span><span class="s1">&#39;ruff format .&#39;</span>
<span class="nb">alias</span><span class="w"> </span>tp-security<span class="o">=</span><span class="s1">&#39;bandit -r api/ services/&#39;</span>

<span class="c1"># Development aliases</span>
<span class="nb">alias</span><span class="w"> </span>tp-up<span class="o">=</span><span class="s1">&#39;docker-compose up -d&#39;</span>
<span class="nb">alias</span><span class="w"> </span>tp-down<span class="o">=</span><span class="s1">&#39;docker-compose down&#39;</span>
<span class="nb">alias</span><span class="w"> </span>tp-logs<span class="o">=</span><span class="s1">&#39;docker-compose logs -f&#39;</span>
</pre></div>
</div>
</section>
</section>
<section id="docker-services-optional">
<h2>Docker Services (Optional)<a class="headerlink" href="#docker-services-optional" title="Link to this heading"></a></h2>
<p>For integration testing, you may need supporting services:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="c1"># docker-compose.test.yml</span>
<span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;3.8&#39;</span>
<span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">postgres</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">postgres:15</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="nt">POSTGRES_DB</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">test_db</span>
<span class="w">      </span><span class="nt">POSTGRES_USER</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">test</span>
<span class="w">      </span><span class="nt">POSTGRES_PASSWORD</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">test</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;5432:5432&quot;</span>

<span class="w">  </span><span class="nt">redis</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">redis:7-alpine</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;6379:6379&quot;</span>

<span class="w">  </span><span class="nt">elasticsearch</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">elasticsearch:8.11.0</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">discovery.type=single-node</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">xpack.security.enabled=false</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;9200:9200&quot;</span>
</pre></div>
</div>
<p>Start test services:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Start test services</span>
docker-compose<span class="w"> </span>-f<span class="w"> </span>docker-compose.test.yml<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># Run integration tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/integration/<span class="w"> </span>-v

<span class="c1"># Stop test services</span>
docker-compose<span class="w"> </span>-f<span class="w"> </span>docker-compose.test.yml<span class="w"> </span>down
</pre></div>
</div>
</section>
<section id="troubleshooting">
<h2>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<section id="common-issues">
<h3>Common Issues<a class="headerlink" href="#common-issues" title="Link to this heading"></a></h3>
<p><strong>1. Import Errors</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Fix PYTHONPATH</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">PYTHONPATH</span><span class="o">=</span><span class="s2">&quot;</span><span class="nv">$PWD</span><span class="s2">:</span><span class="nv">$PYTHONPATH</span><span class="s2">&quot;</span>

<span class="c1"># Or install in development mode</span>
pip<span class="w"> </span>install<span class="w"> </span>-e<span class="w"> </span>.
</pre></div>
</div>
<p><strong>2. Permission Errors</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Make scripts executable</span>
chmod<span class="w"> </span>+x<span class="w"> </span>scripts/test_runner.sh

<span class="c1"># Fix pre-commit permissions</span>
pre-commit<span class="w"> </span>clean
pre-commit<span class="w"> </span>install
</pre></div>
</div>
<p><strong>3. Missing Dependencies</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Reinstall all dependencies</span>
pip<span class="w"> </span>install<span class="w"> </span>-e<span class="w"> </span><span class="s2">&quot;.[dev]&quot;</span><span class="w"> </span>--force-reinstall

<span class="c1"># Or use Nix for guaranteed environment</span>
nix-shell
</pre></div>
</div>
<p><strong>4. Test Failures</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run with verbose output</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/failing_test.py<span class="w"> </span>-v<span class="w"> </span>-s<span class="w"> </span>--tb<span class="o">=</span>long

<span class="c1"># Check for environment issues</span>
python<span class="w"> </span>-c<span class="w"> </span><span class="s2">&quot;import sys; print(sys.path)&quot;</span>
</pre></div>
</div>
<p><strong>5. Performance Issues</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run tests in parallel</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/<span class="w"> </span>-n<span class="w"> </span>auto

<span class="c1"># Skip slow tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;not slow&quot;</span>
</pre></div>
</div>
</section>
<section id="platform-specific-notes">
<h3>Platform-Specific Notes<a class="headerlink" href="#platform-specific-notes" title="Link to this heading"></a></h3>
<p><strong>macOS:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Install Homebrew dependencies</span>
brew<span class="w"> </span>install<span class="w"> </span>python@3.11<span class="w"> </span>git<span class="w"> </span>docker

<span class="c1"># Use pyenv for Python version management</span>
brew<span class="w"> </span>install<span class="w"> </span>pyenv
pyenv<span class="w"> </span>install<span class="w"> </span><span class="m">3</span>.11.6
pyenv<span class="w"> </span>global<span class="w"> </span><span class="m">3</span>.11.6
</pre></div>
</div>
<p><strong>Windows (WSL2):</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Install Python 3.11</span>
sudo<span class="w"> </span>apt<span class="w"> </span>update
sudo<span class="w"> </span>apt<span class="w"> </span>install<span class="w"> </span>python3.11<span class="w"> </span>python3.11-venv<span class="w"> </span>python3.11-dev

<span class="c1"># Install Docker Desktop for Windows</span>
<span class="c1"># Enable WSL2 integration</span>
</pre></div>
</div>
<p><strong>Linux (Ubuntu/Debian):</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Install system dependencies</span>
sudo<span class="w"> </span>apt<span class="w"> </span>update
sudo<span class="w"> </span>apt<span class="w"> </span>install<span class="w"> </span>python3.11<span class="w"> </span>python3.11-venv<span class="w"> </span>python3.11-dev<span class="w"> </span>git

<span class="c1"># Install Docker</span>
curl<span class="w"> </span>-fsSL<span class="w"> </span>https://get.docker.com<span class="w"> </span>-o<span class="w"> </span>get-docker.sh
sudo<span class="w"> </span>sh<span class="w"> </span>get-docker.sh
</pre></div>
</div>
</section>
</section>
<section id="getting-help">
<h2>Getting Help<a class="headerlink" href="#getting-help" title="Link to this heading"></a></h2>
<p>If you encounter issues:</p>
<ol class="arabic simple">
<li><p><strong>Check the documentation</strong> - Most common issues are covered</p></li>
<li><p><strong>Review the logs</strong> - Use verbose output for debugging</p></li>
<li><p><strong>Check GitHub issues</strong> - Search for similar problems</p></li>
<li><p><strong>Ask for help</strong> - Create a new issue with details</p></li>
</ol>
<p><strong>Useful Commands for Debugging:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Show environment information</span>
python<span class="w"> </span>-c<span class="w"> </span><span class="s2">&quot;import sys; print(f&#39;Python: {sys.version}&#39;); print(f&#39;Path: {sys.path}&#39;)&quot;</span>

<span class="c1"># Show installed packages</span>
pip<span class="w"> </span>list<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>-E<span class="w"> </span><span class="s2">&quot;(pytest|hypothesis|ruff|mypy)&quot;</span>

<span class="c1"># Test basic functionality</span>
python<span class="w"> </span>-c<span class="w"> </span><span class="s2">&quot;import pytest, hypothesis, ruff; print(&#39;✅ All imports successful&#39;)&quot;</span>

<span class="c1"># Show test discovery</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>--collect-only<span class="w"> </span>tests/unit/
</pre></div>
</div>
</section>
<section id="next-steps">
<h2>Next Steps<a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>After successful installation:</p>
<ol class="arabic simple">
<li><p><strong>Run the Quick Start</strong> - <a class="reference internal" href="quickstart.html"><span class="doc">Quick Start Guide</span></a></p></li>
<li><p><strong>Explore Unit Testing</strong> - <a class="reference internal" href="unit-testing.html"><span class="doc">Unit Testing</span></a></p></li>
<li><p><strong>Try Property-Based Testing</strong> - <a class="reference internal" href="property-testing.html"><span class="doc">Property-Based Testing</span></a></p></li>
<li><p><strong>Set up your IDE</strong> - Configure your development environment</p></li>
<li><p><strong>Join the community</strong> - Contribute to the project</p></li>
</ol>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="quickstart.html" class="btn btn-neutral float-left" title="Quick Start Guide" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="unit-testing.html" class="btn btn-neutral float-right" title="Unit Testing" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, TurdParty Team.
      <span class="lastupdated">Last updated on Jan 01, 1980.
      </span></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>