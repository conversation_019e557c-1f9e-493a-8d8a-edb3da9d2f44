<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Performance Testing &mdash; TurdParty Testing Framework v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=67647e4a" />
      <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=67647e4a" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=8d563738"></script>
        <script src="_static/doctools.js?v=9a2dae69"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/custom.js?v=3a49a312"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="prev" title="Property-Based Testing" href="property-testing.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            TurdParty Testing Framework
          </a>
              <div class="version">
                1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Quick Start</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="quickstart.html">Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation &amp; Setup</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Core Testing</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="unit-testing.html">Unit Testing</a></li>
<li class="toctree-l1"><a class="reference internal" href="property-testing.html">Property-Based Testing</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Performance Testing</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#current-performance-metrics">Current Performance Metrics</a></li>
<li class="toctree-l2"><a class="reference internal" href="#micro-benchmarking-with-pytest-benchmark">Micro-Benchmarking with pytest-benchmark</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#test-structure">Test Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="#model-performance-testing">Model Performance Testing</a></li>
<li class="toctree-l3"><a class="reference internal" href="#parameterized-performance-tests">Parameterized Performance Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="#complex-performance-scenarios">Complex Performance Scenarios</a></li>
<li class="toctree-l3"><a class="reference internal" href="#async-performance-testing">Async Performance Testing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#running-performance-tests">Running Performance Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#basic-execution">Basic Execution</a></li>
<li class="toctree-l3"><a class="reference internal" href="#benchmark-comparison">Benchmark Comparison</a></li>
<li class="toctree-l3"><a class="reference internal" href="#output-formats">Output Formats</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#load-testing-with-locust">Load Testing with Locust</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#locust-configuration">Locust Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#user-scenarios">User Scenarios</a></li>
<li class="toctree-l3"><a class="reference internal" href="#running-load-tests">Running Load Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="#advanced-load-testing">Advanced Load Testing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#memory-profiling">Memory Profiling</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#memory-usage-testing">Memory Usage Testing</a></li>
<li class="toctree-l3"><a class="reference internal" href="#memory-monitoring">Memory Monitoring</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#performance-regression-testing">Performance Regression Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#continuous-performance-monitoring">Continuous Performance Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance-alerts">Performance Alerts</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#benchmark-design">Benchmark Design</a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance-thresholds">Performance Thresholds</a></li>
<li class="toctree-l3"><a class="reference internal" href="#load-test-strategy">Load Test Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="#continuous-monitoring">Continuous Monitoring</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">TurdParty Testing Framework</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Performance Testing</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/tenbahtsecurity/turdparty/blob/master/tests/docs/performance-testing.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="performance-testing">
<h1>Performance Testing<a class="headerlink" href="#performance-testing" title="Link to this heading"></a></h1>
<p>Performance testing ensures that TurdParty maintains excellent performance characteristics under various conditions. Our performance testing framework uses pytest-benchmark for micro-benchmarks and Locust for load testing, providing comprehensive performance validation.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>Our performance testing strategy includes:</p>
<ul class="simple">
<li><p><strong>Micro-benchmarks</strong> - Individual function and method performance</p></li>
<li><p><strong>Regression testing</strong> - Ensuring performance doesn’t degrade over time</p></li>
<li><p><strong>Load testing</strong> - System behavior under realistic user loads</p></li>
<li><p><strong>Stress testing</strong> - Breaking point identification</p></li>
<li><p><strong>Memory profiling</strong> - Resource usage optimization</p></li>
</ul>
</section>
<section id="current-performance-metrics">
<h2>Current Performance Metrics<a class="headerlink" href="#current-performance-metrics" title="Link to this heading"></a></h2>
<p><strong>Benchmark Results:</strong></p>
<table class="docutils align-default" id="id1">
<caption><span class="caption-text">Performance Benchmarks</span><a class="headerlink" href="#id1" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 40.0%" />
<col style="width: 20.0%" />
<col style="width: 20.0%" />
<col style="width: 20.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Operation</p></th>
<th class="head"><p>Performance</p></th>
<th class="head"><p>Min Time</p></th>
<th class="head"><p>Max Time</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Model Creation</p></td>
<td><p>1.54M ops/sec</p></td>
<td><p>0.64μs</p></td>
<td><p>2.10μs</p></td>
</tr>
<tr class="row-odd"><td><p>JSON Serialization</p></td>
<td><p>850K ops/sec</p></td>
<td><p>1.18μs</p></td>
<td><p>3.50μs</p></td>
</tr>
<tr class="row-even"><td><p>Hash Calculation (1KB)</p></td>
<td><p>45K ops/sec</p></td>
<td><p>22.3μs</p></td>
<td><p>45.7μs</p></td>
</tr>
<tr class="row-odd"><td><p>Hash Calculation (1MB)</p></td>
<td><p>45 ops/sec</p></td>
<td><p>22.1ms</p></td>
<td><p>44.8ms</p></td>
</tr>
</tbody>
</table>
</section>
<section id="micro-benchmarking-with-pytest-benchmark">
<h2>Micro-Benchmarking with pytest-benchmark<a class="headerlink" href="#micro-benchmarking-with-pytest-benchmark" title="Link to this heading"></a></h2>
<section id="test-structure">
<h3>Test Structure<a class="headerlink" href="#test-structure" title="Link to this heading"></a></h3>
<p>Our performance tests are located in <code class="docutils literal notranslate"><span class="pre">tests/performance/test_benchmarks.py</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>tests/performance/
└── test_benchmarks.py
    ├── TestModelPerformance           # Pydantic model benchmarks
    ├── TestHashingPerformance         # Cryptographic operations
    ├── TestDataProcessingPerformance  # Data manipulation
    ├── TestConcurrencyPerformance     # Async operations
    └── TestMemoryPerformance          # Memory usage patterns
</pre></div>
</div>
</section>
<section id="model-performance-testing">
<h3>Model Performance Testing<a class="headerlink" href="#model-performance-testing" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">TestModelPerformance</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Benchmark tests for Pydantic model performance.&quot;&quot;&quot;</span>

    <span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">benchmark</span>
    <span class="k">def</span> <span class="nf">test_file_injection_create_performance</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">benchmark</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Benchmark FileInjectionCreate model creation.&quot;&quot;&quot;</span>
        <span class="n">data</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s2">&quot;filename&quot;</span><span class="p">:</span> <span class="s2">&quot;performance_test.sh&quot;</span><span class="p">,</span>
            <span class="s2">&quot;target_path&quot;</span><span class="p">:</span> <span class="s2">&quot;/app/scripts/performance_test.sh&quot;</span><span class="p">,</span>
            <span class="s2">&quot;permissions&quot;</span><span class="p">:</span> <span class="s2">&quot;0755&quot;</span><span class="p">,</span>
            <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="s2">&quot;Performance benchmark test file&quot;</span>
        <span class="p">}</span>

        <span class="n">result</span> <span class="o">=</span> <span class="n">benchmark</span><span class="p">(</span><span class="n">FileInjectionCreate</span><span class="p">,</span> <span class="o">**</span><span class="n">data</span><span class="p">)</span>

        <span class="k">assert</span> <span class="n">result</span><span class="o">.</span><span class="n">filename</span> <span class="o">==</span> <span class="s2">&quot;performance_test.sh&quot;</span>
        <span class="k">assert</span> <span class="n">result</span><span class="o">.</span><span class="n">permissions</span> <span class="o">==</span> <span class="s2">&quot;0755&quot;</span>

    <span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">benchmark</span>
    <span class="k">def</span> <span class="nf">test_file_injection_serialization_performance</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">benchmark</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Benchmark FileInjectionCreate serialization.&quot;&quot;&quot;</span>
        <span class="n">model</span> <span class="o">=</span> <span class="n">FileInjectionCreate</span><span class="p">(</span>
            <span class="n">filename</span><span class="o">=</span><span class="s2">&quot;benchmark.sh&quot;</span><span class="p">,</span>
            <span class="n">target_path</span><span class="o">=</span><span class="s2">&quot;/app/benchmark.sh&quot;</span><span class="p">,</span>
            <span class="n">permissions</span><span class="o">=</span><span class="s2">&quot;0755&quot;</span><span class="p">,</span>
            <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Serialization benchmark&quot;</span>
        <span class="p">)</span>

        <span class="n">result</span> <span class="o">=</span> <span class="n">benchmark</span><span class="p">(</span><span class="n">model</span><span class="o">.</span><span class="n">model_dump_json</span><span class="p">)</span>

        <span class="c1"># Verify result is valid JSON</span>
        <span class="n">parsed</span> <span class="o">=</span> <span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="n">result</span><span class="p">)</span>
        <span class="k">assert</span> <span class="n">parsed</span><span class="p">[</span><span class="s2">&quot;filename&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;benchmark.sh&quot;</span>
</pre></div>
</div>
</section>
<section id="parameterized-performance-tests">
<h3>Parameterized Performance Tests<a class="headerlink" href="#parameterized-performance-tests" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">TestHashingPerformance</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Benchmark tests for hashing operations.&quot;&quot;&quot;</span>

    <span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">benchmark</span>
    <span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">parametrize</span><span class="p">(</span><span class="s2">&quot;size&quot;</span><span class="p">,</span> <span class="p">[</span><span class="mi">1024</span><span class="p">,</span> <span class="mi">10240</span><span class="p">,</span> <span class="mi">102400</span><span class="p">,</span> <span class="mi">1048576</span><span class="p">])</span>  <span class="c1"># 1KB to 1MB</span>
    <span class="k">def</span> <span class="nf">test_sha256_hashing_performance</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">benchmark</span><span class="p">,</span> <span class="n">size</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Benchmark SHA256 hashing for different file sizes.&quot;&quot;&quot;</span>
        <span class="n">test_data</span> <span class="o">=</span> <span class="sa">b</span><span class="s2">&quot;A&quot;</span> <span class="o">*</span> <span class="n">size</span>

        <span class="n">result</span> <span class="o">=</span> <span class="n">benchmark</span><span class="p">(</span><span class="n">hashlib</span><span class="o">.</span><span class="n">sha256</span><span class="p">,</span> <span class="n">test_data</span><span class="p">)</span>

        <span class="c1"># Verify hash is correct length</span>
        <span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">result</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">())</span> <span class="o">==</span> <span class="mi">64</span>

    <span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">benchmark</span>
    <span class="k">def</span> <span class="nf">test_hash_comparison_performance</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">benchmark</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Benchmark hash comparison operations.&quot;&quot;&quot;</span>
        <span class="n">hash1</span> <span class="o">=</span> <span class="s2">&quot;a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3&quot;</span>
        <span class="n">hash2</span> <span class="o">=</span> <span class="s2">&quot;a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3&quot;</span>

        <span class="n">result</span> <span class="o">=</span> <span class="n">benchmark</span><span class="p">(</span><span class="k">lambda</span><span class="p">:</span> <span class="n">hash1</span> <span class="o">==</span> <span class="n">hash2</span><span class="p">)</span>

        <span class="k">assert</span> <span class="n">result</span> <span class="ow">is</span> <span class="kc">True</span>
</pre></div>
</div>
</section>
<section id="complex-performance-scenarios">
<h3>Complex Performance Scenarios<a class="headerlink" href="#complex-performance-scenarios" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">TestDataProcessingPerformance</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Benchmark tests for data processing operations.&quot;&quot;&quot;</span>

    <span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">benchmark</span>
    <span class="k">def</span> <span class="nf">test_dictionary_processing_performance</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">benchmark</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Benchmark dictionary processing operations.&quot;&quot;&quot;</span>
        <span class="k">def</span> <span class="nf">process_injection_data</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">]:</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;Process injection data dictionary.&quot;&quot;&quot;</span>
            <span class="n">data</span> <span class="o">=</span> <span class="p">{</span>
                <span class="s2">&quot;id&quot;</span><span class="p">:</span> <span class="s2">&quot;benchmark-test&quot;</span><span class="p">,</span>
                <span class="s2">&quot;filename&quot;</span><span class="p">:</span> <span class="s2">&quot;test.sh&quot;</span><span class="p">,</span>
                <span class="s2">&quot;target_path&quot;</span><span class="p">:</span> <span class="s2">&quot;/app/test.sh&quot;</span><span class="p">,</span>
                <span class="s2">&quot;permissions&quot;</span><span class="p">:</span> <span class="s2">&quot;0755&quot;</span><span class="p">,</span>
                <span class="s2">&quot;status&quot;</span><span class="p">:</span> <span class="s2">&quot;pending&quot;</span><span class="p">,</span>
                <span class="s2">&quot;metadata&quot;</span><span class="p">:</span> <span class="p">{</span>
                    <span class="s2">&quot;size&quot;</span><span class="p">:</span> <span class="mi">1024</span><span class="p">,</span>
                    <span class="s2">&quot;hash&quot;</span><span class="p">:</span> <span class="s2">&quot;abc123&quot;</span><span class="p">,</span>
                    <span class="s2">&quot;created&quot;</span><span class="p">:</span> <span class="s2">&quot;2024-01-01T10:00:00Z&quot;</span>
                <span class="p">}</span>
            <span class="p">}</span>

            <span class="c1"># Simulate processing</span>
            <span class="n">processed</span> <span class="o">=</span> <span class="p">{</span>
                <span class="s2">&quot;injection_id&quot;</span><span class="p">:</span> <span class="n">data</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">],</span>
                <span class="s2">&quot;file_info&quot;</span><span class="p">:</span> <span class="p">{</span>
                    <span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="n">data</span><span class="p">[</span><span class="s2">&quot;filename&quot;</span><span class="p">],</span>
                    <span class="s2">&quot;path&quot;</span><span class="p">:</span> <span class="n">data</span><span class="p">[</span><span class="s2">&quot;target_path&quot;</span><span class="p">],</span>
                    <span class="s2">&quot;perms&quot;</span><span class="p">:</span> <span class="n">data</span><span class="p">[</span><span class="s2">&quot;permissions&quot;</span><span class="p">]</span>
                <span class="p">},</span>
                <span class="s2">&quot;status_info&quot;</span><span class="p">:</span> <span class="p">{</span>
                    <span class="s2">&quot;current&quot;</span><span class="p">:</span> <span class="n">data</span><span class="p">[</span><span class="s2">&quot;status&quot;</span><span class="p">],</span>
                    <span class="s2">&quot;size&quot;</span><span class="p">:</span> <span class="n">data</span><span class="p">[</span><span class="s2">&quot;metadata&quot;</span><span class="p">][</span><span class="s2">&quot;size&quot;</span><span class="p">],</span>
                    <span class="s2">&quot;checksum&quot;</span><span class="p">:</span> <span class="n">data</span><span class="p">[</span><span class="s2">&quot;metadata&quot;</span><span class="p">][</span><span class="s2">&quot;hash&quot;</span><span class="p">]</span>
                <span class="p">}</span>
            <span class="p">}</span>

            <span class="k">return</span> <span class="n">processed</span>

        <span class="n">result</span> <span class="o">=</span> <span class="n">benchmark</span><span class="p">(</span><span class="n">process_injection_data</span><span class="p">)</span>

        <span class="k">assert</span> <span class="n">result</span><span class="p">[</span><span class="s2">&quot;injection_id&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;benchmark-test&quot;</span>
        <span class="k">assert</span> <span class="n">result</span><span class="p">[</span><span class="s2">&quot;file_info&quot;</span><span class="p">][</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;test.sh&quot;</span>
</pre></div>
</div>
</section>
<section id="async-performance-testing">
<h3>Async Performance Testing<a class="headerlink" href="#async-performance-testing" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">TestConcurrencyPerformance</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Benchmark tests for concurrency scenarios.&quot;&quot;&quot;</span>

    <span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">benchmark</span>
    <span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">asyncio</span>
    <span class="k">async</span> <span class="k">def</span> <span class="nf">test_async_operation_performance</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">benchmark</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Benchmark async operation performance.&quot;&quot;&quot;</span>
        <span class="kn">import</span> <span class="nn">asyncio</span>

        <span class="k">async</span> <span class="k">def</span> <span class="nf">async_processing</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">]:</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;Simulate async processing.&quot;&quot;&quot;</span>
            <span class="c1"># Simulate multiple async operations</span>
            <span class="n">tasks</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">10</span><span class="p">):</span>
                <span class="k">async</span> <span class="k">def</span> <span class="nf">process_item</span><span class="p">(</span><span class="n">item_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">]:</span>
                    <span class="c1"># Simulate async work</span>
                    <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.001</span><span class="p">)</span>  <span class="c1"># 1ms delay</span>
                    <span class="k">return</span> <span class="p">{</span>
                        <span class="s2">&quot;id&quot;</span><span class="p">:</span> <span class="n">item_id</span><span class="p">,</span>
                        <span class="s2">&quot;processed&quot;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                        <span class="s2">&quot;result&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;result-</span><span class="si">{</span><span class="n">item_id</span><span class="si">}</span><span class="s2">&quot;</span>
                    <span class="p">}</span>

                <span class="n">tasks</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">process_item</span><span class="p">(</span><span class="n">i</span><span class="p">))</span>

            <span class="n">results</span> <span class="o">=</span> <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">gather</span><span class="p">(</span><span class="o">*</span><span class="n">tasks</span><span class="p">)</span>

            <span class="k">return</span> <span class="p">{</span>
                <span class="s2">&quot;processed_count&quot;</span><span class="p">:</span> <span class="nb">len</span><span class="p">(</span><span class="n">results</span><span class="p">),</span>
                <span class="s2">&quot;all_successful&quot;</span><span class="p">:</span> <span class="nb">all</span><span class="p">(</span><span class="n">r</span><span class="p">[</span><span class="s2">&quot;processed&quot;</span><span class="p">]</span> <span class="k">for</span> <span class="n">r</span> <span class="ow">in</span> <span class="n">results</span><span class="p">)</span>
            <span class="p">}</span>

        <span class="n">result</span> <span class="o">=</span> <span class="k">await</span> <span class="n">benchmark</span><span class="p">(</span><span class="n">async_processing</span><span class="p">)</span>

        <span class="k">assert</span> <span class="n">result</span><span class="p">[</span><span class="s2">&quot;processed_count&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="mi">10</span>
        <span class="k">assert</span> <span class="n">result</span><span class="p">[</span><span class="s2">&quot;all_successful&quot;</span><span class="p">]</span> <span class="ow">is</span> <span class="kc">True</span>
</pre></div>
</div>
</section>
</section>
<section id="running-performance-tests">
<h2>Running Performance Tests<a class="headerlink" href="#running-performance-tests" title="Link to this heading"></a></h2>
<section id="basic-execution">
<h3>Basic Execution<a class="headerlink" href="#basic-execution" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run all performance tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/performance/<span class="w"> </span>--benchmark-only<span class="w"> </span>-v

<span class="c1"># Run specific benchmark</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/performance/test_benchmarks.py::TestModelPerformance::test_file_injection_create_performance<span class="w"> </span>--benchmark-only<span class="w"> </span>-v

<span class="c1"># Run with detailed statistics</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/performance/<span class="w"> </span>--benchmark-only<span class="w"> </span>--benchmark-verbose
</pre></div>
</div>
</section>
<section id="benchmark-comparison">
<h3>Benchmark Comparison<a class="headerlink" href="#benchmark-comparison" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Save baseline benchmark</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/performance/<span class="w"> </span>--benchmark-only<span class="w"> </span>--benchmark-save<span class="o">=</span>baseline

<span class="c1"># Compare with baseline</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/performance/<span class="w"> </span>--benchmark-only<span class="w"> </span>--benchmark-compare<span class="o">=</span>baseline

<span class="c1"># Compare and fail if performance degrades</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/performance/<span class="w"> </span>--benchmark-only<span class="w"> </span>--benchmark-compare<span class="o">=</span>baseline<span class="w"> </span>--benchmark-compare-fail<span class="o">=</span>mean:10%
</pre></div>
</div>
</section>
<section id="output-formats">
<h3>Output Formats<a class="headerlink" href="#output-formats" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Generate JSON report</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/performance/<span class="w"> </span>--benchmark-only<span class="w"> </span>--benchmark-json<span class="o">=</span>benchmark-results.json

<span class="c1"># Generate HTML report</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/performance/<span class="w"> </span>--benchmark-only<span class="w"> </span>--benchmark-html<span class="o">=</span>benchmark-results.html

<span class="c1"># Generate histogram</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/performance/<span class="w"> </span>--benchmark-only<span class="w"> </span>--benchmark-histogram<span class="o">=</span>histogram
</pre></div>
</div>
</section>
</section>
<section id="load-testing-with-locust">
<h2>Load Testing with Locust<a class="headerlink" href="#load-testing-with-locust" title="Link to this heading"></a></h2>
<section id="locust-configuration">
<h3>Locust Configuration<a class="headerlink" href="#locust-configuration" title="Link to this heading"></a></h3>
<p>Our load tests are defined in <code class="docutils literal notranslate"><span class="pre">tests/load/locustfile.py</span></code>:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">locust</span> <span class="kn">import</span> <span class="n">HttpUser</span><span class="p">,</span> <span class="n">task</span><span class="p">,</span> <span class="n">between</span>

<span class="k">class</span> <span class="nc">FileInjectionUser</span><span class="p">(</span><span class="n">HttpUser</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Simulates a user performing file injection operations.&quot;&quot;&quot;</span>

    <span class="n">wait_time</span> <span class="o">=</span> <span class="n">between</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>  <span class="c1"># Wait 1-3 seconds between tasks</span>

    <span class="k">def</span> <span class="nf">on_start</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Called when a user starts.&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">injection_ids</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">user_id</span> <span class="o">=</span> <span class="n">random</span><span class="o">.</span><span class="n">randint</span><span class="p">(</span><span class="mi">1000</span><span class="p">,</span> <span class="mi">9999</span><span class="p">)</span>

    <span class="nd">@task</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>
    <span class="k">def</span> <span class="nf">check_health</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Check application health - most common operation.&quot;&quot;&quot;</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/health&quot;</span><span class="p">,</span> <span class="n">catch_response</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span> <span class="k">as</span> <span class="n">response</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">200</span><span class="p">:</span>
                <span class="n">data</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
                <span class="k">if</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;status&quot;</span><span class="p">)</span> <span class="o">==</span> <span class="s2">&quot;healthy&quot;</span><span class="p">:</span>
                    <span class="n">response</span><span class="o">.</span><span class="n">success</span><span class="p">()</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">response</span><span class="o">.</span><span class="n">failure</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Unhealthy status: </span><span class="si">{</span><span class="n">data</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">response</span><span class="o">.</span><span class="n">failure</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Health check failed: </span><span class="si">{</span><span class="n">response</span><span class="o">.</span><span class="n">status_code</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

    <span class="nd">@task</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
    <span class="k">def</span> <span class="nf">upload_file</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Upload a file for injection.&quot;&quot;&quot;</span>
        <span class="c1"># Generate test file content</span>
        <span class="n">file_content</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;#!/bin/bash</span><span class="se">\n</span><span class="s2">echo &#39;Test file from user </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&#39;</span><span class="se">\n</span><span class="s2">date</span><span class="se">\n</span><span class="s2">exit 0</span><span class="se">\n</span><span class="s2">&quot;</span>
        <span class="n">filename</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;test_user_</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">user_id</span><span class="si">}</span><span class="s2">_</span><span class="si">{</span><span class="nb">int</span><span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">())</span><span class="si">}</span><span class="s2">.sh&quot;</span>

        <span class="n">files</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s2">&quot;file&quot;</span><span class="p">:</span> <span class="p">(</span><span class="n">filename</span><span class="p">,</span> <span class="n">file_content</span><span class="o">.</span><span class="n">encode</span><span class="p">(),</span> <span class="s2">&quot;application/x-sh&quot;</span><span class="p">)</span>
        <span class="p">}</span>

        <span class="n">data</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s2">&quot;target_path&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;/app/scripts/</span><span class="si">{</span><span class="n">filename</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
            <span class="s2">&quot;permissions&quot;</span><span class="p">:</span> <span class="s2">&quot;0755&quot;</span><span class="p">,</span>
            <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;Load test file from user </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span>
        <span class="p">}</span>

        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
            <span class="s2">&quot;/api/v1/file-injections/&quot;</span><span class="p">,</span>
            <span class="n">files</span><span class="o">=</span><span class="n">files</span><span class="p">,</span>
            <span class="n">data</span><span class="o">=</span><span class="n">data</span><span class="p">,</span>
            <span class="n">catch_response</span><span class="o">=</span><span class="kc">True</span>
        <span class="p">)</span> <span class="k">as</span> <span class="n">response</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">201</span><span class="p">:</span>
                <span class="n">result</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
                <span class="n">injection_id</span> <span class="o">=</span> <span class="n">result</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;id&quot;</span><span class="p">)</span>
                <span class="k">if</span> <span class="n">injection_id</span><span class="p">:</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">injection_ids</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">injection_id</span><span class="p">)</span>
                    <span class="n">response</span><span class="o">.</span><span class="n">success</span><span class="p">()</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">response</span><span class="o">.</span><span class="n">failure</span><span class="p">(</span><span class="s2">&quot;No injection ID returned&quot;</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">response</span><span class="o">.</span><span class="n">failure</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Upload failed: </span><span class="si">{</span><span class="n">response</span><span class="o">.</span><span class="n">status_code</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="user-scenarios">
<h3>User Scenarios<a class="headerlink" href="#user-scenarios" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">AdminUser</span><span class="p">(</span><span class="n">HttpUser</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Simulates an admin user with different behavior patterns.&quot;&quot;&quot;</span>

    <span class="n">wait_time</span> <span class="o">=</span> <span class="n">between</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="mi">5</span><span class="p">)</span>  <span class="c1"># Admins are less frequent</span>
    <span class="n">weight</span> <span class="o">=</span> <span class="mi">1</span>  <span class="c1"># Lower weight than regular users</span>

    <span class="nd">@task</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span>
    <span class="k">def</span> <span class="nf">check_health_detailed</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Admin health check with more detailed analysis.&quot;&quot;&quot;</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/health&quot;</span><span class="p">,</span> <span class="n">catch_response</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span> <span class="k">as</span> <span class="n">response</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">200</span><span class="p">:</span>
                <span class="n">data</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
                <span class="c1"># Admins care about dependencies</span>
                <span class="k">if</span> <span class="s2">&quot;dependencies&quot;</span> <span class="ow">in</span> <span class="n">data</span><span class="p">:</span>
                    <span class="n">response</span><span class="o">.</span><span class="n">success</span><span class="p">()</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">response</span><span class="o">.</span><span class="n">failure</span><span class="p">(</span><span class="s2">&quot;Missing dependency information&quot;</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">response</span><span class="o">.</span><span class="n">failure</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Health check failed: </span><span class="si">{</span><span class="n">response</span><span class="o">.</span><span class="n">status_code</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

<span class="k">class</span> <span class="nc">HeavyUser</span><span class="p">(</span><span class="n">HttpUser</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Simulates a heavy user that uploads large files.&quot;&quot;&quot;</span>

    <span class="n">wait_time</span> <span class="o">=</span> <span class="n">between</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>  <span class="c1"># Longer wait times due to heavy operations</span>
    <span class="n">weight</span> <span class="o">=</span> <span class="mi">1</span>  <span class="c1"># Lower weight due to resource intensity</span>

    <span class="nd">@task</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
    <span class="k">def</span> <span class="nf">upload_large_file</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Upload a larger file for injection.&quot;&quot;&quot;</span>
        <span class="c1"># Generate larger test file content</span>
        <span class="n">base_content</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;#!/bin/bash</span><span class="se">\n</span><span class="s2"># Heavy load test file</span><span class="se">\n</span><span class="s2">&quot;</span>

        <span class="c1"># Add some bulk content</span>
        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">100</span><span class="p">):</span>
            <span class="n">base_content</span> <span class="o">+=</span> <span class="sa">f</span><span class="s2">&quot;echo &#39;Line </span><span class="si">{</span><span class="n">i</span><span class="si">}</span><span class="s2"> from heavy user&#39;</span><span class="se">\n</span><span class="s2">&quot;</span>

        <span class="n">base_content</span> <span class="o">+=</span> <span class="s2">&quot;exit 0</span><span class="se">\n</span><span class="s2">&quot;</span>

        <span class="n">filename</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;heavy_test_</span><span class="si">{</span><span class="nb">int</span><span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">())</span><span class="si">}</span><span class="s2">.sh&quot;</span>

        <span class="n">files</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s2">&quot;file&quot;</span><span class="p">:</span> <span class="p">(</span><span class="n">filename</span><span class="p">,</span> <span class="n">base_content</span><span class="o">.</span><span class="n">encode</span><span class="p">(),</span> <span class="s2">&quot;application/x-sh&quot;</span><span class="p">)</span>
        <span class="p">}</span>

        <span class="n">data</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s2">&quot;target_path&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;/app/scripts/heavy/</span><span class="si">{</span><span class="n">filename</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
            <span class="s2">&quot;permissions&quot;</span><span class="p">:</span> <span class="s2">&quot;0755&quot;</span><span class="p">,</span>
            <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;Heavy load test file&quot;</span>
        <span class="p">}</span>

        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
            <span class="s2">&quot;/api/v1/file-injections/&quot;</span><span class="p">,</span>
            <span class="n">files</span><span class="o">=</span><span class="n">files</span><span class="p">,</span>
            <span class="n">data</span><span class="o">=</span><span class="n">data</span><span class="p">,</span>
            <span class="n">catch_response</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
            <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span>  <span class="c1"># Longer timeout for large files</span>
        <span class="p">)</span> <span class="k">as</span> <span class="n">response</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">201</span><span class="p">:</span>
                <span class="n">response</span><span class="o">.</span><span class="n">success</span><span class="p">()</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">response</span><span class="o">.</span><span class="n">failure</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Heavy upload failed: </span><span class="si">{</span><span class="n">response</span><span class="o">.</span><span class="n">status_code</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="running-load-tests">
<h3>Running Load Tests<a class="headerlink" href="#running-load-tests" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Basic load test</span>
locust<span class="w"> </span>-f<span class="w"> </span>tests/load/locustfile.py<span class="w"> </span>--headless<span class="w"> </span>-u<span class="w"> </span><span class="m">10</span><span class="w"> </span>-r<span class="w"> </span><span class="m">2</span><span class="w"> </span>-t<span class="w"> </span>30s<span class="w"> </span>--host<span class="o">=</span>http://localhost:8000

<span class="c1"># Load test with HTML report</span>
locust<span class="w"> </span>-f<span class="w"> </span>tests/load/locustfile.py<span class="w"> </span>--headless<span class="w"> </span>-u<span class="w"> </span><span class="m">50</span><span class="w"> </span>-r<span class="w"> </span><span class="m">5</span><span class="w"> </span>-t<span class="w"> </span>2m<span class="w"> </span>--host<span class="o">=</span>http://localhost:8000<span class="w"> </span>--html<span class="o">=</span>load-test-report.html

<span class="c1"># Interactive load test (with web UI)</span>
locust<span class="w"> </span>-f<span class="w"> </span>tests/load/locustfile.py<span class="w"> </span>--host<span class="o">=</span>http://localhost:8000
<span class="c1"># Then open http://localhost:8089</span>
</pre></div>
</div>
</section>
<section id="advanced-load-testing">
<h3>Advanced Load Testing<a class="headerlink" href="#advanced-load-testing" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Custom event handlers for metrics</span>
<span class="kn">from</span> <span class="nn">locust</span> <span class="kn">import</span> <span class="n">events</span>

<span class="nd">@events</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">add_listener</span>
<span class="k">def</span> <span class="nf">on_request</span><span class="p">(</span><span class="n">request_type</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">name</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">response_time</span><span class="p">:</span> <span class="nb">float</span><span class="p">,</span> <span class="n">response_length</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">exception</span><span class="p">:</span> <span class="ne">Exception</span> <span class="o">|</span> <span class="kc">None</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Custom request handler for metrics.&quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="n">exception</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Request failed: </span><span class="si">{</span><span class="n">name</span><span class="si">}</span><span class="s2"> - </span><span class="si">{</span><span class="n">exception</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

<span class="nd">@events</span><span class="o">.</span><span class="n">test_start</span><span class="o">.</span><span class="n">add_listener</span>
<span class="k">def</span> <span class="nf">on_test_start</span><span class="p">(</span><span class="n">environment</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Called when test starts.&quot;&quot;&quot;</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;🚀 Load test starting...&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Target host: </span><span class="si">{</span><span class="n">environment</span><span class="o">.</span><span class="n">host</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

<span class="nd">@events</span><span class="o">.</span><span class="n">test_stop</span><span class="o">.</span><span class="n">add_listener</span>
<span class="k">def</span> <span class="nf">on_test_stop</span><span class="p">(</span><span class="n">environment</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Called when test stops.&quot;&quot;&quot;</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;🏁 Load test completed!&quot;</span><span class="p">)</span>

    <span class="c1"># Print summary statistics</span>
    <span class="n">stats</span> <span class="o">=</span> <span class="n">environment</span><span class="o">.</span><span class="n">stats</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Total requests: </span><span class="si">{</span><span class="n">stats</span><span class="o">.</span><span class="n">total</span><span class="o">.</span><span class="n">num_requests</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Total failures: </span><span class="si">{</span><span class="n">stats</span><span class="o">.</span><span class="n">total</span><span class="o">.</span><span class="n">num_failures</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Average response time: </span><span class="si">{</span><span class="n">stats</span><span class="o">.</span><span class="n">total</span><span class="o">.</span><span class="n">avg_response_time</span><span class="si">:</span><span class="s2">.2f</span><span class="si">}</span><span class="s2">ms&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="memory-profiling">
<h2>Memory Profiling<a class="headerlink" href="#memory-profiling" title="Link to this heading"></a></h2>
<section id="memory-usage-testing">
<h3>Memory Usage Testing<a class="headerlink" href="#memory-usage-testing" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">psutil</span>
<span class="kn">import</span> <span class="nn">os</span>

<span class="k">class</span> <span class="nc">TestMemoryPerformance</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Benchmark tests for memory usage patterns.&quot;&quot;&quot;</span>

    <span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">benchmark</span>
    <span class="k">def</span> <span class="nf">test_large_data_processing_performance</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">benchmark</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Benchmark processing of large data structures.&quot;&quot;&quot;</span>
        <span class="k">def</span> <span class="nf">process_large_dataset</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">]:</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;Process large dataset.&quot;&quot;&quot;</span>
            <span class="c1"># Create large dataset</span>
            <span class="n">dataset</span> <span class="o">=</span> <span class="p">{</span>
                <span class="sa">f</span><span class="s2">&quot;injection-</span><span class="si">{</span><span class="n">i</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">:</span> <span class="p">{</span>
                    <span class="s2">&quot;filename&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;file-</span><span class="si">{</span><span class="n">i</span><span class="si">}</span><span class="s2">.sh&quot;</span><span class="p">,</span>
                    <span class="s2">&quot;content&quot;</span><span class="p">:</span> <span class="s2">&quot;A&quot;</span> <span class="o">*</span> <span class="mi">1000</span><span class="p">,</span>  <span class="c1"># 1KB per file</span>
                    <span class="s2">&quot;metadata&quot;</span><span class="p">:</span> <span class="p">{</span>
                        <span class="s2">&quot;size&quot;</span><span class="p">:</span> <span class="mi">1000</span><span class="p">,</span>
                        <span class="s2">&quot;hash&quot;</span><span class="p">:</span> <span class="n">hashlib</span><span class="o">.</span><span class="n">sha256</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;file-</span><span class="si">{</span><span class="n">i</span><span class="si">}</span><span class="s2">&quot;</span><span class="o">.</span><span class="n">encode</span><span class="p">())</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">(),</span>
                        <span class="s2">&quot;tags&quot;</span><span class="p">:</span> <span class="p">[</span><span class="sa">f</span><span class="s2">&quot;tag-</span><span class="si">{</span><span class="n">j</span><span class="si">}</span><span class="s2">&quot;</span> <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">5</span><span class="p">)]</span>
                    <span class="p">}</span>
                <span class="p">}</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">100</span><span class="p">)</span>  <span class="c1"># 100 files = ~100KB total</span>
            <span class="p">}</span>

            <span class="c1"># Process dataset</span>
            <span class="n">total_size</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span>
                <span class="nb">len</span><span class="p">(</span><span class="n">data</span><span class="p">[</span><span class="s2">&quot;content&quot;</span><span class="p">])</span>
                <span class="k">for</span> <span class="n">data</span> <span class="ow">in</span> <span class="n">dataset</span><span class="o">.</span><span class="n">values</span><span class="p">()</span>
            <span class="p">)</span>

            <span class="n">file_count</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">dataset</span><span class="p">)</span>

            <span class="c1"># Calculate some statistics</span>
            <span class="n">avg_size</span> <span class="o">=</span> <span class="n">total_size</span> <span class="o">/</span> <span class="n">file_count</span> <span class="k">if</span> <span class="n">file_count</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="k">else</span> <span class="mi">0</span>

            <span class="k">return</span> <span class="p">{</span>
                <span class="s2">&quot;file_count&quot;</span><span class="p">:</span> <span class="n">file_count</span><span class="p">,</span>
                <span class="s2">&quot;total_size&quot;</span><span class="p">:</span> <span class="n">total_size</span><span class="p">,</span>
                <span class="s2">&quot;average_size&quot;</span><span class="p">:</span> <span class="n">avg_size</span><span class="p">,</span>
                <span class="s2">&quot;processed&quot;</span><span class="p">:</span> <span class="kc">True</span>
            <span class="p">}</span>

        <span class="n">result</span> <span class="o">=</span> <span class="n">benchmark</span><span class="p">(</span><span class="n">process_large_dataset</span><span class="p">)</span>

        <span class="k">assert</span> <span class="n">result</span><span class="p">[</span><span class="s2">&quot;file_count&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="mi">100</span>
        <span class="k">assert</span> <span class="n">result</span><span class="p">[</span><span class="s2">&quot;total_size&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="mi">100000</span>  <span class="c1"># 100KB</span>
        <span class="k">assert</span> <span class="n">result</span><span class="p">[</span><span class="s2">&quot;average_size&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="mf">1000.0</span>
</pre></div>
</div>
</section>
<section id="memory-monitoring">
<h3>Memory Monitoring<a class="headerlink" href="#memory-monitoring" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">test_memory_usage_monitoring</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Monitor memory usage during test execution.&quot;&quot;&quot;</span>
    <span class="n">process</span> <span class="o">=</span> <span class="n">psutil</span><span class="o">.</span><span class="n">Process</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">getpid</span><span class="p">())</span>

    <span class="c1"># Get initial memory usage</span>
    <span class="n">initial_memory</span> <span class="o">=</span> <span class="n">process</span><span class="o">.</span><span class="n">memory_info</span><span class="p">()</span><span class="o">.</span><span class="n">rss</span>

    <span class="c1"># Perform memory-intensive operation</span>
    <span class="n">large_data</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">1000000</span><span class="p">)]</span>  <span class="c1"># 1M integers</span>

    <span class="c1"># Get peak memory usage</span>
    <span class="n">peak_memory</span> <span class="o">=</span> <span class="n">process</span><span class="o">.</span><span class="n">memory_info</span><span class="p">()</span><span class="o">.</span><span class="n">rss</span>

    <span class="c1"># Clean up</span>
    <span class="k">del</span> <span class="n">large_data</span>

    <span class="c1"># Get final memory usage</span>
    <span class="n">final_memory</span> <span class="o">=</span> <span class="n">process</span><span class="o">.</span><span class="n">memory_info</span><span class="p">()</span><span class="o">.</span><span class="n">rss</span>

    <span class="c1"># Assertions about memory usage</span>
    <span class="n">memory_increase</span> <span class="o">=</span> <span class="n">peak_memory</span> <span class="o">-</span> <span class="n">initial_memory</span>
    <span class="n">memory_cleanup</span> <span class="o">=</span> <span class="n">peak_memory</span> <span class="o">-</span> <span class="n">final_memory</span>

    <span class="k">assert</span> <span class="n">memory_increase</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;Memory should increase during operation&quot;</span>
    <span class="k">assert</span> <span class="n">memory_cleanup</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;Memory should be cleaned up after operation&quot;</span>

    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Initial memory: </span><span class="si">{</span><span class="n">initial_memory</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mi">1024</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mi">1024</span><span class="si">:</span><span class="s2">.2f</span><span class="si">}</span><span class="s2"> MB&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Peak memory: </span><span class="si">{</span><span class="n">peak_memory</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mi">1024</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mi">1024</span><span class="si">:</span><span class="s2">.2f</span><span class="si">}</span><span class="s2"> MB&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Final memory: </span><span class="si">{</span><span class="n">final_memory</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mi">1024</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mi">1024</span><span class="si">:</span><span class="s2">.2f</span><span class="si">}</span><span class="s2"> MB&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="performance-regression-testing">
<h2>Performance Regression Testing<a class="headerlink" href="#performance-regression-testing" title="Link to this heading"></a></h2>
<section id="continuous-performance-monitoring">
<h3>Continuous Performance Monitoring<a class="headerlink" href="#continuous-performance-monitoring" title="Link to this heading"></a></h3>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="c1"># GitHub Actions workflow for performance testing</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Performance Tests</span>

<span class="nt">on</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">push</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">pull_request</span><span class="p p-Indicator">]</span>

<span class="nt">jobs</span><span class="p">:</span>
<span class="w">  </span><span class="nt">performance</span><span class="p">:</span>
<span class="w">    </span><span class="nt">runs-on</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ubuntu-latest</span>
<span class="w">    </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/checkout@v3</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Set up Python</span>
<span class="w">        </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/setup-python@v4</span>
<span class="w">        </span><span class="nt">with</span><span class="p">:</span>
<span class="w">          </span><span class="nt">python-version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;3.11&#39;</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Install dependencies</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">          </span><span class="no">pip install -e &quot;.[dev]&quot;</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Run performance tests</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">          </span><span class="no">python -m pytest tests/performance/ \</span>
<span class="w">            </span><span class="no">--benchmark-only \</span>
<span class="w">            </span><span class="no">--benchmark-json=benchmark-results.json</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Compare with baseline</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">          </span><span class="no">python -m pytest tests/performance/ \</span>
<span class="w">            </span><span class="no">--benchmark-only \</span>
<span class="w">            </span><span class="no">--benchmark-compare=baseline.json \</span>
<span class="w">            </span><span class="no">--benchmark-compare-fail=mean:10%</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Upload benchmark results</span>
<span class="w">        </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/upload-artifact@v3</span>
<span class="w">        </span><span class="nt">with</span><span class="p">:</span>
<span class="w">          </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">benchmark-results</span>
<span class="w">          </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">benchmark-results.json</span>
</pre></div>
</div>
</section>
<section id="performance-alerts">
<h3>Performance Alerts<a class="headerlink" href="#performance-alerts" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Custom benchmark plugin for alerts</span>
<span class="kn">import</span> <span class="nn">pytest</span>

<span class="k">def</span> <span class="nf">pytest_benchmark_compare_machine_info</span><span class="p">(</span><span class="n">config</span><span class="p">,</span> <span class="n">benchinfo</span><span class="p">,</span> <span class="n">compared_benchinfo</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Compare machine info for benchmark consistency.&quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="n">benchinfo</span><span class="p">[</span><span class="s2">&quot;machine_info&quot;</span><span class="p">][</span><span class="s2">&quot;cpu&quot;</span><span class="p">]</span> <span class="o">!=</span> <span class="n">compared_benchinfo</span><span class="p">[</span><span class="s2">&quot;machine_info&quot;</span><span class="p">][</span><span class="s2">&quot;cpu&quot;</span><span class="p">]:</span>
        <span class="n">pytest</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;Different CPU detected - benchmark comparison may be unreliable&quot;</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">pytest_benchmark_compare_failed</span><span class="p">(</span><span class="n">config</span><span class="p">,</span> <span class="n">benchinfo</span><span class="p">,</span> <span class="n">compared_benchinfo</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Handle benchmark comparison failures.&quot;&quot;&quot;</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;⚠️ Performance regression detected!&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Consider investigating the following:&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;- Recent code changes&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;- Dependency updates&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;- System resource availability&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="benchmark-design">
<h3>Benchmark Design<a class="headerlink" href="#benchmark-design" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Isolate what you’re measuring</strong> - Test one thing at a time</p></li>
<li><p><strong>Use realistic data</strong> - Test with production-like inputs</p></li>
<li><p><strong>Warm up the system</strong> - Account for JIT compilation and caching</p></li>
<li><p><strong>Run multiple iterations</strong> - Get statistically significant results</p></li>
</ol>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">benchmark</span>
<span class="k">def</span> <span class="nf">test_good_benchmark</span><span class="p">(</span><span class="n">benchmark</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Example of a well-designed benchmark.&quot;&quot;&quot;</span>
    <span class="c1"># Setup (not measured)</span>
    <span class="n">data</span> <span class="o">=</span> <span class="n">create_realistic_test_data</span><span class="p">()</span>

    <span class="c1"># The actual operation being measured</span>
    <span class="n">result</span> <span class="o">=</span> <span class="n">benchmark</span><span class="p">(</span><span class="n">process_data</span><span class="p">,</span> <span class="n">data</span><span class="p">)</span>

    <span class="c1"># Verification (not measured)</span>
    <span class="k">assert</span> <span class="n">result</span><span class="o">.</span><span class="n">is_valid</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="performance-thresholds">
<h3>Performance Thresholds<a class="headerlink" href="#performance-thresholds" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Set performance expectations</span>
<span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">benchmark</span>
<span class="k">def</span> <span class="nf">test_model_creation_speed</span><span class="p">(</span><span class="n">benchmark</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Model creation should be under 1ms.&quot;&quot;&quot;</span>
    <span class="n">result</span> <span class="o">=</span> <span class="n">benchmark</span><span class="p">(</span><span class="n">FileInjectionCreate</span><span class="p">,</span> <span class="o">**</span><span class="n">test_data</span><span class="p">)</span>

    <span class="c1"># Verify performance threshold</span>
    <span class="k">assert</span> <span class="n">benchmark</span><span class="o">.</span><span class="n">stats</span><span class="o">.</span><span class="n">mean</span> <span class="o">&lt;</span> <span class="mf">0.001</span>  <span class="c1"># Less than 1ms</span>
</pre></div>
</div>
</section>
<section id="load-test-strategy">
<h3>Load Test Strategy<a class="headerlink" href="#load-test-strategy" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Start small</strong> - Begin with low load and increase gradually</p></li>
<li><p><strong>Test realistic scenarios</strong> - Use actual user behavior patterns</p></li>
<li><p><strong>Monitor system resources</strong> - CPU, memory, disk, network</p></li>
<li><p><strong>Test failure modes</strong> - What happens when limits are exceeded</p></li>
</ol>
</section>
<section id="continuous-monitoring">
<h3>Continuous Monitoring<a class="headerlink" href="#continuous-monitoring" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Baseline establishment</strong> - Set performance baselines for comparison</p></li>
<li><p><strong>Regression detection</strong> - Alert on performance degradation</p></li>
<li><p><strong>Trend analysis</strong> - Track performance over time</p></li>
<li><p><strong>Capacity planning</strong> - Use results for scaling decisions</p></li>
</ol>
</section>
</section>
<section id="next-steps">
<h2>Next Steps<a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>After mastering performance testing:</p>
<ol class="arabic simple">
<li><p><strong>Security Testing</strong> - <span class="xref std std-doc">security-testing</span></p></li>
<li><p><strong>Integration Testing</strong> - <span class="xref std std-doc">integration-testing</span></p></li>
<li><p><strong>Advanced Debugging</strong> - <span class="xref std std-doc">debugging</span></p></li>
<li><p><strong>CI/CD Integration</strong> - <span class="xref std std-doc">ci-cd</span></p></li>
</ol>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="property-testing.html" class="btn btn-neutral float-left" title="Property-Based Testing" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, TurdParty Team.
      <span class="lastupdated">Last updated on Jan 01, 1980.
      </span></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>