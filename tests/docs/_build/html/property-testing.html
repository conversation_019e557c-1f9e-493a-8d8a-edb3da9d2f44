<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Property-Based Testing &mdash; TurdParty Testing Framework v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=67647e4a" />
      <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=67647e4a" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=8d563738"></script>
        <script src="_static/doctools.js?v=9a2dae69"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/custom.js?v=3a49a312"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Performance Testing" href="performance-testing.html" />
    <link rel="prev" title="Unit Testing" href="unit-testing.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            TurdParty Testing Framework
          </a>
              <div class="version">
                1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Quick Start</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="quickstart.html">Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation &amp; Setup</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Core Testing</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="unit-testing.html">Unit Testing</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Property-Based Testing</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#what-is-property-based-testing">What is Property-Based Testing?</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#traditional-testing-vs-property-based-testing">Traditional Testing vs Property-Based Testing</a></li>
<li class="toctree-l3"><a class="reference internal" href="#core-concepts">Core Concepts</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#our-property-based-tests">Our Property-Based Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#test-structure">Test Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="#file-injection-model-properties">File Injection Model Properties</a></li>
<li class="toctree-l3"><a class="reference internal" href="#data-integrity-properties">Data Integrity Properties</a></li>
<li class="toctree-l3"><a class="reference internal" href="#validation-properties">Validation Properties</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#hypothesis-strategies">Hypothesis Strategies</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#basic-strategies">Basic Strategies</a></li>
<li class="toctree-l3"><a class="reference internal" href="#custom-strategies">Custom Strategies</a></li>
<li class="toctree-l3"><a class="reference internal" href="#composite-strategies">Composite Strategies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#advanced-hypothesis-features">Advanced Hypothesis Features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#assumptions">Assumptions</a></li>
<li class="toctree-l3"><a class="reference internal" href="#examples-and-targeting">Examples and Targeting</a></li>
<li class="toctree-l3"><a class="reference internal" href="#settings-and-profiles">Settings and Profiles</a></li>
<li class="toctree-l3"><a class="reference internal" href="#stateful-testing">Stateful Testing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#running-property-based-tests">Running Property-Based Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#basic-execution">Basic Execution</a></li>
<li class="toctree-l3"><a class="reference internal" href="#debugging-failures">Debugging Failures</a></li>
<li class="toctree-l3"><a class="reference internal" href="#hypothesis-database">Hypothesis Database</a></li>
<li class="toctree-l3"><a class="reference internal" href="#configuration">Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#writing-good-properties">Writing Good Properties</a></li>
<li class="toctree-l3"><a class="reference internal" href="#strategy-design">Strategy Design</a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#integration-with-ci-cd">Integration with CI/CD</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#monitoring-and-metrics">Monitoring and Metrics</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#next-steps">Next Steps</a></li>
<li class="toctree-l2"><a class="reference internal" href="#resources">Resources</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="performance-testing.html">Performance Testing</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">TurdParty Testing Framework</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Property-Based Testing</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/tenbahtsecurity/turdparty/blob/master/tests/docs/property-testing.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="property-based-testing">
<h1>Property-Based Testing<a class="headerlink" href="#property-based-testing" title="Link to this heading"></a></h1>
<p>Property-based testing with Hypothesis represents a paradigm shift from traditional example-based testing. Instead of writing tests with specific inputs and expected outputs, you define properties that should hold true for a wide range of inputs, and Hypothesis automatically generates test cases to verify these properties.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>Our property-based test suite consists of 9 comprehensive tests that execute in 1.02 seconds, testing thousands of input combinations to discover edge cases that traditional testing might miss.</p>
<p><strong>Key Benefits:</strong></p>
<ul class="simple">
<li><p><strong>Automated edge case discovery</strong> - Hypothesis finds inputs that break your code</p></li>
<li><p><strong>Comprehensive coverage</strong> - Tests thousands of input combinations automatically</p></li>
<li><p><strong>Regression prevention</strong> - Saves failing examples for future test runs</p></li>
<li><p><strong>Documentation</strong> - Properties serve as executable specifications</p></li>
</ul>
</section>
<section id="what-is-property-based-testing">
<h2>What is Property-Based Testing?<a class="headerlink" href="#what-is-property-based-testing" title="Link to this heading"></a></h2>
<section id="traditional-testing-vs-property-based-testing">
<h3>Traditional Testing vs Property-Based Testing<a class="headerlink" href="#traditional-testing-vs-property-based-testing" title="Link to this heading"></a></h3>
<p><strong>Traditional Example-Based Testing:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">test_filename_validation</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test specific examples.&quot;&quot;&quot;</span>
    <span class="k">assert</span> <span class="n">validate_filename</span><span class="p">(</span><span class="s2">&quot;test.txt&quot;</span><span class="p">)</span> <span class="o">==</span> <span class="kc">True</span>
    <span class="k">assert</span> <span class="n">validate_filename</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="p">)</span> <span class="o">==</span> <span class="kc">False</span>
    <span class="k">assert</span> <span class="n">validate_filename</span><span class="p">(</span><span class="s2">&quot;file with spaces.txt&quot;</span><span class="p">)</span> <span class="o">==</span> <span class="kc">True</span>
</pre></div>
</div>
<p><strong>Property-Based Testing:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">hypothesis</span> <span class="kn">import</span> <span class="n">given</span><span class="p">,</span> <span class="n">strategies</span> <span class="k">as</span> <span class="n">st</span>

<span class="nd">@given</span><span class="p">(</span><span class="n">filename</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">())</span>
<span class="k">def</span> <span class="nf">test_filename_validation_properties</span><span class="p">(</span><span class="n">filename</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test properties that should always hold.&quot;&quot;&quot;</span>
    <span class="n">result</span> <span class="o">=</span> <span class="n">validate_filename</span><span class="p">(</span><span class="n">filename</span><span class="p">)</span>

    <span class="c1"># Property: result should always be boolean</span>
    <span class="k">assert</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">result</span><span class="p">,</span> <span class="nb">bool</span><span class="p">)</span>

    <span class="c1"># Property: empty strings should be invalid</span>
    <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">filename</span><span class="o">.</span><span class="n">strip</span><span class="p">())</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
        <span class="k">assert</span> <span class="n">result</span> <span class="o">==</span> <span class="kc">False</span>

    <span class="c1"># Property: valid filenames should roundtrip</span>
    <span class="k">if</span> <span class="n">result</span> <span class="o">==</span> <span class="kc">True</span><span class="p">:</span>
        <span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">filename</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span>
</pre></div>
</div>
</section>
<section id="core-concepts">
<h3>Core Concepts<a class="headerlink" href="#core-concepts" title="Link to this heading"></a></h3>
<p><strong>Properties</strong> are statements that should be true for all valid inputs:</p>
<ul class="simple">
<li><p><strong>Invariants</strong> - Things that never change</p></li>
<li><p><strong>Postconditions</strong> - What should be true after an operation</p></li>
<li><p><strong>Relationships</strong> - How inputs relate to outputs</p></li>
<li><p><strong>Roundtrip properties</strong> - Serialize then deserialize should equal original</p></li>
</ul>
</section>
</section>
<section id="our-property-based-tests">
<h2>Our Property-Based Tests<a class="headerlink" href="#our-property-based-tests" title="Link to this heading"></a></h2>
<section id="test-structure">
<h3>Test Structure<a class="headerlink" href="#test-structure" title="Link to this heading"></a></h3>
<p>Our property-based tests are located in <code class="docutils literal notranslate"><span class="pre">tests/property/test_property_based.py</span></code>:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>tests/property/
└── test_property_based.py
    ├── TestFileInjectionProperties      # Model validation properties
    ├── TestDataIntegrityProperties      # Data consistency properties
    ├── TestValidationProperties         # Input validation properties
    ├── TestConcurrencyProperties        # Concurrent operation properties
    └── TestBoundaryProperties           # Boundary condition properties
</pre></div>
</div>
</section>
<section id="file-injection-model-properties">
<h3>File Injection Model Properties<a class="headerlink" href="#file-injection-model-properties" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">TestFileInjectionProperties</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Property-based tests for file injection models.&quot;&quot;&quot;</span>

    <span class="nd">@given</span><span class="p">(</span>
        <span class="n">filename</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(</span>
            <span class="n">min_size</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
            <span class="n">max_size</span><span class="o">=</span><span class="mi">50</span><span class="p">,</span>
            <span class="n">alphabet</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">characters</span><span class="p">(</span>
                <span class="n">whitelist_categories</span><span class="o">=</span><span class="p">(</span><span class="s2">&quot;Lu&quot;</span><span class="p">,</span> <span class="s2">&quot;Ll&quot;</span><span class="p">,</span> <span class="s2">&quot;Nd&quot;</span><span class="p">),</span>
                <span class="n">whitelist_characters</span><span class="o">=</span><span class="s2">&quot;.-_&quot;</span>
            <span class="p">)</span>
        <span class="p">),</span>
        <span class="n">target_path</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(</span><span class="n">min_size</span><span class="o">=</span><span class="mi">5</span><span class="p">,</span> <span class="n">max_size</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">alphabet</span><span class="o">=</span><span class="s2">&quot;abcdefghijklmnopqrstuvwxyz/&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">map</span><span class="p">(</span>
            <span class="k">lambda</span> <span class="n">x</span><span class="p">:</span> <span class="s2">&quot;/&quot;</span> <span class="o">+</span> <span class="n">x</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot;//&quot;</span><span class="p">,</span> <span class="s2">&quot;/&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">strip</span><span class="p">(</span><span class="s2">&quot;/&quot;</span><span class="p">)</span>
        <span class="p">),</span>
        <span class="n">permissions</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">sampled_from</span><span class="p">([</span><span class="s2">&quot;0644&quot;</span><span class="p">,</span> <span class="s2">&quot;0755&quot;</span><span class="p">,</span> <span class="s2">&quot;0777&quot;</span><span class="p">,</span> <span class="s2">&quot;0600&quot;</span><span class="p">,</span> <span class="s2">&quot;0700&quot;</span><span class="p">]),</span>
        <span class="n">description</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">one_of</span><span class="p">(</span><span class="n">st</span><span class="o">.</span><span class="n">none</span><span class="p">(),</span> <span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(</span><span class="n">max_size</span><span class="o">=</span><span class="mi">100</span><span class="p">))</span>
    <span class="p">)</span>
    <span class="nd">@settings</span><span class="p">(</span><span class="n">suppress_health_check</span><span class="o">=</span><span class="p">[</span><span class="n">HealthCheck</span><span class="o">.</span><span class="n">filter_too_much</span><span class="p">])</span>
    <span class="k">def</span> <span class="nf">test_file_injection_create_roundtrip</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">filename</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">target_path</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">permissions</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">description</span><span class="p">:</span> <span class="nb">str</span> <span class="o">|</span> <span class="kc">None</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Test that FileInjectionCreate can be created and serialized consistently.&quot;&quot;&quot;</span>
        <span class="c1"># Assume valid inputs</span>
        <span class="n">assume</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">filename</span><span class="o">.</span><span class="n">strip</span><span class="p">())</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">assume</span><span class="p">(</span><span class="n">target_path</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s2">&quot;/&quot;</span><span class="p">))</span>
        <span class="n">assume</span><span class="p">(</span><span class="ow">not</span> <span class="nb">any</span><span class="p">(</span><span class="n">char</span> <span class="ow">in</span> <span class="n">filename</span> <span class="k">for</span> <span class="n">char</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;/&#39;</span><span class="p">,</span> <span class="s1">&#39;</span><span class="se">\\</span><span class="s1">&#39;</span><span class="p">,</span> <span class="s1">&#39;</span><span class="se">\0</span><span class="s1">&#39;</span><span class="p">,</span> <span class="s1">&#39;</span><span class="se">\n</span><span class="s1">&#39;</span><span class="p">,</span> <span class="s1">&#39;</span><span class="se">\r</span><span class="s1">&#39;</span><span class="p">]))</span>

        <span class="c1"># Create model</span>
        <span class="n">model</span> <span class="o">=</span> <span class="n">FileInjectionCreate</span><span class="p">(</span>
            <span class="n">filename</span><span class="o">=</span><span class="n">filename</span><span class="p">,</span>
            <span class="n">target_path</span><span class="o">=</span><span class="n">target_path</span><span class="p">,</span>
            <span class="n">permissions</span><span class="o">=</span><span class="n">permissions</span><span class="p">,</span>
            <span class="n">description</span><span class="o">=</span><span class="n">description</span>
        <span class="p">)</span>

        <span class="c1"># Properties that should always hold</span>
        <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">filename</span> <span class="o">==</span> <span class="n">filename</span>
        <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">target_path</span> <span class="o">==</span> <span class="n">target_path</span>
        <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">permissions</span> <span class="o">==</span> <span class="n">permissions</span>
        <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">description</span> <span class="o">==</span> <span class="n">description</span>

        <span class="c1"># Serialization roundtrip property</span>
        <span class="n">json_data</span> <span class="o">=</span> <span class="n">model</span><span class="o">.</span><span class="n">model_dump_json</span><span class="p">()</span>
        <span class="n">recreated</span> <span class="o">=</span> <span class="n">FileInjectionCreate</span><span class="o">.</span><span class="n">model_validate_json</span><span class="p">(</span><span class="n">json_data</span><span class="p">)</span>

        <span class="k">assert</span> <span class="n">recreated</span><span class="o">.</span><span class="n">filename</span> <span class="o">==</span> <span class="n">model</span><span class="o">.</span><span class="n">filename</span>
        <span class="k">assert</span> <span class="n">recreated</span><span class="o">.</span><span class="n">target_path</span> <span class="o">==</span> <span class="n">model</span><span class="o">.</span><span class="n">target_path</span>
        <span class="k">assert</span> <span class="n">recreated</span><span class="o">.</span><span class="n">permissions</span> <span class="o">==</span> <span class="n">model</span><span class="o">.</span><span class="n">permissions</span>
        <span class="k">assert</span> <span class="n">recreated</span><span class="o">.</span><span class="n">description</span> <span class="o">==</span> <span class="n">model</span><span class="o">.</span><span class="n">description</span>
</pre></div>
</div>
</section>
<section id="data-integrity-properties">
<h3>Data Integrity Properties<a class="headerlink" href="#data-integrity-properties" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">TestDataIntegrityProperties</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Property-based tests for data integrity.&quot;&quot;&quot;</span>

    <span class="nd">@given</span><span class="p">(</span><span class="n">data</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">binary</span><span class="p">(</span><span class="n">min_size</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">max_size</span><span class="o">=</span><span class="mi">10000</span><span class="p">))</span>
    <span class="nd">@settings</span><span class="p">(</span><span class="n">suppress_health_check</span><span class="o">=</span><span class="p">[</span><span class="n">HealthCheck</span><span class="o">.</span><span class="n">function_scoped_fixture</span><span class="p">])</span>
    <span class="k">def</span> <span class="nf">test_hash_consistency_property</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">data</span><span class="p">:</span> <span class="nb">bytes</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Test that hash calculation is always consistent.&quot;&quot;&quot;</span>
        <span class="c1"># Calculate hash multiple times</span>
        <span class="n">hash1</span> <span class="o">=</span> <span class="n">hashlib</span><span class="o">.</span><span class="n">sha256</span><span class="p">(</span><span class="n">data</span><span class="p">)</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
        <span class="n">hash2</span> <span class="o">=</span> <span class="n">hashlib</span><span class="o">.</span><span class="n">sha256</span><span class="p">(</span><span class="n">data</span><span class="p">)</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
        <span class="n">hash3</span> <span class="o">=</span> <span class="n">hashlib</span><span class="o">.</span><span class="n">sha256</span><span class="p">(</span><span class="n">data</span><span class="p">)</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>

        <span class="c1"># Hashes should always be identical</span>
        <span class="k">assert</span> <span class="n">hash1</span> <span class="o">==</span> <span class="n">hash2</span> <span class="o">==</span> <span class="n">hash3</span>

        <span class="c1"># Hash should always be 64 characters (SHA256)</span>
        <span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">hash1</span><span class="p">)</span> <span class="o">==</span> <span class="mi">64</span>

        <span class="c1"># Hash should only contain hex characters</span>
        <span class="k">assert</span> <span class="nb">all</span><span class="p">(</span><span class="n">c</span> <span class="ow">in</span> <span class="s1">&#39;0123456789abcdef&#39;</span> <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="n">hash1</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="validation-properties">
<h3>Validation Properties<a class="headerlink" href="#validation-properties" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">TestValidationProperties</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Property-based tests for validation logic.&quot;&quot;&quot;</span>

    <span class="nd">@given</span><span class="p">(</span><span class="n">filename</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(</span><span class="n">max_size</span><span class="o">=</span><span class="mi">1000</span><span class="p">))</span>
    <span class="k">def</span> <span class="nf">test_filename_validation_properties</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">filename</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Test filename validation properties.&quot;&quot;&quot;</span>
        <span class="c1"># Test various filename patterns</span>
        <span class="n">has_path_separator</span> <span class="o">=</span> <span class="s2">&quot;/&quot;</span> <span class="ow">in</span> <span class="n">filename</span> <span class="ow">or</span> <span class="s2">&quot;</span><span class="se">\\</span><span class="s2">&quot;</span> <span class="ow">in</span> <span class="n">filename</span>
        <span class="n">has_null_byte</span> <span class="o">=</span> <span class="s2">&quot;</span><span class="se">\0</span><span class="s2">&quot;</span> <span class="ow">in</span> <span class="n">filename</span>
        <span class="n">has_newline</span> <span class="o">=</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span> <span class="ow">in</span> <span class="n">filename</span> <span class="ow">or</span> <span class="s2">&quot;</span><span class="se">\r</span><span class="s2">&quot;</span> <span class="ow">in</span> <span class="n">filename</span>
        <span class="n">is_empty</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">filename</span><span class="o">.</span><span class="n">strip</span><span class="p">())</span> <span class="o">==</span> <span class="mi">0</span>

        <span class="c1"># If filename has dangerous characters, model creation might fail</span>
        <span class="c1"># This is expected behavior</span>
        <span class="k">if</span> <span class="n">has_path_separator</span> <span class="ow">or</span> <span class="n">has_null_byte</span> <span class="ow">or</span> <span class="n">has_newline</span> <span class="ow">or</span> <span class="n">is_empty</span><span class="p">:</span>
            <span class="c1"># These should be handled by validation in service layer</span>
            <span class="k">pass</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># Safe filenames should always work</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="n">model</span> <span class="o">=</span> <span class="n">FileInjectionCreate</span><span class="p">(</span>
                    <span class="n">filename</span><span class="o">=</span><span class="n">filename</span><span class="p">,</span>
                    <span class="n">target_path</span><span class="o">=</span><span class="s2">&quot;/app/test.sh&quot;</span><span class="p">,</span>
                    <span class="n">permissions</span><span class="o">=</span><span class="s2">&quot;0755&quot;</span>
                <span class="p">)</span>
                <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">filename</span> <span class="o">==</span> <span class="n">filename</span>
            <span class="k">except</span> <span class="ne">Exception</span><span class="p">:</span>
                <span class="c1"># Some edge cases might still fail, which is acceptable</span>
                <span class="k">pass</span>
</pre></div>
</div>
</section>
</section>
<section id="hypothesis-strategies">
<h2>Hypothesis Strategies<a class="headerlink" href="#hypothesis-strategies" title="Link to this heading"></a></h2>
<p>Strategies define how Hypothesis generates test data. Our tests use various strategies to create realistic and edge-case inputs.</p>
<section id="basic-strategies">
<h3>Basic Strategies<a class="headerlink" href="#basic-strategies" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">hypothesis.strategies</span> <span class="k">as</span> <span class="nn">st</span>

<span class="c1"># Text strategies</span>
<span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">()</span>                           <span class="c1"># Any unicode text</span>
<span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(</span><span class="n">min_size</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">max_size</span><span class="o">=</span><span class="mi">100</span><span class="p">)</span>  <span class="c1"># Text with size constraints</span>
<span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(</span><span class="n">alphabet</span><span class="o">=</span><span class="s2">&quot;abcdef&quot;</span><span class="p">)</span>          <span class="c1"># Text from specific alphabet</span>

<span class="c1"># Numeric strategies</span>
<span class="n">st</span><span class="o">.</span><span class="n">integers</span><span class="p">()</span>                       <span class="c1"># Any integer</span>
<span class="n">st</span><span class="o">.</span><span class="n">integers</span><span class="p">(</span><span class="n">min_value</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">max_value</span><span class="o">=</span><span class="mi">100</span><span class="p">)</span>  <span class="c1"># Bounded integers</span>
<span class="n">st</span><span class="o">.</span><span class="n">floats</span><span class="p">(</span><span class="n">min_value</span><span class="o">=</span><span class="mf">0.0</span><span class="p">,</span> <span class="n">max_value</span><span class="o">=</span><span class="mf">1.0</span><span class="p">)</span>  <span class="c1"># Bounded floats</span>

<span class="c1"># Collection strategies</span>
<span class="n">st</span><span class="o">.</span><span class="n">lists</span><span class="p">(</span><span class="n">st</span><span class="o">.</span><span class="n">integers</span><span class="p">())</span>             <span class="c1"># Lists of integers</span>
<span class="n">st</span><span class="o">.</span><span class="n">dictionaries</span><span class="p">(</span><span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(),</span> <span class="n">st</span><span class="o">.</span><span class="n">integers</span><span class="p">())</span>  <span class="c1"># String to int mappings</span>
<span class="n">st</span><span class="o">.</span><span class="n">tuples</span><span class="p">(</span><span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(),</span> <span class="n">st</span><span class="o">.</span><span class="n">integers</span><span class="p">())</span> <span class="c1"># Fixed-size tuples</span>
</pre></div>
</div>
</section>
<section id="custom-strategies">
<h3>Custom Strategies<a class="headerlink" href="#custom-strategies" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Custom filename strategy</span>
<span class="n">filename_strategy</span> <span class="o">=</span> <span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(</span>
    <span class="n">min_size</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
    <span class="n">max_size</span><span class="o">=</span><span class="mi">255</span><span class="p">,</span>
    <span class="n">alphabet</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">characters</span><span class="p">(</span>
        <span class="n">whitelist_categories</span><span class="o">=</span><span class="p">(</span><span class="s2">&quot;Lu&quot;</span><span class="p">,</span> <span class="s2">&quot;Ll&quot;</span><span class="p">,</span> <span class="s2">&quot;Nd&quot;</span><span class="p">),</span>  <span class="c1"># Letters and digits</span>
        <span class="n">whitelist_characters</span><span class="o">=</span><span class="s2">&quot;.-_&quot;</span>                 <span class="c1"># Safe punctuation</span>
    <span class="p">)</span>
<span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x</span><span class="p">:</span> <span class="ow">not</span> <span class="n">x</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;.&#39;</span><span class="p">))</span>  <span class="c1"># No hidden files</span>

<span class="c1"># Custom path strategy</span>
<span class="n">path_strategy</span> <span class="o">=</span> <span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(</span>
    <span class="n">min_size</span><span class="o">=</span><span class="mi">5</span><span class="p">,</span>
    <span class="n">max_size</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span>
    <span class="n">alphabet</span><span class="o">=</span><span class="s2">&quot;abcdefghijklmnopqrstuvwxyz/&quot;</span>
<span class="p">)</span><span class="o">.</span><span class="n">map</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x</span><span class="p">:</span> <span class="s2">&quot;/&quot;</span> <span class="o">+</span> <span class="n">x</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot;//&quot;</span><span class="p">,</span> <span class="s2">&quot;/&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">strip</span><span class="p">(</span><span class="s2">&quot;/&quot;</span><span class="p">))</span>

<span class="c1"># Custom permissions strategy</span>
<span class="n">permissions_strategy</span> <span class="o">=</span> <span class="n">st</span><span class="o">.</span><span class="n">sampled_from</span><span class="p">([</span>
    <span class="s2">&quot;0644&quot;</span><span class="p">,</span> <span class="s2">&quot;0755&quot;</span><span class="p">,</span> <span class="s2">&quot;0777&quot;</span><span class="p">,</span> <span class="s2">&quot;0600&quot;</span><span class="p">,</span> <span class="s2">&quot;0700&quot;</span><span class="p">,</span> <span class="s2">&quot;0750&quot;</span><span class="p">,</span> <span class="s2">&quot;0640&quot;</span>
<span class="p">])</span>
</pre></div>
</div>
</section>
<section id="composite-strategies">
<h3>Composite Strategies<a class="headerlink" href="#composite-strategies" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">hypothesis</span> <span class="kn">import</span> <span class="n">strategies</span> <span class="k">as</span> <span class="n">st</span>
<span class="kn">from</span> <span class="nn">hypothesis.strategies</span> <span class="kn">import</span> <span class="n">composite</span>

<span class="nd">@composite</span>
<span class="k">def</span> <span class="nf">file_injection_data</span><span class="p">(</span><span class="n">draw</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Generate complete file injection data.&quot;&quot;&quot;</span>
    <span class="n">filename</span> <span class="o">=</span> <span class="n">draw</span><span class="p">(</span><span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(</span><span class="n">min_size</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">max_size</span><span class="o">=</span><span class="mi">50</span><span class="p">))</span>
    <span class="n">target_path</span> <span class="o">=</span> <span class="n">draw</span><span class="p">(</span><span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(</span><span class="n">min_size</span><span class="o">=</span><span class="mi">5</span><span class="p">,</span> <span class="n">max_size</span><span class="o">=</span><span class="mi">100</span><span class="p">))</span>
    <span class="n">permissions</span> <span class="o">=</span> <span class="n">draw</span><span class="p">(</span><span class="n">st</span><span class="o">.</span><span class="n">sampled_from</span><span class="p">([</span><span class="s2">&quot;0644&quot;</span><span class="p">,</span> <span class="s2">&quot;0755&quot;</span><span class="p">,</span> <span class="s2">&quot;0777&quot;</span><span class="p">]))</span>
    <span class="n">description</span> <span class="o">=</span> <span class="n">draw</span><span class="p">(</span><span class="n">st</span><span class="o">.</span><span class="n">one_of</span><span class="p">(</span><span class="n">st</span><span class="o">.</span><span class="n">none</span><span class="p">(),</span> <span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(</span><span class="n">max_size</span><span class="o">=</span><span class="mi">200</span><span class="p">)))</span>

    <span class="k">return</span> <span class="p">{</span>
        <span class="s2">&quot;filename&quot;</span><span class="p">:</span> <span class="n">filename</span><span class="p">,</span>
        <span class="s2">&quot;target_path&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;/</span><span class="si">{</span><span class="n">target_path</span><span class="o">.</span><span class="n">strip</span><span class="p">(</span><span class="s1">&#39;/&#39;</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
        <span class="s2">&quot;permissions&quot;</span><span class="p">:</span> <span class="n">permissions</span><span class="p">,</span>
        <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="n">description</span>
    <span class="p">}</span>

<span class="nd">@given</span><span class="p">(</span><span class="n">data</span><span class="o">=</span><span class="n">file_injection_data</span><span class="p">())</span>
<span class="k">def</span> <span class="nf">test_with_composite_strategy</span><span class="p">(</span><span class="n">data</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test using composite strategy.&quot;&quot;&quot;</span>
    <span class="n">model</span> <span class="o">=</span> <span class="n">FileInjectionCreate</span><span class="p">(</span><span class="o">**</span><span class="n">data</span><span class="p">)</span>
    <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">filename</span> <span class="o">==</span> <span class="n">data</span><span class="p">[</span><span class="s2">&quot;filename&quot;</span><span class="p">]</span>
</pre></div>
</div>
</section>
</section>
<section id="advanced-hypothesis-features">
<h2>Advanced Hypothesis Features<a class="headerlink" href="#advanced-hypothesis-features" title="Link to this heading"></a></h2>
<section id="assumptions">
<h3>Assumptions<a class="headerlink" href="#assumptions" title="Link to this heading"></a></h3>
<p>Use <code class="docutils literal notranslate"><span class="pre">assume()</span></code> to filter out invalid inputs:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">hypothesis</span> <span class="kn">import</span> <span class="n">given</span><span class="p">,</span> <span class="n">assume</span>
<span class="kn">import</span> <span class="nn">hypothesis.strategies</span> <span class="k">as</span> <span class="nn">st</span>

<span class="nd">@given</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">integers</span><span class="p">(),</span> <span class="n">y</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">integers</span><span class="p">())</span>
<span class="k">def</span> <span class="nf">test_division_properties</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test division properties.&quot;&quot;&quot;</span>
    <span class="n">assume</span><span class="p">(</span><span class="n">y</span> <span class="o">!=</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Skip cases where y is zero</span>

    <span class="n">result</span> <span class="o">=</span> <span class="n">x</span> <span class="o">/</span> <span class="n">y</span>
    <span class="k">assert</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">result</span><span class="p">,</span> <span class="nb">float</span><span class="p">)</span>
    <span class="k">assert</span> <span class="n">result</span> <span class="o">*</span> <span class="n">y</span> <span class="o">==</span> <span class="n">x</span>  <span class="c1"># Property: (x/y) * y == x</span>
</pre></div>
</div>
</section>
<section id="examples-and-targeting">
<h3>Examples and Targeting<a class="headerlink" href="#examples-and-targeting" title="Link to this heading"></a></h3>
<p>Provide specific examples and guide Hypothesis towards interesting cases:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="nd">@given</span><span class="p">(</span><span class="n">filename</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">())</span>
<span class="nd">@example</span><span class="p">(</span><span class="n">filename</span><span class="o">=</span><span class="s2">&quot;&quot;</span><span class="p">)</span>           <span class="c1"># Always test empty string</span>
<span class="nd">@example</span><span class="p">(</span><span class="n">filename</span><span class="o">=</span><span class="s2">&quot;test.txt&quot;</span><span class="p">)</span>   <span class="c1"># Always test normal case</span>
<span class="nd">@example</span><span class="p">(</span><span class="n">filename</span><span class="o">=</span><span class="s2">&quot;a&quot;</span> <span class="o">*</span> <span class="mi">1000</span><span class="p">)</span>   <span class="c1"># Always test very long string</span>
<span class="k">def</span> <span class="nf">test_filename_with_examples</span><span class="p">(</span><span class="n">filename</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test with specific examples.&quot;&quot;&quot;</span>
    <span class="n">result</span> <span class="o">=</span> <span class="n">validate_filename</span><span class="p">(</span><span class="n">filename</span><span class="p">)</span>
    <span class="k">assert</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">result</span><span class="p">,</span> <span class="nb">bool</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="settings-and-profiles">
<h3>Settings and Profiles<a class="headerlink" href="#settings-and-profiles" title="Link to this heading"></a></h3>
<p>Configure Hypothesis behavior:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">hypothesis</span> <span class="kn">import</span> <span class="n">settings</span><span class="p">,</span> <span class="n">Verbosity</span>

<span class="c1"># Test-specific settings</span>
<span class="nd">@given</span><span class="p">(</span><span class="n">data</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">binary</span><span class="p">(</span><span class="n">min_size</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">max_size</span><span class="o">=</span><span class="mi">1000000</span><span class="p">))</span>
<span class="nd">@settings</span><span class="p">(</span>
    <span class="n">max_examples</span><span class="o">=</span><span class="mi">50</span><span class="p">,</span>           <span class="c1"># Run 50 examples instead of default 100</span>
    <span class="n">deadline</span><span class="o">=</span><span class="mi">5000</span><span class="p">,</span>             <span class="c1"># Allow 5 seconds per example</span>
    <span class="n">verbosity</span><span class="o">=</span><span class="n">Verbosity</span><span class="o">.</span><span class="n">verbose</span>  <span class="c1"># Show detailed output</span>
<span class="p">)</span>
<span class="k">def</span> <span class="nf">test_large_data_processing</span><span class="p">(</span><span class="n">data</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test with custom settings.&quot;&quot;&quot;</span>
    <span class="n">result</span> <span class="o">=</span> <span class="n">process_large_data</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>
    <span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">result</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span>

<span class="c1"># Global profiles (in conftest.py)</span>
<span class="n">settings</span><span class="o">.</span><span class="n">register_profile</span><span class="p">(</span><span class="s2">&quot;dev&quot;</span><span class="p">,</span> <span class="n">max_examples</span><span class="o">=</span><span class="mi">10</span><span class="p">,</span> <span class="n">verbosity</span><span class="o">=</span><span class="n">Verbosity</span><span class="o">.</span><span class="n">verbose</span><span class="p">)</span>
<span class="n">settings</span><span class="o">.</span><span class="n">register_profile</span><span class="p">(</span><span class="s2">&quot;ci&quot;</span><span class="p">,</span> <span class="n">max_examples</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">verbosity</span><span class="o">=</span><span class="n">Verbosity</span><span class="o">.</span><span class="n">normal</span><span class="p">)</span>
<span class="n">settings</span><span class="o">.</span><span class="n">load_profile</span><span class="p">(</span><span class="s2">&quot;dev&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="stateful-testing">
<h3>Stateful Testing<a class="headerlink" href="#stateful-testing" title="Link to this heading"></a></h3>
<p>Test sequences of operations:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">hypothesis.stateful</span> <span class="kn">import</span> <span class="n">RuleBasedStateMachine</span><span class="p">,</span> <span class="n">rule</span><span class="p">,</span> <span class="n">invariant</span>
<span class="kn">import</span> <span class="nn">hypothesis.strategies</span> <span class="k">as</span> <span class="nn">st</span>

<span class="k">class</span> <span class="nc">FileInjectionStateMachine</span><span class="p">(</span><span class="n">RuleBasedStateMachine</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Stateful testing for file injection service.&quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">injections</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">service</span> <span class="o">=</span> <span class="n">FileInjectionService</span><span class="p">()</span>

    <span class="nd">@rule</span><span class="p">(</span><span class="n">filename</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(</span><span class="n">min_size</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">max_size</span><span class="o">=</span><span class="mi">50</span><span class="p">))</span>
    <span class="k">def</span> <span class="nf">create_injection</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">filename</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Rule: create a new injection.&quot;&quot;&quot;</span>
        <span class="n">injection_id</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">service</span><span class="o">.</span><span class="n">create_injection</span><span class="p">({</span>
            <span class="s2">&quot;filename&quot;</span><span class="p">:</span> <span class="n">filename</span><span class="p">,</span>
            <span class="s2">&quot;target_path&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;/app/</span><span class="si">{</span><span class="n">filename</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
            <span class="s2">&quot;permissions&quot;</span><span class="p">:</span> <span class="s2">&quot;0755&quot;</span>
        <span class="p">})</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">injections</span><span class="p">[</span><span class="n">injection_id</span><span class="p">]</span> <span class="o">=</span> <span class="n">filename</span>

    <span class="nd">@rule</span><span class="p">()</span>
    <span class="k">def</span> <span class="nf">list_injections</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Rule: list all injections.&quot;&quot;&quot;</span>
        <span class="n">result</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">service</span><span class="o">.</span><span class="n">list_injections</span><span class="p">()</span>
        <span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">result</span><span class="p">)</span> <span class="o">==</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">injections</span><span class="p">)</span>

    <span class="nd">@invariant</span><span class="p">()</span>
    <span class="k">def</span> <span class="nf">injection_count_consistent</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Invariant: injection count should be consistent.&quot;&quot;&quot;</span>
        <span class="n">stored_count</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">service</span><span class="o">.</span><span class="n">list_injections</span><span class="p">())</span>
        <span class="n">tracked_count</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">injections</span><span class="p">)</span>
        <span class="k">assert</span> <span class="n">stored_count</span> <span class="o">==</span> <span class="n">tracked_count</span>

<span class="c1"># Run the state machine</span>
<span class="n">TestFileInjectionStateMachine</span> <span class="o">=</span> <span class="n">FileInjectionStateMachine</span><span class="o">.</span><span class="n">TestCase</span>
</pre></div>
</div>
</section>
</section>
<section id="running-property-based-tests">
<h2>Running Property-Based Tests<a class="headerlink" href="#running-property-based-tests" title="Link to this heading"></a></h2>
<section id="basic-execution">
<h3>Basic Execution<a class="headerlink" href="#basic-execution" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run all property-based tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/property/<span class="w"> </span>-v

<span class="c1"># Run with Hypothesis statistics</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/property/<span class="w"> </span>--hypothesis-show-statistics

<span class="c1"># Run specific property test</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/property/test_property_based.py::TestFileInjectionProperties::test_file_injection_create_roundtrip<span class="w"> </span>-v
</pre></div>
</div>
</section>
<section id="debugging-failures">
<h3>Debugging Failures<a class="headerlink" href="#debugging-failures" title="Link to this heading"></a></h3>
<p>When Hypothesis finds a failing case, it provides detailed information:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run with verbose Hypothesis output</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/property/<span class="w"> </span>-v<span class="w"> </span>-s<span class="w"> </span>--hypothesis-verbosity<span class="o">=</span>verbose

<span class="c1"># Reproduce a specific failure</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/property/<span class="w"> </span>--hypothesis-seed<span class="o">=</span><span class="m">12345</span>
</pre></div>
</div>
<p>Example failure output:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>FAILED tests/property/test_property_based.py::test_filename_validation - hypothesis.errors.Flaky

Falsifying example: test_filename_validation(filename=&#39;/&#39;)

This test appears to be flaky. It passed when Hypothesis tried to shrink the
failing example, but failed on the original input.
</pre></div>
</div>
</section>
<section id="hypothesis-database">
<h3>Hypothesis Database<a class="headerlink" href="#hypothesis-database" title="Link to this heading"></a></h3>
<p>Hypothesis saves failing examples in a database for regression testing:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># View the Hypothesis database</span>
ls<span class="w"> </span>.hypothesis/examples/

<span class="c1"># Clear the database (start fresh)</span>
rm<span class="w"> </span>-rf<span class="w"> </span>.hypothesis/

<span class="c1"># Run without using saved examples</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/property/<span class="w"> </span>--hypothesis-database<span class="o">=</span>none
</pre></div>
</div>
</section>
<section id="configuration">
<h3>Configuration<a class="headerlink" href="#configuration" title="Link to this heading"></a></h3>
<p>Configure Hypothesis in <code class="docutils literal notranslate"><span class="pre">tests/conftest.py</span></code>:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">hypothesis</span> <span class="kn">import</span> <span class="n">settings</span><span class="p">,</span> <span class="n">Verbosity</span><span class="p">,</span> <span class="n">HealthCheck</span>

<span class="c1"># Development profile - fast feedback</span>
<span class="n">settings</span><span class="o">.</span><span class="n">register_profile</span><span class="p">(</span>
    <span class="s2">&quot;dev&quot;</span><span class="p">,</span>
    <span class="n">max_examples</span><span class="o">=</span><span class="mi">10</span><span class="p">,</span>
    <span class="n">verbosity</span><span class="o">=</span><span class="n">Verbosity</span><span class="o">.</span><span class="n">verbose</span><span class="p">,</span>
    <span class="n">deadline</span><span class="o">=</span><span class="mi">1000</span><span class="p">,</span>  <span class="c1"># 1 second per example</span>
    <span class="n">suppress_health_check</span><span class="o">=</span><span class="p">[</span><span class="n">HealthCheck</span><span class="o">.</span><span class="n">too_slow</span><span class="p">]</span>
<span class="p">)</span>

<span class="c1"># CI profile - thorough testing</span>
<span class="n">settings</span><span class="o">.</span><span class="n">register_profile</span><span class="p">(</span>
    <span class="s2">&quot;ci&quot;</span><span class="p">,</span>
    <span class="n">max_examples</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span>
    <span class="n">verbosity</span><span class="o">=</span><span class="n">Verbosity</span><span class="o">.</span><span class="n">normal</span><span class="p">,</span>
    <span class="n">deadline</span><span class="o">=</span><span class="mi">5000</span><span class="p">,</span>  <span class="c1"># 5 seconds per example</span>
<span class="p">)</span>

<span class="c1"># Load appropriate profile</span>
<span class="kn">import</span> <span class="nn">os</span>
<span class="n">profile</span> <span class="o">=</span> <span class="s2">&quot;ci&quot;</span> <span class="k">if</span> <span class="n">os</span><span class="o">.</span><span class="n">getenv</span><span class="p">(</span><span class="s2">&quot;CI&quot;</span><span class="p">)</span> <span class="k">else</span> <span class="s2">&quot;dev&quot;</span>
<span class="n">settings</span><span class="o">.</span><span class="n">load_profile</span><span class="p">(</span><span class="n">profile</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="writing-good-properties">
<h3>Writing Good Properties<a class="headerlink" href="#writing-good-properties" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Think in terms of invariants</strong> - What should always be true?</p></li>
<li><p><strong>Test relationships</strong> - How do inputs relate to outputs?</p></li>
<li><p><strong>Use roundtrip properties</strong> - Serialize/deserialize should preserve data</p></li>
<li><p><strong>Test error conditions</strong> - What should cause failures?</p></li>
</ol>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="nd">@given</span><span class="p">(</span><span class="n">data</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">dictionaries</span><span class="p">(</span><span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(),</span> <span class="n">st</span><span class="o">.</span><span class="n">integers</span><span class="p">()))</span>
<span class="k">def</span> <span class="nf">test_json_roundtrip_property</span><span class="p">(</span><span class="n">data</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Property: JSON roundtrip should preserve data.&quot;&quot;&quot;</span>
    <span class="c1"># Serialize to JSON</span>
    <span class="n">json_str</span> <span class="o">=</span> <span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>

    <span class="c1"># Deserialize from JSON</span>
    <span class="n">recreated</span> <span class="o">=</span> <span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="n">json_str</span><span class="p">)</span>

    <span class="c1"># Property: original data should equal recreated data</span>
    <span class="k">assert</span> <span class="n">recreated</span> <span class="o">==</span> <span class="n">data</span>
</pre></div>
</div>
</section>
<section id="strategy-design">
<h3>Strategy Design<a class="headerlink" href="#strategy-design" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Start simple</strong> - Begin with basic strategies</p></li>
<li><p><strong>Add constraints gradually</strong> - Refine strategies based on failures</p></li>
<li><p><strong>Use realistic data</strong> - Generate data similar to production</p></li>
<li><p><strong>Handle edge cases</strong> - Include boundary conditions</p></li>
</ol>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Evolution of a filename strategy</span>

<span class="c1"># Too simple - generates invalid filenames</span>
<span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">()</span>

<span class="c1"># Better - excludes some problematic characters</span>
<span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">()</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x</span><span class="p">:</span> <span class="s1">&#39;/&#39;</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">x</span><span class="p">)</span>

<span class="c1"># Good - realistic filenames with proper constraints</span>
<span class="n">st</span><span class="o">.</span><span class="n">text</span><span class="p">(</span>
    <span class="n">min_size</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
    <span class="n">max_size</span><span class="o">=</span><span class="mi">255</span><span class="p">,</span>
    <span class="n">alphabet</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">characters</span><span class="p">(</span>
        <span class="n">whitelist_categories</span><span class="o">=</span><span class="p">(</span><span class="s2">&quot;Lu&quot;</span><span class="p">,</span> <span class="s2">&quot;Ll&quot;</span><span class="p">,</span> <span class="s2">&quot;Nd&quot;</span><span class="p">),</span>
        <span class="n">whitelist_characters</span><span class="o">=</span><span class="s2">&quot;.-_&quot;</span>
    <span class="p">)</span>
<span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x</span><span class="p">:</span> <span class="ow">not</span> <span class="n">x</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;.&#39;</span><span class="p">)</span> <span class="ow">and</span> <span class="n">x</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span> <span class="o">==</span> <span class="n">x</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="performance-optimization">
<h3>Performance Optimization<a class="headerlink" href="#performance-optimization" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Use appropriate example counts</strong> - Balance thoroughness with speed</p></li>
<li><p><strong>Set reasonable deadlines</strong> - Prevent hanging tests</p></li>
<li><p><strong>Filter efficiently</strong> - Use <code class="docutils literal notranslate"><span class="pre">assume()</span></code> sparingly</p></li>
<li><p><strong>Cache expensive operations</strong> - Reuse computed values</p></li>
</ol>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Efficient filtering</span>
<span class="nd">@given</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">integers</span><span class="p">(</span><span class="n">min_value</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">max_value</span><span class="o">=</span><span class="mi">100</span><span class="p">))</span>  <span class="c1"># Better</span>
<span class="k">def</span> <span class="nf">test_positive_numbers</span><span class="p">(</span><span class="n">x</span><span class="p">):</span>
    <span class="k">assert</span> <span class="n">x</span> <span class="o">&gt;</span> <span class="mi">0</span>

<span class="c1"># Instead of:</span>
<span class="nd">@given</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="n">st</span><span class="o">.</span><span class="n">integers</span><span class="p">())</span>
<span class="k">def</span> <span class="nf">test_positive_numbers_slow</span><span class="p">(</span><span class="n">x</span><span class="p">):</span>
    <span class="n">assume</span><span class="p">(</span><span class="n">x</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Slower - rejects many examples</span>
    <span class="k">assert</span> <span class="n">x</span> <span class="o">&gt;</span> <span class="mi">0</span>
</pre></div>
</div>
</section>
</section>
<section id="integration-with-ci-cd">
<h2>Integration with CI/CD<a class="headerlink" href="#integration-with-ci-cd" title="Link to this heading"></a></h2>
<p>Property-based tests in continuous integration:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="c1"># GitHub Actions example</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Run Property-Based Tests</span>
<span class="w">  </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">    </span><span class="no">python -m pytest tests/property/ \</span>
<span class="w">      </span><span class="no">--hypothesis-profile=ci \</span>
<span class="w">      </span><span class="no">--hypothesis-show-statistics \</span>
<span class="w">      </span><span class="no">--junitxml=property-test-results.xml</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Upload Test Results</span>
<span class="w">  </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/upload-artifact@v3</span>
<span class="w">  </span><span class="nt">with</span><span class="p">:</span>
<span class="w">    </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">property-test-results</span>
<span class="w">    </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">property-test-results.xml</span>
</pre></div>
</div>
<section id="monitoring-and-metrics">
<h3>Monitoring and Metrics<a class="headerlink" href="#monitoring-and-metrics" title="Link to this heading"></a></h3>
<p>Track property-based test effectiveness:</p>
<ul class="simple">
<li><p><strong>Coverage of input space</strong> - How much of the possible input space is tested</p></li>
<li><p><strong>Shrinking efficiency</strong> - How well Hypothesis minimizes failing examples</p></li>
<li><p><strong>Test execution time</strong> - Balance between thoroughness and speed</p></li>
<li><p><strong>Failure discovery rate</strong> - How often new edge cases are found</p></li>
</ul>
</section>
</section>
<section id="next-steps">
<h2>Next Steps<a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>After mastering property-based testing:</p>
<ol class="arabic simple">
<li><p><strong>Performance Testing</strong> - <a class="reference internal" href="performance-testing.html"><span class="doc">Performance Testing</span></a></p></li>
<li><p><strong>Security Testing</strong> - <span class="xref std std-doc">security-testing</span></p></li>
<li><p><strong>Advanced Debugging</strong> - <span class="xref std std-doc">debugging</span></p></li>
<li><p><strong>Test Patterns</strong> - <span class="xref std std-doc">test-patterns</span></p></li>
</ol>
</section>
<section id="resources">
<h2>Resources<a class="headerlink" href="#resources" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference external" href="https://hypothesis.readthedocs.io/">Hypothesis Documentation</a></p></li>
<li><p><a class="reference external" href="https://hypothesis.works/articles/what-is-property-based-testing/">Property-Based Testing Patterns</a></p></li>
<li><p><a class="reference external" href="https://hypothesis.readthedocs.io/en/latest/data.html">Hypothesis Strategies Reference</a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="unit-testing.html" class="btn btn-neutral float-left" title="Unit Testing" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="performance-testing.html" class="btn btn-neutral float-right" title="Performance Testing" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, TurdParty Team.
      <span class="lastupdated">Last updated on Jan 01, 1980.
      </span></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>