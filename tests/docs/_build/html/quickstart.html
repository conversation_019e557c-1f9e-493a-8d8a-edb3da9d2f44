<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Quick Start Guide &mdash; TurdParty Testing Framework v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=67647e4a" />
      <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=67647e4a" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=8d563738"></script>
        <script src="_static/doctools.js?v=9a2dae69"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/custom.js?v=3a49a312"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Installation &amp; Setup" href="installation.html" />
    <link rel="prev" title="TurdParty Testing Framework Documentation" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            TurdParty Testing Framework
          </a>
              <div class="version">
                1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Quick Start</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Quick Start Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="#installation">Installation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#development-environment-setup">Development Environment Setup</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#running-your-first-tests">Running Your First Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#unit-tests">Unit Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="#property-based-tests">Property-Based Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance-benchmarks">Performance Benchmarks</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#using-the-modern-test-runner">Using the Modern Test Runner</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#test-coverage">Test Coverage</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#code-quality-checks">Code Quality Checks</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#linting-and-formatting">Linting and Formatting</a></li>
<li class="toctree-l3"><a class="reference internal" href="#type-checking">Type Checking</a></li>
<li class="toctree-l3"><a class="reference internal" href="#security-scanning">Security Scanning</a></li>
<li class="toctree-l3"><a class="reference internal" href="#pre-commit-hooks">Pre-commit Hooks</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#common-workflows">Common Workflows</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#development-workflow">Development Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="#testing-new-features">Testing New Features</a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance-testing">Performance Testing</a></li>
<li class="toctree-l3"><a class="reference internal" href="#load-testing">Load Testing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#configuration-examples">Configuration Examples</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#pytest-configuration">pytest Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#hypothesis-configuration">Hypothesis Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#ruff-configuration">Ruff Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#next-steps">Next Steps</a></li>
<li class="toctree-l2"><a class="reference internal" href="#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation &amp; Setup</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Core Testing</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="unit-testing.html">Unit Testing</a></li>
<li class="toctree-l1"><a class="reference internal" href="property-testing.html">Property-Based Testing</a></li>
<li class="toctree-l1"><a class="reference internal" href="performance-testing.html">Performance Testing</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">TurdParty Testing Framework</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Quick Start Guide</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/tenbahtsecurity/turdparty/blob/master/tests/docs/quickstart.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="quick-start-guide">
<h1>Quick Start Guide<a class="headerlink" href="#quick-start-guide" title="Link to this heading"></a></h1>
<p>This guide will get you up and running with the TurdParty testing framework in minutes.</p>
<section id="prerequisites">
<h2>Prerequisites<a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h2>
<p>Before you begin, ensure you have:</p>
<ul class="simple">
<li><p>Python 3.11 or higher</p></li>
<li><p>Git for version control</p></li>
<li><p>Docker (optional, for integration tests)</p></li>
<li><p>Nix (recommended for development environment)</p></li>
</ul>
</section>
<section id="installation">
<h2>Installation<a class="headerlink" href="#installation" title="Link to this heading"></a></h2>
<section id="development-environment-setup">
<h3>Development Environment Setup<a class="headerlink" href="#development-environment-setup" title="Link to this heading"></a></h3>
<p><strong>Option 1: Using Nix (Recommended)</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Enter the Nix development shell</span>
<span class="nb">cd</span><span class="w"> </span>turdparty-collab
nix-shell

<span class="c1"># All testing tools are now available:</span>
<span class="c1"># pytest, hypothesis, ruff, mypy, bandit, locust, etc.</span>
</pre></div>
</div>
<p><strong>Option 2: Using pip</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Install testing dependencies</span>
pip<span class="w"> </span>install<span class="w"> </span>-r<span class="w"> </span>requirements-dev.txt

<span class="c1"># Or install specific tools</span>
pip<span class="w"> </span>install<span class="w"> </span>pytest<span class="w"> </span>pytest-benchmark<span class="w"> </span>hypothesis<span class="w"> </span>locust<span class="w"> </span>ruff<span class="w"> </span>mypy<span class="w"> </span>bandit
</pre></div>
</div>
<p><strong>Option 3: Using the project’s pyproject.toml</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Install with development dependencies</span>
pip<span class="w"> </span>install<span class="w"> </span>-e<span class="w"> </span><span class="s2">&quot;.[dev]&quot;</span>
</pre></div>
</div>
</section>
</section>
<section id="running-your-first-tests">
<h2>Running Your First Tests<a class="headerlink" href="#running-your-first-tests" title="Link to this heading"></a></h2>
<section id="unit-tests">
<h3>Unit Tests<a class="headerlink" href="#unit-tests" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run all unit tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/<span class="w"> </span>-v

<span class="c1"># Expected output:</span>
<span class="c1"># ======== test session starts ========</span>
<span class="c1"># tests/unit/test_basic.py ............ [ 33%]</span>
<span class="c1"># tests/unit/test_models_validation.py ........................ [100%]</span>
<span class="c1"># ======== 36 passed in 0.06s ========</span>
</pre></div>
</div>
</section>
<section id="property-based-tests">
<h3>Property-Based Tests<a class="headerlink" href="#property-based-tests" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run property-based tests with Hypothesis</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/property/<span class="w"> </span>-v

<span class="c1"># Expected output:</span>
<span class="c1"># ======== test session starts ========</span>
<span class="c1"># tests/property/test_property_based.py ......... [100%]</span>
<span class="c1"># ======== 9 passed in 1.02s ========</span>
</pre></div>
</div>
</section>
<section id="performance-benchmarks">
<h3>Performance Benchmarks<a class="headerlink" href="#performance-benchmarks" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run performance benchmarks</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/performance/test_benchmarks.py::TestModelPerformance::test_file_injection_create_performance<span class="w"> </span>--benchmark-only<span class="w"> </span>-v

<span class="c1"># Expected output:</span>
<span class="c1"># Name (time in us)                              Min      Max     Mean</span>
<span class="c1"># test_file_injection_create_performance      0.6400   2.1000   0.6500</span>
</pre></div>
</div>
</section>
</section>
<section id="using-the-modern-test-runner">
<h2>Using the Modern Test Runner<a class="headerlink" href="#using-the-modern-test-runner" title="Link to this heading"></a></h2>
<p>Our custom test runner provides a unified interface for all testing operations:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Show all available commands</span>
./scripts/test_runner.sh<span class="w"> </span><span class="nb">help</span>

<span class="c1"># Run complete test suite</span>
./scripts/test_runner.sh<span class="w"> </span>all

<span class="c1"># Run specific test types</span>
./scripts/test_runner.sh<span class="w"> </span>unit<span class="w">         </span><span class="c1"># Unit tests only</span>
./scripts/test_runner.sh<span class="w"> </span>property<span class="w">    </span><span class="c1"># Property-based tests</span>
./scripts/test_runner.sh<span class="w"> </span>performance<span class="w"> </span><span class="c1"># Performance benchmarks</span>
./scripts/test_runner.sh<span class="w"> </span>security<span class="w">    </span><span class="c1"># Security tests</span>
./scripts/test_runner.sh<span class="w"> </span>lint<span class="w">        </span><span class="c1"># Code quality checks</span>
</pre></div>
</div>
<section id="test-coverage">
<h3>Test Coverage<a class="headerlink" href="#test-coverage" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run tests with coverage reporting</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/<span class="w"> </span>--cov<span class="o">=</span>api<span class="w"> </span>--cov<span class="o">=</span>services<span class="w"> </span>--cov-report<span class="o">=</span>html<span class="w"> </span>--cov-report<span class="o">=</span>term-missing

<span class="c1"># View HTML coverage report</span>
open<span class="w"> </span>htmlcov/index.html
</pre></div>
</div>
</section>
</section>
<section id="code-quality-checks">
<h2>Code Quality Checks<a class="headerlink" href="#code-quality-checks" title="Link to this heading"></a></h2>
<section id="linting-and-formatting">
<h3>Linting and Formatting<a class="headerlink" href="#linting-and-formatting" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check code quality with Ruff</span>
ruff<span class="w"> </span>check<span class="w"> </span>.

<span class="c1"># Auto-fix issues</span>
ruff<span class="w"> </span>check<span class="w"> </span>.<span class="w"> </span>--fix

<span class="c1"># Format code</span>
ruff<span class="w"> </span>format<span class="w"> </span>.
</pre></div>
</div>
</section>
<section id="type-checking">
<h3>Type Checking<a class="headerlink" href="#type-checking" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run static type checking</span>
mypy<span class="w"> </span>api/<span class="w"> </span>services/
</pre></div>
</div>
</section>
<section id="security-scanning">
<h3>Security Scanning<a class="headerlink" href="#security-scanning" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Scan for security vulnerabilities</span>
bandit<span class="w"> </span>-r<span class="w"> </span>api/<span class="w"> </span>services/

<span class="c1"># Check dependencies for known vulnerabilities</span>
safety<span class="w"> </span>check
</pre></div>
</div>
</section>
<section id="pre-commit-hooks">
<h3>Pre-commit Hooks<a class="headerlink" href="#pre-commit-hooks" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Install pre-commit hooks</span>
pre-commit<span class="w"> </span>install

<span class="c1"># Run hooks manually</span>
pre-commit<span class="w"> </span>run<span class="w"> </span>--all-files
</pre></div>
</div>
</section>
</section>
<section id="common-workflows">
<h2>Common Workflows<a class="headerlink" href="#common-workflows" title="Link to this heading"></a></h2>
<section id="development-workflow">
<h3>Development Workflow<a class="headerlink" href="#development-workflow" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># 1. Make code changes</span>
vim<span class="w"> </span>api/models/file_injection.py

<span class="c1"># 2. Run relevant tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/test_models_validation.py<span class="w"> </span>-v

<span class="c1"># 3. Check code quality</span>
ruff<span class="w"> </span>check<span class="w"> </span>.<span class="w"> </span>--fix
ruff<span class="w"> </span>format<span class="w"> </span>.

<span class="c1"># 4. Run full test suite</span>
./scripts/test_runner.sh<span class="w"> </span>unit

<span class="c1"># 5. Commit changes (pre-commit hooks run automatically)</span>
git<span class="w"> </span>add<span class="w"> </span>.
git<span class="w"> </span>commit<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;feat: improve file injection model validation&quot;</span>
</pre></div>
</div>
</section>
<section id="testing-new-features">
<h3>Testing New Features<a class="headerlink" href="#testing-new-features" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># 1. Write failing test first (TDD)</span>
vim<span class="w"> </span>tests/unit/test_new_feature.py

<span class="c1"># 2. Run the failing test</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/test_new_feature.py<span class="w"> </span>-v

<span class="c1"># 3. Implement the feature</span>
vim<span class="w"> </span>api/services/new_feature.py

<span class="c1"># 4. Run test until it passes</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/test_new_feature.py<span class="w"> </span>-v

<span class="c1"># 5. Add property-based tests for edge cases</span>
vim<span class="w"> </span>tests/property/test_new_feature_properties.py

<span class="c1"># 6. Run comprehensive test suite</span>
./scripts/test_runner.sh<span class="w"> </span>all
</pre></div>
</div>
</section>
<section id="performance-testing">
<h3>Performance Testing<a class="headerlink" href="#performance-testing" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># 1. Write performance test</span>
vim<span class="w"> </span>tests/performance/test_new_feature_performance.py

<span class="c1"># 2. Run benchmark</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/performance/test_new_feature_performance.py<span class="w"> </span>--benchmark-only

<span class="c1"># 3. Compare with baseline</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/performance/<span class="w"> </span>--benchmark-compare<span class="o">=</span>baseline.json
</pre></div>
</div>
</section>
<section id="load-testing">
<h3>Load Testing<a class="headerlink" href="#load-testing" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># 1. Start the application</span>
docker-compose<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># 2. Run load tests</span>
locust<span class="w"> </span>-f<span class="w"> </span>tests/load/locustfile.py<span class="w"> </span>--headless<span class="w"> </span>-u<span class="w"> </span><span class="m">10</span><span class="w"> </span>-r<span class="w"> </span><span class="m">2</span><span class="w"> </span>-t<span class="w"> </span>30s<span class="w"> </span>--host<span class="o">=</span>http://localhost:8000

<span class="c1"># 3. View results</span>
open<span class="w"> </span>load-test-report.html
</pre></div>
</div>
</section>
</section>
<section id="configuration-examples">
<h2>Configuration Examples<a class="headerlink" href="#configuration-examples" title="Link to this heading"></a></h2>
<section id="pytest-configuration">
<h3>pytest Configuration<a class="headerlink" href="#pytest-configuration" title="Link to this heading"></a></h3>
<p>Create or update <code class="docutils literal notranslate"><span class="pre">pyproject.toml</span></code>:</p>
<div class="highlight-toml notranslate"><div class="highlight"><pre><span></span><span class="k">[tool.pytest.ini_options]</span>
<span class="n">minversion</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s2">&quot;7.0&quot;</span>
<span class="n">testpaths</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;tests&quot;</span><span class="p">]</span>
<span class="n">markers</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;unit: Unit tests&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;property: Property-based tests&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;performance: Performance tests&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;security: Security tests&quot;</span><span class="p">,</span>
<span class="p">]</span>
</pre></div>
</div>
</section>
<section id="hypothesis-configuration">
<h3>Hypothesis Configuration<a class="headerlink" href="#hypothesis-configuration" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># In tests/conftest.py</span>
<span class="kn">from</span> <span class="nn">hypothesis</span> <span class="kn">import</span> <span class="n">settings</span><span class="p">,</span> <span class="n">Verbosity</span>

<span class="c1"># Configure Hypothesis globally</span>
<span class="n">settings</span><span class="o">.</span><span class="n">register_profile</span><span class="p">(</span><span class="s2">&quot;dev&quot;</span><span class="p">,</span> <span class="n">max_examples</span><span class="o">=</span><span class="mi">10</span><span class="p">,</span> <span class="n">verbosity</span><span class="o">=</span><span class="n">Verbosity</span><span class="o">.</span><span class="n">verbose</span><span class="p">)</span>
<span class="n">settings</span><span class="o">.</span><span class="n">register_profile</span><span class="p">(</span><span class="s2">&quot;ci&quot;</span><span class="p">,</span> <span class="n">max_examples</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">verbosity</span><span class="o">=</span><span class="n">Verbosity</span><span class="o">.</span><span class="n">normal</span><span class="p">)</span>
<span class="n">settings</span><span class="o">.</span><span class="n">load_profile</span><span class="p">(</span><span class="s2">&quot;dev&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="ruff-configuration">
<h3>Ruff Configuration<a class="headerlink" href="#ruff-configuration" title="Link to this heading"></a></h3>
<div class="highlight-toml notranslate"><div class="highlight"><pre><span></span><span class="c1"># In pyproject.toml</span>
<span class="k">[tool.ruff]</span>
<span class="n">target-version</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s2">&quot;py311&quot;</span>
<span class="n">line-length</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">88</span>

<span class="k">[tool.ruff.lint]</span>
<span class="n">select</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;E&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;W&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;F&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;I&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;B&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;UP&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;PL&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;RUF&quot;</span><span class="p">]</span>
<span class="n">ignore</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;E501&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;B008&quot;</span><span class="p">]</span>
</pre></div>
</div>
</section>
</section>
<section id="next-steps">
<h2>Next Steps<a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>Now that you have the basics working, explore these advanced topics:</p>
<ol class="arabic simple">
<li><p><strong>Writing Effective Tests</strong> - <a class="reference internal" href="unit-testing.html"><span class="doc">Unit Testing</span></a></p></li>
<li><p><strong>Property-Based Testing</strong> - <a class="reference internal" href="property-testing.html"><span class="doc">Property-Based Testing</span></a></p></li>
<li><p><strong>Performance Optimization</strong> - <a class="reference internal" href="performance-testing.html"><span class="doc">Performance Testing</span></a></p></li>
<li><p><strong>Security Validation</strong> - <span class="xref std std-doc">security-testing</span></p></li>
<li><p><strong>CI/CD Integration</strong> - <span class="xref std std-doc">ci-cd</span></p></li>
</ol>
</section>
<section id="troubleshooting">
<h2>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<section id="common-issues">
<h3>Common Issues<a class="headerlink" href="#common-issues" title="Link to this heading"></a></h3>
<p><strong>Import Errors</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Ensure PYTHONPATH is set correctly</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">PYTHONPATH</span><span class="o">=</span><span class="s2">&quot;</span><span class="nv">$PWD</span><span class="s2">:</span><span class="nv">$PYTHONPATH</span><span class="s2">&quot;</span>
</pre></div>
</div>
<p><strong>Permission Errors</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Make scripts executable</span>
chmod<span class="w"> </span>+x<span class="w"> </span>scripts/test_runner.sh
</pre></div>
</div>
<p><strong>Missing Dependencies</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Reinstall development dependencies</span>
pip<span class="w"> </span>install<span class="w"> </span>-e<span class="w"> </span><span class="s2">&quot;.[dev]&quot;</span>
</pre></div>
</div>
<p><strong>Test Failures</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run with verbose output for debugging</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/failing_test.py<span class="w"> </span>-v<span class="w"> </span>-s<span class="w"> </span>--tb<span class="o">=</span>long
</pre></div>
</div>
</section>
<section id="getting-help">
<h3>Getting Help<a class="headerlink" href="#getting-help" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>📖 Read the full documentation</p></li>
<li><p>🐛 Check existing GitHub issues</p></li>
<li><p>💬 Ask questions in discussions</p></li>
<li><p>🤝 Contribute improvements</p></li>
</ul>
<div class="admonition tip">
<p class="admonition-title">Tip</p>
<p>Use the <code class="docutils literal notranslate"><span class="pre">--help</span></code> flag with any command to see available options:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>--help
./scripts/test_runner.sh<span class="w"> </span><span class="nb">help</span>
ruff<span class="w"> </span>--help
</pre></div>
</div>
</div>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="TurdParty Testing Framework Documentation" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="installation.html" class="btn btn-neutral float-right" title="Installation &amp; Setup" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, TurdParty Team.
      <span class="lastupdated">Last updated on Jan 01, 1980.
      </span></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>