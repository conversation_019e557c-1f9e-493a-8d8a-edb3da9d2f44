Search.setIndex({"alltitles": {"API Reference": [[0, "api-reference"]], "Advanced Hypothesis Features": [[3, "advanced-hypothesis-features"]], "Advanced Load Testing": [[2, "advanced-load-testing"]], "Advanced Topics": [[0, "advanced-topics"]], "Architecture Overview": [[0, "architecture-overview"]], "Assertion Strategies": [[5, "assertion-strategies"]], "Assumptions": [[3, "assumptions"]], "Async Performance Testing": [[2, "async-performance-testing"]], "Basic Execution": [[2, "basic-execution"], [3, "basic-execution"], [5, "basic-execution"]], "Basic Functionality Tests": [[5, "basic-functionality-tests"]], "Basic Strategies": [[3, "basic-strategies"]], "Benchmark Comparison": [[2, "benchmark-comparison"]], "Benchmark Design": [[2, "benchmark-design"]], "Best Practices": [[2, "best-practices"], [3, "best-practices"], [5, "best-practices"]], "Code Quality Checks": [[4, "code-quality-checks"]], "Common Fixtures": [[5, "common-fixtures"]], "Common Issues": [[1, "common-issues"], [4, "common-issues"]], "Common Workflows": [[4, "common-workflows"]], "Complex Performance Scenarios": [[2, "complex-performance-scenarios"]], "Composite Strategies": [[3, "composite-strategies"]], "Configuration": [[1, "configuration"], [3, "configuration"]], "Configuration Examples": [[4, "configuration-examples"]], "Continuous Integration": [[5, "continuous-integration"]], "Continuous Monitoring": [[2, "continuous-monitoring"]], "Continuous Performance Monitoring": [[2, "continuous-performance-monitoring"]], "Contributing": [[0, null]], "Core Concepts": [[3, "core-concepts"]], "Core Testing": [[0, null]], "Current Performance Metrics": [[2, "current-performance-metrics"]], "Current Test Status": [[0, "id1"]], "Custom Strategies": [[3, "custom-strategies"]], "Data Integrity Properties": [[3, "data-integrity-properties"]], "Debugging Failures": [[3, "debugging-failures"]], "Debugging Tests": [[5, "debugging-tests"]], "Development": [[0, "development"]], "Development Environment": [[1, "development-environment"]], "Development Environment Setup": [[4, "development-environment-setup"]], "Development Workflow": [[4, "development-workflow"]], "Docker Services (Optional)": [[1, "docker-services-optional"]], "Environment Setup": [[1, "environment-setup"]], "Example Test Structure": [[5, "example-test-structure"]], "Examples & Tutorials": [[0, "examples-tutorials"]], "Examples and Targeting": [[3, "examples-and-targeting"]], "Fast Test Execution": [[5, "fast-test-execution"]], "File Injection Model Properties": [[3, "file-injection-model-properties"]], "Fixtures and Test Data": [[5, "fixtures-and-test-data"]], "Getting Help": [[1, "getting-help"], [4, "getting-help"]], "Getting Started": [[0, "getting-started"]], "Hypothesis Configuration": [[4, "hypothesis-configuration"]], "Hypothesis Database": [[3, "hypothesis-database"]], "Hypothesis Strategies": [[3, "hypothesis-strategies"]], "IDE Configuration": [[1, "ide-configuration"]], "Indices and tables": [[0, "indices-and-tables"]], "Installation": [[4, "installation"]], "Installation & Setup": [[1, null]], "Installation Methods": [[1, "installation-methods"]], "Integration with CI/CD": [[3, "integration-with-ci-cd"]], "Key Features": [[0, "key-features"]], "Key Testing Patterns": [[5, "key-testing-patterns"]], "Linting and Formatting": [[4, "linting-and-formatting"]], "Load Test Strategy": [[2, "load-test-strategy"]], "Load Testing": [[4, "load-testing"]], "Load Testing with Locust": [[2, "load-testing-with-locust"]], "Locust Configuration": [[2, "locust-configuration"]], "Memory Monitoring": [[2, "memory-monitoring"]], "Memory Profiling": [[2, "memory-profiling"]], "Memory Usage Testing": [[2, "memory-usage-testing"]], "Method 1: Nix Development Environment (Recommended)": [[1, "method-1-nix-development-environment-recommended"]], "Method 2: pip Installation": [[1, "method-2-pip-installation"]], "Method 3: Development Dependencies Only": [[1, "method-3-development-dependencies-only"]], "Micro-Benchmarking with pytest-benchmark": [[2, "micro-benchmarking-with-pytest-benchmark"]], "Model Performance Testing": [[2, "model-performance-testing"]], "Model Validation Tests": [[5, "model-validation-tests"]], "Monitoring Test Performance": [[5, "monitoring-test-performance"]], "Monitoring and Metrics": [[3, "monitoring-and-metrics"]], "Need Help?": [[0, null]], "Next Steps": [[1, "next-steps"], [2, "next-steps"], [3, "next-steps"], [4, "next-steps"], [5, "next-steps"]], "Nix Environment Tools": [[1, "id1"]], "Our Property-Based Tests": [[3, "our-property-based-tests"]], "Output Formats": [[2, "output-formats"]], "Overview": [[2, "overview"], [3, "overview"], [5, "overview"]], "Parallel Execution": [[5, "parallel-execution"]], "Parameterized Performance Tests": [[2, "parameterized-performance-tests"]], "Performance Alerts": [[2, "performance-alerts"]], "Performance Benchmarks": [[2, "id1"], [4, "performance-benchmarks"]], "Performance Considerations": [[5, "performance-considerations"]], "Performance Optimization": [[3, "performance-optimization"]], "Performance Regression Testing": [[2, "performance-regression-testing"]], "Performance Testing": [[2, null], [4, "performance-testing"]], "Performance Thresholds": [[2, "performance-thresholds"]], "Platform-Specific Notes": [[1, "platform-specific-notes"]], "Pre-commit Hooks": [[4, "pre-commit-hooks"]], "Pre-commit Hooks Setup": [[1, "pre-commit-hooks-setup"]], "Prerequisites": [[1, "prerequisites"], [4, "prerequisites"]], "Property-Based Testing": [[3, null]], "Property-Based Tests": [[4, "property-based-tests"]], "Pydantic Model Testing": [[5, "pydantic-model-testing"]], "Quality Gates": [[5, "quality-gates"]], "Quick Commands": [[0, "quick-commands"]], "Quick Start": [[0, null]], "Quick Start Guide": [[4, null]], "Resources": [[3, "resources"]], "Ruff Configuration": [[4, "ruff-configuration"]], "Running Load Tests": [[2, "running-load-tests"]], "Running Marked Tests": [[5, "running-marked-tests"]], "Running Performance Tests": [[2, "running-performance-tests"]], "Running Property-Based Tests": [[3, "running-property-based-tests"]], "Running Unit Tests": [[5, "running-unit-tests"]], "Running Your First Tests": [[4, "running-your-first-tests"]], "Security Scanning": [[4, "security-scanning"]], "Settings and Profiles": [[3, "settings-and-profiles"]], "Shell Aliases": [[1, "shell-aliases"]], "Stateful Testing": [[3, "stateful-testing"]], "Strategy Design": [[3, "strategy-design"]], "Support & Contributing": [[0, "support-contributing"]], "Supported Platforms": [[1, "supported-platforms"]], "System Requirements": [[1, "system-requirements"]], "Test All Components": [[1, "test-all-components"]], "Test Coverage": [[4, "test-coverage"]], "Test Data Management": [[5, "test-data-management"]], "Test Installation": [[1, "test-installation"]], "Test Markers": [[5, "test-markers"]], "Test Organization": [[5, "test-organization"]], "Test Results Dashboard": [[0, "test-results-dashboard"]], "Test Structure": [[2, "test-structure"], [3, "test-structure"], [5, "test-structure"]], "Testing Framework": [[0, "testing-framework"]], "Testing New Features": [[4, "testing-new-features"]], "Tool Verification": [[1, "tool-verification"]], "Tools & Configuration": [[0, "tools-configuration"]], "Traditional Testing vs Property-Based Testing": [[3, "traditional-testing-vs-property-based-testing"]], "Troubleshooting": [[1, "troubleshooting"], [4, "troubleshooting"]], "TurdParty Testing Framework Documentation": [[0, null]], "Type Checking": [[4, "type-checking"]], "Unit Testing": [[5, null]], "Unit Tests": [[4, "unit-tests"]], "User Scenarios": [[2, "user-scenarios"]], "Using Fixtures": [[5, "using-fixtures"]], "Using the Modern Test Runner": [[4, "using-the-modern-test-runner"]], "Validation Properties": [[3, "validation-properties"]], "Verification": [[1, "verification"]], "What is Property-Based Testing?": [[3, "what-is-property-based-testing"]], "With Coverage": [[5, "with-coverage"]], "Writing Good Properties": [[3, "writing-good-properties"]], "pytest Configuration": [[1, "pytest-configuration"], [4, "pytest-configuration"]]}, "docnames": ["index", "installation", "performance-testing", "property-testing", "quickstart", "unit-testing"], "envversion": {"sphinx": 62, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.todo": 2, "sphinx.ext.viewcode": 1}, "filenames": ["index.rst", "installation.rst", "performance-testing.rst", "property-testing.rst", "quickstart.rst", "unit-testing.rst"], "indexentries": {}, "objects": {}, "objnames": {}, "objtypes": {}, "terms": {"": [0, 1, 3, 4, 5], "0": [0, 1, 2, 3, 4, 5], "00": 2, "001": 2, "00z": 2, "01": 2, "0123456789abcdef": [3, 5], "01t10": 2, "02": [0, 3, 4], "03": 1, "04": 1, "06": [0, 4, 5], "0600": [3, 5], "0640": 3, "0644": [3, 5], "0700": 3, "0750": 3, "0755": [2, 3, 5], "0777": [3, 5], "1": [0, 2, 3, 4, 5], "10": [2, 3, 4, 5], "100": [2, 3, 4], "1000": [2, 3, 4], "10000": 3, "100000": 2, "1000000": [2, 3], "100kb": 2, "1024": [2, 5], "10240": 2, "102400": 2, "1048576": 2, "10\u03bc": 2, "11": [1, 2, 4], "12": [1, 5], "12345": 3, "15": 1, "16": 5, "18\u03bc": 2, "1kb": 2, "1m": 2, "1mb": 2, "2": [2, 4, 5], "20": 1, "200": [2, 3], "201": 2, "2024": [0, 2], "22": 2, "24": 5, "25": 5, "255": 3, "2f": 2, "2m": 2, "3": [2, 4, 5], "30": [2, 4], "33": 4, "36": [0, 4, 5], "3\u03bc": 2, "4": [1, 4, 5], "44": 2, "45": 2, "45k": 2, "484": 0, "5": [1, 2, 3, 4, 5], "50": [2, 3], "5000": 3, "50\u03bc": 2, "5432": 1, "54m": [0, 2], "6": [1, 4], "6379": 1, "64": [2, 3, 5], "6400": 4, "64\u03bc": 2, "6500": 4, "7": [1, 4], "7\u03bc": 2, "8": 1, "80": [1, 5], "8000": [1, 2, 4], "8089": 2, "850k": 2, "88": 4, "8m": 2, "9": [0, 3, 4, 5], "90": 0, "92": 1, "9200": 1, "95": 0, "9999": 2, "A": 2, "For": 1, "If": [1, 3], "In": [4, 5], "It": 3, "No": [2, 3, 5], "On": 1, "One": 5, "Or": [1, 4], "The": [1, 2, 5], "Then": 2, "These": 3, "_": [2, 3], "__init__": 3, "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3": 2, "aaa": 5, "abc123": 2, "abcdef": 3, "abcdefghijklmnopqrstuvwxyz": 3, "about": 2, "abov": 0, "accept": 3, "account": 2, "act": 5, "action": [2, 3, 5], "activ": 1, "actual": 2, "add": [1, 2, 3, 4], "add_listen": 2, "adequ": 5, "admin": 2, "adminus": 2, "advanc": 4, "after": [1, 2, 3, 5], "alia": 1, "all": [0, 2, 3, 4, 5], "all_success": 2, "allow": 3, "alphabet": 3, "alpin": 1, "alwai": 3, "an": [2, 3], "analysi": 2, "ani": [2, 3, 4, 5], "annot": 5, "api": [1, 2, 4, 5], "api_base_url": 1, "app": [2, 3, 5], "appear": 3, "append": 2, "applic": [1, 2, 4, 5], "approach": 5, "appropri": 3, "apt": 1, "ar": [1, 2, 3, 4, 5], "arrang": 5, "artifact": [2, 3], "ask": [1, 4], "aspect": 5, "assert": [2, 3], "assertionerror": 5, "assum": 3, "assur": 0, "async_process": 2, "asyncio": [1, 2], "attack": 0, "auto": [1, 4, 5], "autom": [0, 3], "automat": [3, 4, 5], "autoupd": 1, "avail": [1, 2, 4], "averag": 2, "average_s": 2, "avg_response_tim": 2, "avg_siz": 2, "avoid": 5, "await": 2, "b": [2, 4, 5], "b008": 4, "b018": 5, "balanc": 3, "bandit": [0, 1, 4], "base": [0, 1, 5], "base_cont": 2, "baselin": [2, 4], "bash": [2, 5], "basic": [0, 1, 4], "befor": 4, "begin": [2, 3, 4], "behavior": [2, 3, 5], "being": 2, "benchinfo": 2, "benchmark": [0, 1, 5], "benefit": 3, "best": [0, 1], "better": 3, "between": [2, 3], "bin": [1, 2, 5], "binari": 3, "boi": 1, "bool": 3, "boolean": 3, "bound": 3, "boundari": [3, 5], "break": [2, 3], "brew": 1, "builder": 5, "built": 0, "bulk": 2, "byte": 3, "c": [1, 3, 5], "cach": [0, 2, 3], "calcul": [2, 3, 5], "call": 2, "can": 3, "capac": 2, "captur": 5, "care": 2, "case": [0, 3, 4, 5], "catch_respons": 2, "categor": 5, "caus": 3, "cd": [0, 1, 2, 4, 5], "chang": [2, 3, 4], "char": [3, 5], "charact": 3, "characterist": 2, "check": [0, 1, 2], "check_health": 2, "check_health_detail": 2, "checkout": 2, "checksum": 2, "chmod": [1, 4], "ci": [0, 2, 4, 5], "class": [2, 3, 5], "clean": [0, 1, 2, 5], "cleanup": 5, "clear": [3, 5], "client": 2, "clone": 1, "code": [0, 1, 2, 3, 5], "codebas": 0, "codecov": 5, "collab": 4, "collect": [1, 3], "com": 1, "combin": 3, "command": [1, 4], "commit": 0, "common": [0, 2], "commun": 1, "compar": [2, 4, 5], "compared_benchinfo": 2, "comparison": 5, "compil": 2, "complet": [2, 3, 4, 5], "complex": 5, "complianc": 0, "compon": [0, 5], "compos": [1, 4], "comprehens": [0, 2, 3, 4, 5], "comput": 3, "concurr": [2, 3], "condit": [2, 3, 5], "config": 2, "conftest": [3, 4, 5], "consid": 2, "consider": 0, "consist": [2, 3, 5], "constant": 5, "constraint": 3, "contain": [3, 5], "content": [2, 5], "continu": [0, 3], "contribut": [1, 4], "control": [1, 4], "core": [1, 5], "correct": 2, "correctli": [4, 5], "count": [0, 3], "cov": [0, 1, 4, 5], "cover": [0, 1], "coverag": [0, 1, 3], "coverage_threshold": 1, "cpu": 2, "creat": [1, 2, 3, 4, 5], "create_file_injection_data": 5, "create_inject": [3, 5], "create_realistic_test_data": 2, "creation": [0, 2, 3, 5], "critic": 5, "cryptograph": 2, "curl": 1, "custom": [2, 4, 5], "d": [1, 4], "danger": 3, "data": [0, 2], "database_url": 1, "dataset": 2, "deadlin": 3, "debian": 1, "debug": [1, 2, 4], "debugg": 5, "decis": 2, "def": [2, 3, 5], "default": [3, 5], "defaultinterpreterpath": 1, "defin": [2, 3], "degrad": 2, "del": 2, "delai": 2, "delet": 5, "depend": [2, 4, 5], "deprecationwarn": 1, "descript": [2, 3, 5], "deseri": [3, 5], "design": [0, 5], "desktop": 1, "detail": [0, 1, 2, 3], "detect": 2, "dev": [1, 2, 3, 4], "develop": [3, 5], "dict": [2, 5], "dictionari": [2, 3], "differ": [2, 5], "digit": 3, "directori": 5, "discov": 3, "discoveri": [0, 1, 3], "discuss": [0, 4], "disk": 2, "divis": 3, "do": 3, "docker": [0, 4], "document": [1, 3, 4], "doesn": 2, "down": 1, "draw": 3, "driven": 0, "due": 2, "dump": 3, "durat": 5, "dure": [2, 5], "e": [1, 2, 4], "e501": 4, "each": 5, "easi": 5, "echo": 2, "edg": [0, 3, 4, 5], "effect": [3, 4], "effici": [0, 3, 5], "elasticsearch": 1, "els": [2, 3], "empti": [3, 5], "enabl": 1, "encod": 2, "encount": 1, "endpoint": 1, "ensur": [2, 4, 5], "enter": [1, 4], "env": 1, "environ": [0, 2, 5], "equal": 3, "error": [1, 3, 4, 5], "establish": 2, "etc": 4, "event": 2, "evolut": 3, "exact": 5, "exampl": 2, "exc_info": 5, "exceed": 2, "excel": 2, "except": [2, 3, 5], "exclud": 3, "execut": [0, 1, 4], "exist": [4, 5], "exit": 2, "expect": [1, 2, 3, 4, 5], "expens": 3, "experi": 1, "explor": [1, 4, 5], "export": [1, 4], "extern": [1, 5], "extrem": 5, "f": [1, 2, 3, 4, 5], "factori": [1, 5], "fail": [2, 3, 4, 5], "failing_test": [1, 4], "failur": [1, 2, 4, 5], "faker": 1, "fals": [1, 3, 5], "falsifi": 3, "fast": [0, 1, 3], "faster": [0, 5], "feat": 4, "featur": 1, "feedback": [0, 3, 5], "field": 5, "file": [0, 1, 2, 4, 5], "file_cont": 2, "file_count": 2, "file_info": 2, "file_inject": 4, "file_injection_data": 3, "fileinjectioncr": [2, 3, 5], "fileinjectionrespons": 5, "fileinjectionservic": [3, 5], "fileinjectionstatemachin": 3, "fileinjectionus": 2, "filenam": [2, 3, 5], "filename_strategi": 3, "filenotfounderror": 5, "filter": 3, "filter_too_much": 3, "filterwarn": 1, "final": [2, 5], "final_memori": 2, "find": 3, "first": 0, "fix": [0, 1, 3, 4, 5], "fixtur": 0, "flag": 4, "flaki": 3, "float": [2, 3], "flush": 5, "focu": 5, "follow": [2, 5], "forc": 1, "form": 5, "format": [0, 1], "found": [3, 5], "foundat": 5, "framework": [1, 2, 4], "frequent": 2, "fresh": 3, "from": [2, 3, 4, 5], "fssl": 1, "full": [0, 1, 4], "function": [0, 1, 2], "function_scoped_fixtur": 3, "fundament": 5, "futur": 3, "gather": 2, "gener": [2, 3], "get": 2, "getenv": 3, "getpid": 2, "git": [1, 4], "github": [0, 1, 2, 3, 4, 5], "given": 3, "global": [1, 3, 4], "got": 5, "gradual": [2, 3], "grep": 1, "group": 5, "guarante": 1, "guid": [0, 1, 3], "ha": 3, "handl": [2, 3, 5], "handler": 2, "hang": 3, "happen": 2, "hardcod": 5, "harden": 0, "has_newlin": 3, "has_null_byt": 3, "has_path_separ": 3, "hash": [2, 3, 5], "hash1": [2, 3], "hash2": [2, 3], "hash3": 3, "hashlib": [2, 3, 5], "have": [4, 5], "headless": [2, 4], "health": 2, "healthcheck": 3, "healthi": 2, "heavi": 2, "heavy_test_": 2, "heavyus": 2, "hello": 5, "help": 5, "hex": 3, "hexdigest": [2, 3, 5], "hidden": 3, "high": 5, "higher": 4, "histogram": 2, "hold": 3, "homebrew": 1, "host": [2, 4], "how": 3, "html": [0, 1, 2, 4, 5], "htmlcov": [4, 5], "http": [1, 2, 4], "httpuser": 2, "hypothesi": [0, 1], "i": [0, 1, 2, 4, 5], "id": 2, "ident": 3, "identif": 2, "ignor": [1, 4], "imag": 1, "immedi": 5, "implement": [0, 4], "import": [1, 2, 3, 4, 5], "improv": [0, 4], "includ": [1, 2, 3, 5], "increas": 2, "index": [0, 4, 5], "individu": [2, 5], "industri": 0, "info": 2, "inform": [1, 2, 3, 5], "infrastructur": 0, "ini_opt": [1, 4], "initi": 2, "initial_memori": 2, "inject": [2, 4, 5], "injection_count_consist": 3, "injection_id": [2, 3], "input": [2, 3, 5], "instal": [0, 2], "instead": 3, "int": [2, 3, 5], "integ": [2, 3], "integr": [0, 1, 2, 4], "intens": 2, "interact": [0, 2], "interest": 3, "interfac": 4, "interpret": 1, "invalid": [3, 5], "invari": 3, "investig": 2, "is_empti": 3, "is_valid": 2, "isinst": [3, 5], "isol": [0, 2, 5], "issu": 0, "item_id": 2, "iter": 2, "j": 2, "jit": 2, "job": 2, "join": [0, 1], "json": [1, 2, 3, 4, 5], "json_data": [3, 5], "json_str": 3, "junitxml": [3, 5], "just": [1, 5], "kei": 3, "known": 4, "kwarg": 2, "l": 3, "lambda": [2, 3], "larg": 2, "large_data": 2, "larger": 2, "last": 5, "latest": [1, 2], "layer": [3, 5], "len": [2, 3, 5], "length": [2, 4], "less": 2, "letter": 3, "lf": 5, "like": 2, "limit": [2, 5], "line": [2, 4], "lint": [0, 1, 5], "linux": 1, "list": [1, 2, 3, 5], "list_inject": 3, "ll": 3, "load": [0, 3], "load_profil": [3, 4], "local": 1, "localhost": [1, 2, 4], "locat": [2, 3], "locust": [0, 1, 4], "locustfil": [2, 4], "log": 1, "log_level": 1, "logic": 3, "long": [1, 3, 4], "longer": 2, "low": 2, "lower": 2, "lu": 3, "m": [0, 1, 2, 3, 4, 5], "machin": [2, 3], "machine_info": 2, "maco": 1, "mai": [1, 2], "main": 1, "maintain": [0, 2], "make": [1, 4, 5], "manag": 1, "mani": 3, "manipul": 2, "manner": 1, "manual": [1, 4], "map": 3, "mark": 2, "marker": [0, 1, 4], "master": [2, 3, 5], "match": 5, "max": [2, 4], "max_exampl": [3, 4], "max_siz": 3, "max_valu": 3, "mb": 2, "mean": [2, 4], "measur": 2, "memori": 0, "memory_cleanup": 2, "memory_increas": 2, "memory_info": 2, "messag": 5, "metadata": [2, 5], "method": [0, 2, 5], "metric": 0, "micro": 0, "might": 3, "min": [2, 4], "min_siz": 3, "min_valu": 3, "minim": [3, 5], "minimum": 5, "minut": 4, "minvers": [1, 4], "miss": [1, 2, 3, 4, 5], "missing_ok": 5, "mode": [1, 2, 5], "model": [0, 4], "model_dump_json": [2, 3, 5], "model_validate_json": [3, 5], "modern": [0, 1, 5], "modul": 0, "more": 2, "most": [1, 2], "much": 3, "multi": 0, "multipl": [0, 2, 3, 5], "must": 5, "mypi": [0, 1, 4], "myst": 1, "n": [1, 2, 3, 5], "name": [2, 3, 4, 5], "namedtemporaryfil": 5, "nd": 3, "ndate": 2, "necho": [2, 5], "need": 1, "network": 2, "never": 3, "new": [1, 3], "new_featur": 4, "nexit": 2, "next": 0, "nix": 4, "node": 1, "none": [2, 3, 5], "nonexist": 5, "noqa": 5, "normal": [3, 4], "now": [1, 4], "num_failur": 2, "num_request": 2, "number": 5, "numer": 3, "o": [1, 2, 3], "often": 3, "on_request": 2, "on_start": 2, "on_test_start": 2, "on_test_stop": 2, "one": [2, 5], "one_of": 3, "ones": 5, "onli": [0, 2, 3, 4, 5], "op": [0, 2], "open": [2, 4, 5], "oper": [2, 3, 4, 5], "optim": [0, 2, 4], "option": [0, 4, 5], "origin": [3, 5], "our": [0, 2, 4, 5], "out": 3, "output": [1, 3, 4, 5], "over": 2, "overrid": 5, "packag": 1, "page": 0, "paradigm": 3, "parallel": [0, 1], "parallel_work": 1, "parameter": 5, "parametr": [2, 5], "pars": 2, "parser": 1, "pass": [0, 1, 3, 4, 5], "path": [1, 2, 3, 5], "path_strategi": 3, "pathlib": 5, "pattern": [0, 2, 3], "pdb": 5, "peak": 2, "peak_memori": 2, "pend": 2, "pep": 0, "per": [2, 3, 5], "perform": [0, 1], "performance_test": 2, "perm": [2, 5], "permiss": [1, 2, 3, 4, 5], "permissions_strategi": 3, "pip": [2, 4], "pipelin": 5, "pl": 4, "plan": 2, "pleas": 0, "plugin": 2, "point": 2, "port": 1, "possibl": [3, 5], "post": 2, "postcondit": 3, "postgr": 1, "postgres_db": 1, "postgres_password": 1, "postgres_us": 1, "postgresql": 1, "practic": 0, "pre": 0, "prerequisit": 0, "preserv": 3, "prevent": 3, "primari": 1, "print": [1, 2, 5], "problem": 1, "problemat": 3, "process": [2, 5], "process_data": 2, "process_file_data": 5, "process_injection_data": 2, "process_item": 2, "process_large_data": 3, "process_large_dataset": 2, "processed_count": 2, "product": [2, 3], "profil": [0, 5], "project": [1, 4], "proper": [3, 5], "properti": [0, 1, 5], "provid": [1, 2, 3, 4, 5], "psutil": 2, "pull": 0, "pull_request": 2, "punctuat": 3, "purpos": [1, 5], "push": 2, "pwd": [1, 4], "py": [1, 2, 3, 4, 5], "py311": 4, "pycharm": 1, "pydant": 2, "pyenv": 1, "pyproject": [1, 4], "pytest": [0, 3, 5], "pytest_benchmark_compare_fail": 2, "pytest_benchmark_compare_machine_info": 2, "pytestarg": 1, "pytesten": 1, "python": [0, 1, 2, 3, 4, 5], "python3": 1, "pythonpath": [1, 4], "qualiti": [0, 1], "question": 4, "quick": 1, "r": [1, 2, 3, 4], "rais": 5, "randint": 2, "random": 2, "rang": [2, 3], "rate": 3, "re": 2, "read": [4, 5], "read_text": 5, "real": 0, "realist": [0, 2, 3], "reason": 3, "recent": 2, "recommend": 4, "recreat": [3, 5], "redi": 1, "redis_url": 1, "reduct": 0, "refer": 3, "refin": 3, "register_profil": [3, 4], "regress": [0, 3], "regular": 2, "reinstal": [1, 4], "reject": 3, "relat": [3, 5], "relationship": 3, "relev": 4, "reliabl": 0, "replac": 3, "report": [0, 1, 2, 4, 5], "repositori": 1, "repres": 3, "reproduc": [1, 3], "request": [0, 2], "request_typ": 2, "requir": [4, 5], "rerun": 0, "resourc": [0, 2], "respons": 2, "response_length": 2, "response_tim": 2, "result": [2, 3, 4, 5], "return": [2, 3, 5], "reus": 3, "reusabl": 5, "review": 1, "rf": 3, "rm": 3, "roundtrip": 3, "rout": 5, "rss": 2, "rtd": 1, "ruf": 4, "ruff": [0, 1, 5], "ruffen": 1, "rule": 3, "rulebasedstatemachin": 3, "run": [0, 1], "runner": [0, 1], "safe": 3, "safeti": [0, 1, 4], "sampl": 5, "sample_fil": 5, "sampled_from": 3, "save": [2, 3], "scale": 2, "scan": [0, 1], "scenario": 0, "script": [0, 1, 2, 4, 5], "search": [0, 1], "sec": [0, 2], "second": [0, 2, 3, 5], "section": 0, "secur": [0, 1, 2, 3], "see": [0, 4, 5], "seed": 3, "select": 4, "self": [2, 3, 5], "sequenc": 3, "serial": [2, 3, 5], "serialize_test": 5, "serv": [3, 5], "servic": [0, 3, 4, 5], "session": [1, 4], "set": [0, 1, 2, 4], "setup": [0, 2, 5], "sh": [0, 1, 2, 3, 4, 5], "sha256": [2, 3, 5], "sha256_hash": 5, "share": 0, "shell": 4, "shift": 3, "should": [2, 3, 5], "show": [1, 3, 4], "shrink": 3, "signific": 2, "similar": [1, 3], "simpl": 3, "simul": [0, 2], "singl": [1, 5], "size": [2, 3, 5], "skip": [1, 3], "sleep": 2, "slow": [1, 5], "slower": 3, "small": 2, "smart": 0, "some": [2, 3], "sourc": 1, "space": 3, "sparingli": 3, "specif": [2, 3, 4, 5], "speed": [3, 5], "sphinx": 1, "squar": 5, "st": 3, "stack": 0, "standard": 0, "start": [1, 2, 3, 5], "startswith": [3, 5], "stat": [0, 2], "statement": [3, 5], "static": [0, 1, 4], "statist": [2, 3], "statu": 2, "status_cod": 2, "status_info": 2, "step": 0, "still": 3, "stop": [1, 2], "stored_count": 3, "str": [2, 3, 5], "strategi": 0, "stress": 2, "string": 3, "strip": 3, "structur": 0, "submit": 0, "success": [1, 2], "sudo": 1, "suffix": 5, "suit": [1, 3, 4, 5], "sum": 2, "summari": 2, "super": 3, "suppress_health_check": 3, "sy": 1, "system": 2, "t": [2, 4], "tag": 2, "target": [2, 4, 5], "target_path": [2, 3, 5], "task": 2, "tb": [1, 4], "tdd": 4, "teardown": [0, 5], "temp_fil": 5, "temp_file_path": 5, "temp_path": 5, "tempfil": 5, "temporari": 5, "tenbahtsecur": 1, "term": [3, 4, 5], "test_async_operation_perform": 2, "test_bas": [1, 4, 5], "test_basic_function": 5, "test_benchmark": [1, 2, 4], "test_complex_oper": 5, "test_create_injection_with_valid_data_returns_injection_model": 5, "test_data": [2, 5], "test_db": 1, "test_dictionary_processing_perform": 2, "test_division_properti": 3, "test_exception_handl": 5, "test_file_injection_create_invalid_data": 5, "test_file_injection_create_minimal_data": 5, "test_file_injection_create_perform": [1, 2, 4], "test_file_injection_create_roundtrip": 3, "test_file_injection_create_valid_data": 5, "test_file_injection_seri": 5, "test_file_injection_serialization_perform": 2, "test_file_injection_servic": 5, "test_file_oper": [1, 5], "test_file_process": 5, "test_filename_valid": [3, 5], "test_filename_validation_properti": 3, "test_filename_with_exampl": 3, "test_good_benchmark": 2, "test_hash_comparison_perform": 2, "test_hash_consistency_properti": 3, "test_hash_function": 5, "test_json_roundtrip_properti": 3, "test_large_data_process": 3, "test_large_data_processing_perform": 2, "test_memory_usage_monitor": 2, "test_mod": 1, "test_model_creation_spe": 2, "test_model_validation_with_custom_messag": 5, "test_models_valid": [4, 5], "test_new_featur": 4, "test_new_feature_perform": 4, "test_new_feature_properti": 4, "test_performance_edge_cas": 5, "test_positive_numb": 3, "test_positive_numbers_slow": 3, "test_property_bas": [3, 4], "test_python_environ": [1, 5], "test_rout": 5, "test_runn": [0, 1, 4], "test_script": 5, "test_sha256_hashing_perform": 2, "test_start": 2, "test_stop": 2, "test_type_annot": 5, "test_user_": 2, "test_various_permiss": 5, "test_with_composite_strategi": 3, "testbasicfunction": [1, 5], "testboundaryproperti": 3, "testcas": 3, "testconcurrencyperform": 2, "testconcurrencyproperti": 3, "testdataintegrityproperti": 3, "testdataprocessingperform": 2, "testfileinjectionmodel": 5, "testfileinjectionproperti": 3, "testfileinjectionservic": 5, "testfileinjectionstatemachin": 3, "testhashingperform": 2, "testmemoryperform": 2, "testmodelperform": [1, 2, 4], "testpath": [1, 4], "testvalidationproperti": 3, "text": [3, 5], "than": 2, "theme": 1, "thi": [0, 1, 3, 4], "thing": [2, 3, 5], "think": 3, "thorough": 3, "thousand": 3, "throughout": 5, "time": [2, 3, 4, 5], "timeout": 2, "togeth": 5, "toml": [1, 4], "too": 3, "too_slow": 3, "tool": 4, "topic": 4, "total": 2, "total_s": 2, "toward": 3, "tp": 1, "track": [2, 3], "tracked_count": 3, "trend": 2, "tri": 3, "troubleshoot": 0, "true": [1, 2, 3, 5], "try": [1, 3, 5], "tupl": 3, "turdparti": [1, 2, 4, 5], "turdparty_env": 1, "txt": [3, 4, 5], "type": [0, 1, 5], "typecheck": 1, "u": [2, 4], "ubuntu": [1, 2], "ui": 2, "under": 2, "understand": 5, "unhealthi": 2, "unicod": 3, "unifi": 4, "unit": [0, 1], "unlink": 5, "unreli": 2, "until": 4, "up": [0, 1, 2, 4, 5], "updat": [1, 2, 4, 5], "upload": [2, 3, 5], "upload_fil": 2, "upload_large_fil": 2, "us": [0, 1, 2, 3], "usag": [0, 1, 5], "user_id": 2, "userwarn": 1, "v": [0, 1, 2, 4, 5], "v1": 2, "v3": [2, 3, 5], "v4": 2, "valid": [0, 2, 4], "valid_file_injection_data": 5, "validate_filenam": [3, 5], "validationerror": 5, "valu": [2, 3, 5], "variabl": 1, "variou": [2, 3], "vector": 0, "venv": 1, "verbos": [1, 2, 3, 4, 5], "veri": 3, "verif": [0, 2], "verifi": [1, 2, 3, 5], "version": [1, 2, 4], "very_long_filenam": 5, "via": 1, "view": [3, 4, 5], "vim": 4, "virtual": 1, "vscode": 1, "vulner": [0, 4], "w": [4, 5], "wait": 2, "wait_tim": 2, "warm": 2, "warn": [0, 2], "we": 0, "web": 2, "weight": 2, "welcom": 0, "well": [2, 3], "what": [0, 2], "when": [2, 3, 5], "where": 3, "which": 3, "whitelist_categori": 3, "whitelist_charact": 3, "wide": 3, "window": 1, "within": 5, "without": [1, 3], "work": [0, 1, 2, 3, 4, 5], "worker": [0, 5], "workflow": [0, 2, 5], "world": [0, 5], "write": [0, 4, 5], "wsl2": 1, "x": [1, 2, 3, 4, 5], "xml": [3, 5], "xpack": 1, "y": 3, "yield": 5, "yml": 1, "you": [1, 2, 3, 4], "your": [0, 1, 3], "zero": [0, 3, 5], "zerodivisionerror": 5}, "titles": ["TurdParty Testing Framework Documentation", "Installation &amp; Setup", "Performance Testing", "Property-Based Testing", "Quick Start Guide", "Unit Testing"], "titleterms": {"1": 1, "2": 1, "3": 1, "With": 5, "advanc": [0, 2, 3], "alert": 2, "alias": 1, "all": 1, "api": 0, "architectur": 0, "assert": 5, "assumpt": 3, "async": 2, "base": [3, 4], "basic": [2, 3, 5], "benchmark": [2, 4], "best": [2, 3, 5], "cd": 3, "check": 4, "ci": 3, "code": 4, "command": 0, "commit": [1, 4], "common": [1, 4, 5], "comparison": 2, "complex": 2, "compon": 1, "composit": 3, "concept": 3, "configur": [0, 1, 2, 3, 4], "consider": 5, "continu": [2, 5], "contribut": 0, "core": [0, 3], "coverag": [4, 5], "current": [0, 2], "custom": 3, "dashboard": 0, "data": [3, 5], "databas": 3, "debug": [3, 5], "depend": 1, "design": [2, 3], "develop": [0, 1, 4], "docker": 1, "document": 0, "environ": [1, 4], "exampl": [0, 3, 4, 5], "execut": [2, 3, 5], "failur": 3, "fast": 5, "featur": [0, 3, 4], "file": 3, "first": 4, "fixtur": 5, "format": [2, 4], "framework": 0, "function": 5, "gate": 5, "get": [0, 1, 4], "good": 3, "guid": 4, "help": [0, 1, 4], "hook": [1, 4], "hypothesi": [3, 4], "i": 3, "id": 1, "indic": 0, "inject": 3, "instal": [1, 4], "integr": [3, 5], "issu": [1, 4], "kei": [0, 5], "lint": 4, "load": [2, 4], "locust": 2, "manag": 5, "mark": 5, "marker": 5, "memori": 2, "method": 1, "metric": [2, 3], "micro": 2, "model": [2, 3, 5], "modern": 4, "monitor": [2, 3, 5], "need": 0, "new": 4, "next": [1, 2, 3, 4, 5], "nix": 1, "note": 1, "onli": 1, "optim": 3, "option": 1, "organ": 5, "our": 3, "output": 2, "overview": [0, 2, 3, 5], "parallel": 5, "parameter": 2, "pattern": 5, "perform": [2, 3, 4, 5], "pip": 1, "platform": 1, "practic": [2, 3, 5], "pre": [1, 4], "prerequisit": [1, 4], "profil": [2, 3], "properti": [3, 4], "pydant": 5, "pytest": [1, 2, 4], "qualiti": [4, 5], "quick": [0, 4], "recommend": 1, "refer": 0, "regress": 2, "requir": 1, "resourc": 3, "result": 0, "ruff": 4, "run": [2, 3, 4, 5], "runner": 4, "scan": 4, "scenario": 2, "secur": 4, "servic": 1, "set": 3, "setup": [1, 4], "shell": 1, "specif": 1, "start": [0, 4], "state": 3, "statu": 0, "step": [1, 2, 3, 4, 5], "strategi": [2, 3, 5], "structur": [2, 3, 5], "support": [0, 1], "system": 1, "tabl": 0, "target": 3, "test": [0, 1, 2, 3, 4, 5], "threshold": 2, "tool": [0, 1], "topic": 0, "tradit": 3, "troubleshoot": [1, 4], "turdparti": 0, "tutori": 0, "type": 4, "unit": [4, 5], "us": [4, 5], "usag": 2, "user": 2, "v": 3, "valid": [3, 5], "verif": 1, "what": 3, "workflow": 4, "write": 3, "your": 4}})