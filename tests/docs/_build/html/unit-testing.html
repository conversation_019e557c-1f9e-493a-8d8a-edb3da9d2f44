<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Unit Testing &mdash; TurdParty Testing Framework v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=67647e4a" />
      <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=67647e4a" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=8d563738"></script>
        <script src="_static/doctools.js?v=9a2dae69"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/custom.js?v=3a49a312"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Property-Based Testing" href="property-testing.html" />
    <link rel="prev" title="Installation &amp; Setup" href="installation.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            TurdParty Testing Framework
          </a>
              <div class="version">
                1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Quick Start</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="quickstart.html">Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation &amp; Setup</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Core Testing</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Unit Testing</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#test-structure">Test Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="#basic-functionality-tests">Basic Functionality Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#example-test-structure">Example Test Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="#key-testing-patterns">Key Testing Patterns</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#model-validation-tests">Model Validation Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#pydantic-model-testing">Pydantic Model Testing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#running-unit-tests">Running Unit Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#basic-execution">Basic Execution</a></li>
<li class="toctree-l3"><a class="reference internal" href="#with-coverage">With Coverage</a></li>
<li class="toctree-l3"><a class="reference internal" href="#parallel-execution">Parallel Execution</a></li>
<li class="toctree-l3"><a class="reference internal" href="#debugging-tests">Debugging Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#test-markers">Test Markers</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#running-marked-tests">Running Marked Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#fixtures-and-test-data">Fixtures and Test Data</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#common-fixtures">Common Fixtures</a></li>
<li class="toctree-l3"><a class="reference internal" href="#using-fixtures">Using Fixtures</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#test-organization">Test Organization</a></li>
<li class="toctree-l3"><a class="reference internal" href="#test-data-management">Test Data Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="#assertion-strategies">Assertion Strategies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#performance-considerations">Performance Considerations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#fast-test-execution">Fast Test Execution</a></li>
<li class="toctree-l3"><a class="reference internal" href="#monitoring-test-performance">Monitoring Test Performance</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#continuous-integration">Continuous Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#quality-gates">Quality Gates</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="property-testing.html">Property-Based Testing</a></li>
<li class="toctree-l1"><a class="reference internal" href="performance-testing.html">Performance Testing</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">TurdParty Testing Framework</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Unit Testing</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/tenbahtsecurity/turdparty/blob/master/tests/docs/unit-testing.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="unit-testing">
<h1>Unit Testing<a class="headerlink" href="#unit-testing" title="Link to this heading"></a></h1>
<p>Unit tests form the foundation of our testing strategy, providing fast feedback on individual component functionality. Our unit test suite consists of 36 tests that execute in just 0.06 seconds.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>Unit tests in TurdParty focus on:</p>
<ul class="simple">
<li><p><strong>Individual component validation</strong> - Testing functions, classes, and methods in isolation</p></li>
<li><p><strong>Fast execution</strong> - Providing immediate feedback during development</p></li>
<li><p><strong>High coverage</strong> - Ensuring all critical code paths are tested</p></li>
<li><p><strong>Clear assertions</strong> - Making test failures easy to understand and fix</p></li>
</ul>
</section>
<section id="test-structure">
<h2>Test Structure<a class="headerlink" href="#test-structure" title="Link to this heading"></a></h2>
<p>Our unit tests are organized in the <code class="docutils literal notranslate"><span class="pre">tests/unit/</span></code> directory:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>tests/unit/
├── test_basic.py                    # Basic functionality tests (12 tests)
├── test_models_validation.py        # Pydantic model tests (24 tests)
├── test_file_injection_service.py   # Service layer tests
├── test_routes.py                   # API route tests
└── test_performance_edge_cases.py   # Edge case tests
</pre></div>
</div>
</section>
<section id="basic-functionality-tests">
<h2>Basic Functionality Tests<a class="headerlink" href="#basic-functionality-tests" title="Link to this heading"></a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">test_basic.py</span></code> file contains fundamental tests that validate core Python functionality and patterns used throughout the application.</p>
<section id="example-test-structure">
<h3>Example Test Structure<a class="headerlink" href="#example-test-structure" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">TestBasicFunctionality</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test basic Python functionality and patterns.&quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">test_python_environment</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Test that Python environment is working correctly.&quot;&quot;&quot;</span>
        <span class="c1"># Test basic Python functionality</span>
        <span class="k">assert</span> <span class="mi">1</span> <span class="o">+</span> <span class="mi">1</span> <span class="o">==</span> <span class="mi">2</span>
        <span class="k">assert</span> <span class="s2">&quot;hello&quot;</span> <span class="o">+</span> <span class="s2">&quot; &quot;</span> <span class="o">+</span> <span class="s2">&quot;world&quot;</span> <span class="o">==</span> <span class="s2">&quot;hello world&quot;</span>

        <span class="c1"># Test list comprehension</span>
        <span class="n">numbers</span> <span class="o">=</span> <span class="p">[</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">5</span><span class="p">]</span>
        <span class="n">squares</span> <span class="o">=</span> <span class="p">[</span><span class="n">x</span><span class="o">**</span><span class="mi">2</span> <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">numbers</span><span class="p">]</span>
        <span class="k">assert</span> <span class="n">squares</span> <span class="o">==</span> <span class="p">[</span><span class="mi">1</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">16</span><span class="p">,</span> <span class="mi">25</span><span class="p">]</span>

    <span class="k">def</span> <span class="nf">test_file_operations</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Test basic file operations.&quot;&quot;&quot;</span>
        <span class="c1"># Create a temporary file</span>
        <span class="k">with</span> <span class="n">tempfile</span><span class="o">.</span><span class="n">NamedTemporaryFile</span><span class="p">(</span><span class="n">mode</span><span class="o">=</span><span class="s2">&quot;w&quot;</span><span class="p">,</span> <span class="n">delete</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span> <span class="k">as</span> <span class="n">temp_file</span><span class="p">:</span>
            <span class="n">temp_file</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;test content&quot;</span><span class="p">)</span>
            <span class="n">temp_file_path</span> <span class="o">=</span> <span class="n">temp_file</span><span class="o">.</span><span class="n">name</span>

        <span class="k">try</span><span class="p">:</span>
            <span class="c1"># Read the file using pathlib (modern approach)</span>
            <span class="n">temp_path</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="n">temp_file_path</span><span class="p">)</span>
            <span class="n">content</span> <span class="o">=</span> <span class="n">temp_path</span><span class="o">.</span><span class="n">read_text</span><span class="p">()</span>

            <span class="k">assert</span> <span class="n">content</span> <span class="o">==</span> <span class="s2">&quot;test content&quot;</span>
            <span class="k">assert</span> <span class="n">temp_path</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span>

        <span class="k">finally</span><span class="p">:</span>
            <span class="c1"># Clean up</span>
            <span class="n">Path</span><span class="p">(</span><span class="n">temp_file_path</span><span class="p">)</span><span class="o">.</span><span class="n">unlink</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="key-testing-patterns">
<h3>Key Testing Patterns<a class="headerlink" href="#key-testing-patterns" title="Link to this heading"></a></h3>
<p><strong>1. Arrange-Act-Assert (AAA) Pattern</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">test_hash_functionality</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test hash calculation functionality.&quot;&quot;&quot;</span>
    <span class="c1"># Arrange</span>
    <span class="n">test_data</span> <span class="o">=</span> <span class="sa">b</span><span class="s2">&quot;test data for hashing&quot;</span>

    <span class="c1"># Act</span>
    <span class="n">sha256_hash</span> <span class="o">=</span> <span class="n">hashlib</span><span class="o">.</span><span class="n">sha256</span><span class="p">(</span><span class="n">test_data</span><span class="p">)</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>

    <span class="c1"># Assert</span>
    <span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">sha256_hash</span><span class="p">)</span> <span class="o">==</span> <span class="mi">64</span>
    <span class="k">assert</span> <span class="nb">all</span><span class="p">(</span><span class="n">c</span> <span class="ow">in</span> <span class="s2">&quot;0123456789abcdef&quot;</span> <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="n">sha256_hash</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>2. Modern Python Patterns</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">test_type_annotations</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test that type annotations work correctly.&quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">process_file_data</span><span class="p">(</span>
        <span class="n">filename</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">size</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">metadata</span><span class="p">:</span> <span class="nb">dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Process file data with type annotations.&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="p">{</span>
            <span class="s2">&quot;filename&quot;</span><span class="p">:</span> <span class="n">filename</span><span class="p">,</span>
            <span class="s2">&quot;size&quot;</span><span class="p">:</span> <span class="n">size</span><span class="p">,</span>
            <span class="s2">&quot;metadata&quot;</span><span class="p">:</span> <span class="n">metadata</span><span class="p">,</span>
            <span class="s2">&quot;processed&quot;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
        <span class="p">}</span>

    <span class="c1"># Test function with type annotations</span>
    <span class="n">result</span> <span class="o">=</span> <span class="n">process_file_data</span><span class="p">(</span><span class="s2">&quot;test.txt&quot;</span><span class="p">,</span> <span class="mi">1024</span><span class="p">,</span> <span class="p">{</span><span class="s2">&quot;type&quot;</span><span class="p">:</span> <span class="s2">&quot;text&quot;</span><span class="p">})</span>

    <span class="k">assert</span> <span class="n">result</span><span class="p">[</span><span class="s2">&quot;filename&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;test.txt&quot;</span>
    <span class="k">assert</span> <span class="n">result</span><span class="p">[</span><span class="s2">&quot;size&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="mi">1024</span>
    <span class="k">assert</span> <span class="n">result</span><span class="p">[</span><span class="s2">&quot;processed&quot;</span><span class="p">]</span> <span class="ow">is</span> <span class="kc">True</span>
</pre></div>
</div>
<p><strong>3. Exception Testing</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">test_exception_handling</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test exception handling patterns.&quot;&quot;&quot;</span>
    <span class="c1"># Test that exceptions are raised correctly</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="mi">1</span> <span class="o">/</span> <span class="mi">0</span>  <span class="c1"># noqa: B018</span>
        <span class="k">raise</span> <span class="ne">AssertionError</span><span class="p">(</span><span class="s2">&quot;Should have raised ZeroDivisionError&quot;</span><span class="p">)</span>
    <span class="k">except</span> <span class="ne">ZeroDivisionError</span><span class="p">:</span>
        <span class="k">pass</span>  <span class="c1"># Expected</span>

    <span class="c1"># Test file not found exception</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">Path</span><span class="p">(</span><span class="s2">&quot;/nonexistent/file.txt&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">read_text</span><span class="p">()</span>
        <span class="k">raise</span> <span class="ne">AssertionError</span><span class="p">(</span><span class="s2">&quot;Should have raised FileNotFoundError&quot;</span><span class="p">)</span>
    <span class="k">except</span> <span class="ne">FileNotFoundError</span><span class="p">:</span>
        <span class="k">pass</span>  <span class="c1"># Expected</span>
</pre></div>
</div>
</section>
</section>
<section id="model-validation-tests">
<h2>Model Validation Tests<a class="headerlink" href="#model-validation-tests" title="Link to this heading"></a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">test_models_validation.py</span></code> file contains comprehensive tests for our Pydantic models, ensuring data validation and serialization work correctly.</p>
<section id="pydantic-model-testing">
<h3>Pydantic Model Testing<a class="headerlink" href="#pydantic-model-testing" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">TestFileInjectionModels</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test Pydantic models for file injection.&quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">test_file_injection_create_valid_data</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Test FileInjectionCreate with valid data.&quot;&quot;&quot;</span>
        <span class="c1"># Test basic model creation</span>
        <span class="n">model</span> <span class="o">=</span> <span class="n">FileInjectionCreate</span><span class="p">(</span>
            <span class="n">filename</span><span class="o">=</span><span class="s2">&quot;test_script.sh&quot;</span><span class="p">,</span>
            <span class="n">target_path</span><span class="o">=</span><span class="s2">&quot;/app/scripts/test_script.sh&quot;</span><span class="p">,</span>
            <span class="n">permissions</span><span class="o">=</span><span class="s2">&quot;0755&quot;</span><span class="p">,</span>
            <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Test script for validation&quot;</span>
        <span class="p">)</span>

        <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">filename</span> <span class="o">==</span> <span class="s2">&quot;test_script.sh&quot;</span>
        <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">target_path</span> <span class="o">==</span> <span class="s2">&quot;/app/scripts/test_script.sh&quot;</span>
        <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">permissions</span> <span class="o">==</span> <span class="s2">&quot;0755&quot;</span>
        <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">description</span> <span class="o">==</span> <span class="s2">&quot;Test script for validation&quot;</span>

    <span class="k">def</span> <span class="nf">test_file_injection_create_minimal_data</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Test FileInjectionCreate with minimal required data.&quot;&quot;&quot;</span>
        <span class="n">model</span> <span class="o">=</span> <span class="n">FileInjectionCreate</span><span class="p">(</span>
            <span class="n">filename</span><span class="o">=</span><span class="s2">&quot;minimal.sh&quot;</span><span class="p">,</span>
            <span class="n">target_path</span><span class="o">=</span><span class="s2">&quot;/app/minimal.sh&quot;</span><span class="p">,</span>
            <span class="n">permissions</span><span class="o">=</span><span class="s2">&quot;0644&quot;</span>
        <span class="p">)</span>

        <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">filename</span> <span class="o">==</span> <span class="s2">&quot;minimal.sh&quot;</span>
        <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">target_path</span> <span class="o">==</span> <span class="s2">&quot;/app/minimal.sh&quot;</span>
        <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">permissions</span> <span class="o">==</span> <span class="s2">&quot;0644&quot;</span>
        <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">description</span> <span class="ow">is</span> <span class="kc">None</span>
</pre></div>
</div>
<p><strong>Validation Testing</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">test_file_injection_create_invalid_data</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test FileInjectionCreate with invalid data.&quot;&quot;&quot;</span>
    <span class="c1"># Test empty filename</span>
    <span class="k">with</span> <span class="n">pytest</span><span class="o">.</span><span class="n">raises</span><span class="p">(</span><span class="n">ValidationError</span><span class="p">)</span> <span class="k">as</span> <span class="n">exc_info</span><span class="p">:</span>
        <span class="n">FileInjectionCreate</span><span class="p">(</span>
            <span class="n">filename</span><span class="o">=</span><span class="s2">&quot;&quot;</span><span class="p">,</span>
            <span class="n">target_path</span><span class="o">=</span><span class="s2">&quot;/app/test.sh&quot;</span><span class="p">,</span>
            <span class="n">permissions</span><span class="o">=</span><span class="s2">&quot;0755&quot;</span>
        <span class="p">)</span>

    <span class="n">errors</span> <span class="o">=</span> <span class="n">exc_info</span><span class="o">.</span><span class="n">value</span><span class="o">.</span><span class="n">errors</span><span class="p">()</span>
    <span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">errors</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span>
    <span class="k">assert</span> <span class="nb">any</span><span class="p">(</span><span class="s2">&quot;filename&quot;</span> <span class="ow">in</span> <span class="nb">str</span><span class="p">(</span><span class="n">error</span><span class="p">)</span> <span class="k">for</span> <span class="n">error</span> <span class="ow">in</span> <span class="n">errors</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Serialization Testing</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">test_file_injection_serialization</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test model serialization and deserialization.&quot;&quot;&quot;</span>
    <span class="c1"># Create model</span>
    <span class="n">original</span> <span class="o">=</span> <span class="n">FileInjectionCreate</span><span class="p">(</span>
        <span class="n">filename</span><span class="o">=</span><span class="s2">&quot;serialize_test.sh&quot;</span><span class="p">,</span>
        <span class="n">target_path</span><span class="o">=</span><span class="s2">&quot;/app/serialize_test.sh&quot;</span><span class="p">,</span>
        <span class="n">permissions</span><span class="o">=</span><span class="s2">&quot;0755&quot;</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Serialization test&quot;</span>
    <span class="p">)</span>

    <span class="c1"># Serialize to JSON</span>
    <span class="n">json_data</span> <span class="o">=</span> <span class="n">original</span><span class="o">.</span><span class="n">model_dump_json</span><span class="p">()</span>
    <span class="k">assert</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">json_data</span><span class="p">,</span> <span class="nb">str</span><span class="p">)</span>

    <span class="c1"># Deserialize from JSON</span>
    <span class="n">recreated</span> <span class="o">=</span> <span class="n">FileInjectionCreate</span><span class="o">.</span><span class="n">model_validate_json</span><span class="p">(</span><span class="n">json_data</span><span class="p">)</span>

    <span class="c1"># Verify all fields match</span>
    <span class="k">assert</span> <span class="n">recreated</span><span class="o">.</span><span class="n">filename</span> <span class="o">==</span> <span class="n">original</span><span class="o">.</span><span class="n">filename</span>
    <span class="k">assert</span> <span class="n">recreated</span><span class="o">.</span><span class="n">target_path</span> <span class="o">==</span> <span class="n">original</span><span class="o">.</span><span class="n">target_path</span>
    <span class="k">assert</span> <span class="n">recreated</span><span class="o">.</span><span class="n">permissions</span> <span class="o">==</span> <span class="n">original</span><span class="o">.</span><span class="n">permissions</span>
    <span class="k">assert</span> <span class="n">recreated</span><span class="o">.</span><span class="n">description</span> <span class="o">==</span> <span class="n">original</span><span class="o">.</span><span class="n">description</span>
</pre></div>
</div>
</section>
</section>
<section id="running-unit-tests">
<h2>Running Unit Tests<a class="headerlink" href="#running-unit-tests" title="Link to this heading"></a></h2>
<section id="basic-execution">
<h3>Basic Execution<a class="headerlink" href="#basic-execution" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run all unit tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/<span class="w"> </span>-v

<span class="c1"># Run specific test file</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/test_basic.py<span class="w"> </span>-v

<span class="c1"># Run specific test class</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/test_models_validation.py::TestFileInjectionModels<span class="w"> </span>-v

<span class="c1"># Run specific test method</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/test_basic.py::TestBasicFunctionality::test_python_environment<span class="w"> </span>-v
</pre></div>
</div>
</section>
<section id="with-coverage">
<h3>With Coverage<a class="headerlink" href="#with-coverage" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run with coverage reporting</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/<span class="w"> </span>--cov<span class="o">=</span>api<span class="w"> </span>--cov<span class="o">=</span>services<span class="w"> </span>--cov-report<span class="o">=</span>html<span class="w"> </span>--cov-report<span class="o">=</span>term-missing

<span class="c1"># View coverage report</span>
open<span class="w"> </span>htmlcov/index.html
</pre></div>
</div>
</section>
<section id="parallel-execution">
<h3>Parallel Execution<a class="headerlink" href="#parallel-execution" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run tests in parallel (faster execution)</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/<span class="w"> </span>-n<span class="w"> </span>auto

<span class="c1"># Run with specific number of workers</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/<span class="w"> </span>-n<span class="w"> </span><span class="m">4</span>
</pre></div>
</div>
</section>
<section id="debugging-tests">
<h3>Debugging Tests<a class="headerlink" href="#debugging-tests" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run with verbose output and no capture (see print statements)</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/test_basic.py<span class="w"> </span>-v<span class="w"> </span>-s

<span class="c1"># Run with debugger on failure</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/test_basic.py<span class="w"> </span>--pdb

<span class="c1"># Run only failed tests from last run</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/<span class="w"> </span>--lf
</pre></div>
</div>
</section>
</section>
<section id="test-markers">
<h2>Test Markers<a class="headerlink" href="#test-markers" title="Link to this heading"></a></h2>
<p>Use markers to categorize and run specific types of tests:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">pytest</span>

<span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">unit</span>
<span class="k">def</span> <span class="nf">test_basic_functionality</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test marked as unit test.&quot;&quot;&quot;</span>
    <span class="k">pass</span>

<span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">slow</span>
<span class="k">def</span> <span class="nf">test_complex_operation</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test marked as slow.&quot;&quot;&quot;</span>
    <span class="k">pass</span>

<span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">parametrize</span><span class="p">(</span><span class="s2">&quot;input,expected&quot;</span><span class="p">,</span> <span class="p">[</span>
    <span class="p">(</span><span class="s2">&quot;test.txt&quot;</span><span class="p">,</span> <span class="kc">True</span><span class="p">),</span>
    <span class="p">(</span><span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="kc">False</span><span class="p">),</span>
    <span class="p">(</span><span class="s2">&quot;very_long_filename.txt&quot;</span><span class="p">,</span> <span class="kc">True</span><span class="p">),</span>
<span class="p">])</span>
<span class="k">def</span> <span class="nf">test_filename_validation</span><span class="p">(</span><span class="nb">input</span><span class="p">,</span> <span class="n">expected</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Parameterized test for filename validation.&quot;&quot;&quot;</span>
    <span class="n">result</span> <span class="o">=</span> <span class="n">validate_filename</span><span class="p">(</span><span class="nb">input</span><span class="p">)</span>
    <span class="k">assert</span> <span class="n">result</span> <span class="o">==</span> <span class="n">expected</span>
</pre></div>
</div>
<section id="running-marked-tests">
<h3>Running Marked Tests<a class="headerlink" href="#running-marked-tests" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run only unit tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>-m<span class="w"> </span>unit

<span class="c1"># Run all except slow tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;not slow&quot;</span>

<span class="c1"># Run unit tests but not slow ones</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;unit and not slow&quot;</span>
</pre></div>
</div>
</section>
</section>
<section id="fixtures-and-test-data">
<h2>Fixtures and Test Data<a class="headerlink" href="#fixtures-and-test-data" title="Link to this heading"></a></h2>
<section id="common-fixtures">
<h3>Common Fixtures<a class="headerlink" href="#common-fixtures" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># In tests/conftest.py</span>
<span class="kn">import</span> <span class="nn">pytest</span>
<span class="kn">from</span> <span class="nn">pathlib</span> <span class="kn">import</span> <span class="n">Path</span>
<span class="kn">import</span> <span class="nn">tempfile</span>

<span class="nd">@pytest</span><span class="o">.</span><span class="n">fixture</span>
<span class="k">def</span> <span class="nf">sample_file</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Create a temporary test file.&quot;&quot;&quot;</span>
    <span class="k">with</span> <span class="n">tempfile</span><span class="o">.</span><span class="n">NamedTemporaryFile</span><span class="p">(</span><span class="n">mode</span><span class="o">=</span><span class="s1">&#39;w&#39;</span><span class="p">,</span> <span class="n">suffix</span><span class="o">=</span><span class="s1">&#39;.sh&#39;</span><span class="p">,</span> <span class="n">delete</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
        <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s1">&#39;#!/bin/bash</span><span class="se">\n</span><span class="s1">echo &quot;Hello, World!&quot;</span><span class="se">\n</span><span class="s1">&#39;</span><span class="p">)</span>
        <span class="n">f</span><span class="o">.</span><span class="n">flush</span><span class="p">()</span>
        <span class="k">yield</span> <span class="n">Path</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">name</span><span class="p">)</span>
        <span class="c1"># Cleanup</span>
        <span class="n">Path</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">name</span><span class="p">)</span><span class="o">.</span><span class="n">unlink</span><span class="p">(</span><span class="n">missing_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

<span class="nd">@pytest</span><span class="o">.</span><span class="n">fixture</span>
<span class="k">def</span> <span class="nf">valid_file_injection_data</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Provide valid file injection data for testing.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="p">{</span>
        <span class="s2">&quot;filename&quot;</span><span class="p">:</span> <span class="s2">&quot;test_script.sh&quot;</span><span class="p">,</span>
        <span class="s2">&quot;target_path&quot;</span><span class="p">:</span> <span class="s2">&quot;/app/scripts/test_script.sh&quot;</span><span class="p">,</span>
        <span class="s2">&quot;permissions&quot;</span><span class="p">:</span> <span class="s2">&quot;0755&quot;</span><span class="p">,</span>
        <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="s2">&quot;Test script for unit testing&quot;</span>
    <span class="p">}</span>
</pre></div>
</div>
</section>
<section id="using-fixtures">
<h3>Using Fixtures<a class="headerlink" href="#using-fixtures" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">test_file_processing</span><span class="p">(</span><span class="n">sample_file</span><span class="p">,</span> <span class="n">valid_file_injection_data</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test file processing with fixtures.&quot;&quot;&quot;</span>
    <span class="c1"># Use the sample file</span>
    <span class="n">content</span> <span class="o">=</span> <span class="n">sample_file</span><span class="o">.</span><span class="n">read_text</span><span class="p">()</span>
    <span class="k">assert</span> <span class="s2">&quot;Hello, World!&quot;</span> <span class="ow">in</span> <span class="n">content</span>

    <span class="c1"># Use the valid data</span>
    <span class="n">model</span> <span class="o">=</span> <span class="n">FileInjectionCreate</span><span class="p">(</span><span class="o">**</span><span class="n">valid_file_injection_data</span><span class="p">)</span>
    <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">filename</span> <span class="o">==</span> <span class="s2">&quot;test_script.sh&quot;</span>
</pre></div>
</div>
</section>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="test-organization">
<h3>Test Organization<a class="headerlink" href="#test-organization" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>One test class per component</strong> - Group related tests together</p></li>
<li><p><strong>Descriptive test names</strong> - Make test purpose clear from the name</p></li>
<li><p><strong>Arrange-Act-Assert</strong> - Follow the AAA pattern consistently</p></li>
<li><p><strong>Test one thing</strong> - Each test should verify a single behavior</p></li>
</ol>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">TestFileInjectionService</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Tests for the FileInjectionService class.&quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">test_create_injection_with_valid_data_returns_injection_model</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Test that creating injection with valid data returns proper model.&quot;&quot;&quot;</span>
        <span class="c1"># Arrange</span>
        <span class="n">service</span> <span class="o">=</span> <span class="n">FileInjectionService</span><span class="p">()</span>
        <span class="n">data</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;filename&quot;</span><span class="p">:</span> <span class="s2">&quot;test.sh&quot;</span><span class="p">,</span> <span class="s2">&quot;target_path&quot;</span><span class="p">:</span> <span class="s2">&quot;/app/test.sh&quot;</span><span class="p">}</span>

        <span class="c1"># Act</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">create_injection</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>

        <span class="c1"># Assert</span>
        <span class="k">assert</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">result</span><span class="p">,</span> <span class="n">FileInjectionResponse</span><span class="p">)</span>
        <span class="k">assert</span> <span class="n">result</span><span class="o">.</span><span class="n">filename</span> <span class="o">==</span> <span class="s2">&quot;test.sh&quot;</span>
</pre></div>
</div>
</section>
<section id="test-data-management">
<h3>Test Data Management<a class="headerlink" href="#test-data-management" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Use factories for complex data</strong> - Create reusable data builders</p></li>
<li><p><strong>Avoid hardcoded values</strong> - Use constants or fixtures</p></li>
<li><p><strong>Test edge cases</strong> - Include boundary values and error conditions</p></li>
</ol>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Test data factory</span>
<span class="k">def</span> <span class="nf">create_file_injection_data</span><span class="p">(</span><span class="o">**</span><span class="n">overrides</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Create file injection data with optional overrides.&quot;&quot;&quot;</span>
    <span class="n">defaults</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;filename&quot;</span><span class="p">:</span> <span class="s2">&quot;default.sh&quot;</span><span class="p">,</span>
        <span class="s2">&quot;target_path&quot;</span><span class="p">:</span> <span class="s2">&quot;/app/default.sh&quot;</span><span class="p">,</span>
        <span class="s2">&quot;permissions&quot;</span><span class="p">:</span> <span class="s2">&quot;0755&quot;</span>
    <span class="p">}</span>
    <span class="n">defaults</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">overrides</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">defaults</span>

<span class="k">def</span> <span class="nf">test_various_permissions</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test different permission values.&quot;&quot;&quot;</span>
    <span class="n">permissions</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;0644&quot;</span><span class="p">,</span> <span class="s2">&quot;0755&quot;</span><span class="p">,</span> <span class="s2">&quot;0777&quot;</span><span class="p">,</span> <span class="s2">&quot;0600&quot;</span><span class="p">]</span>
    <span class="k">for</span> <span class="n">perm</span> <span class="ow">in</span> <span class="n">permissions</span><span class="p">:</span>
        <span class="n">data</span> <span class="o">=</span> <span class="n">create_file_injection_data</span><span class="p">(</span><span class="n">permissions</span><span class="o">=</span><span class="n">perm</span><span class="p">)</span>
        <span class="n">model</span> <span class="o">=</span> <span class="n">FileInjectionCreate</span><span class="p">(</span><span class="o">**</span><span class="n">data</span><span class="p">)</span>
        <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">permissions</span> <span class="o">==</span> <span class="n">perm</span>
</pre></div>
</div>
</section>
<section id="assertion-strategies">
<h3>Assertion Strategies<a class="headerlink" href="#assertion-strategies" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Specific assertions</strong> - Test exact values when possible</p></li>
<li><p><strong>Multiple assertions</strong> - Verify all important aspects</p></li>
<li><p><strong>Custom error messages</strong> - Provide helpful failure information</p></li>
</ol>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">test_model_validation_with_custom_messages</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test with helpful error messages.&quot;&quot;&quot;</span>
    <span class="n">model</span> <span class="o">=</span> <span class="n">FileInjectionCreate</span><span class="p">(</span>
        <span class="n">filename</span><span class="o">=</span><span class="s2">&quot;test.sh&quot;</span><span class="p">,</span>
        <span class="n">target_path</span><span class="o">=</span><span class="s2">&quot;/app/test.sh&quot;</span><span class="p">,</span>
        <span class="n">permissions</span><span class="o">=</span><span class="s2">&quot;0755&quot;</span>
    <span class="p">)</span>

    <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">filename</span> <span class="o">==</span> <span class="s2">&quot;test.sh&quot;</span><span class="p">,</span> <span class="sa">f</span><span class="s2">&quot;Expected &#39;test.sh&#39;, got &#39;</span><span class="si">{</span><span class="n">model</span><span class="o">.</span><span class="n">filename</span><span class="si">}</span><span class="s2">&#39;&quot;</span>
    <span class="k">assert</span> <span class="n">model</span><span class="o">.</span><span class="n">target_path</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s2">&quot;/app/&quot;</span><span class="p">),</span> <span class="s2">&quot;Target path should start with /app/&quot;</span>
    <span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">model</span><span class="o">.</span><span class="n">permissions</span><span class="p">)</span> <span class="o">==</span> <span class="mi">4</span><span class="p">,</span> <span class="sa">f</span><span class="s2">&quot;Permissions should be 4 chars, got </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">model</span><span class="o">.</span><span class="n">permissions</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span>
</pre></div>
</div>
</section>
</section>
<section id="performance-considerations">
<h2>Performance Considerations<a class="headerlink" href="#performance-considerations" title="Link to this heading"></a></h2>
<section id="fast-test-execution">
<h3>Fast Test Execution<a class="headerlink" href="#fast-test-execution" title="Link to this heading"></a></h3>
<p>Our unit tests are designed for speed:</p>
<ul class="simple">
<li><p><strong>36 tests in 0.06 seconds</strong> - Extremely fast feedback</p></li>
<li><p><strong>No external dependencies</strong> - Tests run in isolation</p></li>
<li><p><strong>Minimal setup/teardown</strong> - Efficient fixture usage</p></li>
<li><p><strong>Parallel execution</strong> - Multiple workers for faster runs</p></li>
</ul>
</section>
<section id="monitoring-test-performance">
<h3>Monitoring Test Performance<a class="headerlink" href="#monitoring-test-performance" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run with timing information</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/<span class="w"> </span>--durations<span class="o">=</span><span class="m">10</span>

<span class="c1"># Profile slow tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/<span class="w"> </span>--profile

<span class="c1"># Run with benchmark comparison</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/<span class="w"> </span>--benchmark-only<span class="w"> </span>--benchmark-compare
</pre></div>
</div>
</section>
</section>
<section id="continuous-integration">
<h2>Continuous Integration<a class="headerlink" href="#continuous-integration" title="Link to this heading"></a></h2>
<p>Our unit tests run automatically in CI/CD pipelines:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="c1"># Example GitHub Actions workflow</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Run Unit Tests</span>
<span class="w">  </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">    </span><span class="no">python -m pytest tests/unit/ \</span>
<span class="w">      </span><span class="no">--cov=api \</span>
<span class="w">      </span><span class="no">--cov=services \</span>
<span class="w">      </span><span class="no">--cov-report=xml \</span>
<span class="w">      </span><span class="no">--junitxml=test-results.xml</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Upload Coverage</span>
<span class="w">  </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">codecov/codecov-action@v3</span>
<span class="w">  </span><span class="nt">with</span><span class="p">:</span>
<span class="w">    </span><span class="nt">file</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./coverage.xml</span>
</pre></div>
</div>
<section id="quality-gates">
<h3>Quality Gates<a class="headerlink" href="#quality-gates" title="Link to this heading"></a></h3>
<p>Unit tests serve as quality gates:</p>
<ul class="simple">
<li><p><strong>Minimum 80% coverage</strong> - Ensure adequate test coverage</p></li>
<li><p><strong>Zero test failures</strong> - All tests must pass</p></li>
<li><p><strong>Fast execution</strong> - Tests complete within time limits</p></li>
<li><p><strong>Clean code</strong> - Ruff linting passes</p></li>
</ul>
</section>
</section>
<section id="next-steps">
<h2>Next Steps<a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>After mastering unit testing, explore:</p>
<ol class="arabic simple">
<li><p><strong>Property-Based Testing</strong> - <a class="reference internal" href="property-testing.html"><span class="doc">Property-Based Testing</span></a></p></li>
<li><p><strong>Performance Testing</strong> - <a class="reference internal" href="performance-testing.html"><span class="doc">Performance Testing</span></a></p></li>
<li><p><strong>Integration Testing</strong> - <span class="xref std std-doc">integration-testing</span></p></li>
<li><p><strong>Test Debugging</strong> - <span class="xref std std-doc">debugging</span></p></li>
</ol>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="installation.html" class="btn btn-neutral float-left" title="Installation &amp; Setup" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="property-testing.html" class="btn btn-neutral float-right" title="Property-Based Testing" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, TurdParty Team.
      <span class="lastupdated">Last updated on Jan 01, 1980.
      </span></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>