/* Custom CSS for TurdParty Testing Documentation */

/* Color scheme */
:root {
    --primary-color: #2980B9;
    --secondary-color: #27AE60;
    --accent-color: #E74C3C;
    --warning-color: #F39C12;
    --success-color: #27AE60;
    --info-color: #3498DB;
    --dark-color: #2C3E50;
    --light-color: #ECF0F1;
    --code-bg: #F8F9FA;
    --border-color: #BDC3C7;
}

/* Enhanced code blocks */
.highlight {
    border-radius: 6px;
    border: 1px solid var(--border-color);
    margin: 1em 0;
}

.highlight pre {
    padding: 1em;
    margin: 0;
    background-color: var(--code-bg);
    border-radius: 6px;
    overflow-x: auto;
}

/* Inline code styling */
code {
    background-color: var(--code-bg);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-size: 0.9em;
    border: 1px solid var(--border-color);
}

/* Performance metrics styling */
.performance-metric {
    display: inline-block;
    background: linear-gradient(135deg, var(--success-color), var(--info-color));
    color: white;
    padding: 0.5em 1em;
    border-radius: 20px;
    font-weight: bold;
    margin: 0.2em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Status badges */
.status-badge {
    display: inline-block;
    padding: 0.3em 0.8em;
    border-radius: 15px;
    font-size: 0.8em;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-passing {
    background-color: var(--success-color);
    color: white;
}

.status-working {
    background-color: var(--info-color);
    color: white;
}

.status-configured {
    background-color: var(--warning-color);
    color: white;
}

.status-clean {
    background-color: var(--secondary-color);
    color: white;
}

/* Enhanced admonitions */
.admonition {
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin: 1.5em 0;
    overflow: hidden;
}

.admonition.tip {
    border-left-color: var(--success-color);
}

.admonition.note {
    border-left-color: var(--info-color);
}

.admonition.warning {
    border-left-color: var(--warning-color);
}

.admonition.error {
    border-left-color: var(--accent-color);
}

.admonition-title {
    background-color: var(--light-color);
    padding: 0.8em 1em;
    margin: 0;
    font-weight: bold;
    border-bottom: 1px solid var(--border-color);
}

.admonition p {
    padding: 0 1em 1em 1em;
    margin: 0.5em 0;
}

/* Test result tables */
.test-results-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

.test-results-table th {
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    color: white;
    padding: 1em;
    text-align: left;
    font-weight: bold;
}

.test-results-table td {
    padding: 0.8em 1em;
    border-bottom: 1px solid var(--border-color);
}

.test-results-table tr:nth-child(even) {
    background-color: #F8F9FA;
}

.test-results-table tr:hover {
    background-color: #E8F4FD;
}

/* Command examples */
.command-example {
    background: linear-gradient(135deg, #2C3E50, #34495E);
    color: #ECF0F1;
    padding: 1em;
    border-radius: 8px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    margin: 1em 0;
    position: relative;
    overflow-x: auto;
}

.command-example::before {
    content: "$ ";
    color: var(--success-color);
    font-weight: bold;
}

/* Tool icons */
.tool-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 0.5em;
    vertical-align: middle;
}

/* Framework badges */
.framework-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5em;
    margin: 1em 0;
}

.framework-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5em 1em;
    background: white;
    border: 2px solid var(--primary-color);
    border-radius: 25px;
    text-decoration: none;
    color: var(--primary-color);
    font-weight: bold;
    transition: all 0.3s ease;
}

.framework-badge:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* Statistics cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1em;
    margin: 2em 0;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5em;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    color: var(--primary-color);
    display: block;
    margin-bottom: 0.2em;
}

.stat-label {
    color: var(--dark-color);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9em;
}

/* Progress indicators */
.progress-bar {
    width: 100%;
    height: 20px;
    background-color: var(--light-color);
    border-radius: 10px;
    overflow: hidden;
    margin: 0.5em 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--info-color));
    border-radius: 10px;
    transition: width 0.3s ease;
}

/* Responsive design */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .framework-badges {
        justify-content: center;
    }
    
    .test-results-table {
        font-size: 0.9em;
    }
    
    .test-results-table th,
    .test-results-table td {
        padding: 0.5em;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --code-bg: #2D3748;
        --light-color: #4A5568;
        --border-color: #718096;
    }
    
    .stat-card {
        background: #2D3748;
        color: #E2E8F0;
        border-color: #4A5568;
    }
    
    .test-results-table tr:nth-child(even) {
        background-color: #2D3748;
    }
    
    .test-results-table tr:hover {
        background-color: #4A5568;
    }
}

/* Animation for loading states */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading {
    animation: pulse 2s infinite;
}

/* Print styles */
@media print {
    .framework-badges,
    .stats-grid {
        break-inside: avoid;
    }
    
    .stat-card {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .command-example {
        background: #f5f5f5;
        color: #000;
        border: 1px solid #ccc;
    }
}
