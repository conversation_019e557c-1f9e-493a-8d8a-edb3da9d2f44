#!/bin/bash
# Build script for TurdParty Testing Framework Documentation

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
DOCS_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="${DOCS_DIR}/_build"
SOURCE_DIR="${DOCS_DIR}"
VENV_DIR="${DOCS_DIR}/.venv"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}========================================${NC}"
    echo -e "${PURPLE} $1${NC}"
    echo -e "${PURPLE}========================================${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to setup virtual environment
setup_venv() {
    print_header "Setting up Python Virtual Environment"
    
    if [ ! -d "$VENV_DIR" ]; then
        print_status "Creating virtual environment..."
        python3 -m venv "$VENV_DIR"
    fi
    
    print_status "Activating virtual environment..."
    source "$VENV_DIR/bin/activate"
    
    print_status "Upgrading pip..."
    pip install --upgrade pip
    
    print_status "Installing documentation dependencies..."
    pip install -r requirements.txt
    
    print_success "Virtual environment setup complete"
}

# Function to validate documentation structure
validate_structure() {
    print_header "Validating Documentation Structure"
    
    required_files=(
        "index.rst"
        "quickstart.rst"
        "installation.rst"
        "unit-testing.rst"
        "property-testing.rst"
        "performance-testing.rst"
        "conf.py"
        "requirements.txt"
    )
    
    missing_files=()
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        print_success "All required files present"
    else
        print_error "Missing required files: ${missing_files[*]}"
        exit 1
    fi
    
    # Check for static files
    if [ ! -d "_static" ]; then
        print_warning "Static files directory not found, creating..."
        mkdir -p "_static"
    fi
    
    print_success "Documentation structure validation complete"
}

# Function to clean build directory
clean_build() {
    print_header "Cleaning Build Directory"
    
    if [ -d "$BUILD_DIR" ]; then
        print_status "Removing existing build directory..."
        rm -rf "$BUILD_DIR"
    fi
    
    print_status "Creating fresh build directory..."
    mkdir -p "$BUILD_DIR"
    
    print_success "Build directory cleaned"
}

# Function to build HTML documentation
build_html() {
    print_header "Building HTML Documentation"
    
    print_status "Running Sphinx build..."
    sphinx-build -b html "$SOURCE_DIR" "$BUILD_DIR/html" -W
    
    print_success "HTML documentation built successfully!"
    print_status "Documentation available at: $BUILD_DIR/html/index.html"
}

# Function to build PDF documentation
build_pdf() {
    print_header "Building PDF Documentation"
    
    if ! command_exists latexmk; then
        print_warning "LaTeX not found, skipping PDF build"
        return 0
    fi
    
    print_status "Building LaTeX documentation..."
    sphinx-build -b latex "$SOURCE_DIR" "$BUILD_DIR/latex"
    
    print_status "Converting LaTeX to PDF..."
    cd "$BUILD_DIR/latex"
    latexmk -pdf -f TurdPartyTesting.tex
    cd "$DOCS_DIR"
    
    print_success "PDF documentation built successfully!"
    print_status "PDF available at: $BUILD_DIR/latex/TurdPartyTesting.pdf"
}

# Function to build EPUB documentation
build_epub() {
    print_header "Building EPUB Documentation"
    
    print_status "Running Sphinx EPUB build..."
    sphinx-build -b epub "$SOURCE_DIR" "$BUILD_DIR/epub"
    
    print_success "EPUB documentation built successfully!"
    print_status "EPUB available at: $BUILD_DIR/epub/TurdPartyTesting.epub"
}

# Function to check links
check_links() {
    print_header "Checking Documentation Links"
    
    print_status "Running link checker..."
    sphinx-build -b linkcheck "$SOURCE_DIR" "$BUILD_DIR/linkcheck"
    
    if [ -f "$BUILD_DIR/linkcheck/output.txt" ]; then
        broken_links=$(grep -c "broken" "$BUILD_DIR/linkcheck/output.txt" || true)
        if [ "$broken_links" -gt 0 ]; then
            print_warning "Found $broken_links broken links"
            print_status "Check $BUILD_DIR/linkcheck/output.txt for details"
        else
            print_success "No broken links found"
        fi
    fi
}

# Function to generate statistics
generate_stats() {
    print_header "Generating Documentation Statistics"
    
    rst_files=$(find "$SOURCE_DIR" -name "*.rst" | wc -l)
    md_files=$(find "$SOURCE_DIR" -name "*.md" | wc -l)
    total_lines=$(find "$SOURCE_DIR" -name "*.rst" -o -name "*.md" | xargs wc -l | tail -1 | awk '{print $1}')
    total_words=$(find "$SOURCE_DIR" -name "*.rst" -o -name "*.md" | xargs wc -w | tail -1 | awk '{print $1}')
    
    print_status "Documentation Statistics:"
    echo "  📄 RST files: $rst_files"
    echo "  📄 MD files: $md_files"
    echo "  📏 Total lines: $total_lines"
    echo "  📝 Total words: $total_words"
    
    if [ -d "$BUILD_DIR/html" ]; then
        html_size=$(du -sh "$BUILD_DIR/html" | cut -f1)
        html_files=$(find "$BUILD_DIR/html" -name "*.html" | wc -l)
        echo "  🌐 HTML size: $html_size"
        echo "  🌐 HTML files: $html_files"
    fi
    
    print_success "Statistics generated"
}

# Function to serve documentation locally
serve_docs() {
    print_header "Starting Local Documentation Server"
    
    if [ ! -d "$BUILD_DIR/html" ]; then
        print_error "HTML documentation not found. Run build first."
        exit 1
    fi
    
    print_status "Starting server on http://localhost:8080"
    print_status "Press Ctrl+C to stop"
    
    cd "$BUILD_DIR/html"
    python3 -m http.server 8080
}

# Function to watch for changes
watch_docs() {
    print_header "Watching for Documentation Changes"
    
    if ! command_exists sphinx-autobuild; then
        print_error "sphinx-autobuild not found. Install with: pip install sphinx-autobuild"
        exit 1
    fi
    
    print_status "Starting auto-build server on http://localhost:8080"
    print_status "Press Ctrl+C to stop"
    
    sphinx-autobuild "$SOURCE_DIR" "$BUILD_DIR/html" --host 0.0.0.0 --port 8080
}

# Function to create archive
create_archive() {
    print_header "Creating Documentation Archive"
    
    if [ ! -d "$BUILD_DIR/html" ]; then
        print_error "HTML documentation not found. Run build first."
        exit 1
    fi
    
    timestamp=$(date +%Y%m%d_%H%M%S)
    archive_name="turdparty-testing-docs-${timestamp}.tar.gz"
    
    print_status "Creating archive: $archive_name"
    cd "$BUILD_DIR"
    tar -czf "$archive_name" html/
    
    print_success "Archive created: $BUILD_DIR/$archive_name"
}

# Function to show usage
show_usage() {
    echo "TurdParty Testing Documentation Build Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  setup        Set up virtual environment and dependencies"
    echo "  validate     Validate documentation structure"
    echo "  clean        Clean build directory"
    echo "  html         Build HTML documentation"
    echo "  pdf          Build PDF documentation (requires LaTeX)"
    echo "  epub         Build EPUB documentation"
    echo "  all          Build all formats (HTML, PDF, EPUB)"
    echo "  linkcheck    Check for broken links"
    echo "  stats        Generate documentation statistics"
    echo "  serve        Serve documentation locally"
    echo "  watch        Watch for changes and auto-rebuild"
    echo "  archive      Create documentation archive"
    echo "  full         Complete build process (setup, validate, clean, build all)"
    echo "  help         Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 setup                    # Set up environment"
    echo "  $0 html                     # Build HTML docs"
    echo "  $0 full                     # Complete build process"
    echo "  $0 watch                    # Development mode with auto-rebuild"
}

# Main execution
main() {
    cd "$DOCS_DIR"
    
    case "${1:-help}" in
        "setup")
            setup_venv
            ;;
        "validate")
            validate_structure
            ;;
        "clean")
            clean_build
            ;;
        "html")
            if [ ! -f "$VENV_DIR/bin/activate" ]; then
                setup_venv
            fi
            source "$VENV_DIR/bin/activate"
            validate_structure
            build_html
            ;;
        "pdf")
            if [ ! -f "$VENV_DIR/bin/activate" ]; then
                setup_venv
            fi
            source "$VENV_DIR/bin/activate"
            validate_structure
            build_pdf
            ;;
        "epub")
            if [ ! -f "$VENV_DIR/bin/activate" ]; then
                setup_venv
            fi
            source "$VENV_DIR/bin/activate"
            validate_structure
            build_epub
            ;;
        "all")
            if [ ! -f "$VENV_DIR/bin/activate" ]; then
                setup_venv
            fi
            source "$VENV_DIR/bin/activate"
            validate_structure
            build_html
            build_epub
            build_pdf
            ;;
        "linkcheck")
            if [ ! -f "$VENV_DIR/bin/activate" ]; then
                setup_venv
            fi
            source "$VENV_DIR/bin/activate"
            check_links
            ;;
        "stats")
            generate_stats
            ;;
        "serve")
            serve_docs
            ;;
        "watch")
            if [ ! -f "$VENV_DIR/bin/activate" ]; then
                setup_venv
            fi
            source "$VENV_DIR/bin/activate"
            watch_docs
            ;;
        "archive")
            create_archive
            ;;
        "full")
            setup_venv
            source "$VENV_DIR/bin/activate"
            validate_structure
            clean_build
            build_html
            build_epub
            build_pdf
            check_links
            generate_stats
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
