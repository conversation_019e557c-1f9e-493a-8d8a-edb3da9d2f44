CI/CD Testing Integration
==========================

Overview
--------

CI/CD testing integration in TurdParty ensures automated quality gates, comprehensive test execution, and reliable deployments. The CI/CD pipeline validates code changes through multiple test stages before production deployment.

**Why CI/CD Testing Matters:**

- **Automated Quality**: Consistent test execution without human intervention
- **Fast Feedback**: Quick identification of issues in code changes
- **Deployment Safety**: Prevent broken code from reaching production
- **Scalable Testing**: Parallel test execution for faster results

CI/CD Architecture
------------------

.. mermaid::

   graph TD
       A[Code Push] --> B[CI Pipeline]
       B --> C[Unit Tests]
       B --> D[Integration Tests]
       B --> E[Security Tests]
       B --> F[Performance Tests]
       
       C --> G[Quality Gate]
       D --> G
       E --> G
       F --> G
       
       G --> H[Build Artifacts]
       H --> I[Staging Deploy]
       I --> J[E2E Tests]
       J --> K[Production Deploy]
       
       L[Test Results] --> M[Coverage Report]
       L --> N[Performance Metrics]
       L --> O[Security Report]

GitHub Actions Configuration
----------------------------

Primary CI/CD pipeline using GitHub Actions for comprehensive testing.

.. code-block:: yaml

   # .github/workflows/ci.yml
   name: CI/CD Pipeline
   
   on:
     push:
       branches: [main, develop]
     pull_request:
       branches: [main, develop]
   
   env:
     PYTHON_VERSION: "3.11"
     NODE_VERSION: "18"
   
   jobs:
     # Code Quality and Linting
     quality:
       name: Code Quality
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v4
         
         - name: Setup Python
           uses: actions/setup-python@v4
           with:
             python-version: ${{ env.PYTHON_VERSION }}
             
         - name: Install dependencies
           run: |
             pip install ruff mypy bandit safety
             pip install -r requirements-dev.txt
             
         - name: Run Ruff linting
           run: ruff check .
           
         - name: Run Ruff formatting check
           run: ruff format --check .
           
         - name: Run MyPy type checking
           run: mypy api/ services/ workers/
           
         - name: Run Bandit security scan
           run: bandit -r api/ services/ workers/ -f json -o bandit-report.json
           
         - name: Check dependencies for vulnerabilities
           run: safety check --json --output safety-report.json
           
         - name: Upload security reports
           uses: actions/upload-artifact@v3
           if: always()
           with:
             name: security-reports
             path: |
               bandit-report.json
               safety-report.json

Test Execution Strategy
-----------------------

Multi-stage test execution with parallel processing and smart caching.

.. code-block:: yaml

   # Unit Tests
   unit-tests:
     name: Unit Tests
     runs-on: ubuntu-latest
     strategy:
       matrix:
         python-version: ["3.11", "3.12"]
     steps:
       - uses: actions/checkout@v4
       
       - name: Setup Python ${{ matrix.python-version }}
         uses: actions/setup-python@v4
         with:
           python-version: ${{ matrix.python-version }}
           
       - name: Cache dependencies
         uses: actions/cache@v3
         with:
           path: ~/.cache/pip
           key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
           
       - name: Install dependencies
         run: |
           pip install -r requirements.txt
           pip install -r requirements-test.txt
           
       - name: Run unit tests
         run: |
           pytest tests/unit/ \
             --cov=api --cov=services --cov=workers \
             --cov-report=xml --cov-report=html \
             --junitxml=test-results.xml \
             --maxfail=10 \
             -n auto
             
       - name: Upload coverage to Codecov
         uses: codecov/codecov-action@v3
         with:
           file: ./coverage.xml
           flags: unit-tests
           name: codecov-umbrella
           
       - name: Upload test results
         uses: actions/upload-artifact@v3
         if: always()
         with:
           name: unit-test-results-${{ matrix.python-version }}
           path: |
             test-results.xml
             htmlcov/

Integration Testing Pipeline
----------------------------

Integration tests with real services and databases.

.. code-block:: yaml

   # Integration Tests
   integration-tests:
     name: Integration Tests
     runs-on: ubuntu-latest
     services:
       postgres:
         image: postgres:15
         env:
           POSTGRES_PASSWORD: postgres
           POSTGRES_DB: turdparty_test
         options: >-
           --health-cmd pg_isready
           --health-interval 10s
           --health-timeout 5s
           --health-retries 5
         ports:
           - 5432:5432
           
       redis:
         image: redis:7
         options: >-
           --health-cmd "redis-cli ping"
           --health-interval 10s
           --health-timeout 5s
           --health-retries 5
         ports:
           - 6379:6379
           
     steps:
       - uses: actions/checkout@v4
       
       - name: Setup Python
         uses: actions/setup-python@v4
         with:
           python-version: ${{ env.PYTHON_VERSION }}
           
       - name: Install dependencies
         run: |
           pip install -r requirements.txt
           pip install -r requirements-test.txt
           
       - name: Wait for services
         run: |
           timeout 60 bash -c 'until pg_isready -h localhost -p 5432; do sleep 1; done'
           timeout 60 bash -c 'until redis-cli -h localhost -p 6379 ping; do sleep 1; done'
           
       - name: Setup test database
         run: |
           export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/turdparty_test"
           python scripts/setup_test_db.py
           
       - name: Run integration tests
         env:
           DATABASE_URL: postgresql://postgres:postgres@localhost:5432/turdparty_test
           REDIS_URL: redis://localhost:6379/0
           TEST_ENVIRONMENT: ci
         run: |
           pytest tests/integration/ \
             -v --tb=short \
             --maxfail=5 \
             --junitxml=integration-results.xml

Security Testing Pipeline
--------------------------

Automated security testing with multiple scanning tools.

.. mermaid::

   graph LR
       A[Security Pipeline] --> B[SAST Scanning]
       A --> C[Dependency Check]
       A --> D[Container Scanning]
       A --> E[Secret Detection]
       
       B --> F[Bandit]
       B --> G[Semgrep]
       
       C --> H[Safety]
       C --> I[Snyk]
       
       D --> J[Trivy]
       D --> K[Grype]

.. code-block:: yaml

   # Security Tests
   security-tests:
     name: Security Tests
     runs-on: ubuntu-latest
     steps:
       - uses: actions/checkout@v4
         with:
           fetch-depth: 0  # Full history for secret scanning
           
       - name: Run Trivy vulnerability scanner
         uses: aquasecurity/trivy-action@master
         with:
           scan-type: 'fs'
           scan-ref: '.'
           format: 'sarif'
           output: 'trivy-results.sarif'
           
       - name: Run Semgrep security scan
         uses: returntocorp/semgrep-action@v1
         with:
           config: >-
             p/security-audit
             p/secrets
             p/python
             
       - name: Run security tests
         run: |
           pytest tests/security/ \
             -v --tb=short \
             --junitxml=security-test-results.xml
             
       - name: Upload security scan results
         uses: github/codeql-action/upload-sarif@v2
         if: always()
         with:
           sarif_file: trivy-results.sarif

Performance Testing Pipeline
----------------------------

Automated performance testing and regression detection.

.. code-block:: yaml

   # Performance Tests
   performance-tests:
     name: Performance Tests
     runs-on: ubuntu-latest
     if: github.event_name == 'push' && github.ref == 'refs/heads/main'
     steps:
       - uses: actions/checkout@v4
       
       - name: Setup Python
         uses: actions/setup-python@v4
         with:
           python-version: ${{ env.PYTHON_VERSION }}
           
       - name: Install dependencies
         run: |
           pip install -r requirements.txt
           pip install -r requirements-test.txt
           
       - name: Run performance benchmarks
         run: |
           pytest tests/performance/ \
             --benchmark-only \
             --benchmark-json=benchmark-results.json \
             --benchmark-compare-fail=mean:10% \
             --benchmark-sort=mean
             
       - name: Store benchmark results
         uses: benchmark-action/github-action-benchmark@v1
         with:
           tool: 'pytest'
           output-file-path: benchmark-results.json
           github-token: ${{ secrets.GITHUB_TOKEN }}
           auto-push: true
           comment-on-alert: true
           alert-threshold: '200%'

VM Testing Pipeline
-------------------

Virtual machine testing with Docker and real VM environments.

.. code-block:: yaml

   # VM Tests
   vm-tests:
     name: VM Tests
     runs-on: ubuntu-latest
     if: github.event_name == 'push'
     steps:
       - uses: actions/checkout@v4
       
       - name: Setup Docker Buildx
         uses: docker/setup-buildx-action@v3
         
       - name: Build test VM images
         run: |
           docker build -t turdparty-test-vm:latest -f docker/test-vm/Dockerfile .
           
       - name: Run VM tests
         run: |
           pytest tests/vm/ \
             -v --tb=short \
             --maxfail=3 \
             --junitxml=vm-test-results.xml
         env:
           VM_PROVIDER: docker
           VM_IMAGE: turdparty-test-vm:latest

Deployment Pipeline
-------------------

Automated deployment with comprehensive testing gates.

.. mermaid::

   graph TD
       A[All Tests Pass] --> B[Build Artifacts]
       B --> C[Deploy to Staging]
       C --> D[Smoke Tests]
       D --> E[E2E Tests]
       E --> F[Performance Tests]
       F --> G[Deploy to Production]
       
       H[Test Failure] --> I[Block Deployment]
       I --> J[Notify Team]

.. code-block:: yaml

   # Deployment
   deploy:
     name: Deploy
     runs-on: ubuntu-latest
     needs: [quality, unit-tests, integration-tests, security-tests]
     if: github.ref == 'refs/heads/main' && github.event_name == 'push'
     environment: production
     steps:
       - uses: actions/checkout@v4
       
       - name: Deploy to staging
         run: |
           # Deploy to staging environment
           ./scripts/deploy.sh staging
           
       - name: Run smoke tests
         run: |
           pytest tests/smoke/ \
             --base-url=https://staging.turdparty.com \
             --junitxml=smoke-test-results.xml
             
       - name: Deploy to production
         if: success()
         run: |
           ./scripts/deploy.sh production
           
       - name: Notify deployment
         uses: 8398a7/action-slack@v3
         if: always()
         with:
           status: ${{ job.status }}
           webhook_url: ${{ secrets.SLACK_WEBHOOK }}

Test Reporting and Analytics
----------------------------

Comprehensive test reporting and trend analysis.

.. code-block:: yaml

   # Test Reporting
   reporting:
     name: Test Reporting
     runs-on: ubuntu-latest
     needs: [unit-tests, integration-tests, security-tests, performance-tests]
     if: always()
     steps:
       - name: Download all artifacts
         uses: actions/download-artifact@v3
         
       - name: Generate test report
         run: |
           python scripts/generate_test_report.py \
             --unit-results unit-test-results*/test-results.xml \
             --integration-results integration-results.xml \
             --security-results security-test-results.xml \
             --output test-report.html
             
       - name: Upload test report
         uses: actions/upload-artifact@v3
         with:
           name: test-report
           path: test-report.html
           
       - name: Update test metrics
         run: |
           python scripts/update_test_metrics.py \
             --results-dir . \
             --metrics-file test-metrics.json

Parallel Test Execution
------------------------

Optimized parallel execution for faster feedback.

.. code-block:: yaml

   # Matrix strategy for parallel execution
   test-matrix:
     name: Test Matrix
     runs-on: ubuntu-latest
     strategy:
       fail-fast: false
       matrix:
         test-suite:
           - unit
           - integration
           - property
           - security
         python-version: ["3.11", "3.12"]
         exclude:
           - test-suite: security
             python-version: "3.12"  # Run security tests only on 3.11
     steps:
       - name: Run ${{ matrix.test-suite }} tests
         run: |
           pytest tests/${{ matrix.test-suite }}/ \
             --maxfail=5 \
             -n auto \
             --junitxml=${{ matrix.test-suite }}-results.xml

Caching Strategy
----------------

Intelligent caching for faster CI/CD execution.

.. code-block:: yaml

   # Multi-level caching
   - name: Cache Python dependencies
     uses: actions/cache@v3
     with:
       path: |
         ~/.cache/pip
         ~/.cache/pytest_cache
       key: ${{ runner.os }}-python-${{ hashFiles('**/requirements*.txt') }}
       restore-keys: |
         ${{ runner.os }}-python-
         
   - name: Cache test data
     uses: actions/cache@v3
     with:
       path: tests/data/cache
       key: test-data-${{ hashFiles('tests/data/**') }}
       
   - name: Cache Docker layers
     uses: actions/cache@v3
     with:
       path: /tmp/.buildx-cache
       key: ${{ runner.os }}-buildx-${{ github.sha }}
       restore-keys: |
         ${{ runner.os }}-buildx-

Environment Management
----------------------

Consistent environment configuration across CI/CD stages.

.. code-block:: yaml

   # Environment variables
   env:
     # Test configuration
     TESTING: true
     LOG_LEVEL: DEBUG
     PYTEST_TIMEOUT: 300
     
     # Database configuration
     DATABASE_URL: postgresql://postgres:postgres@localhost:5432/turdparty_test
     REDIS_URL: redis://localhost:6379/0
     
     # Security configuration
     SECRET_KEY: test-secret-key-for-ci
     JWT_SECRET: test-jwt-secret
     
     # Performance configuration
     BENCHMARK_DISABLE_GC: true
     BENCHMARK_MIN_ROUNDS: 3

Monitoring and Alerting
-----------------------

CI/CD pipeline monitoring and failure alerting.

.. code-block:: yaml

   # Failure notifications
   notify-failure:
     name: Notify Failure
     runs-on: ubuntu-latest
     needs: [quality, unit-tests, integration-tests]
     if: failure()
     steps:
       - name: Slack notification
         uses: 8398a7/action-slack@v3
         with:
           status: failure
           channel: '#ci-alerts'
           webhook_url: ${{ secrets.SLACK_WEBHOOK }}
           
       - name: Create GitHub issue
         uses: actions/github-script@v6
         with:
           script: |
             github.rest.issues.create({
               owner: context.repo.owner,
               repo: context.repo.repo,
               title: 'CI Pipeline Failure',
               body: 'CI pipeline failed for commit ${{ github.sha }}'
             })

Best Practices
--------------

**CI/CD Testing Principles:**

1. **Fail Fast**: Stop pipeline on critical test failures
2. **Parallel Execution**: Run independent tests concurrently
3. **Smart Caching**: Cache dependencies and test data
4. **Environment Parity**: Match production environment closely
5. **Comprehensive Reporting**: Detailed test results and metrics

**Pipeline Optimization:**

.. code-block:: yaml

   # Optimized pipeline structure
   jobs:
     # Fast feedback (< 5 minutes)
     quick-checks:
       - linting
       - unit-tests
       - security-scan
     
     # Comprehensive testing (< 15 minutes)
     full-tests:
       needs: quick-checks
       - integration-tests
       - performance-tests
       - vm-tests
     
     # Deployment (< 10 minutes)
     deploy:
       needs: full-tests
       - staging-deploy
       - smoke-tests
       - production-deploy

Related Documentation
---------------------

* :doc:`pytest-config` - pytest CI configuration
* :doc:`performance-testing` - Performance testing in CI
* :doc:`security-testing` - Security testing automation
* :doc:`troubleshooting` - CI/CD troubleshooting guide
