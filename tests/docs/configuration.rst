Test Configuration
==================

Overview
--------

Test configuration in TurdParty ensures consistent, reproducible test execution across environments. Configuration drives test behaviour, data sources, and execution parameters.

**Why Configuration Testing Matters:**

- **Consistency**: Same test behaviour across dev/CI/prod environments
- **Isolation**: Tests don't interfere with each other or production data
- **Debugging**: Clear configuration makes test failures easier to diagnose
- **Scalability**: Configuration enables parallel test execution

Configuration Architecture
---------------------------

.. mermaid::

   graph TD
       A[pytest.ini] --> B[Test Discovery]
       A --> C[Test Execution]
       D[conftest.py] --> E[Fixtures]
       D --> F[Test Data]
       G[Environment Variables] --> H[Test Behaviour]
       I[.env.test] --> J[Database URLs]
       I --> K[API Endpoints]
       
       B --> L[Unit Tests]
       B --> M[Integration Tests]
       B --> N[Property Tests]
       
       E --> O[Database Fixtures]
       E --> P[Mock Services]
       E --> Q[Test Clients]

Core Configuration Files
-------------------------

pytest.ini
~~~~~~~~~~~

Central pytest configuration defining test discovery, markers, and execution behaviour.

.. code-block:: ini

   [tool:pytest]
   testpaths = tests
   python_files = test_*.py
   python_classes = Test*
   python_functions = test_*
   
   markers =
       unit: Unit tests (fast, isolated)
       integration: Integration tests (slower, external deps)
       property: Property-based tests (hypothesis)
       performance: Performance benchmarks
       security: Security validation tests
       vm: Real VM tests (slowest)
   
   addopts = 
       --strict-markers
       --strict-config
       --tb=short
       --cov-report=term-missing
       --cov-report=html
       --cov-fail-under=80

conftest.py
~~~~~~~~~~~

Shared fixtures and test configuration across the test suite.

.. code-block:: python

   # Global test configuration
   pytest_plugins = [
       "tests.fixtures.database",
       "tests.fixtures.api_client", 
       "tests.fixtures.vm_manager",
       "tests.fixtures.test_data"
   ]
   
   def pytest_configure(config):
       """Configure test environment"""
       config.addinivalue_line("markers", "slow: marks tests as slow")
       config.addinivalue_line("markers", "external: requires external services")

Environment Configuration
--------------------------

Test environments use isolated configuration to prevent data corruption and ensure reproducibility.

.. mermaid::

   graph LR
       A[.env.test] --> B[Test Database]
       A --> C[Mock APIs]
       A --> D[Test Storage]
       
       E[.env.ci] --> F[CI Database]
       E --> G[CI Services]
       E --> H[CI Storage]
       
       I[.env.local] --> J[Local Database]
       I --> K[Local Services]
       I --> L[Local Storage]

Configuration Hierarchy
~~~~~~~~~~~~~~~~~~~~~~~~

1. **Environment Variables** (highest priority)
2. **Test-specific .env files**
3. **Default test configuration**
4. **Fallback values** (lowest priority)

.. code-block:: bash

   # Test environment variables
   export TEST_DATABASE_URL="postgresql://test:test@localhost:5432/turdparty_test"
   export TEST_REDIS_URL="redis://localhost:6379/1"
   export TEST_VM_PROVIDER="docker"
   export TEST_LOG_LEVEL="DEBUG"

Database Configuration
----------------------

Test databases use isolated schemas and temporary data to ensure test independence.

.. code-block:: python

   # Database test configuration
   TEST_DATABASE_CONFIG = {
       "url": "postgresql://test:test@localhost:5432/turdparty_test",
       "pool_size": 5,
       "max_overflow": 10,
       "isolation_level": "READ_COMMITTED",
       "echo": False  # Set to True for SQL debugging
   }

**Database Test Strategy:**

- **Isolated schemas**: Each test class gets its own schema
- **Transaction rollback**: Tests run in transactions that rollback
- **Fixture data**: Consistent test data loaded via fixtures
- **Migration testing**: Database schema changes tested separately

API Configuration
-----------------

API tests use dedicated test endpoints and mock external services.

.. code-block:: python

   # API test configuration
   API_TEST_CONFIG = {
       "base_url": "http://localhost:8000",
       "timeout": 30,
       "retries": 3,
       "auth_token": "test_token_12345",
       "rate_limit": False  # Disable rate limiting in tests
   }

VM Configuration
----------------

VM tests use containerized environments for speed and isolation.

.. code-block:: python

   # VM test configuration
   VM_TEST_CONFIG = {
       "provider": "docker",
       "base_image": "ubuntu:22.04",
       "memory": "512m",
       "cpu": "0.5",
       "network": "test_network",
       "cleanup": True  # Auto-cleanup after tests
   }

Performance Configuration
-------------------------

Performance tests use consistent configuration for reliable benchmarks.

.. code-block:: python

   # Performance test configuration
   BENCHMARK_CONFIG = {
       "min_rounds": 5,
       "max_time": 10.0,
       "warmup": True,
       "disable_gc": True,
       "timer": "perf_counter"
   }

Configuration Validation
-------------------------

Test configuration is validated at startup to catch issues early.

.. code-block:: python

   def validate_test_config():
       """Validate test configuration before running tests"""
       required_vars = [
           "TEST_DATABASE_URL",
           "TEST_REDIS_URL", 
           "TEST_VM_PROVIDER"
       ]
       
       missing = [var for var in required_vars if not os.getenv(var)]
       if missing:
           raise ConfigurationError(f"Missing required variables: {missing}")

Best Practices
--------------

**Configuration Management:**

1. **Isolate test data**: Never use production configuration in tests
2. **Use fixtures**: Load configuration through pytest fixtures
3. **Validate early**: Check configuration before running tests
4. **Document dependencies**: Clear documentation of required services
5. **Version configuration**: Track configuration changes in version control

**Common Patterns:**

.. code-block:: python

   @pytest.fixture(scope="session")
   def test_config():
       """Load and validate test configuration"""
       config = load_test_config()
       validate_test_config(config)
       return config
   
   @pytest.fixture(scope="function")
   def isolated_database(test_config):
       """Provide isolated database for each test"""
       with create_test_database(test_config) as db:
           yield db

Troubleshooting
---------------

**Common Configuration Issues:**

1. **Missing environment variables**: Check .env.test file exists
2. **Database connection failures**: Verify test database is running
3. **Port conflicts**: Ensure test services use different ports
4. **Permission errors**: Check file/directory permissions for test data

**Debug Configuration:**

.. code-block:: bash

   # Debug test configuration
   pytest --collect-only  # Show discovered tests
   pytest -v --tb=long    # Verbose output with full tracebacks
   pytest --setup-show    # Show fixture setup/teardown

Related Documentation
---------------------

* :doc:`unit-testing` - Unit test configuration
* :doc:`integration-testing` - Integration test setup  
* :doc:`performance-testing` - Performance test configuration
* :doc:`troubleshooting` - Configuration troubleshooting guide
