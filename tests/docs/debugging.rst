Test Debugging
==============

Overview
--------

Test debugging in TurdParty provides systematic approaches to identify, isolate, and fix test failures. Effective debugging reduces development time and improves test reliability across the entire testing suite.

**Why Test Debugging Matters:**

- **Rapid Problem Resolution**: Quickly identify root causes of test failures
- **Improved Test Reliability**: Fix flaky and brittle tests systematically
- **Development Efficiency**: Reduce time spent investigating test issues
- **Quality Assurance**: Ensure tests accurately reflect system behavior

Debugging Architecture
----------------------

.. mermaid::

   graph TD
       A[Test Failure] --> B[Failure Analysis]
       B --> C[Information Gathering]
       B --> D[Isolation Techniques]
       B --> E[Root Cause Analysis]
       
       C --> F[Logs & Output]
       C --> G[Stack Traces]
       C --> H[System State]
       
       D --> I[Minimal Reproduction]
       D --> J[Binary Search]
       D --> K[Component Isolation]
       
       E --> L[Code Issues]
       E --> M[Environment Problems]
       E --> N[Test Design Flaws]

Debugging Tools and Techniques
------------------------------

**pytest Debugging Options:**

.. code-block:: bash

   # Verbose output with full tracebacks
   pytest -v --tb=long tests/test_file.py
   
   # Stop on first failure
   pytest -x tests/
   
   # Drop into debugger on failure
   pytest --pdb tests/test_file.py::test_function
   
   # Show local variables in tracebacks
   pytest --tb=auto --showlocals tests/
   
   # Capture and display output
   pytest -s tests/  # Don't capture stdout/stderr
   
   # Run only failed tests from last run
   pytest --lf  # Last failed
   pytest --ff  # Failed first, then rest

**Advanced Debugging Configuration:**

.. code-block:: python

   # conftest.py debugging configuration
   def pytest_configure(config):
       """Configure debugging options"""
       if config.getoption("--debug"):
           import logging
           logging.basicConfig(level=logging.DEBUG)
           
           # Enable SQL logging for database tests
           logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
   
   def pytest_addoption(parser):
       """Add custom debugging options"""
       parser.addoption(
           "--debug", action="store_true", default=False,
           help="Enable debug mode with verbose logging"
       )
       parser.addoption(
           "--debug-db", action="store_true", default=False,
           help="Enable database query debugging"
       )

Interactive Debugging
---------------------

**Using pdb for Interactive Debugging:**

.. code-block:: python

   import pdb
   
   def test_complex_analysis():
       """Test with interactive debugging"""
       analysis_data = setup_complex_analysis()
       
       # Set breakpoint for interactive debugging
       pdb.set_trace()
       
       result = perform_analysis(analysis_data)
       
       # Another breakpoint to inspect results
       import pdb; pdb.set_trace()
       
       assert result["status"] == "completed"

**pytest-pdb Integration:**

.. code-block:: bash

   # Drop into debugger on any failure
   pytest --pdb
   
   # Drop into debugger on specific test
   pytest --pdb tests/test_file.py::test_function
   
   # Use pdbpp for enhanced debugging
   pip install pdbpp
   pytest --pdb

**VS Code Debugging Configuration:**

.. code-block:: json

   // .vscode/launch.json
   {
       "version": "0.2.0",
       "configurations": [
           {
               "name": "Debug pytest",
               "type": "python",
               "request": "launch",
               "module": "pytest",
               "args": [
                   "tests/test_file.py::test_function",
                   "-v",
                   "-s"
               ],
               "console": "integratedTerminal",
               "justMyCode": false
           }
       ]
   }

Logging and Output Analysis
---------------------------

**Structured Logging for Tests:**

.. code-block:: python

   import logging
   import structlog
   
   # Configure structured logging for tests
   structlog.configure(
       processors=[
           structlog.stdlib.filter_by_level,
           structlog.stdlib.add_logger_name,
           structlog.stdlib.add_log_level,
           structlog.stdlib.PositionalArgumentsFormatter(),
           structlog.processors.TimeStamper(fmt="iso"),
           structlog.processors.StackInfoRenderer(),
           structlog.processors.format_exc_info,
           structlog.processors.JSONRenderer()
       ],
       context_class=dict,
       logger_factory=structlog.stdlib.LoggerFactory(),
       wrapper_class=structlog.stdlib.BoundLogger,
       cache_logger_on_first_use=True,
   )
   
   logger = structlog.get_logger()
   
   def test_with_structured_logging():
       """Test with comprehensive logging"""
       logger.info("Starting analysis test", test_id="test_001")
       
       try:
           result = perform_analysis()
           logger.info("Analysis completed", 
                      result_status=result.status,
                      execution_time=result.duration)
       except Exception as e:
           logger.error("Analysis failed", 
                       error=str(e),
                       error_type=type(e).__name__)
           raise

**Log Analysis Tools:**

.. code-block:: python

   def analyze_test_logs(log_file):
       """Analyze test logs for patterns and issues"""
       import json
       
       errors = []
       warnings = []
       performance_issues = []
       
       with open(log_file) as f:
           for line in f:
               try:
                   log_entry = json.loads(line)
                   
                   if log_entry.get("level") == "error":
                       errors.append(log_entry)
                   elif log_entry.get("level") == "warning":
                       warnings.append(log_entry)
                   elif log_entry.get("execution_time", 0) > 5.0:
                       performance_issues.append(log_entry)
                       
               except json.JSONDecodeError:
                   continue
       
       return {
           "errors": errors,
           "warnings": warnings,
           "performance_issues": performance_issues
       }

Failure Pattern Analysis
------------------------

**Common Test Failure Patterns:**

.. mermaid::

   graph LR
       A[Test Failures] --> B[Timing Issues]
       A --> C[Environment Issues]
       A --> D[Data Issues]
       A --> E[Logic Issues]
       
       B --> F[Race Conditions]
       B --> G[Timeouts]
       
       C --> H[Missing Dependencies]
       C --> I[Configuration Problems]
       
       D --> J[Stale Test Data]
       D --> K[Data Corruption]
       
       E --> L[Assertion Errors]
       E --> M[Logic Bugs]

**Timing Issue Debugging:**

.. code-block:: python

   import time
   import asyncio
   from unittest.mock import patch
   
   def test_async_operation_timing():
       """Debug timing issues in async operations"""
       start_time = time.time()
       
       async def debug_async_operation():
           logger.debug("Starting async operation")
           
           # Add timing checkpoints
           checkpoint1 = time.time()
           await some_async_operation()
           checkpoint2 = time.time()
           
           logger.debug("Async operation timing",
                       operation_duration=checkpoint2 - checkpoint1,
                       total_duration=checkpoint2 - start_time)
       
       # Run with timeout to catch hanging operations
       try:
           asyncio.wait_for(debug_async_operation(), timeout=30.0)
       except asyncio.TimeoutError:
           logger.error("Async operation timed out")
           raise

**Flaky Test Detection:**

.. code-block:: python

   import pytest
   
   @pytest.mark.flaky(reruns=3, reruns_delay=1)
   def test_potentially_flaky():
       """Test that might be flaky - run multiple times"""
       pass
   
   # Custom flaky test detector
   def detect_flaky_tests():
       """Detect tests that fail intermittently"""
       import subprocess
       import json
       
       results = []
       
       # Run tests multiple times
       for run in range(10):
           result = subprocess.run([
               "pytest", "--json-report", "--json-report-file=test_run.json",
               "tests/"
           ], capture_output=True)
           
           with open("test_run.json") as f:
               test_data = json.load(f)
               results.append(test_data)
       
       # Analyze for flaky patterns
       flaky_tests = []
       for test_name in get_all_test_names(results[0]):
           pass_count = sum(1 for r in results if test_passed(r, test_name))
           if 0 < pass_count < len(results):
               flaky_tests.append({
                   "test": test_name,
                   "pass_rate": pass_count / len(results)
               })
       
       return flaky_tests

Database Test Debugging
------------------------

**Database State Inspection:**

.. code-block:: python

   def debug_database_test():
       """Debug database-related test issues"""
       with database.get_connection() as conn:
           # Inspect database state before test
           logger.debug("Database state before test")
           tables = conn.execute("SELECT tablename FROM pg_tables WHERE schemaname='public'")
           for table in tables:
               count = conn.execute(f"SELECT COUNT(*) FROM {table[0]}").scalar()
               logger.debug(f"Table {table[0]}: {count} rows")
           
           # Run test operation
           result = perform_database_operation()
           
           # Inspect database state after test
           logger.debug("Database state after test")
           # ... repeat inspection
           
           return result

**Transaction Debugging:**

.. code-block:: python

   def test_with_transaction_debugging():
       """Debug transaction-related issues"""
       with database.get_connection() as conn:
           # Start transaction with debugging
           trans = conn.begin()
           
           try:
               logger.debug("Transaction started", transaction_id=id(trans))
               
               # Perform operations with checkpoints
               conn.execute("INSERT INTO analyses ...")
               logger.debug("Insert completed")
               
               conn.execute("UPDATE analyses SET ...")
               logger.debug("Update completed")
               
               # Check transaction state
               in_transaction = conn.in_transaction()
               logger.debug("Transaction state", in_transaction=in_transaction)
               
               trans.commit()
               logger.debug("Transaction committed")
               
           except Exception as e:
               logger.error("Transaction failed", error=str(e))
               trans.rollback()
               logger.debug("Transaction rolled back")
               raise

API Test Debugging
-------------------

**HTTP Request/Response Debugging:**

.. code-block:: python

   import requests
   import logging
   
   # Enable HTTP debugging
   logging.basicConfig(level=logging.DEBUG)
   logging.getLogger("requests.packages.urllib3").setLevel(logging.DEBUG)
   logging.getLogger("urllib3.connectionpool").setLevel(logging.DEBUG)
   
   def test_api_with_debugging():
       """Debug API interactions"""
       # Custom session with debugging
       session = requests.Session()
       
       # Add request/response logging
       def log_request(request):
           logger.debug("API Request",
                       method=request.method,
                       url=request.url,
                       headers=dict(request.headers),
                       body=request.body)
       
       def log_response(response):
           logger.debug("API Response",
                       status_code=response.status_code,
                       headers=dict(response.headers),
                       body=response.text[:1000])  # First 1000 chars
       
       # Hook into requests
       session.hooks['response'].append(lambda r, *args, **kwargs: log_response(r))
       
       response = session.post("/api/analyze", json={"file_hash": "test"})
       assert response.status_code == 200

**WebSocket Debugging:**

.. code-block:: python

   import asyncio
   import websockets
   import json
   
   async def debug_websocket_test():
       """Debug WebSocket communication"""
       async with websockets.connect("ws://localhost:8000/ws") as websocket:
           # Log connection
           logger.debug("WebSocket connected", 
                       local_address=websocket.local_address,
                       remote_address=websocket.remote_address)
           
           # Send message with logging
           message = {"action": "subscribe", "analysis_id": "test"}
           await websocket.send(json.dumps(message))
           logger.debug("WebSocket message sent", message=message)
           
           # Receive with timeout and logging
           try:
               response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
               logger.debug("WebSocket message received", response=response)
           except asyncio.TimeoutError:
               logger.error("WebSocket receive timeout")
               raise

Performance Debugging
----------------------

**Memory Usage Analysis:**

.. code-block:: python

   import tracemalloc
   import psutil
   import gc
   
   def test_with_memory_debugging():
       """Debug memory usage in tests"""
       # Start memory tracing
       tracemalloc.start()
       
       # Get initial memory usage
       process = psutil.Process()
       initial_memory = process.memory_info().rss
       
       # Run test operation
       result = memory_intensive_operation()
       
       # Check memory usage
       current_memory = process.memory_info().rss
       memory_diff = current_memory - initial_memory
       
       # Get top memory allocations
       snapshot = tracemalloc.take_snapshot()
       top_stats = snapshot.statistics('lineno')
       
       logger.debug("Memory usage analysis",
                   memory_increase=memory_diff,
                   top_allocations=[{
                       "file": stat.traceback.format()[0],
                       "size": stat.size
                   } for stat in top_stats[:5]])
       
       # Force garbage collection and check again
       gc.collect()
       final_memory = process.memory_info().rss
       
       if final_memory > initial_memory * 1.5:  # 50% increase
           logger.warning("Potential memory leak detected")

**Performance Profiling:**

.. code-block:: python

   import cProfile
   import pstats
   import io
   
   def test_with_performance_profiling():
       """Profile test performance"""
       profiler = cProfile.Profile()
       
       # Start profiling
       profiler.enable()
       
       # Run test operation
       result = performance_critical_operation()
       
       # Stop profiling
       profiler.disable()
       
       # Analyze results
       stats_stream = io.StringIO()
       stats = pstats.Stats(profiler, stream=stats_stream)
       stats.sort_stats('cumulative')
       stats.print_stats(10)  # Top 10 functions
       
       profile_output = stats_stream.getvalue()
       logger.debug("Performance profile", profile=profile_output)

Test Environment Debugging
---------------------------

**Environment State Inspection:**

.. code-block:: python

   def debug_test_environment():
       """Debug test environment configuration"""
       import os
       import sys
       import platform
       
       env_info = {
           "python_version": sys.version,
           "platform": platform.platform(),
           "working_directory": os.getcwd(),
           "environment_variables": {
               k: v for k, v in os.environ.items() 
               if k.startswith(('TEST_', 'DATABASE_', 'REDIS_'))
           },
           "python_path": sys.path[:5],  # First 5 entries
           "installed_packages": get_installed_packages()
       }
       
       logger.debug("Test environment info", **env_info)

**Dependency Debugging:**

.. code-block:: python

   def debug_test_dependencies():
       """Debug test dependency issues"""
       import pkg_resources
       import importlib
       
       required_packages = [
           'pytest', 'hypothesis', 'requests', 'sqlalchemy'
       ]
       
       for package in required_packages:
           try:
               # Check if package is installed
               version = pkg_resources.get_distribution(package).version
               
               # Try to import
               importlib.import_module(package)
               
               logger.debug("Package check", 
                           package=package, 
                           version=version, 
                           status="OK")
           except Exception as e:
               logger.error("Package issue", 
                           package=package, 
                           error=str(e))

Best Practices
--------------

**Debugging Principles:**

1. **Reproduce Consistently**: Create minimal, reliable reproduction cases
2. **Isolate Variables**: Change one thing at a time when debugging
3. **Use Logging Strategically**: Add logging at key decision points
4. **Preserve Evidence**: Save logs, stack traces, and system state
5. **Document Solutions**: Record fixes for future reference

**Debugging Workflow:**

.. code-block:: python

   def debugging_workflow_example():
       """Example debugging workflow"""
       # 1. Reproduce the failure
       try:
           failing_operation()
       except Exception as e:
           logger.error("Failure reproduced", error=str(e))
       
       # 2. Add debugging information
       with debug_context():
           # 3. Isolate the problem
           minimal_reproduction()
       
       # 4. Fix and verify
       fixed_operation()
       
       # 5. Add regression test
       test_regression_prevention()

Related Documentation
---------------------

* :doc:`troubleshooting` - General troubleshooting guide
* :doc:`performance-testing` - Performance debugging
* :doc:`integration-testing` - Integration test debugging
* :doc:`best-practices` - Testing best practices
