Hypothesis Configuration
========================

Overview
--------

Hypothesis configuration in TurdParty enables property-based testing to discover edge cases and validate system behavior across input ranges. Hypothesis generates test data automatically, finding bugs that traditional example-based tests miss.

**Why Hypothesis Testing Exists:**

- **Edge Case Discovery**: Automatically find boundary conditions and corner cases
- **Input Validation**: Test system behavior across wide input ranges
- **Regression Prevention**: Generate comprehensive test cases for complex logic
- **Specification Verification**: Validate that code meets mathematical properties

Hypothesis Architecture
-----------------------

.. mermaid::

   graph TD
       A[Hypothesis Engine] --> B[Strategy Generation]
       A --> C[Test Execution]
       A --> D[Shrinking Process]
       
       B --> E[Data Strategies]
       B --> F[Composite Strategies]
       B --> G[Custom Strategies]
       
       C --> H[Property Testing]
       C --> I[Stateful Testing]
       C --> J[Example Generation]
       
       D --> K[Minimal Failing Case]
       D --> L[Reproducible Examples]

Core Configuration
------------------

Hypothesis settings control test generation and execution behavior.

.. code-block:: python

   # tests/conftest.py
   from hypothesis import settings, Verbosity
   import hypothesis.strategies as st
   
   # Global Hypothesis configuration
   settings.register_profile("default", 
       max_examples=100,
       deadline=1000,  # 1 second per test
       verbosity=Verbosity.normal,
       suppress_health_check=[
           HealthCheck.too_slow,
           HealthCheck.data_too_large
       ]
   )
   
   settings.register_profile("ci", 
       max_examples=1000,
       deadline=5000,  # 5 seconds in CI
       verbosity=Verbosity.verbose
   )
   
   settings.register_profile("debug", 
       max_examples=10,
       verbosity=Verbosity.debug,
       print_blob=True
   )
   
   # Load profile based on environment
   import os
   profile = os.getenv("HYPOTHESIS_PROFILE", "default")
   settings.load_profile(profile)

Property-Based Test Strategies
------------------------------

Strategies define how Hypothesis generates test data for different types.

**File Hash Strategies:**

.. code-block:: python

   import hypothesis.strategies as st
   from hypothesis import given
   
   # Strategy for valid file hashes
   file_hash_strategy = st.text(
       alphabet=st.characters(whitelist_categories=("Ll", "Nd")),
       min_size=32,
       max_size=64
   ).filter(lambda x: all(c in "0123456789abcdef" for c in x))
   
   # Strategy for malware file types
   malware_extensions = st.sampled_from([
       ".exe", ".dll", ".bat", ".cmd", ".scr", ".pif", 
       ".com", ".jar", ".zip", ".rar"
   ])
   
   # Strategy for file sizes
   file_size_strategy = st.integers(min_value=1, max_value=100_000_000)  # 1B to 100MB

**API Input Strategies:**

.. code-block:: python

   # Strategy for API request data
   api_request_strategy = st.fixed_dictionaries({
       "file_hash": file_hash_strategy,
       "analysis_type": st.sampled_from(["static", "dynamic", "hybrid"]),
       "priority": st.integers(min_value=1, max_value=10),
       "timeout": st.integers(min_value=60, max_value=3600),
       "metadata": st.dictionaries(
           keys=st.text(min_size=1, max_size=50),
           values=st.one_of(st.text(), st.integers(), st.booleans())
       )
   })

Property Testing Examples
-------------------------

Property tests verify system invariants across generated inputs.

**File Processing Properties:**

.. code-block:: python

   from hypothesis import given, assume
   import hypothesis.strategies as st
   
   @given(file_data=st.binary(min_size=1, max_size=1024))
   def test_file_hash_consistency(file_data):
       """File hash should be consistent across multiple calculations"""
       hash1 = calculate_file_hash(file_data)
       hash2 = calculate_file_hash(file_data)
       assert hash1 == hash2
       assert len(hash1) == 64  # SHA-256 hex length
       assert all(c in "0123456789abcdef" for c in hash1)
   
   @given(
       file_data=st.binary(min_size=1, max_size=10240),
       chunk_size=st.integers(min_value=1, max_value=1024)
   )
   def test_chunked_hash_equivalence(file_data, chunk_size):
       """Chunked and full file hashing should produce same result"""
       full_hash = calculate_file_hash(file_data)
       chunked_hash = calculate_chunked_hash(file_data, chunk_size)
       assert full_hash == chunked_hash

**API Validation Properties:**

.. code-block:: python

   @given(request_data=api_request_strategy)
   def test_api_request_validation(request_data):
       """API should validate all request fields properly"""
       response = api_client.post("/api/analyze", json=request_data)
       
       # Valid requests should succeed or fail predictably
       if is_valid_request(request_data):
           assert response.status_code in [200, 201, 202]
           assert "analysis_id" in response.json()
       else:
           assert response.status_code == 400
           assert "error" in response.json()
   
   @given(
       file_hash=file_hash_strategy,
       invalid_chars=st.text(alphabet="!@#$%^&*()", min_size=1, max_size=10)
   )
   def test_hash_injection_prevention(file_hash, invalid_chars):
       """System should reject hashes with invalid characters"""
       malicious_hash = file_hash + invalid_chars
       response = api_client.get(f"/api/analysis/{malicious_hash}")
       assert response.status_code == 400

Stateful Testing
----------------

Stateful testing models complex system interactions over time.

.. mermaid::

   graph LR
       A[Initial State] --> B[Action 1]
       B --> C[State 2]
       C --> D[Action 2]
       D --> E[State 3]
       E --> F[Invariant Check]

.. code-block:: python

   from hypothesis.stateful import RuleBasedStateMachine, rule, invariant
   import hypothesis.strategies as st
   
   class AnalysisSystemStateMachine(RuleBasedStateMachine):
       """Stateful testing for analysis system"""
       
       def __init__(self):
           super().__init__()
           self.analyses = {}
           self.files = {}
       
       @rule(file_data=st.binary(min_size=1, max_size=1024))
       def upload_file(self, file_data):
           """Upload a file to the system"""
           file_hash = calculate_hash(file_data)
           response = api_client.post("/api/upload", files={"file": file_data})
           
           if response.status_code == 201:
               self.files[file_hash] = {
                   "data": file_data,
                   "uploaded": True
               }
       
       @rule(file_hash=st.sampled_from_keys("files"))
       def start_analysis(self, file_hash):
           """Start analysis on uploaded file"""
           if file_hash in self.files:
               response = api_client.post(f"/api/analyze/{file_hash}")
               if response.status_code == 202:
                   analysis_id = response.json()["analysis_id"]
                   self.analyses[analysis_id] = {
                       "file_hash": file_hash,
                       "status": "pending"
                   }
       
       @invariant()
       def analysis_consistency(self):
           """Analyses should maintain consistent state"""
           for analysis_id, analysis in self.analyses.items():
               response = api_client.get(f"/api/analysis/{analysis_id}")
               if response.status_code == 200:
                   current_status = response.json()["status"]
                   # Status should only progress forward
                   assert current_status in ["pending", "running", "completed", "failed"]

Custom Strategies
-----------------

Custom strategies generate domain-specific test data.

.. code-block:: python

   # Custom strategy for malware samples
   @st.composite
   def malware_sample_strategy(draw):
       """Generate realistic malware sample metadata"""
       file_type = draw(st.sampled_from(["PE", "ELF", "Mach-O", "PDF", "Office"]))
       
       if file_type == "PE":
           architecture = draw(st.sampled_from(["x86", "x64"]))
           subsystem = draw(st.sampled_from(["console", "windows", "dll"]))
           return {
               "type": file_type,
               "architecture": architecture,
               "subsystem": subsystem,
               "entry_point": draw(st.integers(min_value=0x1000, max_value=0x10000)),
               "sections": draw(st.lists(st.text(min_size=1, max_size=8), min_size=1, max_size=10))
           }
       elif file_type == "PDF":
           return {
               "type": file_type,
               "version": draw(st.floats(min_value=1.0, max_value=2.0)),
               "pages": draw(st.integers(min_value=1, max_value=1000)),
               "encrypted": draw(st.booleans())
           }
   
   @given(sample=malware_sample_strategy())
   def test_malware_classification(sample):
       """Test malware classification across sample types"""
       result = classify_malware(sample)
       assert result["confidence"] >= 0.0
       assert result["confidence"] <= 1.0
       assert "classification" in result

Performance Testing with Hypothesis
-----------------------------------

Hypothesis can generate performance test cases automatically.

.. code-block:: python

   from hypothesis import given, settings
   import hypothesis.strategies as st
   import time
   
   @given(
       data_size=st.integers(min_value=1024, max_value=1024*1024),  # 1KB to 1MB
       chunk_size=st.integers(min_value=64, max_value=8192)
   )
   @settings(max_examples=50, deadline=5000)
   def test_processing_performance_properties(data_size, chunk_size):
       """Processing time should scale predictably with data size"""
       test_data = b"A" * data_size
       
       start_time = time.time()
       result = process_data(test_data, chunk_size)
       processing_time = time.time() - start_time
       
       # Performance should scale roughly linearly
       time_per_byte = processing_time / data_size
       assert time_per_byte < 0.001  # Less than 1ms per byte
       
       # Larger chunks should be more efficient
       if chunk_size > 1024:
           assert time_per_byte < 0.0005  # Better performance with larger chunks

Hypothesis Database
-------------------

Hypothesis database stores examples for reproducible testing.

.. code-block:: python

   # Configure Hypothesis database
   from hypothesis import settings
   from hypothesis.database import ExampleDatabase
   
   # Use file-based database for persistence
   settings.register_profile("persistent",
       database=ExampleDatabase(".hypothesis/examples"),
       max_examples=200
   )
   
   # Disable database for CI (faster startup)
   settings.register_profile("ci",
       database=None,
       max_examples=1000
   )

Error Reproduction
------------------

Hypothesis provides tools for reproducing and debugging failures.

.. code-block:: python

   from hypothesis import given, example, reproduce_failure
   
   @given(st.text())
   @example("specific_failing_case")  # Always test this specific case
   def test_text_processing(text):
       """Test text processing with generated and specific examples"""
       result = process_text(text)
       assert isinstance(result, str)
       assert len(result) >= 0
   
   # Reproduce specific failure
   @reproduce_failure('6.82.4', b'AAAAAAAAAA==')
   @given(st.text())
   def test_reproduce_specific_failure(text):
       """Reproduce a specific failure case"""
       # This will run the exact failing case first
       pass

Configuration Profiles
----------------------

Different Hypothesis profiles for different testing scenarios.

.. code-block:: python

   # Development profile - fast feedback
   settings.register_profile("dev",
       max_examples=20,
       deadline=500,
       verbosity=Verbosity.normal
   )
   
   # Thorough testing profile
   settings.register_profile("thorough",
       max_examples=2000,
       deadline=10000,
       verbosity=Verbosity.verbose
   )
   
   # Debugging profile
   settings.register_profile("debug",
       max_examples=5,
       verbosity=Verbosity.debug,
       print_blob=True
   )

Best Practices
--------------

**Hypothesis Testing Principles:**

1. **Focus on Properties**: Test invariants, not specific examples
2. **Use Appropriate Strategies**: Match strategies to domain constraints
3. **Handle Assumptions**: Use `assume()` to filter invalid inputs
4. **Minimize Examples**: Use shrinking to find minimal failing cases
5. **Combine with Unit Tests**: Use both property and example-based tests

**Common Patterns:**

.. code-block:: python

   # Property testing pattern
   @given(st.data())
   def test_roundtrip_property(data):
       """Data should survive encode/decode roundtrip"""
       original = data.draw(st.binary())
       encoded = encode_data(original)
       decoded = decode_data(encoded)
       assert decoded == original
   
   # Stateful testing pattern
   @rule(action=st.sampled_from(["create", "update", "delete"]))
   def perform_action(self, action):
       """Perform state-changing action"""
       # Implementation depends on action
       pass

Integration with pytest
-----------------------

Hypothesis integrates seamlessly with pytest for comprehensive testing.

.. code-block:: bash

   # Run property tests
   pytest -m property
   
   # Run with specific Hypothesis profile
   HYPOTHESIS_PROFILE=thorough pytest tests/property/
   
   # Debug property test failures
   pytest tests/property/test_file.py -v --hypothesis-show-statistics

Related Documentation
---------------------

* :doc:`property-testing` - Property-based testing guide
* :doc:`unit-testing` - Unit testing fundamentals
* :doc:`performance-testing` - Performance property testing
* :doc:`troubleshooting` - Hypothesis troubleshooting guide
