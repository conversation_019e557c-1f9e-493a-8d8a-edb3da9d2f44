💩🎉TurdParty🎉💩 Integration Testing
=====================================

Overview
--------

TurdParty uses a **revolutionary 3-tier integration testing strategy** that provides fast feedback for developers while ensuring comprehensive coverage for production deployments. Our tiered approach eliminates the traditional trade-off between speed and thoroughness.

**Why Tiered Integration Testing:**

- **⚡ Fast CI/CD Feedback**: Tier 1 tests complete in < 2 minutes for every commit
- **🎯 Comprehensive Coverage**: Tier 3 tests provide full production-like validation
- **🚀 Developer Productivity**: No more waiting hours for test results
- **📊 Intelligent Resource Usage**: Right level of testing for each scenario

Tiered Testing Architecture
---------------------------

.. mermaid::

   graph TD
       A[💩🎉TurdParty🎉💩 Integration Tests] --> B[⚡ Tier 1: Fast Tests]
       A --> C[🏃 Tier 2: Medium Tests]
       A --> D[🐌 Tier 3: Full Tests]

       B --> E[Mock-based Testing]
       B --> F[Container Operations]
       B --> G[API Contract Validation]
       B --> H[< 2 minutes total]

       C --> I[Real but Constrained]
       C --> J[Docker VMs Only]
       C --> K[Small Files & Timeouts]
       C --> L[< 10 minutes total]

       D --> M[Production-like Testing]
       D --> N[Real VMs & Services]
       D --> O[Full Binary Analysis]
       D --> P[< 60 minutes total]

**🎯 Test Tier Selection Strategy:**

.. list-table:: When to Use Each Tier
   :header-rows: 1
   :widths: 20 20 20 40

   * - **Tier**
     - **Duration**
     - **Use Case**
     - **Coverage**
   * - **⚡ Tier 1 (Fast)**
     - < 2 minutes
     - Every commit, PR
     - API contracts, basic workflows
   * - **🏃 Tier 2 (Medium)**
     - < 10 minutes
     - Nightly, release branches
     - Real integration, constrained
   * - **🐌 Tier 3 (Full)**
     - < 60 minutes
     - Weekly, major releases
     - Complete E2E, production-like

⚡ Tier 1: Fast Integration Tests (< 2 minutes)
=================================================

**Purpose**: Lightning-fast feedback for every commit and pull request.

**Strategy**: Mock-based testing with zero infrastructure dependencies.

**Target Runtime**: < 2 minutes total for all Tier 1 tests.

Fibratus Light Tests
--------------------

Mock-based Fibratus integration tests for rapid CI/CD feedback.

.. code-block:: python

   @pytest.mark.tier1
   def test_fibratus_integration_light():
       """Test Fibratus integration with mocked operations"""
       with patch('requests.post') as mock_post:
           # Mock VM creation
           mock_post.return_value.status_code = 201
           mock_post.return_value.json.return_value = {
               "vm_id": "mock-vm-123",
               "status": "creating",
               "template": "ubuntu:latest"
           }

           # Test VM creation API contract
           vm_data = {
               "name": "fibratus-test",
               "template": "ubuntu:latest",
               "vm_type": "docker",
               "memory_mb": 512,
               "cpus": 1
           }

           response = requests.post("/api/v1/vms/", json=vm_data)
           assert response.status_code == 201

           vm_info = response.json()
           assert "vm_id" in vm_info
           assert vm_info["template"] == "ubuntu:latest"

**Fibratus Light Features:**

- **Mock binary downloads** (no 8MB+ real files)
- **Mock VM creation** (no 10-minute timeouts)
- **Mock telemetry collection** (sample ECS data)
- **API contract validation** (ensure proper interfaces)
- **Error handling scenarios** (test failure modes)

**Runtime**: ~30 seconds for 9 comprehensive tests

Workflow Light Tests
--------------------

Container-based workflow tests with minimal resource usage.

.. code-block:: python

   @pytest.mark.tier1
   def test_workflow_integration_light():
       """Test complete workflow with lightweight operations"""
       # Use small test file (1KB vs 8MB)
       test_content = "Test malware sample\n" * 50  # ~1KB

       with tempfile.NamedTemporaryFile(suffix='.txt') as f:
           f.write(test_content.encode())
           f.flush()

           # Mock file upload
           with patch('requests.post') as mock_post:
               mock_post.return_value.status_code = 201
               mock_post.return_value.json.return_value = {
                   "file_id": "light-file-123",
                   "size": len(test_content),
                   "status": "uploaded"
               }

               # Test file upload
               files = {'file': ('test.txt', open(f.name, 'rb'))}
               response = requests.post("/api/v1/files/upload", files=files)

               assert response.status_code == 201
               assert response.json()["size"] == len(test_content)

**Workflow Light Features:**

- **Docker containers** (5s startup vs 2min VMs)
- **Small test files** (1KB vs 8MB)
- **Short monitoring** (10s vs 5min)
- **Mock ELK integration** (in-memory data)
- **Concurrent operations** (2-3 VMs max)

**Runtime**: ~45 seconds for 8 workflow tests

Performance Light Tests
-----------------------

Micro-benchmark tests for rapid performance validation.

.. code-block:: python

   @pytest.mark.tier1
   def test_api_response_time_light():
       """Test API response time with lightweight requests"""
       response_times = []

       with patch('requests.get') as mock_get:
           # Mock fast API responses (50ms)
           mock_get.return_value.status_code = 200
           mock_get.return_value.elapsed.total_seconds.return_value = 0.05

           # Test multiple API calls
           for i in range(10):
               start_time = time.time()
               response = requests.get("/api/v1/health")
               response_time = time.time() - start_time
               response_times.append(response_time * 1000)  # Convert to ms

           # Validate response times
           avg_response_time = sum(response_times) / len(response_times)
           assert avg_response_time < 1000, f"Average response time {avg_response_time:.2f}ms exceeds 1000ms"

**Performance Light Features:**

- **Micro-benchmarks** (5 concurrent requests vs 100)
- **Memory monitoring** (100MB threshold)
- **CPU usage validation** (50% threshold)
- **Database query simulation** (mocked operations)
- **Regression detection** (baseline comparison)

**Runtime**: ~20 seconds for 8 performance tests

VM Operations Light Tests
-------------------------

Docker-only VM tests with pre-pulled images for maximum speed.

.. code-block:: python

   @pytest.mark.tier1
   def test_vm_lifecycle_light():
       """Test VM lifecycle with Docker containers"""
       mock_vm_id = f"light-vm-{int(time.time())}"

       with patch('requests.post') as mock_post:
           # Mock fast VM creation
           mock_post.return_value.status_code = 201
           mock_post.return_value.json.return_value = {
               "vm_id": mock_vm_id,
               "vm_type": "docker",
               "template": "alpine:latest",  # Pre-pulled image
               "status": "creating"
           }

           # Test VM creation
           vm_data = {
               "name": "light-test",
               "template": "alpine:latest",
               "vm_type": "docker",
               "memory_mb": 128,  # Minimal memory
               "cpus": 1
           }

           start_time = time.time()
           response = requests.post("/api/v1/vms/", json=vm_data)
           creation_time = time.time() - start_time

           assert response.status_code == 201
           assert creation_time < 5.0  # 5 seconds vs 2 minutes

**VM Operations Light Features:**

- **Pre-pulled images** (alpine, ubuntu, busybox)
- **Fast timeouts** (5s vs 2min)
- **Minimal resources** (64-256MB memory)
- **Basic lifecycle testing** (create, status, delete)
- **Command execution** (mocked operations)

**Runtime**: ~25 seconds for 8 VM operation tests

🏃 Tier 2: Medium Integration Tests (< 10 minutes)
===================================================

**Purpose**: Real but constrained integration testing for nightly builds and release branches.

**Strategy**: Real services with lightweight configurations and reduced timeouts.

**Target Runtime**: < 10 minutes total for all Tier 2 tests.

.. note::
   **🚧 Tier 2 tests are planned for future implementation.**

   These tests will provide a middle ground between fast mocked tests and full production-like tests:

   - **Real Docker VMs** (no Windows VMs)
   - **Small real binaries** (1MB test executables vs 8MB+ samples)
   - **Reduced timeouts** (2min vs 10min)
   - **Short monitoring** (30s vs 5min)
   - **Real ELK integration** (with constrained data)

Fibratus Medium Tests (Planned)
-------------------------------

Real but lightweight Fibratus integration with Docker VMs.

.. code-block:: python

   @pytest.mark.tier2
   def test_fibratus_integration_medium():
       """Test Fibratus with real but constrained operations"""
       # Use small real binary (1MB test executable)
       test_binary = "tests/data/small_test_app.exe"  # 1MB vs 8MB

       # Create Docker VM (not Windows)
       vm_data = {
           "name": "fibratus-medium-test",
           "template": "ubuntu:latest",
           "vm_type": "docker",
           "memory_mb": 512,
           "cpus": 1
       }

       response = api_client.post("/api/v1/vms/", json=vm_data)
       vm_id = response.json()["vm_id"]

       # Wait for VM (2min timeout vs 10min)
       vm_ready = wait_for_vm_ready(vm_id, timeout=120)
       assert vm_ready

       # Inject small binary
       with open(test_binary, 'rb') as f:
           injection_response = api_client.post(
               f"/api/v1/vms/{vm_id}/inject",
               files={"file": f}
           )

       # Monitor for 30 seconds (vs 5 minutes)
       telemetry = monitor_vm_telemetry(vm_id, duration=30)
       assert len(telemetry) >= 5  # Expect some events

**Estimated Runtime**: ~5 minutes for medium Fibratus tests

Tier 3: Full Integration Tests (< 60 minutes)
===============================================

**Purpose**: Complete production-like testing for comprehensive validation before major releases.

**Strategy**: Real services, real VMs, real binaries with full timeouts and monitoring.

**Target Runtime**: < 60 minutes total for all Tier 3 tests.

.. note::
   **Tier 3 tests are the existing heavy integration tests with improvements:**

   - **Real Windows VMs** (full Vagrant/libvirt support)
   - **Real malware binaries** (8MB+ samples)
   - **Full timeouts** (10min VM creation, 5min monitoring)
   - **Complete ELK pipeline** (real Elasticsearch indexing)
   - **Production-like scenarios** (multiple concurrent analyses)

Real VM Integration Tests
-------------------------

Full VM lifecycle testing with Windows templates and real malware analysis.

.. code-block:: python

   @pytest.mark.tier3
   @pytest.mark.slow
   def test_complete_malware_analysis_workflow():
       """Test complete malware analysis with real VMs and binaries"""
       # Use real malware sample
       malware_sample = "tests/data/notepadplusplus_installer.exe"  # 8MB+

       # Create Windows VM
       vm_data = {
           "name": "full-analysis-test",
           "template": "windows10",
           "vm_type": "vagrant",
           "memory_mb": 2048,
           "cpus": 2,
           "domain": "TurdParty"
       }

       response = api_client.post("/api/v1/vms/", json=vm_data)
       vm_id = response.json()["vm_id"]

       # Wait for VM to be ready (10 minute timeout)
       vm_ready = wait_for_vm_ready(vm_id, timeout=600)
       assert vm_ready, "VM failed to become ready within 10 minutes"

       # Inject real binary
       with open(malware_sample, 'rb') as f:
           injection_response = api_client.post(
               f"/api/v1/vms/{vm_id}/inject",
               files={"malware": f}
           )

       injection_id = injection_response.json()["injection_id"]

       # Wait for injection completion (10 minute timeout)
       injection_complete = wait_for_injection_complete(
           vm_id, injection_id, timeout=600
       )
       assert injection_complete

       # Monitor for full duration (5 minutes)
       telemetry = monitor_vm_telemetry(vm_id, duration=300)

       # Verify comprehensive telemetry collection
       assert len(telemetry) >= 50  # Expect substantial telemetry

       # Verify ECS data in Elasticsearch
       es_query = {
           "query": {
               "bool": {
                   "must": [
                       {"term": {"turdparty.vm_id": vm_id}},
                       {"term": {"turdparty.injection_id": injection_id}}
                   ]
               }
           }
       }

       es_response = elasticsearch_client.search(
           index="turdparty-*", body=es_query
       )

       assert es_response["hits"]["total"]["value"] >= 50

       # Cleanup
       cleanup_vm(vm_id)

**Estimated Runtime**: ~45 minutes for comprehensive analysis tests

Test Execution Strategy
-----------------------

Run the appropriate tier based on your testing needs and time constraints.

**Tier 1: Fast Tests (Every Commit/PR)**

.. code-block:: bash

   # Run all Tier 1 fast tests (< 2 minutes)
   pytest tests/integration/test_fibratus_light.py \
          tests/integration/test_workflow_light.py \
          tests/integration/test_performance_light.py \
          tests/integration/test_vm_operations_light.py \
          -v --tb=short --disable-warnings

   # Or use the parallel script (recommended)
   scripts/run-parallel-tests.sh

**Tier 2: Medium Tests (Nightly/Release Branches)**

.. code-block:: bash

   # Run Tier 2 tests when implemented (< 10 minutes)
   TURDPARTY_TEST_TIER=medium pytest tests/integration/ -m tier2

   # Or with environment variable
   export TURDPARTY_TEST_TIER=medium
   scripts/run-parallel-tests.sh

**Tier 3: Full Tests (Weekly/Major Releases)**

.. code-block:: bash

   # Run all integration tests including heavy ones (< 60 minutes)
   TURDPARTY_TEST_TIER=full pytest tests/integration/ -v

   # Run specific heavy test categories
   pytest tests/integration/test_fibratus_real_injection.py -v
   pytest tests/integration/test_complete_workflow.py -v
   pytest tests/integration/test_performance_load.py -v

**Environment Variables**

.. code-block:: bash

   # Control test tier execution
   export TURDPARTY_TEST_TIER=fast    # Default: Tier 1 only
   export TURDPARTY_TEST_TIER=medium  # Tier 1 + Tier 2
   export TURDPARTY_TEST_TIER=full    # All tiers

   # Skip infrastructure-dependent tests
   export TURDPARTY_SKIP_INFRASTRUCTURE=true

   # Control parallel execution
   export TURDPARTY_MAX_PARALLEL_JOBS=6

Performance Comparison
--------------------

Our tiered approach delivers dramatic performance improvements while maintaining comprehensive coverage.

.. list-table:: Performance Comparison
   :header-rows: 1
   :widths: 25 25 25 25

   * - **Metric**
     - **Before (Heavy)**
     - **After (Tier 1)**
     - **Improvement**
   * - **Integration Test Runtime**
     - 484+ seconds
     - 3.91 seconds
     - **900x faster**
   * - **Total Parallel Script**
     - 95 seconds
     - 38 seconds
     - **60% faster**
   * - **Infrastructure Dependencies**
     - Required
     - Zero
     - **CI/CD ready**
   * - **Developer Feedback Time**
     - 8+ hours
     - < 2 minutes
     - **240x faster**

**Real-World Impact:**

- **Before**: Developers waited hours for integration test feedback
- **After**: Developers get feedback in under 2 minutes on every commit
- **CI/CD**: Pipelines complete 60% faster with more comprehensive coverage
- **Resource Usage**: 90% reduction in compute resources for regular testing

Test Data Management
--------------------

Each tier uses appropriate test data for its performance requirements.

**Tier 1 Test Data:**

.. code-block:: python

   @pytest.fixture(scope="class")
   def light_test_data():
       """Lightweight test data for fast tests"""
       return {
           "small_file": "Test content\n" * 50,  # ~1KB
           "mock_binary": b"MOCK_BINARY_DATA",
           "test_vm_template": "alpine:latest",
           "monitoring_duration": 5,  # 5 seconds
           "expected_events": 3
       }

**Tier 2 Test Data (Planned):**

.. code-block:: python

   @pytest.fixture(scope="class")
   def medium_test_data():
       """Medium-sized test data for constrained real testing"""
       return {
           "small_binary": "tests/data/small_test_app.exe",  # 1MB
           "docker_template": "ubuntu:latest",
           "monitoring_duration": 30,  # 30 seconds
           "timeout": 120  # 2 minutes
       }

**Tier 3 Test Data:**

.. code-block:: python

   @pytest.fixture(scope="class")
   def full_test_data():
       """Full test data for comprehensive testing"""
       return {
           "real_malware": "tests/data/notepadplusplus_installer.exe",  # 8MB+
           "windows_template": "windows10",
           "monitoring_duration": 300,  # 5 minutes
           "timeout": 600  # 10 minutes
       }

Best Practices
--------------

**Tiered Testing Principles:**

1. **Choose the Right Tier**: Use Tier 1 for rapid feedback, Tier 3 for comprehensive validation
2. **Mock Appropriately**: Tier 1 uses mocks for speed, Tier 3 uses real services for accuracy
3. **Optimize for Feedback Speed**: Prioritize fast tests in CI/CD pipelines
4. **Maintain Test Independence**: Each tier should provide value independently
5. **Resource Management**: Use appropriate resources for each tier's requirements

**Tier Selection Guidelines:**

.. code-block:: python

   # Tier 1: Every commit, PR, local development
   @pytest.mark.tier1
   def test_api_contract_validation():
       """Fast contract validation with mocks"""
       pass

   # Tier 2: Nightly builds, release branches
   @pytest.mark.tier2
   def test_constrained_integration():
       """Real but lightweight integration"""
       pass

   # Tier 3: Weekly, major releases, pre-production
   @pytest.mark.tier3
   @pytest.mark.slow
   def test_full_production_scenario():
       """Complete production-like testing"""
       pass

**Infrastructure Requirements:**

.. list-table:: Infrastructure by Tier
   :header-rows: 1
   :widths: 20 30 50

   * - **Tier**
     - **Requirements**
     - **Notes**
   * - **Tier 1**
     - None (mocked)
     - Runs anywhere, no setup needed
   * - **Tier 2**
     - Docker only
     - Lightweight containers, pre-pulled images
   * - **Tier 3**
     - Full stack
     - Docker, Vagrant, ELK, MinIO, all services

**Migration from Heavy Tests:**

If you have existing heavy integration tests, migrate them using this strategy:

1. **Create Tier 1 version** with mocked dependencies
2. **Keep original as Tier 3** with infrastructure checks
3. **Add Tier 2 version** (optional) with constrained real operations
4. **Update CI/CD** to use appropriate tier for each scenario

UUID Run Tracking & Footprint Analysis
======================================

💩🎉TurdParty🎉💩 implements sophisticated **multiple run tracking** for the same UUID, enabling comprehensive analysis of malware behavior changes between executions and distinguishing between **install footprint** and **runtime footprint**.

Multiple Run Architecture
-------------------------

.. mermaid::

   graph TD
       A[File UUID] --> B[Run 1: uuid-run-1]
       A --> C[Run 2: uuid-run-2]
       A --> D[Run N: uuid-run-N]

       B --> E[Install Events]
       B --> F[Runtime Events]

       C --> G[Install Events]
       C --> H[Runtime Events]

       D --> I[Install Events]
       D --> J[Runtime Events]

       E --> K[Install ECS Index]
       F --> L[Runtime ECS Index]
       G --> K
       H --> L
       I --> K
       J --> L

       style A fill:#ff9999
       style K fill:#99ccff
       style L fill:#99ffcc

Run Tracking System
------------------

**Sequential Run Numbers**
   Each UUID execution gets an incremental run number (1, 2, 3, etc.)

**Unique Run IDs**
   Format: ``{file_id}-run-{run_number}`` (e.g., ``malware-uuid-123-run-2``)

**Persistent Storage**
   Run metadata stored in ``/tmp/turdparty_runs.json`` with full execution history

**Event Correlation**
   All events for a run share the same ``run_id`` for easy correlation

Install vs Runtime Footprint Distinction
----------------------------------------

.. mermaid::

   graph LR
       A[File Execution] --> B{Phase}

       B -->|Install Phase| C[Install Footprint]
       B -->|Runtime Phase| D[Runtime Footprint]

       C --> E[File System Changes]
       C --> F[Registry Modifications]
       C --> G[Service Installation]
       C --> H[Persistence Mechanisms]

       D --> I[Process Execution]
       D --> J[Network Connections]
       D --> K[API Calls]
       D --> L[Data Exfiltration]

       E --> M[turdparty-install-ecs-*]
       F --> M
       G --> M
       H --> M

       I --> N[turdparty-runtime-ecs-*]
       J --> N
       K --> N
       L --> N

       style C fill:#ffcccc
       style D fill:#ccffcc
       style M fill:#ff9999
       style N fill:#99ff99

ECS Event Structure
------------------

**Enhanced TurdParty Fields:**

.. code-block:: json

   {
     "turdparty": {
       "file_id": "uuid-of-file",
       "run_id": "uuid-of-file-run-2",
       "run_number": 2,
       "execution_type": "install|runtime",
       "footprint_type": "install|runtime",
       "phase": "install|runtime",
       "install_uuid": "unique-install-event-id",
       "runtime_uuid": "unique-runtime-event-id",
       "workflow_id": "workflow-identifier",
       "vm_id": "vm-instance-identifier"
     }
   }

**Separate ECS Indexes:**

* **Install Events**: ``turdparty-install-ecs-YYYY.MM.DD``
* **Runtime Events**: ``turdparty-runtime-ecs-YYYY.MM.DD``

Run Comparison & Analysis
------------------------

.. mermaid::

   graph TD
       A[Run Tracker] --> B[Run 1 Data]
       A --> C[Run 2 Data]

       B --> D[Install Events: 5]
       B --> E[Runtime Events: 10]
       B --> F[Duration: 120s]

       C --> G[Install Events: 7]
       C --> H[Runtime Events: 15]
       C --> I[Duration: 95s]

       D --> J[Delta Analysis]
       E --> J
       F --> J
       G --> J
       H --> J
       I --> J

       J --> K[Install Delta: +2]
       J --> L[Runtime Delta: +5]
       J --> M[Time Delta: -25s]

       style J fill:#ffff99
       style K fill:#ffcccc
       style L fill:#ccffcc
       style M fill:#ccccff

**Automatic Delta Calculation:**

.. code-block:: json

   {
     "comparison": {
       "differences": {
         "install_events_delta": 2,
         "runtime_events_delta": 5,
         "time_difference": "25.0 seconds faster"
       }
     }
   }

Practical Use Cases
------------------

**1. Malware Behavior Analysis**

.. mermaid::

   graph LR
       A[First Execution] --> B[High Install Events]
       A --> C[Moderate Runtime Events]

       D[Second Execution] --> E[Low Install Events]
       D --> F[High Runtime Events]

       B --> G[Persistence Installation]
       C --> H[Initial Payload]

       E --> I[Skip Installation]
       F --> J[Full Malicious Activity]

       style A fill:#ffcccc
       style D fill:#ccffcc
       style G fill:#ff9999
       style I fill:#99ff99

**2. Evasion Detection**

* **Run 1**: Benign behavior to establish trust
* **Run 2**: Malicious behavior after avoiding detection
* **Analysis**: Delta reveals evasion techniques

**3. Environment Adaptation**

* **Different VMs**: Same UUID in various environments
* **Footprint Comparison**: How malware adapts to systems
* **Persistence Analysis**: What changes between environments

Query Examples
-------------

**Find All Runs of a UUID:**

.. code-block:: bash

   # Elasticsearch query
   GET /turdparty-*/_search
   {
     "query": {
       "term": { "turdparty.file_id": "your-uuid-here" }
     },
     "sort": [{ "turdparty.run_number": "asc" }]
   }

**Compare Install vs Runtime for Same Run:**

.. code-block:: bash

   # Install events for run 2
   GET /turdparty-install-ecs-*/_search
   {
     "query": {
       "term": { "turdparty.run_id": "uuid-run-2" }
     }
   }

   # Runtime events for run 2
   GET /turdparty-runtime-ecs-*/_search
   {
     "query": {
       "term": { "turdparty.run_id": "uuid-run-2" }
     }
   }

**Run Comparison Analysis:**

.. code-block:: python

   from utils.run_tracker import compare_runs

   # Compare two runs of the same file
   comparison = compare_runs("malware-uuid-123", 1, 2)

   print(f"Install events changed by: {comparison['differences']['install_events_delta']}")
   print(f"Runtime events changed by: {comparison['differences']['runtime_events_delta']}")

Implementation Integration
-------------------------

**File Injection Workflow:**

.. code-block:: python

   # Start new run
   run_number, run_id = start_file_run(file_id, workflow_id, vm_id)

   # Log install events with run tracking
   monitor.log_install_event(workflow_id, vm_id, file_id, event_data, run_number)

   # Log runtime events with run tracking
   monitor.log_runtime_event(workflow_id, vm_id, file_id, event_data, run_number)

   # Complete run with event counts
   complete_file_run(file_id, run_number,
                    install_events=5, runtime_events=12,
                    install_uuid=install_uuid)

**ServiceURLManager Integration:**

All run tracking and analysis tools use centralized URL management:

* **Elasticsearch**: ``http://elasticsearch.turdparty.localhost``
* **Kibana**: ``http://kibana.turdparty.localhost``
* **Reports**: Generated with proper Traefik routing

Related Documentation
---------------------

* :doc:`unit-testing` - Unit testing fundamentals
* :doc:`real-vm-testing` - VM integration testing (Tier 3)
* :doc:`performance-testing` - Performance testing across tiers
* :doc:`ci-cd` - CI/CD pipeline configuration for tiered testing
* :doc:`troubleshooting` - Troubleshooting integration tests
