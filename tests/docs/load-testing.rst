Load Testing
============

Overview
--------

Load testing validates TurdParty's performance under realistic user loads, identifying bottlenecks and capacity limits. Load tests ensure the platform remains responsive during peak usage and scales appropriately.

**Why Load Testing Exists:**

- **Capacity Planning**: Determine maximum concurrent users and analyses
- **Performance Validation**: Ensure response times remain acceptable under load
- **Bottleneck Identification**: Find system constraints before production
- **Scalability Testing**: Verify horizontal and vertical scaling effectiveness

Load Testing Architecture
-------------------------

.. mermaid::

   graph TD
       A[Load Testing] --> B[User Simulation]
       A --> C[Performance Monitoring]
       A --> D[Resource Tracking]
       
       B --> E[Locust Workers]
       B --> F[Test Scenarios]
       B --> G[Load Patterns]
       
       C --> H[Response Times]
       C --> I[Throughput]
       C --> J[Error Rates]
       
       D --> K[CPU Usage]
       D --> L[Memory Usage]
       D --> M[Database Load]
       D --> N[Network I/O]

Load Testing Tools
------------------

TurdParty uses Locust for realistic load simulation with Python-based test scenarios.

.. code-block:: python

   from locust import HttpUser, task, between
   
   class MalwareAnalysisUser(HttpUser):
       """Simulate typical malware analysis user behaviour"""
       wait_time = between(5, 15)  # 5-15 seconds between requests
       
       def on_start(self):
           """Login and setup user session"""
           response = self.client.post("/auth/login", json={
               "username": "test_user",
               "password": "test_password"
           })
           self.token = response.json()["access_token"]
           self.client.headers.update({
               "Authorization": f"Bearer {self.token}"
           })
       
       @task(3)
       def upload_sample(self):
           """Upload malware sample (most common action)"""
           with open("tests/data/sample.exe", "rb") as f:
               self.client.post("/api/upload", files={"file": f})
       
       @task(2)
       def check_analysis_status(self):
           """Check analysis status (frequent polling)"""
           self.client.get("/api/analyses?status=running")
       
       @task(1)
       def download_report(self):
           """Download analysis report (less frequent)"""
           self.client.get("/api/reports/latest")

Load Test Scenarios
-------------------

Different load patterns simulate various usage scenarios.

**Steady Load Testing:**

.. code-block:: python

   @pytest.mark.load
   def test_steady_load_capacity():
       """Test system capacity under steady load"""
       load_config = {
           "users": 50,
           "spawn_rate": 5,  # 5 users per second
           "duration": "10m"
       }
       
       results = run_locust_test(
           user_class=MalwareAnalysisUser,
           **load_config
       )
       
       # Verify performance criteria
       assert results.avg_response_time < 2000  # < 2 seconds
       assert results.failure_rate < 0.01       # < 1% failures
       assert results.requests_per_second > 10  # > 10 RPS

**Spike Load Testing:**

.. mermaid::

   graph LR
       A[Normal Load] --> B[Sudden Spike]
       B --> C[Peak Load]
       C --> D[Return to Normal]
       
       E[Metrics] --> F[Response Time]
       E --> G[Error Rate]
       E --> H[Recovery Time]

.. code-block:: python

   @pytest.mark.load
   def test_spike_load_handling():
       """Test system behaviour during traffic spikes"""
       # Start with normal load
       normal_load = start_load_test(users=10, spawn_rate=2)
       time.sleep(60)  # Run for 1 minute
       
       # Spike to high load
       spike_load = increase_load(users=100, spawn_rate=20)
       spike_metrics = collect_metrics(duration=120)  # 2 minutes
       
       # Return to normal
       reduce_load(users=10, spawn_rate=5)
       recovery_metrics = collect_metrics(duration=60)
       
       # Verify spike handling
       assert spike_metrics.max_response_time < 5000  # < 5 seconds
       assert spike_metrics.error_rate < 0.05         # < 5% errors
       assert recovery_metrics.avg_response_time < 2000  # Quick recovery

Database Load Testing
---------------------

Database load tests verify data layer performance under concurrent access.

.. code-block:: python

   @pytest.mark.load
   @pytest.mark.database
   def test_database_concurrent_access():
       """Test database performance with concurrent queries"""
       
       def database_workload():
           """Simulate typical database operations"""
           with database.get_connection() as conn:
               # Read operations (80% of load)
               for _ in range(8):
                   conn.execute("SELECT * FROM analyses WHERE status = 'completed' LIMIT 10")
               
               # Write operations (20% of load)
               for _ in range(2):
                   conn.execute(
                       "INSERT INTO analysis_logs (analysis_id, message) VALUES (%s, %s)",
                       (random.randint(1, 1000), "Test log message")
                   )
       
       # Run concurrent database workload
       with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
           futures = [executor.submit(database_workload) for _ in range(100)]
           
           start_time = time.time()
           results = [future.result() for future in futures]
           duration = time.time() - start_time
       
       # Verify database performance
       assert duration < 30  # Complete within 30 seconds
       assert all(result is None for result in results)  # No exceptions

VM Load Testing
---------------

VM load tests verify virtual machine management under concurrent analysis requests.

.. mermaid::

   graph TD
       A[Load Test] --> B[VM Pool]
       B --> C[VM1: Analysis A]
       B --> D[VM2: Analysis B]
       B --> E[VM3: Analysis C]
       B --> F[VM4: Analysis D]
       
       G[Metrics] --> H[VM Creation Time]
       G --> I[Analysis Queue Length]
       G --> J[Resource Utilization]
       G --> K[VM Cleanup Time]

.. code-block:: python

   @pytest.mark.load
   @pytest.mark.vm
   def test_vm_pool_under_load():
       """Test VM management with concurrent analysis requests"""
       
       def submit_vm_analysis():
           """Submit analysis requiring VM"""
           response = api_client.post("/api/vm-analysis", json={
               "file_hash": generate_random_hash(),
               "analysis_type": "dynamic",
               "timeout": 300
           })
           return response.status_code == 202
       
       # Submit many concurrent VM analyses
       with concurrent.futures.ThreadPoolExecutor(max_workers=15) as executor:
           futures = [executor.submit(submit_vm_analysis) for _ in range(50)]
           
           # Monitor VM pool metrics
           vm_metrics = monitor_vm_pool(duration=300)  # 5 minutes
           
           results = [future.result() for future in futures]
       
       # Verify VM pool performance
       assert sum(results) >= 45  # At least 90% success rate
       assert vm_metrics.avg_creation_time < 60  # < 1 minute VM creation
       assert vm_metrics.max_queue_length < 20   # Reasonable queue size

API Load Testing
----------------

API load tests verify REST endpoint performance under high request volumes.

.. code-block:: python

   @pytest.mark.load
   def test_api_endpoint_performance():
       """Test API performance under load"""
       
       # Define API test scenarios
       api_scenarios = [
           {"method": "GET", "url": "/api/analyses", "weight": 40},
           {"method": "POST", "url": "/api/upload", "weight": 30},
           {"method": "GET", "url": "/api/reports/{id}", "weight": 20},
           {"method": "DELETE", "url": "/api/analyses/{id}", "weight": 10}
       ]
       
       def api_request_worker():
           """Execute weighted API requests"""
           scenario = random.choices(
               api_scenarios, 
               weights=[s["weight"] for s in api_scenarios]
           )[0]
           
           if "{id}" in scenario["url"]:
               url = scenario["url"].replace("{id}", str(random.randint(1, 1000)))
           else:
               url = scenario["url"]
           
           response = api_client.request(scenario["method"], url)
           return response.status_code < 500  # Not server error
       
       # Execute load test
       results = []
       start_time = time.time()
       
       with concurrent.futures.ThreadPoolExecutor(max_workers=25) as executor:
           futures = [executor.submit(api_request_worker) for _ in range(1000)]
           results = [future.result() for future in futures]
       
       duration = time.time() - start_time
       
       # Verify API performance
       success_rate = sum(results) / len(results)
       requests_per_second = len(results) / duration
       
       assert success_rate > 0.95  # > 95% success rate
       assert requests_per_second > 20  # > 20 RPS

Memory Load Testing
-------------------

Memory load tests verify system behaviour under memory pressure.

.. code-block:: python

   @pytest.mark.load
   @pytest.mark.memory
   def test_memory_usage_under_load():
       """Test memory usage during high load"""
       import psutil
       
       # Monitor memory before load test
       initial_memory = psutil.virtual_memory().percent
       
       # Generate memory-intensive workload
       def memory_intensive_analysis():
           """Simulate memory-heavy analysis"""
           large_data = bytearray(10 * 1024 * 1024)  # 10MB
           response = api_client.post("/api/analyse-large",
                                    data=large_data)
           return response.status_code == 202
       
       # Run concurrent memory-intensive operations
       with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
           futures = [executor.submit(memory_intensive_analysis) for _ in range(20)]
           
           # Monitor memory during execution
           peak_memory = initial_memory
           for _ in range(30):  # Monitor for 30 seconds
               current_memory = psutil.virtual_memory().percent
               peak_memory = max(peak_memory, current_memory)
               time.sleep(1)
           
           results = [future.result() for future in futures]
       
       # Verify memory usage stays reasonable
       memory_increase = peak_memory - initial_memory
       assert memory_increase < 50  # < 50% memory increase
       assert sum(results) > 15     # Most operations succeed

Load Test Reporting
-------------------

Load test results are collected and analysed for performance insights.

.. code-block:: python

   def generate_load_test_report(test_results):
       """Generate comprehensive load test report"""
       report = {
           "summary": {
               "total_requests": test_results.total_requests,
               "avg_response_time": test_results.avg_response_time,
               "95th_percentile": test_results.percentile_95,
               "99th_percentile": test_results.percentile_99,
               "failure_rate": test_results.failure_rate,
               "requests_per_second": test_results.rps
           },
           "performance_criteria": {
               "response_time_met": test_results.avg_response_time < 2000,
               "failure_rate_met": test_results.failure_rate < 0.01,
               "throughput_met": test_results.rps > 10
           },
           "bottlenecks": identify_bottlenecks(test_results),
           "recommendations": generate_recommendations(test_results)
       }
       
       return report

Best Practices
--------------

**Load Testing Principles:**

1. **Realistic Scenarios**: Model actual user behaviour patterns
2. **Gradual Ramp-up**: Increase load gradually to identify breaking points
3. **Monitor Resources**: Track CPU, memory, disk, and network usage
4. **Test Different Patterns**: Steady load, spike load, stress testing
5. **Automate Testing**: Include load tests in CI/CD pipeline

**Load Test Execution:**

.. code-block:: bash

   # Run basic load test
   locust -f tests/load/locustfile.py --host=http://localhost:8000
   
   # Run headless load test
   locust -f tests/load/locustfile.py --host=http://localhost:8000 \
          --users 50 --spawn-rate 5 --run-time 10m --html report.html
   
   # Run distributed load test
   locust -f tests/load/locustfile.py --master --host=http://localhost:8000
   locust -f tests/load/locustfile.py --worker --master-host=localhost

Performance Criteria
---------------------

Load tests validate against specific performance criteria:

- **Response Time**: 95% of requests < 2 seconds
- **Throughput**: > 10 requests per second per core
- **Error Rate**: < 1% of requests fail
- **Resource Usage**: < 80% CPU, < 90% memory
- **Scalability**: Linear performance improvement with resources

Related Documentation
---------------------

* :doc:`performance-testing` - Performance benchmarking
* :doc:`integration-testing` - Integration performance testing
* :doc:`troubleshooting` - Load testing troubleshooting
* :doc:`best-practices` - Performance optimisation guidelines
