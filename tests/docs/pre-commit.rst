Pre-commit Hooks
================

Overview
--------

Pre-commit hooks in TurdParty automatically enforce code quality, security, and consistency before code enters the repository. Pre-commit hooks catch issues early, preventing broken builds and maintaining high code standards.

**Why Pre-commit Hooks Exist:**

- **Early Detection**: Catch issues before they reach the repository
- **Consistency**: Enforce uniform code style and quality standards
- **Security**: Prevent secrets and vulnerabilities from being committed
- **Efficiency**: Automated checks reduce manual code review overhead

Pre-commit Architecture
-----------------------

.. mermaid::

   graph TD
       A[Git Commit] --> B[Pre-commit Hooks]
       B --> C[Code Formatting]
       B --> D[Linting]
       B --> E[Security Checks]
       B --> F[Test Execution]
       
       C --> G[Ruff Format]
       C --> H[Import Sorting]
       
       D --> I[Ruff Linting]
       D --> J[Type Checking]
       
       E --> K[Secret Detection]
       E --> L[Vulnerability Scan]
       
       F --> M[Unit Tests]
       F --> N[Security Tests]
       
       G --> O[Commit Success/Fail]
       I --> O
       K --> O
       M --> O

Configuration
-------------

Pre-commit configuration is defined in `.pre-commit-config.yaml`.

.. code-block:: yaml

   # .pre-commit-config.yaml
   repos:
     # Ruff - Fast Python linting and formatting
     - repo: https://github.com/astral-sh/ruff-pre-commit
       rev: v0.1.6
       hooks:
         - id: ruff
           name: Ruff Linter
           args: [--fix, --exit-non-zero-on-fix]
           types: [python]
         - id: ruff-format
           name: Ruff Formatter
           types: [python]
   
     # Built-in hooks for common checks
     - repo: https://github.com/pre-commit/pre-commit-hooks
       rev: v4.5.0
       hooks:
         - id: trailing-whitespace
           name: Trim Trailing Whitespace
         - id: end-of-file-fixer
           name: Fix End of Files
         - id: check-yaml
           name: Check YAML Syntax
         - id: check-toml
           name: Check TOML Syntax
         - id: check-json
           name: Check JSON Syntax
         - id: check-merge-conflict
           name: Check for Merge Conflicts
         - id: check-added-large-files
           name: Check for Large Files
           args: ['--maxkb=1000']
         - id: detect-private-key
           name: Detect Private Keys
         - id: check-case-conflict
           name: Check Case Conflicts
         - id: mixed-line-ending
           name: Check Line Endings
   
     # MyPy for type checking
     - repo: https://github.com/pre-commit/mirrors-mypy
       rev: v1.7.1
       hooks:
         - id: mypy
           name: MyPy Type Checking
           additional_dependencies: [types-requests, types-PyYAML]
           args: [--ignore-missing-imports, --strict]
           exclude: ^(tests/|migrations/)
   
     # Bandit for security scanning
     - repo: https://github.com/PyCQA/bandit
       rev: 1.7.5
       hooks:
         - id: bandit
           name: Bandit Security Scan
           args: [-r, -f, json, -o, bandit-report.json]
           exclude: ^tests/
   
     # Secret detection
     - repo: https://github.com/Yelp/detect-secrets
       rev: v1.4.0
       hooks:
         - id: detect-secrets
           name: Detect Secrets
           args: ['--baseline', '.secrets.baseline']
   
     # Dockerfile linting
     - repo: https://github.com/hadolint/hadolint
       rev: v2.12.0
       hooks:
         - id: hadolint-docker
           name: Dockerfile Linting
   
     # Shell script checking
     - repo: https://github.com/shellcheck-py/shellcheck-py
       rev: v0.9.0.6
       hooks:
         - id: shellcheck
           name: Shell Script Checking
   
     # Commit message validation
     - repo: https://github.com/commitizen-tools/commitizen
       rev: v3.12.0
       hooks:
         - id: commitizen
           name: Validate Commit Message
           stages: [commit-msg]

Hook Categories
---------------

Pre-commit hooks are organized by function and execution speed.

**Fast Hooks (< 1 second):**

.. code-block:: yaml

   # Quick formatting and basic checks
   - id: trailing-whitespace
   - id: end-of-file-fixer
   - id: check-yaml
   - id: check-json
   - id: detect-private-key

**Medium Hooks (1-10 seconds):**

.. code-block:: yaml

   # Code quality and linting
   - id: ruff
   - id: ruff-format
   - id: mypy
   - id: bandit

**Slow Hooks (> 10 seconds):**

.. code-block:: yaml

   # Comprehensive testing (optional in pre-commit)
   - repo: local
     hooks:
       - id: pytest-fast
         name: Fast Test Suite
         entry: pytest tests/unit/ -x --tb=short
         language: system
         pass_filenames: false
         stages: [manual]  # Only run when explicitly requested

Code Quality Hooks
-------------------

Code quality hooks ensure consistent style and catch common issues.

**Ruff Integration:**

.. code-block:: yaml

   - repo: https://github.com/astral-sh/ruff-pre-commit
     rev: v0.1.6
     hooks:
       - id: ruff
         name: Ruff Linter
         args: [--fix, --exit-non-zero-on-fix]
         files: ^(api|services|workers|tests)/.*\.py$
       - id: ruff-format
         name: Ruff Formatter
         files: ^(api|services|workers|tests)/.*\.py$

**Type Checking:**

.. code-block:: yaml

   - repo: https://github.com/pre-commit/mirrors-mypy
     rev: v1.7.1
     hooks:
       - id: mypy
         name: MyPy Type Checking
         additional_dependencies: [
           types-requests,
           types-PyYAML,
           types-redis,
           sqlalchemy[mypy]
         ]
         args: [
           --config-file=pyproject.toml,
           --strict,
           --ignore-missing-imports
         ]
         exclude: ^(tests/|migrations/|scripts/)

Security Hooks
--------------

Security hooks prevent vulnerabilities and secrets from entering the codebase.

.. mermaid::

   graph LR
       A[Code Changes] --> B[Secret Detection]
       A --> C[Vulnerability Scan]
       A --> D[Dependency Check]
       
       B --> E[API Keys]
       B --> F[Passwords]
       B --> G[Certificates]
       
       C --> H[Code Patterns]
       C --> I[Unsafe Functions]
       C --> J[SQL Injection]

**Secret Detection:**

.. code-block:: yaml

   - repo: https://github.com/Yelp/detect-secrets
     rev: v1.4.0
     hooks:
       - id: detect-secrets
         name: Detect Secrets
         args: [
           '--baseline', '.secrets.baseline',
           '--exclude-files', '.*\.lock$',
           '--exclude-files', '.*\.min\.js$'
         ]

**Security Scanning:**

.. code-block:: yaml

   - repo: https://github.com/PyCQA/bandit
     rev: 1.7.5
     hooks:
       - id: bandit
         name: Bandit Security Scan
         args: [
           -r,
           -f, json,
           -o, bandit-report.json,
           --skip, B101,B601  # Skip assert and shell injection in tests
         ]
         exclude: ^tests/

Documentation Hooks
-------------------

Documentation hooks ensure documentation quality and consistency.

.. code-block:: yaml

   # Markdown linting
   - repo: https://github.com/igorshubovych/markdownlint-cli
     rev: v0.37.0
     hooks:
       - id: markdownlint
         name: Markdown Linting
         args: [--fix]
         files: \.md$
   
   # Sphinx documentation
   - repo: local
     hooks:
       - id: sphinx-build
         name: Sphinx Documentation Build
         entry: sphinx-build -W -b html docs docs/_build/html
         language: system
         files: ^docs/.*\.(rst|py)$
         pass_filenames: false

Infrastructure Hooks
---------------------

Infrastructure hooks validate configuration files and deployment scripts.

.. code-block:: yaml

   # Docker validation
   - repo: https://github.com/hadolint/hadolint
     rev: v2.12.0
     hooks:
       - id: hadolint-docker
         name: Dockerfile Linting
         args: [--ignore, DL3008, --ignore, DL3009]
   
   # YAML validation
   - repo: https://github.com/adrienverge/yamllint
     rev: v1.33.0
     hooks:
       - id: yamllint
         name: YAML Linting
         args: [-c=.yamllint.yml]
   
   # Terraform validation (if used)
   - repo: https://github.com/antonbabenko/pre-commit-terraform
     rev: v1.83.6
     hooks:
       - id: terraform_fmt
         name: Terraform Format
       - id: terraform_validate
         name: Terraform Validate

Custom Hooks
------------

Custom hooks for project-specific requirements.

.. code-block:: yaml

   # Local custom hooks
   - repo: local
     hooks:
       # API schema validation
       - id: api-schema-check
         name: API Schema Validation
         entry: python scripts/validate_api_schema.py
         language: system
         files: ^api/.*\.py$
         pass_filenames: false
       
       # Database migration check
       - id: migration-check
         name: Database Migration Check
         entry: python scripts/check_migrations.py
         language: system
         files: ^migrations/.*\.py$
         pass_filenames: false
       
       # Test data validation
       - id: test-data-check
         name: Test Data Validation
         entry: python scripts/validate_test_data.py
         language: system
         files: ^tests/data/.*$
         pass_filenames: false

Performance Optimization
-------------------------

Pre-commit hook performance optimization for faster development workflow.

**Parallel Execution:**

.. code-block:: yaml

   # Enable parallel execution
   default_install_hook_types: [pre-commit, pre-push, commit-msg]
   default_stages: [commit, push]
   
   # Fail fast configuration
   fail_fast: false  # Continue running all hooks even if one fails

**Selective Hook Execution:**

.. code-block:: yaml

   # Only run on specific file types
   - id: ruff
     files: \.py$
     exclude: ^(migrations/|docs/)
   
   # Only run on changed files
   - id: mypy
     files: ^(api|services)/.*\.py$
     exclude: ^tests/

**Caching:**

.. code-block:: bash

   # Pre-commit uses caching automatically
   # Cache location: ~/.cache/pre-commit
   
   # Clear cache if needed
   pre-commit clean
   
   # Update hook repositories
   pre-commit autoupdate

Installation and Setup
----------------------

Pre-commit hook installation and configuration process.

.. code-block:: bash

   # Install pre-commit
   pip install pre-commit
   
   # Install hooks in repository
   pre-commit install
   
   # Install commit message hooks
   pre-commit install --hook-type commit-msg
   
   # Install pre-push hooks
   pre-commit install --hook-type pre-push

**Initial Setup:**

.. code-block:: bash

   # Run hooks on all files (initial setup)
   pre-commit run --all-files
   
   # Update hook versions
   pre-commit autoupdate
   
   # Test hook configuration
   pre-commit run --config .pre-commit-config.yaml

Hook Execution
--------------

Different ways to execute pre-commit hooks.

.. code-block:: bash

   # Automatic execution (on git commit)
   git commit -m "Add new feature"
   
   # Manual execution on all files
   pre-commit run --all-files
   
   # Run specific hook
   pre-commit run ruff
   
   # Run hooks on specific files
   pre-commit run --files api/models.py services/vm.py
   
   # Skip hooks for emergency commits
   git commit -m "Emergency fix" --no-verify

Bypass and Override
-------------------

Strategic bypassing of hooks when necessary.

.. code-block:: bash

   # Skip all hooks
   git commit --no-verify -m "Emergency commit"
   
   # Skip specific hook
   SKIP=mypy git commit -m "Skip type checking"
   
   # Skip multiple hooks
   SKIP=mypy,bandit git commit -m "Skip type checking and security scan"

**Emergency Procedures:**

.. code-block:: bash

   # Emergency commit process
   git add .
   git commit --no-verify -m "EMERGENCY: Critical security fix"
   git push
   
   # Follow up with proper commit
   pre-commit run --all-files  # Fix all issues
   git add .
   git commit -m "Fix pre-commit issues from emergency commit"

CI/CD Integration
-----------------

Pre-commit hooks in continuous integration pipelines.

.. mermaid::

   graph LR
       A[Developer Commit] --> B[Pre-commit Hooks]
       B --> C[Git Push]
       C --> D[CI Pipeline]
       D --> E[Pre-commit CI]
       E --> F[Additional Tests]
       F --> G[Deploy]

**GitHub Actions Integration:**

.. code-block:: yaml

   # .github/workflows/pre-commit.yml
   name: Pre-commit
   on: [push, pull_request]
   
   jobs:
     pre-commit:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v4
         - uses: actions/setup-python@v4
           with:
             python-version: "3.11"
         - uses: pre-commit/action@v3.0.0
           with:
             extra_args: --all-files

Troubleshooting
---------------

Common pre-commit hook issues and solutions.

**Hook Failures:**

.. code-block:: bash

   # Debug hook execution
   pre-commit run --verbose ruff
   
   # Check hook configuration
   pre-commit validate-config
   
   # Reinstall hooks
   pre-commit uninstall
   pre-commit install

**Performance Issues:**

.. code-block:: bash

   # Profile hook execution time
   time pre-commit run --all-files
   
   # Disable slow hooks for development
   SKIP=mypy,bandit git commit -m "Fast commit"

Best Practices
--------------

**Pre-commit Hook Principles:**

1. **Fast Feedback**: Keep hooks fast for good developer experience
2. **Fail Fast**: Stop on first critical failure to save time
3. **Incremental**: Only run on changed files when possible
4. **Consistent**: Same checks in pre-commit and CI/CD
5. **Configurable**: Allow bypassing for emergency situations

**Hook Organization:**

.. code-block:: yaml

   # Organize hooks by speed and importance
   repos:
     # Fast, critical hooks first
     - repo: https://github.com/pre-commit/pre-commit-hooks
       # ... fast hooks
     
     # Medium speed hooks
     - repo: https://github.com/astral-sh/ruff-pre-commit
       # ... linting hooks
     
     # Slow hooks last (or manual only)
     - repo: local
       hooks:
         - id: comprehensive-tests
           stages: [manual]

Related Documentation
---------------------

* :doc:`ruff-config` - Ruff linting configuration
* :doc:`ci-cd` - CI/CD pipeline integration
* :doc:`best-practices` - Development best practices
* :doc:`troubleshooting` - Pre-commit troubleshooting guide
