pytest Configuration
===================

Overview
--------

pytest configuration in TurdParty provides consistent test execution, discovery, and reporting across all test types. Configuration ensures tests run reliably in development, CI, and production environments.

**Why pytest Configuration Matters:**

- **Consistency**: Same test behavior across environments and developers
- **Discovery**: Automatic test detection and organization
- **Reporting**: Comprehensive test results and coverage information
- **Performance**: Optimized test execution with parallel processing

Configuration Architecture
---------------------------

.. mermaid::

   graph TD
       A[pytest.ini] --> B[Test Discovery]
       A --> C[Test Execution]
       A --> D[Test Reporting]
       
       E[conftest.py] --> F[Fixtures]
       E --> G[Plugins]
       E --> H[Hooks]
       
       I[pyproject.toml] --> J[Tool Configuration]
       I --> K[Dependencies]
       
       B --> L[Unit Tests]
       B --> M[Integration Tests]
       B --> N[Property Tests]
       B --> O[Performance Tests]

Core Configuration Files
-------------------------

pytest.ini
~~~~~~~~~~~

Primary pytest configuration file defining test discovery and execution behavior.

.. code-block:: ini

   [tool:pytest]
   # Test discovery
   testpaths = tests
   python_files = test_*.py *_test.py
   python_classes = Test* *Tests
   python_functions = test_*
   
   # Test markers
   markers =
       unit: Unit tests (fast, isolated)
       integration: Integration tests (slower, external dependencies)
       property: Property-based tests using Hypothesis
       performance: Performance benchmarks and load tests
       security: Security validation tests
       vm: Virtual machine tests (slowest)
       slow: Tests that take more than 1 second
       external: Tests requiring external services
       flaky: Tests that may fail intermittently
   
   # Execution options
   addopts = 
       --strict-markers
       --strict-config
       --tb=short
       --maxfail=10
       --durations=10
       --cov=api
       --cov=services
       --cov=workers
       --cov-report=term-missing
       --cov-report=html:htmlcov
       --cov-report=xml:coverage.xml
       --cov-fail-under=80
       --junitxml=test-results.xml
   
   # Filtering
   filterwarnings =
       error
       ignore::UserWarning
       ignore::DeprecationWarning:hypothesis.*
       ignore::PendingDeprecationWarning
   
   # Logging
   log_cli = true
   log_cli_level = INFO
   log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
   log_cli_date_format = %Y-%m-%d %H:%M:%S

conftest.py Structure
~~~~~~~~~~~~~~~~~~~~~

Global test configuration and shared fixtures.

.. code-block:: python

   # tests/conftest.py
   import pytest
   import asyncio
   from pathlib import Path
   
   # Plugin registration
   pytest_plugins = [
       "tests.fixtures.database",
       "tests.fixtures.api_client",
       "tests.fixtures.vm_manager",
       "tests.fixtures.test_data",
       "tests.fixtures.mock_services"
   ]
   
   def pytest_configure(config):
       """Configure pytest environment"""
       # Add custom markers
       config.addinivalue_line("markers", "slow: marks tests as slow")
       config.addinivalue_line("markers", "external: requires external services")
       
       # Set test environment
       os.environ["TESTING"] = "true"
       os.environ["LOG_LEVEL"] = "DEBUG"
   
   def pytest_collection_modifyitems(config, items):
       """Modify test collection"""
       # Auto-mark slow tests
       for item in items:
           if "slow" in item.nodeid or "integration" in item.nodeid:
               item.add_marker(pytest.mark.slow)
   
   @pytest.fixture(scope="session", autouse=True)
   def setup_test_environment():
       """Setup global test environment"""
       # Initialize test database
       setup_test_database()
       
       # Start test services
       start_test_services()
       
       yield
       
       # Cleanup
       cleanup_test_environment()

Test Markers
------------

Test markers categorize and control test execution.

.. mermaid::

   graph LR
       A[Test Markers] --> B[unit]
       A --> C[integration]
       A --> D[property]
       A --> E[performance]
       A --> F[security]
       A --> G[vm]
       
       B --> H[Fast Execution]
       C --> I[External Dependencies]
       D --> J[Hypothesis Testing]
       E --> K[Benchmarking]
       F --> L[Security Validation]
       G --> M[VM Management]

**Marker Usage:**

.. code-block:: python

   @pytest.mark.unit
   def test_file_hash_calculation():
       """Unit test for file hash calculation"""
       pass
   
   @pytest.mark.integration
   @pytest.mark.slow
   def test_database_integration():
       """Integration test with database"""
       pass
   
   @pytest.mark.property
   def test_file_validation_properties():
       """Property-based test for file validation"""
       pass
   
   @pytest.mark.performance
   @pytest.mark.benchmark
   def test_analysis_performance():
       """Performance benchmark test"""
       pass

**Running Specific Markers:**

.. code-block:: bash

   # Run only unit tests
   pytest -m unit
   
   # Run integration tests excluding slow ones
   pytest -m "integration and not slow"
   
   # Run all tests except VM tests
   pytest -m "not vm"
   
   # Run security and performance tests
   pytest -m "security or performance"

Coverage Configuration
----------------------

Coverage configuration ensures comprehensive test coverage reporting.

.. code-block:: ini

   # Coverage settings in pytest.ini
   [coverage:run]
   source = api, services, workers
   omit = 
       */tests/*
       */venv/*
       */migrations/*
       */settings/*
       */__pycache__/*
   
   [coverage:report]
   exclude_lines =
       pragma: no cover
       def __repr__
       raise AssertionError
       raise NotImplementedError
       if __name__ == .__main__.:
   
   [coverage:html]
   directory = htmlcov
   
   [coverage:xml]
   output = coverage.xml

Parallel Execution
------------------

pytest-xdist enables parallel test execution for faster feedback.

.. code-block:: ini

   # Parallel execution configuration
   addopts = 
       --numprocesses=auto
       --dist=loadscope
       --maxprocesses=4

.. code-block:: bash

   # Run tests in parallel
   pytest -n auto  # Use all CPU cores
   pytest -n 4     # Use 4 processes
   pytest --dist=loadfile  # Distribute by file

Plugin Configuration
--------------------

pytest plugins extend functionality for specific testing needs.

**Essential Plugins:**

.. code-block:: python

   # requirements-test.txt
   pytest>=7.4.0
   pytest-cov>=4.1.0
   pytest-xdist>=3.3.0
   pytest-mock>=3.11.0
   pytest-asyncio>=0.21.0
   pytest-benchmark>=4.0.0
   pytest-html>=3.2.0
   pytest-json-report>=1.5.0

**Plugin Configuration:**

.. code-block:: ini

   # pytest-asyncio configuration
   asyncio_mode = auto
   
   # pytest-benchmark configuration
   [tool:pytest-benchmark]
   min_rounds = 5
   max_time = 10.0
   warmup = true
   disable_gc = true
   
   # pytest-html configuration
   [tool:pytest-html]
   self_contained = true

Custom Fixtures
---------------

Custom fixtures provide reusable test setup and teardown.

.. code-block:: python

   # tests/fixtures/database.py
   @pytest.fixture(scope="session")
   def database_engine():
       """Provide database engine for tests"""
       engine = create_test_engine()
       yield engine
       engine.dispose()
   
   @pytest.fixture(scope="function")
   def database_transaction(database_engine):
       """Provide database transaction that rolls back"""
       connection = database_engine.connect()
       transaction = connection.begin()
       
       yield connection
       
       transaction.rollback()
       connection.close()
   
   @pytest.fixture(scope="function")
   def api_client():
       """Provide authenticated API client"""
       client = TestClient(app)
       token = create_test_token()
       client.headers.update({"Authorization": f"Bearer {token}"})
       return client

Environment-Specific Configuration
-----------------------------------

Different configurations for different environments.

.. mermaid::

   graph TD
       A[Environment Config] --> B[Local Development]
       A --> C[CI/CD Pipeline]
       A --> D[Production Testing]
       
       B --> E[Full Test Suite]
       B --> F[Fast Feedback]
       
       C --> G[Parallel Execution]
       C --> H[Coverage Reporting]
       
       D --> I[Smoke Tests]
       D --> J[Critical Path Tests]

**Local Development:**

.. code-block:: ini

   # pytest-local.ini
   [tool:pytest]
   addopts = 
       --tb=short
       --maxfail=3
       -x  # Stop on first failure
       --lf  # Run last failed tests first

**CI/CD Configuration:**

.. code-block:: ini

   # pytest-ci.ini
   [tool:pytest]
   addopts = 
       --strict-markers
       --strict-config
       --tb=short
       --maxfail=10
       --numprocesses=auto
       --cov-fail-under=80
       --junitxml=test-results.xml

Test Data Management
--------------------

Configuration for test data and fixtures.

.. code-block:: python

   # Test data configuration
   TEST_DATA_CONFIG = {
       "base_path": Path("tests/data"),
       "malware_samples": "samples/",
       "expected_results": "expected/",
       "fixtures": "fixtures/",
       "temp_dir": "/tmp/turdparty_tests"
   }
   
   @pytest.fixture(scope="session")
   def test_data_path():
       """Provide path to test data"""
       return TEST_DATA_CONFIG["base_path"]

Performance Optimization
------------------------

Configuration optimizations for faster test execution.

.. code-block:: python

   # Performance optimizations
   def pytest_configure(config):
       """Optimize pytest performance"""
       # Disable unnecessary plugins in CI
       if os.getenv("CI"):
           config.option.no_cov = True  # Disable coverage in some CI runs
       
       # Set optimal worker count
       if config.option.numprocesses == "auto":
           config.option.numprocesses = min(cpu_count(), 8)

**Fast Test Execution:**

.. code-block:: bash

   # Fast test runs for development
   pytest --lf --ff  # Last failed, then failed first
   pytest -x --tb=no  # Stop on first failure, no traceback
   pytest tests/unit/ --no-cov  # Skip coverage for speed

Debugging Configuration
-----------------------

Configuration for debugging test failures.

.. code-block:: ini

   # Debug configuration
   [tool:pytest-debug]
   addopts = 
       -v
       -s
       --tb=long
       --capture=no
       --log-cli-level=DEBUG

.. code-block:: bash

   # Debug specific test
   pytest tests/test_file.py::test_function -v -s --pdb
   
   # Debug with full output
   pytest --tb=long --capture=no --log-cli-level=DEBUG

Best Practices
--------------

**Configuration Management:**

1. **Environment Separation**: Different configs for dev/CI/prod
2. **Marker Consistency**: Use consistent marker naming across team
3. **Coverage Goals**: Set realistic coverage targets (80%+)
4. **Performance Monitoring**: Track test execution times
5. **Plugin Management**: Only include necessary plugins

**Common Patterns:**

.. code-block:: python

   # Conditional test execution
   @pytest.mark.skipif(not has_docker(), reason="Docker not available")
   def test_container_functionality():
       pass
   
   # Parametrized tests
   @pytest.mark.parametrize("input,expected", [
       ("test.exe", True),
       ("test.txt", False),
   ])
   def test_malware_detection(input, expected):
       assert is_malware(input) == expected

Related Documentation
---------------------

* :doc:`unit-testing` - Unit test configuration
* :doc:`integration-testing` - Integration test setup
* :doc:`performance-testing` - Performance test configuration
* :doc:`troubleshooting` - pytest troubleshooting guide
