Real VM Testing Documentation
=============================

.. highlight:: bash

This document provides comprehensive guidance for TurdParty's real VM testing suite - a zero-mock testing approach using actual Docker containers and Vagrant VMs.

Overview
--------

TurdParty implements **zero-mock VM testing** to ensure production-equivalent validation:

* **Real Docker Containers** - Actual container creation and management
* **Real Vagrant VMs** - Full virtual machines with VirtualBox
* **Production Equivalence** - Tests mirror production environments exactly
* **No Simulations** - All operations use real infrastructure components

Test Architecture
------------------

VM Test Components
~~~~~~~~~~~~~~~~~~

.. code-block:: text

    tests/integration/
    ├── test_real_vm_operations.py      # Docker container tests
    ├── test_real_vagrant_vms.py        # Vagrant VM tests
    └── test_real_vm_workflow.py        # End-to-end workflow tests

    scripts/
    ├── test-real-vm-core.sh           # Core VM functionality
    ├── test-real-docker-vms.sh        # Docker-specific tests
    └── run-real-vm-tests.sh           # Comprehensive test runner

Test Categories
~~~~~~~~~~~~~~~

**Docker VM Tests**
    * Ubuntu 20.04/22.04 container testing
    * Alpine Linux minimal container validation
    * CentOS enterprise container testing
    * Resource limit enforcement (memory/CPU)
    * Network connectivity validation
    * File operations and injection testing

**Vagrant VM Tests**
    * VirtualBox VM creation and management
    * SSH connectivity and command execution
    * VM provisioning with custom scripts
    * Windows VM testing (optional)
    * Complete VM lifecycle operations

**Workflow Integration Tests**
    * MinIO file storage operations
    * File injection into running VMs
    * Concurrent VM processing
    * Resource monitoring and metrics
    * Auto-termination after 30 minutes

Quick Start
-----------

Prerequisites
~~~~~~~~~~~~~

Ensure required software is installed and running:

.. code-block:: bash

    # Check Docker availability
    docker --version && docker info

    # Check Vagrant availability  
    vagrant --version

    # Check VirtualBox availability
    VBoxManage --version

    # Check TurdParty API availability
    curl http://localhost:8000/health/

Core VM Tests
~~~~~~~~~~~~~

Run the essential VM functionality tests:

.. code-block:: bash

    # Execute core VM tests (recommended)
    ./scripts/test-real-vm-core.sh

Expected output::

    🎯 Core Real VM Tests - PASSED 6/6
    ✅ Test 1: Ubuntu Docker VM Creation - PASSED
    ✅ Test 2: Alpine Linux Docker VM Creation - PASSED  
    ✅ Test 3: File Operations in VM - PASSED
    ✅ Test 4: Network Connectivity - PASSED
    ✅ Test 5: VM Lifecycle Operations - PASSED
    ✅ Test 6: Resource Monitoring - PASSED

Comprehensive Test Suite
~~~~~~~~~~~~~~~~~~~~~~~~

Run all VM tests including Vagrant VMs:

.. code-block:: bash

    # Run complete VM test suite
    ./scripts/run-real-vm-tests.sh

    # Include Windows VM tests (requires Windows Vagrant box)
    ./scripts/run-real-vm-tests.sh --windows

Python Integration Tests
~~~~~~~~~~~~~~~~~~~~~~~~

Execute Python-based integration tests:

.. code-block:: bash

    # Run with nix-shell for dependencies
    nix-shell -p python3Full python3Packages.pytest python3Packages.httpx python3Packages.docker python3Packages.minio --run "python3 -m pytest tests/integration/ -v"

    # Run specific test modules
    python3 -m pytest tests/integration/test_real_vm_operations.py -v
    python3 -m pytest tests/integration/test_real_vagrant_vms.py -v
    python3 -m pytest tests/integration/test_real_vm_workflow.py -v

Test Implementation
-------------------

Docker Container Tests
~~~~~~~~~~~~~~~~~~~~~~

Real Docker container creation and management:

.. code-block:: python

    def test_docker_ubuntu_vm_lifecycle(self):
        """Test complete Docker Ubuntu VM lifecycle."""
        
        # Create VM via API
        vm_data = {
            "name": f"test-ubuntu-{int(time.time())}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 512,
            "cpus": 1,
            "domain": "TurdParty"
        }
        
        response = client.post(f"{API_BASE_URL}/api/v1/vms/", json=vm_data)
        assert response.status_code == 201
        
        # Verify actual Docker container exists
        containers = DOCKER_CLIENT.containers.list(
            filters={"label": f"turdparty.vm.name={vm_data['name']}"}
        )
        assert len(containers) > 0

Vagrant VM Tests
~~~~~~~~~~~~~~~~

Real VirtualBox VM operations:

.. code-block:: python

    def test_vagrant_ubuntu_vm_lifecycle(self):
        """Test complete Vagrant Ubuntu VM lifecycle."""
        
        # Create Vagrantfile
        create_vagrantfile(vm_dir, "ubuntu/focal64", memory_mb=1024, cpus=1)
        
        # Execute vagrant up
        result = subprocess.run(
            ["vagrant", "up"],
            cwd=vm_dir,
            capture_output=True,
            timeout=600
        )
        
        assert result.returncode == 0
        
        # Test SSH connectivity
        ssh_result = subprocess.run(
            ["vagrant", "ssh", "-c", "cat /etc/os-release"],
            cwd=vm_dir,
            capture_output=True
        )
        assert ssh_result.returncode == 0

File Injection Tests
~~~~~~~~~~~~~~~~~~~~

Real file operations in VMs:

.. code-block:: python

    def test_file_injection_to_docker_vm(self):
        """Test injecting a real file into a Docker VM."""
        
        # Create test file
        test_content = "TurdParty VM test file"
        with open("/tmp/test_file.txt", "w") as f:
            f.write(test_content)
        
        # Inject into container using docker cp
        container.put_archive("/tmp/", open("/tmp/test_file.txt", "rb").read())
        
        # Verify file exists in container
        exec_result = container.exec_run("cat /tmp/test_file.txt")
        assert exec_result.exit_code == 0
        assert test_content in exec_result.output.decode()

Test Results
------------

Successful Execution
~~~~~~~~~~~~~~~~~~~~

Core VM tests demonstrate production-ready functionality:

.. code-block:: text

    🐳 Testing real Docker VM creation...
    ✅ Docker connected
    📊 API Response: 201
    ✅ VM created: 43e0f9e2-c073-4efc-bd99-96704614205c
    ✅ Docker container found: turdparty_vm_real-test-1749559242
    📊 Container status: running
    ✅ Operating System: Ubuntu 20.04.6 LTS
    ✅ TurdParty environment configured
    ✅ Memory limit: 512MB
    ✅ Alpine version: 3.21.3
    ✅ File creation successful
    ✅ Network connectivity validated
    ✅ Container lifecycle operations successful

Validation Coverage
~~~~~~~~~~~~~~~~~~~

**Container Operations**
    * Real Docker container creation and management
    * Ubuntu, Alpine, CentOS image support
    * Resource limit enforcement (memory/CPU)
    * TurdParty labeling and environment configuration

**File System Operations**
    * File creation, reading, and permissions
    * File injection using docker cp
    * Volume mounting and access validation

**Network Operations**
    * Container IP assignment and networking
    * External connectivity testing
    * Network isolation validation

**Lifecycle Operations**
    * Container start, stop, restart, pause
    * Graceful and forced termination
    * Resource cleanup and management

**Resource Monitoring**
    * Real-time CPU and memory usage
    * Container statistics collection
    * Resource limit validation

Test Markers
------------

Use pytest markers to run specific test categories:

.. code-block:: bash

    # Run only Docker VM tests
    pytest tests/integration/ -m docker

    # Run only Vagrant VM tests  
    pytest tests/integration/ -m vagrant

    # Run slow VM tests (full VMs)
    pytest tests/integration/ -m slow

    # Skip Windows VM tests
    pytest tests/integration/ -m "not windows"

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Docker daemon not running**
    Start Docker service and verify with ``docker info``

**Vagrant box not found**
    Download required boxes: ``vagrant box add ubuntu/focal64``

**VirtualBox not available**
    Install VirtualBox and verify with ``VBoxManage --version``

**Permission denied**
    Add user to docker group: ``sudo usermod -aG docker $USER``

**VM creation timeout**
    Increase timeout values for slow systems

Debug Commands
~~~~~~~~~~~~~~

.. code-block:: bash

    # Check Docker containers
    docker ps -a --filter "label=turdparty.vm=true"

    # Check Vagrant VMs
    vagrant global-status

    # View container logs
    docker logs <container_id>

    # Check VM test logs
    tail -f /tmp/turdparty_vm_tests/vm_tests_*.log

    # Monitor container resources
    docker stats $(docker ps --filter "label=turdparty.vm=true" -q)

Best Practices
--------------

Test Design
~~~~~~~~~~~

1. **Use real infrastructure** - No mocks for VM operations
2. **Test resource limits** - Verify memory/CPU enforcement
3. **Validate cleanup** - Ensure proper resource cleanup
4. **Test error conditions** - Handle failures gracefully
5. **Monitor performance** - Track test execution times

Resource Management
~~~~~~~~~~~~~~~~~~~

1. **Cleanup after tests** - Remove containers and VMs
2. **Use minimal resources** - Start with small allocations
3. **Parallel execution** - Run independent tests concurrently
4. **Timeout handling** - Set appropriate timeouts for operations
5. **Resource monitoring** - Track system resource usage

Security Considerations
~~~~~~~~~~~~~~~~~~~~~~~

1. **Isolated networks** - Use dedicated test networks
2. **Temporary data** - Use temporary files and directories
3. **Access controls** - Limit VM access and permissions
4. **Clean environments** - Start with fresh VM instances
5. **Audit logging** - Log all VM operations for security

Related Documentation
---------------------

* :doc:`unit-testing` - Unit testing guidelines
* :doc:`integration-testing` - Integration testing approach
* :doc:`performance-testing` - Performance testing guide
* `VM Management API <http://localhost:8000/docs>`_ - Interactive API reference
* `Main Testing Guide <../TESTING.md>`_ - Comprehensive testing documentation

This real VM testing approach ensures that TurdParty's VM management system is thoroughly validated with production-equivalent infrastructure, providing confidence in deployment and operation.
