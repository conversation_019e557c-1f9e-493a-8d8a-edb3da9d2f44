Test Reporting
==============

Overview
--------

Test reporting in TurdParty provides comprehensive insights into test execution, coverage, performance, and quality metrics. Effective reporting enables data-driven decisions about code quality and testing effectiveness.

**Why Test Reporting Matters:**

- **Quality Visibility**: Clear metrics on code quality and test coverage
- **Trend Analysis**: Track quality improvements and regressions over time
- **Decision Support**: Data-driven insights for development priorities
- **Compliance**: Documentation for quality standards and regulations

Reporting Architecture
----------------------

.. mermaid::

   graph TD
       A[Test Execution] --> B[Data Collection]
       B --> C[Coverage Analysis]
       B --> D[Performance Metrics]
       B --> E[Quality Metrics]
       
       C --> F[Line Coverage]
       C --> G[Branch Coverage]
       C --> H[Function Coverage]
       
       D --> I[Execution Time]
       D --> J[Memory Usage]
       D --> K[Benchmark Results]
       
       E --> L[Test Results]
       E --> M[Code Quality]
       E --> N[Security Findings]
       
       F --> O[HTML Reports]
       G --> O
       I --> P[Performance Reports]
       L --> Q[Test Summary]

Coverage Reporting
------------------

**pytest-cov Configuration:**

.. code-block:: ini

   # pytest.ini
   [tool:pytest]
   addopts = 
       --cov=api
       --cov=services
       --cov=workers
       --cov-report=html:htmlcov
       --cov-report=xml:coverage.xml
       --cov-report=term-missing
       --cov-report=json:coverage.json
       --cov-fail-under=80

**Advanced Coverage Configuration:**

.. code-block:: ini

   # .coveragerc
   [run]
   source = api, services, workers
   omit = 
       */tests/*
       */venv/*
       */migrations/*
       */__pycache__/*
       */conftest.py
   
   branch = true
   parallel = true
   
   [report]
   exclude_lines =
       pragma: no cover
       def __repr__
       if self.debug:
       if settings.DEBUG
       raise AssertionError
       raise NotImplementedError
       if 0:
       if __name__ == .__main__.:
       class .*\bProtocol\):
       @(abc\.)?abstractmethod
   
   [html]
   directory = htmlcov
   title = TurdParty Test Coverage Report
   
   [xml]
   output = coverage.xml
   
   [json]
   output = coverage.json

**Coverage Analysis Tools:**

.. code-block:: python

   import json
   import coverage
   
   def analyze_coverage_trends():
       """Analyze coverage trends over time"""
       with open('coverage.json') as f:
           current_coverage = json.load(f)
       
       # Load historical coverage data
       historical_data = load_historical_coverage()
       
       analysis = {
           "current_coverage": current_coverage["totals"]["percent_covered"],
           "coverage_trend": calculate_trend(historical_data),
           "uncovered_lines": current_coverage["totals"]["missing_lines"],
           "new_uncovered": find_new_uncovered_lines(current_coverage, historical_data[-1]),
           "coverage_by_module": {
               module: data["summary"]["percent_covered"]
               for module, data in current_coverage["files"].items()
           }
       }
       
       return analysis

**Coverage Visualization:**

.. code-block:: python

   import matplotlib.pyplot as plt
   import pandas as pd
   
   def generate_coverage_charts():
       """Generate coverage visualization charts"""
       coverage_data = load_coverage_history()
       
       # Coverage trend over time
       plt.figure(figsize=(12, 6))
       plt.subplot(1, 2, 1)
       plt.plot(coverage_data['date'], coverage_data['coverage'])
       plt.title('Coverage Trend Over Time')
       plt.xlabel('Date')
       plt.ylabel('Coverage %')
       plt.xticks(rotation=45)
       
       # Coverage by module
       plt.subplot(1, 2, 2)
       module_coverage = coverage_data.groupby('module')['coverage'].last()
       plt.bar(module_coverage.index, module_coverage.values)
       plt.title('Coverage by Module')
       plt.xlabel('Module')
       plt.ylabel('Coverage %')
       plt.xticks(rotation=45)
       
       plt.tight_layout()
       plt.savefig('coverage_report.png')

Performance Reporting
---------------------

**pytest-benchmark Integration:**

.. code-block:: python

   import pytest
   
   def test_file_processing_performance(benchmark):
       """Benchmark file processing performance"""
       test_file = create_test_file(size=1024*1024)  # 1MB
       
       result = benchmark(process_file, test_file)
       
       # Add custom metrics
       benchmark.extra_info.update({
           'file_size': len(test_file),
           'throughput_mb_per_sec': len(test_file) / benchmark.stats.mean / 1024 / 1024
       })
       
       return result

**Performance Trend Analysis:**

.. code-block:: python

   def analyze_performance_trends():
       """Analyze performance trends from benchmark data"""
       import json
       import statistics
       
       # Load benchmark results
       with open('.benchmarks/latest.json') as f:
           benchmark_data = json.load(f)
       
       performance_analysis = {}
       
       for benchmark in benchmark_data['benchmarks']:
           test_name = benchmark['name']
           stats = benchmark['stats']
           
           performance_analysis[test_name] = {
               'mean_time': stats['mean'],
               'std_dev': stats['stddev'],
               'min_time': stats['min'],
               'max_time': stats['max'],
               'ops_per_second': 1.0 / stats['mean'] if stats['mean'] > 0 else 0,
               'performance_grade': calculate_performance_grade(stats['mean'])
           }
       
       return performance_analysis

**Performance Dashboard:**

.. code-block:: python

   def generate_performance_dashboard():
       """Generate performance dashboard"""
       import plotly.graph_objects as go
       from plotly.subplots import make_subplots
       
       perf_data = load_performance_history()
       
       # Create subplots
       fig = make_subplots(
           rows=2, cols=2,
           subplot_titles=('Response Time Trend', 'Throughput Trend', 
                          'Memory Usage', 'Error Rate'),
           specs=[[{"secondary_y": False}, {"secondary_y": False}],
                  [{"secondary_y": False}, {"secondary_y": False}]]
       )
       
       # Response time trend
       fig.add_trace(
           go.Scatter(x=perf_data['date'], y=perf_data['response_time'],
                     name='Response Time', line=dict(color='blue')),
           row=1, col=1
       )
       
       # Throughput trend
       fig.add_trace(
           go.Scatter(x=perf_data['date'], y=perf_data['throughput'],
                     name='Throughput', line=dict(color='green')),
           row=1, col=2
       )
       
       # Memory usage
       fig.add_trace(
           go.Scatter(x=perf_data['date'], y=perf_data['memory_usage'],
                     name='Memory Usage', line=dict(color='orange')),
           row=2, col=1
       )
       
       # Error rate
       fig.add_trace(
           go.Scatter(x=perf_data['date'], y=perf_data['error_rate'],
                     name='Error Rate', line=dict(color='red')),
           row=2, col=2
       )
       
       fig.update_layout(title_text="Performance Dashboard")
       fig.write_html("performance_dashboard.html")

Quality Metrics Reporting
--------------------------

**Code Quality Metrics:**

.. code-block:: python

   def collect_quality_metrics():
       """Collect comprehensive code quality metrics"""
       import subprocess
       import json
       
       metrics = {}
       
       # Ruff linting results
       ruff_result = subprocess.run(
           ['ruff', 'check', '.', '--format=json'],
           capture_output=True, text=True
       )
       if ruff_result.stdout:
           ruff_issues = json.loads(ruff_result.stdout)
           metrics['linting'] = {
               'total_issues': len(ruff_issues),
               'issues_by_severity': count_by_severity(ruff_issues),
               'issues_by_category': count_by_category(ruff_issues)
           }
       
       # MyPy type checking results
       mypy_result = subprocess.run(
           ['mypy', 'api/', 'services/', '--json-report', 'mypy_report'],
           capture_output=True, text=True
       )
       
       # Bandit security scan results
       bandit_result = subprocess.run(
           ['bandit', '-r', '.', '-f', 'json'],
           capture_output=True, text=True
       )
       if bandit_result.stdout:
           bandit_data = json.loads(bandit_result.stdout)
           metrics['security'] = {
               'total_issues': len(bandit_data.get('results', [])),
               'high_severity': len([r for r in bandit_data.get('results', []) 
                                   if r['issue_severity'] == 'HIGH']),
               'medium_severity': len([r for r in bandit_data.get('results', []) 
                                     if r['issue_severity'] == 'MEDIUM'])
           }
       
       return metrics

**Test Quality Metrics:**

.. code-block:: python

   def analyze_test_quality():
       """Analyze test suite quality metrics"""
       import ast
       import os
       
       test_metrics = {
           'total_tests': 0,
           'test_files': 0,
           'avg_assertions_per_test': 0,
           'tests_without_assertions': 0,
           'duplicate_test_names': [],
           'long_tests': [],  # Tests with many lines
           'test_coverage_by_type': {}
       }
       
       test_names = set()
       total_assertions = 0
       
       for root, dirs, files in os.walk('tests/'):
           for file in files:
               if file.startswith('test_') and file.endswith('.py'):
                   test_metrics['test_files'] += 1
                   file_path = os.path.join(root, file)
                   
                   with open(file_path, 'r') as f:
                       tree = ast.parse(f.read())
                   
                   for node in ast.walk(tree):
                       if isinstance(node, ast.FunctionDef) and node.name.startswith('test_'):
                           test_metrics['total_tests'] += 1
                           
                           # Check for duplicate names
                           if node.name in test_names:
                               test_metrics['duplicate_test_names'].append(node.name)
                           test_names.add(node.name)
                           
                           # Count assertions
                           assertions = count_assertions(node)
                           total_assertions += assertions
                           
                           if assertions == 0:
                               test_metrics['tests_without_assertions'] += 1
                           
                           # Check test length
                           if len(node.body) > 20:  # More than 20 statements
                               test_metrics['long_tests'].append(node.name)
       
       if test_metrics['total_tests'] > 0:
           test_metrics['avg_assertions_per_test'] = total_assertions / test_metrics['total_tests']
       
       return test_metrics

Test Results Reporting
-----------------------

**JUnit XML Integration:**

.. code-block:: bash

   # Generate JUnit XML reports
   pytest --junitxml=test-results.xml tests/

**Custom Test Reporter:**

.. code-block:: python

   import pytest
   import json
   import time
   from datetime import datetime
   
   class CustomTestReporter:
       """Custom test reporter for detailed test analytics"""
       
       def __init__(self):
           self.test_results = []
           self.start_time = None
           
       def pytest_runtest_setup(self, item):
           """Called before each test"""
           self.start_time = time.time()
           
       def pytest_runtest_teardown(self, item, nextitem):
           """Called after each test"""
           duration = time.time() - self.start_time if self.start_time else 0
           
           result = {
               'test_name': item.name,
               'test_file': str(item.fspath),
               'duration': duration,
               'markers': [mark.name for mark in item.iter_markers()],
               'timestamp': datetime.utcnow().isoformat()
           }
           
           self.test_results.append(result)
           
       def pytest_sessionfinish(self, session, exitstatus):
           """Called after all tests complete"""
           summary = {
               'total_tests': len(self.test_results),
               'total_duration': sum(r['duration'] for r in self.test_results),
               'exit_status': exitstatus,
               'test_results': self.test_results
           }
           
           with open('custom_test_report.json', 'w') as f:
               json.dump(summary, f, indent=2)

**Test Trend Analysis:**

.. code-block:: python

   def analyze_test_trends():
       """Analyze test execution trends"""
       import pandas as pd
       import matplotlib.pyplot as plt
       
       # Load historical test data
       test_history = load_test_history()
       df = pd.DataFrame(test_history)
       
       # Calculate trends
       trends = {
           'test_count_trend': calculate_trend(df['total_tests']),
           'duration_trend': calculate_trend(df['total_duration']),
           'failure_rate_trend': calculate_trend(df['failure_rate']),
           'avg_test_duration': df['total_duration'] / df['total_tests']
       }
       
       # Generate trend charts
       fig, axes = plt.subplots(2, 2, figsize=(15, 10))
       
       # Test count over time
       axes[0, 0].plot(df['date'], df['total_tests'])
       axes[0, 0].set_title('Total Tests Over Time')
       axes[0, 0].set_xlabel('Date')
       axes[0, 0].set_ylabel('Number of Tests')
       
       # Test duration over time
       axes[0, 1].plot(df['date'], df['total_duration'])
       axes[0, 1].set_title('Test Duration Over Time')
       axes[0, 1].set_xlabel('Date')
       axes[0, 1].set_ylabel('Duration (seconds)')
       
       # Failure rate over time
       axes[1, 0].plot(df['date'], df['failure_rate'])
       axes[1, 0].set_title('Test Failure Rate Over Time')
       axes[1, 0].set_xlabel('Date')
       axes[1, 0].set_ylabel('Failure Rate (%)')
       
       # Average test duration
       axes[1, 1].plot(df['date'], trends['avg_test_duration'])
       axes[1, 1].set_title('Average Test Duration Over Time')
       axes[1, 1].set_xlabel('Date')
       axes[1, 1].set_ylabel('Duration (seconds)')
       
       plt.tight_layout()
       plt.savefig('test_trends.png')
       
       return trends

Comprehensive Reporting Dashboard
---------------------------------

**HTML Dashboard Generation:**

.. code-block:: python

   def generate_comprehensive_dashboard():
       """Generate comprehensive test reporting dashboard"""
       from jinja2 import Template
       
       # Collect all metrics
       coverage_data = analyze_coverage_trends()
       performance_data = analyze_performance_trends()
       quality_data = collect_quality_metrics()
       test_data = analyze_test_quality()
       
       # Dashboard template
       dashboard_template = Template("""
       <!DOCTYPE html>
       <html>
       <head>
           <title>TurdParty Test Dashboard</title>
           <style>
               body { font-family: Arial, sans-serif; margin: 20px; }
               .metric-card { 
                   border: 1px solid #ddd; 
                   padding: 20px; 
                   margin: 10px; 
                   border-radius: 5px; 
                   display: inline-block; 
                   width: 300px; 
               }
               .metric-value { font-size: 2em; font-weight: bold; }
               .metric-label { color: #666; }
               .status-good { color: green; }
               .status-warning { color: orange; }
               .status-error { color: red; }
           </style>
       </head>
       <body>
           <h1>TurdParty Test Dashboard</h1>
           <p>Generated: {{ timestamp }}</p>
           
           <h2>Coverage Metrics</h2>
           <div class="metric-card">
               <div class="metric-value {{ 'status-good' if coverage_data.current_coverage >= 80 else 'status-warning' }}">
                   {{ "%.1f"|format(coverage_data.current_coverage) }}%
               </div>
               <div class="metric-label">Test Coverage</div>
           </div>
           
           <h2>Performance Metrics</h2>
           {% for test_name, metrics in performance_data.items() %}
           <div class="metric-card">
               <div class="metric-value">{{ "%.2f"|format(metrics.mean_time * 1000) }}ms</div>
               <div class="metric-label">{{ test_name }}</div>
           </div>
           {% endfor %}
           
           <h2>Quality Metrics</h2>
           <div class="metric-card">
               <div class="metric-value {{ 'status-good' if quality_data.linting.total_issues == 0 else 'status-warning' }}">
                   {{ quality_data.linting.total_issues }}
               </div>
               <div class="metric-label">Linting Issues</div>
           </div>
           
           <h2>Test Suite Health</h2>
           <div class="metric-card">
               <div class="metric-value">{{ test_data.total_tests }}</div>
               <div class="metric-label">Total Tests</div>
           </div>
           <div class="metric-card">
               <div class="metric-value">{{ "%.1f"|format(test_data.avg_assertions_per_test) }}</div>
               <div class="metric-label">Avg Assertions per Test</div>
           </div>
       </body>
       </html>
       """)
       
       # Render dashboard
       dashboard_html = dashboard_template.render(
           timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
           coverage_data=coverage_data,
           performance_data=performance_data,
           quality_data=quality_data,
           test_data=test_data
       )
       
       with open('test_dashboard.html', 'w') as f:
           f.write(dashboard_html)

CI/CD Integration
-----------------

**GitHub Actions Reporting:**

.. code-block:: yaml

   # .github/workflows/test-reporting.yml
   - name: Generate Test Reports
     run: |
       pytest --cov --junitxml=test-results.xml --html=test-report.html
       python scripts/generate_dashboard.py
       
   - name: Upload Test Reports
     uses: actions/upload-artifact@v3
     with:
       name: test-reports
       path: |
         test-results.xml
         test-report.html
         test_dashboard.html
         coverage.xml
         htmlcov/
         
   - name: Comment PR with Results
     uses: actions/github-script@v6
     with:
       script: |
         const fs = require('fs');
         const coverage = JSON.parse(fs.readFileSync('coverage.json'));
         const coveragePercent = coverage.totals.percent_covered;
         
         github.rest.issues.createComment({
           issue_number: context.issue.number,
           owner: context.repo.owner,
           repo: context.repo.repo,
           body: `## Test Results\n\n✅ Coverage: ${coveragePercent.toFixed(1)}%\n\n[View detailed report](${process.env.GITHUB_SERVER_URL}/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId})`
         });

Best Practices
--------------

**Reporting Principles:**

1. **Actionable Insights**: Reports should guide specific actions
2. **Trend Analysis**: Track changes over time, not just current state
3. **Multiple Formats**: HTML for humans, JSON/XML for tools
4. **Automated Generation**: Integrate reporting into CI/CD pipeline
5. **Stakeholder-Specific**: Different reports for different audiences

**Report Optimization:**

.. code-block:: python

   def optimize_reporting():
       """Optimize reporting for performance and usefulness"""
       # Cache expensive calculations
       # Generate incremental reports
       # Use efficient data formats
       # Implement report archival
       pass

Related Documentation
---------------------

* :doc:`performance-testing` - Performance metrics and reporting
* :doc:`ci-cd` - CI/CD integration for reporting
* :doc:`best-practices` - Testing and reporting best practices
* :doc:`troubleshooting` - Reporting troubleshooting guide
