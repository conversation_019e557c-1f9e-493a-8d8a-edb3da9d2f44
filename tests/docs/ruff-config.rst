Ruff Configuration
==================

Overview
--------

Ruff configuration in TurdParty provides fast Python linting and formatting, ensuring code quality and consistency across the entire codebase. Ruff replaces multiple tools (flake8, isort, black) with a single, high-performance solution.

**Why Ruff Configuration Matters:**

- **Code Quality**: Catch bugs, style issues, and potential problems early
- **Consistency**: Enforce uniform code style across team and codebase
- **Performance**: 10-100x faster than traditional Python linters
- **Integration**: Seamless integration with editors, CI/CD, and pre-commit hooks

Ruff Architecture
-----------------

.. mermaid::

   graph TD
       A[Ruff Engine] --> B[Linting Rules]
       A --> C[Code Formatting]
       A --> D[Import Sorting]
       
       B --> E[Error Detection]
       B --> F[Style Enforcement]
       B --> G[Security Checks]
       
       C --> H[Code Formatting]
       C --> I[Line Length]
       C --> J[Indentation]
       
       D --> K[Import Organization]
       D --> L[Dependency Grouping]

Core Configuration
------------------

Ruff configuration is defined in `pyproject.toml` for centralized tool management.

.. code-block:: toml

   # pyproject.toml
   [tool.ruff]
   # Target Python 3.11+
   target-version = "py311"
   
   # File discovery
   include = ["*.py", "*.pyi", "**/pyproject.toml"]
   exclude = [
       ".bzr",
       ".direnv", 
       ".eggs",
       ".git",
       ".hg",
       ".mypy_cache",
       ".nox",
       ".pants.d",
       ".ruff_cache",
       ".svn",
       ".tox",
       ".venv",
       "__pypackages__",
       "_build",
       "buck-out",
       "build",
       "dist",
       "node_modules",
       "venv",
       "migrations",
   ]
   
   # Line length (matches Black)
   line-length = 88
   
   # Indentation
   indent-width = 4
   
   # Enable specific rule categories
   select = [
       "E",    # pycodestyle errors
       "W",    # pycodestyle warnings  
       "F",    # Pyflakes
       "I",    # isort
       "N",    # pep8-naming
       "D",    # pydocstyle
       "UP",   # pyupgrade
       "YTT",  # flake8-2020
       "ANN",  # flake8-annotations
       "S",    # flake8-bandit
       "BLE",  # flake8-blind-except
       "FBT",  # flake8-boolean-trap
       "B",    # flake8-bugbear
       "A",    # flake8-builtins
       "COM",  # flake8-commas
       "C4",   # flake8-comprehensions
       "DTZ",  # flake8-datetimez
       "T10",  # flake8-debugger
       "EM",   # flake8-errmsg
       "EXE",  # flake8-executable
       "FA",   # flake8-future-annotations
       "ISC",  # flake8-implicit-str-concat
       "ICN",  # flake8-import-conventions
       "G",    # flake8-logging-format
       "INP",  # flake8-no-pep420
       "PIE",  # flake8-pie
       "T20",  # flake8-print
       "PYI",  # flake8-pyi
       "PT",   # flake8-pytest-style
       "Q",    # flake8-quotes
       "RSE",  # flake8-raise
       "RET",  # flake8-return
       "SLF",  # flake8-self
       "SLOT", # flake8-slots
       "SIM",  # flake8-simplify
       "TID",  # flake8-tidy-imports
       "TCH",  # flake8-type-checking
       "INT",  # flake8-gettext
       "ARG",  # flake8-unused-arguments
       "PTH",  # flake8-use-pathlib
       "ERA",  # eradicate
       "PD",   # pandas-vet
       "PGH",  # pygrep-hooks
       "PL",   # Pylint
       "TRY",  # tryceratops
       "FLY",  # flynt
       "NPY",  # NumPy-specific rules
       "PERF", # Perflint
       "RUF",  # Ruff-specific rules
   ]
   
   # Disable specific rules
   ignore = [
       "D100",   # Missing docstring in public module
       "D104",   # Missing docstring in public package
       "D203",   # 1 blank line required before class docstring
       "D213",   # Multi-line docstring summary should start at the second line
       "ANN101", # Missing type annotation for self in method
       "ANN102", # Missing type annotation for cls in classmethod
       "S101",   # Use of assert detected (allow in tests)
       "PLR0913", # Too many arguments to function call
       "PLR2004", # Magic value used in comparison
   ]
   
   # Allow autofix for specific rules
   fixable = ["ALL"]
   unfixable = []
   
   # Allow unused variables when underscore-prefixed
   dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

Rule Categories
---------------

Ruff organizes rules into logical categories for targeted enforcement.

**Error Detection Rules:**

.. code-block:: python

   # F401 - Unused import
   import unused_module  # Ruff will flag this
   
   # F841 - Unused variable
   def process_data():
       unused_var = "test"  # Ruff will flag this
       return "result"
   
   # E711 - Comparison to None
   if value == None:  # Should be: if value is None:
       pass

**Security Rules (Bandit):**

.. code-block:: python

   # S108 - Hardcoded temporary file
   temp_file = "/tmp/temp.txt"  # Flagged as insecure
   
   # S105 - Hardcoded password
   PASSWORD = "secret123"  # Flagged as security risk
   
   # S301 - Pickle usage
   import pickle  # Flagged for security review

**Code Quality Rules:**

.. code-block:: python

   # B006 - Mutable default argument
   def process_items(items=[]):  # Should use None and create list inside
       pass
   
   # C901 - Function too complex
   def complex_function():  # Ruff will flag high cyclomatic complexity
       # Many nested if/else statements
       pass

Per-File Configuration
----------------------

Different rules can be applied to different file types and directories.

.. code-block:: toml

   [tool.ruff.per-file-ignores]
   # Tests can use assert statements and have relaxed docstring requirements
   "tests/**/*.py" = [
       "S101",   # Allow assert in tests
       "D100",   # Don't require module docstrings in tests
       "D103",   # Don't require function docstrings in tests
       "PLR2004", # Allow magic values in tests
       "ANN",    # Don't require type annotations in tests
   ]
   
   # Migration files have different requirements
   "*/migrations/*.py" = [
       "E501",   # Allow long lines in migrations
       "D",      # Don't require docstrings in migrations
   ]
   
   # Configuration files
   "*/settings/*.py" = [
       "F405",   # Allow star imports in settings
       "F403",   # Allow undefined names from star imports
   ]
   
   # Scripts and utilities
   "scripts/*.py" = [
       "T201",   # Allow print statements in scripts
       "S602",   # Allow subprocess with shell=True in scripts
   ]

Import Sorting Configuration
----------------------------

Ruff includes isort-compatible import sorting functionality.

.. code-block:: toml

   [tool.ruff.isort]
   # Import organization
   known-first-party = ["api", "services", "workers", "tests"]
   known-third-party = ["fastapi", "sqlalchemy", "pytest", "hypothesis"]
   
   # Section organization
   section-order = [
       "future",
       "standard-library", 
       "third-party",
       "first-party",
       "local-folder"
   ]
   
   # Formatting
   combine-as-imports = true
   force-wrap-aliases = true
   split-on-trailing-comma = true
   
   # Lines configuration
   lines-after-imports = 2
   lines-between-sections = 1

**Import Sorting Example:**

.. code-block:: python

   # Before Ruff formatting
   from api.models import Analysis
   import os
   from fastapi import FastAPI
   import sys
   from services.vm import VMManager
   
   # After Ruff formatting
   import os
   import sys
   
   from fastapi import FastAPI
   
   from api.models import Analysis
   from services.vm import VMManager

Formatting Configuration
------------------------

Ruff provides Black-compatible code formatting.

.. code-block:: toml

   [tool.ruff.format]
   # Use double quotes for strings
   quote-style = "double"
   
   # Use spaces around the equals sign in keyword arguments
   indent-style = "space"
   
   # Respect magic trailing commas
   skip-magic-trailing-comma = false
   
   # Automatically detect line endings
   line-ending = "auto"
   
   # Format docstrings
   docstring-code-format = true
   docstring-code-line-length = 72

**Formatting Examples:**

.. code-block:: python

   # Before formatting
   def analyze_file(file_path,analysis_type="static",timeout=300,verbose=False):
       if analysis_type=="dynamic":
           result=run_dynamic_analysis(file_path,timeout)
       else:
           result=run_static_analysis(file_path)
       return result
   
   # After Ruff formatting
   def analyze_file(
       file_path,
       analysis_type="static", 
       timeout=300,
       verbose=False
   ):
       if analysis_type == "dynamic":
           result = run_dynamic_analysis(file_path, timeout)
       else:
           result = run_static_analysis(file_path)
       return result

Docstring Configuration
-----------------------

Ruff enforces docstring conventions for better documentation.

.. code-block:: toml

   [tool.ruff.pydocstyle]
   # Use Google docstring convention
   convention = "google"
   
   # Ignore specific docstring rules
   ignore-decorators = ["typing.overload"]

**Docstring Examples:**

.. code-block:: python

   def analyze_malware(file_path: str, analysis_type: str = "static") -> dict:
       """Analyze malware sample using specified analysis type.
       
       Args:
           file_path: Path to the malware sample file
           analysis_type: Type of analysis ("static" or "dynamic")
           
       Returns:
           Dictionary containing analysis results with keys:
           - malware_detected: Boolean indicating if malware was found
           - confidence: Float between 0.0 and 1.0
           - details: Dictionary with detailed analysis information
           
       Raises:
           FileNotFoundError: If the specified file doesn't exist
           ValueError: If analysis_type is not supported
           
       Example:
           >>> result = analyze_malware("sample.exe", "static")
           >>> print(result["malware_detected"])
           True
       """
       pass

Integration with Development Tools
----------------------------------

Ruff integrates with various development tools for seamless workflow.

**VS Code Integration:**

.. code-block:: json

   // .vscode/settings.json
   {
       "python.linting.enabled": true,
       "python.linting.ruffEnabled": true,
       "python.formatting.provider": "none",
       "python.formatting.ruffPath": "ruff",
       "editor.formatOnSave": true,
       "editor.codeActionsOnSave": {
           "source.organizeImports": true,
           "source.fixAll": true
       }
   }

**Pre-commit Integration:**

.. code-block:: yaml

   # .pre-commit-config.yaml
   repos:
     - repo: https://github.com/astral-sh/ruff-pre-commit
       rev: v0.1.6
       hooks:
         - id: ruff
           args: [--fix, --exit-non-zero-on-fix]
         - id: ruff-format

CI/CD Integration
-----------------

Ruff runs in CI/CD pipelines for automated code quality enforcement.

.. mermaid::

   graph LR
       A[Code Push] --> B[CI Pipeline]
       B --> C[Ruff Check]
       C --> D[Ruff Format]
       D --> E[Tests]
       E --> F[Deploy]
       
       C --> G[Quality Gate]
       D --> G
       G --> H[Pass/Fail]

**GitHub Actions Integration:**

.. code-block:: yaml

   # .github/workflows/code-quality.yml
   name: Code Quality
   on: [push, pull_request]
   
   jobs:
     ruff:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v4
         - uses: actions/setup-python@v4
           with:
             python-version: "3.11"
         - name: Install Ruff
           run: pip install ruff
         - name: Run Ruff Linter
           run: ruff check .
         - name: Run Ruff Formatter
           run: ruff format --check .

Performance Optimization
-------------------------

Ruff configuration can be optimized for different performance requirements.

.. code-block:: toml

   [tool.ruff]
   # Cache configuration
   cache-dir = ".ruff_cache"
   
   # Respect gitignore
   respect-gitignore = true
   
   # File size limits
   max-doc-length = 72
   
   # Performance settings for large codebases
   preview = false  # Disable preview rules for stability

**Performance Commands:**

.. code-block:: bash

   # Fast check (no fixes)
   ruff check .
   
   # Check with fixes
   ruff check . --fix
   
   # Format code
   ruff format .
   
   # Check specific files
   ruff check api/ services/
   
   # Show statistics
   ruff check . --statistics

Custom Rules and Plugins
-------------------------

Ruff supports custom rules for project-specific requirements.

.. code-block:: toml

   [tool.ruff.flake8-naming]
   # Custom naming conventions
   classmethod-decorators = ["classmethod", "pydantic.validator"]
   staticmethod-decorators = ["staticmethod", "pydantic.root_validator"]
   
   [tool.ruff.flake8-pytest-style]
   # Pytest-specific rules
   fixture-parentheses = false
   parametrize-names-type = "tuple"
   parametrize-values-type = "tuple"
   parametrize-values-row-type = "tuple"

Error Suppression
-----------------

Strategic error suppression for specific cases.

.. code-block:: python

   # Suppress specific rule for one line
   import pickle  # noqa: S301
   
   # Suppress multiple rules
   password = "temp_password"  # noqa: S105, E501
   
   # Suppress for entire file
   # ruff: noqa
   
   # Suppress specific rule for entire file
   # ruff: noqa: S101

Best Practices
--------------

**Ruff Configuration Principles:**

1. **Start Strict**: Enable comprehensive rules, then selectively disable
2. **Consistent Formatting**: Use automatic formatting for consistency
3. **Gradual Adoption**: Introduce rules incrementally in existing codebases
4. **Team Alignment**: Ensure team agrees on style and quality standards
5. **CI Integration**: Enforce rules in continuous integration

**Common Patterns:**

.. code-block:: bash

   # Development workflow
   ruff check . --fix  # Fix auto-fixable issues
   ruff format .       # Format code
   
   # Pre-commit check
   ruff check . --diff  # Show what would be fixed
   
   # CI/CD check
   ruff check . --output-format=github  # GitHub Actions format

Related Documentation
---------------------

* :doc:`pre-commit` - Pre-commit hook configuration
* :doc:`ci-cd` - CI/CD pipeline integration
* :doc:`best-practices` - Code quality best practices
* :doc:`troubleshooting` - Ruff troubleshooting guide
