Security Testing
================

Overview
--------

Security testing validates TurdParty's defence mechanisms against malicious inputs, unauthorised access, and attack vectors. Security tests ensure the platform safely analyses malware without compromising the host system.

**Why Security Testing Exists:**

- **Malware Containment**: Prevent malware from escaping analysis environment
- **Input Validation**: Block malicious payloads and injection attacks
- **Access Control**: Verify authentication and authorization mechanisms
- **Data Protection**: Ensure sensitive data remains secure during analysis

Security Test Architecture
---------------------------

.. mermaid::

   graph TD
       A[Security Tests] --> B[Input Validation]
       A --> C[Container Security]
       A --> D[Authentication]
       A --> E[Authorization]
       
       B --> F[SQL Injection]
       B --> G[XSS Prevention]
       B --> H[File Upload Validation]
       B --> I[Command Injection]
       
       C --> J[Container Escape]
       C --> K[Privilege Escalation]
       C --> L[Resource Limits]
       C --> M[Network Isolation]
       
       D --> N[JWT Validation]
       D --> O[Session Management]
       D --> P[Rate Limiting]
       
       E --> Q[Role-Based Access]
       E --> R[API Permissions]
       E --> S[File Access Control]

Input Validation Tests
----------------------

Input validation prevents malicious data from compromising the system.

**Test Categories:**

.. code-block:: python

   # SQL Injection Prevention
   @pytest.mark.security
   def test_sql_injection_prevention():
       """Verify SQL injection attempts are blocked"""
       malicious_inputs = [
           "'; DROP TABLE users; --",
           "1' OR '1'='1",
           "UNION SELECT * FROM sensitive_data"
       ]
       
       for payload in malicious_inputs:
           response = api_client.post("/search", {"query": payload})
           assert response.status_code == 400
           assert "Invalid input" in response.json()["error"]

**Why These Tests Matter:**

- **Prevent Data Breaches**: Block unauthorized database access
- **Maintain Data Integrity**: Prevent data corruption or deletion
- **Compliance**: Meet security standards and regulations

Container Security Tests
------------------------

Container security tests verify malware cannot escape the analysis environment.

.. mermaid::

   graph LR
       A[Malware Sample] --> B[Analysis Container]
       B --> C[Security Boundaries]
       C --> D[Host System]
       
       E[Security Tests] --> F[Escape Attempts]
       F --> G[Privilege Escalation]
       F --> H[Network Breakout]
       F --> I[File System Access]
       
       G --> J[BLOCKED]
       H --> J
       I --> J

.. code-block:: python

   @pytest.mark.security
   @pytest.mark.vm
   def test_container_escape_prevention():
       """Verify malware cannot escape container"""
       with vm_manager.create_analysis_vm() as vm:
           # Attempt container escape techniques
           escape_commands = [
               "docker run --privileged -v /:/host alpine chroot /host",
               "nsenter -t 1 -m -u -i -n -p -- bash",
               "runc exec --user 0 container_id /bin/bash"
           ]
           
           for command in escape_commands:
               result = vm.execute(command)
               assert result.exit_code != 0
               assert "permission denied" in result.stderr.lower()

Authentication Tests
--------------------

Authentication tests verify user identity validation and session management.

.. code-block:: python

   @pytest.mark.security
   def test_jwt_token_validation():
       """Verify JWT tokens are properly validated"""
       # Test invalid tokens
       invalid_tokens = [
           "invalid.jwt.token",
           "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid",
           None,
           ""
       ]
       
       for token in invalid_tokens:
           headers = {"Authorization": f"Bearer {token}"} if token else {}
           response = api_client.get("/protected", headers=headers)
           assert response.status_code == 401

**Authentication Security Logic:**

1. **Token Validation**: Verify JWT signature and expiration
2. **Session Management**: Secure session creation and destruction
3. **Rate Limiting**: Prevent brute force attacks
4. **Multi-Factor**: Additional authentication factors when required

Authorization Tests
-------------------

Authorization tests verify users can only access permitted resources.

.. code-block:: python

   @pytest.mark.security
   def test_role_based_access_control():
       """Verify users can only access authorized resources"""
       # Test different user roles
       test_cases = [
           ("analyst", "/api/samples", 200),
           ("analyst", "/api/admin/users", 403),
           ("admin", "/api/admin/users", 200),
           ("guest", "/api/samples", 403)
       ]
       
       for role, endpoint, expected_status in test_cases:
           token = create_test_token(role=role)
           headers = {"Authorization": f"Bearer {token}"}
           response = api_client.get(endpoint, headers=headers)
           assert response.status_code == expected_status

Data Protection Tests
---------------------

Data protection tests ensure sensitive information remains secure.

.. mermaid::

   graph TD
       A[Sensitive Data] --> B[Encryption at Rest]
       A --> C[Encryption in Transit]
       A --> D[Access Logging]
       
       B --> E[Database Encryption]
       B --> F[File Encryption]
       
       C --> G[TLS/HTTPS]
       C --> H[API Encryption]
       
       D --> I[Audit Logs]
       D --> J[Access Monitoring]

.. code-block:: python

   @pytest.mark.security
   def test_sensitive_data_encryption():
       """Verify sensitive data is encrypted"""
       # Upload sensitive test file
       sensitive_data = "CONFIDENTIAL: Test malware sample"
       response = api_client.post("/upload", files={"file": sensitive_data})
       
       # Verify data is encrypted in storage
       file_id = response.json()["file_id"]
       stored_data = storage.read_file(file_id)
       
       # Data should not be readable in plain text
       assert sensitive_data not in stored_data.decode('utf-8', errors='ignore')
       assert len(stored_data) > len(sensitive_data)  # Encrypted data is larger

Vulnerability Scanning
----------------------

Automated vulnerability scanning identifies security weaknesses.

.. code-block:: python

   @pytest.mark.security
   def test_dependency_vulnerabilities():
       """Verify no known vulnerabilities in dependencies"""
       # Run security audit on dependencies
       result = subprocess.run(
           ["safety", "check", "--json"],
           capture_output=True,
           text=True
       )
       
       if result.returncode != 0:
           vulnerabilities = json.loads(result.stdout)
           # Filter out acceptable vulnerabilities
           critical_vulns = [
               v for v in vulnerabilities 
               if v["severity"] in ["high", "critical"]
           ]
           assert len(critical_vulns) == 0, f"Critical vulnerabilities found: {critical_vulns}"

Security Test Execution
-----------------------

Security tests run in isolated environments to prevent system compromise.

.. code-block:: bash

   # Run all security tests
   pytest tests/security/ -v -m security
   
   # Run specific security categories
   pytest -k "input_validation" -m security
   pytest -k "container_security" -m security
   pytest -k "authentication" -m security
   
   # Run with security reporting
   pytest tests/security/ --security-report=security_report.json

Performance Impact
------------------

Security measures impact performance; tests verify acceptable overhead.

.. code-block:: python

   @pytest.mark.security
   @pytest.mark.performance
   def test_security_performance_impact():
       """Verify security measures don't severely impact performance"""
       # Measure performance with security enabled
       with_security = benchmark_api_request(security_enabled=True)
       without_security = benchmark_api_request(security_enabled=False)
       
       # Security overhead should be < 20%
       overhead = (with_security - without_security) / without_security
       assert overhead < 0.20, f"Security overhead too high: {overhead:.2%}"

Best Practices
--------------

**Security Testing Principles:**

1. **Defense in Depth**: Test multiple security layers
2. **Fail Secure**: Verify system fails to secure state
3. **Least Privilege**: Test minimal permission enforcement
4. **Input Validation**: Validate all external inputs
5. **Regular Updates**: Keep security tests current with threats

**Common Security Test Patterns:**

.. code-block:: python

   @pytest.fixture
   def security_test_environment():
       """Isolated environment for security testing"""
       with create_isolated_environment() as env:
           env.enable_security_monitoring()
           yield env
           env.collect_security_logs()

Compliance Testing
------------------

Compliance tests verify adherence to security standards and regulations.

.. code-block:: python

   @pytest.mark.security
   @pytest.mark.compliance
   def test_gdpr_data_handling():
       """Verify GDPR compliance for data handling"""
       # Test data retention policies
       # Test data deletion capabilities
       # Test data access controls
       # Test data portability

Related Documentation
---------------------

* :doc:`unit-testing` - Unit test security considerations
* :doc:`integration-testing` - Integration security testing
* :doc:`real-vm-testing` - VM security isolation
* :doc:`troubleshooting` - Security test troubleshooting
