#!/bin/bash
# Simple script to serve TurdParty testing documentation locally

set -euo pipefail

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

DOCS_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="${DOCS_DIR}/_build/html"
PORT="${1:-8080}"

echo -e "${BLUE}🚀 TurdParty Testing Documentation Server${NC}"
echo -e "${BLUE}=========================================${NC}"

# Check if documentation is built
if [ ! -d "$BUILD_DIR" ]; then
    echo -e "${YELLOW}⚠️  Documentation not found. Building now...${NC}"
    cd "$DOCS_DIR"
    nix-shell -p python311 python311Packages.sphinx python311Packages.sphinx-rtd-theme --run "python -m sphinx -b html . _build/html"
fi

# Start server
echo -e "${GREEN}📖 Starting documentation server...${NC}"
echo -e "${GREEN}🌐 URL: http://localhost:${PORT}${NC}"
echo -e "${GREEN}📁 Serving: ${BUILD_DIR}${NC}"
echo -e "${YELLOW}💡 Press Ctrl+C to stop${NC}"
echo ""

cd "$BUILD_DIR"
python3 -m http.server "$PORT"
