Test Patterns
=============

Overview
--------

Test patterns in TurdParty provide proven solutions for common testing challenges. These patterns ensure consistent, maintainable, and effective tests across the codebase while reducing duplication and improving reliability.

**Why Test Patterns Matter:**

- **Consistency**: Standardized approaches across the testing codebase
- **Maintainability**: Easier to update and modify tests over time
- **Reliability**: Proven patterns reduce flaky and brittle tests
- **Efficiency**: Reusable patterns speed up test development

Test Pattern Architecture
-------------------------

.. mermaid::

   graph TD
       A[Test Patterns] --> B[Structural Patterns]
       A --> C[Behavioral Patterns]
       A --> D[Integration Patterns]
       
       B --> E[Arrange-Act-Assert]
       B --> F[Given-When-Then]
       B --> G[Test Fixtures]
       
       C --> H[Test Doubles]
       C --> I[Data Builders]
       C --> J[Page Objects]
       
       D --> K[Contract Testing]
       D --> L[Test Containers]
       D --> M[Test Pyramids]

Arrange-Act-Assert Pattern
--------------------------

The AAA pattern provides clear test structure and improves readability.

.. code-block:: python

   def test_file_hash_calculation():
       """Test file hash calculation using AAA pattern"""
       # Arrange - Setup test data and dependencies
       test_file_content = b"malware sample content"
       expected_hash = "a1b2c3d4e5f6..."
       file_processor = FileProcessor()
       
       # Act - Execute the behavior being tested
       actual_hash = file_processor.calculate_hash(test_file_content)
       
       # Assert - Verify the expected outcome
       assert actual_hash == expected_hash
       assert len(actual_hash) == 64  # SHA-256 length
       assert all(c in "0123456789abcdef" for c in actual_hash)

**AAA Pattern Benefits:**

- **Clear Structure**: Easy to understand test flow
- **Separation of Concerns**: Distinct setup, execution, and verification
- **Maintainability**: Changes to one section don't affect others

Given-When-Then Pattern
-----------------------

BDD-style pattern for behavior-driven test scenarios.

.. code-block:: python

   def test_malware_analysis_workflow():
       """Test malware analysis workflow using Given-When-Then"""
       # Given - Initial system state
       given_uploaded_malware_sample()
       given_analysis_engine_is_ready()
       given_vm_pool_has_available_instances()
       
       # When - Action or event occurs
       when_analysis_is_requested("sample.exe", analysis_type="dynamic")
       
       # Then - Expected outcomes
       then_analysis_should_be_queued()
       then_vm_should_be_allocated()
       then_analysis_status_should_be("pending")

**Implementation with pytest-bdd:**

.. code-block:: python

   from pytest_bdd import given, when, then, scenario
   
   @scenario("features/malware_analysis.feature", "Dynamic analysis workflow")
   def test_dynamic_analysis():
       pass
   
   @given("a malware sample is uploaded")
   def uploaded_sample(api_client):
       response = api_client.post("/upload", files={"file": get_test_malware()})
       return response.json()["file_id"]
   
   @when("dynamic analysis is requested")
   def request_analysis(api_client, uploaded_sample):
       return api_client.post(f"/analyze/{uploaded_sample}", 
                             json={"type": "dynamic"})
   
   @then("analysis should be queued")
   def verify_queued(request_analysis):
       assert request_analysis.status_code == 202
       assert request_analysis.json()["status"] == "queued"

Test Fixture Patterns
---------------------

Reusable test setup and teardown patterns.

.. mermaid::

   graph LR
       A[Test Fixtures] --> B[Session Scope]
       A --> C[Function Scope]
       A --> D[Class Scope]
       
       B --> E[Database Setup]
       C --> F[Test Data]
       D --> G[Test Class State]

**Hierarchical Fixtures:**

.. code-block:: python

   @pytest.fixture(scope="session")
   def database_engine():
       """Session-scoped database engine"""
       engine = create_test_engine()
       yield engine
       engine.dispose()
   
   @pytest.fixture(scope="function")
   def database_transaction(database_engine):
       """Function-scoped database transaction"""
       connection = database_engine.connect()
       transaction = connection.begin()
       yield connection
       transaction.rollback()
       connection.close()
   
   @pytest.fixture
   def sample_analysis(database_transaction):
       """Test analysis record"""
       analysis = Analysis(
           file_hash="abc123",
           status="completed",
           created_at=datetime.utcnow()
       )
       database_transaction.add(analysis)
       database_transaction.commit()
       return analysis

**Factory Fixtures:**

.. code-block:: python

   @pytest.fixture
   def analysis_factory(database_transaction):
       """Factory for creating test analyses"""
       created_analyses = []
       
       def create_analysis(**kwargs):
           defaults = {
               "file_hash": generate_random_hash(),
               "status": "pending",
               "analysis_type": "static"
           }
           defaults.update(kwargs)
           
           analysis = Analysis(**defaults)
           database_transaction.add(analysis)
           database_transaction.commit()
           created_analyses.append(analysis)
           return analysis
       
       yield create_analysis
       
       # Cleanup
       for analysis in created_analyses:
           database_transaction.delete(analysis)

Test Double Patterns
--------------------

Patterns for mocks, stubs, and fakes in testing.

**Mock Pattern:**

.. code-block:: python

   def test_vm_creation_with_mock():
       """Test VM creation using mock pattern"""
       with patch('services.vm.VMProvider') as mock_provider:
           # Configure mock behavior
           mock_instance = Mock()
           mock_instance.create_vm.return_value = {"vm_id": "test-vm-123"}
           mock_provider.return_value = mock_instance
           
           # Execute test
           vm_manager = VMManager()
           result = vm_manager.create_analysis_vm("windows10")
           
           # Verify mock interactions
           mock_provider.assert_called_once()
           mock_instance.create_vm.assert_called_once_with("windows10")
           assert result["vm_id"] == "test-vm-123"

**Stub Pattern:**

.. code-block:: python

   class StubVirusTotalAPI:
       """Stub for VirusTotal API testing"""
       
       def __init__(self, responses=None):
           self.responses = responses or {}
       
       def scan_file(self, file_hash):
           """Return predefined response for file hash"""
           return self.responses.get(file_hash, {
               "malicious": 0,
               "suspicious": 0,
               "clean": 1
           })
   
   def test_threat_intelligence_with_stub():
       """Test threat intelligence using stub"""
       # Setup stub with test data
       stub_api = StubVirusTotalAPI({
           "malicious_hash": {"malicious": 45, "suspicious": 5, "clean": 20},
           "clean_hash": {"malicious": 0, "suspicious": 0, "clean": 70}
       })
       
       threat_intel = ThreatIntelligence(api=stub_api)
       
       # Test malicious file detection
       result = threat_intel.check_reputation("malicious_hash")
       assert result["threat_level"] == "high"

**Fake Pattern:**

.. code-block:: python

   class FakeFileStorage:
       """Fake file storage for testing"""
       
       def __init__(self):
           self.files = {}
       
       def store_file(self, file_id, content):
           self.files[file_id] = content
           return f"fake://storage/{file_id}"
       
       def retrieve_file(self, file_id):
           if file_id not in self.files:
               raise FileNotFoundError(f"File {file_id} not found")
           return self.files[file_id]
       
       def delete_file(self, file_id):
           if file_id in self.files:
               del self.files[file_id]

Data Builder Pattern
--------------------

Pattern for creating complex test data objects.

.. code-block:: python

   class AnalysisBuilder:
       """Builder for creating test Analysis objects"""
       
       def __init__(self):
           self.reset()
       
       def reset(self):
           self._analysis = {
               "file_hash": "default_hash",
               "status": "pending",
               "analysis_type": "static",
               "created_at": datetime.utcnow(),
               "metadata": {}
           }
           return self
       
       def with_hash(self, file_hash):
           self._analysis["file_hash"] = file_hash
           return self
       
       def with_status(self, status):
           self._analysis["status"] = status
           return self
       
       def with_type(self, analysis_type):
           self._analysis["analysis_type"] = analysis_type
           return self
       
       def with_metadata(self, **metadata):
           self._analysis["metadata"].update(metadata)
           return self
       
       def completed(self):
           self._analysis["status"] = "completed"
           self._analysis["completed_at"] = datetime.utcnow()
           return self
       
       def malicious(self):
           self._analysis["metadata"]["malware_detected"] = True
           self._analysis["metadata"]["threat_level"] = "high"
           return self
       
       def build(self):
           return Analysis(**self._analysis)

**Usage Example:**

.. code-block:: python

   def test_malicious_analysis_reporting():
       """Test reporting for malicious analysis"""
       # Build complex test data using builder pattern
       analysis = (AnalysisBuilder()
                  .with_hash("malicious_sample_hash")
                  .with_type("dynamic")
                  .completed()
                  .malicious()
                  .with_metadata(
                      file_size=1024000,
                      file_type="PE",
                      detection_engines=["engine1", "engine2"]
                  )
                  .build())
       
       # Test the reporting functionality
       report = generate_analysis_report(analysis)
       assert report["threat_assessment"] == "malicious"
       assert report["confidence"] > 0.8

Page Object Pattern
-------------------

Pattern for web UI testing with Playwright/Selenium.

.. code-block:: python

   class AnalysisDashboardPage:
       """Page object for analysis dashboard"""
       
       def __init__(self, page):
           self.page = page
           
       # Locators
       @property
       def upload_button(self):
           return self.page.locator('[data-testid="upload-button"]')
       
       @property
       def analysis_table(self):
           return self.page.locator('[data-testid="analysis-table"]')
       
       @property
       def status_filter(self):
           return self.page.locator('[data-testid="status-filter"]')
       
       # Actions
       def upload_file(self, file_path):
           """Upload a file for analysis"""
           self.upload_button.click()
           self.page.set_input_files('[data-testid="file-input"]', file_path)
           self.page.click('[data-testid="submit-upload"]')
       
       def filter_by_status(self, status):
           """Filter analyses by status"""
           self.status_filter.select_option(status)
           self.page.wait_for_load_state("networkidle")
       
       def get_analysis_count(self):
           """Get number of analyses in table"""
           return self.analysis_table.locator('tbody tr').count()
       
       # Assertions
       def should_show_analysis(self, file_hash):
           """Verify analysis is displayed"""
           row = self.analysis_table.locator(f'tr:has-text("{file_hash}")')
           expect(row).to_be_visible()

**Usage in Tests:**

.. code-block:: python

   def test_file_upload_workflow(page):
       """Test file upload workflow using page object"""
       dashboard = AnalysisDashboardPage(page)
       
       # Navigate to dashboard
       page.goto("/dashboard")
       
       # Upload file
       dashboard.upload_file("tests/data/sample.exe")
       
       # Verify upload success
       expect(page.locator('.success-message')).to_contain_text("File uploaded")
       
       # Check analysis appears in table
       dashboard.should_show_analysis("sample.exe")

Contract Testing Pattern
------------------------

Pattern for testing API contracts and service interactions.

.. mermaid::

   graph LR
       A[Consumer] --> B[Contract]
       B --> C[Provider]
       
       D[Consumer Tests] --> E[Contract Verification]
       F[Provider Tests] --> E

.. code-block:: python

   class AnalysisAPIContract:
       """Contract definition for Analysis API"""
       
       @staticmethod
       def upload_file_contract():
           return {
               "request": {
                   "method": "POST",
                   "path": "/api/upload",
                   "headers": {"Content-Type": "multipart/form-data"},
                   "body": {"file": "binary_data"}
               },
               "response": {
                   "status": 201,
                   "headers": {"Content-Type": "application/json"},
                   "body": {
                       "file_id": "string",
                       "file_hash": "string",
                       "upload_time": "datetime"
                   }
               }
           }
   
   def test_upload_api_contract_compliance():
       """Test API compliance with contract"""
       contract = AnalysisAPIContract.upload_file_contract()
       
       # Test request format
       response = api_client.post(
           contract["request"]["path"],
           files={"file": b"test content"}
       )
       
       # Verify response format
       assert response.status_code == contract["response"]["status"]
       assert response.headers["Content-Type"] == contract["response"]["headers"]["Content-Type"]
       
       # Verify response schema
       data = response.json()
       assert "file_id" in data
       assert "file_hash" in data
       assert "upload_time" in data

Test Container Pattern
----------------------

Pattern for integration testing with containerized dependencies.

.. code-block:: python

   import testcontainers.postgres as postgres
   import testcontainers.redis as redis
   
   @pytest.fixture(scope="session")
   def test_containers():
       """Setup test containers for integration testing"""
       # Start PostgreSQL container
       postgres_container = postgres.PostgresContainer("postgres:15")
       postgres_container.start()
       
       # Start Redis container
       redis_container = redis.RedisContainer("redis:7")
       redis_container.start()
       
       containers = {
           "postgres": postgres_container,
           "redis": redis_container
       }
       
       yield containers
       
       # Cleanup
       for container in containers.values():
           container.stop()
   
   def test_database_integration_with_containers(test_containers):
       """Test database integration using test containers"""
       postgres_container = test_containers["postgres"]
       
       # Get connection details
       db_url = postgres_container.get_connection_url()
       
       # Test database operations
       engine = create_engine(db_url)
       with engine.connect() as conn:
           result = conn.execute(text("SELECT 1"))
           assert result.scalar() == 1

Parameterized Test Pattern
--------------------------

Pattern for testing multiple scenarios with different data.

.. code-block:: python

   @pytest.mark.parametrize("file_type,expected_result", [
       ("exe", True),
       ("dll", True),
       ("txt", False),
       ("jpg", False),
       ("pdf", True),
       ("zip", True),
   ])
   def test_malware_file_type_detection(file_type, expected_result):
       """Test malware detection for different file types"""
       detector = MalwareDetector()
       result = detector.is_suspicious_file_type(file_type)
       assert result == expected_result

**Complex Parameterization:**

.. code-block:: python

   test_scenarios = [
       pytest.param(
           {"file_size": 1024, "file_type": "exe", "entropy": 7.8},
           {"malware_detected": True, "confidence": 0.9},
           id="high-entropy-executable"
       ),
       pytest.param(
           {"file_size": 512, "file_type": "txt", "entropy": 2.1},
           {"malware_detected": False, "confidence": 0.1},
           id="low-entropy-text"
       ),
   ]
   
   @pytest.mark.parametrize("file_metadata,expected_analysis", test_scenarios)
   def test_malware_analysis_scenarios(file_metadata, expected_analysis):
       """Test various malware analysis scenarios"""
       analyzer = MalwareAnalyzer()
       result = analyzer.analyze(file_metadata)
       
       assert result["malware_detected"] == expected_analysis["malware_detected"]
       assert abs(result["confidence"] - expected_analysis["confidence"]) < 0.1

Best Practices
--------------

**Test Pattern Principles:**

1. **Single Responsibility**: Each test should verify one behavior
2. **Independence**: Tests should not depend on each other
3. **Repeatability**: Tests should produce consistent results
4. **Fast Feedback**: Optimize for quick test execution
5. **Clear Intent**: Test names and structure should be self-documenting

**Pattern Selection Guidelines:**

.. code-block:: python

   # Use AAA for simple unit tests
   def test_simple_calculation():
       # Arrange
       calculator = Calculator()
       # Act
       result = calculator.add(2, 3)
       # Assert
       assert result == 5
   
   # Use Given-When-Then for complex scenarios
   def test_complex_workflow():
       # Given
       given_system_is_ready()
       # When
       when_user_performs_action()
       # Then
       then_expected_outcome_occurs()
   
   # Use builders for complex test data
   def test_with_complex_data():
       analysis = AnalysisBuilder().malicious().completed().build()
       # Test with complex analysis object

Related Documentation
---------------------

* :doc:`unit-testing` - Unit testing fundamentals
* :doc:`integration-testing` - Integration testing patterns
* :doc:`best-practices` - Testing best practices
* :doc:`troubleshooting` - Pattern troubleshooting guide
