Test Troubleshooting
===================

Overview
--------

Test troubleshooting in TurdParty provides systematic approaches to diagnose and resolve common testing issues. This guide covers frequent problems, diagnostic techniques, and proven solutions for maintaining a healthy test suite.

**Why Troubleshooting Matters:**

- **Rapid Resolution**: Quickly identify and fix test issues to maintain development velocity
- **Knowledge Preservation**: Document solutions for recurring problems
- **Team Efficiency**: Reduce time spent on common testing problems
- **System Reliability**: Maintain confidence in test results and system quality

Troubleshooting Architecture
----------------------------

.. mermaid::

   graph TD
       A[Test Issue] --> B[Problem Classification]
       B --> C[Test Failures]
       B --> D[Performance Issues]
       B --> E[Environment Problems]
       B --> F[Configuration Issues]
       
       C --> G[Assertion Failures]
       C --> H[Exception Errors]
       C --> I[Timeout Issues]
       
       D --> J[Slow Execution]
       D --> K[Memory Problems]
       D --> L[Resource Contention]
       
       E --> M[Missing Dependencies]
       E --> N[Service Unavailable]
       E --> O[Permission Issues]
       
       F --> P[Wrong Settings]
       F --> Q[Path Problems]
       F --> R[Version Conflicts]

Common Test Failures
--------------------

**Assertion Failures:**

.. code-block:: python

   # Problem: Assertion failure with unclear error message
   def test_analysis_result():
       result = analyze_file("sample.exe")
       assert result.confidence == 0.85  # Fails with: AssertionError
   
   # Solution: Add descriptive error messages
   def test_analysis_result_improved():
       result = analyze_file("sample.exe")
       assert result.confidence == 0.85, (
           f"Expected confidence 0.85, got {result.confidence}. "
           f"Analysis details: {result.details}"
       )
   
   # Better: Use approximate comparisons for floats
   def test_analysis_result_best():
       result = analyze_file("sample.exe")
       expected_confidence = 0.85
       tolerance = 0.01
       
       assert abs(result.confidence - expected_confidence) < tolerance, (
           f"Confidence {result.confidence} not within {tolerance} of {expected_confidence}"
       )

**Exception Handling Issues:**

.. code-block:: python

   # Problem: Unexpected exceptions in tests
   def test_file_processing_problematic():
       # This might raise FileNotFoundError, PermissionError, etc.
       result = process_file("test_file.exe")
       assert result.success
   
   # Solution: Explicit exception handling and validation
   def test_file_processing_robust():
       try:
           result = process_file("test_file.exe")
           assert result.success
       except FileNotFoundError:
           pytest.fail("Test file not found - check test data setup")
       except PermissionError:
           pytest.fail("Permission denied - check file permissions")
       except Exception as e:
           pytest.fail(f"Unexpected error: {type(e).__name__}: {e}")
   
   # Better: Use proper test setup and validation
   def test_file_processing_best(tmp_path):
       # Create test file with known content
       test_file = tmp_path / "test_sample.exe"
       test_file.write_bytes(get_test_malware_bytes())
       
       # Ensure file exists and is readable
       assert test_file.exists(), "Test file creation failed"
       assert test_file.is_file(), "Test file is not a regular file"
       
       result = process_file(str(test_file))
       assert result.success

**Timeout Issues:**

.. code-block:: python

   # Problem: Tests timing out unexpectedly
   @pytest.mark.timeout(30)  # 30 second timeout
   def test_analysis_with_timeout():
       result = perform_long_analysis()  # Sometimes takes > 30 seconds
       assert result.completed
   
   # Solution: Identify and fix timeout causes
   def test_analysis_timeout_diagnosis():
       import time
       start_time = time.time()
       
       try:
           result = perform_long_analysis()
           execution_time = time.time() - start_time
           
           # Log timing information for analysis
           logger.info(f"Analysis completed in {execution_time:.2f} seconds")
           
           # Fail if taking too long (but with useful info)
           if execution_time > 25:  # Warning threshold
               logger.warning(f"Analysis took {execution_time:.2f}s - investigate performance")
           
           assert result.completed
           
       except Exception as e:
           execution_time = time.time() - start_time
           logger.error(f"Analysis failed after {execution_time:.2f}s: {e}")
           raise

Performance Issues
------------------

**Slow Test Execution:**

.. code-block:: bash

   # Diagnose slow tests
   pytest --durations=10  # Show 10 slowest tests
   pytest --durations=0   # Show all test durations
   
   # Profile specific test
   pytest tests/test_slow.py -v --tb=short --durations=0

**Memory Issues:**

.. code-block:: python

   import psutil
   import gc
   
   def test_memory_usage_monitoring():
       """Monitor memory usage during test execution"""
       process = psutil.Process()
       initial_memory = process.memory_info().rss
       
       # Perform memory-intensive operation
       large_data = perform_analysis_with_large_dataset()
       
       current_memory = process.memory_info().rss
       memory_increase = current_memory - initial_memory
       
       # Log memory usage for monitoring
       logger.info(f"Memory increase: {memory_increase / 1024 / 1024:.1f} MB")
       
       # Clean up explicitly
       del large_data
       gc.collect()
       
       final_memory = process.memory_info().rss
       memory_after_cleanup = final_memory - initial_memory
       
       # Warn if memory not released
       if memory_after_cleanup > memory_increase * 0.5:
           logger.warning("Potential memory leak detected")

**Database Performance Issues:**

.. code-block:: python

   def test_database_performance_diagnosis():
       """Diagnose database performance issues"""
       import time
       
       # Enable SQL logging for diagnosis
       logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
       
       start_time = time.time()
       
       # Perform database operations
       with database.get_connection() as conn:
           # Monitor query execution time
           query_start = time.time()
           result = conn.execute("SELECT * FROM analyses WHERE status = 'completed'")
           query_time = time.time() - query_start
           
           logger.info(f"Query executed in {query_time:.3f} seconds")
           
           if query_time > 1.0:  # Slow query threshold
               logger.warning(f"Slow query detected: {query_time:.3f}s")
               # Suggest adding indexes or optimizing query
       
       total_time = time.time() - start_time
       assert total_time < 5.0, f"Database test took too long: {total_time:.2f}s"

Environment Issues
------------------

**Missing Dependencies:**

.. code-block:: python

   def diagnose_missing_dependencies():
       """Diagnose missing or incorrect dependencies"""
       import importlib
       import sys
       
       required_modules = [
           'pytest', 'hypothesis', 'requests', 'sqlalchemy', 
           'redis', 'docker', 'psutil'
       ]
       
       missing_modules = []
       version_issues = []
       
       for module_name in required_modules:
           try:
               module = importlib.import_module(module_name)
               if hasattr(module, '__version__'):
                   logger.info(f"{module_name}: {module.__version__}")
               else:
                   logger.info(f"{module_name}: version unknown")
           except ImportError as e:
               missing_modules.append(module_name)
               logger.error(f"Missing module: {module_name} - {e}")
       
       if missing_modules:
           raise EnvironmentError(
               f"Missing required modules: {missing_modules}. "
               f"Run: pip install {' '.join(missing_modules)}"
           )

**Service Availability Issues:**

.. code-block:: python

   def test_service_availability():
       """Test and diagnose service availability issues"""
       import socket
       import requests
       
       services = {
           'database': ('localhost', 5432),
           'redis': ('localhost', 6379),
           'api': ('localhost', 8000)
       }
       
       for service_name, (host, port) in services.items():
           try:
               # Test network connectivity
               sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
               sock.settimeout(5)
               result = sock.connect_ex((host, port))
               sock.close()
               
               if result != 0:
                   logger.error(f"{service_name} not reachable at {host}:{port}")
                   continue
               
               # Test service-specific health
               if service_name == 'api':
                   response = requests.get(f"http://{host}:{port}/health", timeout=5)
                   assert response.status_code == 200
               
               logger.info(f"{service_name} is available at {host}:{port}")
               
           except Exception as e:
               logger.error(f"{service_name} health check failed: {e}")
               raise EnvironmentError(f"{service_name} is not available")

**File System Issues:**

.. code-block:: python

   def diagnose_filesystem_issues():
       """Diagnose file system related test issues"""
       import os
       import tempfile
       import shutil
       
       # Check working directory
       cwd = os.getcwd()
       logger.info(f"Current working directory: {cwd}")
       
       # Check test data directory
       test_data_dir = "tests/data"
       if not os.path.exists(test_data_dir):
           logger.error(f"Test data directory not found: {test_data_dir}")
           raise FileNotFoundError(f"Missing test data directory: {test_data_dir}")
       
       # Check permissions
       if not os.access(test_data_dir, os.R_OK):
           raise PermissionError(f"Cannot read test data directory: {test_data_dir}")
       
       # Check temporary directory
       temp_dir = tempfile.gettempdir()
       logger.info(f"Temporary directory: {temp_dir}")
       
       # Test file operations
       try:
           test_file = os.path.join(temp_dir, "test_write_permissions")
           with open(test_file, 'w') as f:
               f.write("test")
           os.remove(test_file)
           logger.info("File system write permissions OK")
       except Exception as e:
           raise PermissionError(f"Cannot write to temporary directory: {e}")

Configuration Issues
--------------------

**pytest Configuration Problems:**

.. code-block:: bash

   # Validate pytest configuration
   pytest --collect-only  # Check test discovery
   pytest --markers       # List available markers
   pytest --fixtures      # List available fixtures
   
   # Check configuration file syntax
   python -c "import configparser; configparser.ConfigParser().read('pytest.ini')"

**Environment Variable Issues:**

.. code-block:: python

   def diagnose_environment_variables():
       """Diagnose environment variable configuration issues"""
       import os
       
       required_env_vars = [
           'DATABASE_URL',
           'REDIS_URL', 
           'TEST_DATA_PATH',
           'LOG_LEVEL'
       ]
       
       missing_vars = []
       invalid_vars = []
       
       for var_name in required_env_vars:
           value = os.getenv(var_name)
           
           if value is None:
               missing_vars.append(var_name)
               continue
           
           # Validate specific environment variables
           if var_name.endswith('_URL'):
               if not (value.startswith('http://') or 
                      value.startswith('https://') or
                      value.startswith('postgresql://') or
                      value.startswith('redis://')):
                   invalid_vars.append(f"{var_name}={value}")
           
           logger.info(f"{var_name}={value}")
       
       if missing_vars:
           logger.error(f"Missing environment variables: {missing_vars}")
       
       if invalid_vars:
           logger.error(f"Invalid environment variables: {invalid_vars}")
       
       if missing_vars or invalid_vars:
           raise EnvironmentError("Environment configuration issues detected")

Flaky Test Issues
-----------------

**Identifying Flaky Tests:**

.. code-block:: python

   def detect_flaky_tests():
       """Detect and analyze flaky tests"""
       import subprocess
       import json
       
       # Run tests multiple times to detect flakiness
       results = []
       for run in range(10):
           result = subprocess.run([
               'pytest', '--json-report', '--json-report-file=test_run.json',
               'tests/', '-x'  # Stop on first failure
           ], capture_output=True)
           
           with open('test_run.json') as f:
               test_data = json.load(f)
               results.append(test_data)
       
       # Analyze results for flaky patterns
       test_outcomes = {}
       for result in results:
           for test in result.get('tests', []):
               test_name = test['nodeid']
               outcome = test['outcome']
               
               if test_name not in test_outcomes:
                   test_outcomes[test_name] = []
               test_outcomes[test_name].append(outcome)
       
       # Identify flaky tests (inconsistent outcomes)
       flaky_tests = []
       for test_name, outcomes in test_outcomes.items():
           unique_outcomes = set(outcomes)
           if len(unique_outcomes) > 1:
               pass_rate = outcomes.count('passed') / len(outcomes)
               flaky_tests.append({
                   'test': test_name,
                   'pass_rate': pass_rate,
                   'outcomes': outcomes
               })
       
       return flaky_tests

**Fixing Flaky Tests:**

.. code-block:: python

   # Common flaky test patterns and fixes
   
   # Problem: Race conditions in async code
   async def test_flaky_async_operation():
       result = await async_operation()
       # This might fail if operation isn't complete
       assert result.status == "completed"
   
   # Solution: Proper waiting and polling
   async def test_fixed_async_operation():
       result = await async_operation()
       
       # Wait for operation to complete with timeout
       timeout = 30
       start_time = time.time()
       
       while result.status != "completed":
           if time.time() - start_time > timeout:
               pytest.fail(f"Operation didn't complete within {timeout}s")
           
           await asyncio.sleep(0.1)  # Small delay
           result = await get_operation_status(result.id)
       
       assert result.status == "completed"
   
   # Problem: Timing-dependent tests
   def test_flaky_timing():
       start_time = time.time()
       perform_operation()
       duration = time.time() - start_time
       assert duration < 1.0  # Flaky - depends on system load
   
   # Solution: Focus on behavior, not timing
   def test_fixed_behavior():
       result = perform_operation()
       assert result.success  # Test behavior, not timing
       
       # If timing is important, use appropriate tolerances
       if hasattr(result, 'duration'):
           assert result.duration < 5.0  # More reasonable threshold

Debugging Tools and Techniques
------------------------------

**Advanced Debugging:**

.. code-block:: python

   def test_with_advanced_debugging():
       """Example of advanced debugging techniques"""
       import pdb
       import traceback
       import sys
       
       try:
           # Enable detailed logging
           logging.getLogger().setLevel(logging.DEBUG)
           
           # Capture system state before test
           initial_state = capture_system_state()
           
           # Run test operation with debugging
           result = perform_complex_operation()
           
           # Capture system state after test
           final_state = capture_system_state()
           
           # Compare states for debugging
           state_diff = compare_states(initial_state, final_state)
           logger.debug(f"System state changes: {state_diff}")
           
           assert result.success
           
       except Exception as e:
           # Enhanced error reporting
           logger.error(f"Test failed with {type(e).__name__}: {e}")
           logger.error(f"Full traceback: {traceback.format_exc()}")
           
           # Drop into debugger for interactive investigation
           if os.getenv('DEBUG_ON_FAILURE'):
               pdb.post_mortem()
           
           raise

**Log Analysis:**

.. code-block:: python

   def analyze_test_logs():
       """Analyze test logs for patterns and issues"""
       import re
       
       log_patterns = {
           'errors': re.compile(r'ERROR.*'),
           'warnings': re.compile(r'WARNING.*'),
           'timeouts': re.compile(r'timeout|timed out', re.IGNORECASE),
           'memory_issues': re.compile(r'memory|oom|out of memory', re.IGNORECASE),
           'connection_issues': re.compile(r'connection.*failed|refused', re.IGNORECASE)
       }
       
       issues_found = {pattern: [] for pattern in log_patterns}
       
       with open('test.log') as f:
           for line_num, line in enumerate(f, 1):
               for pattern_name, pattern in log_patterns.items():
                   if pattern.search(line):
                       issues_found[pattern_name].append((line_num, line.strip()))
       
       # Report findings
       for pattern_name, matches in issues_found.items():
           if matches:
               logger.warning(f"Found {len(matches)} {pattern_name} in logs")
               for line_num, line in matches[:5]:  # Show first 5
                   logger.warning(f"  Line {line_num}: {line}")

Quick Reference
---------------

**Common Commands:**

.. code-block:: bash

   # Debug failing tests
   pytest tests/test_file.py::test_function -v -s --tb=long
   
   # Run with debugger
   pytest tests/test_file.py::test_function --pdb
   
   # Show test durations
   pytest --durations=10
   
   # Run only failed tests
   pytest --lf
   
   # Collect tests without running
   pytest --collect-only
   
   # Show available fixtures
   pytest --fixtures
   
   # Validate configuration
   pytest --markers

**Environment Checks:**

.. code-block:: bash

   # Check Python environment
   python --version
   pip list | grep -E "(pytest|hypothesis|requests)"
   
   # Check services
   pg_isready -h localhost -p 5432
   redis-cli -h localhost -p 6379 ping
   curl -f http://localhost:8000/health
   
   # Check file permissions
   ls -la tests/data/
   touch /tmp/test_write && rm /tmp/test_write

**Quick Fixes:**

.. code-block:: bash

   # Clear pytest cache
   pytest --cache-clear
   
   # Reinstall test dependencies
   pip install -r requirements-test.txt --force-reinstall
   
   # Reset test database
   python scripts/reset_test_db.py
   
   # Clean up test artifacts
   find . -name "*.pyc" -delete
   find . -name "__pycache__" -type d -exec rm -rf {} +

Related Documentation
---------------------

* :doc:`debugging` - Detailed debugging techniques
* :doc:`best-practices` - Testing best practices
* :doc:`configuration` - Test configuration guide
* :doc:`ci-cd` - CI/CD troubleshooting
