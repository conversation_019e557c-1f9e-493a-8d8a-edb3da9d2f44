💩🎉TurdParty🎉💩 UUID Run Tracking
====================================

Overview
--------

TurdParty implements a sophisticated **multiple run tracking system** that enables comprehensive analysis of malware behavior changes between executions of the same file UUID. This system distinguishes between **install footprint** and **runtime footprint**, providing crucial insights into malware persistence, evasion techniques, and environment adaptation.

**Key Capabilities:**

- **Multiple Run Tracking**: Execute the same UUID multiple times with unique run identifiers
- **Install vs Runtime Distinction**: Separate tracking of installation and runtime behaviors
- **Footprint Comparison**: Analyze differences between runs to detect behavioral changes
- **Persistent Storage**: Maintain run history for long-term analysis
- **ECS Integration**: Enhanced Elasticsearch events with run tracking metadata

Architecture Overview
---------------------

.. mermaid::

   graph TB
       A[File UUID] --> B[RunTracker System]
       B --> C[Run 1]
       B --> D[Run 2]
       B --> E[Run N]
       
       C --> F[Install Phase]
       C --> G[Runtime Phase]
       
       D --> H[Install Phase]
       D --> I[Runtime Phase]
       
       F --> J[Install ECS Events]
       G --> K[Runtime ECS Events]
       H --> J
       I --> K
       
       J --> L[turdparty-install-ecs-*]
       K --> M[turdparty-runtime-ecs-*]
       
       B --> N[Comparison Engine]
       N --> O[Delta Analysis]
       N --> P[Behavioral Insights]
       
       style A fill:#ff9999
       style B fill:#99ccff
       style L fill:#ffcccc
       style M fill:#ccffcc

Run Identification System
-------------------------

**Sequential Run Numbers**
   Each execution of a UUID gets an incremental run number starting from 1.

**Unique Run IDs**
   Format: ``{file_id}-run-{run_number}``
   
   Examples:
   - ``malware-sample-123-run-1``
   - ``malware-sample-123-run-2``
   - ``malware-sample-123-run-3``

**Persistent Metadata**
   All run information is stored in ``/tmp/turdparty_runs.json`` with:
   
   - Run start/end times
   - Event counts (install vs runtime)
   - UUIDs for correlation
   - Workflow and VM identifiers

Install vs Runtime Footprint
----------------------------

.. mermaid::

   graph LR
       A[Malware Execution] --> B{Execution Phase}
       
       B -->|Install Phase| C[Install Footprint]
       B -->|Runtime Phase| D[Runtime Footprint]
       
       C --> E[File System Changes]
       C --> F[Registry Modifications]
       C --> G[Service Installation]
       C --> H[Persistence Setup]
       C --> I[DLL Injection]
       
       D --> J[Process Execution]
       D --> K[Network Communication]
       D --> L[API Calls]
       D --> M[Data Collection]
       D --> N[Command & Control]
       
       E --> O[Install ECS Index]
       F --> O
       G --> O
       H --> O
       I --> O
       
       J --> P[Runtime ECS Index]
       K --> P
       L --> P
       M --> P
       N --> P
       
       style C fill:#ffcccc
       style D fill:#ccffcc
       style O fill:#ff9999
       style P fill:#99ff99

**Install Footprint Events:**
- File system modifications
- Registry key creation/modification
- Service installation and configuration
- Persistence mechanism setup
- DLL injection and hooking
- Initial network beacons

**Runtime Footprint Events:**
- Process creation and execution
- Network connections and data transfer
- API calls and system interactions
- Data collection and exfiltration
- Command and control communication
- Payload execution

ECS Event Enhancement
--------------------

**Enhanced TurdParty Fields:**

.. code-block:: json

   {
     "@timestamp": "2024-01-15T10:30:00.000Z",
     "ecs": {"version": "8.11.0"},
     "event": {
       "kind": "event",
       "category": ["file", "process"],
       "type": ["installation", "change"],
       "action": "file_injection",
       "outcome": "success"
     },
     "turdparty": {
       "file_id": "malware-sample-uuid-123",
       "run_id": "malware-sample-uuid-123-run-2",
       "run_number": 2,
       "execution_type": "install",
       "footprint_type": "install",
       "phase": "install",
       "install_uuid": "install-event-uuid-456",
       "runtime_uuid": null,
       "workflow_id": "workflow-789",
       "vm_id": "vm-instance-101"
     },
     "file": {
       "name": "malware.exe",
       "path": "/tmp/malware.exe",
       "hash": {"sha256": "abc123..."}
     },
     "message": "File injected into VM (Run #2)"
   }

**Field Descriptions:**

- ``file_id``: Original UUID of the uploaded file
- ``run_id``: Unique identifier for this specific execution
- ``run_number``: Sequential number for this UUID (1, 2, 3, etc.)
- ``execution_type``: Either "install" or "runtime"
- ``footprint_type``: Distinguishes install vs runtime footprint
- ``phase``: Current execution phase
- ``install_uuid``: Unique ID for install-time events
- ``runtime_uuid``: Unique ID for runtime events

Run Comparison Engine
--------------------

.. mermaid::

   graph TD
       A[Run Comparison Request] --> B[Load Run 1 Data]
       A --> C[Load Run 2 Data]
       
       B --> D[Install Events: 5]
       B --> E[Runtime Events: 10]
       B --> F[Duration: 120s]
       B --> G[Install UUID: abc123]
       
       C --> H[Install Events: 7]
       C --> I[Runtime Events: 15]
       C --> J[Duration: 95s]
       C --> K[Install UUID: def456]
       
       D --> L[Delta Calculator]
       E --> L
       F --> L
       H --> L
       I --> L
       J --> L
       
       L --> M[Install Delta: +2]
       L --> N[Runtime Delta: +5]
       L --> O[Time Delta: -25s]
       L --> P[Behavior Analysis]
       
       style L fill:#ffff99
       style M fill:#ffcccc
       style N fill:#ccffcc
       style O fill:#ccccff
       style P fill:#99ffff

**Automatic Delta Calculation:**

.. code-block:: json

   {
     "file_id": "malware-sample-123",
     "comparison": {
       "run_1": {
         "run_number": 1,
         "install_events": 5,
         "runtime_events": 10,
         "duration": "120.5 seconds"
       },
       "run_2": {
         "run_number": 2,
         "install_events": 7,
         "runtime_events": 15,
         "duration": "95.2 seconds"
       },
       "differences": {
         "install_events_delta": 2,
         "runtime_events_delta": 5,
         "time_difference": "25.3 seconds faster",
         "behavioral_change": "increased_activity"
       }
     }
   }

Practical Use Cases
-------------------

**1. Malware Persistence Analysis**

.. mermaid::

   graph LR
       A[First Run] --> B[High Install Activity]
       A --> C[Low Runtime Activity]
       
       D[Second Run] --> E[Low Install Activity]
       D --> F[High Runtime Activity]
       
       B --> G[Registry Keys Created]
       B --> H[Services Installed]
       B --> I[Files Dropped]
       
       E --> J[Persistence Detected]
       F --> K[Full Payload Execution]
       F --> L[Network Communication]
       
       style A fill:#ffcccc
       style D fill:#ccffcc
       style G fill:#ff6666
       style J fill:#66ff66

**Analysis**: First run establishes persistence (high install events), second run leverages existing persistence (low install events, high runtime activity).

**2. Evasion Technique Detection**

.. code-block:: python

   # Example: Detecting time-delayed malware
   comparison = compare_runs("time-bomb-malware", 1, 2)
   
   if comparison['differences']['runtime_events_delta'] > 10:
       print("Possible time-delayed activation detected")
       print(f"Runtime activity increased by {comparison['differences']['runtime_events_delta']} events")

**3. Environment Adaptation Analysis**

Different VMs, same malware:
- VM 1 (Windows 10): 15 install events, 25 runtime events
- VM 2 (Windows 11): 12 install events, 30 runtime events
- Analysis: Malware adapts installation strategy based on OS version

Query Examples
-------------

**Find All Runs of a Specific UUID:**

.. code-block:: bash

   # Elasticsearch query to find all runs
   GET /turdparty-*/_search
   {
     "query": {
       "term": { "turdparty.file_id": "malware-sample-123" }
     },
     "sort": [
       { "turdparty.run_number": "asc" },
       { "@timestamp": "asc" }
     ],
     "size": 1000
   }

**Compare Install vs Runtime Events for Same Run:**

.. code-block:: bash

   # Install events for run 2
   GET /turdparty-install-ecs-*/_search
   {
     "query": {
       "bool": {
         "must": [
           {"term": {"turdparty.file_id": "malware-sample-123"}},
           {"term": {"turdparty.run_number": 2}}
         ]
       }
     }
   }

   # Runtime events for run 2
   GET /turdparty-runtime-ecs-*/_search
   {
     "query": {
       "bool": {
         "must": [
           {"term": {"turdparty.file_id": "malware-sample-123"}},
           {"term": {"turdparty.run_number": 2}}
         ]
       }
     }
   }

**Aggregate Analysis Across Runs:**

.. code-block:: bash

   # Count events by run number and type
   GET /turdparty-*/_search
   {
     "size": 0,
     "query": {
       "term": { "turdparty.file_id": "malware-sample-123" }
     },
     "aggs": {
       "by_run": {
         "terms": { "field": "turdparty.run_number" },
         "aggs": {
           "by_type": {
             "terms": { "field": "turdparty.execution_type" }
           }
         }
       }
     }
   }

Implementation Guide
-------------------

**Starting a New Run:**

.. code-block:: python

   from utils.run_tracker import start_file_run, complete_file_run

   # Start tracking a new run
   file_id = "malware-sample-uuid-123"
   workflow_id = "analysis-workflow-456"
   vm_id = "vm-instance-789"

   run_number, run_id = start_file_run(file_id, workflow_id, vm_id)
   print(f"Started run {run_number} with ID: {run_id}")

**Logging Events with Run Tracking:**

.. code-block:: python

   from services.monitoring.fibratus.monitor import TurdPartyVMMonitor

   monitor = TurdPartyVMMonitor()

   # Log install event with run tracking
   install_uuid = monitor.log_install_event(
       workflow_id=workflow_id,
       vm_id=vm_id,
       file_id=file_id,
       event_data={
           "action": "file_injection",
           "file": {"name": "malware.exe", "path": "/tmp/malware.exe"},
           "message": "Malware injected into VM"
       },
       run_number=run_number
   )

   # Log runtime event with run tracking
   runtime_uuid = monitor.log_runtime_event(
       workflow_id=workflow_id,
       vm_id=vm_id,
       file_id=file_id,
       event_data={
           "action": "process_execution",
           "process": {"name": "malware.exe", "pid": 1234},
           "message": "Malware process started"
       },
       run_number=run_number
   )

**Completing a Run:**

.. code-block:: python

   # Complete the run with event counts
   complete_file_run(
       file_id=file_id,
       run_number=run_number,
       install_events=5,
       runtime_events=12,
       install_uuid=install_uuid,
       runtime_uuids=[runtime_uuid]
   )

**Comparing Runs:**

.. code-block:: python

   from utils.run_tracker import compare_runs, get_file_runs

   # Get all runs for a file
   all_runs = get_file_runs("malware-sample-123")
   print(f"Total runs: {all_runs['total_runs']}")

   # Compare specific runs
   comparison = compare_runs("malware-sample-123", 1, 2)

   print(f"Install events delta: {comparison['differences']['install_events_delta']}")
   print(f"Runtime events delta: {comparison['differences']['runtime_events_delta']}")
   print(f"Time difference: {comparison['differences']['time_difference']}")

Best Practices
--------------

**Run Management:**

1. **Consistent File IDs**: Use the same file_id for multiple runs of the same binary
2. **Meaningful Workflow IDs**: Include analysis type or purpose in workflow identifiers
3. **VM Isolation**: Use separate VMs for each run to avoid contamination
4. **Event Correlation**: Always include run_number in event logging

**Analysis Guidelines:**

1. **Baseline Establishment**: Use first run as baseline for comparison
2. **Delta Thresholds**: Define meaningful thresholds for behavioral changes
3. **Time Considerations**: Account for timing differences in analysis
4. **Context Preservation**: Maintain VM and environment context for accurate comparison

**Performance Optimization:**

1. **Cleanup Old Runs**: Regularly clean up runs older than 30 days
2. **Index Management**: Use appropriate Elasticsearch index patterns
3. **Query Optimization**: Use specific run_id filters for targeted queries
4. **Storage Management**: Monitor /tmp/turdparty_runs.json size

Troubleshooting
---------------

**Common Issues:**

**Run Tracking Not Working:**

.. code-block:: bash

   # Check if run tracker is accessible
   python -c "from utils.run_tracker import get_run_tracker; print(get_run_tracker().get_all_files_summary())"

**Missing Run Data:**

.. code-block:: bash

   # Check persistent storage
   cat /tmp/turdparty_runs.json | jq '.runs'

**ECS Events Missing Run Fields:**

.. code-block:: bash

   # Verify ECS event structure
   curl -s "http://elasticsearch.turdparty.localhost/turdparty-*/_search?size=1" | jq '.hits.hits[0]._source.turdparty'

**Performance Issues:**

.. code-block:: bash

   # Clean up old runs
   python -c "from utils.run_tracker import get_run_tracker; print(get_run_tracker().cleanup_old_runs(7))"

Related Documentation
--------------------

* :doc:`integration-testing` - Integration testing with run tracking
* :doc:`real-vm-testing` - VM testing with multiple runs
* :doc:`elasticsearch-integration` - ECS event structure and indexing
* :doc:`performance-testing` - Performance impact of run tracking
