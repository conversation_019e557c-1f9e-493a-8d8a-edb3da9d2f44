# API Testing Strategy

This document outlines our approach for testing the API endpoints of the TurdParty application. We use Playwright for API testing due to its powerful request capabilities and integration with our existing test infrastructure.

## Test Coverage Goals

1. **Core Functionality**: Ensure all API endpoints respond with expected status codes and payloads
2. **Authentication**: Verify auth endpoints work properly and protected endpoints require authentication
3. **Error Handling**: Test edge cases and error responses
4. **Integration**: Confirm that endpoints work together for key workflows

## Test Implementation

We have implemented a multi-layer testing approach:

1. **Simple Health Check Tests** (`api-health.spec.js`):
   - Basic connectivity tests
   - Health endpoint verification

2. **API Connectivity Tests** (`api-connectivity.spec.js`):
   - Tests basic connectivity to critical endpoints
   - Detects network or configuration issues between test and API containers

3. **Comprehensive Endpoint Tests** (`api-endpoints.spec.js`):
   - Tests all major API endpoints
   - Verifies authentication, status codes, and basic response structures
   - Handles both success and error cases

## API Server for Testing

We've created a simplified API test server that:

1. Mimics the behavior of the main API
2. Includes endpoints for all major functionality
3. Requires proper authentication
4. Returns realistic responses with appropriate status codes

This server runs in its own Docker container connected to the test network, making it accessible to the Playwright test container.

## Running Tests

Tests can be run using our test runner script:

```bash
tests/playwright/run-api-tests.sh [options]
```

Options:
- `all`: Run all API tests
- `html`: Generate HTML test report
- `<filename.spec.js>`: Run a specific test file

## Test Environment

Our tests can run in:

1. **Docker containers**: Using a dedicated API test container and Playwright test container
2. **Local environment**: For development and debugging
3. **CI/CD pipeline**: For automated testing in the pipeline

## Authentication

All tests handle authentication appropriately:

1. Public endpoints are tested without authentication
2. Protected endpoints are tested both with and without valid authentication
3. Tests include proper handling of token acquisition and management

## Expected Results

Our tests verify that:

1. All endpoints return appropriate HTTP status codes (200, 201, 400, 401, etc.)
2. Response payloads match expected structures
3. Authentication works as expected
4. Core application workflows function correctly (file upload, VM creation, etc.)

## Next Steps

1. **Expand test coverage**:
   - Add more detailed tests for specific endpoints
   - Add tests for pagination and filtering
   - Test batch operations and complex queries

2. **Performance testing**:
   - Add response time measurements
   - Implement load testing for critical endpoints

3. **Security testing**:
   - Test authorization boundaries
   - Test for common API vulnerabilities
   - Verify proper input validation 