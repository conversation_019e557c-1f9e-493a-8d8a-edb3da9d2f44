# API Integration Testing

This directory contains tests for verifying API functionality in our application. These tests ensure that all API endpoints are accessible and behaving correctly.

## API Test Structure

- `api-health.spec.js` - Basic API health check test
- `api-endpoints.spec.js` - Comprehensive test of all API endpoints
- `api-with-retry.spec.js` - Example of API testing with explicit retry limits

## Retry Limitations

To prevent excessive retries in our tests, we've implemented two mechanisms:

1. **Configuration-level retry limits** - The Playwright configuration in `playwright.config.js` sets a reasonable number of retries (1-2) for all tests
2. **Utility-based retry limits** - The `retry-helper.js` utility enforces a max of 5 retries for any API request

This ensures that tests will attempt to recover from transient issues but won't waste resources with too many retries.

## Using the Retry Helper

For endpoints that might need retry logic, use the `retry-helper.js` utility:

```javascript
const { createRetryableRequest, MAX_RETRIES } = require('./utils/retry-helper');

test('API endpoint with retry', async ({ request }) => {
  // Create a retryable request client
  const retryableRequest = createRetryableRequest(request);
  
  // Make a request with retry limit
  const response = await retryableRequest.get('/api/endpoint', {
    retries: MAX_RETRIES 
  });
  
  expect(response.ok()).toBeTruthy();
});
```

## Running API Tests

Use the provided script to run API tests:

```bash
# Run all API tests
./tests/playwright/run-api-tests.sh all

# Run a specific test file
./tests/playwright/run-api-tests.sh api-health.spec.js

# Generate HTML report
./tests/playwright/run-api-tests.sh all html
```

## API Test Environment

The API tests run against a containerized test API server, which provides consistent test data. This environment is automatically set up when running the tests.

- Test API server listens on port 8000 inside the container (mapped to 3055 on the host)
- The test server includes sample data for users, files, and VMs
- Auth endpoints use simplistic token-based authentication for testing

## Debugging API Tests

For debugging API tests, view the detailed logs by running:

```bash
# With detailed output
DEBUG=pw:api ./tests/playwright/run-api-tests.sh api-health.spec.js
```

Or use the HTML report for a visual representation of test results:

```bash
./tests/playwright/run-api-tests.sh all html
``` 