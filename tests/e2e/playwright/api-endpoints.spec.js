// @ts-check
const { test, expect } = require('@playwright/test');
const { testEnv, logEnvironment } = require('./config/test-environment');
const fs = require('fs');
const path = require('path');

// API URL configuration - try container hostname first
const possibleApiUrls = [
  'http://api-test:8000',      // Docker container name (preferred)
  'http://localhost:3055',     // Host port mapping
  'http://localhost:8000',     // Direct port
  process.env.API_URL,         // Environment variable if set
].filter(Boolean);

let apiUrl = possibleApiUrls[0]; // Default to first option
const apiPrefix = '/api/v1';

// Endpoint paths - centralized for easier maintenance
const ENDPOINTS = {
  // Health and version
  HEALTH: `${apiPrefix}/health/`,
  VERSION: `${apiPrefix}/version/`,
  
  // Auth
  LOGIN: `${apiPrefix}/auth/login/`,
  LOGOUT: `${apiPrefix}/auth/logout/`,
  USER_ME: `${apiPrefix}/users/me/`,
  
  // Files
  FILES: `${apiPrefix}/files/`,
  FILE_UPLOAD: `${apiPrefix}/files/upload/`,
  
  // VMs
  VMS: `${apiPrefix}/vms/`,
  VAGRANT_VMS: `${apiPrefix}/vagrant/vms/`,
  VM_OPERATIONS: `${apiPrefix}/vagrant/vms/operations/`,
  
  // Integration
  FILE_TO_VM: `${apiPrefix}/file-to-vm/`,
  
  // System
  SYSTEM_INFO: `${apiPrefix}/system/info/`,
  STORAGE_STATUS: `${apiPrefix}/storage/status/`,
  
  // MinIO
  MINIO_HEALTH: `${apiPrefix}/minio/health/`,
  MINIO_STATUS: `${apiPrefix}/minio/status/`,
  
  // Additional services
  STATIC_ANALYSIS: `${apiPrefix}/static-analysis/`,
  DOCKER_STATUS: `${apiPrefix}/docker/status/`,
  
  // Documentation
  DOCS: `${apiPrefix}/docs/`,
};

// Test credentials
const TEST_USER = {
  username: 'testuser',
  password: 'password123'
};

// Store auth token for authenticated requests
let authToken = '';

// Find the first working API URL before running tests
async function findWorkingApiUrl(request) {
  // Try each URL until we find one that works
  for (const url of possibleApiUrls) {
    try {
      console.log(`Testing API URL: ${url}${ENDPOINTS.HEALTH}`);
      const response = await request.get(`${url}${ENDPOINTS.HEALTH}`, {
        timeout: 5000,
        maxRedirects: 3
      });
      
      if (response.ok()) {
        console.log(`Found working API URL: ${url}`);
        return url;
      }
    } catch (error) {
      console.log(`Failed to connect to ${url}: ${error.message}`);
    }
  }
  
  console.log(`No working API URL found, defaulting to ${possibleApiUrls[0]}`);
  return possibleApiUrls[0];
}

test.describe('API Endpoints Availability', () => {
  
  test.beforeAll(async ({ request }) => {
    // Find a working API URL first
    apiUrl = await findWorkingApiUrl(request);
    console.log(`Using API URL: ${apiUrl}`);
    
    // Authenticate and get token for protected endpoints
    const response = await request.post(`${apiUrl}${ENDPOINTS.LOGIN}`, {
      form: {
        username: TEST_USER.username,
        password: TEST_USER.password
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data.access_token).toBeDefined();
    authToken = data.access_token;
    console.log('Authentication successful');
  });
  
  test('Health endpoint should return OK', async ({ request }) => {
    const response = await request.get(`${apiUrl}${ENDPOINTS.HEALTH}`);
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data.status).toBe('ok');
  });
  
  test('Version endpoint should return version info', async ({ request }) => {
    const response = await request.get(`${apiUrl}${ENDPOINTS.VERSION}`);
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data.version).toBeDefined();
  });
  
  test('User info endpoint should return authenticated user data', async ({ request }) => {
    const response = await request.get(`${apiUrl}${ENDPOINTS.USER_ME}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data.username).toBe(TEST_USER.username);
  });
  
  test('Files endpoint should list user files', async ({ request }) => {
    const response = await request.get(`${apiUrl}${ENDPOINTS.FILES}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(Array.isArray(data)).toBeTruthy();
  });
  
  test('VM endpoint should list VMs', async ({ request }) => {
    const response = await request.get(`${apiUrl}${ENDPOINTS.VMS}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(Array.isArray(data)).toBeTruthy();
  });
  
  test('VM operations endpoint should list available operations', async ({ request }) => {
    const response = await request.get(`${apiUrl}${ENDPOINTS.VM_OPERATIONS}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data.operations).toBeDefined();
    expect(Array.isArray(data.operations)).toBeTruthy();
  });
  
  test('System info endpoint should return system information', async ({ request }) => {
    const response = await request.get(`${apiUrl}${ENDPOINTS.SYSTEM_INFO}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data.version).toBeDefined();
    expect(data.hostname).toBeDefined();
  });
  
  test('MinIO health endpoint should return status', async ({ request }) => {
    const response = await request.get(`${apiUrl}${ENDPOINTS.MINIO_HEALTH}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data.status).toBeDefined();
  });
  
  test('Docker status endpoint should return container information', async ({ request }) => {
    const response = await request.get(`${apiUrl}${ENDPOINTS.DOCKER_STATUS}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data.status).toBeDefined();
  });
  
  test('API documentation endpoint should return OpenAPI schema', async ({ request }) => {
    const response = await request.get(`${apiUrl}${ENDPOINTS.DOCS}`);
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data.openapi).toBeDefined();
  });
  
  test('File upload endpoint should accept file uploads', async ({ request }) => {
    // Create a simple test file for upload
    const formData = new FormData();
    const testBlob = new Blob(['test file content'], { type: 'text/plain' });
    formData.append('file', testBlob, 'test.txt');
    
    const response = await request.post(`${apiUrl}${ENDPOINTS.FILE_UPLOAD}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      },
      multipart: {
        file: {
          name: 'test.txt',
          mimeType: 'text/plain',
          buffer: Buffer.from('test file content')
        }
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data.file_id).toBeDefined();
    expect(data.status).toBe('uploaded');
  });
  
  test('File-to-VM endpoint should handle file attachment to VM', async ({ request }) => {
    // First get a file ID from the files list
    const filesResponse = await request.get(`${apiUrl}${ENDPOINTS.FILES}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(filesResponse.ok()).toBeTruthy();
    const files = await filesResponse.json();
    expect(files.length).toBeGreaterThan(0);
    
    // Get a VM name from the VMs list
    const vmsResponse = await request.get(`${apiUrl}${ENDPOINTS.VMS}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(vmsResponse.ok()).toBeTruthy();
    const vms = await vmsResponse.json();
    expect(vms.length).toBeGreaterThan(0);
    
    // Now try to attach the file to the VM
    const response = await request.post(`${apiUrl}${ENDPOINTS.FILE_TO_VM}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      data: {
        file_id: files[0].id,
        vm_name: vms[0].name
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data.status).toBe('success');
  });
  
  test('Logout endpoint should invalidate the auth token', async ({ request }) => {
    const response = await request.post(`${apiUrl}${ENDPOINTS.LOGOUT}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data.detail).toBe('Successfully logged out');
    
    // Verify token is no longer valid
    const userResponse = await request.get(`${apiUrl}${ENDPOINTS.USER_ME}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(userResponse.status()).toBe(401);
  });
}); 