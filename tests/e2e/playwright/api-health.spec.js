// @ts-check
const { test, expect } = require('@playwright/test');

test('API health endpoint should return OK', async ({ request }) => {
  // Define possible API URLs in priority order
  const possibleUrls = [
    'http://api-test:8000',      // Docker container name (preferred)
    'http://localhost:3055',     // Host port mapping
    'http://localhost:8000',     // Direct port
    process.env.API_URL,         // Environment variable if set
  ].filter(Boolean); // Remove undefined/null entries
  
  // Log what we're going to try
  console.log(`Will try API URLs in this order: ${possibleUrls.join(', ')}`);
  
  let lastError = null;
  
  // Try each URL until one works
  for (const apiUrl of possibleUrls) {
    try {
      console.log(`Testing API health at ${apiUrl}`);
      const response = await request.get(`${apiUrl}/api/v1/health/`, {
        timeout: 5000,       // 5 second timeout per attempt
        maxRedirects: 3      // Allow up to 3 redirects
      });
      
      console.log(`Response status from ${apiUrl}: ${response.status()}`);
      
      // If we get here, the request succeeded
      expect(response.ok()).toBeTruthy();
      
      // Check the response body
      const responseBody = await response.json();
      expect(responseBody.status).toBe('ok');
      
      console.log(`API health check successful using ${apiUrl}`);
      return; // Exit after first successful call
    } catch (error) {
      console.log(`Failed to reach ${apiUrl}: ${error.message}`);
      lastError = error;
      // Continue to the next URL
    }
  }
  
  // If we get here, all attempts failed
  throw new Error(`Failed to connect to API health endpoint: ${lastError?.message || 'Unknown error'}`);
}); 