// @ts-check
const { test, expect } = require('@playwright/test');
const { testEnv, apiUrl, logEnvironment } = require('../config/test-environment');

test.describe('API Connectivity Tests', () => {
  test.beforeAll(() => {
    // Log the environment configuration
    logEnvironment();
  });

  test('should connect to API health endpoint', async ({ request }) => {
    // API health endpoint test
    const healthUrl = `${testEnv.api.url}${testEnv.api.endpoints.health}`;
    console.log(`Testing API health endpoint at: ${healthUrl}`);
    
    // Attempt connection to API health endpoint
    const response = await request.get(healthUrl, {
      // Ensure we follow redirects
      maxRedirects: 5
    });
    
    // Log response details
    console.log(`API Response status: ${response.status()}`);
    if (response.ok()) {
      const body = await response.text();
      console.log('API Health response:', body);
    }
    
    // Assert that the API health endpoint returns 200 OK
    expect(response.status()).toBe(200);
  });

  test('should verify API version endpoint', async ({ request }) => {
    // Test the version endpoint (if it exists)
    const versionUrl = `${testEnv.api.url}${testEnv.api.prefix}/version/`;
    console.log(`Testing API version endpoint at: ${versionUrl}`);
    
    // Try with max redirects
    const response = await request.get(versionUrl, {
      maxRedirects: 5
    });
    
    console.log(`API Version Response status: ${response.status()}`);
    
    // 404 is acceptable as the version endpoint might not exist
    expect(response.status()).toBeLessThan(500);
  });

  // Test a generic API endpoint with authentication
  test('should check VM endpoint availability', async ({ request }) => {
    // Just check if the endpoint exists (or returns proper error)
    const endpoint = testEnv.api.endpoints.vms;
    console.log(`Testing endpoint existence: ${testEnv.api.url}${endpoint}`);
    
    // Make request without authentication, just checking if endpoint exists
    const response = await request.get(`${testEnv.api.url}${endpoint}`, {
      maxRedirects: 5
    });
    
    console.log(`Endpoint response status: ${response.status()}`);
    
    // 401 (Unauthorized) means endpoint exists but requires auth
    // 404 (Not Found) is also acceptable - API might not have VM endpoint in test environment
    // 200 means endpoint exists and doesn't require auth
    expect([200, 401, 403, 404]).toContain(response.status());
  });
}); 