// @ts-check
const { test, expect } = require('@playwright/test');
const { testEnv, apiUrl, logEnvironment } = require('../config/test-environment');
const fs = require('fs');
const path = require('path');

// Test auth token for authenticated requests
let authToken = null;

// Helper function to get auth headers
const getAuthHeaders = () => {
  return {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
};

test.describe('API Endpoints Functionality', () => {
  test.beforeAll(async ({ request }) => {
    // Log environment configuration
    logEnvironment();
    
    // Get auth token for authenticated requests
    try {
      const loginUrl = `${testEnv.api.url}${testEnv.api.endpoints.login}`;
      const loginResponse = await request.post(loginUrl, {
        data: {
          username: testEnv.auth.username,
          password: testEnv.auth.password
        }
      });
      
      if (loginResponse.ok()) {
        const loginData = await loginResponse.json();
        authToken = loginData.access_token;
        console.log('Authentication successful');
      } else {
        console.log(`Auth failed with status ${loginResponse.status()}`);
        // Proceed with tests, they'll handle auth failures appropriately
      }
    } catch (error) {
      console.log('Error during authentication:', error.message);
    }
  });

  // ---- Core Health and Status Endpoints ----
  
  test('Health endpoint should return OK status', async ({ request }) => {
    const healthUrl = `${testEnv.api.url}${testEnv.api.endpoints.health}`;
    console.log(`Testing health endpoint: ${healthUrl}`);
    
    const response = await request.get(healthUrl, {
      maxRedirects: 5
    });
    
    console.log(`Health response status: ${response.status()}`);
    expect(response.ok()).toBeTruthy();
    
    // Verify response structure
    const data = await response.json();
    expect(data).toHaveProperty('status');
  });

  test('API version endpoint should be available', async ({ request }) => {
    const versionUrl = `${testEnv.api.url}${testEnv.api.prefix}/version/`;
    console.log(`Testing version endpoint: ${versionUrl}`);
    
    const response = await request.get(versionUrl, {
      maxRedirects: 5
    });
    
    console.log(`Version response status: ${response.status()}`);
    // Status below 500 is acceptable (200 OK or 404 Not Found)
    expect(response.status()).toBeLessThan(500);
  });

  // ---- Authentication Endpoints ----
  
  test('Auth login endpoint should accept valid credentials', async ({ request }) => {
    const loginUrl = `${testEnv.api.url}${testEnv.api.endpoints.login}`;
    console.log(`Testing login endpoint: ${loginUrl}`);
    
    const response = await request.post(loginUrl, {
      data: {
        username: testEnv.auth.username,
        password: testEnv.auth.password
      }
    });
    
    console.log(`Login response status: ${response.status()}`);
    
    // Check for success (200) or meaningful error (401 for invalid creds)
    expect([200, 401]).toContain(response.status());
    
    if (response.ok()) {
      const data = await response.json();
      expect(data).toHaveProperty('access_token');
    }
  });
  
  test('Auth login should reject invalid credentials', async ({ request }) => {
    const loginUrl = `${testEnv.api.url}${testEnv.api.endpoints.login}`;
    console.log(`Testing login with invalid credentials: ${loginUrl}`);
    
    const response = await request.post(loginUrl, {
      data: {
        username: 'invaliduser',
        password: 'invalidpassword'
      }
    });
    
    console.log(`Invalid login response status: ${response.status()}`);
    expect(response.status()).toBe(401);
  });

  // ---- User Management Endpoints ----
  
  test('User information endpoint should be available', async ({ request }) => {
    const userUrl = `${testEnv.api.url}${testEnv.api.prefix}/users/me/`;
    console.log(`Testing user info endpoint: ${userUrl}`);
    
    const response = await request.get(userUrl, {
      headers: getAuthHeaders()
    });
    
    console.log(`User info response status: ${response.status()}`);
    
    // Should return 200 if authenticated, 401 if not
    expect([200, 401]).toContain(response.status());
    
    if (response.ok()) {
      const data = await response.json();
      expect(data).toHaveProperty('id');
      expect(data).toHaveProperty('email');
    }
  });

  // ---- File Management Endpoints ----
  
  test('File list endpoint should be available', async ({ request }) => {
    const filesUrl = `${testEnv.api.url}${testEnv.api.endpoints.files}`;
    console.log(`Testing file list endpoint: ${filesUrl}`);
    
    const response = await request.get(filesUrl, {
      headers: getAuthHeaders()
    });
    
    console.log(`File list response status: ${response.status()}`);
    
    // Should return 200 if authenticated, 401 if not
    expect([200, 401, 404]).toContain(response.status());
    
    if (response.ok()) {
      const data = await response.json();
      expect(Array.isArray(data) || data.hasOwnProperty('items')).toBeTruthy();
    }
  });

  test('File upload endpoint should be available', async ({ request }) => {
    const uploadUrl = `${testEnv.api.url}${testEnv.api.prefix}/files/upload/`;
    console.log(`Testing file upload endpoint: ${uploadUrl}`);
    
    // Create a temporary file for testing
    const tempFilePath = path.join(__dirname, 'temp-test-file.txt');
    try {
      fs.writeFileSync(tempFilePath, 'Test file content');
      
      const response = await request.post(uploadUrl, {
        headers: {
          'Authorization': authToken ? `Bearer ${authToken}` : '',
        },
        multipart: {
          file: {
            name: 'test.txt',
            mimeType: 'text/plain',
            buffer: fs.readFileSync(tempFilePath)
          }
        }
      });
      
      console.log(`File upload response status: ${response.status()}`);
      
      // Check if endpoint exists and responds appropriately
      expect([200, 201, 400, 401, 404]).toContain(response.status());
    } catch (error) {
      console.log('Error during file upload test:', error.message);
    } finally {
      // Clean up temporary file
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
    }
  });

  // ---- VM Management Endpoints ----
  
  test('VM list endpoint should be available', async ({ request }) => {
    const vmUrl = `${testEnv.api.url}${testEnv.api.endpoints.vms}`;
    console.log(`Testing VM list endpoint: ${vmUrl}`);
    
    const response = await request.get(vmUrl, {
      headers: getAuthHeaders()
    });
    
    console.log(`VM list response status: ${response.status()}`);
    
    // Should return 200 if authenticated, 401 if not
    expect([200, 401, 404]).toContain(response.status());
    
    if (response.ok()) {
      const data = await response.json();
      expect(Array.isArray(data) || data.hasOwnProperty('items')).toBeTruthy();
    }
  });

  // ---- Vagrant-specific Endpoints ----
  
  test('Vagrant VM creation endpoint should be available', async ({ request }) => {
    const vagrantUrl = `${testEnv.api.url}${testEnv.api.prefix}/vagrant/vms/`;
    console.log(`Testing Vagrant VM creation endpoint: ${vagrantUrl}`);
    
    const response = await request.post(vagrantUrl, {
      headers: getAuthHeaders(),
      data: {
        name: 'test-vm',
        provider: 'virtualbox',
        box: 'ubuntu/focal64'
      }
    });
    
    console.log(`Vagrant VM creation response status: ${response.status()}`);
    
    // Check for proper status codes - 201 Created, 400 Bad Request, 401 Unauthorized
    expect([201, 400, 401, 404]).toContain(response.status());
  });

  test('Vagrant VM operation endpoints should be available', async ({ request }) => {
    const vagrantOpUrl = `${testEnv.api.url}${testEnv.api.prefix}/vagrant/vms/operations/`;
    console.log(`Testing Vagrant VM operations endpoint: ${vagrantOpUrl}`);
    
    const response = await request.get(vagrantOpUrl, {
      headers: getAuthHeaders()
    });
    
    console.log(`Vagrant VM operations response status: ${response.status()}`);
    
    // Check for proper status codes
    expect([200, 401, 404]).toContain(response.status());
  });

  // ---- File-to-VM Integration Endpoints ----
  
  test('File-to-VM integration endpoint should be available', async ({ request }) => {
    const fileToVmUrl = `${testEnv.api.url}${testEnv.api.prefix}/file-to-vm/`;
    console.log(`Testing file-to-VM endpoint: ${fileToVmUrl}`);
    
    const response = await request.post(fileToVmUrl, {
      headers: getAuthHeaders(),
      data: {
        file_id: 'test-file-id',
        vm_name: 'test-vm'
      }
    });
    
    console.log(`File-to-VM response status: ${response.status()}`);
    
    // Check for proper status codes
    expect([200, 201, 400, 401, 404]).toContain(response.status());
  });

  // ---- System Information Endpoints ----
  
  test('System info endpoint should be available', async ({ request }) => {
    const sysInfoUrl = `${testEnv.api.url}${testEnv.api.prefix}/system/info/`;
    console.log(`Testing system info endpoint: ${sysInfoUrl}`);
    
    const response = await request.get(sysInfoUrl, {
      headers: getAuthHeaders()
    });
    
    console.log(`System info response status: ${response.status()}`);
    
    // Should return 200 if authenticated, 401 if not
    expect([200, 401, 404]).toContain(response.status());
  });

  // ---- Storage Endpoints ----
  
  test('Storage status endpoint should be available', async ({ request }) => {
    const storageUrl = `${testEnv.api.url}${testEnv.api.prefix}/storage/status/`;
    console.log(`Testing storage status endpoint: ${storageUrl}`);
    
    const response = await request.get(storageUrl, {
      headers: getAuthHeaders()
    });
    
    console.log(`Storage status response status: ${response.status()}`);
    
    // Should return 200 if authenticated, 401 if not
    expect([200, 401, 404]).toContain(response.status());
  });

  // ---- MinIO Endpoints ----
  
  test('MinIO health endpoint should be available', async ({ request }) => {
    const minioHealthUrl = `${testEnv.api.url}${testEnv.api.prefix}/minio/health/`;
    console.log(`Testing MinIO health endpoint: ${minioHealthUrl}`);
    
    const response = await request.get(minioHealthUrl, {
      headers: getAuthHeaders()
    });
    
    console.log(`MinIO health response status: ${response.status()}`);
    
    // Should return 200 if authenticated, 401 if not
    expect([200, 401, 404]).toContain(response.status());
  });
  
  test('MinIO status endpoint should be available', async ({ request }) => {
    const minioStatusUrl = `${testEnv.api.url}${testEnv.api.prefix}/minio/status/`;
    console.log(`Testing MinIO status endpoint: ${minioStatusUrl}`);
    
    const response = await request.get(minioStatusUrl, {
      headers: getAuthHeaders()
    });
    
    console.log(`MinIO status response status: ${response.status()}`);
    
    // Should return 200 if authenticated, 401 if not
    expect([200, 401, 404]).toContain(response.status());
  });

  // ---- Static Analysis Endpoints ----
  
  test('Static analysis endpoint should be available', async ({ request }) => {
    const staticAnalysisUrl = `${testEnv.api.url}${testEnv.api.prefix}/static-analysis/`;
    console.log(`Testing static analysis endpoint: ${staticAnalysisUrl}`);
    
    const response = await request.get(staticAnalysisUrl, {
      headers: getAuthHeaders()
    });
    
    console.log(`Static analysis response status: ${response.status()}`);
    
    // Should return 200 if authenticated, 401 if not
    expect([200, 401, 404]).toContain(response.status());
  });

  // ---- Docker Endpoints ----
  
  test('Docker status endpoint should be available', async ({ request }) => {
    const dockerStatusUrl = `${testEnv.api.url}${testEnv.api.prefix}/docker/status/`;
    console.log(`Testing Docker status endpoint: ${dockerStatusUrl}`);
    
    const response = await request.get(dockerStatusUrl, {
      headers: getAuthHeaders()
    });
    
    console.log(`Docker status response status: ${response.status()}`);
    
    // Should return 200 if authenticated, 401 if not
    expect([200, 401, 404]).toContain(response.status());
  });

  // ---- Documentation Endpoints ----
  
  test('API documentation endpoint should be available', async ({ request }) => {
    const docsUrl = `${testEnv.api.url}${testEnv.api.prefix}/docs/`;
    console.log(`Testing API docs endpoint: ${docsUrl}`);
    
    const response = await request.get(docsUrl);
    
    console.log(`API docs response status: ${response.status()}`);
    
    // Documentation should be publicly accessible
    expect([200, 404]).toContain(response.status());
  });
}); 