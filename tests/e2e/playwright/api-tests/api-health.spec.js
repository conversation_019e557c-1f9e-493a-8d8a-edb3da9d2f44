// @ts-check
const { test, expect } = require('@playwright/test');

test('API health endpoint should return OK', async ({ request }) => {
  // In Docker, we need to use the container name as the hostname
  // If API_URL is set, use that, otherwise default to the Docker network address
  const apiUrl = process.env.API_URL || 'http://api-test:8000';
  
  console.log(`Testing API at ${apiUrl}`);
  
  const response = await request.get(`${apiUrl}/api/v1/health/`);
  expect(response.ok()).toBeTruthy();
  
  const responseBody = await response.json();
  expect(responseBody.status).toBe('ok');
}); 