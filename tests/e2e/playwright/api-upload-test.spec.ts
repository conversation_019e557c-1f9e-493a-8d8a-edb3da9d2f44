import { test, expect } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

// This test focuses on API-only file uploads without UI interaction
test.describe('API-only File Upload Tests', () => {
  // Define test file details
  const testFileName = `api-test-${uuidv4().substring(0, 8)}.txt`;
  const testFilePath = path.join(__dirname, `../../${testFileName}`);
  const testFileContent = `Test file content for API upload test
Created at: ${new Date().toISOString()}
UUID: ${uuidv4()}
Data: ${Array.from({ length: 10 }, () => Math.random().toString(36).substring(2)).join(' ')}`;
  
  // Set up the test file before all tests
  test.beforeAll(async () => {
    // Create test file
    fs.writeFileSync(testFilePath, testFileContent);
    console.log(`Created test file at ${testFilePath} (${Buffer.from(testFileContent).length} bytes)`);
  });
  
  // Clean up the test file after all tests
  test.afterAll(async () => {
    // Remove test file
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
      console.log(`Removed test file at ${testFilePath}`);
    }
  });
  
  // Helper function to get authentication token
  async function getAuthToken(page) {
    // Navigate to a page in the app first to ensure localStorage is available
    await page.goto('/');
    
    // Get the auth token from localStorage
    const token = await page.evaluate(() => {
      return localStorage.getItem('authToken') || '';
    });
    
    if (!token) {
      console.log('No authentication token found in localStorage, checking for test token API');
      // Try to get a test token if we're in test mode
      try {
        await page.goto('/api/v1/auth/test-token');
        const responseText = await page.textContent('body');
        try {
          const response = JSON.parse(responseText);
          return response.token || '';
        } catch (e) {
          console.log('Failed to parse test token response');
          return '';
        }
      } catch (e) {
        console.log('Failed to get test token');
        return '';
      }
    }
    
    return token;
  }
  
  test('should upload a file via direct API call', async ({ page, request }) => {
    // Get authentication token
    const token = await getAuthToken(page);
    expect(token, 'Authentication token should be available').toBeTruthy();
    
    console.log(`Using authentication token: ${token.substring(0, 15)}...`);
    
    // Prepare form data for the API request
    const formData = new FormData();
    
    // Read the file content
    const fileContent = fs.readFileSync(testFilePath);
    const fileBlob = new Blob([fileContent], { type: 'text/plain' });
    
    // Add file to form data
    formData.append('file', fileBlob, testFileName);
    
    // Add description
    const description = `API upload test - ${new Date().toISOString()}`;
    formData.append('description', description);
    
    // Make the API request
    const response = await request.post('/api/v1/file_upload/', {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      multipart: {
        file: {
          name: testFileName,
          mimeType: 'text/plain',
          buffer: fileContent
        },
        description: description
      }
    });
    
    // Verify successful response
    expect(response.ok()).toBeTruthy();
    
    // Log response details
    const responseData = await response.json();
    console.log('API Response:', responseData);
    
    // Verify file was uploaded successfully
    expect(responseData).toHaveProperty('id');
    expect(responseData).toHaveProperty('filename', testFileName);
    expect(responseData).toHaveProperty('file_size');
    expect(responseData).toHaveProperty('download_url');
    
    // Get the file ID for verification
    const fileId = responseData.id;
    console.log(`Uploaded file ID: ${fileId}`);
    
    // Verify the file can be accessed
    // Navigate to files list page
    await page.goto('/files');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of file list
    await page.screenshot({ path: 'test_screenshots/api-upload-file-list.png' });
    
    // Find the uploaded file by ID or filename
    const fileVisible = await page.locator(`.ant-table-row[data-row-key="${fileId}"], tr:has-text("${testFileName}")`).isVisible();
    expect(fileVisible).toBeTruthy();
    
    console.log('File upload via API completed successfully');
  });
  
  test('should fail to upload file with invalid token', async ({ page, request }) => {
    // Use an invalid token
    const invalidToken = 'invalid-token-12345';
    
    // Read the file content
    const fileContent = fs.readFileSync(testFilePath);
    
    // Make the API request with invalid token
    const response = await request.post('/api/v1/file_upload/', {
      headers: {
        'Authorization': `Bearer ${invalidToken}`
      },
      multipart: {
        file: {
          name: testFileName,
          mimeType: 'text/plain',
          buffer: fileContent
        },
        description: 'API upload test with invalid token'
      }
    });
    
    // Verify unauthorized response
    expect(response.status()).toBe(401);
    console.log('Received expected unauthorized response for invalid token');
    
    // Take screenshot of current page
    await page.screenshot({ path: 'test_screenshots/api-upload-invalid-token.png' });
  });
}); 