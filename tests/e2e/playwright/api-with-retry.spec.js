// @ts-check
const { test, expect } = require('@playwright/test');
const { createRetryableRequest, MAX_RETRIES } = require('./utils/retry-helper');

test('API health endpoint with retry limit', async ({ request }) => {
  // Create a retryable request client
  const retryableRequest = createRetryableRequest(request);
  
  // Make a request with retry limit
  const apiUrl = process.env.API_URL || 'http://api-test:8000';
  
  console.log(`Testing API health with max ${MAX_RETRIES} retries`);
  
  // Make a request with retry functionality built in
  const response = await retryableRequest.get(`${apiUrl}/api/v1/health/`, {
    retries: MAX_RETRIES // Explicitly limit to our MAX_RETRIES constant
  });
  
  // Verify the response
  expect(response.ok()).toBeTruthy();
  const data = await response.json();
  expect(data.status).toBe('ok');
  console.log('API health check successful');
}); 