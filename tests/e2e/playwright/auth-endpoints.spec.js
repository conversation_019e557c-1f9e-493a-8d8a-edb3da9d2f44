// @ts-check
const { test, expect } = require('@playwright/test');
const { testEnv, apiUrl, logEnvironment } = require('./config/test-environment');

test.describe('Authentication API Tests', () => {
  // Log environment info before all tests
  test.beforeAll(() => {
    logEnvironment();
    console.log('Testing authentication endpoints');
  });

  // Test login endpoint
  test('should handle login requests', async ({ request }) => {
    console.log('Testing login endpoint...');
    
    // Prepare login request data
    const loginData = {
      username: testEnv.auth.username,
      password: testEnv.auth.password
    };
    
    // Prepare request options
    const options = {
      maxRedirects: 5,
      data: loginData
    };
    
    // Login request
    const response = await request.post(`${testEnv.api.url}${testEnv.api.endpoints.login}`, options);
    
    console.log(`Login Response Status: ${response.status()}`);
    
    // In test environments, 404 is acceptable if the endpoint doesn't exist
    // 200/201 means login succeeded
    // 401 means invalid credentials but endpoint exists
    expect([200, 201, 401, 404]).toContain(response.status());
    
    // If login succeeded, verify token was returned
    if (response.status() === 200 || response.status() === 201) {
      const body = await response.json();
      console.log('Login response received');
      expect(body).toHaveProperty('token');
      // Store token for subsequent tests
      process.env.TEST_AUTH_TOKEN = body.token;
    }
  });

  // Test user info endpoint
  test('should retrieve user information if authenticated', async ({ request }) => {
    console.log('Testing user info endpoint...');
    
    // Prepare request options
    const options = {
      maxRedirects: 5
    };
    
    // Add auth token if available
    if (process.env.TEST_AUTH_TOKEN) {
      options.headers = {
        'Authorization': `Bearer ${process.env.TEST_AUTH_TOKEN}`
      };
    }
    
    // Request user info
    const response = await request.get(`${testEnv.api.url}${testEnv.api.prefix}/users/me/`, options);
    
    console.log(`User Info Response Status: ${response.status()}`);
    
    // 200 means success
    // 401 means unauthorized but endpoint exists
    // 404 means endpoint doesn't exist in test environment
    expect([200, 401, 404]).toContain(response.status());
    
    // If successful, verify user info structure
    if (response.status() === 200) {
      const body = await response.json();
      console.log('User info received');
      expect(body).toHaveProperty('username');
    }
  });

  // Test logout endpoint
  test('should handle logout requests', async ({ request }) => {
    console.log('Testing logout endpoint...');
    
    // Prepare request options
    const options = {
      maxRedirects: 5
    };
    
    // Add auth token if available
    if (process.env.TEST_AUTH_TOKEN) {
      options.headers = {
        'Authorization': `Bearer ${process.env.TEST_AUTH_TOKEN}`
      };
    }
    
    // Logout request
    const response = await request.post(`${testEnv.api.url}${testEnv.api.prefix}/auth/logout/`, options);
    
    console.log(`Logout Response Status: ${response.status()}`);
    
    // 200 means successful logout
    // 401 means unauthorized but endpoint exists
    // 404 means endpoint doesn't exist in test environment
    expect([200, 401, 404]).toContain(response.status());
    
    // Clear token after logout
    if (response.status() === 200) {
      delete process.env.TEST_AUTH_TOKEN;
    }
  });
}); 