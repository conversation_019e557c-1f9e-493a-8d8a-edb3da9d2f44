import { test, expect, Page } from '@playwright/test';
import * as path from 'path';
import * as fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

// This test validates the complete end-to-end flow of:
// 1. File upload through the UI
// 2. API validation of the uploaded file
// 3. VM creation from the uploaded file
// 4. Verification of VM status and accessibility
test.describe('Complete Upload to VM End-to-End Flow', () => {
  // Define test file details with unique identifier
  const testFileName = `test-upload-${uuidv4().substring(0, 8)}.txt`;
  const testFilePath = path.join(__dirname, `../../${testFileName}`);
  const testFileContent = `Test file content for complete E2E flow
Created at: ${new Date().toISOString()}
UUID: ${uuidv4()}
Random data: ${Array.from({ length: 20 }, () => Math.random().toString(36).substring(2)).join(' ')}`;
  
  // Set up the test file before all tests
  test.beforeAll(async () => {
    // Create test file with unique content
    fs.writeFileSync(testFilePath, testFileContent);
    console.log(`Created test file at ${testFilePath} (${Buffer.from(testFileContent).length} bytes)`);
  });
  
  // Clean up the test file after all tests
  test.afterAll(async () => {
    // Remove test file
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
      console.log(`Removed test file at ${testFilePath}`);
    }
  });
  
  // Helper function to log test steps
  function logStep(message: string) {
    console.log(`\n[TEST STEP] ${message}`);
  }
  
  // Helper function to wait for VM status
  async function waitForVMStatus(page: Page, vmId: string, targetStatus: string, maxAttempts = 15, intervalMs = 5000) {
    logStep(`Waiting for VM ${vmId} to reach status: ${targetStatus}`);
    
    for (let i = 0; i < maxAttempts; i++) {
      // Go to VM details page
      await page.goto(`/vagrant/vms/${vmId}`);
      await page.waitForLoadState('networkidle');
      
      // Take screenshot to track progress
      await page.screenshot({ path: `test_screenshots/vm-status-check-${i}.png` });
      
      // Check if status matches target
      const statusElement = page.locator('.vm-status');
      if (await statusElement.isVisible()) {
        const status = await statusElement.textContent();
        console.log(`[Attempt ${i+1}/${maxAttempts}] Current VM status: ${status}`);
        
        if (status?.toLowerCase().includes(targetStatus.toLowerCase())) {
          console.log(`✅ VM reached "${targetStatus}" status`);
          return true;
        }
      } else {
        console.log(`[Attempt ${i+1}/${maxAttempts}] Status element not visible`);
      }
      
      // Wait before checking again
      if (i < maxAttempts - 1) {
        console.log(`Waiting ${intervalMs/1000} seconds before next check...`);
        await page.waitForTimeout(intervalMs);
      }
    }
    
    console.log(`❌ Failed to reach "${targetStatus}" status after ${maxAttempts} attempts`);
    return false;
  }
  
  // Helper function to verify auth status and refresh if needed
  async function ensureAuthentication(page: Page) {
    logStep('Ensuring user is authenticated');
    
    // Check page for auth-related elements
    const hasLoginButton = await page.locator('button:has-text("Login"), button:has-text("Sign In")').isVisible();
    const hasAuthError = await page.locator('.auth-error, .login-required').isVisible();
    
    if (hasLoginButton || hasAuthError) {
      console.log('Not authenticated, attempting to log in');
      
      // Navigate to login page or click login button
      if (hasLoginButton) {
        await page.locator('button:has-text("Login"), button:has-text("Sign In")').click();
      } else {
        await page.goto('/login');
      }
      
      // Wait for login form
      await page.waitForSelector('input[type="text"], input[type="email"]', { timeout: 10000 });
      
      // Fill in credentials (these should be configured via environment variables in production)
      await page.fill('input[type="text"], input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'test-password');
      
      // Submit the form
      await page.click('button[type="submit"]');
      
      // Wait for redirect after login
      await page.waitForNavigation({ timeout: 15000 });
      
      // Take screenshot after login
      await page.screenshot({ path: 'test_screenshots/after-login.png' });
      
      // Verify login success (adjust selectors based on your UI)
      const isLoggedIn = await page.locator('.user-profile, .user-avatar, .user-menu').isVisible();
      expect(isLoggedIn).toBeTruthy();
    } else {
      console.log('User already authenticated');
    }
  }

  test('Complete E2E flow: Upload file, create VM, and verify VM creation', async ({ page }) => {
    // Set longer timeout for this test
    test.setTimeout(300000); // 5 minutes
    
    // Step 1: Ensure user is authenticated
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await ensureAuthentication(page);
    
    // Step 2: Navigate to the file upload page
    logStep('Navigating to file upload page');
    await page.goto('/upload');
    await page.waitForLoadState('networkidle');
    await page.screenshot({ path: 'test_screenshots/upload-page.png' });
    
    // Step 3: Upload the test file
    logStep('Uploading test file');
    
    // Fill description field with timestamp to easily identify this upload
    const uploadDescription = `E2E test upload - ${new Date().toISOString()}`;
    await page.fill('textarea[placeholder*="description"]', uploadDescription);
    
    // Upload the file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);
    await page.screenshot({ path: 'test_screenshots/file-selected.png' });
    
    // Submit the upload
    await page.click('button:has-text("Upload")');
    
    // Wait for upload to complete (watching for success message)
    await page.waitForSelector('.ant-message-success, .upload-success', { timeout: 60000 });
    await page.screenshot({ path: 'test_screenshots/upload-success.png' });
    
    // Step 4: Verify file appears in the file list
    logStep('Verifying file appears in file list');
    await page.goto('/files');
    await page.waitForLoadState('networkidle');
    
    // Wait for the file list to load
    await page.waitForSelector('.ant-table-row');
    await page.screenshot({ path: 'test_screenshots/file-list.png' });
    
    // Check if our file is in the list using the description we set
    const fileRow = page.locator(`.ant-table-row:has-text("${uploadDescription}")`).first();
    await fileRow.waitFor({ state: 'visible', timeout: 10000 });
    
    // Get the file ID from the row
    const fileId = await fileRow.getAttribute('data-row-key');
    expect(fileId).toBeTruthy();
    console.log(`Found uploaded file with ID: ${fileId}`);
    
    // Step 5: Navigate to VM creation page
    logStep('Navigating to VM creation page');
    await page.goto('/vagrant/vms/new');
    await page.waitForLoadState('networkidle');
    await page.screenshot({ path: 'test_screenshots/vm-creation-page.png' });
    
    // Step 6: Fill out VM creation form
    logStep('Creating new VM from uploaded file');
    
    // Generate unique VM name with timestamp
    const vmName = `e2e-test-vm-${new Date().getTime()}`;
    
    // Fill VM name field
    await page.fill('input[placeholder*="VM name"], input#vm-name', vmName);
    
    // Fill VM description
    await page.fill('textarea[placeholder*="description"], textarea#description', 
      `VM created from uploaded file in E2E test - ${new Date().toISOString()}`);
    
    // Select template (this will vary based on your UI)
    await page.click('.ant-select-selector');
    await page.waitForSelector('.ant-select-dropdown');
    
    // Select Ubuntu template if available, otherwise the first template
    try {
      await page.click('.ant-select-item:has-text("Ubuntu")');
    } catch {
      console.log('Ubuntu template not found, selecting first available template');
      await page.click('.ant-select-item:first-child');
    }
    
    // Optional: Select the uploaded file if there's a file selection step
    const hasFileSelect = await page.locator('select#fileId, .file-selector').isVisible();
    if (hasFileSelect) {
      console.log('File selection field found, selecting uploaded file');
      await page.selectOption('select#fileId', { value: fileId! });
    }
    
    // Take screenshot of the filled form
    await page.screenshot({ path: 'test_screenshots/vm-form-filled.png' });
    
    // Step 7: Submit the VM creation form
    logStep('Submitting VM creation form');
    await page.click('button:has-text("Create")');
    
    // Wait for creation process to start and redirect to VM list
    await page.waitForURL('/vagrant/vms', { timeout: 60000 });
    await page.screenshot({ path: 'test_screenshots/vm-created.png' });
    
    // Step 8: Find the newly created VM in the list
    logStep('Finding newly created VM in the list');
    
    // Wait for the VM to appear in the list
    await page.waitForSelector(`.ant-table-row:has-text("${vmName}")`, { timeout: 30000 });
    
    // Get the VM ID
    const vmRow = page.locator(`.ant-table-row:has-text("${vmName}")`).first();
    const vmId = await vmRow.getAttribute('data-row-key');
    
    if (!vmId) {
      throw new Error('Could not find VM ID');
    }
    
    console.log(`Found VM with ID: ${vmId}`);
    
    // Step 9: Navigate to VM details page
    logStep('Navigating to VM details page');
    await page.goto(`/vagrant/vms/${vmId}`);
    await page.waitForLoadState('networkidle');
    await page.screenshot({ path: 'test_screenshots/vm-details.png' });
    
    // Step 10: Verify VM reaches running state
    logStep('Verifying VM reaches running state');
    const vmRunning = await waitForVMStatus(page, vmId, 'running', 20, 10000); // 20 attempts, 10 seconds between checks
    
    // Take final screenshot of VM status
    await page.screenshot({ path: 'test_screenshots/vm-final-status.png' });
    
    // Assertion for VM status
    expect(vmRunning).toBeTruthy();
    
    // Step 11: Optional - Verify VM can be accessed via SSH or console
    // This step would depend on your UI and connectivity options
    logStep('Checking VM access options');
    
    const hasConsoleButton = await page.locator('button:has-text("Console"), a:has-text("Console")').isVisible();
    if (hasConsoleButton) {
      console.log('Console access button found');
      // Take screenshot showing console button
      await page.locator('button:has-text("Console"), a:has-text("Console")').scrollIntoViewIfNeeded();
      await page.screenshot({ path: 'test_screenshots/vm-console-button.png' });
    }
    
    const hasSSHButton = await page.locator('button:has-text("SSH"), a:has-text("SSH")').isVisible();
    if (hasSSHButton) {
      console.log('SSH access button found');
      // Take screenshot showing SSH button
      await page.locator('button:has-text("SSH"), a:has-text("SSH")').scrollIntoViewIfNeeded();
      await page.screenshot({ path: 'test_screenshots/vm-ssh-button.png' });
    }
    
    console.log('✅ Complete E2E test finished successfully');
  });
}); 