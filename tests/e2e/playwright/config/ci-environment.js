/**
 * CI Environment Configuration Helper
 * 
 * Extends the test environment configuration with CI-specific settings.
 * This module is loaded in CI environments to customize test behavior.
 */

const { testEnv, isRunningInContainer } = require('./test-environment');

/**
 * Determines if we're running in a CI environment
 * @returns {boolean} True if in CI environment
 */
function isRunningInCI() {
  return !!process.env.CI || !!process.env.GITHUB_ACTIONS;
}

/**
 * Configures the test environment for CI
 * @returns {Object} The updated environment configuration
 */
function configureCIEnvironment() {
  console.log('Configuring CI-specific test environment...');
  
  // Only make changes if we're in CI
  if (!isRunningInCI()) {
    console.log('Not running in CI environment, using default configuration');
    return testEnv;
  }
  
  // Clone the environment to avoid modifying the original
  const ciEnv = JSON.parse(JSON.stringify(testEnv));
  
  // CI-specific customizations
  ciEnv.isCI = true;
  
  // Increase timeouts for CI environment (which might be slower)
  ciEnv.timeouts = {
    ...ciEnv.timeouts,
    defaultTest: 90000,     // 1.5 minutes
    longRunning: 600000,    // 10 minutes for VM operations
    apiRequest: 15000,      // 15 seconds for API requests
  };
  
  // Configure artifact paths for GitHub Actions
  if (process.env.GITHUB_WORKSPACE) {
    ciEnv.artifacts = {
      screenshots: process.env.GITHUB_WORKSPACE + '/playwright-screenshots',
      videos: process.env.GITHUB_WORKSPACE + '/playwright-videos',
      reports: process.env.GITHUB_WORKSPACE + '/playwright-report',
    };
  }
  
  // Configure parallel test execution in CI
  ciEnv.parallelTests = process.env.CI_PARALLEL_TESTS === 'true';
  
  // Configure retries for CI (more retries on CI for flaky tests)
  ciEnv.testRetries = parseInt(process.env.TEST_RETRIES || '2', 10);
  
  // Log the updated configuration
  console.log('CI environment configuration:');
  console.log(`- API URL: ${ciEnv.api.url}`);
  console.log(`- Default timeout: ${ciEnv.timeouts.defaultTest}ms`);
  console.log(`- Parallel tests: ${ciEnv.parallelTests}`);
  console.log(`- Test retries: ${ciEnv.testRetries}`);
  
  return ciEnv;
}

// Export the environment and helper functions
module.exports = {
  isRunningInCI,
  configureCIEnvironment,
  ciTestEnv: configureCIEnvironment(),
}; 