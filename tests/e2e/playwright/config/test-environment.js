/**
 * Test Environment Configuration
 * 
 * This module provides a centralized configuration for Playwright tests,
 * with automatic environment detection (Docker container vs local development).
 */

const { execSync } = require('child_process');
const os = require('os');
const fs = require('fs');
const path = require('path');

// Default configuration values
const DEFAULT_CONFIG = {
  // API configuration
  api: {
    url: 'http://localhost:3050',
    endpoints: {
      health: '/api/health',
      login: '/api/auth/login',
      files: '/api/files',
      vms: '/api/vms',
    },
  },
  // Test timeouts
  timeouts: {
    defaultTest: 60000,     // 1 minute
    longRunning: 300000,    // 5 minutes for VM operations
    apiRequest: 10000,      // 10 seconds for API requests
    uiInteraction: 5000,    // 5 seconds for UI interactions
  },
  // Test user credentials
  auth: {
    username: 'testuser',
    password: 'password123',
  },
  // Directory for test artifacts
  artifacts: {
    screenshots: 'test_screenshots',
    videos: 'test_videos',
    reports: 'test-results',
  },
  // Browser configuration
  browser: {
    headless: true,
    slowMo: 100,            // Slow down by 100ms for visibility during debugging
  }
};

/**
 * Determines if we're running in a Docker container
 * @returns {boolean} True if in container, false otherwise
 */
function isRunningInContainer() {
  try {
    return fs.existsSync('/.dockerenv') || 
           execSync('grep -q docker /proc/1/cgroup').toString().trim() !== '';
  } catch (e) {
    return false;
  }
}

/**
 * Detects the API host based on environment
 * @returns {string} The API URL
 */
function detectApiHost() {
  // Check if running in Docker
  if (isRunningInContainer()) {
    // First try using the container hostname (most reliable in Docker networks)
    try {
      // Test if we can ping the API container by hostname
      const output = require('child_process').execSync('ping -c 1 api-test 2>/dev/null || echo "ERROR"').toString();
      if (!output.includes('ERROR')) {
        console.log('API container found via hostname in the Docker network');
        return 'http://api-test:8000';  // Use container name when in the same Docker network
      }
    } catch (e) {
      console.log('Error checking API container via hostname:', e.message);
    }
    
    // Check if API host is defined in container's hosts file
    try {
      const hostsFile = fs.readFileSync('/etc/hosts', 'utf8');
      const apiHostMatch = hostsFile.match(/(\d+\.\d+\.\d+\.\d+)\s+api/);
      if (apiHostMatch && apiHostMatch[1]) {
        console.log(`API host found in /etc/hosts: ${apiHostMatch[1]}`);
        return `http://${apiHostMatch[1]}:8000`;
      }
    } catch (e) {
      console.log('Error reading /etc/hosts:', e.message);
    }
    
    // Fallback to environment variable or different container network address and port options
    // The container runs on 8000 internally but exposes 3050 to the host
    const containerApiHost = process.env.API_HOST || 'http://api-test:8000';
    console.log('Using fallback API URL:', containerApiHost);
    return containerApiHost;
  }
  
  // For local development, try API connection on various ports/hostnames
  const possibleUrls = [
    process.env.API_URL,         // Environment variable (highest priority)
    'http://localhost:3055',     // Host port mapping   
    'http://localhost:8000',     // Direct port (if running locally)
    'http://127.0.0.1:3055',     // Alternative localhost
    'http://127.0.0.1:8000',     // Alternative direct port
  ].filter(Boolean); // Remove undefined values
  
  // Try to find the first URL that responds
  for (const url of possibleUrls) {
    try {
      console.log(`Trying API URL: ${url}`);
      const http = require('http');
      const options = new URL(url);
      const req = http.request(options);
      req.end();
      // If no error is thrown, use this URL
      return url;
    } catch (e) {
      console.log(`Failed to connect to ${url}: ${e.message}`);
      // Continue to the next URL
    }
  }
  
  // Default to the first defined URL or a safe default
  return possibleUrls[0] || 'http://localhost:3055';
}

/**
 * Builds the test environment configuration
 * @returns {Object} The environment configuration
 */
function buildConfig() {
  const apiUrl = detectApiHost();
  console.log(`Detected API URL: ${apiUrl}`);
  
  // Deep clone the default config and update API URL
  const config = JSON.parse(JSON.stringify(DEFAULT_CONFIG));
  config.api.url = apiUrl;
  
  // Update API endpoints to use the correct prefix and trailing slashes
  config.api.endpoints = {
    health: '/api/v1/health/',
    login: '/api/v1/auth/login/',
    files: '/api/v1/files/',
    vms: '/api/v1/vms/',
  };
  
  // Set API prefix
  config.api.prefix = '/api/v1';
  
  // Update headless mode from environment variable if set
  if (process.env.HEADLESS === 'false') {
    config.browser.headless = false;
  }
  
  // Update slowMo from environment variable if set for debugging
  if (process.env.SLOW_MO) {
    config.browser.slowMo = parseInt(process.env.SLOW_MO, 10);
  }
  
  return config;
}

/**
 * The test environment configuration
 */
const testEnv = buildConfig();

/**
 * Builds a full API endpoint URL
 * @param {string} endpoint - The endpoint path
 * @returns {string} The complete URL
 */
function apiUrl(endpoint) {
  const baseUrl = testEnv.api.url;
  // Handle both absolute and relative endpoints
  if (endpoint.startsWith('/')) {
    return `${baseUrl}${endpoint}`;
  }
  return `${baseUrl}/${endpoint}`;
}

/**
 * Logs the current test environment configuration
 */
function logEnvironment() {
  console.log('=== Test Environment ===');
  console.log(`API URL: ${testEnv.api.url}`);
  console.log(`Running in container: ${isRunningInContainer()}`);
  console.log(`Headless mode: ${testEnv.browser.headless}`);
  console.log(`Default timeout: ${testEnv.timeouts.defaultTest}ms`);
  console.log('=======================');
}

module.exports = {
  testEnv,
  apiUrl,
  isRunningInContainer,
  logEnvironment
}; 