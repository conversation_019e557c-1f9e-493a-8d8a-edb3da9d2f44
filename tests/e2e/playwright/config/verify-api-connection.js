/**
 * API Connectivity Verification Script
 * 
 * This script is used in CI/CD to verify that the API is accessible
 * before running tests. It will exit with a non-zero status if the
 * API health endpoint cannot be reached.
 */

const http = require('http');
const https = require('https');
const { testEnv } = require('./test-environment');

console.log('=== API Connectivity Verification ===');
console.log(`Testing API URL: ${testEnv.api.url}${testEnv.api.endpoints.health}`);

// Parse URL to determine protocol and build request options
const url = new URL(`${testEnv.api.url}${testEnv.api.endpoints.health}`);
const options = {
  hostname: url.hostname,
  port: url.port || (url.protocol === 'https:' ? 443 : 80),
  path: url.pathname,
  method: 'GET',
  timeout: 5000,  // 5 second timeout
  headers: {
    'User-Agent': 'API-Verify/1.0'
  }
};

console.log(`Request details: ${url.protocol}//${options.hostname}:${options.port}${options.path}`);

// Select the appropriate protocol
const protocol = url.protocol === 'https:' ? https : http;

// Maximum number of retries
const maxRetries = 3;
let retryCount = 0;

function makeRequest() {
  console.log(`Attempt ${retryCount + 1}/${maxRetries} to connect to API...`);
  
  const req = protocol.request(options, (res) => {
    console.log(`Response status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        console.log('API is accessible!');
        console.log(`Response body: ${data}`);
        console.log('Verification successful');
        process.exit(0);
      } else {
        console.log(`Error: Received status code ${res.statusCode}`);
        retryOrFail();
      }
    });
  });
  
  req.on('error', (error) => {
    console.error(`Error connecting to API: ${error.message}`);
    retryOrFail();
  });
  
  req.on('timeout', () => {
    console.error('Connection timed out');
    req.destroy();
    retryOrFail();
  });
  
  req.end();
}

function retryOrFail() {
  retryCount++;
  if (retryCount < maxRetries) {
    console.log(`Retrying in 5 seconds...`);
    setTimeout(makeRequest, 5000);
  } else {
    console.error('All connection attempts failed');
    console.error('API verification failed. Check API container or network configuration.');
    process.exit(1);
  }
}

// Start the verification process
makeRequest(); 