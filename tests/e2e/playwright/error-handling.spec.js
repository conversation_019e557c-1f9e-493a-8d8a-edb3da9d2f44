// @ts-check
const { test, expect } = require('@playwright/test');
const { testEnv, apiUrl, logEnvironment } = require('./config/test-environment');

test.describe('Error Handling and Edge Cases', () => {
  // Log environment info before all tests
  test.beforeAll(() => {
    logEnvironment();
    console.log('Testing error handling and edge cases');
  });

  // Test invalid authentication
  test('should handle invalid authentication gracefully', async ({ request }) => {
    console.log('Testing invalid authentication handling...');
    
    // Prepare login with invalid credentials
    const invalidLoginData = {
      username: 'invalid_user',
      password: 'wrong_password'
    };
    
    // Prepare request options
    const options = {
      maxRedirects: 5,
      data: invalidLoginData
    };
    
    // Login request with invalid credentials
    const response = await request.post(`${testEnv.api.url}${testEnv.api.endpoints.login}`, options);
    
    console.log(`Invalid Login Response Status: ${response.status()}`);
    
    // 401 means authentication failed but endpoint exists (expected result)
    // 404 means endpoint doesn't exist in test environment (acceptable for tests)
    // 400 means invalid request format (also acceptable)
    expect([400, 401, 404]).toContain(response.status());
    
    // If 401, verify error message
    if (response.status() === 401) {
      const body = await response.json();
      console.log('Invalid login response message:', body.message || body.detail || 'No message');
      expect(body).toHaveProperty('detail');
    }
  });

  // Test request with missing required fields
  test('should validate required fields', async ({ request }) => {
    console.log('Testing required field validation...');
    
    // Missing required fields in login request
    const incompleteData = {
      username: 'testuser'
      // Missing password field
    };
    
    // Prepare request options
    const options = {
      maxRedirects: 5,
      data: incompleteData
    };
    
    // Login request with missing fields
    const response = await request.post(`${testEnv.api.url}${testEnv.api.endpoints.login}`, options);
    
    console.log(`Incomplete Request Response Status: ${response.status()}`);
    
    // 400 means bad request due to missing fields (expected result)
    // 404 means endpoint doesn't exist in test environment (acceptable for tests)
    // 422 is also common for validation errors
    expect([400, 404, 422]).toContain(response.status());
    
    // If validation error, verify error message
    if (response.status() === 400 || response.status() === 422) {
      const body = await response.json();
      console.log('Validation error:', body.message || body.detail || 'No message');
      // Should have some kind of error message
      expect(body).toHaveProperty('detail');
    }
  });

  // Test invalid resource ID
  test('should handle invalid resource IDs correctly', async ({ request }) => {
    console.log('Testing invalid resource ID handling...');
    
    // Request with invalid resource ID
    const invalidResourceId = 'nonexistent-id-12345';
    
    // Prepare request options
    const options = {
      maxRedirects: 5
    };
    
    // Add auth token if available
    if (process.env.TEST_AUTH_TOKEN) {
      options.headers = {
        'Authorization': `Bearer ${process.env.TEST_AUTH_TOKEN}`
      };
    }
    
    // Request resource with invalid ID
    const response = await request.get(`${testEnv.api.url}${testEnv.api.endpoints.files}${invalidResourceId}/`, options);
    
    console.log(`Invalid Resource Response Status: ${response.status()}`);
    
    // 404 means resource not found (expected result)
    // 400 means bad request format (also acceptable)
    expect([400, 404]).toContain(response.status());
    
    // If response has error details, verify them
    if (response.headers()['content-type']?.includes('application/json')) {
      const body = await response.json();
      console.log('Invalid resource error:', body.message || body.detail || 'No message');
      // Should have some kind of error message
      expect(body).toHaveProperty('detail');
    }
  });

  // Test rate limiting behavior (if implemented)
  test('should have rate limiting protection', async ({ request }) => {
    console.log('Testing rate limiting protection...');
    
    // Prepare request options
    const options = {
      maxRedirects: 5
    };
    
    // Make 10 consecutive requests to potentially trigger rate limiting
    let rateLimit = false;
    let consecutiveRequests = 10;
    
    for (let i = 0; i < consecutiveRequests; i++) {
      const response = await request.get(`${testEnv.api.url}${testEnv.api.endpoints.health}`, options);
      
      // Check for rate limiting headers
      const rateLimitHeader = response.headers()['x-ratelimit-limit'] || 
                              response.headers()['x-rate-limit-limit'] || 
                              response.headers()['ratelimit-limit'];
                              
      const rateLimitRemaining = response.headers()['x-ratelimit-remaining'] || 
                                response.headers()['x-rate-limit-remaining'] || 
                                response.headers()['ratelimit-remaining'];
      
      // If we get a 429 response or specific headers, rate limiting is implemented
      if (response.status() === 429 || (rateLimitHeader && rateLimitRemaining)) {
        rateLimit = true;
        console.log(`Rate limiting detected! Limit: ${rateLimitHeader}, Remaining: ${rateLimitRemaining}`);
        break;
      }
    }
    
    // Not a failure if rate limiting isn't implemented, just log it
    console.log(rateLimit ? 'Rate limiting is implemented' : 'No rate limiting detected');
  });

  // Test security headers
  test('should set appropriate security headers', async ({ request }) => {
    console.log('Testing security headers...');
    
    // Make a simple request to check headers
    const response = await request.get(`${testEnv.api.url}${testEnv.api.endpoints.health}`);
    
    console.log(`Response Status: ${response.status()}`);
    
    // Get all headers
    const headers = response.headers();
    console.log('Security headers present:');
    
    // Check for common security headers
    const securityHeaders = [
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection',
      'content-security-policy',
      'strict-transport-security',
      'referrer-policy'
    ];
    
    // Log which security headers are present
    securityHeaders.forEach(header => {
      if (headers[header]) {
        console.log(`- ${header}: ${headers[header]}`);
      }
    });
    
    // Not a failure if headers aren't present, just informational
  });
}); 