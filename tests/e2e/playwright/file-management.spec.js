// @ts-check
const { test, expect } = require('@playwright/test');
const { testEnv, apiUrl, logEnvironment } = require('./config/test-environment');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

test.describe('File Management API Tests', () => {
  // Test data
  const testFileName = `test-file-${Date.now()}.txt`;
  const testFileContent = `Test file content generated at ${new Date().toISOString()}`;
  let testFileId = null;
  let testFilePath = null;

  // Log environment info before all tests
  test.beforeAll(async () => {
    logEnvironment();
    console.log('Testing file management endpoints');
    
    // Create a temporary test file
    testFilePath = path.join(process.cwd(), 'test_artifacts', testFileName);
    try {
      // Ensure directory exists
      await fs.promises.mkdir(path.dirname(testFilePath), { recursive: true });
      // Write test file
      await fs.promises.writeFile(testFilePath, testFileContent);
      console.log(`Created test file: ${testFilePath}`);
    } catch (error) {
      console.error(`Error creating test file: ${error.message}`);
    }
  });

  // Clean up after tests
  test.afterAll(async () => {
    // Clean up test file
    try {
      if (testFilePath && fs.existsSync(testFilePath)) {
        await fs.promises.unlink(testFilePath);
        console.log(`Deleted test file: ${testFilePath}`);
      }
    } catch (error) {
      console.error(`Error cleaning up test file: ${error.message}`);
    }
  });

  // Test listing files endpoint
  test('should list available files', async ({ request }) => {
    console.log('Testing file listing endpoint...');
    
    // Prepare request options
    const options = {
      maxRedirects: 5
    };
    
    // Add auth token if available
    if (process.env.TEST_AUTH_TOKEN) {
      options.headers = {
        'Authorization': `Bearer ${process.env.TEST_AUTH_TOKEN}`
      };
    }
    
    // List files request
    const response = await request.get(`${testEnv.api.url}${testEnv.api.endpoints.files}`, options);
    
    console.log(`File List Response Status: ${response.status()}`);
    
    // 200 means successful listing
    // 401 means unauthorized but endpoint exists
    // 404 means endpoint doesn't exist in test environment
    expect([200, 401, 404]).toContain(response.status());
    
    // If successful, verify response structure
    if (response.status() === 200) {
      const body = await response.json();
      console.log(`Files listed: ${body.length || 0}`);
      expect(Array.isArray(body)).toBeTruthy();
    }
  });

  // Test file upload endpoint
  test('should upload a file', async ({ request }) => {
    console.log('Testing file upload endpoint...');
    
    // Skip if test file doesn't exist
    if (!testFilePath || !fs.existsSync(testFilePath)) {
      console.log('Skipping test: Test file not created successfully');
      test.skip();
      return;
    }
    
    // Create form data for upload
    const formData = new FormData();
    const fileContent = await fs.promises.readFile(testFilePath);
    const blob = new Blob([fileContent], { type: 'text/plain' });
    formData.append('file', blob, testFileName);
    
    // Prepare request options
    const options = {
      maxRedirects: 5,
      data: formData
    };
    
    // Add auth token if available
    if (process.env.TEST_AUTH_TOKEN) {
      options.headers = {
        'Authorization': `Bearer ${process.env.TEST_AUTH_TOKEN}`
      };
    }
    
    // Upload file request
    const response = await request.post(`${testEnv.api.url}${testEnv.api.endpoints.files}upload/`, options);
    
    console.log(`File Upload Response Status: ${response.status()}`);
    
    // 200/201 means successful upload
    // 401 means unauthorized but endpoint exists
    // 404 means endpoint doesn't exist in test environment
    expect([200, 201, 401, 404]).toContain(response.status());
    
    // If successful, verify response and store file ID
    if (response.status() === 200 || response.status() === 201) {
      const body = await response.json();
      console.log('File uploaded successfully');
      expect(body).toHaveProperty('id');
      expect(body).toHaveProperty('filename');
      expect(body.filename).toBe(testFileName);
      testFileId = body.id;
    }
  });

  // Test getting file info endpoint
  test('should retrieve file information', async ({ request }) => {
    console.log('Testing file info endpoint...');
    
    // Skip if previous upload didn't succeed
    if (!testFileId) {
      console.log('Skipping test: No file ID available from previous upload test');
      test.skip();
      return;
    }
    
    // Prepare request options
    const options = {
      maxRedirects: 5
    };
    
    // Add auth token if available
    if (process.env.TEST_AUTH_TOKEN) {
      options.headers = {
        'Authorization': `Bearer ${process.env.TEST_AUTH_TOKEN}`
      };
    }
    
    // Get file info request
    const response = await request.get(`${testEnv.api.url}${testEnv.api.endpoints.files}${testFileId}/`, options);
    
    console.log(`File Info Response Status: ${response.status()}`);
    
    // 200 means successful retrieval
    // 401 means unauthorized but endpoint exists
    // 404 means endpoint doesn't exist or file not found
    expect([200, 401, 404]).toContain(response.status());
    
    // If successful, verify file info
    if (response.status() === 200) {
      const body = await response.json();
      console.log('File info retrieved');
      expect(body).toHaveProperty('id');
      expect(body).toHaveProperty('filename');
      expect(body.id).toBe(testFileId);
    }
  });

  // Test file download endpoint
  test('should download a file', async ({ request }) => {
    console.log('Testing file download endpoint...');
    
    // Skip if previous upload didn't succeed
    if (!testFileId) {
      console.log('Skipping test: No file ID available from previous upload test');
      test.skip();
      return;
    }
    
    // Prepare request options
    const options = {
      maxRedirects: 5
    };
    
    // Add auth token if available
    if (process.env.TEST_AUTH_TOKEN) {
      options.headers = {
        'Authorization': `Bearer ${process.env.TEST_AUTH_TOKEN}`
      };
    }
    
    // Download file request
    const response = await request.get(`${testEnv.api.url}${testEnv.api.endpoints.files}${testFileId}/download/`, options);
    
    console.log(`File Download Response Status: ${response.status()}`);
    
    // 200 means successful download
    // 401 means unauthorized but endpoint exists
    // 404 means endpoint doesn't exist or file not found
    expect([200, 401, 404]).toContain(response.status());
    
    // If successful, verify downloaded content
    if (response.status() === 200) {
      const downloadedContent = await response.text();
      console.log('File downloaded successfully');
      expect(downloadedContent).toBe(testFileContent);
    }
  });

  // Test file deletion endpoint
  test('should delete a file', async ({ request }) => {
    console.log('Testing file deletion endpoint...');
    
    // Skip if previous upload didn't succeed
    if (!testFileId) {
      console.log('Skipping test: No file ID available from previous upload test');
      test.skip();
      return;
    }
    
    // Prepare request options
    const options = {
      maxRedirects: 5
    };
    
    // Add auth token if available
    if (process.env.TEST_AUTH_TOKEN) {
      options.headers = {
        'Authorization': `Bearer ${process.env.TEST_AUTH_TOKEN}`
      };
    }
    
    // Delete file request
    const response = await request.delete(`${testEnv.api.url}${testEnv.api.endpoints.files}${testFileId}/`, options);
    
    console.log(`File Deletion Response Status: ${response.status()}`);
    
    // 204 means successful deletion with no content
    // 200 means successful deletion with content
    // 401 means unauthorized but endpoint exists
    // 404 means endpoint doesn't exist or file not found
    expect([200, 204, 401, 404]).toContain(response.status());
  });
}); 