// @ts-check
const { test, expect } = require('@playwright/test');

test('Frontend landing page should load', async ({ page }) => {
  // We can use a simple check of the API for the frontend test
  const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3100';
  
  console.log(`Testing Frontend at ${frontendUrl}`);
  
  await page.goto(frontendUrl);
  
  // Simple check for page title
  const title = await page.title();
  console.log(`Page title: ${title}`);
  
  // Check for common elements
  const bodyText = await page.textContent('body');
  expect(bodyText).not.toBeNull();
  
  console.log('Frontend test completed successfully');
}); 