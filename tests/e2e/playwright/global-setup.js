// @ts-check
const { chromium } = require('@playwright/test');
const { testEnv, logEnvironment } = require('./config/test-environment');
const path = require('path');
const fs = require('fs');

/**
 * Global setup for Playwright tests
 * - Handles authentication
 * - Creates storage state for logged in user
 * - Ensures test directories exist
 */
async function globalSetup() {
  // Log environment information
  logEnvironment();

  // Create directories for test artifacts if they don't exist
  ensureDirectoriesExist([
    testEnv.artifacts.screenshots,
    testEnv.artifacts.reports,
    testEnv.artifacts.videos
  ]);

  // Set up authentication storage state
  await setupAuthState();

  console.log('✅ Global setup complete');
}

/**
 * Creates authentication state for tests to use
 */
async function setupAuthState() {
  console.log('Setting up authentication state...');
  const browser = await chromium.launch({
    headless: testEnv.browser.headless,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const page = await browser.newPage();
    
    // Go to login page
    await page.goto(`${testEnv.api.url}/login`);
    
    // Fill in login form
    await page.fill('input[name="username"]', testEnv.auth.username);
    await page.fill('input[name="password"]', testEnv.auth.password);
    
    // Submit form
    await page.click('button:has-text("Login")');
    
    // Wait for successful login
    await page.waitForTimeout(1000);
    
    // Save storage state for later use in tests
    await page.context().storageState({ path: './tests/playwright/auth-state.json' });
    
    console.log('✓ Authentication state saved');
  } catch (e) {
    console.warn('⚠️ Failed to set up auth state:', e.message);
    console.log('Tests will continue but may fail if they require authentication');
  } finally {
    await browser.close();
  }
}

/**
 * Ensures directories exist for test artifacts
 * @param {string[]} dirs - Directories to create
 */
function ensureDirectoriesExist(dirs) {
  dirs.forEach(dir => {
    const absolutePath = path.isAbsolute(dir) ? dir : path.join(process.cwd(), dir);
    if (!fs.existsSync(absolutePath)) {
      fs.mkdirSync(absolutePath, { recursive: true });
      console.log(`Created directory: ${absolutePath}`);
    }
  });
}

module.exports = globalSetup; 