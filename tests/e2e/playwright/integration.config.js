// @ts-check
const { defineConfig } = require('@playwright/test');

/**
 * Playwright configuration for integration tests with real VMs
 * 
 * This configuration has increased timeouts and specific settings
 * optimized for testing with real Vagrant VMs that can take longer
 * to start, stop, and provision.
 */
module.exports = defineConfig({
  testDir: './',
  
  // Increased timeouts for real VM testing
  timeout: 600000, // 10 minutes global timeout
  expect: {
    timeout: 30000  // 30 seconds for assertions
  },
  
  // Run tests in sequence to avoid resource contention with VMs
  fullyParallel: false,
  workers: 1,
  
  // Retry tests to handle intermittent issues
  retries: 1,
  
  // Detailed reporting
  reporter: [
    ['html', { outputFolder: 'test-results/html' }],
    ['json', { outputFile: 'test-results/integration-results.json' }],
    ['list']
  ],
  
  // Use specific project for VM tests
  projects: [
    {
      name: 'vagrant-integration',
      testMatch: /.*vagrant-integration\.spec\.js/,
      use: {
        baseURL: process.env.BASE_URL || 'http://localhost:3100',
        trace: 'retain-on-failure',
        screenshot: 'on',
        video: 'on-first-retry',
        
        // Longer navigation timeout
        navigationTimeout: 60000,
        
        // Viewport large enough to see VM details
        viewport: { width: 1600, height: 900 },
        
        // Additional timeout configuration
        actionTimeout: 30000,
        
        // Launch options to ensure stability
        launchOptions: {
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu'
          ]
        }
      }
    }
  ],
  
  // Output test artifacts to a dedicated directory
  outputDir: 'test-results/artifacts',
  
  use: {
    // Universal settings for all projects
    baseURL: process.env.BASE_URL || 'http://localhost:3100',
    trace: 'retain-on-failure',
  }
}); 