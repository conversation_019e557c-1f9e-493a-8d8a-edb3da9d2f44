// @ts-check
const { test, expect } = require('@playwright/test');
const { login } = require('../utils/auth-helpers');
const { createISOFile, generateUniqueFilename, cleanupTestFiles } = require('../utils/file-fixtures');

// Increase timeouts for E2E operations
test.setTimeout(600000); // 10 minutes for VM operations

/**
 * Helper function to poll until VM reaches desired state
 * @param {import('@playwright/test').Page} page 
 * @param {string} vmName 
 * @param {string} expectedState 
 * @param {number} timeoutMs 
 * @param {number} pollIntervalMs 
 */
async function pollVMUntilState(page, vmName, expectedState, timeoutMs = 300000, pollIntervalMs = 5000) {
  const startTime = Date.now();
  let currentState = '';
  
  console.log(`Waiting for VM "${vmName}" to reach state "${expectedState}"...`);
  
  while (Date.now() - startTime < timeoutMs) {
    // Navigate to VM status page and refresh
    await page.goto('/vm_status');
    await page.click('button:has-text("Refresh")');
    await page.waitForTimeout(1000);
    
    // Find our VM in the list
    const vmRow = page.locator(`.ant-table-row:has-text("${vmName}")`);
    if (await vmRow.count() === 0) {
      console.log(`VM "${vmName}" not found in list, retrying...`);
      await page.waitForTimeout(pollIntervalMs);
      continue;
    }
    
    // Get current state
    const stateCell = vmRow.locator('td:nth-child(2)');
    currentState = await stateCell.textContent() || '';
    console.log(`Current state of VM "${vmName}": ${currentState}`);
    
    // Check if we've reached the expected state
    if (currentState.toLowerCase().includes(expectedState.toLowerCase())) {
      console.log(`VM "${vmName}" has reached expected state "${expectedState}"`);
      return true;
    }
    
    // Wait before polling again
    console.log(`Waiting ${pollIntervalMs/1000} seconds before checking again...`);
    await page.waitForTimeout(pollIntervalMs);
  }
  
  throw new Error(`Timeout waiting for VM "${vmName}" to reach state "${expectedState}". Current state: ${currentState}`);
}

test.describe('File Upload to VM Integration Tests', () => {
  let testVMName;
  let testFileName;
  let testFilePath;
  
  // Set up unique names and test file
  test.beforeAll(() => {
    testVMName = `filevm-test-${Date.now()}`;
    testFileName = generateUniqueFilename('test-file', 'iso');
    testFilePath = createISOFile(testFileName, 200); // Create a 200KB ISO file
    console.log(`Created test file: ${testFilePath}`);
  });
  
  // Clean up any VMs and files created during testing
  test.afterAll(async ({ browser }) => {
    // Create a new page for cleanup
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
      // Login
      await login(page, 'testuser', 'password123');
      
      // Clean up VM
      await page.goto('/vm_status');
      const vmRow = page.locator(`.ant-table-row:has-text("${testVMName}")`);
      
      if (await vmRow.count() > 0) {
        console.log(`Cleaning up test VM: ${testVMName}`);
        
        // Check if VM is running and stop it first if needed
        const statusCell = vmRow.locator('td:nth-child(2)');
        const status = await statusCell.textContent() || '';
        
        if (status.toLowerCase().includes('running')) {
          // Stop VM first
          await vmRow.locator('button:has-text("Stop")').click();
          await page.locator('.ant-modal-confirm .ant-btn-primary').click();
          await pollVMUntilState(page, testVMName, 'stopped', 180000);
        }
        
        // Delete VM
        await page.goto('/vm_status');
        await page.click('button:has-text("Refresh")');
        const vmRowAgain = page.locator(`.ant-table-row:has-text("${testVMName}")`);
        await vmRowAgain.locator('button:has-text("Delete")').click();
        await page.locator('.ant-modal-confirm .ant-btn-primary').click();
        
        // Wait for deletion to complete
        await page.waitForTimeout(10000);
        console.log(`Test VM ${testVMName} has been deleted`);
      }
      
      // Clean up file
      await page.goto('/file_upload');
      await page.waitForTimeout(2000);
      
      // Look for our test file and delete it
      const fileRow = page.locator(`.ant-table-row:has-text("${testFileName}")`);
      
      if (await fileRow.count() > 0) {
        console.log(`Cleaning up test file: ${testFileName}`);
        await fileRow.locator('button:has-text("Delete")').click();
        await page.locator('.ant-modal-confirm .ant-btn-primary').click();
        await page.waitForTimeout(2000);
      }
    } catch (error) {
      console.error(`Error during cleanup: ${error.message}`);
    } finally {
      // Clean up local test files
      cleanupTestFiles([testFileName]);
      
      await context.close();
    }
  });
  
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await login(page, 'testuser', 'password123');
  });
  
  test('complete end-to-end file to VM integration flow', async ({ page }) => {
    // Step 1: Upload a file through the UI
    console.log('Step 1: Uploading file');
    await page.goto('/file_upload');
    
    // Click upload button to open modal
    await page.click('button:has-text("Upload"), button:has-text("Add File")');
    await page.waitForSelector('.ant-modal, .upload-modal');
    
    // Set file description
    await page.fill('textarea[name="description"], textarea[placeholder*="description"]', 'Test file for VM integration testing');
    
    // Upload file
    await page.setInputFiles('input[type="file"]', testFilePath);
    
    // Take screenshot before submitting
    await page.screenshot({ path: 'test-results/file-to-vm-1-before-upload.png' });
    
    // Submit upload
    await page.click('.ant-modal button[type="submit"], .ant-modal .ant-btn-primary');
    
    // Wait for upload to complete
    await page.waitForResponse(response => 
      response.url().includes('/api/file/upload') && 
      response.status() === 200
    );
    
    // Verify file appears in list
    await page.waitForSelector(`.ant-table-row:has-text("${testFileName}")`);
    await page.screenshot({ path: 'test-results/file-to-vm-2-after-upload.png' });
    
    // Step 2: Create a VM
    console.log('Step 2: Creating VM');
    await page.goto('/vm_injection');
    
    // Fill VM creation form
    await page.fill('input[name="vm_name"]', testVMName);
    await page.fill('input[name="memory"]', '1024');
    await page.fill('input[name="cpu"]', '1');
    await page.fill('input[name="disk"]', '10');
    
    // Select Vagrant VM type
    await page.selectOption('select[name="vm_type"], select[name="provider"]', 'vagrant');
    
    // Select a Vagrant box
    await page.selectOption('select[name="box"], select[name="os_type"]', 'ubuntu/bionic64');
    
    // Take screenshot before submitting
    await page.screenshot({ path: 'test-results/file-to-vm-3-before-vm-creation.png' });
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Wait for VM creation to start
    await page.waitForResponse(response => 
      (response.url().includes('/api/vm/create') || 
      response.url().includes('/api/vagrant_vm/create')) &&
      response.status() === 200
    );
    
    // Wait for VM to appear in list
    await page.goto('/vm_status');
    
    // Wait for the VM to appear in the list
    let retries = 0;
    const maxRetries = 10;
    let vmFound = false;
    
    while (retries < maxRetries && !vmFound) {
      await page.click('button:has-text("Refresh")');
      await page.waitForTimeout(5000);
      
      // Check if our VM appears in the list
      const vmRow = page.locator(`.ant-table-row:has-text("${testVMName}")`);
      vmFound = await vmRow.count() > 0;
      
      if (!vmFound) {
        console.log(`VM not found yet, retrying (${retries + 1}/${maxRetries})...`);
        retries++;
      }
    }
    
    expect(vmFound).toBeTruthy();
    await page.screenshot({ path: 'test-results/file-to-vm-4-vm-created.png' });
    
    // Step 3: Start the VM
    console.log('Step 3: Starting VM');
    const vmRow = page.locator(`.ant-table-row:has-text("${testVMName}")`);
    
    // Check if VM is already running
    const statusCell = vmRow.locator('td:nth-child(2)');
    const currentStatus = await statusCell.textContent() || '';
    
    if (!currentStatus.toLowerCase().includes('running')) {
      // Start the VM
      await vmRow.locator('button:has-text("Start")').click();
      
      // Confirm start operation
      await page.locator('.ant-modal-confirm .ant-btn-primary').click();
      
      // Wait for start to initiate
      await page.waitForResponse(response => 
        response.url().includes('/api/vm/start') || 
        response.url().includes('/api/vagrant_vm/start')
      );
      
      // Poll until VM is running
      await pollVMUntilState(page, testVMName, 'running');
    }
    
    await page.screenshot({ path: 'test-results/file-to-vm-5-vm-running.png' });
    
    // Step 4: Attach the file to the VM
    console.log('Step 4: Attaching file to VM');
    await page.goto('/file_to_vm');
    
    // Select the file from the dropdown
    const fileSelector = page.locator('select[name="file_id"], .file-select');
    await fileSelector.selectOption({ label: testFileName });
    
    // Select the VM from the dropdown
    const vmSelector = page.locator('select[name="vm_id"], .vm-select');
    await vmSelector.selectOption({ label: testVMName });
    
    // Take screenshot before attaching
    await page.screenshot({ path: 'test-results/file-to-vm-6-before-attach.png' });
    
    // Submit the attachment form
    await page.click('button[type="submit"], button:has-text("Attach")');
    
    // Wait for attachment to complete
    await page.waitForResponse(response => 
      response.url().includes('/api/file_to_vm') && 
      response.status() === 200
    );
    
    // Verify success message
    const successMessageElement = page.locator('.ant-message-success, .success-message');
    await expect(successMessageElement).toBeVisible();
    
    const successMessageText = await successMessageElement.textContent() || '';
    expect(successMessageText.toLowerCase().includes('attached') || 
           successMessageText.toLowerCase().includes('success')).toBeTruthy();
    
    await page.screenshot({ path: 'test-results/file-to-vm-7-after-attach.png' });
    
    // Step 5: Verify the attachment in VM details
    console.log('Step 5: Verifying file attachment in VM details');
    await page.goto('/vm_status');
    
    // Click on the VM to see details
    await page.click(`.ant-table-row:has-text("${testVMName}") td:first-child`);
    
    // Wait for details page to load
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of VM details with attached file
    await page.screenshot({ path: 'test-results/file-to-vm-8-vm-details.png' });
    
    // Look for file attachment section
    const attachmentsSection = page.locator('.attached-files, .file-attachments, .vm-files');
    
    // If attachments section exists, verify our file is there
    if (await attachmentsSection.count() > 0) {
      const fileInfo = attachmentsSection.locator(`:has-text("${testFileName}")`);
      expect(await fileInfo.count()).toBeGreaterThan(0);
    } else {
      // Alternative: Navigate to file-to-vm page and verify relationship
      await page.goto('/file_to_vm');
      
      // Check if our file-VM relationship is in the list
      const relationshipRow = page.locator(`.ant-table-row:has-text("${testFileName}"):has-text("${testVMName}")`);
      expect(await relationshipRow.count()).toBeGreaterThan(0);
    }
    
    // Step 6: Clean up - Detach file from VM
    console.log('Step 6: Detaching file from VM');
    await page.goto('/file_to_vm');
    
    // Find and click detach button for our file-VM relationship
    const relationshipRow = page.locator(`.ant-table-row:has-text("${testFileName}"):has-text("${testVMName}")`);
    await relationshipRow.locator('button:has-text("Detach"), button:has-text("Remove")').click();
    
    // Confirm detachment
    await page.locator('.ant-modal-confirm .ant-btn-primary').click();
    
    // Wait for detachment to complete
    await page.waitForResponse(response => 
      response.url().includes('/api/file_to_vm/detach') && 
      response.status() === 200
    );
    
    // Take screenshot after detachment
    await page.screenshot({ path: 'test-results/file-to-vm-9-after-detach.png' });
    
    // Step 7: Stop VM
    console.log('Step 7: Stopping VM');
    await page.goto('/vm_status');
    
    // Find VM again
    const vmRowAgain = page.locator(`.ant-table-row:has-text("${testVMName}")`);
    
    // Stop the VM
    await vmRowAgain.locator('button:has-text("Stop")').click();
    
    // Confirm stop operation
    await page.locator('.ant-modal-confirm .ant-btn-primary').click();
    
    // Wait for VM to be stopped
    await pollVMUntilState(page, testVMName, 'stopped', 300000, 10000);
    
    // Take final screenshot
    await page.screenshot({ path: 'test-results/file-to-vm-10-complete.png' });
  });
}); 