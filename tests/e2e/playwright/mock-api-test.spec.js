const { test, expect } = require('@playwright/test');

/**
 * Tests that use the mock API server
 * These tests should work without external dependencies
 */

test.describe('Mock API Tests', () => {
  
  // Start the mock API server before all tests
  test.beforeAll(async () => {
    // Start the mock API server using a shell command
    const { execSync } = require('child_process');
    try {
      execSync('bash /app/tests/playwright/mocks/start-mock-api.sh', {
        stdio: 'inherit'
      });
      console.log('Mock API server started');
    } catch (error) {
      console.error('Failed to start mock API server:', error);
      throw error;
    }
  });

  // Clean up the mock API server after all tests
  test.afterAll(async () => {
    // Stop the mock API server using the PID saved during startup
    const { execSync } = require('child_process');
    try {
      const pid = execSync('cat /tmp/mock-api-pid.txt').toString().trim();
      if (pid) {
        execSync(`kill ${pid}`);
        console.log(`Mock API server (PID: ${pid}) stopped`);
      }
    } catch (error) {
      console.error('Failed to stop mock API server:', error);
    }
  });

  test('should connect to the mock API health endpoint', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/v1/health/');
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.status).toBe('ok');
    expect(data.version).toBeDefined();
  });

  test('should retrieve VM list from mock API', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/v1/vms/');
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(Array.isArray(data)).toBe(true);
    expect(data.length).toBeGreaterThan(0);
    
    // Verify VM data structure
    const vm = data[0];
    expect(vm.id).toBeDefined();
    expect(vm.name).toBeDefined();
    expect(vm.status).toBeDefined();
  });

  test('should create a new VM through mock API', async ({ request }) => {
    const newVm = {
      name: 'Test VM Created During Test',
      os: 'ubuntu'
    };

    const response = await request.post('http://localhost:8000/api/v1/vms/', {
      data: newVm
    });
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.id).toBeDefined();
    expect(data.name).toBe(newVm.name);
    expect(data.status).toBe('created');
    expect(data.os).toBe(newVm.os);
    expect(data.created_at).toBeDefined();
  });

  test('should upload a file through mock API', async ({ request }) => {
    const newFile = {
      name: 'test-upload.txt',
      size: 1024,
      mime_type: 'text/plain'
    };

    const response = await request.post('http://localhost:8000/api/v1/files/upload/', {
      data: newFile
    });
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.id).toBeDefined();
    expect(data.name).toBe(newFile.name);
    expect(data.size).toBe(newFile.size);
    expect(data.mime_type).toBe(newFile.mime_type);
    expect(data.created_at).toBeDefined();
  });

  test('should authenticate with mock API', async ({ request }) => {
    const credentials = {
      username: 'testuser',
      password: 'password'
    };

    const response = await request.post('http://localhost:8000/api/v1/auth/login/', {
      data: credentials
    });
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.token).toBeDefined();
    expect(data.user_id).toBeDefined();
  });

  test('should retrieve system information from mock API', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/v1/system/info/');
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.os).toBeDefined();
    expect(data.version).toBeDefined();
    expect(data.cpu_cores).toBeDefined();
    expect(data.memory_total).toBeDefined();
  });
}); 