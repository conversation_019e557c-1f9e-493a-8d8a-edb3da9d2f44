/**
 * Mock API server for Playwright tests
 * Used when the real API server isn't available
 */

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const app = express();
const port = 8000;

// Enable CORS and JSON body parsing
app.use(cors());
app.use(bodyParser.json());

// Mock data
const mockData = {
  vms: [
    { id: 1, name: 'Test VM 1', status: 'running', os: 'ubuntu', created_at: '2023-01-01' },
    { id: 2, name: 'Test VM 2', status: 'stopped', os: 'centos', created_at: '2023-01-02' },
  ],
  files: [
    { id: 1, name: 'test-file-1.txt', size: 1024, mime_type: 'text/plain', created_at: '2023-01-01' },
    { id: 2, name: 'test-file-2.jpg', size: 2048, mime_type: 'image/jpeg', created_at: '2023-01-02' },
  ],
  users: [
    { id: 1, username: 'testuser', email: '<EMAIL>' }
  ]
};

// Health endpoint
app.get('/api/v1/health/', (req, res) => {
  res.json({ status: 'ok', version: '1.0.0' });
});

// Version endpoint
app.get('/api/v1/version/', (req, res) => {
  res.json({ version: '1.0.0', build: '2023.1.1' });
});

// Auth endpoints
app.post('/api/v1/auth/login/', (req, res) => {
  res.json({ token: 'mock-jwt-token', user_id: 1 });
});

app.post('/api/v1/auth/test-token/', (req, res) => {
  res.json({ token: 'mock-jwt-token', user_id: 1 });
});

app.get('/api/v1/auth/user/', (req, res) => {
  res.json(mockData.users[0]);
});

app.post('/api/v1/auth/logout/', (req, res) => {
  res.json({ success: true });
});

// VM endpoints
app.get('/api/v1/vms/', (req, res) => {
  res.json(mockData.vms);
});

app.post('/api/v1/vms/', (req, res) => {
  const newVm = {
    id: mockData.vms.length + 1,
    name: req.body.name || 'New VM',
    status: 'created',
    os: req.body.os || 'ubuntu',
    created_at: new Date().toISOString()
  };
  mockData.vms.push(newVm);
  res.json(newVm);
});

app.get('/api/v1/vms/:id/', (req, res) => {
  const vm = mockData.vms.find(v => v.id === parseInt(req.params.id));
  if (vm) {
    res.json(vm);
  } else {
    res.status(404).json({ error: 'VM not found' });
  }
});

app.post('/api/v1/vms/:id/start/', (req, res) => {
  const vm = mockData.vms.find(v => v.id === parseInt(req.params.id));
  if (vm) {
    vm.status = 'running';
    res.json(vm);
  } else {
    res.status(404).json({ error: 'VM not found' });
  }
});

app.post('/api/v1/vms/:id/stop/', (req, res) => {
  const vm = mockData.vms.find(v => v.id === parseInt(req.params.id));
  if (vm) {
    vm.status = 'stopped';
    res.json(vm);
  } else {
    res.status(404).json({ error: 'VM not found' });
  }
});

app.delete('/api/v1/vms/:id/', (req, res) => {
  const index = mockData.vms.findIndex(v => v.id === parseInt(req.params.id));
  if (index !== -1) {
    mockData.vms.splice(index, 1);
    res.json({ success: true });
  } else {
    res.status(404).json({ error: 'VM not found' });
  }
});

// File endpoints
app.get('/api/v1/files/', (req, res) => {
  res.json(mockData.files);
});

app.post('/api/v1/files/upload/', (req, res) => {
  const newFile = {
    id: mockData.files.length + 1,
    name: req.body.name || 'uploaded-file.txt',
    size: req.body.size || 1024,
    mime_type: req.body.mime_type || 'text/plain',
    created_at: new Date().toISOString()
  };
  mockData.files.push(newFile);
  res.json(newFile);
});

// System settings
app.get('/api/v1/system/info/', (req, res) => {
  res.json({
    os: 'Linux',
    version: '5.4.0',
    cpu_cores: 4,
    memory_total: 8192,
    memory_used: 4096
  });
});

app.get('/api/v1/system/status/', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: 86400,
    active_connections: 5
  });
});

app.get('/api/v1/system/settings/', (req, res) => {
  res.json({
    max_upload_size: 104857600,
    max_vms_per_user: 5,
    enable_notifications: true
  });
});

// Start the server
const server = app.listen(port, '0.0.0.0', () => {
  console.log(`Mock API server running at http://0.0.0.0:${port}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM signal received: closing HTTP server');
  server.close(() => {
    console.log('HTTP server closed');
  });
});

// Export for testing
module.exports = { app, server }; 