#!/bin/bash

# Script to start the mock API server for tests

# Install required dependencies if they don't exist
if ! npm list | grep -q express; then
  echo "Installing required dependencies..."
  npm install --no-save express cors body-parser
fi

# Start the mock API server
echo "Starting mock API server on port 8000..."
node $(dirname "$0")/mock-api.js &
PID=$!

# Save the PID for later cleanup
echo $PID > /tmp/mock-api-pid.txt

echo "Mock API server started with PID $PID"
echo "Server will be available at http://localhost:8000/api/v1/health/"

# Add a small delay to ensure the server is up
sleep 2

# Check if the server is responding
if curl -s http://localhost:8000/api/v1/health/ | grep -q "ok"; then
  echo "Mock API server is running correctly!"
else
  echo "Warning: Mock API server may not be running correctly."
fi 