// @ts-check
const { devices } = require('@playwright/test');
const { testEnv } = require('./config/test-environment');

/**
 * @see https://playwright.dev/docs/test-configuration
 */
const config = {
  testDir: './tests/playwright',
  /* Maximum time one test can run for */
  timeout: testEnv.timeouts.defaultTest,
  /* Fail the build on CI if there are test failures */
  forbidOnly: !!process.env.CI,
  /* Limited retries for stability (max 5 retries) */
  retries: process.env.CI ? 2 : 1,
  /* Opt out of parallel tests */
  workers: 1,
  /* Reporter to use */
  reporter: [
    ['list'],
    ['html', { open: 'never', outputFolder: testEnv.artifacts.reports }],
  ],
  /* Shared settings for all tests */
  use: {
    /* Base URL for navigation */
    baseURL: testEnv.api.url,
    /* Maximum time each action can take */
    actionTimeout: testEnv.timeouts.uiInteraction,
    /* Navigation timeout */
    navigationTimeout: testEnv.timeouts.apiRequest,
    /* Collect trace when retrying a failed test */
    trace: 'on-first-retry',
    /* Take screenshot on test failure */
    screenshot: 'only-on-failure',
    /* Video recording for failed tests */
    video: 'on-first-retry',
    /* Browser configuration */
    headless: testEnv.browser.headless,
    slowMo: testEnv.browser.slowMo,
    /* Use built-in test pages rather than starting a new server */
    launchOptions: {
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    },
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
      },
    },
  ],

  /* Path to the global setup file */
  globalSetup: './tests/playwright/global-setup.js',

  /* Generate a unique test-output directory for each test run */
  outputDir: `${testEnv.artifacts.reports}/${new Date().toISOString().replace(/:/g, '-')}`,
};

module.exports = config; 