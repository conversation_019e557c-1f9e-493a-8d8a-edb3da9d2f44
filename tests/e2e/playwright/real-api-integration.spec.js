/**
 * Real API Integration Tests for TurdParty
 * 
 * These tests use the actual TurdParty API service running in Docker containers
 * instead of mock implementations. This ensures tests validate real functionality.
 * 
 * Prerequisites:
 * - Docker and docker-compose available
 * - TurdParty services running via docker-compose up
 * - Real database, MinIO, and Elasticsearch services
 */

const { test, expect } = require('@playwright/test');
const { execSync } = require('child_process');

/**
 * Real API Integration Test Suite
 * 
 * Tests the complete TurdParty API stack with real services:
 * - PostgreSQL database persistence
 * - MinIO file storage
 * - Elasticsearch logging
 * - Real VM operations
 */
test.describe('Real API Integration Tests', () => {
  
  let apiBaseUrl;
  let servicesReady = false;
  
  test.beforeAll(async () => {
    // Use real API service URL
    apiBaseUrl = process.env.API_BASE_URL || 'http://localhost:8000';
    
    console.log('🚀 Starting real TurdParty API services...');
    
    try {
      // Start the real API services using docker-compose
      console.log('📦 Starting TurdParty services with docker-compose...');
      execSync('docker-compose up -d api database redis elasticsearch storage', {
        stdio: 'inherit',
        timeout: 120000 // 2 minutes timeout
      });
      
      // Wait for services to be ready
      console.log('⏳ Waiting for services to be ready...');
      await waitForServices(apiBaseUrl);
      
      servicesReady = true;
      console.log('✅ Real API services are ready for testing');
      
    } catch (error) {
      console.error('❌ Failed to start real API services:', error);
      throw new Error('Cannot run real API tests without services');
    }
  });

  test.afterAll(async () => {
    if (servicesReady) {
      console.log('🧹 Cleaning up test data and services...');
      try {
        // Clean up test data but keep services running for other tests
        await cleanupTestData(apiBaseUrl);
        console.log('✅ Test cleanup completed');
      } catch (error) {
        console.warn('⚠️ Test cleanup failed:', error);
      }
    }
  });

  test('should connect to real API health endpoint', async ({ request }) => {
    test.skip(!servicesReady, 'Real API services not available');
    
    const response = await request.get(`${apiBaseUrl}/health/`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.status).toBe('healthy');
    expect(data.version).toBeDefined();
    expect(data.timestamp).toBeDefined();
    
    console.log('✅ Real API health check passed');
  });

  test('should retrieve real VM list from API', async ({ request }) => {
    test.skip(!servicesReady, 'Real API services not available');
    
    const response = await request.get(`${apiBaseUrl}/api/v1/vms/`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(Array.isArray(data)).toBe(true);
    
    // Real API may have no VMs initially - that's valid
    console.log(`✅ Retrieved ${data.length} VMs from real API`);
  });

  test('should create real VM using API', async ({ request }) => {
    test.skip(!servicesReady, 'Real API services not available');
    
    const vmData = {
      name: `playwright-test-vm-${Date.now()}`,
      template: 'ubuntu:20.04',
      vm_type: 'docker',
      memory_mb: 512,
      cpus: 1,
      domain: 'TurdParty',
      description: 'Real integration test VM'
    };

    const response = await request.post(`${apiBaseUrl}/api/v1/vms/`, {
      data: vmData
    });
    
    expect(response.status()).toBe(201);
    
    const createdVM = await response.json();
    expect(createdVM.vm_id).toBeDefined();
    expect(createdVM.name).toBe(vmData.name);
    expect(createdVM.status).toBeDefined();
    
    console.log(`✅ Created real VM: ${createdVM.vm_id}`);
    
    // Store VM ID for cleanup
    test.info().annotations.push({ type: 'vm_id', description: createdVM.vm_id });
  });

  test('should upload real file to MinIO storage', async ({ request }) => {
    test.skip(!servicesReady, 'Real API services not available');
    
    // Create a real test file
    const testFileContent = Buffer.from('#!/bin/bash\necho "Real TurdParty test file"\ndate\n');
    const fileName = `real-test-${Date.now()}.sh`;
    
    const formData = new FormData();
    formData.append('file', new Blob([testFileContent], { type: 'application/x-sh' }), fileName);
    formData.append('description', 'Real integration test file');
    
    const response = await request.post(`${apiBaseUrl}/api/v1/files/upload/`, {
      multipart: {
        file: {
          name: fileName,
          mimeType: 'application/x-sh',
          buffer: testFileContent
        },
        description: 'Real integration test file'
      }
    });
    
    expect(response.status()).toBe(201);
    
    const uploadResult = await response.json();
    expect(uploadResult.file_id).toBeDefined();
    expect(uploadResult.filename).toBe(fileName);
    expect(uploadResult.storage_url).toContain('minio');
    
    console.log(`✅ Uploaded real file to MinIO: ${uploadResult.file_id}`);
    
    // Store file ID for cleanup
    test.info().annotations.push({ type: 'file_id', description: uploadResult.file_id });
  });

  test('should perform real file injection workflow', async ({ request }) => {
    test.skip(!servicesReady, 'Real API services not available');
    
    // First create a VM
    const vmResponse = await request.post(`${apiBaseUrl}/api/v1/vms/`, {
      data: {
        name: `injection-test-vm-${Date.now()}`,
        template: 'ubuntu:20.04',
        vm_type: 'docker',
        memory_mb: 256,
        cpus: 1,
        domain: 'TurdParty'
      }
    });
    
    expect(vmResponse.status()).toBe(201);
    const vm = await vmResponse.json();
    
    // Upload a test file
    const testScript = Buffer.from('#!/bin/bash\necho "Injected script executed"\nps aux\n');
    const uploadResponse = await request.post(`${apiBaseUrl}/api/v1/files/upload/`, {
      multipart: {
        file: {
          name: 'injection-test.sh',
          mimeType: 'application/x-sh',
          buffer: testScript
        }
      }
    });
    
    expect(uploadResponse.status()).toBe(201);
    const file = await uploadResponse.json();
    
    // Perform real file injection
    const injectionResponse = await request.post(`${apiBaseUrl}/api/v1/files/inject/`, {
      data: {
        file_id: file.file_id,
        vm_id: vm.vm_id,
        target_path: '/tmp/injected-script.sh',
        permissions: '755',
        description: 'Real injection test'
      }
    });
    
    expect(injectionResponse.status()).toBe(201);
    const injection = await injectionResponse.json();
    
    expect(injection.id).toBeDefined();
    expect(injection.status).toBe('pending');
    
    console.log(`✅ Real file injection initiated: ${injection.id}`);
    
    // Store IDs for cleanup
    test.info().annotations.push({ type: 'vm_id', description: vm.vm_id });
    test.info().annotations.push({ type: 'file_id', description: file.file_id });
    test.info().annotations.push({ type: 'injection_id', description: injection.id });
  });

  test('should generate real ECS events in Elasticsearch', async ({ request }) => {
    test.skip(!servicesReady, 'Real API services not available');
    
    // Check Elasticsearch connectivity
    const esResponse = await request.get(`${apiBaseUrl}/api/v1/system/elasticsearch/health/`);
    expect(esResponse.status()).toBe(200);
    
    // Trigger a workflow that generates real ECS events
    const workflowResponse = await request.post(`${apiBaseUrl}/api/v1/workflows/start/`, {
      data: {
        workflow_type: 'file_analysis',
        description: 'Real ECS event generation test'
      }
    });
    
    expect(workflowResponse.status()).toBe(201);
    const workflow = await workflowResponse.json();
    
    expect(workflow.workflow_id).toBeDefined();
    expect(workflow.status).toBe('started');
    
    console.log(`✅ Real workflow started: ${workflow.workflow_id}`);
    
    // Wait a moment for events to be generated
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Verify ECS events were created
    const eventsResponse = await request.get(`${apiBaseUrl}/api/v1/events/search/?workflow_id=${workflow.workflow_id}`);
    expect(eventsResponse.status()).toBe(200);
    
    const events = await eventsResponse.json();
    expect(events.total).toBeGreaterThan(0);
    expect(events.events[0]).toHaveProperty('@timestamp');
    expect(events.events[0]).toHaveProperty('ecs');
    
    console.log(`✅ Generated ${events.total} real ECS events`);
  });

});

/**
 * Wait for all required services to be ready
 */
async function waitForServices(baseUrl, maxAttempts = 30) {
  const services = [
    { name: 'API', url: `${baseUrl}/health/` },
    { name: 'Database', url: `${baseUrl}/api/v1/system/database/health/` },
    { name: 'MinIO', url: `${baseUrl}/api/v1/system/storage/health/` },
    { name: 'Elasticsearch', url: `${baseUrl}/api/v1/system/elasticsearch/health/` }
  ];
  
  for (const service of services) {
    console.log(`⏳ Waiting for ${service.name}...`);
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await fetch(service.url);
        if (response.ok) {
          console.log(`✅ ${service.name} is ready`);
          break;
        }
      } catch (error) {
        // Service not ready yet
      }
      
      if (attempt === maxAttempts) {
        throw new Error(`${service.name} failed to become ready after ${maxAttempts} attempts`);
      }
      
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
}

/**
 * Clean up test data created during tests
 */
async function cleanupTestData(baseUrl) {
  // This would implement cleanup of test VMs, files, etc.
  // For now, just log that cleanup would happen
  console.log('🧹 Test data cleanup would be implemented here');
  
  // In a real implementation, this would:
  // 1. Delete test VMs created during tests
  // 2. Remove test files from MinIO
  // 3. Clean up test database records
  // 4. Remove test ECS events (if needed)
}
