#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}==================================================${NC}"
echo -e "${YELLOW}    Running API Integration Tests                  ${NC}"
echo -e "${YELLOW}==================================================${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  echo -e "${RED}Error: Docker is not running.${NC}"
  exit 1
fi

# Process command line arguments
TEST_FILE="tests/playwright/api-health.spec.js"
REPORTER="list"

if [ "$1" == "all" ]; then
  TEST_FILE="tests/playwright/*.spec.js"
  echo -e "${YELLOW}Running all API tests${NC}"
elif [ "$1" == "html" ]; then
  REPORTER="html"
  echo -e "${YELLOW}Using HTML reporter${NC}"
elif [ -n "$1" ]; then
  # If argument is provided and not 'all' or 'html', assume it's a specific test file
  TEST_FILE="tests/playwright/$1"
  if [ ! -f "$TEST_FILE" ]; then
    echo -e "${RED}Error: Test file $TEST_FILE does not exist.${NC}"
    exit 1
  fi
  echo -e "${YELLOW}Running specific test: $TEST_FILE${NC}"
fi

# Check if API container is running
if ! docker ps | grep -q "api-test"; then
  echo -e "${YELLOW}API container not found. Setting up API test environment...${NC}"
  
  # Create Docker network if it doesn't exist
  echo -e "${YELLOW}Creating test network...${NC}"
  docker network create test-network 2>/dev/null || true
  
  # Clean up old containers if they exist
  echo -e "${YELLOW}Cleaning up old containers...${NC}"
  docker stop api-test 2>/dev/null || true
  docker rm api-test 2>/dev/null || true
  
  # Build API test server
  echo -e "${YELLOW}Building API test server...${NC}"
  docker build -t api-test-server -f Dockerfile.simple .
  
  # Start API container
  echo -e "${YELLOW}Starting API container...${NC}"
  docker run -d -p 3055:8000 --name api-test --network test-network api-test-server
  
  # Wait for API to start
  echo -e "${YELLOW}Waiting for API to start...${NC}"
  sleep 2
  
  # Check if API is running
  if ! docker ps | grep -q "api-test"; then
    echo -e "${RED}Error: API container failed to start.${NC}"
    exit 1
  fi
fi

# Connect Playwright container to network if it exists
if docker ps | grep -q "turdparty_playwright" || docker ps | grep -q "playwright-test"; then
  echo -e "${YELLOW}Connecting Playwright container to network...${NC}"
  
  # Try both possible container names
  docker network connect test-network turdparty_playwright 2>/dev/null || true
  docker network connect test-network playwright-test 2>/dev/null || true
else
  echo -e "${YELLOW}Playwright container not found. Starting persistent test environment...${NC}"
  .dockerwrapper/persistent-test-env.sh
  docker network connect test-network playwright-test 2>/dev/null || true
fi

# Run tests
echo -e "${YELLOW}Running API tests: $TEST_FILE${NC}"
if [ "$REPORTER" == "html" ]; then
  .dockerwrapper/run-test-direct.sh "$TEST_FILE" "html"
else
  .dockerwrapper/run-test-direct.sh "$TEST_FILE"
fi

# Capture test result
TEST_RESULT=$?

# Show test result
if [ $TEST_RESULT -eq 0 ]; then
  echo -e "${GREEN}==================================================${NC}"
  echo -e "${GREEN}    API tests completed successfully!              ${NC}"
  echo -e "${GREEN}==================================================${NC}"
else
  echo -e "${RED}==================================================${NC}"
  echo -e "${RED}    API tests failed with code $TEST_RESULT            ${NC}"
  echo -e "${RED}==================================================${NC}"
fi

# Ask if user wants to clean up
read -p "Clean up API test container? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
  echo -e "${YELLOW}Cleaning up containers...${NC}"
  docker stop api-test 2>/dev/null || true
  docker rm api-test 2>/dev/null || true
  echo -e "${GREEN}Cleanup complete.${NC}"
else
  echo -e "${YELLOW}Leaving API container running.${NC}"
  echo -e "${YELLOW}You can clean up later with: docker stop api-test && docker rm api-test${NC}"
fi

exit $TEST_RESULT 