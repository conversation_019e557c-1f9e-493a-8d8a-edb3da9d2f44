#!/bin/bash

# Script to run Playwright tests locally with different options
# Usage: ./run-tests.sh [test-file] [reporter] [mode]
#   test-file: Specific test file to run (default: all tests)
#   reporter: Reporter to use (list, html, dot, line, github, json, junit)
#   mode: debug (runs with headed browser), workers=N (runs with N workers)

# Color definitions
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
TEST_FILE="tests/playwright/"
REPORTER="list,html"
MODE=""
WORKERS="1"
RETRIES="0"
DEBUG=""

# Parse arguments
if [ $# -ge 1 ]; then
  # Check if first argument is a specific test
  if [[ $1 == *".spec.js" || $1 == *".test.js" || $1 == *"/" ]]; then
    TEST_FILE="tests/playwright/$1"
    echo -e "${BLUE}Running specific test:${NC} $TEST_FILE"
  fi
  
  # Check if specific reporter was specified
  if [[ $1 == "list" || $2 == "list" || $3 == "list" ]]; then
    REPORTER="list"
  elif [[ $1 == "html" || $2 == "html" || $3 == "html" ]]; then
    REPORTER="html"
  elif [[ $1 == "dot" || $2 == "dot" || $3 == "dot" ]]; then
    REPORTER="dot"
  elif [[ $1 == "json" || $2 == "json" || $3 == "json" ]]; then
    REPORTER="json"
  elif [[ $1 == "junit" || $2 == "junit" || $3 == "junit" ]]; then
    REPORTER="junit"
  fi
  
  # Check if debug mode
  if [[ $1 == "debug" || $2 == "debug" || $3 == "debug" ]]; then
    DEBUG="--headed --debug"
    echo -e "${YELLOW}Running in debug mode with headed browser${NC}"
  fi
  
  # Check for workers
  for arg in "$@"; do
    if [[ $arg == workers=* ]]; then
      WORKERS="${arg#workers=}"
      echo -e "${BLUE}Using${NC} $WORKERS ${BLUE}workers${NC}"
    fi
  done
  
  # Check for retries
  for arg in "$@"; do
    if [[ $arg == retries=* ]]; then
      RETRIES="${arg#retries=}"
      echo -e "${BLUE}Using${NC} $RETRIES ${BLUE}retries${NC}"
    fi
  done
fi

# Check if we should run in Docker or locally
if [ -n "$(docker ps -q -f name=turdparty_playwright)" ]; then
  echo -e "${GREEN}Running tests in Docker container...${NC}"
  
  # Ensure the container is properly configured
  echo -e "${BLUE}Ensuring test container is properly configured...${NC}"
  .dockerwrapper/fix-persistent-container.sh
  
  # Run the test in Docker
  echo -e "${GREEN}Running tests: ${TEST_FILE}${NC}"
  docker exec turdparty_playwright bash -c "cd /app && npx playwright test ${TEST_FILE} --reporter=${REPORTER} --workers=${WORKERS} --retries=${RETRIES} ${DEBUG}"
  
  # Copy results if using HTML reporter
  if [[ $REPORTER == *"html"* ]]; then
    echo -e "${BLUE}Copying HTML report from container...${NC}"
    docker cp turdparty_playwright:/app/playwright-report ./
    echo -e "${GREEN}HTML report available at:${NC} ./playwright-report/index.html"
  fi

else
  echo -e "${YELLOW}No Docker container found, running tests locally...${NC}"
  
  # Check if Playwright is installed
  if ! command -v npx &> /dev/null || ! npx playwright --version &> /dev/null; then
    echo -e "${RED}Playwright not installed locally. Install it with:${NC}"
    echo "npm install -D @playwright/test"
    echo "npx playwright install"
    exit 1
  fi
  
  # Run the test locally
  echo -e "${GREEN}Running tests: ${TEST_FILE}${NC}"
  npx playwright test ${TEST_FILE} --reporter=${REPORTER} --workers=${WORKERS} --retries=${RETRIES} ${DEBUG}
  
  # Show HTML report location if using HTML reporter
  if [[ $REPORTER == *"html"* ]]; then
    echo -e "${GREEN}HTML report available at:${NC} ./playwright-report/index.html"
  fi
fi 