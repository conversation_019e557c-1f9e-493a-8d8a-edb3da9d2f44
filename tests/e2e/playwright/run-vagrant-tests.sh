#!/bin/bash
# Script to run Vagrant integration tests

set -e  # Exit on error

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Create directories for test results
echo -e "${YELLOW}Setting up test environment...${NC}"
mkdir -p test-results/html
mkdir -p test-results/artifacts
mkdir -p test-results/screenshots

# Check if Vagrant is installed
if ! command -v vagrant &> /dev/null; then
    echo -e "${RED}Error: Vagrant is not installed. Please install Vagrant before running these tests.${NC}"
    exit 1
fi

# Check if the API is running
echo -e "${YELLOW}Checking if API is running...${NC}"
API_URL="${API_URL:-http://localhost:3050}"
if ! curl -s --head "${API_URL}/api/v1/health" | grep "200 OK" > /dev/null; then
    echo -e "${RED}Error: API is not running at ${API_URL}. Please start the API server first.${NC}"
    exit 1
fi

# Check if the frontend is running
echo -e "${YELLOW}Checking if frontend is running...${NC}"
FRONTEND_URL="${FRONTEND_URL:-http://localhost:3100}"
if ! curl -s --head "${FRONTEND_URL}" | grep "200 OK" > /dev/null; then
    echo -e "${RED}Error: Frontend is not running at ${FRONTEND_URL}. Please start the frontend server first.${NC}"
    exit 1
fi

# Set environment variables
export BASE_URL="${FRONTEND_URL}"
export API_URL="${API_URL}"
export TEST_USER="${TEST_USER:-testuser}"
export TEST_PASSWORD="${TEST_PASSWORD:-password123}"

# Log test environment
echo -e "${GREEN}Test environment:${NC}"
echo -e "  Base URL: ${BASE_URL}"
echo -e "  API URL: ${API_URL}"
echo -e "  Test User: ${TEST_USER}"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}Installing dependencies...${NC}"
    npm install
fi

# Run the tests
echo -e "${GREEN}Running Vagrant integration tests...${NC}"
echo -e "${YELLOW}Note: These tests can take a long time as they're working with real VMs${NC}"
echo -e "${YELLOW}      Please be patient and do not interrupt the tests.${NC}"

# Run with custom timeout and retry options
npx playwright test --config=integration.config.js vm-management/vagrant-integration.spec.js

# Check test result
TEST_RESULT=$?

# Display test results location
echo -e "${GREEN}Test run completed. HTML report is available at:${NC}"
echo -e "  ${PWD}/test-results/html/index.html"

# Clean up any leftover VMs
echo -e "${YELLOW}Cleaning up test VMs...${NC}"
LEFTOVER_VMS=$(curl -s "${API_URL}/api/vm/list" | grep -o '"name":"test-vagrant-[0-9]\+"' | cut -d'"' -f4)

if [ -n "$LEFTOVER_VMS" ]; then
    echo -e "${YELLOW}Found leftover test VMs. Attempting to clean up:${NC}"
    for VM in $LEFTOVER_VMS; do
        echo -e "  Cleaning up VM: ${VM}"
        
        # Check if VM is running
        VM_STATUS=$(curl -s "${API_URL}/api/vm/status/${VM}" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        
        if [ "$VM_STATUS" = "running" ]; then
            echo -e "    Stopping VM: ${VM}"
            curl -s -X POST "${API_URL}/api/vm/stop/${VM}" > /dev/null
            # Wait for VM to stop
            sleep 10
        fi
        
        echo -e "    Deleting VM: ${VM}"
        curl -s -X DELETE "${API_URL}/api/vm/delete/${VM}" > /dev/null
    done
    echo -e "${GREEN}Cleanup completed.${NC}"
else
    echo -e "${GREEN}No leftover test VMs found.${NC}"
fi

echo -e "${GREEN}All tests and cleanup completed.${NC}"
exit $TEST_RESULT 