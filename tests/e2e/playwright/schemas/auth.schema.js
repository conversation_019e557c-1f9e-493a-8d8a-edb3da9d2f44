/**
 * JSON Schema definitions for authentication-related API responses
 */

// Login response schema
const loginResponseSchema = {
  type: 'object',
  required: ['token', 'user'],
  properties: {
    token: { type: 'string' },
    user: {
      type: 'object',
      required: ['username', 'id'],
      properties: {
        id: { type: 'string' },
        username: { type: 'string' },
        email: { type: 'string' },
        first_name: { type: 'string' },
        last_name: { type: 'string' },
        is_admin: { type: 'boolean' }
      }
    }
  }
};

// User info response schema
const userInfoSchema = {
  type: 'object',
  required: ['username', 'id'],
  properties: {
    id: { type: 'string' },
    username: { type: 'string' },
    email: { type: 'string' },
    first_name: { type: 'string' },
    last_name: { type: 'string' },
    is_admin: { type: 'boolean' },
    created_at: { type: 'string' },
    last_login: { type: 'string' }
  }
};

// Authentication error schema
const authErrorSchema = {
  type: 'object',
  required: ['detail'],
  properties: {
    detail: { type: 'string' }
  }
};

module.exports = {
  loginResponseSchema,
  userInfoSchema,
  authErrorSchema
}; 