/**
 * JSON Schema definitions for file management API responses
 */

// File list response schema
const fileListSchema = {
  type: 'array',
  items: {
    type: 'object',
    required: ['id', 'filename', 'size', 'created_at'],
    properties: {
      id: { type: 'string' },
      filename: { type: 'string' },
      size: { type: 'number' },
      created_at: { type: 'string' },
      updated_at: { type: 'string' },
      content_type: { type: 'string' },
      owner_id: { type: 'string' },
      path: { type: 'string' },
      tags: {
        type: 'array',
        items: { type: 'string' }
      }
    }
  }
};

// File detail schema
const fileDetailSchema = {
  type: 'object',
  required: ['id', 'filename', 'size', 'created_at'],
  properties: {
    id: { type: 'string' },
    filename: { type: 'string' },
    size: { type: 'number' },
    created_at: { type: 'string' },
    updated_at: { type: 'string' },
    content_type: { type: 'string' },
    owner_id: { type: 'string' },
    path: { type: 'string' },
    tags: {
      type: 'array',
      items: { type: 'string' }
    },
    permissions: {
      type: 'object',
      properties: {
        read: { type: 'boolean' },
        write: { type: 'boolean' },
        delete: { type: 'boolean' }
      }
    }
  }
};

// File upload response schema
const fileUploadSchema = {
  type: 'object',
  required: ['id', 'filename', 'size'],
  properties: {
    id: { type: 'string' },
    filename: { type: 'string' },
    size: { type: 'number' },
    content_type: { type: 'string' },
    path: { type: 'string' },
    created_at: { type: 'string' }
  }
};

// File operation error schema
const fileErrorSchema = {
  type: 'object',
  required: ['detail'],
  properties: {
    detail: { type: 'string' },
    code: { type: 'string' }
  }
};

module.exports = {
  fileListSchema,
  fileDetailSchema,
  fileUploadSchema,
  fileErrorSchema
}; 