// @ts-check
const { test, expect } = require('@playwright/test');
const { testEnv, apiUrl, logEnvironment } = require('./config/test-environment');

test.describe('System Information and Settings API Tests', () => {
  // Log environment info before all tests
  test.beforeAll(() => {
    logEnvironment();
    console.log('Testing system information and settings endpoints');
  });

  // Test system information endpoint
  test('should retrieve system information', async ({ request }) => {
    console.log('Testing system information endpoint...');
    
    // Prepare request options
    const options = {
      maxRedirects: 5
    };
    
    // Add auth token if available
    if (process.env.TEST_AUTH_TOKEN) {
      options.headers = {
        'Authorization': `Bearer ${process.env.TEST_AUTH_TOKEN}`
      };
    }
    
    // System info request
    const response = await request.get(`${testEnv.api.url}${testEnv.api.prefix}/system/info/`, options);
    
    console.log(`System Info Response Status: ${response.status()}`);
    
    // 200 means successful info retrieval
    // 401 means unauthorized but endpoint exists
    // 404 means endpoint doesn't exist in test environment
    expect([200, 401, 404]).toContain(response.status());
    
    // If successful, verify response structure
    if (response.status() === 200) {
      const body = await response.json();
      console.log('System info retrieved successfully');
      expect(body).toHaveProperty('hostname');
      expect(body).toHaveProperty('cpu_count');
      expect(body).toHaveProperty('memory_total');
    }
  });

  // Test getting system status
  test('should retrieve system status', async ({ request }) => {
    console.log('Testing system status endpoint...');
    
    // Prepare request options
    const options = {
      maxRedirects: 5
    };
    
    // Add auth token if available
    if (process.env.TEST_AUTH_TOKEN) {
      options.headers = {
        'Authorization': `Bearer ${process.env.TEST_AUTH_TOKEN}`
      };
    }
    
    // System status request
    const response = await request.get(`${testEnv.api.url}${testEnv.api.prefix}/system/status/`, options);
    
    console.log(`System Status Response Status: ${response.status()}`);
    
    // 200 means successful status retrieval
    // 401 means unauthorized but endpoint exists
    // 404 means endpoint doesn't exist in test environment
    expect([200, 401, 404]).toContain(response.status());
    
    // If successful, verify response structure
    if (response.status() === 200) {
      const body = await response.json();
      console.log('System status retrieved successfully');
      expect(body).toHaveProperty('status');
      expect(body).toHaveProperty('uptime');
    }
  });

  // Test getting application settings
  test('should retrieve application settings', async ({ request }) => {
    console.log('Testing application settings endpoint...');
    
    // Prepare request options
    const options = {
      maxRedirects: 5
    };
    
    // Add auth token if available
    if (process.env.TEST_AUTH_TOKEN) {
      options.headers = {
        'Authorization': `Bearer ${process.env.TEST_AUTH_TOKEN}`
      };
    }
    
    // Settings request
    const response = await request.get(`${testEnv.api.url}${testEnv.api.prefix}/settings/`, options);
    
    console.log(`Settings Response Status: ${response.status()}`);
    
    // 200 means successful settings retrieval
    // 401 means unauthorized but endpoint exists
    // 404 means endpoint doesn't exist in test environment
    expect([200, 401, 404]).toContain(response.status());
    
    // If successful, verify response structure
    if (response.status() === 200) {
      const body = await response.json();
      console.log('Settings retrieved successfully');
      expect(body).toHaveProperty('settings');
    }
  });

  // Test updating application settings
  test('should update application settings', async ({ request }) => {
    console.log('Testing settings update endpoint...');
    
    // Create test settings
    const testSettings = {
      settings: {
        vm_default_memory: 2048,
        vm_default_cpus: 2,
        notification_enabled: true
      }
    };
    
    // Prepare request options
    const options = {
      maxRedirects: 5,
      data: testSettings
    };
    
    // Add auth token if available
    if (process.env.TEST_AUTH_TOKEN) {
      options.headers = {
        'Authorization': `Bearer ${process.env.TEST_AUTH_TOKEN}`
      };
    }
    
    // Settings update request
    const response = await request.put(`${testEnv.api.url}${testEnv.api.prefix}/settings/`, options);
    
    console.log(`Settings Update Response Status: ${response.status()}`);
    
    // 200 means successful update
    // 401 means unauthorized but endpoint exists
    // 404 means endpoint doesn't exist in test environment
    expect([200, 401, 404]).toContain(response.status());
    
    // If successful, verify response structure
    if (response.status() === 200) {
      const body = await response.json();
      console.log('Settings updated successfully');
      expect(body).toHaveProperty('settings');
      expect(body.settings).toHaveProperty('vm_default_memory');
      expect(body.settings.vm_default_memory).toBe(testSettings.settings.vm_default_memory);
    }
  });

  // Test system logs endpoint
  test('should retrieve system logs', async ({ request }) => {
    console.log('Testing system logs endpoint...');
    
    // Prepare request options
    const options = {
      maxRedirects: 5
    };
    
    // Add auth token if available
    if (process.env.TEST_AUTH_TOKEN) {
      options.headers = {
        'Authorization': `Bearer ${process.env.TEST_AUTH_TOKEN}`
      };
    }
    
    // System logs request
    const response = await request.get(`${testEnv.api.url}${testEnv.api.prefix}/system/logs/`, options);
    
    console.log(`System Logs Response Status: ${response.status()}`);
    
    // 200 means successful logs retrieval
    // 401 means unauthorized but endpoint exists
    // 404 means endpoint doesn't exist in test environment
    expect([200, 401, 404]).toContain(response.status());
    
    // If successful, verify response structure
    if (response.status() === 200) {
      const body = await response.json();
      console.log('System logs retrieved successfully');
      expect(body).toHaveProperty('logs');
      expect(Array.isArray(body.logs)).toBeTruthy();
    }
  });
}); 