/**
 * Playwright tests for Inspektor Gadget Analysis Dashboard
 * 
 * Tests the complete UI workflow for:
 * - Starting binary analysis
 * - Monitoring analysis progress
 * - Viewing analysis results
 * - Navigating to Kibana dashboards
 */

const { test, expect } = require('@playwright/test');
const { v4: uuidv4 } = require('uuid');

test.describe('Inspektor Gadget Analysis Dashboard', () => {
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the analysis page
    await page.goto('/analysis');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('should display analysis dashboard with all required elements', async ({ page }) => {
    // Check for main dashboard elements
    await expect(page.locator('h1')).toContainText('Binary Analysis Dashboard');
    
    // Check for start analysis button
    await expect(page.locator('[data-testid="start-analysis-btn"]')).toBeVisible();
    
    // Check for analysis history table
    await expect(page.locator('[data-testid="analysis-history-table"]')).toBeVisible();
    
    // Check for VM template selector
    await expect(page.locator('[data-testid="vm-template-selector"]')).toBeVisible();
    
    // Check for gadget selection
    await expect(page.locator('[data-testid="gadget-selection"]')).toBeVisible();
  });

  test('should start new analysis with file upload', async ({ page }) => {
    // Click start analysis button
    await page.click('[data-testid="start-analysis-btn"]');
    
    // Wait for analysis modal to open
    await expect(page.locator('[data-testid="analysis-modal"]')).toBeVisible();
    
    // Upload a test file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles('./tests/fixtures/test_binary.sh');
    
    // Select VM template
    await page.selectOption('[data-testid="vm-template-select"]', 'ubuntu-22.04');
    
    // Select gadgets
    await page.check('[data-testid="gadget-trace_exec"]');
    await page.check('[data-testid="gadget-trace_tcp"]');
    await page.check('[data-testid="gadget-trace_open"]');
    
    // Set timeout
    await page.fill('[data-testid="timeout-input"]', '300');
    
    // Start analysis
    await page.click('[data-testid="start-analysis-submit"]');
    
    // Wait for success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Analysis started successfully');
    
    // Check that analysis appears in history
    await expect(page.locator('[data-testid="analysis-history-table"] tbody tr')).toHaveCountGreaterThan(0);
  });

  test('should monitor analysis progress in real-time', async ({ page }) => {
    // Start an analysis first (mock or use existing)
    const analysisId = 'test-analysis-' + uuidv4();
    
    // Mock analysis status endpoint
    await page.route(`/api/v1/analysis/${analysisId}/status`, async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          analysis_id: analysisId,
          status: 'running',
          progress: 65,
          events_captured: 1247,
          vm_status: 'running',
          data_stream_status: 'active',
          started_at: new Date().toISOString(),
          estimated_completion: new Date(Date.now() + 120000).toISOString()
        })
      });
    });
    
    // Navigate to analysis detail page
    await page.goto(`/analysis/${analysisId}`);
    
    // Check progress indicators
    await expect(page.locator('[data-testid="analysis-status"]')).toContainText('running');
    await expect(page.locator('[data-testid="progress-bar"]')).toBeVisible();
    await expect(page.locator('[data-testid="progress-percentage"]')).toContainText('65%');
    
    // Check event counter
    await expect(page.locator('[data-testid="events-captured"]')).toContainText('1,247');
    
    // Check VM status
    await expect(page.locator('[data-testid="vm-status"]')).toContainText('running');
    
    // Check data stream status
    await expect(page.locator('[data-testid="data-stream-status"]')).toContainText('active');
    
    // Check estimated completion time
    await expect(page.locator('[data-testid="estimated-completion"]')).toBeVisible();
  });

  test('should display analysis results when completed', async ({ page }) => {
    const analysisId = 'completed-analysis-' + uuidv4();
    
    // Mock completed analysis results
    await page.route(`/api/v1/analysis/${analysisId}/results`, async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          analysis_id: analysisId,
          file_uuid: uuidv4(),
          vm_template: 'ubuntu-22.04',
          status: 'completed',
          summary: {
            total_events: 5000,
            process_events: 150,
            network_events: 300,
            file_events: 800,
            security_events: 25
          },
          elasticsearch_indices: ['inspektor-gadget-2025.01.21'],
          kibana_dashboard_url: `https://kibana.turdparty.localhost/app/dashboards#/view/${analysisId}`,
          started_at: new Date(Date.now() - 300000).toISOString(),
          completed_at: new Date().toISOString(),
          total_duration: 300
        })
      });
    });
    
    // Navigate to results page
    await page.goto(`/analysis/${analysisId}/results`);
    
    // Check summary statistics
    await expect(page.locator('[data-testid="total-events"]')).toContainText('5,000');
    await expect(page.locator('[data-testid="process-events"]')).toContainText('150');
    await expect(page.locator('[data-testid="network-events"]')).toContainText('300');
    await expect(page.locator('[data-testid="file-events"]')).toContainText('800');
    await expect(page.locator('[data-testid="security-events"]')).toContainText('25');
    
    // Check duration
    await expect(page.locator('[data-testid="analysis-duration"]')).toContainText('5 minutes');
    
    // Check Kibana dashboard link
    const kibanaLink = page.locator('[data-testid="kibana-dashboard-link"]');
    await expect(kibanaLink).toBeVisible();
    await expect(kibanaLink).toHaveAttribute('href', /kibana\.turdparty\.localhost/);
    
    // Check Elasticsearch indices
    await expect(page.locator('[data-testid="elasticsearch-indices"]')).toContainText('inspektor-gadget-2025.01.21');
  });

  test('should navigate to Kibana dashboard', async ({ page }) => {
    const analysisId = 'kibana-test-' + uuidv4();
    const kibanaUrl = `https://kibana.turdparty.localhost/app/dashboards#/view/${analysisId}`;
    
    // Mock the results with Kibana URL
    await page.route(`/api/v1/analysis/${analysisId}/results`, async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          analysis_id: analysisId,
          kibana_dashboard_url: kibanaUrl,
          status: 'completed',
          summary: { total_events: 1000 }
        })
      });
    });
    
    await page.goto(`/analysis/${analysisId}/results`);
    
    // Click Kibana dashboard link
    const [newPage] = await Promise.all([
      page.waitForEvent('popup'),
      page.click('[data-testid="kibana-dashboard-link"]')
    ]);
    
    // Verify new page opened with correct URL
    await expect(newPage).toHaveURL(kibanaUrl);
  });

  test('should handle analysis errors gracefully', async ({ page }) => {
    const analysisId = 'error-analysis-' + uuidv4();
    
    // Mock failed analysis
    await page.route(`/api/v1/analysis/${analysisId}/status`, async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          analysis_id: analysisId,
          status: 'failed',
          progress: 45,
          events_captured: 500,
          vm_status: 'destroyed',
          data_stream_status: 'inactive',
          error_message: 'VM provisioning failed: Insufficient resources'
        })
      });
    });
    
    await page.goto(`/analysis/${analysisId}`);
    
    // Check error status
    await expect(page.locator('[data-testid="analysis-status"]')).toContainText('failed');
    
    // Check error message
    await expect(page.locator('[data-testid="error-message"]')).toContainText('VM provisioning failed');
    
    // Check that retry button is available
    await expect(page.locator('[data-testid="retry-analysis-btn"]')).toBeVisible();
  });

  test('should stop running analysis', async ({ page }) => {
    const analysisId = 'stop-test-' + uuidv4();
    
    // Mock running analysis
    await page.route(`/api/v1/analysis/${analysisId}/status`, async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          analysis_id: analysisId,
          status: 'running',
          progress: 30,
          events_captured: 200,
          vm_status: 'running',
          data_stream_status: 'active'
        })
      });
    });
    
    // Mock stop endpoint
    await page.route(`/api/v1/analysis/${analysisId}`, async route => {
      if (route.request().method() === 'DELETE') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            message: `Analysis ${analysisId} stopped successfully`
          })
        });
      }
    });
    
    await page.goto(`/analysis/${analysisId}`);
    
    // Click stop button
    await page.click('[data-testid="stop-analysis-btn"]');
    
    // Confirm in dialog
    await page.click('[data-testid="confirm-stop-btn"]');
    
    // Check success message
    await expect(page.locator('[data-testid="success-message"]')).toContainText('stopped successfully');
  });

  test('should filter and search analysis history', async ({ page }) => {
    // Mock analysis list
    await page.route('/api/v1/analysis/', async route => {
      const url = new URL(route.request().url());
      const status = url.searchParams.get('status');
      
      let analyses = [
        {
          analysis_id: 'analysis-1',
          status: 'completed',
          vm_template: 'ubuntu-22.04',
          started_at: new Date(Date.now() - 3600000).toISOString(),
          summary: { total_events: 1000 }
        },
        {
          analysis_id: 'analysis-2', 
          status: 'running',
          vm_template: 'centos-stream-9',
          started_at: new Date(Date.now() - 1800000).toISOString(),
          summary: { total_events: 500 }
        },
        {
          analysis_id: 'analysis-3',
          status: 'failed',
          vm_template: 'debian-12',
          started_at: new Date(Date.now() - 7200000).toISOString(),
          summary: { total_events: 0 }
        }
      ];
      
      if (status) {
        analyses = analyses.filter(a => a.status === status);
      }
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(analyses)
      });
    });
    
    await page.goto('/analysis');
    
    // Test status filter
    await page.selectOption('[data-testid="status-filter"]', 'completed');
    await page.waitForTimeout(500);
    
    // Should only show completed analyses
    const rows = page.locator('[data-testid="analysis-history-table"] tbody tr');
    await expect(rows).toHaveCount(1);
    await expect(rows.first().locator('[data-testid="status-cell"]')).toContainText('completed');
    
    // Test search functionality
    await page.fill('[data-testid="search-input"]', 'analysis-2');
    await page.waitForTimeout(500);
    
    // Should filter to matching analysis
    await expect(rows).toHaveCount(1);
    await expect(rows.first().locator('[data-testid="id-cell"]')).toContainText('analysis-2');
  });

  test('should export analysis data', async ({ page }) => {
    const analysisId = 'export-test-' + uuidv4();
    
    await page.goto(`/analysis/${analysisId}/results`);
    
    // Test JSON export
    const [download] = await Promise.all([
      page.waitForEvent('download'),
      page.click('[data-testid="export-json-btn"]')
    ]);
    
    expect(download.suggestedFilename()).toMatch(/analysis-.*\.json$/);
    
    // Test CSV export
    const [csvDownload] = await Promise.all([
      page.waitForEvent('download'),
      page.click('[data-testid="export-csv-btn"]')
    ]);
    
    expect(csvDownload.suggestedFilename()).toMatch(/analysis-.*\.csv$/);
  });

  test('should display real-time event stream', async ({ page }) => {
    const analysisId = 'stream-test-' + uuidv4();
    
    // Mock WebSocket connection for real-time events
    await page.addInitScript(() => {
      window.mockWebSocket = true;
    });
    
    await page.goto(`/analysis/${analysisId}/live`);
    
    // Check that event stream container is visible
    await expect(page.locator('[data-testid="event-stream"]')).toBeVisible();
    
    // Check event counters
    await expect(page.locator('[data-testid="process-event-counter"]')).toBeVisible();
    await expect(page.locator('[data-testid="network-event-counter"]')).toBeVisible();
    await expect(page.locator('[data-testid="file-event-counter"]')).toBeVisible();
    await expect(page.locator('[data-testid="security-event-counter"]')).toBeVisible();
    
    // Check event filtering controls
    await expect(page.locator('[data-testid="event-type-filter"]')).toBeVisible();
    await expect(page.locator('[data-testid="severity-filter"]')).toBeVisible();
  });

});
