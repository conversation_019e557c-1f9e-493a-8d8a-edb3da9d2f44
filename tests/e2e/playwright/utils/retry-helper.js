/**
 * Retry Helper Utility
 * Provides functions for limiting retries in Playwright tests.
 */

const MAX_RETRIES = 5;

/**
 * Executes a function with retry logic, limiting to a maximum number of attempts
 * @param {Function} fn - The async function to execute
 * @param {Object} options - Options for controlling retry behavior
 * @param {number} [options.maxRetries=5] - Maximum number of retry attempts
 * @param {number} [options.delayMs=1000] - Delay between retries in milliseconds
 * @param {Function} [options.onRetry] - Function to call before each retry attempt
 * @param {boolean} [options.throwOnMaxRetries=true] - Whether to throw an error when max retries is reached
 * @returns {Promise<any>} - The result of the function
 */
async function withRetry(fn, options = {}) {
  const { 
    maxRetries = MAX_RETRIES, 
    delayMs = 1000, 
    onRetry = null,
    throwOnMaxRetries = true
  } = options;
  
  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // Log the error with attempt number
      console.log(`Attempt ${attempt + 1}/${maxRetries + 1} failed:`, error.message);
      
      // If we've reached max retries, either throw or return null
      if (attempt >= maxRetries) {
        if (throwOnMaxRetries) {
          console.log(`Max retries (${maxRetries}) reached. Giving up.`);
          throw new Error(`Failed after ${maxRetries + 1} attempts: ${lastError.message}`);
        } else {
          console.log(`Max retries (${maxRetries}) reached. Returning null.`);
          return null;
        }
      }
      
      // Call the onRetry callback if provided
      if (onRetry && typeof onRetry === 'function') {
        await onRetry(attempt, lastError);
      }
      
      // Wait before the next attempt
      console.log(`Waiting ${delayMs}ms before next attempt...`);
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }
}

/**
 * Creates a retryable request wrapper around the Playwright request object
 * @param {Object} request - The Playwright request object
 * @returns {Object} - A wrapped request object with retry capabilities
 */
function createRetryableRequest(request) {
  return {
    get: async (url, options = {}) => {
      const { retries = MAX_RETRIES, ...requestOptions } = options;
      return withRetry(() => request.get(url, requestOptions), { maxRetries: retries });
    },
    post: async (url, options = {}) => {
      const { retries = MAX_RETRIES, ...requestOptions } = options;
      return withRetry(() => request.post(url, requestOptions), { maxRetries: retries });
    },
    put: async (url, options = {}) => {
      const { retries = MAX_RETRIES, ...requestOptions } = options;
      return withRetry(() => request.put(url, requestOptions), { maxRetries: retries });
    },
    delete: async (url, options = {}) => {
      const { retries = MAX_RETRIES, ...requestOptions } = options;
      return withRetry(() => request.delete(url, requestOptions), { maxRetries: retries });
    },
    patch: async (url, options = {}) => {
      const { retries = MAX_RETRIES, ...requestOptions } = options;
      return withRetry(() => request.patch(url, requestOptions), { maxRetries: retries });
    },
    head: async (url, options = {}) => {
      const { retries = MAX_RETRIES, ...requestOptions } = options;
      return withRetry(() => request.head(url, requestOptions), { maxRetries: retries });
    }
  };
}

module.exports = {
  MAX_RETRIES,
  withRetry,
  createRetryableRequest
}; 