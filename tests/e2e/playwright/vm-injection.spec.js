// @ts-check
const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');
const os = require('os');

/**
 * End-to-end test for VM file injection using the UI.
 * This test:
 * 1. Uploads a file through the UI
 * 2. Creates a VM through the UI
 * 3. Injects the file into the VM
 * 4. Verifies the file injection
 * 5. Tears down the VM
 */
test('VM file injection end-to-end test', async ({ page }) => {
  // Create a test file
  const testFilePath = path.join(os.tmpdir(), `test-file-${Date.now()}.txt`);
  const testFileContent = 'This is a test file for VM injection via UI.';
  fs.writeFileSync(testFilePath, testFileContent);
  console.log(`Created test file at ${testFilePath}`);

  try {
    // Navigate to the file upload page
    await page.goto('/files/upload');
    await page.waitForLoadState('networkidle');
    
    // Upload the file
    const fileInput = await page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);
    await page.click('button:has-text("Upload")');
    
    // Wait for the upload to complete and get the file ID
    await page.waitForSelector('text=Upload successful');
    const fileIdElement = await page.locator('.file-id');
    const fileId = await fileIdElement.textContent();
    console.log(`File uploaded with ID: ${fileId}`);
    
    // Navigate to the VM management page
    await page.goto('/vagrant/vms');
    await page.waitForLoadState('networkidle');
    
    // Create a new VM
    await page.click('button:has-text("Create VM")');
    await page.fill('input[name="vm-name"]', 'test-vm');
    await page.click('button:has-text("Create")');
    
    // Wait for the VM to be created and get the VM ID
    await page.waitForSelector('text=VM created successfully');
    const vmIdElement = await page.locator('.vm-id');
    const vmId = await vmIdElement.textContent();
    console.log(`VM created with ID: ${vmId}`);
    
    // Wait for the VM to be ready (status: running)
    console.log('Waiting for VM to be ready...');
    await page.waitForSelector(`text=Status: running`, { timeout: 300000 }); // 5 minutes timeout
    console.log('VM is ready');
    
    // Navigate to the file injection page
    await page.goto('/vagrant/inject');
    await page.waitForLoadState('networkidle');
    
    // Select the VM and file
    await page.selectOption('select[name="vm-id"]', vmId);
    await page.selectOption('select[name="file-id"]', fileId);
    
    // Set the target path
    await page.fill('input[name="target-path"]', '/tmp/injection_test/test_file.txt');
    
    // Inject the file
    await page.click('button:has-text("Inject")');
    
    // Wait for the injection to complete
    await page.waitForSelector('text=File injected successfully');
    console.log('File injected successfully');
    
    // Verify the file injection
    await page.goto(`/vagrant/vms/${vmId}`);
    await page.waitForLoadState('networkidle');
    
    // Execute a command to check the file
    await page.fill('input[name="command"]', 'cat /tmp/injection_test/test_file.txt');
    await page.click('button:has-text("Execute")');
    
    // Wait for the command to complete and check the output
    await page.waitForSelector('.command-output');
    const commandOutput = await page.locator('.command-output').textContent();
    expect(commandOutput).toContain(testFileContent);
    console.log('File injection verified');
    
    // Destroy the VM
    await page.click('button:has-text("Destroy VM")');
    await page.click('button:has-text("Confirm")');
    
    // Wait for the VM to be destroyed
    await page.waitForSelector('text=VM destroyed successfully');
    console.log('VM destroyed successfully');
    
  } finally {
    // Clean up the test file
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
      console.log(`Deleted test file ${testFilePath}`);
    }
  }
});
