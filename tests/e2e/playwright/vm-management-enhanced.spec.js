// @ts-check
const { test, expect } = require('@playwright/test');

/**
 * Enhanced VM Management Tests based on reference repository patterns
 * Tests VM creation, actions, templates, and integration workflows
 */

// Test configuration
const API_BASE_URL = process.env.API_URL || 'http://localhost:8000';
const TEST_VM_PREFIX = 'test-vm-playwright';
const TEST_TIMEOUT = 60000; // 60 seconds

test.describe('Enhanced VM Management API', () => {
  let testVmId = null;
  let testVmName = null;

  test.beforeEach(async () => {
    // Generate unique VM name for each test
    testVmName = `${TEST_VM_PREFIX}-${Date.now()}`;
  });

  test.afterEach(async ({ request }) => {
    // Cleanup: Delete test VM if it was created
    if (testVmId) {
      try {
        await request.delete(`${API_BASE_URL}/api/v1/vms/${testVmId}?force=true`);
        console.log(`Cleaned up test VM: ${testVmId}`);
      } catch (error) {
        console.log(`Cleanup failed for VM ${testVmId}:`, error.message);
      }
      testVmId = null;
    }
  });

  test('should get VM templates with descriptions and compatibility', async ({ request }) => {
    console.log('Testing VM templates endpoint...');
    
    const response = await request.get(`${API_BASE_URL}/api/v1/vms/templates`);
    
    expect(response.status()).toBe(200);
    
    const templates = await response.json();
    expect(Array.isArray(templates)).toBeTruthy();
    expect(templates.length).toBeGreaterThan(0);
    
    // Verify template structure
    const template = templates[0];
    expect(template).toHaveProperty('value');
    expect(template).toHaveProperty('name');
    expect(template).toHaveProperty('description');
    expect(template).toHaveProperty('compatible_vm_types');
    expect(template).toHaveProperty('recommended');
    
    // Verify Ubuntu templates exist
    const ubuntuTemplates = templates.filter(t => t.name.includes('UBUNTU'));
    expect(ubuntuTemplates.length).toBeGreaterThan(0);
    
    console.log(`Found ${templates.length} VM templates`);
  });

  test('should create Docker VM with enhanced schema', async ({ request }) => {
    console.log(`Creating Docker VM: ${testVmName}`);
    
    const vmData = {
      name: testVmName,
      template: 'DOCKER_UBUNTU_2004',
      vm_type: 'docker',
      memory_mb: 512,
      cpus: 1,
      disk_gb: 10,
      domain: 'TurdParty',
      description: 'Test Docker VM created by Playwright',
      auto_start: true
    };
    
    const response = await request.post(`${API_BASE_URL}/api/v1/vms/`, {
      data: vmData
    });
    
    expect(response.status()).toBe(201);
    
    const vm = await response.json();
    testVmId = vm.vm_id;
    
    // Verify response structure
    expect(vm).toHaveProperty('vm_id');
    expect(vm).toHaveProperty('name', testVmName);
    expect(vm).toHaveProperty('template');
    expect(vm).toHaveProperty('vm_type', 'docker');
    expect(vm).toHaveProperty('memory_mb', 512);
    expect(vm).toHaveProperty('cpus', 1);
    expect(vm).toHaveProperty('domain', 'TurdParty');
    expect(vm).toHaveProperty('status');
    expect(vm).toHaveProperty('runtime_minutes');
    expect(vm).toHaveProperty('is_expired');
    
    console.log(`VM created successfully: ${vm.vm_id}`);
  });

  test('should reject VM creation with non-TurdParty domain', async ({ request }) => {
    console.log('Testing domain enforcement...');
    
    const vmData = {
      name: testVmName,
      template: 'UBUNTU_2004',
      vm_type: 'docker',
      memory_mb: 512,
      cpus: 1,
      domain: 'InvalidDomain'
    };
    
    const response = await request.post(`${API_BASE_URL}/api/v1/vms/`, {
      data: vmData
    });
    
    expect(response.status()).toBe(400);
    
    const error = await response.json();
    expect(error.detail).toContain('TurdParty domain');
    
    console.log('Domain enforcement working correctly');
  });

  test('should perform VM actions using action endpoint', async ({ request }) => {
    console.log('Testing VM action endpoint...');
    
    // First create a VM
    const vmData = {
      name: testVmName,
      template: 'DOCKER_ALPINE',
      vm_type: 'docker',
      memory_mb: 256,
      cpus: 1,
      domain: 'TurdParty',
      auto_start: false
    };
    
    const createResponse = await request.post(`${API_BASE_URL}/api/v1/vms/`, {
      data: vmData
    });
    
    expect(createResponse.status()).toBe(201);
    const vm = await createResponse.json();
    testVmId = vm.vm_id;
    
    // Test START action
    const startAction = {
      action: 'start',
      force: false
    };
    
    const startResponse = await request.post(`${API_BASE_URL}/api/v1/vms/${testVmId}/action`, {
      data: startAction
    });
    
    expect(startResponse.status()).toBe(200);
    const startResult = await startResponse.json();
    expect(startResult).toHaveProperty('action', 'start');
    expect(startResult).toHaveProperty('task_id');
    expect(startResult).toHaveProperty('message');
    
    // Test STOP action
    const stopAction = {
      action: 'stop',
      force: true
    };
    
    const stopResponse = await request.post(`${API_BASE_URL}/api/v1/vms/${testVmId}/action`, {
      data: stopAction
    });
    
    expect(stopResponse.status()).toBe(200);
    const stopResult = await stopResponse.json();
    expect(stopResult).toHaveProperty('action', 'stop');
    expect(stopResult).toHaveProperty('force', true);
    
    console.log('VM actions working correctly');
  });

  test('should handle invalid VM actions gracefully', async ({ request }) => {
    console.log('Testing invalid VM actions...');
    
    // Create a VM first
    const vmData = {
      name: testVmName,
      template: 'UBUNTU_2004',
      vm_type: 'docker',
      domain: 'TurdParty'
    };
    
    const createResponse = await request.post(`${API_BASE_URL}/api/v1/vms/`, {
      data: vmData
    });
    
    expect(createResponse.status()).toBe(201);
    const vm = await createResponse.json();
    testVmId = vm.vm_id;
    
    // Test invalid action
    const invalidAction = {
      action: 'invalid_action',
      force: false
    };
    
    const response = await request.post(`${API_BASE_URL}/api/v1/vms/${testVmId}/action`, {
      data: invalidAction
    });
    
    expect(response.status()).toBe(422); // Validation error
    
    console.log('Invalid action handling working correctly');
  });

  test('should list VMs with pagination and filtering', async ({ request }) => {
    console.log('Testing VM listing with pagination...');
    
    // Create a test VM first
    const vmData = {
      name: testVmName,
      template: 'ALPINE',
      vm_type: 'docker',
      domain: 'TurdParty'
    };
    
    const createResponse = await request.post(`${API_BASE_URL}/api/v1/vms/`, {
      data: vmData
    });
    
    expect(createResponse.status()).toBe(201);
    const vm = await createResponse.json();
    testVmId = vm.vm_id;
    
    // Test listing with pagination
    const listResponse = await request.get(`${API_BASE_URL}/api/v1/vms/?skip=0&limit=10`);
    
    expect(listResponse.status()).toBe(200);
    
    const vmList = await listResponse.json();
    expect(vmList).toHaveProperty('vms');
    expect(vmList).toHaveProperty('total');
    expect(vmList).toHaveProperty('skip', 0);
    expect(vmList).toHaveProperty('limit', 10);
    expect(Array.isArray(vmList.vms)).toBeTruthy();
    
    // Verify our VM is in the list
    const ourVm = vmList.vms.find(v => v.vm_id === testVmId);
    expect(ourVm).toBeTruthy();
    expect(ourVm.name).toBe(testVmName);
    
    console.log(`Found ${vmList.total} VMs in listing`);
  });

  test('should get VM details by ID', async ({ request }) => {
    console.log('Testing VM details retrieval...');
    
    // Create a VM first
    const vmData = {
      name: testVmName,
      template: 'CENTOS_7',
      vm_type: 'docker',
      memory_mb: 1024,
      cpus: 2,
      domain: 'TurdParty',
      description: 'Detailed test VM'
    };
    
    const createResponse = await request.post(`${API_BASE_URL}/api/v1/vms/`, {
      data: vmData
    });
    
    expect(createResponse.status()).toBe(201);
    const vm = await createResponse.json();
    testVmId = vm.vm_id;
    
    // Get VM details
    const detailsResponse = await request.get(`${API_BASE_URL}/api/v1/vms/${testVmId}`);
    
    expect(detailsResponse.status()).toBe(200);
    
    const vmDetails = await detailsResponse.json();
    expect(vmDetails.vm_id).toBe(testVmId);
    expect(vmDetails.name).toBe(testVmName);
    expect(vmDetails.memory_mb).toBe(1024);
    expect(vmDetails.cpus).toBe(2);
    expect(vmDetails.description).toBe('Detailed test VM');
    expect(vmDetails).toHaveProperty('runtime_minutes');
    expect(vmDetails).toHaveProperty('is_expired');
    
    console.log('VM details retrieval working correctly');
  });

  test('should handle VM not found errors', async ({ request }) => {
    console.log('Testing VM not found handling...');
    
    const fakeVmId = '00000000-0000-0000-0000-000000000000';
    
    // Test getting non-existent VM
    const getResponse = await request.get(`${API_BASE_URL}/api/v1/vms/${fakeVmId}`);
    expect(getResponse.status()).toBe(404);
    
    // Test action on non-existent VM
    const actionResponse = await request.post(`${API_BASE_URL}/api/v1/vms/${fakeVmId}/action`, {
      data: { action: 'start', force: false }
    });
    expect(actionResponse.status()).toBe(404);
    
    console.log('VM not found handling working correctly');
  });
});

test.describe('VM Integration Workflows', () => {
  test('should support complete VM lifecycle', async ({ request }) => {
    console.log('Testing complete VM lifecycle...');
    
    const testVmName = `lifecycle-test-${Date.now()}`;
    let testVmId = null;
    
    try {
      // 1. Create VM
      const createData = {
        name: testVmName,
        template: 'DOCKER_UBUNTU_2004',
        vm_type: 'docker',
        memory_mb: 512,
        cpus: 1,
        domain: 'TurdParty',
        description: 'Lifecycle test VM'
      };
      
      const createResponse = await request.post(`${API_BASE_URL}/api/v1/vms/`, {
        data: createData
      });
      
      expect(createResponse.status()).toBe(201);
      const vm = await createResponse.json();
      testVmId = vm.vm_id;
      
      // 2. Start VM
      const startResponse = await request.post(`${API_BASE_URL}/api/v1/vms/${testVmId}/action`, {
        data: { action: 'start', force: false }
      });
      expect(startResponse.status()).toBe(200);
      
      // 3. Stop VM
      const stopResponse = await request.post(`${API_BASE_URL}/api/v1/vms/${testVmId}/action`, {
        data: { action: 'stop', force: false }
      });
      expect(stopResponse.status()).toBe(200);
      
      // 4. Destroy VM
      const destroyResponse = await request.post(`${API_BASE_URL}/api/v1/vms/${testVmId}/action`, {
        data: { action: 'destroy', force: true }
      });
      expect(destroyResponse.status()).toBe(200);
      
      console.log('Complete VM lifecycle test passed');
      
    } finally {
      // Cleanup
      if (testVmId) {
        try {
          await request.delete(`${API_BASE_URL}/api/v1/vms/${testVmId}?force=true`);
        } catch (error) {
          console.log('Cleanup error:', error.message);
        }
      }
    }
  });
});
