import { test, expect } from '@playwright/test';

/**
 * VM Management UI Tests
 * Tests the functionality of the VM management UI.
 */
test.describe('VM Management UI', () => {
  // Test auth state
  let authToken: string;

  test.beforeAll(async ({ request }) => {
    // Get test auth token
    try {
      const response = await request.post('/api/v1/auth/test-token');
      const data = await response.json();
      authToken = data.access_token;
      console.log('Successfully obtained test token');
    } catch (error) {
      console.error('Failed to get test token:', error);
    }
  });

  test.beforeEach(async ({ page }) => {
    // Set auth token in local storage
    await page.goto('/');
    if (authToken) {
      await page.evaluate((token) => {
        localStorage.setItem('authToken', token);
      }, authToken);
    }
    
    // Navigate to the VM management page
    await page.goto('/vagrant_vm');
    
    // Wait for the page to load
    await page.waitForSelector('.vagrant-vm-container', { timeout: 10000 });
  });

  test('should display the VM management page correctly', async ({ page }) => {
    // Verify page title
    await expect(page.locator('.vagrant-vm-header h1')).toHaveText('Vagrant VM Management');
    
    // Verify create button exists
    await expect(page.locator('button:text("Create New VM")')).toBeVisible();
    
    // Verify table is visible
    await expect(page.locator('.vagrant-vm-table')).toBeVisible();
    
    // Take a screenshot
    await page.screenshot({ path: 'test-screenshots/vm-management-page.png' });
  });

  test('should show create VM form when clicking create button', async ({ page }) => {
    // Click create button
    await page.click('button:text("Create New VM")');
    
    // Verify modal is visible
    await expect(page.locator('.ant-modal-title:text("Create New Vagrant VM")')).toBeVisible();
    
    // Verify form fields are visible
    await expect(page.locator('input[placeholder="Enter a name for this VM"]')).toBeVisible();
    await expect(page.locator('select[name="template"]')).toBeVisible();
    await expect(page.locator('input[name="cpus"]')).toBeVisible();
    await expect(page.locator('input[name="memory_mb"]')).toBeVisible();
    await expect(page.locator('input[name="disk_gb"]')).toBeVisible();
    
    // Cancel the modal
    await page.click('button:text("Cancel")');
    await expect(page.locator('.ant-modal-title')).not.toBeVisible();
  });

  test('should make API request when creating a VM', async ({ page }) => {
    // Intercept API requests
    await page.route('**/api/v1/vagrant_vm', async (route) => {
      const request = route.request();
      if (request.method() === 'POST') {
        // Log the request data
        const postData = request.postDataJSON();
        console.log('VM creation request:', postData);
        
        // Mock a successful response
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            id: '123e4567-e89b-12d3-a456-426614174000',
            name: postData.name,
            template: postData.template,
            memory_mb: postData.memory_mb,
            cpus: postData.cpus,
            disk_gb: postData.disk_gb,
            status: 'PENDING',
            created_on: new Date().toISOString(),
            domain: 'TurdParty'
          })
        });
      } else {
        await route.continue();
      }
    });
    
    // Click create button
    await page.click('button:text("Create New VM")');
    
    // Fill in the form
    await page.fill('input[placeholder="Enter a name for this VM"]', 'Test VM');
    
    // Select template (may need adjustment based on actual implementation)
    await page.selectOption('select[name="template"]', { label: 'Ubuntu 20.04 LTS' });
    
    // Set resources
    await page.fill('input[name="cpus"]', '2');
    await page.fill('input[name="memory_mb"]', '2048');
    await page.fill('input[name="disk_gb"]', '20');
    
    // Submit the form
    await page.click('.ant-modal-footer button[type="button"]:text("OK")');
    
    // Verify success message appears
    await expect(page.locator('.ant-message')).toContainText('successfully');
  });

  test('should handle API errors gracefully', async ({ page }) => {
    // Intercept API requests to simulate error
    await page.route('**/api/v1/vagrant_vm', async (route) => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({
            detail: 'Internal server error'
          })
        });
      } else {
        await route.continue();
      }
    });
    
    // Refresh the page to trigger the error
    await page.reload();
    
    // Verify error message appears
    await expect(page.locator('.ant-alert-error')).toBeVisible();
    await expect(page.locator('.ant-alert-error')).toContainText('Failed to connect to the server');
    
    // Take a screenshot of the error state
    await page.screenshot({ path: 'test-screenshots/vm-management-error.png' });
  });
}); 