// @ts-check
const { test, expect } = require('@playwright/test');
const { login } = require('../utils/auth-helpers');

// Increase timeouts for VM operations
test.setTimeout(600000); // 10 minutes for VM operations

/**
 * Helper function to poll until VM reaches desired state
 * @param {import('@playwright/test').Page} page 
 * @param {string} vmName 
 * @param {string} expectedState 
 * @param {number} timeoutMs 
 * @param {number} pollIntervalMs 
 */
async function pollVMUntilState(page, vmName, expectedState, timeoutMs = 300000, pollIntervalMs = 5000) {
  const startTime = Date.now();
  let currentState = '';
  
  console.log(`Waiting for VM "${vmName}" to reach state "${expectedState}"...`);
  
  while (Date.now() - startTime < timeoutMs) {
    // Navigate to VM status page and refresh
    await page.goto('/vm_status');
    await page.click('button:has-text("Refresh")');
    await page.waitForTimeout(1000);
    
    // Find our VM in the list
    const vmRow = page.locator(`.ant-table-row:has-text("${vmName}")`);
    if (await vmRow.count() === 0) {
      console.log(`VM "${vmName}" not found in list, retrying...`);
      await page.waitForTimeout(pollIntervalMs);
      continue;
    }
    
    // Get current state
    const stateCell = vmRow.locator('td:nth-child(2)');
    currentState = await stateCell.textContent() || '';
    console.log(`Current state of VM "${vmName}": ${currentState}`);
    
    // Check if we've reached the expected state
    if (currentState.toLowerCase().includes(expectedState.toLowerCase())) {
      console.log(`VM "${vmName}" has reached expected state "${expectedState}"`);
      return true;
    }
    
    // Wait before polling again
    console.log(`Waiting ${pollIntervalMs/1000} seconds before checking again...`);
    await page.waitForTimeout(pollIntervalMs);
  }
  
  throw new Error(`Timeout waiting for VM "${vmName}" to reach state "${expectedState}". Current state: ${currentState}`);
}

test.describe('Vagrant VM Integration Tests', () => {
  let testVMName;
  
  // Create a unique VM name for each test run
  test.beforeAll(() => {
    testVMName = `test-vagrant-${Date.now()}`;
  });
  
  // Clean up any VMs created during testing
  test.afterAll(async ({ browser }) => {
    // Create a new page for cleanup
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
      // Login
      await login(page, 'testuser', 'password123');
      
      // Go to VM status page
      await page.goto('/vm_status');
      
      // Look for our test VM
      const vmRow = page.locator(`.ant-table-row:has-text("${testVMName}")`);
      if (await vmRow.count() > 0) {
        console.log(`Cleaning up test VM: ${testVMName}`);
        
        // Check if VM is running and stop it first if needed
        const statusCell = vmRow.locator('td:nth-child(2)');
        const status = await statusCell.textContent() || '';
        
        if (status.toLowerCase().includes('running')) {
          // Stop VM first
          await vmRow.locator('button:has-text("Stop")').click();
          await page.locator('.ant-modal-confirm .ant-btn-primary').click();
          await pollVMUntilState(page, testVMName, 'stopped', 180000);
        }
        
        // Delete VM
        await page.goto('/vm_status');
        await page.click('button:has-text("Refresh")');
        const vmRowAgain = page.locator(`.ant-table-row:has-text("${testVMName}")`);
        await vmRowAgain.locator('button:has-text("Delete")').click();
        await page.locator('.ant-modal-confirm .ant-btn-primary').click();
        
        // Wait for deletion to complete
        await page.waitForTimeout(10000);
        console.log(`Test VM ${testVMName} has been deleted`);
      }
    } catch (error) {
      console.error(`Error during cleanup: ${error.message}`);
    } finally {
      await context.close();
    }
  });
  
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await login(page, 'testuser', 'password123');
  });
  
  test('should create a new Vagrant VM with basic configuration', async ({ page }) => {
    // Go to VM creation page
    await page.goto('/vm_injection');
    await page.waitForLoadState('networkidle');
    
    console.log(`Creating test VM: ${testVMName}`);
    
    // Fill VM creation form
    await page.fill('input[name="vm_name"]', testVMName);
    await page.fill('input[name="memory"]', '1024');
    await page.fill('input[name="cpu"]', '1');
    await page.fill('input[name="disk"]', '10');
    
    // Select Vagrant VM type - adjust selector based on actual UI
    await page.selectOption('select[name="vm_type"], select[name="provider"]', 'vagrant');
    
    // Select a Vagrant box - adjust based on available options
    await page.selectOption('select[name="box"], select[name="os_type"]', 'ubuntu/bionic64');
    
    // Take screenshot before submitting
    await page.screenshot({ path: 'test-results/before-vm-creation.png' });
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Wait for the creation process to start
    await page.waitForResponse(response => 
      response.url().includes('/api/vm/create') || 
      response.url().includes('/api/vagrant_vm/create')
    );
    
    // Check for immediate success message
    const successMessage = await page.locator('.ant-message-success, .success-message').textContent();
    console.log(`Creation response: ${successMessage}`);
    expect(successMessage).toBeTruthy();
    
    // Go to VM status page and wait for VM to appear
    await page.goto('/vm_status');
    
    // Wait for the VM to appear in the list (may take some time for backend processing)
    let retries = 0;
    const maxRetries = 10;
    let vmFound = false;
    
    while (retries < maxRetries && !vmFound) {
      await page.click('button:has-text("Refresh")');
      await page.waitForTimeout(5000);
      
      // Check if our VM appears in the list
      const vmRow = page.locator(`.ant-table-row:has-text("${testVMName}")`);
      vmFound = await vmRow.count() > 0;
      
      if (!vmFound) {
        console.log(`VM not found yet, retrying (${retries + 1}/${maxRetries})...`);
        retries++;
      }
    }
    
    expect(vmFound).toBeTruthy();
    
    // Take screenshot after VM is created
    await page.screenshot({ path: 'test-results/after-vm-creation.png' });
  });
  
  test('should start the Vagrant VM and verify it is running', async ({ page }) => {
    // Go to VM status page
    await page.goto('/vm_status');
    await page.waitForLoadState('networkidle');
    
    // Find our test VM
    const vmRow = page.locator(`.ant-table-row:has-text("${testVMName}")`);
    expect(await vmRow.count()).toBe(1);
    
    // Check current status
    const statusCell = vmRow.locator('td:nth-child(2)');
    const currentStatus = await statusCell.textContent() || '';
    console.log(`Current VM status: ${currentStatus}`);
    
    // If VM is already running, we'll skip the start operation
    if (currentStatus.toLowerCase().includes('running')) {
      console.log('VM is already running, skipping start operation');
      return;
    }
    
    // Start the VM
    await vmRow.locator('button:has-text("Start")').click();
    
    // Confirm start operation
    await page.locator('.ant-modal-confirm .ant-btn-primary').click();
    
    // Wait for start operation to initiate
    await page.waitForResponse(response => 
      response.url().includes('/api/vm/start') || 
      response.url().includes('/api/vagrant_vm/start')
    );
    
    // Take screenshot after initiating start
    await page.screenshot({ path: 'test-results/vm-starting.png' });
    
    // Poll until VM reaches running state or timeout
    await pollVMUntilState(page, testVMName, 'running');
    
    // Take screenshot of running VM
    await page.screenshot({ path: 'test-results/vm-running.png' });
  });
  
  test('should stop the Vagrant VM and verify it is stopped', async ({ page }) => {
    // Go to VM status page
    await page.goto('/vm_status');
    await page.waitForLoadState('networkidle');
    
    // Find our test VM
    const vmRow = page.locator(`.ant-table-row:has-text("${testVMName}")`);
    expect(await vmRow.count()).toBe(1);
    
    // Check current status
    const statusCell = vmRow.locator('td:nth-child(2)');
    const currentStatus = await statusCell.textContent() || '';
    console.log(`Current VM status: ${currentStatus}`);
    
    // If VM is already stopped, we'll skip the stop operation
    if (currentStatus.toLowerCase().includes('stopped') || 
        currentStatus.toLowerCase().includes('poweroff')) {
      console.log('VM is already stopped, skipping stop operation');
      return;
    }
    
    // Stop the VM
    await vmRow.locator('button:has-text("Stop")').click();
    
    // Confirm stop operation
    await page.locator('.ant-modal-confirm .ant-btn-primary').click();
    
    // Wait for stop operation to initiate
    await page.waitForResponse(response => 
      response.url().includes('/api/vm/stop') || 
      response.url().includes('/api/vagrant_vm/stop')
    );
    
    // Take screenshot after initiating stop
    await page.screenshot({ path: 'test-results/vm-stopping.png' });
    
    // Poll until VM reaches stopped state or timeout
    await pollVMUntilState(page, testVMName, 'stopped', 300000, 10000);
    
    // Take screenshot of stopped VM
    await page.screenshot({ path: 'test-results/vm-stopped.png' });
  });
  
  test('should show VM details with accurate information', async ({ page }) => {
    // Go to VM status page
    await page.goto('/vm_status');
    await page.waitForLoadState('networkidle');
    
    // Find our test VM
    const vmRow = page.locator(`.ant-table-row:has-text("${testVMName}")`);
    expect(await vmRow.count()).toBe(1);
    
    // Click on VM name to view details (adjust if UI navigation is different)
    await vmRow.locator('td:first-child').click();
    
    // Wait for details page to load
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of details page
    await page.screenshot({ path: 'test-results/vm-details.png' });
    
    // Verify details are shown
    const detailsPanel = page.locator('.vm-details, .ant-descriptions');
    expect(await detailsPanel.count()).toBe(1);
    
    // Verify important details are correct
    const nameDetail = page.locator('.ant-descriptions-item:has-text("Name") .ant-descriptions-item-content');
    expect(await nameDetail.textContent()).toContain(testVMName);
    
    const memoryDetail = page.locator('.ant-descriptions-item:has-text("Memory") .ant-descriptions-item-content');
    expect(await memoryDetail.textContent()).toContain('1024');
    
    const cpuDetail = page.locator('.ant-descriptions-item:has-text("CPU") .ant-descriptions-item-content');
    expect(await cpuDetail.textContent()).toContain('1');
    
    // Verify VM type shows as Vagrant
    const typeDetail = page.locator('.ant-descriptions-item:has-text("Type") .ant-descriptions-item-content');
    expect(await typeDetail.textContent()).toContain('vagrant');
  });
  
  test('should delete the Vagrant VM', async ({ page }) => {
    // This test should be run last since it removes the VM
    
    // Go to VM status page
    await page.goto('/vm_status');
    await page.waitForLoadState('networkidle');
    
    // Find our test VM
    const vmRow = page.locator(`.ant-table-row:has-text("${testVMName}")`);
    
    // Skip test if VM doesn't exist anymore
    const vmExists = await vmRow.count() > 0;
    test.skip(!vmExists, 'Test VM does not exist, skipping deletion test');
    if (!vmExists) return;
    
    // Check current status
    const statusCell = vmRow.locator('td:nth-child(2)');
    const currentStatus = await statusCell.textContent() || '';
    
    // If VM is running, stop it first
    if (currentStatus.toLowerCase().includes('running')) {
      console.log('VM is running, stopping it before deletion');
      
      // Stop the VM
      await vmRow.locator('button:has-text("Stop")').click();
      
      // Confirm stop operation
      await page.locator('.ant-modal-confirm .ant-btn-primary').click();
      
      // Wait for VM to be stopped
      await pollVMUntilState(page, testVMName, 'stopped', 300000, 10000);
      
      // Refresh page to ensure UI is updated
      await page.goto('/vm_status');
      await page.waitForLoadState('networkidle');
    }
    
    // Find VM row again after potential stop operation
    const vmRowAgain = page.locator(`.ant-table-row:has-text("${testVMName}")`);
    expect(await vmRowAgain.count()).toBe(1);
    
    // Delete the VM
    await vmRowAgain.locator('button:has-text("Delete")').click();
    
    // Confirm deletion
    await page.locator('.ant-modal-confirm .ant-btn-primary').click();
    
    // Wait for deletion to start
    await page.waitForResponse(response => 
      response.url().includes('/api/vm/delete') || 
      response.url().includes('/api/vagrant_vm/delete')
    );
    
    // Deletion can take time, wait a bit before checking
    await page.waitForTimeout(10000);
    
    // Refresh page to see if VM is gone
    await page.goto('/vm_status');
    await page.click('button:has-text("Refresh")');
    
    // Poll until VM disappears from list
    let retries = 0;
    const maxRetries = 10;
    let vmExists2 = true;
    
    while (retries < maxRetries && vmExists2) {
      await page.click('button:has-text("Refresh")');
      await page.waitForTimeout(5000);
      
      const vmRowAfterDelete = page.locator(`.ant-table-row:has-text("${testVMName}")`);
      vmExists2 = await vmRowAfterDelete.count() > 0;
      
      if (vmExists2) {
        console.log(`VM still exists, retrying (${retries + 1}/${maxRetries})...`);
        retries++;
      }
    }
    
    // Verify VM no longer exists
    const finalCheck = page.locator(`.ant-table-row:has-text("${testVMName}")`);
    expect(await finalCheck.count()).toBe(0);
    
    // Take screenshot after deletion
    await page.screenshot({ path: 'test-results/after-vm-deletion.png' });
  });
}); 