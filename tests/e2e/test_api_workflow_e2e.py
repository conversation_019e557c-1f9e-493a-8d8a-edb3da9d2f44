"""
API-based End-to-End Tests for TurdParty Workflow.

Tests the complete workflow using API calls instead of browser automation.
This allows E2E testing without browser dependencies.
"""

import asyncio
import io
import json
import tempfile
import time
from pathlib import Path
from typing import Dict, Any
from unittest.mock import patch

import pytest
import httpx
from fastapi.testclient import TestClient

# Import the FastAPI app
try:
    from services.api.src.main import create_application
    APP_AVAILABLE = True
except ImportError:
    APP_AVAILABLE = False


@pytest.mark.skipif(not APP_AVAILABLE, reason="FastAPI app not available")
class TestAPIWorkflowE2E:
    """API-based end-to-end tests for complete TurdParty workflow."""
    
    @pytest.fixture(scope="class")
    def api_client(self):
        """Create API client for testing."""
        app = create_application()
        return TestClient(app)
    
    @pytest.fixture
    def test_file_content(self):
        """Create test file content."""
        return b"""#!/bin/bash
echo "E2E API test script"
echo "Timestamp: $(date)"
echo "Hostname: $(hostname)"
echo "Current user: $(whoami)"
echo "Working directory: $(pwd)"
ls -la /app
exit 0
"""
    
    @pytest.fixture
    def test_file_metadata(self):
        """Test file metadata."""
        return {
            "filename": "e2e_api_test_script.sh",
            "target_path": "/app/scripts/e2e_api_test_script.sh",
            "permissions": "0755",
            "description": "E2E API test script upload"
        }
    
    def test_complete_file_upload_workflow(self, api_client, test_file_content, test_file_metadata):
        """Test complete file upload workflow via API."""
        # Step 1: Upload file
        files = {
            "file": (test_file_metadata["filename"], test_file_content, "application/x-sh")
        }
        data = {
            "target_path": test_file_metadata["target_path"],
            "permissions": test_file_metadata["permissions"],
            "description": test_file_metadata["description"]
        }
        
        response = api_client.post("/api/v1/files/upload", files=files, data=data)
        
        # Should either succeed or return proper error
        assert response.status_code in [201, 400, 422, 500]
        
        if response.status_code == 201:
            upload_data = response.json()
            assert "file_id" in upload_data
            assert upload_data["filename"] == test_file_metadata["filename"]
            
            file_id = upload_data["file_id"]
            
            # Step 2: Verify file was uploaded
            get_response = api_client.get(f"/api/v1/files/{file_id}")
            assert get_response.status_code == 200
            
            file_data = get_response.json()
            assert file_data["file_id"] == file_id
            assert file_data["filename"] == test_file_metadata["filename"]
    
    def test_vm_creation_and_management_workflow(self, api_client):
        """Test VM creation and management workflow via API."""
        # Step 1: Create VM
        vm_data = {
            "name": "e2e-test-vm",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 512,
            "cpus": 1,
            "description": "E2E test VM"
        }
        
        response = api_client.post("/api/v1/vms/", json=vm_data)
        
        # Should either succeed or return proper error
        assert response.status_code in [201, 400, 422, 500]
        
        if response.status_code == 201:
            vm_response = response.json()
            assert "vm_id" in vm_response
            assert vm_response["name"] == vm_data["name"]
            
            vm_id = vm_response["vm_id"]
            
            # Step 2: Get VM details
            get_response = api_client.get(f"/api/v1/vms/{vm_id}")
            assert get_response.status_code in [200, 404]
            
            if get_response.status_code == 200:
                vm_details = get_response.json()
                assert vm_details["vm_id"] == vm_id
                assert vm_details["name"] == vm_data["name"]
                
                # Step 3: Try VM actions
                action_data = {"action": "start"}
                action_response = api_client.post(f"/api/v1/vms/{vm_id}/action", json=action_data)
                assert action_response.status_code in [200, 400, 404, 500]
                
                # Step 4: Clean up - delete VM
                delete_response = api_client.delete(f"/api/v1/vms/{vm_id}")
                assert delete_response.status_code in [200, 404, 500]
    
    def test_workflow_creation_and_monitoring(self, api_client, test_file_content, test_file_metadata):
        """Test workflow creation and monitoring via API."""
        # Step 1: Upload a file first
        files = {
            "file": (test_file_metadata["filename"], test_file_content, "application/x-sh")
        }
        data = {
            "target_path": test_file_metadata["target_path"],
            "permissions": test_file_metadata["permissions"],
            "description": test_file_metadata["description"]
        }
        
        upload_response = api_client.post("/api/v1/files/upload", files=files, data=data)
        
        if upload_response.status_code == 201:
            upload_data = upload_response.json()
            file_id = upload_data["file_id"]
            
            # Step 2: Create workflow
            workflow_data = {
                "file_id": file_id,
                "vm_template": "ubuntu:20.04",
                "analysis_duration": 300,  # 5 minutes
                "description": "E2E test workflow"
            }
            
            workflow_response = api_client.post("/api/v1/workflows/", json=workflow_data)
            assert workflow_response.status_code in [201, 400, 422, 500]
            
            if workflow_response.status_code == 201:
                workflow_data_response = workflow_response.json()
                assert "workflow_id" in workflow_data_response
                
                workflow_id = workflow_data_response["workflow_id"]
                
                # Step 3: Monitor workflow
                get_workflow_response = api_client.get(f"/api/v1/workflows/{workflow_id}")
                assert get_workflow_response.status_code in [200, 404]
                
                if get_workflow_response.status_code == 200:
                    workflow_details = get_workflow_response.json()
                    assert workflow_details["workflow_id"] == workflow_id
                    assert "status" in workflow_details
    
    def test_system_health_and_status(self, api_client):
        """Test system health and status endpoints."""
        # Test basic health
        health_response = api_client.get("/health")
        assert health_response.status_code == 200
        
        health_data = health_response.json()
        assert "status" in health_data
        assert "timestamp" in health_data
        
        # Test API v1 health
        api_health_response = api_client.get("/api/v1/health")
        assert api_health_response.status_code == 200
        
        # Test system info
        system_response = api_client.get("/api/v1/system/info")
        assert system_response.status_code in [200, 404, 500]
        
        if system_response.status_code == 200:
            system_data = system_response.json()
            assert isinstance(system_data, dict)
    
    def test_storage_and_file_management(self, api_client):
        """Test storage and file management endpoints."""
        # Test storage status
        storage_response = api_client.get("/api/v1/storage/status")
        assert storage_response.status_code in [200, 404, 500]
        
        # Test files list
        files_response = api_client.get("/api/v1/files/")
        assert files_response.status_code in [200, 500]
        
        if files_response.status_code == 200:
            files_data = files_response.json()
            assert isinstance(files_data, dict)
            assert "files" in files_data or "total" in files_data
    
    def test_error_handling_and_validation(self, api_client):
        """Test error handling and validation across the API."""
        # Test invalid file upload
        invalid_response = api_client.post("/api/v1/files/upload")
        assert invalid_response.status_code in [400, 422]
        
        # Test invalid VM creation
        invalid_vm_data = {
            "name": "",  # Invalid empty name
            "template": "invalid-template",
            "vm_type": "invalid-type"
        }
        invalid_vm_response = api_client.post("/api/v1/vms/", json=invalid_vm_data)
        assert invalid_vm_response.status_code == 422
        
        # Test nonexistent resource access (use valid UUID format)
        # Note: In test environment without database, may return 500 instead of 404
        nonexistent_file_response = api_client.get("/api/v1/files/00000000-0000-0000-0000-000000000000")
        assert nonexistent_file_response.status_code in [404, 500]

        nonexistent_vm_response = api_client.get("/api/v1/vms/00000000-0000-0000-0000-000000000000")
        assert nonexistent_vm_response.status_code in [404, 500]
    
    def test_api_documentation_and_schema(self, api_client):
        """Test API documentation and schema endpoints."""
        # Test OpenAPI schema
        schema_response = api_client.get("/openapi.json")
        assert schema_response.status_code == 200
        
        schema_data = schema_response.json()
        assert "openapi" in schema_data
        assert "info" in schema_data
        assert "paths" in schema_data
        
        # Verify key endpoints are documented
        paths = schema_data["paths"]
        expected_paths = ["/health", "/api/v1/health"]
        
        for path in expected_paths:
            if path in paths:
                assert isinstance(paths[path], dict)
        
        # Test Swagger UI
        docs_response = api_client.get("/docs")
        assert docs_response.status_code == 200
        
        # Test ReDoc
        redoc_response = api_client.get("/redoc")
        assert redoc_response.status_code == 200
    
    def test_concurrent_api_operations(self, api_client):
        """Test concurrent API operations."""
        import threading
        import time
        
        results = []
        start_time = time.time()
        
        def make_health_request():
            response = api_client.get("/health")
            results.append(response.status_code)
        
        # Make 5 concurrent health requests
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_health_request)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        duration = time.time() - start_time
        
        # All requests should succeed
        assert all(status == 200 for status in results)
        assert len(results) == 5
        
        # Should complete within reasonable time
        assert duration < 5.0
