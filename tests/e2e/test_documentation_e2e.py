"""
End-to-end tests for TurdParty documentation
Tests complete user workflows through documentation
"""

import pytest
import requests
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException


class TestDocumentationE2E:
    """End-to-end tests for documentation user experience"""
    
    @pytest.fixture(scope="class")
    def driver(self):
        """Selenium WebDriver with headless Chrome"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.implicitly_wait(10)
            yield driver
        except WebDriverException as e:
            pytest.skip(f"Chrome WebDriver not available: {e}")
        finally:
            if 'driver' in locals():
                driver.quit()
    
    @pytest.fixture(scope="class")
    def base_url(self):
        """Base URL for documentation"""
        return "http://docs.turdparty.localhost"
    
    def test_documentation_loads_successfully(self, driver, base_url):
        """Test documentation page loads without errors"""
        driver.get(base_url)
        
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Check page title
        assert "TurdParty" in driver.title, f"Expected TurdParty in title, got: {driver.title}"
        
        # Check for main content
        body = driver.find_element(By.TAG_NAME, "body")
        assert body.text, "Page body should not be empty"
        
        # Check for no JavaScript errors
        logs = driver.get_log('browser')
        errors = [log for log in logs if log['level'] == 'SEVERE']
        assert len(errors) == 0, f"JavaScript errors found: {errors}"
    
    def test_dark_mode_default(self, driver, base_url):
        """Test documentation defaults to dark mode"""
        driver.get(base_url)
        
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "html"))
        )
        
        # Check for dark theme attribute
        html_element = driver.find_element(By.TAG_NAME, "html")
        theme_attr = html_element.get_attribute("data-theme")
        
        assert theme_attr == "dark", f"Expected dark theme, got: {theme_attr}"
        
        # Check computed styles for dark colors
        body = driver.find_element(By.TAG_NAME, "body")
        bg_color = body.value_of_css_property("background-color")
        
        # Dark background should be dark (low RGB values)
        assert "rgb" in bg_color.lower(), f"Background color should be RGB, got: {bg_color}"
    
    def test_theme_toggle_functionality(self, driver, base_url):
        """Test theme toggle button works"""
        driver.get(base_url)
        
        # Wait for theme toggle to be available
        try:
            theme_toggle = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.ID, "theme-toggle"))
            )
        except TimeoutException:
            # Theme toggle might not be implemented yet
            pytest.skip("Theme toggle button not found")
        
        # Get initial theme
        html_element = driver.find_element(By.TAG_NAME, "html")
        initial_theme = html_element.get_attribute("data-theme")
        
        # Click theme toggle
        theme_toggle.click()
        
        # Wait for theme change
        time.sleep(1)
        
        # Check theme changed
        new_theme = html_element.get_attribute("data-theme")
        assert new_theme != initial_theme, "Theme should change when toggle is clicked"
    
    def test_navigation_links_work(self, driver, base_url):
        """Test navigation links in documentation"""
        driver.get(base_url)
        
        # Wait for navigation to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "nav-card"))
        )
        
        # Find navigation links
        nav_links = driver.find_elements(By.CLASS_NAME, "nav-card")
        assert len(nav_links) > 0, "Should have navigation cards"
        
        # Test first navigation link
        if nav_links:
            first_link = nav_links[0]
            href = first_link.get_attribute("href")
            
            if href and not href.startswith("javascript:"):
                # Click the link
                first_link.click()
                
                # Wait for navigation or new page
                time.sleep(2)
                
                # Check we navigated somewhere
                current_url = driver.current_url
                assert current_url != base_url or "#" in current_url, \
                    "Navigation should change URL or scroll to section"
    
    def test_mermaid_diagrams_render(self, driver, base_url):
        """Test Mermaid diagrams render correctly"""
        driver.get(base_url)
        
        # Wait for Mermaid to load and render
        try:
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.CLASS_NAME, "mermaid"))
            )
        except TimeoutException:
            pytest.skip("Mermaid diagrams not found")
        
        # Find Mermaid diagrams
        mermaid_elements = driver.find_elements(By.CLASS_NAME, "mermaid")
        assert len(mermaid_elements) > 0, "Should have Mermaid diagrams"
        
        # Check if diagrams have been processed
        for diagram in mermaid_elements:
            # Mermaid adds SVG elements when rendered
            svg_elements = diagram.find_elements(By.TAG_NAME, "svg")
            if svg_elements:
                assert len(svg_elements) > 0, "Mermaid diagram should contain SVG"
                
                # Check SVG has content
                svg = svg_elements[0]
                assert svg.size['width'] > 0, "SVG should have width"
                assert svg.size['height'] > 0, "SVG should have height"
    
    def test_responsive_design(self, driver, base_url):
        """Test documentation is responsive"""
        # Test desktop size
        driver.set_window_size(1920, 1080)
        driver.get(base_url)
        
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        desktop_layout = driver.find_element(By.TAG_NAME, "body")
        desktop_width = desktop_layout.size['width']
        
        # Test mobile size
        driver.set_window_size(375, 667)  # iPhone size
        time.sleep(1)  # Allow layout to adjust
        
        mobile_layout = driver.find_element(By.TAG_NAME, "body")
        mobile_width = mobile_layout.size['width']
        
        # Layout should adapt to different sizes
        assert mobile_width < desktop_width, "Layout should adapt to mobile size"
        
        # Check for mobile-friendly elements
        viewport_meta = driver.find_elements(By.XPATH, "//meta[@name='viewport']")
        assert len(viewport_meta) > 0, "Should have viewport meta tag for mobile"
    
    def test_api_documentation_accessible(self, driver, base_url):
        """Test API documentation section is accessible"""
        api_url = f"{base_url}/api/"
        driver.get(api_url)
        
        # Wait for Swagger UI to load
        try:
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.ID, "swagger-ui"))
            )
        except TimeoutException:
            pytest.skip("Swagger UI not found")
        
        # Check Swagger UI loaded
        swagger_ui = driver.find_element(By.ID, "swagger-ui")
        assert swagger_ui.is_displayed(), "Swagger UI should be visible"
        
        # Check for API content
        page_text = driver.find_element(By.TAG_NAME, "body").text.lower()
        api_terms = ["api", "endpoint", "swagger", "openapi"]
        
        found_terms = [term for term in api_terms if term in page_text]
        assert len(found_terms) > 0, f"API documentation should contain API terms, found: {found_terms}"
    
    def test_status_integration_works(self, driver, base_url):
        """Test status integration in documentation"""
        driver.get(base_url)
        
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Look for status elements
        status_elements = driver.find_elements(By.ID, "status")
        if status_elements:
            status_element = status_elements[0]
            
            # Wait for status to update
            time.sleep(3)
            
            status_text = status_element.text.lower()
            
            # Should show some kind of status
            status_indicators = ["operational", "system", "available", "unknown"]
            has_status = any(indicator in status_text for indicator in status_indicators)
            
            assert has_status, f"Status element should show system status, got: {status_text}"
    
    def test_search_functionality(self, driver, base_url):
        """Test search functionality if available"""
        driver.get(base_url)
        
        # Look for search input
        search_inputs = driver.find_elements(By.XPATH, "//input[@type='search']")
        search_inputs.extend(driver.find_elements(By.XPATH, "//input[@placeholder*='search']"))
        
        if search_inputs:
            search_input = search_inputs[0]
            
            # Test search
            search_input.send_keys("API")
            time.sleep(1)
            
            # Look for search results
            search_results = driver.find_elements(By.CLASS_NAME, "search-result")
            
            # If search is implemented, should have results
            if search_results:
                assert len(search_results) > 0, "Search should return results for 'API'"
        else:
            pytest.skip("Search functionality not implemented")
    
    def test_accessibility_features(self, driver, base_url):
        """Test basic accessibility features"""
        driver.get(base_url)
        
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Check for proper heading structure
        headings = driver.find_elements(By.XPATH, "//h1 | //h2 | //h3 | //h4 | //h5 | //h6")
        assert len(headings) > 0, "Should have proper heading structure"
        
        # Check for alt text on images
        images = driver.find_elements(By.TAG_NAME, "img")
        for img in images:
            alt_text = img.get_attribute("alt")
            assert alt_text is not None, "Images should have alt text"
        
        # Check for language attribute
        html_element = driver.find_element(By.TAG_NAME, "html")
        lang_attr = html_element.get_attribute("lang")
        assert lang_attr is not None, "HTML should have language attribute"
    
    def test_performance_metrics(self, driver, base_url):
        """Test basic performance metrics"""
        start_time = time.time()
        
        driver.get(base_url)
        
        # Wait for page to be fully loaded
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        load_time = time.time() - start_time
        
        # Page should load reasonably quickly
        assert load_time < 10.0, f"Page should load in under 10 seconds, took {load_time:.2f}s"
        
        # Check for performance timing if available
        try:
            navigation_timing = driver.execute_script(
                "return window.performance.timing.loadEventEnd - window.performance.timing.navigationStart"
            )
            
            if navigation_timing > 0:
                timing_seconds = navigation_timing / 1000
                print(f"Navigation timing: {timing_seconds:.2f}s")
                assert timing_seconds < 5.0, f"Navigation timing too slow: {timing_seconds:.2f}s"
        except Exception:
            # Performance timing might not be available
            pass


if __name__ == "__main__":
    # Run tests with verbose output
    pytest.main([__file__, "-v", "--tb=short", "-m", "not slow"])
