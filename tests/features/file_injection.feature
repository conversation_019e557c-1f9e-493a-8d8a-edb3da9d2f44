Feature: File Injection
  As a system administrator
  I want to inject files into the system
  So that I can deploy scripts and configurations

  Background:
    Given the TurdParty API is running
    And the ELK stack is available

  Scenario: Successfully inject a shell script
    Given I have a shell script file "deploy.sh"
    When I upload the file for injection to "/app/scripts/deploy.sh" with permissions "0755"
    Then the injection should be created successfully
    And the injection status should be "pending"
    And the file should be logged to ELK stack

  Scenario: Process a pending injection
    Given I have created a file injection with ID "test-injection-123"
    And the injection status is "pending"
    When I process the injection
    Then the injection status should be "completed"
    And the installation base should be logged to ELK stack
    And the file should be available at the target path

  Scenario: Retrieve injection status
    Given I have created a file injection with ID "test-injection-456"
    When I request the injection status
    Then I should receive the current status
    And the response should include progress information

  Sc<PERSON>rio: List all injections with pagination
    Given I have created 10 file injections
    When I request injections with skip=5 and limit=3
    Then I should receive 3 injections
    And the injections should be properly paginated

  Scenario: Filter injections by status
    Given I have created injections with different statuses
    When I request injections with status filter "completed"
    Then I should only receive completed injections

  Scenario: Delete an injection
    Given I have created a file injection with ID "test-injection-789"
    When I delete the injection
    Then the injection should be removed
    And the deletion should be logged to ELK stack

  Scenario: Handle invalid file upload
    Given I have an invalid file
    When I attempt to upload the file for injection
    Then the upload should fail with validation error
    And no injection should be created

  Scenario: Process non-existent injection
    Given there is no injection with ID "non-existent-id"
    When I attempt to process the injection
    Then I should receive a not found error

  Scenario: ELK logging verification
    Given I have created and processed a file injection
    When I query the ELK stack for injection events
    Then I should find logs for injection creation
    And I should find logs for injection processing
    And I should find installation base information

  Scenario Outline: Inject files with different permissions
    Given I have a file "test_file.txt"
    When I upload the file with permissions "<permissions>"
    Then the injection should be created with permissions "<permissions>"
    And the file should be processed correctly

    Examples:
      | permissions |
      | 0644        |
      | 0755        |
      | 0777        |

  Scenario: Concurrent file injections
    Given I have multiple files to inject
    When I create multiple injections simultaneously
    Then all injections should be created successfully
    And each injection should have a unique ID
    And all injections should be logged separately

  Scenario: File injection with MinIO storage
    Given I have a file "storage_test.sh" with content "#!/bin/bash\necho 'MinIO test'\n"
    When I upload the file to MinIO storage
    Then the file should be stored with a UUID
    And the file should be downloadable from MinIO
    And the file metadata should be correct

  Scenario: Worker queue processing
    Given I have uploaded a file for injection
    And the file is stored in MinIO
    When the worker queue processes the file
    Then a VM should be created for processing
    And the file should be injected into the VM
    And the processing should be logged to ELK

  Scenario: VM lifecycle management
    Given I have a file injection request
    When the VM is created for processing
    Then the VM should be running within 2 minutes
    And the VM should have the correct configuration
    When the processing is complete
    Then the VM should be destroyed after 30 minutes
    And a new VM should be provisioned to maintain the pool

  Scenario: End-to-end workflow validation
    Given I have a test script "e2e_test.sh"
    When I upload the file via API
    Then the file should receive a UUID and be stored in MinIO
    When the worker processes the file
    Then a VM should be created and the file injected
    And runtime data should be streamed to ELK
    When I check the ELK dashboard
    Then I should see the complete workflow logs
    And the analysis results should be available

  Scenario: Error recovery and retry logic
    Given I have a file injection that fails initially
    When the worker encounters an error
    Then the task should be retried according to policy
    And error details should be logged to ELK
    When the retry succeeds
    Then the injection should complete successfully
    And both failure and success should be logged

  Scenario: Security validation
    Given I have a potentially malicious file "malware_test.exe"
    When I attempt to upload the file
    Then the file should be scanned for threats
    And if threats are detected, the upload should be rejected
    And the security event should be logged to ELK

  Scenario: Performance monitoring
    Given I have multiple concurrent file injections
    When the system processes the files
    Then response times should be within acceptable limits
    And resource usage should be monitored
    And performance metrics should be logged to ELK

  Scenario: Data retention and cleanup
    Given I have processed files older than the retention period
    When the cleanup process runs
    Then old files should be removed from MinIO
    And old logs should be archived in ELK
    And the cleanup should be logged for audit purposes
