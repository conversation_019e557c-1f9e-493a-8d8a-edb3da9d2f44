# 💩🎉TurdParty🎉💩 Integration Tests

Comprehensive integration testing suite for the TurdParty binary analysis framework, featuring:
- **Enhanced VM Pool System** with priority-based allocation and Elasticsearch logging
- **Real VM injection** with MinIO storage and ELK data capture
- **Multi-template support** for Windows, Ubuntu, and Alpine Linux VMs

## 🎯 Overview

This integration test suite implements comprehensive TurdParty testing across two main areas:

### 🚀 Enhanced VM Pool System Tests
- **API Endpoint Testing** - All enhanced VM pool REST endpoints
- **Celery Integration** - Task execution and queue processing with Elasticsearch logging
- **Priority-Based Allocation** - Critical to Background priority levels
- **Multi-Template Support** - Windows 10, Ubuntu, Alpine Linux templates
- **Performance Benchmarking** - Load testing and response time analysis
- **Database Operations** - Pool configurations and VM tracking

### 🔬 Complete Binary Analysis Workflow Tests
1. **Download** top Linux and Windows applications
2. **Upload** to MinIO with UUID and Blake3 hash metadata
3. **Inject** files into isolated VMs (Ubuntu/Windows)
4. **Install** applications and capture footprints
5. **Store** results in ELK stack with benchmarks
6. **Clean up** all test resources

## 📋 Test Applications

### Linux Applications (10 total)
- **DEB Packages** (3): Firefox, VLC, GIMP
- **AppImage** (3): Obsidian, KDENLive, Blender
- **TAR.GZ** (3): Discord, VS Code, Node.js
- **Custom Choice** (1): Docker

### Windows Applications (10 total)
- **EXE Packages** (5): Firefox, Chrome, VLC, 7-Zip, Notepad++
- **MSI Packages** (5): Python, Git, Node.js, VS Code, Discord

## 🛠️ Prerequisites

### Required Software
- **Python 3.12+** with Nix environment
- **Vagrant** for VM management
- **VirtualBox** for VM hosting
- **MinIO** server running on localhost:9000
- **ELK Stack** (Elasticsearch on localhost:9200)

### Required Services
```bash
# Ensure these services are running before tests:
docker-compose up -d minio elasticsearch kibana
```

### VM Images
The tests will automatically download these Vagrant boxes:
- `ubuntu/focal64` for Ubuntu tests
- `gusztavvargadr/windows-10` for Windows tests

## 🚀 Quick Start

### 1. Enhanced VM Pool System Tests

#### 🏃‍♂️ Quick Smoke Tests
```bash
./scripts/run-enhanced-vm-pool-tests.sh quick
```

#### 🧪 Full Enhanced VM Pool Integration Tests
```bash
./scripts/run-enhanced-vm-pool-tests.sh integration
```

#### 🔬 Pytest-Compatible Tests
```bash
./scripts/run-enhanced-vm-pool-tests.sh pytest
```

#### 📈 Performance Benchmarks
```bash
./scripts/run-enhanced-vm-pool-tests.sh performance
```

#### 📊 Elasticsearch Logging Tests
```bash
./scripts/run-enhanced-vm-pool-tests.sh elasticsearch
```

#### 🎯 All Enhanced VM Pool Tests
```bash
./scripts/run-enhanced-vm-pool-tests.sh all
```

### 2. Binary Analysis Workflow Tests

#### Basic Setup Verification
```bash
# Run basic integration test to verify setup
cd tests/integration
python run_basic_test.py
```

#### Full Integration Test Suite
```bash
# Run complete integration tests (2 Ubuntu + 2 Windows apps)
python run_integration_tests.py
```

#### Individual Test Categories
```bash
# Test application downloads and MinIO operations
nix-shell --run "pytest test_app_downloader.py -v"

# Test VM injection workflow
nix-shell --run "pytest test_vm_injection.py -v -m slow"

# Test benchmark persistence
nix-shell --run "pytest test_benchmark_persistence.py -v"

# Test setup verification
nix-shell --run "pytest test_setup_verification.py -v"
```

## 📁 File Structure

```
tests/integration/
├── README.md                      # This file
├── .env.test                      # Test environment configuration
├── run_basic_test.py              # Basic setup verification
├── run_integration_tests.py       # Full integration test runner
├── test_app_downloader.py         # Download and MinIO tests
├── test_vm_injection.py           # VM injection and ELK tests
├── test_benchmark_persistence.py  # Benchmark storage tests
└── test_setup_verification.py     # Setup verification tests

tests/data/
├── test_applications.json         # Application definitions
└── benchmarks.json                # Persistent benchmark data
```

## ⚙️ Configuration

### Environment Variables (.env.test)

**Core Settings:**
```bash
TEST_MODE=true
DEBUG=true
VM_CAPTURE_TIMEOUT_MINUTES=5
```

**MinIO Configuration:**
```bash
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_TEST_BUCKET_PREFIX=turdparty-test
```

**VM Configuration:**
```bash
VAGRANT_GRPC_PORT=40000
VBOX_MEMORY_MB=2048
VBOX_CPUS=2
DISABLE_VM_NETWORKING=true
DISABLE_VM_SHARED_FOLDERS=true
```

**ELK Configuration:**
```bash
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX_PREFIX=turdparty-test
```

## 🧪 Test Categories

### 1. Application Downloader Tests
- Download Linux and Windows applications
- Generate Blake3 and SHA256 hashes
- Upload to MinIO with UUID metadata
- Verify file integrity and metadata

### 2. VM Injection Tests
- Create isolated Ubuntu and Windows VMs
- Inject downloaded files into VMs
- Execute silent installations
- Capture installation footprints
- Store results in ELK stack

### 3. Benchmark Persistence Tests
- Record download/upload performance
- Track VM boot and installation times
- Calculate application performance rankings
- Maintain persistent benchmark history

### 4. Setup Verification Tests
- Verify all dependencies are available
- Check configuration validity
- Test service connectivity
- Validate test data structure

## 📊 Benchmark Data

### Tracked Metrics
- **Download Performance**: Speed, file size, success rate
- **Upload Performance**: MinIO upload speed and reliability
- **VM Performance**: Boot time, injection time, installation time
- **Application Performance**: Installation success rate, footprint size
- **Platform Comparison**: Ubuntu vs Windows performance

### Benchmark Persistence
```json
{
  "benchmarks": {
    "download_times": [...],
    "upload_times": [...],
    "vm_boot_times": [...],
    "install_times": [...]
  },
  "platform_benchmarks": {
    "ubuntu": {...},
    "windows": {...}
  },
  "application_benchmarks": {
    "firefox": {...},
    "chrome": {...}
  }
}
```

## 🔒 Security Features

### VM Isolation
- **Networking disabled** - VMs cannot access external networks
- **Shared folders disabled** - No host filesystem access
- **Sandbox mode** - Complete isolation from host system
- **Automatic cleanup** - VMs destroyed after each test

### File Safety
- **No malware scanning** - Files downloaded as-is for testing
- **Temporary storage** - Downloads cleaned up automatically
- **Isolated execution** - Applications run only in VMs

## 🎯 Test Execution Flow

### Complete Workflow (per application)
1. **Download** application from official source
2. **Calculate** Blake3 and SHA256 hashes
3. **Upload** to MinIO with UUID metadata
4. **Create** isolated VM (Ubuntu or Windows)
5. **Inject** file into VM filesystem
6. **Install** application silently
7. **Capture** installation footprint for 5 minutes
8. **Extract** installation files back to MinIO
9. **Store** all data in ELK stack
10. **Destroy** VM and cleanup resources

### Performance Targets
- **VM Boot Time**: < 2 minutes
- **File Injection**: < 30 seconds
- **Installation Time**: < 5 minutes (timeout)
- **Data Capture**: 5 minutes (configurable)
- **Total Test Time**: < 10 minutes per application

## 🐛 Troubleshooting

### Common Issues

**VM Creation Fails:**
```bash
# Check VirtualBox status
VBoxManage --version

# Check Vagrant status
vagrant --version

# Check available VM boxes
vagrant box list
```

**MinIO Connection Fails:**
```bash
# Verify MinIO is running
curl http://localhost:9000/minio/health/live

# Check MinIO credentials in .env.test
```

**Download Timeouts:**
```bash
# Increase timeout in .env.test
DOWNLOAD_TIMEOUT_SECONDS=600

# Check network connectivity
curl -I https://download.mozilla.org/
```

**ELK Connection Issues:**
```bash
# Verify Elasticsearch is running
curl http://localhost:9200/_cluster/health

# Check ELK configuration in .env.test
```

### Debug Mode
```bash
# Run with debug output
DEBUG=true python run_integration_tests.py

# Run individual test with verbose output
pytest test_vm_injection.py::TestVMInjection::test_ubuntu_vm_injection_workflow -v -s
```

## 📈 Performance Monitoring

### Real-time Monitoring
- **ELK Dashboard**: View test results in Kibana
- **Benchmark Trends**: Track performance over time
- **Success Rates**: Monitor installation success by platform
- **Resource Usage**: VM memory and CPU utilisation

### Benchmark Analysis
```bash
# View current benchmark statistics
python -c "
from tests.integration.test_benchmark_persistence import BenchmarkManager
from tests.conftest import load_test_config
bm = BenchmarkManager(load_test_config())
stats = bm.get_benchmark_statistics()
print(json.dumps(stats, indent=2))
"
```

## 🤝 Contributing

### Adding New Applications
1. Edit `tests/data/test_applications.json`
2. Add application with required fields:
   - `name`, `description`, `url`, `filename`, `type`, `install_command`
3. Test with basic integration test first
4. Run full integration test suite

### Adding New Platforms
1. Extend `VagrantVMManager` class
2. Add platform-specific Vagrantfile generation
3. Update benchmark tracking
4. Add platform-specific tests

### Performance Optimisation
1. Monitor benchmark trends
2. Identify bottlenecks in test execution
3. Optimise VM configurations
4. Improve parallel execution

---

**🎯 Ready to test the complete TurdParty workflow with real VMs and applications!**
