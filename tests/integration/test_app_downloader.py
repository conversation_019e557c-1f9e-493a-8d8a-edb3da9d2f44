"""
TurdParty Application Downloader and MinIO Integration Tests

This module handles downloading test applications and uploading them to MinIO
with proper UUID and Blake3 hash metadata.
"""

import hashlib
import json
from pathlib import Path
import time
import uuid

import blake3
import httpx
from minio import Minio
from minio.error import S3Error, InvalidResponseError
import pytest
import sys
import logging

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))
from utils.service_urls import ServiceURLManager
from tests.conftest import load_test_config

# Configure testing logger
test_logger = logging.getLogger('turdparty.testing')
test_logger.setLevel(logging.INFO)
if not test_logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('🧪 [TESTING] %(asctime)s - %(message)s')
    handler.setFormatter(formatter)
    test_logger.addHandler(handler)


class ApplicationDownloader:
    """Downloads and manages test applications."""

    def __init__(self, config: dict):
        self.config = config
        self.download_dir = Path(config.get('TEST_DOWNLOAD_DIR', '/tmp/turdparty-test-downloads'))
        self.download_dir.mkdir(parents=True, exist_ok=True)
        self.timeout = int(config.get('DOWNLOAD_TIMEOUT_SECONDS', 300))

    async def download_file(self, url: str, filename: str) -> tuple[Path, dict]:
        """Download a file and return path and metadata."""
        start_time = time.time()
        file_path = self.download_dir / filename

        # Skip if already downloaded
        if file_path.exists():
            return file_path, self._get_file_metadata(file_path, time.time() - start_time)

        async with httpx.AsyncClient(timeout=self.timeout) as client, \
                   client.stream('GET', url, follow_redirects=True) as response:
                response.raise_for_status()

                with file_path.open('wb') as f:
                    async for chunk in response.aiter_bytes(chunk_size=8192):
                        f.write(chunk)

        download_time = time.time() - start_time
        metadata = self._get_file_metadata(file_path, download_time)

        return file_path, metadata

    def _get_file_metadata(self, file_path: Path, download_time: float) -> dict:
        """Generate file metadata including Blake3 hash."""
        file_size = file_path.stat().st_size

        # Calculate Blake3 hash
        blake3_hasher = blake3.blake3()
        with file_path.open('rb') as f:
            while chunk := f.read(8192):
                blake3_hasher.update(chunk)
        blake3_hash = blake3_hasher.hexdigest()

        # Calculate SHA256 for comparison
        sha256_hasher = hashlib.sha256()
        with file_path.open('rb') as f:
            while chunk := f.read(8192):
                sha256_hasher.update(chunk)
        sha256_hash = sha256_hasher.hexdigest()

        return {
            'file_uuid': str(uuid.uuid4()),
            'filename': file_path.name,
            'file_size': file_size,
            'blake3_hash': blake3_hash,
            'sha256_hash': sha256_hash,
            'download_time_seconds': download_time,
            'download_timestamp': time.time(),
            'file_path': str(file_path)
        }


class MinIOTestManager:
    """Manages MinIO operations for testing with central URL management."""

    def __init__(self, config: dict):
        self.config = config

        # Use ServiceURLManager for MinIO configuration with Traefik URLs
        self.url_manager = ServiceURLManager('development')  # Use development for Traefik URLs

        # Get MinIO service URL and parse endpoint
        minio_url = self.url_manager.get_service_url('minio')
        # Extract endpoint from URL (remove protocol)
        endpoint = minio_url.replace('http://', '').replace('https://', '')

        # Handle boolean conversion properly
        secure = config.get('MINIO_SECURE', False)
        if isinstance(secure, str):
            secure = secure.lower() == 'true'

        test_logger.info(f"Connecting to MinIO at {endpoint} for testing")

        self.client = Minio(
            endpoint=endpoint,
            access_key=config.get('MINIO_ACCESS_KEY', 'minioadmin'),
            secret_key=config.get('MINIO_SECRET_KEY', 'minioadmin'),
            secure=secure
        )

        # Use dedicated testing bucket prefix to avoid conflicts
        self.bucket_prefix = 'turdparty-testing'
        self.test_buckets: list[str] = []

        test_logger.info(f"MinIO Test Manager initialized with bucket prefix: {self.bucket_prefix}")

    def create_test_bucket(self) -> str:
        """Create a test bucket with UUID for testing purposes."""
        bucket_name = f"{self.bucket_prefix}-{uuid.uuid4().hex[:8]}"

        test_logger.info(f"Creating testing bucket: {bucket_name}")

        try:
            if not self.client.bucket_exists(bucket_name):
                self.client.make_bucket(bucket_name)
                test_logger.info(f"✅ Testing bucket created: {bucket_name}")
            else:
                test_logger.info(f"✅ Testing bucket already exists: {bucket_name}")

            self.test_buckets.append(bucket_name)
            return bucket_name
        except S3Error as e:
            test_logger.error(f"❌ Failed to create testing bucket {bucket_name}: {e}")
            raise Exception(f"Failed to create bucket {bucket_name}: {e}") from e

    def upload_file_with_metadata(self, bucket_name: str, file_path: Path, metadata: dict) -> str:
        """Upload file to MinIO testing bucket with metadata."""
        object_name = f"testing/{metadata['file_uuid']}/{file_path.name}"

        test_logger.info(f"Uploading test file to bucket {bucket_name}: {file_path.name}")

        # Prepare metadata for MinIO with testing markers
        minio_metadata = {
            'file-uuid': metadata['file_uuid'],
            'blake3-hash': metadata['blake3_hash'],
            'sha256-hash': metadata['sha256_hash'],
            'file-size': str(metadata['file_size']),
            'download-time': str(metadata['download_time_seconds']),
            'upload-timestamp': str(time.time()),
            'testing-marker': 'true',  # Mark as testing data
            'test-session': f"pytest-{int(time.time())}"
        }

        try:
            start_time = time.time()
            self.client.fput_object(
                bucket_name=bucket_name,
                object_name=object_name,
                file_path=str(file_path),
                metadata=minio_metadata
            )
            upload_time = time.time() - start_time

            # Verify upload
            stat = self.client.stat_object(bucket_name, object_name)

            test_logger.info(f"✅ Test file uploaded successfully: {object_name} ({stat.size} bytes)")

            return {
                'object_name': object_name,
                'upload_time_seconds': upload_time,
                'verified_size': stat.size,
                'etag': stat.etag,
                'metadata': stat.metadata
            }

        except S3Error as e:
            test_logger.error(f"❌ Failed to upload test file {file_path.name}: {e}")
            raise Exception(f"Failed to upload {file_path.name}: {e}") from e

    def verify_file_in_bucket(self, bucket_name: str, object_name: str, expected_metadata: dict) -> bool:
        """Verify file exists in bucket with correct metadata."""
        try:
            stat = self.client.stat_object(bucket_name, object_name)

            # Check file size
            if stat.size != expected_metadata['file_size']:
                return False

            # Check Blake3 hash in metadata
            stored_blake3 = stat.metadata.get('blake3-hash')
            if stored_blake3 != expected_metadata['blake3_hash']:
                return False

            # Check UUID in metadata
            stored_uuid = stat.metadata.get('file-uuid')
            return stored_uuid == expected_metadata['file_uuid']

        except S3Error:
            return False

    def cleanup_test_buckets(self):
        """Clean up all testing buckets and objects."""
        test_logger.info(f"Cleaning up {len(self.test_buckets)} testing buckets")

        for bucket_name in self.test_buckets:
            try:
                test_logger.info(f"Cleaning up testing bucket: {bucket_name}")

                # List and delete all objects
                objects = list(self.client.list_objects(bucket_name, recursive=True))
                if objects:
                    test_logger.info(f"Removing {len(objects)} test objects from {bucket_name}")
                    for obj in objects:
                        self.client.remove_object(bucket_name, obj.object_name)
                        test_logger.debug(f"Removed test object: {obj.object_name}")

                # Delete bucket
                self.client.remove_bucket(bucket_name)
                test_logger.info(f"✅ Testing bucket removed: {bucket_name}")

            except S3Error as e:
                test_logger.warning(f"⚠️ Failed to cleanup testing bucket {bucket_name}: {e}")

        self.test_buckets.clear()
        test_logger.info("✅ All testing buckets cleaned up")


@pytest.fixture
def test_config():
    """Load test configuration."""
    return load_test_config()


@pytest.fixture
def app_downloader(test_config):
    """Create application downloader."""
    return ApplicationDownloader(test_config)


@pytest.fixture
def minio_manager(test_config):
    """Create MinIO test manager with central URL configuration."""
    test_logger.info("🧪 Setting up MinIO test manager")

    # Override config with central URL manager settings
    enhanced_config = test_config.copy()

    # Use ServiceURLManager to get proper MinIO configuration with Traefik URLs
    url_manager = ServiceURLManager('development')  # Use Traefik URLs
    minio_url = url_manager.get_service_url('minio')

    # Extract endpoint from URL (remove protocol for MinIO client)
    endpoint = minio_url.replace('http://', '').replace('https://', '')
    enhanced_config['MINIO_ENDPOINT'] = endpoint

    test_logger.info(f"Using MinIO Traefik endpoint from central config: {minio_url} -> {endpoint}")

    manager = MinIOTestManager(enhanced_config)
    yield manager

    # Cleanup after test
    auto_cleanup = enhanced_config.get('AUTO_CLEANUP_MINIO', True)
    if isinstance(auto_cleanup, str):
        auto_cleanup = auto_cleanup.lower() == 'true'

    if auto_cleanup:
        test_logger.info("🧹 Auto-cleanup enabled, cleaning up test buckets")
        manager.cleanup_test_buckets()
    else:
        test_logger.warning("⚠️ Auto-cleanup disabled, test buckets will remain")


@pytest.fixture
def test_applications():
    """Load test applications from JSON."""
    apps_file = Path(__file__).parent.parent / 'data' / 'test_applications.json'
    with apps_file.open() as f:
        return json.load(f)


class TestApplicationDownloader:
    """Test application downloading functionality."""

    @pytest.mark.asyncio
    async def test_download_linux_deb_package(self, app_downloader, test_applications):
        """Test downloading a Linux .deb package."""
        app = test_applications['linux_applications']['deb_packages'][0]  # Firefox

        file_path, metadata = await app_downloader.download_file(
            app['url'],
            app['filename']
        )

        # Verify file was downloaded
        assert file_path.exists()
        assert file_path.stat().st_size > 0

        # Verify metadata
        assert 'file_uuid' in metadata
        assert 'blake3_hash' in metadata
        assert 'sha256_hash' in metadata
        assert metadata['file_size'] > 0
        assert metadata['download_time_seconds'] >= 0

        # Verify Blake3 hash format (64 hex characters)
        assert len(metadata['blake3_hash']) == 64
        assert all(c in '0123456789abcdef' for c in metadata['blake3_hash'])

    @pytest.mark.asyncio
    async def test_download_windows_exe_package(self, app_downloader, test_applications):
        """Test downloading a Windows .exe package."""
        app = test_applications['windows_applications']['exe_packages'][1]  # Chrome

        file_path, metadata = await app_downloader.download_file(
            app['url'],
            app['filename']
        )

        # Verify file was downloaded
        assert file_path.exists()
        assert file_path.stat().st_size > 0

        # Verify metadata structure
        assert 'file_uuid' in metadata
        assert 'blake3_hash' in metadata
        assert metadata['file_size'] > 0


class TestMinIOIntegration:
    """Test MinIO bucket operations."""

    def test_create_test_bucket(self, minio_manager):
        """Test creating a test bucket."""
        try:
            bucket_name = minio_manager.create_test_bucket()

            # Verify bucket name format
            assert bucket_name.startswith(minio_manager.bucket_prefix)
            assert len(bucket_name.split('-')) >= 3  # prefix-test-uuid

            # Verify bucket exists
            assert minio_manager.client.bucket_exists(bucket_name)
        except (InvalidResponseError, S3Error, ConnectionError, Exception) as e:
            if (isinstance(e, InvalidResponseError) and "404" in str(e)) or \
               "Connection" in str(e) or "timeout" in str(e).lower() or "refused" in str(e).lower():
                pytest.skip(f"MinIO service not available: {e}")
            else:
                raise

    @pytest.mark.asyncio
    async def test_upload_file_with_blake3_metadata(self, app_downloader, minio_manager, test_applications):
        """Test uploading file with Blake3 metadata."""
        try:
            # Download a small test file
            app = test_applications['linux_applications']['tar_gz_packages'][2]  # Node.js
            file_path, metadata = await app_downloader.download_file(
                app['url'],
                app['filename']
            )

            # Create test bucket
            bucket_name = minio_manager.create_test_bucket()

            # Upload file
            upload_result = minio_manager.upload_file_with_metadata(
                bucket_name, file_path, metadata
            )

            # Verify upload result
            assert 'object_name' in upload_result
            assert 'upload_time_seconds' in upload_result
            assert upload_result['verified_size'] == metadata['file_size']

            # Verify file in bucket
            assert minio_manager.verify_file_in_bucket(
                bucket_name, upload_result['object_name'], metadata
            )
        except (InvalidResponseError, S3Error, ConnectionError, Exception) as e:
            if (isinstance(e, InvalidResponseError) and "404" in str(e)) or \
               "Connection" in str(e) or "timeout" in str(e).lower() or "refused" in str(e).lower():
                pytest.skip(f"MinIO service not available: {e}")
            else:
                raise

    def test_bucket_cleanup(self, minio_manager):
        """Test bucket cleanup functionality."""
        try:
            # Create multiple test buckets
            bucket1 = minio_manager.create_test_bucket()
            bucket2 = minio_manager.create_test_bucket()

            # Verify buckets exist
            assert minio_manager.client.bucket_exists(bucket1)
            assert minio_manager.client.bucket_exists(bucket2)

            # Cleanup
            minio_manager.cleanup_test_buckets()

            # Verify buckets are removed
            assert not minio_manager.client.bucket_exists(bucket1)
            assert not minio_manager.client.bucket_exists(bucket2)
            assert len(minio_manager.test_buckets) == 0
        except (InvalidResponseError, S3Error, ConnectionError, Exception) as e:
            if (isinstance(e, InvalidResponseError) and "404" in str(e)) or \
               "Connection" in str(e) or "timeout" in str(e).lower() or "refused" in str(e).lower():
                pytest.skip(f"MinIO service not available: {e}")
            else:
                raise
