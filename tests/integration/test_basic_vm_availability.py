"""
Integration Tests for Basic VM Availability

Tests the basic VM availability manager to ensure VMs can be allocated
immediately from pre-created pools.
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta

from services.workers.tasks.basic_vm_availability_manager import (
    availability_manager, maintain_vm_pools, allocate_vm_immediately, get_pool_status
)


class TestBasicVMAvailability:
    """Test basic VM availability functionality."""
    
    @pytest.mark.asyncio
    async def test_pool_status_retrieval(self):
        """Test that pool status can be retrieved."""
        status = availability_manager.get_pool_status()
        
        assert isinstance(status, dict)
        assert len(status) > 0
        
        # Check that all expected templates are present
        expected_templates = [
            "ubuntu:20.04",
            "ubuntu:22.04", 
            "alpine:latest",
            "10Baht/windows10-turdparty"
        ]
        
        for template in expected_templates:
            assert template in status
            
            pool_status = status[template]
            assert "ready" in pool_status
            assert "total" in pool_status
            assert "min_ready" in pool_status
            assert "max_total" in pool_status
            assert "needs_provisioning" in pool_status
            assert "can_provision" in pool_status
    
    def test_pool_maintenance_task(self):
        """Test pool maintenance task execution."""
        # Execute maintenance task
        result = maintain_vm_pools.delay()
        maintenance_result = result.get(timeout=60)
        
        assert maintenance_result is not None
        assert "maintenance_completed" in maintenance_result
        assert maintenance_result["maintenance_completed"] is True
        assert "actions_taken" in maintenance_result
        assert "pool_status" in maintenance_result
        
        # Verify actions were logged
        actions = maintenance_result["actions_taken"]
        assert isinstance(actions, list)
        
        # If actions were taken, verify they have the right structure
        for action in actions:
            assert "action" in action
            assert "template" in action
            assert "vm_id" in action
            assert action["action"] == "provision"
    
    def test_vm_allocation_immediate(self):
        """Test immediate VM allocation."""
        template = "alpine:latest"  # Use Alpine as it's lightweight
        
        # First ensure pool has VMs
        maintain_vm_pools.delay().get(timeout=60)
        
        # Wait a bit for VMs to be created
        time.sleep(10)
        
        # Try to allocate a VM
        result = allocate_vm_immediately.delay(template, "test_user")
        allocation_result = result.get(timeout=30)
        
        assert allocation_result is not None
        assert "success" in allocation_result
        assert "message" in allocation_result
        
        if allocation_result["success"]:
            # Immediate allocation succeeded
            assert "vm_id" in allocation_result
            assert "vm" in allocation_result
            assert "allocated_at" in allocation_result
            
            vm_details = allocation_result["vm"]
            assert vm_details["template"] == template
            assert vm_details["status"] == "running"
            
        else:
            # VM is being provisioned
            assert allocation_result.get("status") == "provisioning"
            assert "estimated_wait_minutes" in allocation_result
    
    def test_pool_status_task(self):
        """Test pool status task execution."""
        result = get_pool_status.delay()
        status = result.get(timeout=30)
        
        assert isinstance(status, dict)
        assert len(status) > 0
        
        # Verify status structure
        for template, pool_status in status.items():
            assert isinstance(pool_status, dict)
            assert "ready" in pool_status
            assert "total" in pool_status
            assert "creating" in pool_status
            assert "running" in pool_status
            assert "min_ready" in pool_status
            assert "max_total" in pool_status
    
    def test_multiple_allocations(self):
        """Test multiple VM allocations to verify pool management."""
        template = "ubuntu:20.04"
        
        # Ensure pool is maintained
        maintain_vm_pools.delay().get(timeout=60)
        time.sleep(5)
        
        # Get initial status
        initial_status = availability_manager.get_pool_status()
        initial_ready = initial_status[template]["ready"]
        
        allocations = []
        
        # Try to allocate multiple VMs
        for i in range(min(3, initial_ready)):  # Don't exceed available VMs
            result = allocate_vm_immediately.delay(template, f"test_user_{i}")
            allocation_result = result.get(timeout=30)
            allocations.append(allocation_result)
        
        # Check results
        successful_allocations = [a for a in allocations if a.get("success")]
        
        # At least some allocations should succeed if we had ready VMs
        if initial_ready > 0:
            assert len(successful_allocations) > 0
        
        # Verify each successful allocation
        for allocation in successful_allocations:
            assert "vm_id" in allocation
            assert "vm" in allocation
            vm_details = allocation["vm"]
            assert vm_details["template"] == template
    
    def test_pool_configuration(self):
        """Test that pool configurations are properly set."""
        configs = availability_manager.pool_configs
        
        # Verify all expected templates have configurations
        expected_templates = [
            "ubuntu:20.04",
            "ubuntu:22.04",
            "alpine:latest", 
            "10Baht/windows10-turdparty"
        ]
        
        for template in expected_templates:
            assert template in configs
            
            config = configs[template]
            assert config.template == template
            assert config.min_ready > 0
            assert config.max_total >= config.min_ready
            assert config.vm_type in ["docker", "vagrant"]
            assert config.memory_mb > 0
            assert config.cpus > 0
    
    def test_vm_provisioning(self):
        """Test VM provisioning functionality."""
        template = "alpine:latest"  # Use lightweight template
        
        # Provision a VM
        vm_id = availability_manager.provision_vm(template)
        
        assert vm_id is not None
        assert isinstance(vm_id, str)
        
        # Wait a bit and check if VM appears in database
        time.sleep(5)
        
        status = availability_manager.get_pool_status()
        template_status = status[template]
        
        # Should have at least one VM (creating or ready)
        assert template_status["total"] > 0


class TestBasicVMAvailabilityAPI:
    """Test the API integration for basic VM availability."""
    
    @pytest.mark.asyncio
    async def test_api_integration_placeholder(self):
        """Placeholder for API integration tests."""
        # This would test the actual API endpoints
        # For now, we'll just verify the manager works
        
        status = availability_manager.get_pool_status()
        assert isinstance(status, dict)
        
        # In a full test, you would:
        # 1. Make HTTP requests to the API endpoints
        # 2. Verify responses match expected schemas
        # 3. Test error handling
        # 4. Test authentication and authorization
        
        # Example of what the test would look like:
        # async with httpx.AsyncClient() as client:
        #     response = await client.get("/api/v1/vm-allocation/pools/status")
        #     assert response.status_code == 200
        #     data = response.json()
        #     assert isinstance(data, dict)


if __name__ == "__main__":
    # Run a simple test
    print("Running basic VM availability test...")
    
    # Test pool status
    status = availability_manager.get_pool_status()
    print(f"Pool status: {status}")
    
    # Test maintenance
    print("Testing pool maintenance...")
    result = maintain_vm_pools.delay()
    maintenance_result = result.get(timeout=60)
    print(f"Maintenance result: {maintenance_result}")
    
    print("Basic test completed!")
