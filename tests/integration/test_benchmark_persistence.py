"""
TurdParty Benchmark Persistence System

This module handles persistent storage and analysis of benchmark data
from VM injection tests.
"""

import json
from pathlib import Path
import statistics
import time

import pytest

from tests.conftest import load_test_config


class BenchmarkManager:
    """Manages benchmark data persistence and analysis."""

    def __init__(self, config: dict):
        self.config = config
        self.benchmark_file = Path(config.get('TEST_BENCHMARK_FILE', 'tests/data/benchmarks.json'))
        self.benchmark_file.parent.mkdir(parents=True, exist_ok=True)

        # Initialize benchmark file if it doesn't exist
        if not self.benchmark_file.exists():
            self._initialize_benchmark_file()

    def _initialize_benchmark_file(self):
        """Initialize empty benchmark file."""
        initial_data = {
            'metadata': {
                'created': time.time(),
                'version': '1.0.0',
                'description': 'TurdParty VM injection benchmark data'
            },
            'benchmarks': {
                'download_times': [],
                'upload_times': [],
                'vm_boot_times': [],
                'injection_times': [],
                'install_times': [],
                'capture_volumes': []
            },
            'platform_benchmarks': {
                'ubuntu': {
                    'install_times': [],
                    'footprint_sizes': [],
                    'success_rates': []
                },
                'windows': {
                    'install_times': [],
                    'footprint_sizes': [],
                    'success_rates': []
                }
            },
            'application_benchmarks': {}
        }

        with self.benchmark_file.open('w') as f:
            json.dump(initial_data, f, indent=2)

    def load_benchmarks(self) -> dict:
        """Load existing benchmark data."""
        try:
            with self.benchmark_file.open() as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            self._initialize_benchmark_file()
            return self.load_benchmarks()

    def save_benchmarks(self, data: dict):
        """Save benchmark data to file."""
        data['metadata']['last_updated'] = time.time()

        with self.benchmark_file.open('w') as f:
            json.dump(data, f, indent=2)

    def record_download_benchmark(self, app_name: str, file_size: int, download_time: float):
        """Record download benchmark."""
        data = self.load_benchmarks()

        benchmark_entry = {
            'app_name': app_name,
            'file_size_bytes': file_size,
            'download_time_seconds': download_time,
            'download_speed_mbps': (file_size / (1024 * 1024)) / download_time if download_time > 0 else 0,
            'timestamp': time.time()
        }

        data['benchmarks']['download_times'].append(benchmark_entry)
        self.save_benchmarks(data)

    def record_upload_benchmark(self, app_name: str, file_size: int, upload_time: float):
        """Record upload benchmark."""
        data = self.load_benchmarks()

        benchmark_entry = {
            'app_name': app_name,
            'file_size_bytes': file_size,
            'upload_time_seconds': upload_time,
            'upload_speed_mbps': (file_size / (1024 * 1024)) / upload_time if upload_time > 0 else 0,
            'timestamp': time.time()
        }

        data['benchmarks']['upload_times'].append(benchmark_entry)
        self.save_benchmarks(data)

    def record_vm_benchmark(self, platform: str, boot_time: float, injection_time: float,
                           install_time: float, install_success: bool, footprint_size: int):
        """Record VM operation benchmark."""
        data = self.load_benchmarks()

        # General benchmarks
        data['benchmarks']['vm_boot_times'].append({
            'platform': platform,
            'boot_time_seconds': boot_time,
            'timestamp': time.time()
        })

        data['benchmarks']['injection_times'].append({
            'platform': platform,
            'injection_time_seconds': injection_time,
            'timestamp': time.time()
        })

        data['benchmarks']['install_times'].append({
            'platform': platform,
            'install_time_seconds': install_time,
            'install_success': install_success,
            'timestamp': time.time()
        })

        # Platform-specific benchmarks
        if platform in data['platform_benchmarks']:
            data['platform_benchmarks'][platform]['install_times'].append({
                'install_time_seconds': install_time,
                'install_success': install_success,
                'timestamp': time.time()
            })

            data['platform_benchmarks'][platform]['footprint_sizes'].append({
                'footprint_size_files': footprint_size,
                'timestamp': time.time()
            })

            # Calculate success rate
            recent_installs = data['platform_benchmarks'][platform]['install_times'][-10:]  # Last 10
            success_count = sum(1 for install in recent_installs if install['install_success'])
            success_rate = success_count / len(recent_installs) if recent_installs else 0

            data['platform_benchmarks'][platform]['success_rates'].append({
                'success_rate': success_rate,
                'sample_size': len(recent_installs),
                'timestamp': time.time()
            })

        self.save_benchmarks(data)

    def record_application_benchmark(self, app_name: str, platform: str,
                                   install_time: float, install_success: bool,
                                   footprint_data: dict):
        """Record application-specific benchmark."""
        data = self.load_benchmarks()

        if app_name not in data['application_benchmarks']:
            data['application_benchmarks'][app_name] = {
                'ubuntu': [],
                'windows': []
            }

        if platform in data['application_benchmarks'][app_name]:
            benchmark_entry = {
                'install_time_seconds': install_time,
                'install_success': install_success,
                'filesystem_changes': len(footprint_data.get('filesystem_changes', [])),
                'running_processes': len(footprint_data.get('running_processes', [])),
                'timestamp': time.time()
            }

            data['application_benchmarks'][app_name][platform].append(benchmark_entry)

        self.save_benchmarks(data)

    def get_benchmark_statistics(self) -> dict:
        """Calculate benchmark statistics."""
        data = self.load_benchmarks()
        stats = {}

        # Download statistics
        download_times = [b['download_time_seconds'] for b in data['benchmarks']['download_times']]
        if download_times:
            stats['download'] = {
                'mean_seconds': statistics.mean(download_times),
                'median_seconds': statistics.median(download_times),
                'min_seconds': min(download_times),
                'max_seconds': max(download_times),
                'count': len(download_times)
            }

        # Upload statistics
        upload_times = [b['upload_time_seconds'] for b in data['benchmarks']['upload_times']]
        if upload_times:
            stats['upload'] = {
                'mean_seconds': statistics.mean(upload_times),
                'median_seconds': statistics.median(upload_times),
                'min_seconds': min(upload_times),
                'max_seconds': max(upload_times),
                'count': len(upload_times)
            }

        # VM boot statistics
        vm_boot_times = [b['boot_time_seconds'] for b in data['benchmarks']['vm_boot_times']]
        if vm_boot_times:
            stats['vm_boot'] = {
                'mean_seconds': statistics.mean(vm_boot_times),
                'median_seconds': statistics.median(vm_boot_times),
                'min_seconds': min(vm_boot_times),
                'max_seconds': max(vm_boot_times),
                'count': len(vm_boot_times)
            }

        # Install statistics by platform
        for platform in ['ubuntu', 'windows']:
            platform_installs = data['platform_benchmarks'].get(platform, {}).get('install_times', [])
            if platform_installs:
                install_times = [i['install_time_seconds'] for i in platform_installs]
                success_count = sum(1 for i in platform_installs if i['install_success'])

                stats[f'{platform}_install'] = {
                    'mean_seconds': statistics.mean(install_times),
                    'median_seconds': statistics.median(install_times),
                    'min_seconds': min(install_times),
                    'max_seconds': max(install_times),
                    'success_rate': success_count / len(platform_installs),
                    'count': len(platform_installs)
                }

        return stats

    def get_application_performance_ranking(self) -> list[dict]:
        """Get application performance ranking."""
        data = self.load_benchmarks()
        rankings = []

        for app_name, platforms in data['application_benchmarks'].items():
            app_stats = {'app_name': app_name}

            for platform in ['ubuntu', 'windows']:
                if platforms.get(platform):
                    installs = platforms[platform]
                    install_times = [i['install_time_seconds'] for i in installs]
                    success_count = sum(1 for i in installs if i['install_success'])

                    app_stats[f'{platform}_mean_time'] = statistics.mean(install_times)
                    app_stats[f'{platform}_success_rate'] = success_count / len(installs)
                    app_stats[f'{platform}_count'] = len(installs)
                else:
                    app_stats[f'{platform}_mean_time'] = None
                    app_stats[f'{platform}_success_rate'] = None
                    app_stats[f'{platform}_count'] = 0

            rankings.append(app_stats)

        # Sort by overall performance (combination of speed and success rate)
        def performance_score(app):
            ubuntu_score = 0
            windows_score = 0

            if app['ubuntu_mean_time'] and app['ubuntu_success_rate']:
                ubuntu_score = app['ubuntu_success_rate'] / app['ubuntu_mean_time']

            if app['windows_mean_time'] and app['windows_success_rate']:
                windows_score = app['windows_success_rate'] / app['windows_mean_time']

            return ubuntu_score + windows_score

        rankings.sort(key=performance_score, reverse=True)
        return rankings

    def cleanup_old_benchmarks(self, days_to_keep: int = 30):
        """Remove benchmark data older than specified days."""
        data = self.load_benchmarks()
        cutoff_time = time.time() - (days_to_keep * 24 * 3600)

        # Clean up general benchmarks
        for benchmark_type in data['benchmarks']:
            if isinstance(data['benchmarks'][benchmark_type], list):
                data['benchmarks'][benchmark_type] = [
                    b for b in data['benchmarks'][benchmark_type]
                    if b.get('timestamp', 0) > cutoff_time
                ]

        # Clean up platform benchmarks
        for platform in data['platform_benchmarks']:
            for metric_type in data['platform_benchmarks'][platform]:
                if isinstance(data['platform_benchmarks'][platform][metric_type], list):
                    data['platform_benchmarks'][platform][metric_type] = [
                        b for b in data['platform_benchmarks'][platform][metric_type]
                        if b.get('timestamp', 0) > cutoff_time
                    ]

        # Clean up application benchmarks
        for app_name in data['application_benchmarks']:
            for platform in data['application_benchmarks'][app_name]:
                if isinstance(data['application_benchmarks'][app_name][platform], list):
                    data['application_benchmarks'][app_name][platform] = [
                        b for b in data['application_benchmarks'][app_name][platform]
                        if b.get('timestamp', 0) > cutoff_time
                    ]

        self.save_benchmarks(data)


@pytest.fixture
def benchmark_manager():
    """Create benchmark manager."""
    config = load_test_config()
    return BenchmarkManager(config)


class TestBenchmarkPersistence:
    """Test benchmark persistence functionality."""

    def test_benchmark_file_initialization(self, benchmark_manager):
        """Test benchmark file is properly initialized."""
        data = benchmark_manager.load_benchmarks()

        assert 'metadata' in data
        assert 'benchmarks' in data
        assert 'platform_benchmarks' in data
        assert 'application_benchmarks' in data

        # Check required benchmark categories
        assert 'download_times' in data['benchmarks']
        assert 'upload_times' in data['benchmarks']
        assert 'vm_boot_times' in data['benchmarks']
        assert 'install_times' in data['benchmarks']

        # Check platform categories
        assert 'ubuntu' in data['platform_benchmarks']
        assert 'windows' in data['platform_benchmarks']

    def test_record_download_benchmark(self, benchmark_manager):
        """Test recording download benchmark."""
        benchmark_manager.record_download_benchmark(
            app_name='test_app',
            file_size=1024 * 1024,  # 1MB
            download_time=5.0
        )

        data = benchmark_manager.load_benchmarks()
        download_benchmarks = data['benchmarks']['download_times']

        assert len(download_benchmarks) > 0
        latest = download_benchmarks[-1]
        assert latest['app_name'] == 'test_app'
        assert latest['file_size_bytes'] == 1024 * 1024
        assert latest['download_time_seconds'] == 5.0
        assert latest['download_speed_mbps'] > 0

    def test_record_vm_benchmark(self, benchmark_manager):
        """Test recording VM benchmark."""
        benchmark_manager.record_vm_benchmark(
            platform='ubuntu',
            boot_time=30.0,
            injection_time=2.0,
            install_time=15.0,
            install_success=True,
            footprint_size=10
        )

        data = benchmark_manager.load_benchmarks()

        # Check general benchmarks
        assert len(data['benchmarks']['vm_boot_times']) > 0
        assert len(data['benchmarks']['injection_times']) > 0
        assert len(data['benchmarks']['install_times']) > 0

        # Check platform-specific benchmarks
        ubuntu_data = data['platform_benchmarks']['ubuntu']
        assert len(ubuntu_data['install_times']) > 0
        assert len(ubuntu_data['footprint_sizes']) > 0
        assert len(ubuntu_data['success_rates']) > 0

    def test_benchmark_statistics(self, benchmark_manager):
        """Test benchmark statistics calculation."""
        # Record some test data
        benchmark_manager.record_download_benchmark('app1', 1000000, 5.0)
        benchmark_manager.record_download_benchmark('app2', 2000000, 8.0)
        benchmark_manager.record_upload_benchmark('app1', 1000000, 3.0)

        stats = benchmark_manager.get_benchmark_statistics()

        assert 'download' in stats
        assert 'upload' in stats

        # Check download stats
        download_stats = stats['download']
        assert download_stats['count'] >= 2
        assert download_stats['mean_seconds'] > 0
        assert download_stats['min_seconds'] <= download_stats['max_seconds']

    def test_application_performance_ranking(self, benchmark_manager):
        """Test application performance ranking."""
        # Record some application benchmarks
        benchmark_manager.record_application_benchmark(
            'fast_app', 'ubuntu', 10.0, True, {'filesystem_changes': ['file1']}
        )
        benchmark_manager.record_application_benchmark(
            'slow_app', 'ubuntu', 30.0, True, {'filesystem_changes': ['file1', 'file2']}
        )

        rankings = benchmark_manager.get_application_performance_ranking()

        assert len(rankings) >= 2
        assert all('app_name' in app for app in rankings)
        assert all('ubuntu_mean_time' in app for app in rankings)
        assert all('ubuntu_success_rate' in app for app in rankings)

    def test_benchmark_cleanup(self, benchmark_manager):
        """Test old benchmark cleanup."""
        # Record a benchmark with old timestamp
        data = benchmark_manager.load_benchmarks()
        old_timestamp = time.time() - (40 * 24 * 3600)  # 40 days ago

        data['benchmarks']['download_times'].append({
            'app_name': 'old_app',
            'file_size_bytes': 1000,
            'download_time_seconds': 1.0,
            'download_speed_mbps': 1.0,
            'timestamp': old_timestamp
        })

        benchmark_manager.save_benchmarks(data)

        # Cleanup benchmarks older than 30 days
        benchmark_manager.cleanup_old_benchmarks(days_to_keep=30)

        # Verify old data was removed
        updated_data = benchmark_manager.load_benchmarks()
        download_times = updated_data['benchmarks']['download_times']

        # Should not contain the old benchmark
        old_benchmarks = [b for b in download_times if b.get('timestamp', 0) == old_timestamp]
        assert len(old_benchmarks) == 0
