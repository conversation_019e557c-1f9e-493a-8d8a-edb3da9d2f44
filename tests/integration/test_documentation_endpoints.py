"""
Integration tests for TurdParty documentation endpoints via Traefik
Tests all documentation URLs and content accessibility
"""

import pytest
import requests
import time
from urllib.parse import urljoin
import json

try:
    from bs4 import BeautifulSoup
    HAS_BS4 = True
except ImportError:
    HAS_BS4 = False


class TestDocumentationEndpoints:
    """Test documentation accessibility through Traefik routing"""
    
    @pytest.fixture(scope="class")
    def base_urls(self):
        """Base URLs for different services"""
        try:
            from utils.service_urls import ServiceURLManager
            url_manager = ServiceURLManager('development')
            frontend_url = url_manager.get_service_url('frontend')
            api_url = url_manager.get_service_url('api')
            return {
                'docs': f"{frontend_url}/docs",  # Docs served by frontend
                'api': api_url,
                'frontend': frontend_url,
                'status': 'http://status.turdparty.localhost',
                'kibana': 'http://kibana.turdparty.localhost'
            }
        except (ImportError, ValueError):
            # Fallback URLs
            return {
                'docs': 'http://frontend.turdparty.localhost/docs',
                'api': 'http://localhost:8000',
                'frontend': 'http://frontend.turdparty.localhost',
                'status': 'http://status.turdparty.localhost',
                'kibana': 'http://kibana.turdparty.localhost'
            }
    
    @pytest.fixture(scope="class")
    def session(self):
        """HTTP session with reasonable timeouts"""
        session = requests.Session()
        session.timeout = 10
        return session
    
    def test_docs_main_page_accessible(self, base_urls, session):
        """Test main documentation page is accessible"""
        url = base_urls['docs']

        try:
            response = session.get(url)
            if response.status_code == 200:
                # Check content type
                assert 'text/html' in response.headers.get('content-type', ''), \
                    "Documentation should return HTML content"

                # Check for basic content
                content = response.text.lower()
                assert 'turdparty' in content, "Documentation should contain TurdParty branding"
                assert 'api' in content, "Documentation should mention API"
            elif response.status_code == 404:
                # Documentation service not available in test environment
                pytest.skip(f"Documentation service not available at {url} (404)")
            else:
                assert False, f"Unexpected status code {response.status_code} for {url}"
        except requests.exceptions.RequestException as e:
            pytest.skip(f"Documentation service not reachable at {url}: {e}")
    
    @pytest.mark.skipif(not HAS_BS4, reason="BeautifulSoup not available")
    def test_docs_dark_mode_default(self, base_urls, session):
        """Test documentation defaults to dark mode"""
        url = base_urls['docs']

        try:
            response = session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Check for dark theme attribute
                html_tag = soup.find('html')
                assert html_tag is not None, "HTML tag should exist"

                theme_attr = html_tag.get('data-theme')
                assert theme_attr == 'dark', f"Expected dark theme, got {theme_attr}"
            elif response.status_code == 404:
                pytest.skip(f"Documentation service not available at {url} (404)")
            else:
                assert False, f"Unexpected status code {response.status_code} for {url}"
        except requests.exceptions.RequestException as e:
            pytest.skip(f"Documentation service not reachable at {url}: {e}")
    
    def test_docs_mermaid_integration(self, base_urls, session):
        """Test Mermaid diagrams are included"""
        url = base_urls['docs']

        try:
            response = session.get(url)
            if response.status_code == 200:
                content = response.text

                # Check for Mermaid script inclusion
                assert 'mermaid' in content.lower(), "Mermaid should be included"

                # Check for system architecture diagram
                assert 'system-architecture' in content, "System architecture diagram should be present"

                # Check for Mermaid diagram content
                assert 'graph TB' in content or 'graph TD' in content, \
                    "Mermaid diagram syntax should be present"
            elif response.status_code == 404:
                pytest.skip(f"Documentation service not available at {url} (404)")
            else:
                assert False, f"Unexpected status code {response.status_code} for {url}"
        except requests.exceptions.RequestException as e:
            pytest.skip(f"Documentation service not reachable at {url}: {e}")
    
    def test_docs_api_section_accessible(self, base_urls, session):
        """Test API documentation section is accessible"""
        url = urljoin(base_urls['docs'], '/api/')

        try:
            response = session.get(url)
            if response.status_code == 200:
                # Check for Swagger UI content
                content = response.text.lower()
                assert 'swagger' in content, "API docs should include Swagger UI"
                assert 'openapi' in content, "API docs should reference OpenAPI spec"
            elif response.status_code == 404:
                pytest.skip(f"API documentation service not available at {url} (404)")
            else:
                assert False, f"Unexpected status code {response.status_code} for {url}"
        except requests.exceptions.RequestException as e:
            pytest.skip(f"API documentation service not reachable at {url}: {e}")
    
    def test_docs_openapi_spec_available(self, base_urls, session):
        """Test OpenAPI specification is available"""
        url = urljoin(base_urls['docs'], '/api/openapi.json')

        try:
            response = session.get(url)
            if response.status_code == 200:
                # Check content type
                assert 'application/json' in response.headers.get('content-type', ''), \
                    "OpenAPI spec should be JSON"

                # Validate JSON structure
                spec = response.json()
                assert 'openapi' in spec, "Should be valid OpenAPI specification"
                assert 'info' in spec, "OpenAPI spec should have info section"
                assert 'paths' in spec, "OpenAPI spec should have paths section"
            elif response.status_code == 404:
                pytest.skip(f"OpenAPI spec service not available at {url} (404)")
            else:
                assert False, f"Unexpected status code {response.status_code} for {url}"
        except requests.exceptions.RequestException as e:
            pytest.skip(f"OpenAPI spec service not reachable at {url}: {e}")
    
    def test_docs_static_assets_accessible(self, base_urls, session):
        """Test static assets (CSS, JS) are accessible"""
        base_url = base_urls['docs']

        try:
            # Test theme toggle JavaScript
            js_url = urljoin(base_url, '/_static/theme-toggle.js')
            response = session.get(js_url)
            if response.status_code == 200:
                # Test RAG status JavaScript
                rag_url = urljoin(base_url, '/_static/rag-status.js')
                response = session.get(rag_url)
                if response.status_code == 200:
                    # Check JavaScript content
                    js_content = response.text
                    assert 'TurdPartyStatusIntegration' in js_content, \
                        "RAG status integration should be present"
                elif response.status_code == 404:
                    pytest.skip(f"RAG status JS not available at {rag_url} (404)")
                else:
                    assert False, f"Unexpected status code {response.status_code} for {rag_url}"
            elif response.status_code == 404:
                pytest.skip(f"Theme toggle JS not available at {js_url} (404)")
            else:
                assert False, f"Unexpected status code {response.status_code} for {js_url}"
        except requests.exceptions.RequestException as e:
            pytest.skip(f"Static assets service not reachable: {e}")
    
    def test_docs_responsive_design(self, base_urls, session):
        """Test documentation is mobile-responsive"""
        url = base_urls['docs']

        try:
            # Test with mobile user agent
            headers = {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
            }

            response = session.get(url, headers=headers)
            if response.status_code == 200:
                content = response.text
                assert 'viewport' in content, "Should include viewport meta tag for mobile"
                assert 'width=device-width' in content, "Should set proper viewport width"
            elif response.status_code == 404:
                pytest.skip(f"Documentation service not available at {url} (404)")
            else:
                assert False, f"Unexpected status code {response.status_code} for {url}"
        except requests.exceptions.RequestException as e:
            pytest.skip(f"Documentation service not reachable at {url}: {e}")
    
    @pytest.mark.skipif(not HAS_BS4, reason="BeautifulSoup not available")
    def test_docs_navigation_links(self, base_urls, session):
        """Test navigation links in documentation"""
        url = base_urls['docs']

        try:
            response = session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Find navigation links
                nav_links = soup.find_all('a', class_='nav-card')
                assert len(nav_links) > 0, "Should have navigation cards"

                # Check for essential links
                link_texts = [link.get_text().lower() for link in nav_links]
                assert any('documentation' in text for text in link_texts), \
                    "Should have documentation link"
                assert any('api' in text for text in link_texts), \
                    "Should have API link"
            elif response.status_code == 404:
                pytest.skip(f"Documentation service not available at {url} (404)")
            else:
                assert False, f"Unexpected status code {response.status_code} for {url}"
        except requests.exceptions.RequestException as e:
            pytest.skip(f"Documentation service not reachable at {url}: {e}")
    
    def test_docs_status_integration(self, base_urls, session):
        """Test status integration in documentation"""
        url = base_urls['docs']

        try:
            response = session.get(url)
            if response.status_code == 200:
                content = response.text

                # Check for status checking JavaScript
                assert '/api/v1/status' in content, "Should check API status"
                assert 'status' in content.lower(), "Should reference status"

                # Check for status legend
                assert 'operational' in content.lower(), "Should include operational status"
                assert 'degraded' in content.lower(), "Should include degraded status"
            elif response.status_code == 404:
                pytest.skip(f"Documentation service not available at {url} (404)")
            else:
                assert False, f"Unexpected status code {response.status_code} for {url}"
        except requests.exceptions.RequestException as e:
            pytest.skip(f"Documentation service not reachable at {url}: {e}")
    
    def test_traefik_routing_headers(self, base_urls, session):
        """Test Traefik routing headers are present"""
        url = base_urls['docs']

        try:
            response = session.get(url)
            if response.status_code == 404:
                pytest.skip(f"Documentation service not available at {url} (404)")

            assert response.status_code == 200

            # Check for common reverse proxy headers
            headers = response.headers

            # These headers might be added by Traefik
            expected_headers = ['server', 'content-type', 'content-length']
            for header in expected_headers:
                assert header in headers, f"Expected header {header} not found"
        except requests.exceptions.RequestException as e:
            pytest.skip(f"Documentation service not reachable at {url}: {e}")
    
    def test_docs_error_pages(self, base_urls, session):
        """Test error page handling"""
        # Test 404 page
        url = urljoin(base_urls['docs'], '/nonexistent-page')
        
        response = session.get(url)
        assert response.status_code == 404, "Should return 404 for non-existent pages"
    
    def test_docs_health_endpoint(self, base_urls, session):
        """Test documentation service health endpoint"""
        url = urljoin(base_urls['docs'], '/health')

        try:
            response = session.get(url)
            if response.status_code == 404:
                pytest.skip(f"Documentation health endpoint not available at {url} (404)")

            # Nginx health endpoint should return 200
            assert response.status_code == 200, "Health endpoint should be accessible"
        except requests.exceptions.RequestException as e:
            pytest.skip(f"Documentation health endpoint not reachable at {url}: {e}")
    
    def test_cross_service_links(self, base_urls, session):
        """Test links between different services work"""
        try:
            # Get main docs page
            docs_response = session.get(base_urls['docs'])
            if docs_response.status_code == 404:
                pytest.skip(f"Documentation service not available at {base_urls['docs']} (404)")

            assert docs_response.status_code == 200

            soup = BeautifulSoup(docs_response.text, 'html.parser')

            # Find links to other services
            links = soup.find_all('a', href=True)
            external_links = [
                link['href'] for link in links
                if link['href'].startswith('http') and 'turdparty.localhost' in link['href']
            ]

            # If no external links found, that's okay - just log it
            if len(external_links) == 0:
                print("No external service links found in documentation")
                return

            # Test a few key service links (with timeout handling)
            key_services = [
                'http://kibana.turdparty.localhost',
                'http://status.turdparty.localhost'
            ]

            for service_url in key_services:
                if service_url in str(external_links):
                    try:
                        # Quick connectivity test with short timeout
                        response = session.get(service_url, timeout=5)
                        # Don't assert success as services might not be running
                        # Just verify the URL is reachable
                        print(f"Service {service_url}: {response.status_code}")
                    except requests.exceptions.RequestException as e:
                        print(f"Service {service_url} not reachable: {e}")
        except requests.exceptions.RequestException as e:
            pytest.skip(f"Documentation service not reachable at {base_urls['docs']}: {e}")
    
    def test_docs_content_completeness(self, base_urls, session):
        """Test documentation contains essential content sections"""
        url = base_urls['docs']

        try:
            response = session.get(url)
            if response.status_code == 404:
                pytest.skip(f"Documentation service not available at {url} (404)")

            assert response.status_code == 200

            content = response.text.lower()

            # Check for essential sections
            essential_sections = [
                'overview',
                'api',
                'websocket',
                'vm management',
                'system architecture',
                'quick start'
            ]

            for section in essential_sections:
                assert section in content, f"Documentation should include {section} section"
        except requests.exceptions.RequestException as e:
            pytest.skip(f"Documentation service not reachable at {url}: {e}")
    
    def test_docs_accessibility_features(self, base_urls, session):
        """Test accessibility features in documentation"""
        url = base_urls['docs']

        try:
            response = session.get(url)
            if response.status_code == 404:
                pytest.skip(f"Documentation service not available at {url} (404)")

            assert response.status_code == 200

            soup = BeautifulSoup(response.text, 'html.parser')

            # Check for accessibility features
            assert soup.find('html', {'lang': True}), "Should have language attribute"

            # Check for proper heading structure
            headings = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
            assert len(headings) > 0, "Should have proper heading structure"

            # Check for alt text on images (if any)
            images = soup.find_all('img')
            for img in images:
                assert img.get('alt') is not None, "Images should have alt text"
        except requests.exceptions.RequestException as e:
            pytest.skip(f"Documentation service not reachable at {url}: {e}")
    
    def test_docs_performance_headers(self, base_urls, session):
        """Test performance-related headers"""
        url = base_urls['docs']

        try:
            response = session.get(url)
            if response.status_code == 404:
                pytest.skip(f"Documentation service not available at {url} (404)")

            assert response.status_code == 200

            headers = response.headers

            # Check for caching headers
            cache_headers = ['cache-control', 'etag', 'last-modified']
            cache_present = any(header in headers for header in cache_headers)

            # At least one caching header should be present for performance
            assert cache_present, "Should have caching headers for performance"
        except requests.exceptions.RequestException as e:
            pytest.skip(f"Documentation service not reachable at {url}: {e}")
    
    @pytest.mark.slow
    def test_docs_load_time(self, base_urls, session):
        """Test documentation load time is reasonable"""
        url = base_urls['docs']
        
        start_time = time.time()
        response = session.get(url)
        load_time = time.time() - start_time
        
        assert response.status_code == 200
        assert load_time < 5.0, f"Documentation should load in under 5 seconds, took {load_time:.2f}s"
    
    def test_docs_security_headers(self, base_urls, session):
        """Test security headers are present"""
        url = base_urls['docs']

        try:
            response = session.get(url)
            if response.status_code == 404:
                pytest.skip(f"Documentation service not available at {url} (404)")

            assert response.status_code == 200

            headers = response.headers

            # Check for basic security headers
            security_headers = [
                'x-frame-options',
                'x-content-type-options',
                'x-xss-protection'
            ]

            for header in security_headers:
                if header in headers:
                    print(f"Security header present: {header}")
                # Note: Not asserting as these might be added by Traefik or nginx config
        except requests.exceptions.RequestException as e:
            pytest.skip(f"Documentation service not reachable at {url}: {e}")


if __name__ == "__main__":
    # Run tests with verbose output
    pytest.main([__file__, "-v", "--tb=short"])
