"""
💩🎉TurdParty🎉💩 ECS Logging Integration Tests

This module tests that all containers properly log to Elasticsearch using
ECS (Elastic Common Schema) format with comprehensive debugging context.

Test Categories:
1. ECS Format Compliance
2. Service Identification
3. Correlation ID Tracking
4. Debug Context Availability
5. Log Aggregation and Search
"""

import pytest
import json
import time
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any


class TestECSLogging:
    """Test suite for ECS logging compliance and functionality."""
    
    @pytest.fixture
    def elasticsearch_url(self):
        """Elasticsearch URL for testing."""
        return "http://localhost:9200"
    
    @pytest.fixture
    def kibana_url(self):
        """Kibana URL for testing."""
        return "http://localhost:5601"
    
    def wait_for_elasticsearch(self, elasticsearch_url: str, timeout: int = 30):
        """Wait for Elasticsearch to be available."""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{elasticsearch_url}/_cluster/health")
                if response.status_code == 200:
                    return True
            except requests.exceptions.RequestException:
                pass
            time.sleep(1)
        return False
    
    def get_recent_logs(self, elasticsearch_url: str, service_name: str = None, 
                       minutes: int = 5) -> List[Dict[str, Any]]:
        """Get recent logs from Elasticsearch."""
        # Calculate time range
        now = datetime.now(datetime.UTC)
        since = now - timedelta(minutes=minutes)
        
        # Build query
        query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "range": {
                                "@timestamp": {
                                    "gte": since.isoformat(),
                                    "lte": now.isoformat()
                                }
                            }
                        }
                    ]
                }
            },
            "sort": [{"@timestamp": {"order": "desc"}}],
            "size": 100
        }
        
        # Add service filter if specified
        if service_name:
            query["query"]["bool"]["must"].append({
                "term": {"service.name": service_name}
            })
        
        # Search across all ECS TurdParty indices
        try:
            response = requests.post(
                f"{elasticsearch_url}/ecs-turdparty-*/_search",
                json=query,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                return [hit["_source"] for hit in data.get("hits", {}).get("hits", [])]
            else:
                return []
        except requests.exceptions.RequestException:
            return []
    
    def test_elasticsearch_available(self, elasticsearch_url):
        """Test that Elasticsearch is available and healthy."""
        assert self.wait_for_elasticsearch(elasticsearch_url), (
            "Elasticsearch is not available for testing"
        )
        
        # Check cluster health
        response = requests.get(f"{elasticsearch_url}/_cluster/health")
        assert response.status_code == 200
        
        health = response.json()
        assert health["status"] in ["green", "yellow"], (
            f"Elasticsearch cluster health is {health['status']}"
        )
    
    def test_ecs_indices_exist(self, elasticsearch_url):
        """Test that ECS-compliant indices exist."""
        response = requests.get(f"{elasticsearch_url}/_cat/indices/ecs-turdparty-*?format=json")
        assert response.status_code == 200
        
        indices = response.json()
        assert len(indices) > 0, "No ECS TurdParty indices found"
        
        # Verify index naming convention
        for index in indices:
            index_name = index["index"]
            assert index_name.startswith("ecs-turdparty-"), (
                f"Index {index_name} doesn't follow ECS naming convention"
            )
    
    def test_api_service_ecs_logging(self, elasticsearch_url):
        """Test that API service logs are ECS-compliant."""
        logs = self.get_recent_logs(elasticsearch_url, service_name="api")
        
        assert len(logs) > 0, "No API service logs found in Elasticsearch"
        
        # Test ECS compliance for API logs
        for log in logs[:5]:  # Test first 5 logs
            # Core ECS fields
            assert "@timestamp" in log, "Missing @timestamp field"
            assert "message" in log, "Missing message field"
            
            # ECS Service fields
            assert "service" in log, "Missing service object"
            assert "name" in log["service"], "Missing service.name"
            assert log["service"]["name"] == "api", "Incorrect service name"
            
            # ECS Event fields
            assert "event" in log, "Missing event object"
            assert "category" in log["event"], "Missing event.category"
            assert "type" in log["event"], "Missing event.type"
            
            # ECS Labels for debugging
            assert "labels" in log, "Missing labels object"
            assert "platform" in log["labels"], "Missing labels.platform"
            assert log["labels"]["platform"] == "turdparty", "Incorrect platform label"
    
    def test_websocket_logging_context(self, elasticsearch_url):
        """Test that WebSocket-related logs have proper context."""
        # Get logs that mention WebSocket
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"match": {"message": "WebSocket"}},
                        {
                            "range": {
                                "@timestamp": {
                                    "gte": "now-5m"
                                }
                            }
                        }
                    ]
                }
            },
            "size": 10
        }
        
        try:
            response = requests.post(
                f"{elasticsearch_url}/ecs-turdparty-*/_search",
                json=query,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                websocket_logs = [hit["_source"] for hit in data.get("hits", {}).get("hits", [])]
                
                if websocket_logs:
                    for log in websocket_logs:
                        # Verify WebSocket-specific categorization
                        if "event" in log and "category" in log["event"]:
                            assert "network" in log["event"]["category"] or "application" in log["event"]["category"]
                        
                        # Verify protocol labeling
                        if "labels" in log and "protocol" in log["labels"]:
                            assert log["labels"]["protocol"] == "websocket"
        except requests.exceptions.RequestException:
            pytest.skip("Could not test WebSocket logging - Elasticsearch not accessible")
    
    def test_route_registration_logging(self, elasticsearch_url):
        """Test that route registration logs have proper context."""
        # Search for route registration logs
        query = {
            "query": {
                "bool": {
                    "should": [
                        {"match": {"message": "Registering"}},
                        {"match": {"message": "Registered"}},
                        {"match": {"message": "Failed to register"}},
                        {"match": {"message": "ABOUT TO REGISTER"}}
                    ],
                    "minimum_should_match": 1,
                    "must": [
                        {
                            "range": {
                                "@timestamp": {
                                    "gte": "now-10m"
                                }
                            }
                        }
                    ]
                }
            },
            "size": 20
        }
        
        try:
            response = requests.post(
                f"{elasticsearch_url}/ecs-turdparty-*/_search",
                json=query,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                registration_logs = [hit["_source"] for hit in data.get("hits", {}).get("hits", [])]
                
                if registration_logs:
                    for log in registration_logs:
                        # Verify route registration categorization
                        if "labels" in log and "operation" in log["labels"]:
                            assert log["labels"]["operation"] == "route_registration"
                        
                        # Verify event categorization
                        if "event" in log:
                            assert "category" in log["event"]
                            assert "configuration" in log["event"]["category"] or "application" in log["event"]["category"]
        except requests.exceptions.RequestException:
            pytest.skip("Could not test route registration logging - Elasticsearch not accessible")
    
    def test_correlation_id_tracking(self, elasticsearch_url):
        """Test that correlation IDs are properly tracked across logs."""
        logs = self.get_recent_logs(elasticsearch_url, minutes=10)
        
        # Find logs with correlation IDs
        correlated_logs = [
            log for log in logs 
            if "trace" in log and "id" in log["trace"] and log["trace"]["id"] != "no-correlation"
        ]
        
        if correlated_logs:
            # Group by correlation ID
            correlation_groups = {}
            for log in correlated_logs:
                correlation_id = log["trace"]["id"]
                if correlation_id not in correlation_groups:
                    correlation_groups[correlation_id] = []
                correlation_groups[correlation_id].append(log)
            
            # Verify correlation tracking
            for correlation_id, group_logs in correlation_groups.items():
                assert len(group_logs) > 0, f"Empty correlation group for {correlation_id}"
                
                # All logs in group should have same correlation ID
                for log in group_logs:
                    assert log["trace"]["id"] == correlation_id
    
    def test_service_identification_complete(self, elasticsearch_url):
        """Test that all expected services are logging with proper identification."""
        expected_services = [
            "api", "database", "redis", "elasticsearch", "logstash", 
            "kibana", "frontend", "docs", "status", "vm-monitor",
            "celery-worker", "celery-beat", "minio"
        ]
        
        # Get unique service names from recent logs
        query = {
            "aggs": {
                "services": {
                    "terms": {
                        "field": "service.name",
                        "size": 50
                    }
                }
            },
            "size": 0,
            "query": {
                "range": {
                    "@timestamp": {
                        "gte": "now-10m"
                    }
                }
            }
        }
        
        try:
            response = requests.post(
                f"{elasticsearch_url}/ecs-turdparty-*/_search",
                json=query,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                service_buckets = data.get("aggregations", {}).get("services", {}).get("buckets", [])
                found_services = [bucket["key"] for bucket in service_buckets]
                
                # We should find at least some of the expected services
                common_services = set(expected_services) & set(found_services)
                assert len(common_services) > 0, (
                    f"No expected services found logging. Found: {found_services}"
                )
        except requests.exceptions.RequestException:
            pytest.skip("Could not test service identification - Elasticsearch not accessible")
    
    def test_debug_context_availability(self, elasticsearch_url):
        """Test that comprehensive debug context is available in logs."""
        logs = self.get_recent_logs(elasticsearch_url, service_name="api", minutes=5)
        
        if logs:
            # Check for debug context fields
            debug_fields = [
                "labels.environment",
                "labels.platform", 
                "labels.log_processor",
                "labels.ecs_version",
                "service.environment",
                "event.dataset",
                "event.module"
            ]
            
            context_found = False
            for log in logs:
                field_count = 0
                for field_path in debug_fields:
                    if self._get_nested_field(log, field_path):
                        field_count += 1
                
                if field_count >= len(debug_fields) // 2:  # At least half the fields
                    context_found = True
                    break
            
            assert context_found, (
                "Insufficient debug context found in logs. "
                f"Expected fields: {debug_fields}"
            )
    
    def _get_nested_field(self, obj: Dict, field_path: str) -> Any:
        """Get nested field value using dot notation."""
        keys = field_path.split('.')
        current = obj
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        return current


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
