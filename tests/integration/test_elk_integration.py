"""
Integration tests for ELK stack integration.

Tests the complete ELK logging pipeline including Elasticsearch indexing,
Logstash processing, and log retrieval. Ensures PEP8, PEP257, and PEP484 compliance.
"""

import asyncio
from datetime import datetime, timedelta
import os
from typing import Any

from elasticsearch import AsyncElasticsearch
import pytest

from api.services.elk_logger import ELKLogger


@pytest.mark.skipif(
    os.environ.get("IN_DOCKER") != "true",
    reason="Integration tests should only run in Docker"
)
class TestELKIntegration:
    """Integration test suite for ELK stack."""

    @pytest.fixture
    async def elk_logger(self) -> ELKLogger:
        """Create an ELK logger instance for integration testing."""
        logger = ELKLogger()
        yield logger
        await logger.close()

    @pytest.fixture
    def sample_injection_data(self) -> dict[str, Any]:
        """Provide sample injection data for testing."""
        return {
            "injection_id": "test-integration-123",
            "filename": "integration_test.sh",
            "target_path": "/app/scripts/integration_test.sh",
            "status": "pending",
            "event_type": "injection_created"
        }

    @pytest.fixture
    def sample_installation_data(self) -> dict[str, Any]:
        """Provide sample installation data for testing."""
        return {
            "os_type": "ubuntu",
            "os_version": "20.04",
            "architecture": "x86_64",
            "kernel_version": "5.4.0-74-generic",
            "installed_packages": ["curl", "wget", "vim", "git"],
            "environment_variables": {
                "PATH": "/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin",
                "HOME": "/root",
                "SHELL": "/bin/bash"
            },
            "network_interfaces": [
                {"name": "eth0", "ip": "*************", "mac": "00:11:22:33:44:55"}
            ]
        }

    @pytest.mark.asyncio
    async def test_file_injection_event_logging_and_retrieval(
        self,
        elk_logger: ELKLogger,
        sample_injection_data: dict[str, Any],
    ) -> None:
        """Test complete file injection event logging and retrieval."""
        # Act - Log the event
        await elk_logger.log_file_injection_event(
            injection_id=sample_injection_data["injection_id"],
            event_type=sample_injection_data["event_type"],
            filename=sample_injection_data["filename"],
            target_path=sample_injection_data["target_path"],
            status=sample_injection_data["status"]
        )

        # Wait for indexing
        await asyncio.sleep(2)

        # Act - Search for the logged event
        search_query = f"injection_id:{sample_injection_data['injection_id']}"
        search_result = await elk_logger.search_logs(search_query)

        # Assert
        assert "hits" in search_result
        assert search_result["hits"]["total"]["value"] > 0

        # Verify the logged data
        hit = search_result["hits"]["hits"][0]
        source = hit["_source"]
        assert source["injection_id"] == sample_injection_data["injection_id"]
        assert source["event_type"] == sample_injection_data["event_type"]

    @pytest.mark.asyncio
    async def test_installation_base_logging_and_retrieval(
        self,
        elk_logger: ELKLogger,
        sample_injection_data: dict[str, Any],
        sample_installation_data: dict[str, Any],
    ) -> None:
        """Test complete installation base logging and retrieval."""
        # Act - Log installation base
        await elk_logger.log_installation_base(
            injection_id=sample_injection_data["injection_id"],
            installation_data=sample_installation_data
        )

        # Wait for indexing
        await asyncio.sleep(2)

        # Act - Search for the logged installation base
        search_query = f"injection_id:{sample_injection_data['injection_id']} AND event_type:installation_base"
        search_result = await elk_logger.search_logs(search_query)

        # Assert
        assert "hits" in search_result
        assert search_result["hits"]["total"]["value"] > 0

        # Verify the logged data
        hit = search_result["hits"]["hits"][0]
        source = hit["_source"]
        assert source["injection_id"] == sample_injection_data["injection_id"]
        assert source["installation_data"]["os_type"] == sample_installation_data["os_type"]

    @pytest.mark.asyncio
    async def test_system_event_logging_and_retrieval(
        self,
        elk_logger: ELKLogger,
    ) -> None:
        """Test complete system event logging and retrieval."""
        # Arrange
        event_type = "integration_test_event"
        message = "Integration test system event"
        details = {"test_id": "integration-123", "component": "elk_logger"}

        # Act - Log system event
        await elk_logger.log_system_event(
            event_type=event_type,
            message=message,
            level="INFO",
            details=details
        )

        # Wait for indexing
        await asyncio.sleep(2)

        # Act - Search for the logged event
        search_query = f"event_type:{event_type}"
        search_result = await elk_logger.search_logs(search_query)

        # Assert
        assert "hits" in search_result
        assert search_result["hits"]["total"]["value"] > 0

        # Verify the logged data
        hit = search_result["hits"]["hits"][0]
        source = hit["_source"]
        assert source["event_type"] == event_type
        assert source["message"] == message
        assert source["details"]["test_id"] == details["test_id"]

    @pytest.mark.asyncio
    async def test_date_range_search(
        self,
        elk_logger: ELKLogger,
        sample_injection_data: dict[str, Any],
    ) -> None:
        """Test log searching with date range filters."""
        # Arrange
        now = datetime.now(datetime.UTC)
        from_date = now - timedelta(minutes=5)
        to_date = now + timedelta(minutes=5)

        # Act - Log an event
        await elk_logger.log_file_injection_event(
            injection_id=sample_injection_data["injection_id"],
            event_type="date_range_test",
            filename=sample_injection_data["filename"],
            target_path=sample_injection_data["target_path"],
            status=sample_injection_data["status"]
        )

        # Wait for indexing
        await asyncio.sleep(2)

        # Act - Search with date range
        search_result = await elk_logger.search_logs(
            query="event_type:date_range_test",
            from_date=from_date,
            to_date=to_date
        )

        # Assert
        assert "hits" in search_result
        assert search_result["hits"]["total"]["value"] > 0

    @pytest.mark.asyncio
    async def test_multiple_concurrent_logs(
        self,
        elk_logger: ELKLogger,
    ) -> None:
        """Test handling of multiple concurrent log operations."""
        # Arrange
        num_logs = 10
        injection_ids = [f"concurrent-test-{i}" for i in range(num_logs)]

        # Act - Create multiple concurrent log operations
        tasks = [
            elk_logger.log_file_injection_event(
                injection_id=injection_id,
                event_type="concurrent_test",
                filename=f"test_{i}.sh",
                target_path=f"/app/test_{i}.sh",
                status="pending"
            )
            for i, injection_id in enumerate(injection_ids)
        ]

        await asyncio.gather(*tasks)

        # Wait for indexing
        await asyncio.sleep(3)

        # Act - Search for all logged events
        search_result = await elk_logger.search_logs("event_type:concurrent_test")

        # Assert
        assert "hits" in search_result
        assert search_result["hits"]["total"]["value"] >= num_logs

    @pytest.mark.asyncio
    async def test_log_aggregation_and_analytics(
        self,
        elk_logger: ELKLogger,
    ) -> None:
        """Test log aggregation and analytics capabilities."""
        # Arrange - Log events with different statuses
        statuses = ["pending", "in_progress", "completed", "failed"]

        for i, status in enumerate(statuses):
            await elk_logger.log_file_injection_event(
                injection_id=f"analytics-test-{i}",
                event_type="analytics_test",
                filename=f"analytics_{i}.sh",
                target_path=f"/app/analytics_{i}.sh",
                status=status
            )

        # Wait for indexing
        await asyncio.sleep(3)

        # Act - Perform aggregation search
        search_body = {
            "query": {
                "match": {"event_type": "analytics_test"}
            },
            "aggs": {
                "status_counts": {
                    "terms": {
                        "field": "details.status.keyword"
                    }
                }
            }
        }

        # Use direct Elasticsearch client for aggregation
        es_client = AsyncElasticsearch([
            f"http://{elk_logger.elasticsearch_host}:{elk_logger.elasticsearch_port}"
        ])

        try:
            result = await es_client.search(
                index="turdparty-*",
                body=search_body
            )

            # Assert
            assert "aggregations" in result
            assert "status_counts" in result["aggregations"]
            buckets = result["aggregations"]["status_counts"]["buckets"]
            assert len(buckets) == len(statuses)

        finally:
            await es_client.close()

    @pytest.mark.asyncio
    async def test_error_handling_with_invalid_data(
        self,
        elk_logger: ELKLogger,
    ) -> None:
        """Test error handling with invalid or malformed data."""
        # Act & Assert - Should not raise exceptions
        await elk_logger.log_file_injection_event(
            injection_id="",  # Empty injection ID
            event_type="error_test",
            filename=None,  # None filename
            target_path="",  # Empty target path
            status="invalid_status"
        )

        # Test with very large data
        large_details = {"large_field": "x" * 10000}
        await elk_logger.log_system_event(
            event_type="large_data_test",
            message="Testing large data handling",
            details=large_details
        )

        # Wait for processing
        await asyncio.sleep(2)

        # Verify logs were still processed (even if with errors)
        search_result = await elk_logger.search_logs("event_type:error_test OR event_type:large_data_test")
        assert "hits" in search_result

    @pytest.mark.asyncio
    async def test_index_rotation_and_cleanup(
        self,
        elk_logger: ELKLogger,
    ) -> None:
        """Test index rotation and cleanup functionality."""
        # Act - Log events to trigger index creation
        await elk_logger.log_file_injection_event(
            injection_id="rotation-test-123",
            event_type="rotation_test",
            filename="rotation_test.sh",
            target_path="/app/rotation_test.sh",
            status="pending"
        )

        # Wait for indexing
        await asyncio.sleep(2)

        # Act - Check if indices are created with proper naming
        es_client = AsyncElasticsearch([
            f"http://{elk_logger.elasticsearch_host}:{elk_logger.elasticsearch_port}"
        ])

        try:
            indices = await es_client.indices.get_alias(index="turdparty-*")

            # Assert
            assert len(indices) > 0

            # Verify index naming pattern
            current_month = datetime.now().strftime('%Y.%m')
            expected_patterns = [
                f"turdparty-file-injections-{current_month}",
                f"turdparty-system-events-{current_month}"
            ]

            index_names = list(indices.keys())
            for pattern in expected_patterns:
                assert any(pattern in name for name in index_names)

        finally:
            await es_client.close()
