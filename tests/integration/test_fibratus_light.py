"""
Fast Fibratus Integration Tests - Tier 1 (Lightweight)
Mock-based tests for rapid CI/CD feedback without heavy operations.
Target runtime: < 30 seconds
"""

import pytest
import time
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Dict, List, Any


class TestFibratusLightIntegration:
    """Fast, mock-based Fibratus integration tests for CI/CD pipelines."""

    @pytest.fixture(scope="class")
    def mock_config(self):
        """Lightweight test configuration with mocked endpoints."""
        return {
            "api_base_url": "http://localhost:8000/api/v1",
            "elasticsearch_url": "http://localhost:9200",
            "test_binary_name": "test-app-light.exe",
            "vm_template": "ubuntu:latest",  # Fast Docker template
            "monitoring_duration": 5,  # 5 seconds vs 5 minutes
            "max_wait_time": 10,  # 10 seconds vs 10 minutes
            "expected_min_events": 3
        }

    @pytest.fixture(scope="class")
    def mock_binary_data(self):
        """Mock binary data instead of real download."""
        return {
            "content": b"MOCK_BINARY_DATA_FOR_TESTING",
            "size": 1024,
            "hash": "mock_blake3_hash_12345",
            "filename": "test-app-light.exe"
        }

    def test_01_verify_infrastructure_mocked(self, mock_config):
        """Test infrastructure verification with mocked responses."""
        print("🔍 Testing infrastructure verification (mocked)...")
        
        with patch('requests.get') as mock_get:
            # Mock successful API health check
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = {"status": "healthy"}
            
            # Test API health check
            import requests
            response = requests.get(f"{mock_config['api_base_url']}/health")
            assert response.status_code == 200
            
            # Test Elasticsearch health check
            response = requests.get(f"{mock_config['elasticsearch_url']}/_cluster/health")
            assert response.status_code == 200
            
        with patch('subprocess.run') as mock_subprocess:
            # Mock Docker containers check
            mock_subprocess.return_value.stdout = "turdpartycollab_api\nturdpartycollab_worker_vm\nturdpartycollab_elasticsearch\nturdpartycollab_database"
            mock_subprocess.return_value.returncode = 0
            
            import subprocess
            result = subprocess.run(["docker", "ps", "--format", "{{.Names}}"], 
                                  capture_output=True, text=True)
            containers = result.stdout.strip().split('\n')
            
            required_containers = [
                "turdpartycollab_api",
                "turdpartycollab_worker_vm", 
                "turdpartycollab_elasticsearch",
                "turdpartycollab_database"
            ]
            
            for container in required_containers:
                assert any(container in c for c in containers)
        
        print("✅ Infrastructure verification (mocked) passed")

    def test_02_create_vm_mocked(self, mock_config):
        """Test VM creation with mocked API responses."""
        print("🖥️ Testing VM creation (mocked)...")
        
        mock_vm_id = f"mock-vm-{int(time.time())}"
        
        with patch('requests.post') as mock_post, patch('requests.get') as mock_get:
            # Mock VM creation response
            mock_post.return_value.status_code = 201
            mock_post.return_value.json.return_value = {
                "vm_id": mock_vm_id,
                "status": "creating",
                "template": mock_config["vm_template"]
            }
            
            # Mock VM status check (ready after 1 check)
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = {
                "vm_id": mock_vm_id,
                "status": "running",
                "ip_address": "*************"
            }
            
            import requests
            
            # Test VM creation
            vm_data = {
                "name": f"fibratus-light-test-{int(time.time())}",
                "template": mock_config["vm_template"],
                "vm_type": "docker",  # Fast Docker instead of Vagrant
                "memory_mb": 512,  # Minimal memory
                "cpus": 1,
                "domain": "TurdParty"
            }
            
            response = requests.post(f"{mock_config['api_base_url']}/vms/", json=vm_data)
            assert response.status_code == 201
            
            vm_info = response.json()
            assert vm_info["vm_id"] == mock_vm_id
            
            # Test VM status check
            response = requests.get(f"{mock_config['api_base_url']}/vms/{mock_vm_id}")
            assert response.status_code == 200
            
            vm_status = response.json()
            assert vm_status["status"] == "running"
            
        print(f"✅ VM creation (mocked) passed: {mock_vm_id}")

    def test_03_verify_monitoring_capability_mocked(self, mock_config):
        """Test monitoring system verification with mocked responses."""
        print("🔍 Testing monitoring capability (mocked)...")
        
        with patch('requests.get') as mock_get:
            # Mock monitoring status endpoint
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = {
                "monitoring_available": True,
                "fibratus_version": "mock-1.0.0",
                "ecs_enabled": True
            }
            
            import requests
            response = requests.get(f"{mock_config['api_base_url']}/vms/mock-vm/monitoring/status")
            assert response.status_code == 200
            
            monitoring_status = response.json()
            assert monitoring_status["monitoring_available"] is True
            
        print("✅ Monitoring capability (mocked) verified")

    def test_04_inject_binary_mocked(self, mock_config, mock_binary_data):
        """Test binary injection with mocked file operations."""
        print("💉 Testing binary injection (mocked)...")
        
        mock_file_id = f"mock-file-{int(time.time())}"
        mock_injection_id = f"mock-injection-{int(time.time())}"
        
        with patch('requests.post') as mock_post, patch('requests.get') as mock_get:
            # Mock file upload
            mock_post.return_value.status_code = 201
            mock_post.return_value.json.return_value = {
                "file_id": mock_file_id,
                "filename": mock_binary_data["filename"],
                "size": mock_binary_data["size"],
                "hash": mock_binary_data["hash"]
            }
            
            # Mock file injection
            mock_post.return_value.json.return_value = {
                "injection_id": mock_injection_id,
                "status": "started",
                "file_id": mock_file_id
            }
            
            # Mock injection status
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = {
                "injection_id": mock_injection_id,
                "status": "completed",
                "file_path": "/tmp/test-app-light.exe"
            }
            
            import requests
            
            # Test file upload (mocked)
            files = {'file': (mock_binary_data["filename"], mock_binary_data["content"])}
            response = requests.post(f"{mock_config['api_base_url']}/files/upload", files=files)
            assert response.status_code == 201
            
            upload_info = response.json()
            assert upload_info["file_id"] == mock_file_id
            
            # Test file injection (mocked)
            injection_data = {
                "file_path": f"/tmp/{mock_binary_data['filename']}",
                "injection_path": "/tmp/test-app-light.exe"
            }
            
            response = requests.post(f"{mock_config['api_base_url']}/vms/mock-vm/inject", json=injection_data)
            assert response.status_code == 201
            
            injection_result = response.json()
            assert injection_result["injection_id"] == mock_injection_id
            
            # Test injection status
            response = requests.get(f"{mock_config['api_base_url']}/vms/mock-vm/injections/{mock_injection_id}")
            assert response.status_code == 200
            
            status = response.json()
            assert status["status"] == "completed"
            
        print(f"✅ Binary injection (mocked) passed: {mock_injection_id}")

    def test_05_verify_telemetry_collection_mocked(self, mock_config):
        """Test telemetry collection with mocked ECS data."""
        print("📊 Testing telemetry collection (mocked)...")
        
        mock_events = [
            {
                "_source": {
                    "@timestamp": datetime.now().isoformat(),
                    "event": {"category": "file", "action": "file_created"},
                    "file": {"path": "/tmp/test-app-light.exe", "size": 1024},
                    "turdparty": {"vm_id": "mock-vm", "injection_id": "mock-injection"}
                }
            },
            {
                "_source": {
                    "@timestamp": datetime.now().isoformat(),
                    "event": {"category": "process", "action": "process_start"},
                    "process": {"name": "test-app-light.exe", "pid": 1234},
                    "turdparty": {"vm_id": "mock-vm", "injection_id": "mock-injection"}
                }
            },
            {
                "_source": {
                    "@timestamp": datetime.now().isoformat(),
                    "event": {"category": "configuration", "action": "registry_key_created"},
                    "registry": {"key": "HKLM\\Software\\TestApp", "value": "InstallPath"},
                    "turdparty": {"vm_id": "mock-vm", "injection_id": "mock-injection"}
                }
            }
        ]
        
        with patch('requests.get') as mock_get, patch('requests.post') as mock_post:
            # Mock Elasticsearch indices check
            mock_get.return_value.status_code = 200
            mock_get.return_value.text = "green open turdparty-install-ecs-2025.06.15\ngreen open turdparty-runtime-ecs-2025.06.15"
            
            # Mock event count
            mock_get.return_value.json.return_value = {"count": 150}
            
            # Mock event search
            mock_post.return_value.status_code = 200
            mock_post.return_value.json.return_value = {
                "hits": {
                    "total": {"value": len(mock_events)},
                    "hits": mock_events
                }
            }
            
            import requests
            
            # Test indices check
            response = requests.get(f"{mock_config['elasticsearch_url']}/_cat/indices/turdparty-*")
            assert response.status_code == 200
            
            # Test event count
            response = requests.get(f"{mock_config['elasticsearch_url']}/turdparty-*/_count")
            assert response.status_code == 200
            
            total_events = response.json()["count"]
            assert total_events >= mock_config["expected_min_events"]
            
            # Test event search
            query = {"query": {"match_all": {}}, "size": 10}
            response = requests.post(f"{mock_config['elasticsearch_url']}/turdparty-*/_search", json=query)
            assert response.status_code == 200
            
            search_results = response.json()
            events = search_results["hits"]["hits"]
            assert len(events) == len(mock_events)
            
            # Verify event categories
            categories = [event["_source"]["event"]["category"] for event in events]
            assert "file" in categories
            assert "process" in categories
            assert "configuration" in categories
            
        print(f"✅ Telemetry collection (mocked) verified: {len(mock_events)} events")

    def test_06_verify_api_contracts(self, mock_config):
        """Test API contract compliance without real operations."""
        print("📋 Testing API contracts...")
        
        # Test expected API endpoints and response formats
        expected_endpoints = [
            "/health",
            "/vms/",
            "/vms/{vm_id}",
            "/vms/{vm_id}/inject",
            "/vms/{vm_id}/injections/{injection_id}",
            "/files/upload",
            "/files/{file_id}"
        ]
        
        for endpoint in expected_endpoints:
            # Verify endpoint format is valid
            assert endpoint.startswith("/")
            if "{" in endpoint:
                assert "}" in endpoint
                
        # Test expected response fields
        expected_vm_fields = ["vm_id", "status", "template", "memory_mb", "cpus"]
        expected_injection_fields = ["injection_id", "status", "file_id"]
        expected_file_fields = ["file_id", "filename", "size", "hash"]
        
        # Verify field lists are complete
        assert len(expected_vm_fields) >= 5
        assert len(expected_injection_fields) >= 3
        assert len(expected_file_fields) >= 4
        
        print("✅ API contracts verified")

    def test_07_error_handling_scenarios(self, mock_config):
        """Test error handling without real failures."""
        print("⚠️ Testing error handling scenarios...")
        
        with patch('requests.get') as mock_get, patch('requests.post') as mock_post:
            # Test API unavailable scenario
            mock_get.side_effect = Exception("Connection refused")
            
            import requests
            try:
                requests.get(f"{mock_config['api_base_url']}/health")
                assert False, "Should have raised exception"
            except Exception as e:
                assert "Connection refused" in str(e)
            
            # Test invalid VM ID scenario
            mock_get.side_effect = None
            mock_get.return_value.status_code = 404
            mock_get.return_value.json.return_value = {"error": "VM not found"}
            
            response = requests.get(f"{mock_config['api_base_url']}/vms/invalid-vm-id")
            assert response.status_code == 404
            
            # Test injection failure scenario
            mock_post.return_value.status_code = 400
            mock_post.return_value.json.return_value = {"error": "Invalid file format"}
            
            response = requests.post(f"{mock_config['api_base_url']}/vms/mock-vm/inject", json={})
            assert response.status_code == 400
            
        print("✅ Error handling scenarios verified")

    def test_08_performance_validation(self, mock_config):
        """Test performance expectations without real load."""
        print("⚡ Testing performance validation...")
        
        # Test response time expectations
        start_time = time.time()
        
        # Simulate fast operations
        time.sleep(0.1)  # 100ms simulated operation
        
        operation_time = time.time() - start_time
        assert operation_time < 1.0, f"Operation took {operation_time}s, expected < 1s"
        
        # Test concurrent operation simulation
        concurrent_operations = 5
        start_time = time.time()
        
        for i in range(concurrent_operations):
            time.sleep(0.01)  # 10ms per operation
            
        total_time = time.time() - start_time
        assert total_time < 1.0, f"Concurrent operations took {total_time}s, expected < 1s"
        
        print(f"✅ Performance validation passed: {operation_time:.3f}s operation, {total_time:.3f}s concurrent")

    def test_09_cleanup_validation(self, mock_config):
        """Test cleanup procedures without real resources."""
        print("🧹 Testing cleanup validation...")
        
        with patch('requests.delete') as mock_delete:
            # Mock successful cleanup
            mock_delete.return_value.status_code = 200
            mock_delete.return_value.json.return_value = {"status": "deleted"}
            
            import requests
            
            # Test VM cleanup
            response = requests.delete(f"{mock_config['api_base_url']}/vms/mock-vm")
            assert response.status_code == 200
            
            # Test file cleanup
            response = requests.delete(f"{mock_config['api_base_url']}/files/mock-file")
            assert response.status_code == 200
            
        print("✅ Cleanup validation passed")


if __name__ == "__main__":
    # Run fast tests with minimal output
    pytest.main([__file__, "-v", "--tb=short", "--disable-warnings"])
