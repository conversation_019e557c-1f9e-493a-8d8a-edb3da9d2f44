"""
Integration tests for file injection workflow.
"""
import os
import time
from unittest.mock import patch

import pytest
import requests
from utils.service_urls import ServiceURLManager

# Skip tests if not running in Docker
pytestmark = pytest.mark.skipif(
    os.environ.get("IN_DOCKER") != "true",
    reason="Integration tests should only run in Docker"
)

# Use ServiceURLManager for consistent URL handling
service_manager = ServiceURLManager()
API_URL = service_manager.get_service_url("api")


@pytest.fixture
def test_file_upload():
    """Create a test file upload."""
    # Upload a file through the API
    file_content = b"This is a test file for injection."
    files = {"file": ("test_injection.txt", file_content, "text/plain")}
    data = {"description": "Test file for injection"}

    response = requests.post(f"{API_URL}/api/v1/files/upload", files=files, data=data)
    assert response.status_code == 200
    result = response.json()

    # Wait for task to complete
    time.sleep(2)

    # Return the file ID
    return result["file_id"]


@pytest.fixture
def test_vm():
    """Create a test VM."""
    # Create a VM through the API
    create_data = {
        "name": "test-injection-vm",
        "template": "ubuntu/focal64",
        "vm_type": "docker",
        "memory_mb": 1024,
        "cpus": 1
    }
    response = requests.post(f"{API_URL}/api/v1/vms/", json=create_data)
    assert response.status_code == 201
    result = response.json()

    # Return the VM ID
    return result["vm_id"]


def test_file_injection_api(test_file_upload):
    """Test the file injection API endpoint."""
    file_id = test_file_upload

    # Create a file injection
    injection_data = {
        "file_id": file_id,
        "target_path": "/tmp/test_file.txt",
        "description": "Test file injection"
    }
    response = requests.post(f"{API_URL}/api/v1/file_injection/", files={"file": ("test.txt", b"test content")}, data=injection_data)
    assert response.status_code == 201
    result = response.json()
    assert "injection_id" in result

    injection_id = result["injection_id"]

    # Get the file injection
    response = requests.get(f"{API_URL}/api/v1/file_injection/{injection_id}")
    assert response.status_code == 200
    injection_data = response.json()
    assert injection_data["file_id"] == file_id


def test_vm_injection_api(test_file_upload, test_vm):
    """Test the VM injection API endpoint."""
    file_id = test_file_upload
    vm_id = test_vm

    # Inject file into VM
    injection_data = {
        "file_id": file_id,
        "target_path": "/tmp/injected_file.txt"
    }

    response = requests.post(f"{API_URL}/api/v1/vms/{vm_id}/inject", json=injection_data)
    assert response.status_code == 200
    result = response.json()
    assert "injection_id" in result

    injection_id = result["injection_id"]

    # Get the injection status
    response = requests.get(f"{API_URL}/api/v1/vms/{vm_id}/injections/{injection_id}")
    assert response.status_code == 200
    injection_status = response.json()
    assert injection_status["vm_id"] == vm_id
