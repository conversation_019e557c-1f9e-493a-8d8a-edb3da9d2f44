"""
Enhanced File Injection Workflow Testing

Tests comprehensive file injection workflows including upload, VM allocation,
injection, execution, and monitoring with real API calls and validation.
"""

# Use direct API URL for container testing
from pathlib import Path
import tempfile
import time
import uuid

import pytest
import requests

from utils.service_urls import ServiceURLManager


class TestFileInjectionWorkflow:
    """Comprehensive file injection workflow tests."""

    @pytest.fixture(scope="class")
    def service_manager(self):
        """Get ServiceURLManager instance."""
        return ServiceURLManager("development")

    @pytest.fixture(scope="class")
    def api_url(self, service_manager):
        """Get API base URL."""
        return service_manager.get_service_url("api")

    @pytest.fixture
    def test_file(self):
        """Create a test file for injection."""
        # Create a simple test script
        content = """#!/bin/bash
echo "TurdParty test file executed successfully"
echo "Current directory: $(pwd)"
echo "Current user: $(whoami)"
echo "Date: $(date)"
ls -la /tmp/ | head -10
"""

        # Create temporary file
        with tempfile.NamedTemporaryFile(mode="w", suffix=".sh", delete=False) as f:
            f.write(content)
            test_file_path = Path(f.name)

        yield test_file_path

        # Cleanup
        if test_file_path.exists():
            test_file_path.unlink()

    def test_file_upload_workflow(self, api_url, test_file):
        """Test file upload workflow through API."""
        print("🔄 Testing file upload workflow...")
        print(f"   API URL: {api_url}")
        print(f"   Test file: {test_file}")

        # Upload file through API
        with open(test_file, "rb") as f:
            files = {"file": (test_file.name, f, "application/x-shellscript")}
            data = {"description": "TurdParty test file for injection workflow"}

            response = requests.post(
                f"{api_url}/api/v1/files/upload", files=files, data=data, timeout=60
            )

        print(f"   Upload response status: {response.status_code}")

        # Validate upload response
        assert (
            response.status_code == 200
        ), f"File upload failed: {response.status_code} - {response.text}"

        upload_result = response.json()
        assert "file_id" in upload_result, "Upload response should contain file_id"
        assert "filename" in upload_result, "Upload response should contain filename"
        assert "file_hash" in upload_result, "Upload response should contain file_hash"

        file_id = upload_result["file_id"]
        print(f"   ✅ File uploaded successfully: {file_id}")
        print(f"   Filename: {upload_result['filename']}")
        print(f"   File hash: {upload_result['file_hash']}")

        # Wait for processing
        time.sleep(2)

        # Verify file can be retrieved
        response = requests.get(f"{api_url}/api/v1/files/{file_id}", timeout=30)
        assert (
            response.status_code == 200
        ), f"File retrieval failed: {response.status_code}"

        file_info = response.json()
        assert (
            file_info["file_id"] == file_id
        ), "Retrieved file ID should match uploaded file ID"

        return file_id, upload_result

    def test_vm_creation_for_injection(self, api_url):
        """Test VM creation for file injection."""
        print("🖥️ Testing VM creation for injection...")

        # Create VM for injection
        vm_data = {
            "name": f"injection-test-vm-{uuid.uuid4().hex[:8]}",
            "template": "ubuntu:20.04",  # Use available template
            "vm_type": "docker",
            "memory_mb": 1024,
            "cpus": 1,
            "domain": "TurdParty",
            "description": "VM for file injection testing",
            "auto_start": True,
        }

        response = requests.post(f"{api_url}/api/v1/vms/", json=vm_data, timeout=30)

        print(f"   VM creation response status: {response.status_code}")

        if response.status_code == 201:
            vm_result = response.json()
            vm_id = vm_result["vm_id"]
            print(f"   ✅ VM created successfully: {vm_id}")
            print(f"   VM name: {vm_result['name']}")
            print(f"   VM status: {vm_result['status']}")

            return vm_id, vm_result
        else:
            print(f"   ⚠️ VM creation failed: {response.status_code} - {response.text}")
            # For testing purposes, we'll skip VM creation if it fails
            pytest.skip(f"VM creation failed: {response.status_code}")

    def test_vm_allocation_from_pool(self, api_url):
        """Test VM allocation from pool."""
        print("🎯 Testing VM allocation from pool...")

        # Try to allocate VM from pool
        allocation_data = {
            "template": "ubuntu:20.04",
            "requester_id": f"test_user_{uuid.uuid4().hex[:8]}",
        }

        response = requests.post(
            f"{api_url}/api/v1/vm-allocation/allocate", json=allocation_data, timeout=60
        )

        print(f"   Allocation response status: {response.status_code}")

        if response.status_code == 200:
            allocation_result = response.json()

            if allocation_result.get("success"):
                vm_id = allocation_result["vm_id"]
                vm_details = allocation_result["vm"]

                print(f"   ✅ VM allocated successfully: {vm_id}")
                print(f"   VM status: {vm_details['status']}")
                print(
                    f"   Allocation time: {allocation_result.get('duration_ms', 'unknown')}ms"
                )

                return vm_id, allocation_result
            else:
                print(
                    f"   ⏳ VM allocation in progress: {allocation_result.get('message', 'unknown')}"
                )
                pytest.skip("VM allocation in progress, skipping injection test")
        else:
            print(
                f"   ⚠️ VM allocation failed: {response.status_code} - {response.text}"
            )
            pytest.skip(f"VM allocation failed: {response.status_code}")

    def test_file_injection_process(self, api_url, test_file):
        """Test complete file injection process."""
        print("💉 Testing complete file injection process...")

        # Step 1: Upload file
        file_id, upload_result = self.test_file_upload_workflow(api_url, test_file)

        # Step 2: Get or create VM
        try:
            vm_id, vm_result = self.test_vm_allocation_from_pool(api_url)
        except pytest.skip.Exception:
            # Fallback to VM creation if pool allocation fails
            vm_id, vm_result = self.test_vm_creation_for_injection(api_url)

        # Step 3: Inject file into VM
        print(f"   Injecting file {file_id} into VM {vm_id}...")

        injection_data = {
            "file_uuid": file_id,
            "injection_path": f"/tmp/{test_file.name}",
            "execute_after_injection": True,
            "permissions": "0755",
        }

        response = requests.post(
            f"{api_url}/api/v1/vms/{vm_id}/inject",
            json=injection_data,
            timeout=300,  # 5 minutes for injection
        )

        print(f"   Injection response status: {response.status_code}")

        if response.status_code in [200, 202]:
            injection_result = response.json()
            print("   ✅ File injection initiated successfully")

            if "job_id" in injection_result:
                job_id = injection_result["job_id"]
                print(f"   Job ID: {job_id}")

                # Wait for injection to complete
                self._wait_for_injection_completion(api_url, job_id)

            return {
                "file_id": file_id,
                "vm_id": vm_id,
                "injection_result": injection_result,
                "upload_result": upload_result,
                "vm_result": vm_result,
            }
        else:
            print(
                f"   ❌ File injection failed: {response.status_code} - {response.text}"
            )
            assert False, f"File injection failed: {response.status_code}"

    def _wait_for_injection_completion(self, api_url, job_id, timeout=300):
        """Wait for injection job to complete."""
        print(f"   ⏳ Waiting for injection job {job_id} to complete...")

        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{api_url}/api/v1/jobs/{job_id}", timeout=10)

                if response.status_code == 200:
                    job_data = response.json()
                    status = job_data.get("status", "unknown")

                    print(f"   Job status: {status}")

                    if status == "completed":
                        print("   ✅ Injection job completed successfully")
                        return True
                    elif status == "failed":
                        error = job_data.get("error", "Unknown error")
                        print(f"   ❌ Injection job failed: {error}")
                        return False
                    elif status in ["pending", "running", "in_progress"]:
                        # Job still running, continue waiting
                        pass
                    else:
                        print(f"   ⚠️ Unknown job status: {status}")

            except Exception as e:
                print(f"   ⚠️ Error checking job status: {e}")

            time.sleep(5)  # Wait 5 seconds before checking again

        print(f"   ⏰ Injection job timed out after {timeout}s")
        return False

    def test_injection_monitoring_and_validation(self, api_url, test_file):
        """Test injection monitoring and result validation."""
        print("📊 Testing injection monitoring and validation...")

        # Run complete injection process
        injection_data = self.test_file_injection_process(api_url, test_file)

        vm_id = injection_data["vm_id"]
        file_id = injection_data["file_id"]

        # Check VM status after injection
        response = requests.get(f"{api_url}/api/v1/vms/{vm_id}", timeout=30)

        if response.status_code == 200:
            vm_status = response.json()
            print(f"   VM status after injection: {vm_status['status']}")
            print(f"   VM runtime: {vm_status.get('runtime_minutes', 0)} minutes")

        # Try to get injection logs/results
        try:
            response = requests.get(f"{api_url}/api/v1/vms/{vm_id}/logs", timeout=30)
            if response.status_code == 200:
                logs = response.json()
                print(f"   ✅ Retrieved VM logs: {len(logs)} entries")
            else:
                print(f"   ⚠️ Could not retrieve VM logs: {response.status_code}")
        except Exception as e:
            print(f"   ⚠️ Error retrieving logs: {e}")

        print("   ✅ Injection monitoring completed")
        return injection_data


class TestFileInjectionPerformance:
    """Performance tests for file injection workflow."""

    def test_injection_performance_metrics(self):
        """Test injection performance metrics."""
        print("⚡ Testing injection performance metrics...")

        # This would test:
        # 1. File upload speed
        # 2. VM allocation time
        # 3. Injection execution time
        # 4. Overall workflow duration

        # For now, we'll just validate that the workflow components exist
        service_manager = ServiceURLManager("development")
        api_url = service_manager.get_service_url("api")

        # Test API health
        response = requests.get(f"{api_url}/health", timeout=10)
        assert response.status_code == 200, "API should be healthy"

        print("   ✅ API health check passed")
        print(f"   API URL: {api_url}")


if __name__ == "__main__":
    # Run tests manually for debugging
    print("🧪 Running File Injection Workflow Tests...")

    test_instance = TestFileInjectionWorkflow()

    try:
        # Create test file
        import tempfile

        content = "#!/bin/bash\necho 'Test file executed'\n"
        with tempfile.NamedTemporaryFile(mode="w", suffix=".sh", delete=False) as f:
            f.write(content)
            test_file = Path(f.name)

        # Get API URL
        service_manager = ServiceURLManager("development")
        api_url = service_manager.get_service_url("api")

        print(f"\nAPI URL: {api_url}")
        print(f"Test file: {test_file}")

        # Run tests
        print("\n1. Testing file upload...")
        file_id, upload_result = test_instance.test_file_upload_workflow(
            api_url, test_file
        )

        print("\n2. Testing VM allocation...")
        try:
            vm_id, vm_result = test_instance.test_vm_allocation_from_pool(api_url)
        except:
            print("   Pool allocation failed, trying VM creation...")
            vm_id, vm_result = test_instance.test_vm_creation_for_injection(api_url)

        print("\n3. Testing complete injection process...")
        injection_data = test_instance.test_file_injection_process(api_url, test_file)

        print("\n✅ All file injection workflow tests completed successfully!")

        # Cleanup
        test_file.unlink()

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
