#!/usr/bin/env python3
"""
gRPC Injection Logic Tests

Tests the specific gRPC injection routing and fallback logic
by examining Celery worker logs and injection behavior.
"""

import json
import subprocess
import time
import uuid
from typing import Dict, Any, List


class GRPCInjectionLogicTester:
    """Test gRPC injection logic and routing."""
    
    def __init__(self):
        self.api_base = "http://api.turdparty.localhost/api/v1"
        self.test_file_id = "902c5e5f-1109-4c1d-ba68-a50da622395d"
        self.test_vms = []
        
    def http_request(self, method: str, endpoint: str, data: Dict = None) -> Dict[str, Any]:
        """Make HTTP request using curl."""
        url = f"{self.api_base}{endpoint}"
        
        if method.upper() == "GET":
            cmd = ["curl", "-s", url]
        elif method.upper() == "POST":
            cmd = ["curl", "-s", "-X", "POST", "-H", "Content-Type: application/json"]
            if data:
                cmd.extend(["-d", json.dumps(data)])
            cmd.append(url)
        elif method.upper() == "DELETE":
            cmd = ["curl", "-s", "-X", "DELETE", url]
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0 and result.stdout.strip():
                try:
                    return {"success": True, "data": json.loads(result.stdout)}
                except json.JSONDecodeError:
                    return {"success": True, "data": {"raw": result.stdout}}
            else:
                return {"success": False, "error": result.stderr or "Request failed"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_celery_logs(self, filter_text: str = None) -> List[str]:
        """Get recent Celery worker logs."""
        try:
            cmd = ["docker", "logs", "turdpartycollab_celery_worker", "--tail", "100"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                if filter_text:
                    lines = [line for line in lines if filter_text in line]
                return lines
            else:
                return []
        except Exception:
            return []
    
    def wait_for_vm_ready(self, vm_id: str, timeout: int = 60) -> bool:
        """Wait for VM to be ready."""
        start_time = time.time()
        while time.time() - start_time < timeout:
            response = self.http_request("GET", f"/vms/{vm_id}")
            if response["success"]:
                vm_data = response["data"]
                if vm_data.get("status") == "running":
                    return True
            time.sleep(2)
        return False
    
    def cleanup_resources(self):
        """Clean up test VMs."""
        for vm_id in self.test_vms:
            try:
                self.http_request("DELETE", f"/vms/{vm_id}")
            except Exception:
                pass
        self.test_vms.clear()
    
    def test_docker_injection_routing(self) -> bool:
        """Test that Docker VMs use Docker injection method."""
        print("\n🐳 Testing Docker VM Injection Routing")
        print("-" * 50)
        
        # Create Docker VM
        vm_config = {
            "name": f"test-docker-routing-{uuid.uuid4().hex[:8]}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 1024,
            "cpus": 1,
            "disk_gb": 10,
            "description": "Docker injection routing test"
        }
        
        response = self.http_request("POST", "/vms/", vm_config)
        if not response["success"]:
            print(f"  ❌ VM creation failed: {response['error']}")
            return False
        
        vm_data = response["data"]
        vm_id = vm_data["vm_id"]
        self.test_vms.append(vm_id)
        vm_name = vm_data["name"]
        
        print(f"  ✅ Docker VM created: {vm_id}")
        
        # Wait for VM ready
        if not self.wait_for_vm_ready(vm_id):
            print("  ❌ VM failed to reach running state")
            return False
        
        # Clear logs before injection
        time.sleep(2)
        
        # Inject file
        injection_config = {
            "file_id": self.test_file_id,
            "injection_path": "/tmp/docker-routing-test.exe",
            "execute_after_injection": False,
            "permissions": "0755"
        }
        
        response = self.http_request("POST", f"/vms/{vm_id}/inject", injection_config)
        if not response["success"]:
            print(f"  ❌ Injection failed: {response['error']}")
            return False
        
        injection_id = response["data"]["injection_id"]
        print(f"  ✅ Injection queued: {injection_id}")
        
        # Wait for injection processing and check logs
        time.sleep(10)
        
        # Check Celery logs for Docker injection method
        logs = self.get_celery_logs(vm_name)
        docker_injection_found = False
        
        for log_line in logs:
            if "Using Docker injection for container VM" in log_line and vm_name in log_line:
                docker_injection_found = True
                print(f"  ✅ Found Docker injection log: {log_line.strip()}")
                break
        
        if not docker_injection_found:
            print("  ❌ Docker injection method not detected in logs")
            print("  Recent logs:")
            for log_line in logs[-5:]:
                print(f"    {log_line.strip()}")
            return False
        
        print("  ✅ Docker VM correctly routed to Docker injection method")
        return True
    
    def test_vagrant_injection_routing(self) -> bool:
        """Test that Vagrant VMs use gRPC injection method."""
        print("\n🏠 Testing Vagrant VM gRPC Injection Routing")
        print("-" * 50)
        
        # Create Vagrant VM
        vm_config = {
            "name": f"test-vagrant-routing-{uuid.uuid4().hex[:8]}",
            "template": "10Baht/windows10-turdparty",
            "vm_type": "vagrant",
            "memory_mb": 4096,
            "cpus": 2,
            "disk_gb": 50,
            "description": "Vagrant gRPC injection routing test"
        }
        
        response = self.http_request("POST", "/vms/", vm_config)
        if not response["success"]:
            print(f"  ❌ VM creation failed: {response['error']}")
            return False
        
        vm_data = response["data"]
        vm_id = vm_data["vm_id"]
        self.test_vms.append(vm_id)
        vm_name = vm_data["name"]
        
        print(f"  ✅ Vagrant VM created: {vm_id}")
        
        # Wait for VM ready
        if not self.wait_for_vm_ready(vm_id):
            print("  ❌ VM failed to reach running state")
            return False
        
        # Clear logs before injection
        time.sleep(2)
        
        # Inject file
        injection_config = {
            "file_id": self.test_file_id,
            "injection_path": "C:\\TurdParty\\vagrant-routing-test.exe",
            "execute_after_injection": False,
            "permissions": "0755"
        }
        
        response = self.http_request("POST", f"/vms/{vm_id}/inject", injection_config)
        if not response["success"]:
            print(f"  ❌ Injection failed: {response['error']}")
            return False
        
        injection_id = response["data"]["injection_id"]
        print(f"  ✅ Injection queued: {injection_id}")
        
        # Wait for injection processing and check logs
        time.sleep(15)  # Vagrant injection takes longer
        
        # Check Celery logs for gRPC injection method
        logs = self.get_celery_logs(vm_name)
        grpc_injection_found = False
        grpc_connection_attempted = False
        ssh_fallback_found = False
        
        for log_line in logs:
            if "Using gRPC injection for Vagrant VM" in log_line and vm_name in log_line:
                grpc_injection_found = True
                print(f"  ✅ Found gRPC injection log: {log_line.strip()}")
            elif "Connecting to VM gRPC service" in log_line:
                grpc_connection_attempted = True
                print(f"  ✅ Found gRPC connection attempt: {log_line.strip()}")
            elif "SSH injection simulated" in log_line and vm_name in log_line:
                ssh_fallback_found = True
                print(f"  ✅ Found SSH fallback log: {log_line.strip()}")
        
        if not grpc_injection_found:
            print("  ❌ gRPC injection method not detected in logs")
            print("  Recent logs:")
            for log_line in logs[-10:]:
                if vm_name in log_line or "injection" in log_line.lower():
                    print(f"    {log_line.strip()}")
            return False
        
        if grpc_connection_attempted:
            print("  ✅ gRPC connection was attempted")
        
        if ssh_fallback_found:
            print("  ✅ SSH fallback was executed (expected behavior)")
        
        print("  ✅ Vagrant VM correctly routed to gRPC injection method")
        return True
    
    def test_vm_type_detection_accuracy(self) -> bool:
        """Test VM type detection accuracy by examining logs."""
        print("\n🔍 Testing VM Type Detection Accuracy")
        print("-" * 50)
        
        test_cases = [
            {
                "name": "ubuntu-container",
                "template": "ubuntu:20.04",
                "vm_type": "docker",
                "expected_method": "Docker injection"
            },
            {
                "name": "windows-vagrant",
                "template": "gusztavvargadr/windows-10",
                "vm_type": "vagrant",
                "expected_method": "gRPC injection"
            }
        ]
        
        success_count = 0
        
        for case in test_cases:
            print(f"\n  Testing {case['name']}...")
            
            vm_config = {
                "name": f"test-detection-{case['name']}-{uuid.uuid4().hex[:8]}",
                "template": case["template"],
                "vm_type": case["vm_type"],
                "memory_mb": 1024 if case["vm_type"] == "docker" else 4096,
                "cpus": 1 if case["vm_type"] == "docker" else 2,
                "disk_gb": 10 if case["vm_type"] == "docker" else 50,
                "description": f"Detection test: {case['name']}"
            }
            
            response = self.http_request("POST", "/vms/", vm_config)
            if not response["success"]:
                print(f"    ❌ VM creation failed")
                continue
            
            vm_data = response["data"]
            vm_id = vm_data["vm_id"]
            vm_name = vm_data["name"]
            self.test_vms.append(vm_id)
            
            if self.wait_for_vm_ready(vm_id, timeout=90):
                # Quick injection to trigger detection logic
                injection_config = {
                    "file_id": self.test_file_id,
                    "injection_path": "/tmp/detection-test.exe" if case["vm_type"] == "docker" else "C:\\TurdParty\\detection-test.exe",
                    "execute_after_injection": False,
                    "permissions": "0755"
                }
                
                self.http_request("POST", f"/vms/{vm_id}/inject", injection_config)
                time.sleep(8)
                
                # Check logs for correct detection
                logs = self.get_celery_logs(vm_name)
                method_detected = False
                
                for log_line in logs:
                    if case["expected_method"] in log_line and vm_name in log_line:
                        method_detected = True
                        print(f"    ✅ Correct method detected: {case['expected_method']}")
                        break
                
                if method_detected:
                    success_count += 1
                else:
                    print(f"    ❌ Expected method '{case['expected_method']}' not found in logs")
            else:
                print(f"    ❌ VM failed to start")
        
        print(f"\n  Detection accuracy: {success_count}/{len(test_cases)} tests passed")
        return success_count == len(test_cases)
    
    def run_injection_logic_tests(self) -> bool:
        """Run all injection logic tests."""
        print("🧪 Starting gRPC Injection Logic Tests")
        print("=" * 80)
        
        tests = [
            ("Docker Injection Routing", self.test_docker_injection_routing),
            ("Vagrant gRPC Injection Routing", self.test_vagrant_injection_routing),
            ("VM Type Detection Accuracy", self.test_vm_type_detection_accuracy)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                    print(f"\n✅ {test_name}: PASSED")
                else:
                    print(f"\n❌ {test_name}: FAILED")
            except Exception as e:
                print(f"\n❌ {test_name}: ERROR - {e}")
                import traceback
                traceback.print_exc()
        
        print(f"\n📊 Injection Logic Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All injection logic tests completed successfully!")
            return True
        else:
            print("⚠️ Some injection logic tests failed!")
            return False


if __name__ == "__main__":
    tester = GRPCInjectionLogicTester()
    
    try:
        success = tester.run_injection_logic_tests()
        exit_code = 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        exit_code = 130
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        exit_code = 1
    finally:
        print("\n🧹 Cleaning up test resources...")
        tester.cleanup_resources()
    
    exit(exit_code)
