#!/usr/bin/env python3
"""
Simple Integration Tests for gRPC VM Injection System

Uses basic HTTP calls to test the complete workflow without external dependencies.
"""

import json
import subprocess
import time
import uuid
from typing import Dict, Any


class SimpleGRPCIntegrationTester:
    """Simple integration tester using curl for HTTP calls."""
    
    def __init__(self):
        self.api_base = "http://api.turdparty.localhost/api/v1"
        self.test_file_id = "902c5e5f-1109-4c1d-ba68-a50da622395d"
        self.test_vms = []
        
    def http_request(self, method: str, endpoint: str, data: Dict = None) -> Dict[str, Any]:
        """Make HTTP request using curl."""
        url = f"{self.api_base}{endpoint}"
        
        if method.upper() == "GET":
            cmd = ["curl", "-s", url]
        elif method.upper() == "POST":
            cmd = ["curl", "-s", "-X", "POST", "-H", "Content-Type: application/json"]
            if data:
                cmd.extend(["-d", json.dumps(data)])
            cmd.append(url)
        elif method.upper() == "DELETE":
            cmd = ["curl", "-s", "-X", "DELETE", url]
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0 and result.stdout.strip():
                try:
                    return {"success": True, "data": json.loads(result.stdout)}
                except json.JSONDecodeError:
                    return {"success": True, "data": {"raw": result.stdout}}
            else:
                return {"success": False, "error": result.stderr or "Request failed"}
        except subprocess.TimeoutExpired:
            return {"success": False, "error": "Request timeout"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def wait_for_vm_ready(self, vm_id: str, timeout: int = 60) -> bool:
        """Wait for VM to be in running state."""
        start_time = time.time()
        while time.time() - start_time < timeout:
            response = self.http_request("GET", f"/vms/{vm_id}")
            if response["success"] and "data" in response:
                vm_data = response["data"]
                if vm_data.get("status") == "running":
                    print(f"    ✅ VM {vm_id} is running")
                    return True
                else:
                    print(f"    ⏳ VM {vm_id} status: {vm_data.get('status', 'unknown')}")
            time.sleep(3)
        return False
    
    def wait_for_injection_complete(self, vm_id: str, injection_id: str, timeout: int = 120) -> bool:
        """Wait for injection to complete."""
        start_time = time.time()
        while time.time() - start_time < timeout:
            response = self.http_request("GET", f"/vms/{vm_id}/injections/{injection_id}")
            if response["success"] and "data" in response:
                injection_data = response["data"]
                status = injection_data.get("status")
                if status in ["MONITORING", "COMPLETED"]:
                    print(f"    ✅ Injection {injection_id} completed with status: {status}")
                    return True
                elif status == "FAILED":
                    print(f"    ❌ Injection {injection_id} failed: {injection_data.get('error_message')}")
                    return False
                else:
                    print(f"    ⏳ Injection {injection_id} status: {status}")
            time.sleep(5)
        return False
    
    def cleanup_resources(self):
        """Clean up test VMs."""
        for vm_id in self.test_vms:
            try:
                self.http_request("DELETE", f"/vms/{vm_id}")
                print(f"    🧹 Cleaned up VM: {vm_id}")
            except Exception:
                pass
        self.test_vms.clear()
    
    def test_docker_vm_workflow(self) -> bool:
        """Test complete Docker VM workflow."""
        print("\n🐳 Testing Docker VM Creation and Injection Workflow")
        print("=" * 60)
        
        # 1. Create Docker VM
        vm_config = {
            "name": f"test-docker-simple-{uuid.uuid4().hex[:8]}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 1024,
            "cpus": 1,
            "disk_gb": 10,
            "description": "Simple integration test Docker VM"
        }
        
        print("1. Creating Docker VM...")
        response = self.http_request("POST", "/vms/", vm_config)
        if not response["success"]:
            print(f"    ❌ VM creation failed: {response['error']}")
            return False
        
        vm_data = response["data"]
        vm_id = vm_data["vm_id"]
        self.test_vms.append(vm_id)
        print(f"    ✅ Docker VM created: {vm_id}")
        
        # 2. Wait for VM to be ready
        print("2. Waiting for VM to be ready...")
        if not self.wait_for_vm_ready(vm_id):
            print("    ❌ VM failed to reach running state")
            return False
        
        # 3. Inject file
        print("3. Injecting file...")
        injection_config = {
            "file_id": self.test_file_id,
            "injection_path": "/tmp/simple-test.exe",
            "execute_after_injection": True,
            "permissions": "0755"
        }
        
        response = self.http_request("POST", f"/vms/{vm_id}/inject", injection_config)
        if not response["success"]:
            print(f"    ❌ Injection failed: {response['error']}")
            return False
        
        injection_data = response["data"]
        injection_id = injection_data["injection_id"]
        print(f"    ✅ File injection queued: {injection_id}")
        
        # 4. Wait for injection to complete
        print("4. Waiting for injection to complete...")
        if not self.wait_for_injection_complete(vm_id, injection_id):
            print("    ❌ Injection failed to complete")
            return False
        
        print("    ✅ Docker VM workflow completed successfully!")
        return True
    
    def test_vagrant_vm_workflow(self) -> bool:
        """Test complete Vagrant VM workflow."""
        print("\n🏠 Testing Vagrant VM Creation and gRPC Injection Workflow")
        print("=" * 60)
        
        # 1. Create Vagrant VM
        vm_config = {
            "name": f"test-vagrant-simple-{uuid.uuid4().hex[:8]}",
            "template": "10Baht/windows10-turdparty",
            "vm_type": "vagrant",
            "memory_mb": 4096,
            "cpus": 2,
            "disk_gb": 50,
            "description": "Simple integration test Vagrant VM"
        }
        
        print("1. Creating Vagrant VM...")
        response = self.http_request("POST", "/vms/", vm_config)
        if not response["success"]:
            print(f"    ❌ VM creation failed: {response['error']}")
            return False
        
        vm_data = response["data"]
        vm_id = vm_data["vm_id"]
        self.test_vms.append(vm_id)
        print(f"    ✅ Vagrant VM created: {vm_id}")
        
        # 2. Wait for VM to be ready
        print("2. Waiting for VM to be ready...")
        if not self.wait_for_vm_ready(vm_id):
            print("    ❌ VM failed to reach running state")
            return False
        
        # 3. Inject file (should use gRPC path)
        print("3. Injecting file via gRPC...")
        injection_config = {
            "file_id": self.test_file_id,
            "injection_path": "C:\\TurdParty\\simple-test.exe",
            "execute_after_injection": True,
            "permissions": "0755"
        }
        
        response = self.http_request("POST", f"/vms/{vm_id}/inject", injection_config)
        if not response["success"]:
            print(f"    ❌ Injection failed: {response['error']}")
            return False
        
        injection_data = response["data"]
        injection_id = injection_data["injection_id"]
        print(f"    ✅ gRPC file injection queued: {injection_id}")
        
        # 4. Wait for injection to complete
        print("4. Waiting for gRPC injection to complete...")
        if not self.wait_for_injection_complete(vm_id, injection_id):
            print("    ❌ gRPC injection failed to complete")
            return False
        
        print("    ✅ Vagrant VM gRPC workflow completed successfully!")
        return True
    
    def test_vm_type_detection(self) -> bool:
        """Test VM type detection by creating different VM types."""
        print("\n🔍 Testing VM Type Detection")
        print("=" * 60)
        
        test_cases = [
            {
                "name": "docker-detection",
                "template": "ubuntu:20.04",
                "vm_type": "docker",
                "expected_id_format": "container"
            },
            {
                "name": "vagrant-detection",
                "template": "10Baht/windows10-turdparty", 
                "vm_type": "vagrant",
                "expected_id_format": "uuid"
            }
        ]
        
        success_count = 0
        
        for case in test_cases:
            print(f"Testing {case['name']}...")
            
            vm_config = {
                "name": f"test-{case['name']}-{uuid.uuid4().hex[:8]}",
                "template": case["template"],
                "vm_type": case["vm_type"],
                "memory_mb": 1024 if case["vm_type"] == "docker" else 4096,
                "cpus": 1 if case["vm_type"] == "docker" else 2,
                "disk_gb": 10 if case["vm_type"] == "docker" else 50,
                "description": f"Type detection test: {case['name']}"
            }
            
            response = self.http_request("POST", "/vms/", vm_config)
            if not response["success"]:
                print(f"    ❌ {case['name']} VM creation failed")
                continue
            
            vm_data = response["data"]
            vm_id = vm_data["vm_id"]
            self.test_vms.append(vm_id)
            
            # Wait for VM ready and check characteristics
            if self.wait_for_vm_ready(vm_id, timeout=90):
                # Get VM details
                response = self.http_request("GET", f"/vms/{vm_id}")
                if response["success"]:
                    vm_status = response["data"]
                    vm_id_external = vm_status.get("vm_id_external", "")
                    
                    # Verify VM ID format matches expected type
                    if case["expected_id_format"] == "container":
                        # Docker container IDs are 64 character hex
                        if len(vm_id_external) == 64:
                            print(f"    ✅ {case['name']}: Correct container ID format")
                            success_count += 1
                        else:
                            print(f"    ❌ {case['name']}: Unexpected ID format: {vm_id_external}")
                    elif case["expected_id_format"] == "uuid":
                        # Vagrant VMs use UUID format (36 chars with 4 dashes)
                        if len(vm_id_external) == 36 and vm_id_external.count("-") == 4:
                            print(f"    ✅ {case['name']}: Correct UUID format")
                            success_count += 1
                        else:
                            print(f"    ❌ {case['name']}: Unexpected ID format: {vm_id_external}")
                else:
                    print(f"    ❌ {case['name']}: Failed to get VM status")
            else:
                print(f"    ❌ {case['name']}: VM failed to start")
        
        print(f"VM type detection: {success_count}/{len(test_cases)} tests passed")
        return success_count == len(test_cases)
    
    def run_all_tests(self) -> bool:
        """Run all integration tests."""
        print("🚀 Starting Simple gRPC VM Injection Integration Tests")
        print("=" * 80)
        
        tests = [
            ("Docker VM Workflow", self.test_docker_vm_workflow),
            ("Vagrant VM Workflow", self.test_vagrant_vm_workflow),
            ("VM Type Detection", self.test_vm_type_detection)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                print(f"\n🧪 Running: {test_name}")
                if test_func():
                    passed += 1
                    print(f"✅ {test_name}: PASSED")
                else:
                    print(f"❌ {test_name}: FAILED")
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")
                import traceback
                traceback.print_exc()
        
        print(f"\n📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All integration tests completed successfully!")
            return True
        else:
            print("⚠️ Some integration tests failed!")
            return False


if __name__ == "__main__":
    tester = SimpleGRPCIntegrationTester()
    
    try:
        success = tester.run_all_tests()
        exit_code = 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        exit_code = 130
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        exit_code = 1
    finally:
        print("\n🧹 Cleaning up test resources...")
        tester.cleanup_resources()
    
    exit(exit_code)
