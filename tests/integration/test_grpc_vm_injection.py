"""
Integration Tests for gRPC VM Injection System

Tests the complete workflow from VM creation through file injection
for both Docker and Vagrant VMs using the new gRPC architecture.
"""

import json
import time
import uuid
from typing import Dict, Any
import requests
from pathlib import Path

# Test Configuration
API_BASE_URL = "http://api.turdparty.localhost/api/v1"
TEST_FILE_ID = "902c5e5f-1109-4c1d-ba68-a50da622395d"  # Known test file
TIMEOUT_VM_CREATION = 60  # seconds
TIMEOUT_INJECTION = 120   # seconds


class TestGRPCVMInjectionSystem:
    """Integration tests for the gRPC-enabled VM injection system."""
    
    def __init__(self):
        """Initialize test suite."""
        self.test_vms = []
        self.test_injections = []
    
    def _cleanup_test_resources(self):
        """Clean up test VMs and injections."""
        for vm_id in self.test_vms:
            try:
                requests.delete(f"{API_BASE_URL}/vms/{vm_id}")
            except Exception:
                pass
    
    def _wait_for_vm_ready(self, vm_id: str, timeout: int = TIMEOUT_VM_CREATION) -> Dict[str, Any]:
        """Wait for VM to be in running state."""
        start_time = time.time()
        while time.time() - start_time < timeout:
            response = requests.get(f"{API_BASE_URL}/vms/{vm_id}")
            if response.status_code == 200:
                vm_data = response.json()
                if vm_data.get("status") == "running":
                    return vm_data
            time.sleep(2)
        raise TimeoutError(f"VM {vm_id} did not reach running state within {timeout}s")
    
    def _wait_for_injection_complete(self, vm_id: str, injection_id: str, 
                                   timeout: int = TIMEOUT_INJECTION) -> Dict[str, Any]:
        """Wait for injection to complete."""
        start_time = time.time()
        while time.time() - start_time < timeout:
            response = requests.get(f"{API_BASE_URL}/vms/{vm_id}/injections/{injection_id}")
            if response.status_code == 200:
                injection_data = response.json()
                status = injection_data.get("status")
                if status in ["MONITORING", "COMPLETED"]:
                    return injection_data
                elif status == "FAILED":
                    raise RuntimeError(f"Injection failed: {injection_data.get('error_message')}")
            time.sleep(3)
        raise TimeoutError(f"Injection {injection_id} did not complete within {timeout}s")

    def test_docker_vm_creation_and_injection(self):
        """Test complete Docker VM workflow with injection."""
        print("\n🐳 Testing Docker VM creation and injection...")
        
        # 1. Create Docker VM
        vm_config = {
            "name": f"test-docker-integration-{uuid.uuid4().hex[:8]}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 1024,
            "cpus": 1,
            "disk_gb": 10,
            "description": "Integration test Docker VM"
        }
        
        response = requests.post(f"{API_BASE_URL}/vms/", json=vm_config)
        assert response.status_code == 200, f"VM creation failed: {response.text}"
        
        vm_data = response.json()
        vm_id = vm_data["vm_id"]
        self.test_vms.append(vm_id)
        
        print(f"✅ Docker VM created: {vm_id}")
        
        # 2. Wait for VM to be ready
        vm_status = self._wait_for_vm_ready(vm_id)
        assert vm_status["status"] == "running"
        assert vm_status["vm_id_external"] is not None  # Should have container ID
        print(f"✅ Docker VM running: {vm_status['vm_id_external']}")
        
        # 3. Inject file
        injection_config = {
            "file_id": TEST_FILE_ID,
            "injection_path": "/tmp/integration-test.exe",
            "execute_after_injection": True,
            "permissions": "0755"
        }
        
        response = requests.post(f"{API_BASE_URL}/vms/{vm_id}/inject", json=injection_config)
        assert response.status_code == 200, f"Injection failed: {response.text}"
        
        injection_data = response.json()
        injection_id = injection_data["injection_id"]
        self.test_injections.append(injection_id)
        
        print(f"✅ File injection queued: {injection_id}")
        
        # 4. Wait for injection to complete
        injection_status = self._wait_for_injection_complete(vm_id, injection_id)
        assert injection_status["status"] in ["MONITORING", "COMPLETED"]
        assert injection_status["injection_completed"] is True
        
        print(f"✅ Docker injection completed: {injection_status['current_step']}")
        
        # 5. Verify injection results
        results = injection_status.get("results", {})
        assert results.get("injection_completed") is True
        assert results.get("injection_path") == "/tmp/integration-test.exe"
        assert results.get("file_size") > 0
        
        print(f"✅ Docker injection verified: {results['file_size']} bytes")

    def test_vagrant_vm_creation_and_grpc_injection(self):
        """Test complete Vagrant VM workflow with gRPC injection."""
        print("\n🏠 Testing Vagrant VM creation and gRPC injection...")
        
        # 1. Create Vagrant VM
        vm_config = {
            "name": f"test-vagrant-integration-{uuid.uuid4().hex[:8]}",
            "template": "10Baht/windows10-turdparty",
            "vm_type": "vagrant",
            "memory_mb": 4096,
            "cpus": 2,
            "disk_gb": 50,
            "description": "Integration test Vagrant VM with gRPC"
        }
        
        response = requests.post(f"{API_BASE_URL}/vms/", json=vm_config)
        assert response.status_code == 200, f"VM creation failed: {response.text}"
        
        vm_data = response.json()
        vm_id = vm_data["vm_id"]
        self.test_vms.append(vm_id)
        
        print(f"✅ Vagrant VM created: {vm_id}")
        
        # 2. Wait for VM to be ready
        vm_status = self._wait_for_vm_ready(vm_id)
        assert vm_status["status"] == "running"
        assert len(vm_status["vm_id_external"]) == 36  # Should be UUID format
        assert vm_status["ip_address"] is not None
        
        print(f"✅ Vagrant VM running: {vm_status['vm_id_external']} @ {vm_status['ip_address']}")
        
        # 3. Inject file (should use gRPC path)
        injection_config = {
            "file_id": TEST_FILE_ID,
            "injection_path": "C:\\TurdParty\\integration-test.exe",
            "execute_after_injection": True,
            "permissions": "0755"
        }
        
        response = requests.post(f"{API_BASE_URL}/vms/{vm_id}/inject", json=injection_config)
        assert response.status_code == 200, f"Injection failed: {response.text}"
        
        injection_data = response.json()
        injection_id = injection_data["injection_id"]
        self.test_injections.append(injection_id)
        
        print(f"✅ gRPC file injection queued: {injection_id}")
        
        # 4. Wait for injection to complete
        injection_status = self._wait_for_injection_complete(vm_id, injection_id)
        assert injection_status["status"] in ["MONITORING", "COMPLETED"]
        assert injection_status["injection_completed"] is True
        
        print(f"✅ gRPC injection completed: {injection_status['current_step']}")
        
        # 5. Verify injection results
        results = injection_status.get("results", {})
        assert results.get("injection_completed") is True
        assert results.get("injection_path") == "C:\\TurdParty\\integration-test.exe"
        assert results.get("file_size") > 0
        
        print(f"✅ gRPC injection verified: {results['file_size']} bytes")

    def test_vm_type_detection_accuracy(self):
        """Test that VM type detection correctly routes injection methods."""
        print("\n🔍 Testing VM type detection accuracy...")
        
        test_cases = [
            {
                "name": "docker-detection-test",
                "template": "ubuntu:20.04",
                "vm_type": "docker",
                "expected_method": "docker"
            },
            {
                "name": "vagrant-detection-test", 
                "template": "10Baht/windows10-turdparty",
                "vm_type": "vagrant",
                "expected_method": "grpc"
            },
            {
                "name": "windows-detection-test",
                "template": "gusztavvargadr/windows-10",
                "vm_type": "vagrant", 
                "expected_method": "grpc"
            }
        ]
        
        for case in test_cases:
            print(f"  Testing {case['name']}...")
            
            # Create VM
            vm_config = {
                "name": f"test-{case['name']}-{uuid.uuid4().hex[:8]}",
                "template": case["template"],
                "vm_type": case["vm_type"],
                "memory_mb": 1024 if case["vm_type"] == "docker" else 4096,
                "cpus": 1 if case["vm_type"] == "docker" else 2,
                "disk_gb": 10 if case["vm_type"] == "docker" else 50,
                "description": f"VM type detection test: {case['name']}"
            }
            
            response = requests.post(f"{API_BASE_URL}/vms/", json=vm_config)
            assert response.status_code == 200
            
            vm_data = response.json()
            vm_id = vm_data["vm_id"]
            self.test_vms.append(vm_id)
            
            # Wait for VM ready
            vm_status = self._wait_for_vm_ready(vm_id)
            
            # Verify VM characteristics match expected type
            if case["expected_method"] == "docker":
                # Docker VMs should have container-style IDs
                assert len(vm_status["vm_id_external"]) == 64  # Container ID length
                assert vm_status["ip_address"].startswith("172.17.")  # Docker network
            else:
                # Vagrant VMs should have UUID-style IDs
                assert len(vm_status["vm_id_external"]) == 36  # UUID length
                assert vm_status["vm_id_external"].count("-") == 4  # UUID format
            
            print(f"    ✅ {case['name']}: {case['expected_method']} method detected")

    def test_injection_error_handling(self):
        """Test error handling in injection system."""
        print("\n⚠️  Testing injection error handling...")
        
        # Test injection to non-existent VM
        fake_vm_id = str(uuid.uuid4())
        injection_config = {
            "file_id": TEST_FILE_ID,
            "injection_path": "/tmp/error-test.exe",
            "execute_after_injection": False
        }
        
        response = requests.post(f"{API_BASE_URL}/vms/{fake_vm_id}/inject", json=injection_config)
        assert response.status_code == 404, "Should fail for non-existent VM"
        print("✅ Non-existent VM injection properly rejected")
        
        # Test injection with invalid file ID
        # First create a valid VM
        vm_config = {
            "name": f"test-error-handling-{uuid.uuid4().hex[:8]}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 1024,
            "cpus": 1,
            "disk_gb": 10,
            "description": "Error handling test VM"
        }
        
        response = requests.post(f"{API_BASE_URL}/vms/", json=vm_config)
        assert response.status_code == 200
        
        vm_data = response.json()
        vm_id = vm_data["vm_id"]
        self.test_vms.append(vm_id)
        
        # Wait for VM ready
        self._wait_for_vm_ready(vm_id)
        
        # Try injection with invalid file ID
        invalid_injection_config = {
            "file_id": str(uuid.uuid4()),  # Non-existent file
            "injection_path": "/tmp/invalid-file-test.exe",
            "execute_after_injection": False
        }
        
        response = requests.post(f"{API_BASE_URL}/vms/{vm_id}/inject", json=invalid_injection_config)
        # This might succeed initially but fail during processing
        if response.status_code == 200:
            injection_data = response.json()
            injection_id = injection_data["injection_id"]
            
            # Wait and check if it fails properly
            time.sleep(10)
            response = requests.get(f"{API_BASE_URL}/vms/{vm_id}/injections/{injection_id}")
            if response.status_code == 200:
                injection_status = response.json()
                # Should either be failed or still processing
                assert injection_status["status"] in ["FAILED", "PROCESSING", "QUEUED"]
                print("✅ Invalid file ID injection handled appropriately")

    def test_concurrent_injections(self):
        """Test handling of concurrent injections."""
        print("\n🔄 Testing concurrent injections...")
        
        # Create multiple VMs
        vm_configs = [
            {
                "name": f"test-concurrent-{i}-{uuid.uuid4().hex[:8]}",
                "template": "ubuntu:20.04",
                "vm_type": "docker",
                "memory_mb": 1024,
                "cpus": 1,
                "disk_gb": 10,
                "description": f"Concurrent test VM {i}"
            }
            for i in range(3)
        ]
        
        # Create all VMs
        vm_ids = []
        for config in vm_configs:
            response = requests.post(f"{API_BASE_URL}/vms/", json=config)
            assert response.status_code == 200
            vm_data = response.json()
            vm_id = vm_data["vm_id"]
            vm_ids.append(vm_id)
            self.test_vms.append(vm_id)
        
        # Wait for all VMs to be ready
        for vm_id in vm_ids:
            self._wait_for_vm_ready(vm_id)
            print(f"  ✅ VM {vm_id} ready")
        
        # Start concurrent injections
        injection_ids = []
        for i, vm_id in enumerate(vm_ids):
            injection_config = {
                "file_id": TEST_FILE_ID,
                "injection_path": f"/tmp/concurrent-test-{i}.exe",
                "execute_after_injection": True,
                "permissions": "0755"
            }
            
            response = requests.post(f"{API_BASE_URL}/vms/{vm_id}/inject", json=injection_config)
            assert response.status_code == 200
            injection_data = response.json()
            injection_ids.append((vm_id, injection_data["injection_id"]))
            print(f"  ✅ Injection {i} queued: {injection_data['injection_id']}")
        
        # Wait for all injections to complete
        completed_injections = 0
        for vm_id, injection_id in injection_ids:
            try:
                injection_status = self._wait_for_injection_complete(vm_id, injection_id)
                if injection_status["injection_completed"]:
                    completed_injections += 1
                    print(f"  ✅ Injection {injection_id} completed")
            except Exception as e:
                print(f"  ⚠️ Injection {injection_id} failed: {e}")
        
        assert completed_injections >= 2, f"Expected at least 2 successful injections, got {completed_injections}"
        print(f"✅ Concurrent injections: {completed_injections}/{len(injection_ids)} successful")


if __name__ == "__main__":
    # Run tests manually if executed directly
    test_suite = TestGRPCVMInjectionSystem()
    test_suite.setup_test_data()
    
    try:
        print("🧪 Starting gRPC VM Injection Integration Tests...")
        test_suite.test_docker_vm_creation_and_injection()
        test_suite.test_vagrant_vm_creation_and_grpc_injection()
        test_suite.test_vm_type_detection_accuracy()
        test_suite.test_injection_error_handling()
        test_suite.test_concurrent_injections()
        print("\n🎉 All integration tests completed successfully!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
    finally:
        test_suite._cleanup_test_resources()
