"""
💩🎉TurdParty🎉💩 Mock Detection and Enforcement Tests

This test suite ensures that NO mock, simulation, or fake functionality
exists in the production API code.

Tests cover:
- Source code scanning for mock imports
- Runtime detection of mock objects
- Verification of real implementations
- Detection of hardcoded test data
- Enforcement of real-only policy
"""

import pytest
import os
import re
import ast
import inspect
import logging
from typing import List, Dict, Any, Set
from pathlib import Path

logger = logging.getLogger(__name__)

# Directories to scan for mock code
API_DIRECTORIES = [
    "services/api/src",
    "api/services",
    "api/models",
]

# Files that are allowed to have mocks (test files only)
ALLOWED_MOCK_FILES = {
    "test_",
    "conftest.py",
    "_test.py",
    "mock_",
    "fake_",
}

# Forbidden mock patterns
FORBIDDEN_PATTERNS = [
    r"from unittest\.mock import",
    r"from mock import",
    r"import mock",
    r"@mock\.",
    r"@patch\(",
    r"Mock\(",
    r"MagicMock\(",
    r"AsyncMock\(",
    r"mock_",
    r"fake_",
    r"simulate_",
    r"dummy_",
    r"test_data_",
]

# Suspicious hardcoded values that might indicate mock data
SUSPICIOUS_VALUES = [
    "25.5",      # Common mock CPU percentage
    "45.2",      # Common mock memory percentage  
    "1024.0",    # Common mock memory MB
    "1048576",   # Common mock network bytes (1MB)
    "524288",    # Common mock network bytes (512KB)
    "3600",      # Common mock uptime (1 hour)
    "test_vm",   # Mock VM ID
    "fake_container",  # Mock container name
    "dummy_process",   # Mock process name
]


class TestNoMocksEnforcement:
    """Test suite to enforce no mocks in production code"""
    
    def get_api_files(self) -> List[Path]:
        """Get all Python files in API directories"""
        files = []
        
        for directory in API_DIRECTORIES:
            if os.path.exists(directory):
                for root, dirs, filenames in os.walk(directory):
                    for filename in filenames:
                        if filename.endswith('.py'):
                            file_path = Path(root) / filename
                            files.append(file_path)
        
        return files
    
    def is_test_file(self, file_path: Path) -> bool:
        """Check if a file is a test file (allowed to have mocks)"""
        filename = file_path.name.lower()
        
        for allowed_pattern in ALLOWED_MOCK_FILES:
            if allowed_pattern in filename:
                return True
        
        # Check if in test directory
        if "test" in str(file_path).lower():
            return True
        
        return False
    
    def scan_file_for_mocks(self, file_path: Path) -> List[Dict[str, Any]]:
        """Scan a file for mock patterns"""
        violations = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                for pattern in FORBIDDEN_PATTERNS:
                    if re.search(pattern, line, re.IGNORECASE):
                        violations.append({
                            "file": str(file_path),
                            "line": line_num,
                            "pattern": pattern,
                            "content": line.strip(),
                            "type": "mock_import_or_usage"
                        })
        
        except Exception as e:
            logger.warning(f"Could not scan file {file_path}: {e}")
        
        return violations
    
    def scan_file_for_suspicious_values(self, file_path: Path) -> List[Dict[str, Any]]:
        """Scan a file for suspicious hardcoded values"""
        violations = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                stripped_line = line.strip()
                
                # Skip comments and docstrings
                if stripped_line.startswith('#') or stripped_line.startswith('"""') or stripped_line.startswith("'''"):
                    continue
                
                # Skip log messages and error handling
                if any(keyword in stripped_line.lower() for keyword in ['logger.', 'log(', 'error', 'exception']):
                    continue
                
                for suspicious_value in SUSPICIOUS_VALUES:
                    if suspicious_value in line:
                        # Check if it's in a return statement or assignment (more suspicious)
                        if any(keyword in stripped_line for keyword in ['return', '=', ':']):
                            violations.append({
                                "file": str(file_path),
                                "line": line_num,
                                "value": suspicious_value,
                                "content": line.strip(),
                                "type": "suspicious_hardcoded_value"
                            })
        
        except Exception as e:
            logger.warning(f"Could not scan file {file_path}: {e}")
        
        return violations
    
    def test_no_mock_imports_in_production_code(self):
        """Test that production code has no mock imports"""
        api_files = self.get_api_files()
        violations = []
        
        for file_path in api_files:
            if self.is_test_file(file_path):
                continue  # Skip test files
            
            file_violations = self.scan_file_for_mocks(file_path)
            violations.extend(file_violations)
        
        if violations:
            violation_summary = "\n".join([
                f"  {v['file']}:{v['line']} - {v['pattern']} - {v['content']}"
                for v in violations
            ])
            pytest.fail(f"Found {len(violations)} mock violations in production code:\n{violation_summary}")
        
        logger.info(f"✅ No mock imports found in {len(api_files)} production files")
    
    def test_no_suspicious_hardcoded_values(self):
        """Test that production code has no suspicious hardcoded values"""
        api_files = self.get_api_files()
        violations = []
        
        for file_path in api_files:
            if self.is_test_file(file_path):
                continue  # Skip test files
            
            file_violations = self.scan_file_for_suspicious_values(file_path)
            violations.extend(file_violations)
        
        # Filter out false positives (allow some common values in specific contexts)
        filtered_violations = []
        for violation in violations:
            content = violation['content'].lower()
            
            # Allow timeout values
            if 'timeout' in content or 'sleep' in content:
                continue
            
            # Allow buffer sizes
            if 'buffer' in content or 'size' in content:
                continue
            
            # Allow configuration defaults
            if 'default' in content or 'config' in content:
                continue
            
            filtered_violations.append(violation)
        
        if filtered_violations:
            violation_summary = "\n".join([
                f"  {v['file']}:{v['line']} - {v['value']} - {v['content']}"
                for v in filtered_violations
            ])
            logger.warning(f"Found {len(filtered_violations)} suspicious hardcoded values:\n{violation_summary}")
            # Don't fail the test, just warn (these might be legitimate)
        
        logger.info(f"✅ Scanned {len(api_files)} files for suspicious values")
    
    def test_vm_metrics_service_is_real(self):
        """Test that VM metrics service uses real implementations"""
        # Import the service
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '../../services/api/src'))
        
        try:
            from services.vm_metrics_service import vm_metrics_service, VMMetricsService
        except ImportError:
            # Try alternative path
            sys.path.append(os.path.join(os.path.dirname(__file__), '../../api/services'))
            from vm_metrics_service import vm_metrics_service, VMMetricsService
        
        # Check that it's a real class, not a mock
        assert not hasattr(vm_metrics_service, '_mock_name'), "VM metrics service appears to be a mock object"
        assert not hasattr(vm_metrics_service, 'assert_called'), "VM metrics service appears to be a mock object"
        
        # Check methods exist and are real
        assert hasattr(vm_metrics_service, 'get_vm_metrics')
        assert hasattr(vm_metrics_service, 'initialize')
        assert callable(vm_metrics_service.get_vm_metrics)
        assert callable(vm_metrics_service.initialize)
        
        # Check source code doesn't contain mock patterns
        source = inspect.getsource(VMMetricsService)
        for pattern in FORBIDDEN_PATTERNS:
            assert not re.search(pattern, source, re.IGNORECASE), f"Found forbidden pattern {pattern} in VM metrics service"
        
        logger.info("✅ VM metrics service verified as real implementation")
    
    def test_vm_service_is_real(self):
        """Test that VM service uses real implementations"""
        # Import the service
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '../../api/services'))
        
        try:
            from vm_service import VMService
        except ImportError:
            pytest.skip("VM service not found - may be in different location")
        
        # Create instance
        vm_service = VMService()
        
        # Check that it's a real class, not a mock
        assert not hasattr(vm_service, '_mock_name'), "VM service appears to be a mock object"
        assert not hasattr(vm_service, 'assert_called'), "VM service appears to be a mock object"
        
        # Check methods exist and are real
        assert hasattr(vm_service, 'create_vm')
        assert hasattr(vm_service, 'get_vm')
        assert callable(vm_service.create_vm)
        assert callable(vm_service.get_vm)
        
        logger.info("✅ VM service verified as real implementation")
    
    def test_api_routes_use_real_services(self):
        """Test that API routes import and use real services"""
        # Check VM routes
        vm_routes_file = None
        possible_paths = [
            "services/api/src/routes/v1/vms.py",
            "api/routes/v1/vms.py",
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                vm_routes_file = path
                break
        
        if not vm_routes_file:
            pytest.skip("VM routes file not found")
        
        # Scan the routes file
        violations = self.scan_file_for_mocks(Path(vm_routes_file))
        
        # Filter out allowed patterns (like error handling)
        serious_violations = [
            v for v in violations 
            if not any(allowed in v['content'].lower() for allowed in ['except', 'error', 'log'])
        ]
        
        if serious_violations:
            violation_summary = "\n".join([
                f"  Line {v['line']}: {v['content']}"
                for v in serious_violations
            ])
            pytest.fail(f"Found mock usage in API routes:\n{violation_summary}")
        
        logger.info("✅ API routes verified to use real services")
    
    def test_docker_client_is_real(self):
        """Test that Docker client usage is real, not mocked"""
        # Import VM metrics service
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '../../services/api/src'))
        
        try:
            from services.vm_metrics_service import vm_metrics_service
        except ImportError:
            sys.path.append(os.path.join(os.path.dirname(__file__), '../../api/services'))
            from vm_metrics_service import vm_metrics_service
        
        # Initialize the service
        import asyncio
        asyncio.run(vm_metrics_service.initialize())
        
        # Check Docker client
        if vm_metrics_service.docker_client:
            # Should be a real Docker client, not a mock
            client = vm_metrics_service.docker_client
            assert not hasattr(client, '_mock_name'), "Docker client appears to be a mock"
            assert not hasattr(client, 'assert_called'), "Docker client appears to be a mock"
            
            # Should have real Docker client methods
            assert hasattr(client, 'containers')
            assert hasattr(client, 'images')
            
            logger.info("✅ Docker client verified as real implementation")
        else:
            logger.warning("⚠️ Docker client not available (may be expected in test environment)")
    
    def test_no_mock_fixtures_in_production(self):
        """Test that production code doesn't accidentally import test fixtures"""
        api_files = self.get_api_files()
        violations = []
        
        fixture_patterns = [
            r"@pytest\.fixture",
            r"from.*conftest import",
            r"import.*conftest",
            r"mock_.*_service",
            r"fake_.*_service",
        ]
        
        for file_path in api_files:
            if self.is_test_file(file_path):
                continue  # Skip test files
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                
                for line_num, line in enumerate(lines, 1):
                    for pattern in fixture_patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            violations.append({
                                "file": str(file_path),
                                "line": line_num,
                                "pattern": pattern,
                                "content": line.strip()
                            })
            
            except Exception as e:
                logger.warning(f"Could not scan file {file_path}: {e}")
        
        if violations:
            violation_summary = "\n".join([
                f"  {v['file']}:{v['line']} - {v['content']}"
                for v in violations
            ])
            pytest.fail(f"Found test fixture usage in production code:\n{violation_summary}")
        
        logger.info(f"✅ No test fixtures found in {len(api_files)} production files")
