"""
Parallel Multi-VM Analysis Test Suite
Tests TurdParty's ability to handle multiple concurrent analyses
"""

import pytest
import time
import requests
import asyncio
import concurrent.futures
from datetime import datetime
from typing import Dict, List, Any


class TestParallelAnalysis:
    """Test parallel analysis capabilities with multiple VMs."""
    
    # Class variables for tracking parallel operations
    parallel_vms = {}
    parallel_analyses = {}
    performance_metrics = {}
    
    @pytest.fixture(scope="class")
    def parallel_config(self):
        """Configuration for parallel analysis testing."""
        return {
            "api_base_url": "http://localhost:8000/api/v1",
            "elasticsearch_url": "http://localhost:9200",
            "concurrent_vms": 3,
            "test_scenarios": [
                {
                    "name": "windows-installer",
                    "template": "custom",
                    "file_type": "installer",
                    "expected_events": ["file", "process", "registry"]
                },
                {
                    "name": "linux-script", 
                    "template": "ubuntu-20.04",
                    "file_type": "script",
                    "expected_events": ["file", "process"]
                },
                {
                    "name": "windows-executable",
                    "template": "custom",
                    "file_type": "executable", 
                    "expected_events": ["file", "process", "network"]
                }
            ],
            "performance_thresholds": {
                "vm_creation_time": 300,  # 5 minutes max
                "injection_time": 60,     # 1 minute max
                "telemetry_delay": 30     # 30 seconds max
            }
        }
    
    def test_01_create_parallel_vms(self, parallel_config):
        """Create multiple VMs in parallel."""
        print(f"🚀 Creating {parallel_config['concurrent_vms']} VMs in parallel...")
        
        def create_vm(scenario_index, scenario):
            """Create a single VM."""
            vm_name = f"parallel-{scenario['name']}-{scenario_index}-{int(time.time())}"
            
            vm_data = {
                "name": vm_name,
                "template": scenario["template"],
                "vm_type": "vagrant",
                "memory_mb": 2048,  # Reduced for parallel testing
                "cpus": 1,
                "disk_gb": 20,
                "description": f"Parallel test VM for {scenario['name']}",
                "auto_start": True,
                "provision_script": f"# {scenario['template']} template\nconfig.vm.box = 'gusztavvargadr/windows-10'" if scenario["template"] == "custom" else None
            }
            
            start_time = time.time()
            
            try:
                response = requests.post(
                    f"{parallel_config['api_base_url']}/vms/",
                    json=vm_data,
                    timeout=30
                )
                
                creation_time = time.time() - start_time
                
                if response.status_code == 201:
                    vm_info = response.json()
                    vm_id = vm_info["vm_id"]
                    
                    return {
                        "scenario_index": scenario_index,
                        "scenario": scenario,
                        "vm_id": vm_id,
                        "vm_name": vm_name,
                        "creation_time": creation_time,
                        "status": "created",
                        "error": None
                    }
                else:
                    return {
                        "scenario_index": scenario_index,
                        "scenario": scenario,
                        "vm_id": None,
                        "vm_name": vm_name,
                        "creation_time": creation_time,
                        "status": "failed",
                        "error": f"HTTP {response.status_code}: {response.text[:100]}"
                    }
            except Exception as e:
                return {
                    "scenario_index": scenario_index,
                    "scenario": scenario,
                    "vm_id": None,
                    "vm_name": vm_name,
                    "creation_time": time.time() - start_time,
                    "status": "error",
                    "error": str(e)
                }
        
        # Create VMs in parallel using ThreadPoolExecutor
        with concurrent.futures.ThreadPoolExecutor(max_workers=parallel_config['concurrent_vms']) as executor:
            futures = []
            
            for i, scenario in enumerate(parallel_config['test_scenarios']):
                future = executor.submit(create_vm, i, scenario)
                futures.append(future)
            
            # Collect results
            vm_results = []
            for future in concurrent.futures.as_completed(futures):
                result = future.result()
                vm_results.append(result)
                
                if result["status"] == "created":
                    print(f"✅ VM {result['scenario_index']}: {result['vm_id']} ({result['creation_time']:.1f}s)")
                    TestParallelAnalysis.parallel_vms[result["scenario_index"]] = result
                else:
                    print(f"❌ VM {result['scenario_index']}: {result['status']} - {result['error']}")
        
        # Verify at least some VMs were created
        successful_vms = len([r for r in vm_results if r["status"] == "created"])
        assert successful_vms > 0, f"No VMs created successfully. Results: {vm_results}"
        
        print(f"✅ Parallel VM creation completed: {successful_vms}/{len(vm_results)} successful")
        
        # Store performance metrics
        creation_times = [r["creation_time"] for r in vm_results if r["status"] == "created"]
        TestParallelAnalysis.performance_metrics["vm_creation"] = {
            "successful": successful_vms,
            "total": len(vm_results),
            "avg_time": sum(creation_times) / len(creation_times) if creation_times else 0,
            "max_time": max(creation_times) if creation_times else 0,
            "min_time": min(creation_times) if creation_times else 0
        }
    
    def test_02_wait_for_parallel_vm_readiness(self, parallel_config):
        """Wait for all VMs to be ready in parallel."""
        print("⏳ Waiting for parallel VMs to be ready...")
        
        def wait_for_vm_ready(vm_info):
            """Wait for a single VM to be ready."""
            vm_id = vm_info["vm_id"]
            scenario_index = vm_info["scenario_index"]
            start_time = time.time()
            
            while time.time() - start_time < parallel_config['performance_thresholds']['vm_creation_time']:
                try:
                    response = requests.get(f"{parallel_config['api_base_url']}/vms/{vm_id}")
                    if response.status_code == 200:
                        vm_status = response.json()
                        if vm_status.get("status") == "running":
                            ready_time = time.time() - start_time
                            print(f"✅ VM {scenario_index} ready: {vm_id} ({ready_time:.1f}s)")
                            return {
                                "scenario_index": scenario_index,
                                "vm_id": vm_id,
                                "ready_time": ready_time,
                                "status": "ready"
                            }
                except Exception as e:
                    print(f"⚠️ VM {scenario_index} check error: {e}")
                
                time.sleep(10)
            
            return {
                "scenario_index": scenario_index,
                "vm_id": vm_id,
                "ready_time": time.time() - start_time,
                "status": "timeout"
            }
        
        # Wait for VMs in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(TestParallelAnalysis.parallel_vms)) as executor:
            futures = []
            
            for vm_info in TestParallelAnalysis.parallel_vms.values():
                future = executor.submit(wait_for_vm_ready, vm_info)
                futures.append(future)
            
            # Collect readiness results
            ready_results = []
            for future in concurrent.futures.as_completed(futures):
                result = future.result()
                ready_results.append(result)
        
        # Update VM info with readiness status
        for result in ready_results:
            scenario_index = result["scenario_index"]
            if scenario_index in TestParallelAnalysis.parallel_vms:
                TestParallelAnalysis.parallel_vms[scenario_index].update({
                    "ready_time": result["ready_time"],
                    "ready_status": result["status"]
                })
        
        ready_vms = len([r for r in ready_results if r["status"] == "ready"])
        print(f"✅ VM readiness check completed: {ready_vms}/{len(ready_results)} ready")
        
        # Update performance metrics
        ready_times = [r["ready_time"] for r in ready_results if r["status"] == "ready"]
        TestParallelAnalysis.performance_metrics["vm_readiness"] = {
            "ready": ready_vms,
            "total": len(ready_results),
            "avg_time": sum(ready_times) / len(ready_times) if ready_times else 0,
            "max_time": max(ready_times) if ready_times else 0
        }
    
    def test_03_parallel_file_injections(self, parallel_config):
        """Execute file injections in parallel across multiple VMs."""
        print("💉 Executing parallel file injections...")
        
        def inject_file_to_vm(vm_info):
            """Inject file to a single VM."""
            vm_id = vm_info["vm_id"]
            scenario_index = vm_info["scenario_index"]
            scenario = vm_info["scenario"]
            
            if vm_info.get("ready_status") != "ready":
                return {
                    "scenario_index": scenario_index,
                    "vm_id": vm_id,
                    "status": "skipped",
                    "error": "VM not ready"
                }
            
            start_time = time.time()
            
            # Prepare injection based on scenario
            if scenario["file_type"] == "installer":
                injection_data = {
                    "file_path": "/tmp/notepadpp-installer.exe",
                    "injection_path": "/tmp/test_installer.exe"
                }
            elif scenario["file_type"] == "script":
                injection_data = {
                    "file_path": "/tmp/test_script.sh",
                    "injection_path": "/tmp/test_script.sh"
                }
            else:  # executable
                injection_data = {
                    "file_path": "/tmp/test_executable.exe",
                    "injection_path": "/tmp/test_executable.exe"
                }
            
            try:
                response = requests.post(
                    f"{parallel_config['api_base_url']}/vms/{vm_id}/inject",
                    json=injection_data,
                    timeout=parallel_config['performance_thresholds']['injection_time']
                )
                
                injection_time = time.time() - start_time
                
                if response.status_code == 200:
                    injection_result = response.json()
                    injection_id = injection_result.get("injection_id") or injection_result.get("task_id")
                    
                    return {
                        "scenario_index": scenario_index,
                        "vm_id": vm_id,
                        "injection_id": injection_id,
                        "injection_time": injection_time,
                        "status": "injected",
                        "error": None
                    }
                else:
                    return {
                        "scenario_index": scenario_index,
                        "vm_id": vm_id,
                        "injection_id": None,
                        "injection_time": injection_time,
                        "status": "failed",
                        "error": f"HTTP {response.status_code}"
                    }
            except Exception as e:
                return {
                    "scenario_index": scenario_index,
                    "vm_id": vm_id,
                    "injection_id": None,
                    "injection_time": time.time() - start_time,
                    "status": "error",
                    "error": str(e)
                }
        
        # Execute injections in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(TestParallelAnalysis.parallel_vms)) as executor:
            futures = []
            
            for vm_info in TestParallelAnalysis.parallel_vms.values():
                future = executor.submit(inject_file_to_vm, vm_info)
                futures.append(future)
            
            # Collect injection results
            injection_results = []
            for future in concurrent.futures.as_completed(futures):
                result = future.result()
                injection_results.append(result)
                
                if result["status"] == "injected":
                    print(f"✅ Injection {result['scenario_index']}: {result['injection_id']} ({result['injection_time']:.1f}s)")
                    TestParallelAnalysis.parallel_analyses[result["scenario_index"]] = result
                else:
                    print(f"❌ Injection {result['scenario_index']}: {result['status']} - {result.get('error', 'Unknown')}")
        
        successful_injections = len([r for r in injection_results if r["status"] == "injected"])
        print(f"✅ Parallel injections completed: {successful_injections}/{len(injection_results)} successful")
        
        # Store performance metrics
        injection_times = [r["injection_time"] for r in injection_results if r["status"] == "injected"]
        TestParallelAnalysis.performance_metrics["injections"] = {
            "successful": successful_injections,
            "total": len(injection_results),
            "avg_time": sum(injection_times) / len(injection_times) if injection_times else 0,
            "max_time": max(injection_times) if injection_times else 0
        }
    
    def test_04_monitor_parallel_telemetry(self, parallel_config):
        """Monitor telemetry collection from parallel analyses."""
        print("📡 Monitoring parallel telemetry collection...")
        
        # Get baseline event count
        response = requests.get(f"{parallel_config['elasticsearch_url']}/turdparty-*/_count")
        baseline_events = response.json().get("count", 0) if response.status_code == 200 else 0
        
        print(f"📊 Baseline events: {baseline_events}")
        
        # Monitor for 3 minutes
        monitoring_duration = 180
        check_interval = 30
        telemetry_timeline = []
        
        start_time = time.time()
        
        while time.time() - start_time < monitoring_duration:
            current_time = datetime.now(datetime.UTC).isoformat()
            
            # Check total events
            response = requests.get(f"{parallel_config['elasticsearch_url']}/turdparty-*/_count")
            if response.status_code == 200:
                current_events = response.json().get("count", 0)
                new_events = current_events - baseline_events
                
                # Check events by category
                event_categories = self._check_parallel_event_categories(parallel_config)
                
                telemetry_timeline.append({
                    "timestamp": current_time,
                    "total_events": current_events,
                    "new_events": new_events,
                    "categories": event_categories,
                    "rate": new_events / ((time.time() - start_time) / 60)
                })
                
                print(f"📊 {current_time}: {current_events} total ({new_events} new, {telemetry_timeline[-1]['rate']:.1f}/min)")
                print(f"📋 Categories: {list(event_categories.keys())}")
            
            time.sleep(check_interval)
        
        TestParallelAnalysis.performance_metrics["telemetry"] = {
            "timeline": telemetry_timeline,
            "final_new_events": telemetry_timeline[-1]["new_events"] if telemetry_timeline else 0,
            "avg_rate": sum(t["rate"] for t in telemetry_timeline) / len(telemetry_timeline) if telemetry_timeline else 0
        }
        
        final_events = telemetry_timeline[-1]["new_events"] if telemetry_timeline else 0
        print(f"✅ Parallel telemetry monitoring completed: {final_events} new events")
    
    def test_05_validate_parallel_performance(self, parallel_config):
        """Validate performance of parallel operations."""
        print("⚡ Validating parallel performance...")
        
        performance_summary = {}
        
        # VM Creation Performance
        vm_metrics = TestParallelAnalysis.performance_metrics.get("vm_creation", {})
        vm_performance = {
            "success_rate": vm_metrics.get("successful", 0) / vm_metrics.get("total", 1) * 100,
            "avg_creation_time": vm_metrics.get("avg_time", 0),
            "max_creation_time": vm_metrics.get("max_time", 0),
            "within_threshold": vm_metrics.get("max_time", 0) <= parallel_config['performance_thresholds']['vm_creation_time']
        }
        performance_summary["vm_creation"] = vm_performance
        
        # VM Readiness Performance
        readiness_metrics = TestParallelAnalysis.performance_metrics.get("vm_readiness", {})
        readiness_performance = {
            "ready_rate": readiness_metrics.get("ready", 0) / readiness_metrics.get("total", 1) * 100,
            "avg_ready_time": readiness_metrics.get("avg_time", 0),
            "max_ready_time": readiness_metrics.get("max_time", 0)
        }
        performance_summary["vm_readiness"] = readiness_performance
        
        # Injection Performance
        injection_metrics = TestParallelAnalysis.performance_metrics.get("injections", {})
        injection_performance = {
            "success_rate": injection_metrics.get("successful", 0) / injection_metrics.get("total", 1) * 100,
            "avg_injection_time": injection_metrics.get("avg_time", 0),
            "max_injection_time": injection_metrics.get("max_time", 0),
            "within_threshold": injection_metrics.get("max_time", 0) <= parallel_config['performance_thresholds']['injection_time']
        }
        performance_summary["injections"] = injection_performance
        
        # Telemetry Performance
        telemetry_metrics = TestParallelAnalysis.performance_metrics.get("telemetry", {})
        telemetry_performance = {
            "events_collected": telemetry_metrics.get("final_new_events", 0),
            "avg_rate": telemetry_metrics.get("avg_rate", 0),
            "collection_active": telemetry_metrics.get("final_new_events", 0) > 0
        }
        performance_summary["telemetry"] = telemetry_performance
        
        # Overall Performance Score
        overall_score = (
            vm_performance["success_rate"] * 0.3 +
            readiness_performance["ready_rate"] * 0.2 +
            injection_performance["success_rate"] * 0.3 +
            (100 if telemetry_performance["collection_active"] else 0) * 0.2
        )
        
        performance_summary["overall"] = {
            "score": overall_score,
            "grade": "A" if overall_score >= 90 else "B" if overall_score >= 75 else "C" if overall_score >= 60 else "D"
        }
        
        TestParallelAnalysis.performance_metrics["summary"] = performance_summary
        
        print(f"📊 Parallel Performance Summary:")
        print(f"   VM Creation: {vm_performance['success_rate']:.1f}% success, {vm_performance['avg_creation_time']:.1f}s avg")
        print(f"   VM Readiness: {readiness_performance['ready_rate']:.1f}% ready, {readiness_performance['avg_ready_time']:.1f}s avg")
        print(f"   Injections: {injection_performance['success_rate']:.1f}% success, {injection_performance['avg_injection_time']:.1f}s avg")
        print(f"   Telemetry: {telemetry_performance['events_collected']} events, {telemetry_performance['avg_rate']:.1f}/min rate")
        print(f"   Overall Score: {overall_score:.1f}/100 (Grade: {performance_summary['overall']['grade']})")
        
        # Validate minimum performance requirements
        assert vm_performance["success_rate"] >= 50, f"VM creation success rate too low: {vm_performance['success_rate']:.1f}%"
        assert injection_performance["success_rate"] >= 50, f"Injection success rate too low: {injection_performance['success_rate']:.1f}%"
        assert telemetry_performance["collection_active"], "No telemetry collection detected"
        
        print("✅ Parallel performance validation completed")
    
    def test_06_cleanup_parallel_resources(self, parallel_config):
        """Clean up parallel test resources."""
        print("🧹 Cleaning up parallel resources...")
        
        cleanup_results = []
        
        # Clean up VMs in parallel
        def cleanup_vm(vm_info):
            vm_id = vm_info["vm_id"]
            scenario_index = vm_info["scenario_index"]
            
            try:
                response = requests.delete(f"{parallel_config['api_base_url']}/vms/{vm_id}")
                return {
                    "scenario_index": scenario_index,
                    "vm_id": vm_id,
                    "status": "cleaned" if response.status_code in [200, 204, 404] else "failed",
                    "response_code": response.status_code
                }
            except Exception as e:
                return {
                    "scenario_index": scenario_index,
                    "vm_id": vm_id,
                    "status": "error",
                    "error": str(e)
                }
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(TestParallelAnalysis.parallel_vms)) as executor:
            futures = []
            
            for vm_info in TestParallelAnalysis.parallel_vms.values():
                future = executor.submit(cleanup_vm, vm_info)
                futures.append(future)
            
            for future in concurrent.futures.as_completed(futures):
                result = future.result()
                cleanup_results.append(result)
                
                if result["status"] == "cleaned":
                    print(f"✅ VM {result['scenario_index']} cleaned: {result['vm_id']}")
                else:
                    print(f"⚠️ VM {result['scenario_index']} cleanup issue: {result.get('error', 'Unknown')}")
        
        successful_cleanups = len([r for r in cleanup_results if r["status"] == "cleaned"])
        print(f"✅ Parallel cleanup completed: {successful_cleanups}/{len(cleanup_results)} successful")
        
        # Final parallel test summary
        print(f"🎉 PARALLEL ANALYSIS TEST COMPLETED!")
        print(f"   VMs Created: {len(TestParallelAnalysis.parallel_vms)}")
        print(f"   Analyses Executed: {len(TestParallelAnalysis.parallel_analyses)}")
        print(f"   Performance Grade: {TestParallelAnalysis.performance_metrics['summary']['overall']['grade']}")
        print(f"   Overall Score: {TestParallelAnalysis.performance_metrics['summary']['overall']['score']:.1f}/100")
    
    # Helper methods
    def _check_parallel_event_categories(self, parallel_config) -> Dict[str, int]:
        """Check event categories during parallel monitoring."""
        try:
            query = {
                "aggs": {
                    "categories": {
                        "terms": {"field": "event.category.keyword", "size": 10}
                    }
                },
                "size": 0
            }
            
            response = requests.post(
                f"{parallel_config['elasticsearch_url']}/turdparty-*/_search",
                json=query,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                results = response.json()
                buckets = results.get("aggregations", {}).get("categories", {}).get("buckets", [])
                return {bucket["key"]: bucket["doc_count"] for bucket in buckets}
        except Exception as e:
            print(f"⚠️ Error checking event categories: {e}")
        
        return {}
