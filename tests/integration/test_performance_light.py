"""
Fast Performance Integration Tests - Tier 1 (Lightweight)
Micro-benchmark tests for rapid CI/CD performance validation.
Target runtime: < 20 seconds
"""

import pytest
import time
import asyncio
import json
import psutil
import threading
from unittest.mock import Mock, patch
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Any


class TestPerformanceLightIntegration:
    """Fast, micro-benchmark performance tests for CI/CD pipelines."""

    @pytest.fixture(scope="class")
    def perf_config(self):
        """Lightweight performance test configuration."""
        return {
            "api_base_url": "http://localhost:8000/api/v1",
            "concurrent_requests": 5,  # 5 vs 100
            "test_duration": 5,  # 5 seconds vs 5 minutes
            "response_timeout": 2,  # 2 seconds
            "memory_threshold_mb": 100,  # 100MB threshold
            "cpu_threshold_percent": 50,  # 50% CPU threshold
            "max_response_time_ms": 1000  # 1 second max response
        }

    def test_01_api_response_time_light(self, perf_config):
        """Test API response time with lightweight requests."""
        print("⚡ Testing API response time (light)...")
        
        response_times = []
        
        with patch('requests.get') as mock_get:
            # Mock fast API responses
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = {"status": "healthy", "timestamp": time.time()}
            mock_get.return_value.elapsed.total_seconds.return_value = 0.05  # 50ms
            
            import requests
            
            # Test multiple API calls
            for i in range(10):
                start_time = time.time()
                
                response = requests.get(f"{perf_config['api_base_url']}/health")
                assert response.status_code == 200
                
                response_time = time.time() - start_time
                response_times.append(response_time * 1000)  # Convert to ms
                
                time.sleep(0.1)  # Brief pause between requests
                
        # Validate response times
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        assert avg_response_time < perf_config["max_response_time_ms"], \
            f"Average response time {avg_response_time:.2f}ms exceeds {perf_config['max_response_time_ms']}ms"
        
        assert max_response_time < perf_config["max_response_time_ms"], \
            f"Max response time {max_response_time:.2f}ms exceeds {perf_config['max_response_time_ms']}ms"
        
        print(f"✅ API response time (light) passed: avg={avg_response_time:.2f}ms, max={max_response_time:.2f}ms")

    def test_02_concurrent_request_handling_light(self, perf_config):
        """Test concurrent request handling with lightweight load."""
        print("🔀 Testing concurrent request handling (light)...")
        
        concurrent_requests = perf_config["concurrent_requests"]
        
        def make_request(request_id):
            """Simulate a single API request."""
            start_time = time.time()
            
            # Simulate request processing
            time.sleep(0.05)  # 50ms processing time
            
            return {
                "request_id": request_id,
                "response_time": time.time() - start_time,
                "status": "success"
            }
        
        # Test concurrent requests
        start_time = time.time()
        results = []
        
        with ThreadPoolExecutor(max_workers=concurrent_requests) as executor:
            futures = [executor.submit(make_request, i) for i in range(concurrent_requests)]
            
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
                
        total_time = time.time() - start_time
        
        # Validate concurrent performance
        assert len(results) == concurrent_requests
        assert total_time < 2.0, f"Concurrent requests took {total_time:.2f}s, expected < 2s"
        
        # Check all requests succeeded
        successful_requests = [r for r in results if r["status"] == "success"]
        assert len(successful_requests) == concurrent_requests
        
        avg_response_time = sum(r["response_time"] for r in results) / len(results)
        
        print(f"✅ Concurrent request handling (light) passed: {concurrent_requests} requests in {total_time:.2f}s, avg={avg_response_time:.3f}s")

    def test_03_memory_usage_monitoring_light(self, perf_config):
        """Test memory usage monitoring with lightweight operations."""
        print("💾 Testing memory usage monitoring (light)...")
        
        # Get baseline memory usage
        process = psutil.Process()
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        memory_samples = [baseline_memory]
        
        # Simulate memory-intensive operations
        test_data = []
        for i in range(1000):  # Small dataset
            test_data.append({
                "id": i,
                "data": f"test_data_{i}" * 10,  # Small strings
                "timestamp": time.time()
            })
            
            if i % 200 == 0:  # Sample every 200 iterations
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_samples.append(current_memory)
                
        # Calculate memory usage
        peak_memory = max(memory_samples)
        memory_increase = peak_memory - baseline_memory
        
        # Validate memory usage
        assert memory_increase < perf_config["memory_threshold_mb"], \
            f"Memory increase {memory_increase:.2f}MB exceeds threshold {perf_config['memory_threshold_mb']}MB"
        
        # Cleanup test data
        del test_data
        
        final_memory = process.memory_info().rss / 1024 / 1024
        memory_cleanup = peak_memory - final_memory
        
        print(f"✅ Memory usage monitoring (light) passed: baseline={baseline_memory:.2f}MB, peak={peak_memory:.2f}MB, increase={memory_increase:.2f}MB")

    def test_04_cpu_usage_monitoring_light(self, perf_config):
        """Test CPU usage monitoring with lightweight operations."""
        print("🖥️ Testing CPU usage monitoring (light)...")
        
        cpu_samples = []
        
        # Monitor CPU during light computational work
        def cpu_intensive_task():
            """Light CPU-intensive task."""
            result = 0
            for i in range(100000):  # Small computation
                result += i * 2
            return result
        
        def monitor_cpu():
            """Monitor CPU usage."""
            for _ in range(10):  # 10 samples over 1 second
                cpu_percent = psutil.cpu_percent(interval=0.1)
                cpu_samples.append(cpu_percent)
        
        # Start CPU monitoring
        monitor_thread = threading.Thread(target=monitor_cpu)
        monitor_thread.start()
        
        # Perform CPU-intensive tasks
        start_time = time.time()
        results = []
        
        with ThreadPoolExecutor(max_workers=2) as executor:
            futures = [executor.submit(cpu_intensive_task) for _ in range(3)]
            
            for future in as_completed(futures):
                results.append(future.result())
                
        task_time = time.time() - start_time
        monitor_thread.join()
        
        # Validate CPU usage
        if cpu_samples:
            avg_cpu = sum(cpu_samples) / len(cpu_samples)
            max_cpu = max(cpu_samples)
            
            # CPU usage should be reasonable for light tasks
            assert max_cpu < perf_config["cpu_threshold_percent"], \
                f"Max CPU usage {max_cpu:.2f}% exceeds threshold {perf_config['cpu_threshold_percent']}%"
        else:
            avg_cpu = 0
            max_cpu = 0
            
        assert task_time < 2.0, f"CPU tasks took {task_time:.2f}s, expected < 2s"
        
        print(f"✅ CPU usage monitoring (light) passed: avg={avg_cpu:.2f}%, max={max_cpu:.2f}%, time={task_time:.2f}s")

    def test_05_database_query_performance_light(self, perf_config):
        """Test database query performance with mocked operations."""
        print("🗄️ Testing database query performance (light)...")
        
        query_times = []
        
        with patch('requests.post') as mock_post:
            # Mock database query responses
            mock_post.return_value.status_code = 200
            mock_post.return_value.json.return_value = {
                "results": [{"id": i, "name": f"record_{i}"} for i in range(10)],
                "count": 10,
                "query_time": 0.05  # 50ms
            }
            
            import requests
            
            # Test multiple queries
            queries = [
                {"table": "vms", "filter": {"status": "running"}},
                {"table": "files", "filter": {"size_gt": 1000}},
                {"table": "injections", "filter": {"status": "completed"}},
                {"table": "events", "filter": {"timestamp_gt": "2025-01-01"}},
                {"table": "users", "filter": {"active": True}}
            ]
            
            for query in queries:
                start_time = time.time()
                
                response = requests.post(f"{perf_config['api_base_url']}/query", json=query)
                assert response.status_code == 200
                
                query_time = time.time() - start_time
                query_times.append(query_time * 1000)  # Convert to ms
                
                result = response.json()
                assert "results" in result
                assert result["count"] >= 0
                
        # Validate query performance
        avg_query_time = sum(query_times) / len(query_times)
        max_query_time = max(query_times)
        
        assert avg_query_time < 500, f"Average query time {avg_query_time:.2f}ms exceeds 500ms"
        assert max_query_time < 1000, f"Max query time {max_query_time:.2f}ms exceeds 1000ms"
        
        print(f"✅ Database query performance (light) passed: avg={avg_query_time:.2f}ms, max={max_query_time:.2f}ms")

    def test_06_file_operation_performance_light(self, perf_config):
        """Test file operation performance with small files."""
        print("📁 Testing file operation performance (light)...")
        
        import tempfile
        import os
        
        operation_times = {}
        
        # Test file creation
        start_time = time.time()
        temp_files = []
        
        for i in range(10):  # Create 10 small files
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
                f.write(f"Test file {i}\n" * 100)  # ~1KB each
                temp_files.append(f.name)
                
        operation_times["file_creation"] = time.time() - start_time
        
        # Test file reading
        start_time = time.time()
        total_content = ""
        
        for file_path in temp_files:
            with open(file_path, 'r') as f:
                content = f.read()
                total_content += content
                
        operation_times["file_reading"] = time.time() - start_time
        
        # Test file deletion
        start_time = time.time()
        
        for file_path in temp_files:
            os.remove(file_path)
            
        operation_times["file_deletion"] = time.time() - start_time
        
        # Validate file operation performance
        for operation, duration in operation_times.items():
            assert duration < 1.0, f"{operation} took {duration:.3f}s, expected < 1s"
            
        total_files_processed = len(temp_files)
        total_content_size = len(total_content)
        
        print(f"✅ File operation performance (light) passed: {total_files_processed} files, {total_content_size} bytes")
        for operation, duration in operation_times.items():
            print(f"   {operation}: {duration:.3f}s")

    def test_07_network_latency_simulation_light(self, perf_config):
        """Test network latency simulation with mocked delays."""
        print("🌐 Testing network latency simulation (light)...")
        
        latency_samples = []
        
        # Simulate various network conditions
        network_conditions = [
            {"name": "local", "delay": 0.001},      # 1ms
            {"name": "lan", "delay": 0.005},        # 5ms
            {"name": "wan", "delay": 0.050},        # 50ms
            {"name": "slow", "delay": 0.200},       # 200ms
        ]
        
        with patch('requests.get') as mock_get:
            for condition in network_conditions:
                # Mock network delay
                def delayed_response(*args, **kwargs):
                    time.sleep(condition["delay"])
                    response = Mock()
                    response.status_code = 200
                    response.json.return_value = {"status": "ok", "latency": condition["delay"]}
                    return response
                
                mock_get.side_effect = delayed_response
                
                import requests
                
                # Measure actual latency
                start_time = time.time()
                response = requests.get(f"{perf_config['api_base_url']}/ping")
                actual_latency = time.time() - start_time
                
                assert response.status_code == 200
                latency_samples.append({
                    "condition": condition["name"],
                    "expected": condition["delay"],
                    "actual": actual_latency
                })
                
        # Validate latency measurements
        for sample in latency_samples:
            expected = sample["expected"]
            actual = sample["actual"]
            tolerance = expected * 0.5  # 50% tolerance
            
            assert abs(actual - expected) <= tolerance, \
                f"{sample['condition']} latency {actual:.3f}s differs from expected {expected:.3f}s by more than {tolerance:.3f}s"
                
        avg_latency = sum(s["actual"] for s in latency_samples) / len(latency_samples)
        
        print(f"✅ Network latency simulation (light) passed: avg={avg_latency:.3f}s")
        for sample in latency_samples:
            print(f"   {sample['condition']}: {sample['actual']:.3f}s (expected {sample['expected']:.3f}s)")

    def test_08_performance_regression_detection_light(self, perf_config):
        """Test performance regression detection with baseline comparison."""
        print("📊 Testing performance regression detection (light)...")
        
        # Simulate baseline performance metrics
        baseline_metrics = {
            "api_response_time": 0.050,  # 50ms
            "query_time": 0.030,         # 30ms
            "file_operation": 0.100,     # 100ms
            "memory_usage": 50.0,        # 50MB
            "cpu_usage": 15.0            # 15%
        }
        
        # Simulate current performance metrics
        current_metrics = {
            "api_response_time": 0.055,  # 55ms (10% increase)
            "query_time": 0.028,         # 28ms (7% decrease)
            "file_operation": 0.120,     # 120ms (20% increase)
            "memory_usage": 48.0,        # 48MB (4% decrease)
            "cpu_usage": 18.0            # 18% (20% increase)
        }
        
        regression_threshold = 0.25  # 25% threshold (more lenient for light tests)
        regressions = []
        improvements = []
        
        for metric, baseline_value in baseline_metrics.items():
            current_value = current_metrics[metric]
            change_percent = (current_value - baseline_value) / baseline_value
            
            if change_percent > regression_threshold:
                regressions.append({
                    "metric": metric,
                    "baseline": baseline_value,
                    "current": current_value,
                    "change": change_percent * 100
                })
            elif change_percent < -0.05:  # 5% improvement threshold
                improvements.append({
                    "metric": metric,
                    "baseline": baseline_value,
                    "current": current_value,
                    "change": change_percent * 100
                })
        
        # Validate no significant regressions
        assert len(regressions) == 0, f"Performance regressions detected: {regressions}"
        
        print(f"✅ Performance regression detection (light) passed: {len(improvements)} improvements, 0 regressions")
        for improvement in improvements:
            print(f"   {improvement['metric']}: {improvement['change']:.1f}% improvement")


if __name__ == "__main__":
    # Run fast performance tests
    pytest.main([__file__, "-v", "--tb=short", "--disable-warnings"])
