"""
Real API Integration Tests for TurdParty
Converted from mock-based tests to real service integration.
Uses centralized ServiceURLManager and Traefik routing.
"""
import os
import sys
import time
import uuid
from typing import Dict, Any
import logging

import pytest
import requests

# Add utils to path for ServiceURLManager
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'utils'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get API URL from centralized ServiceURLManager
try:
    from service_urls import ServiceURLManager
    url_manager = ServiceURLManager('development')
    traefik_url = url_manager.get_service_url('api')

    # Use Traefik routing for proper service integration
    if traefik_url and 'api.turdparty.localhost' in traefik_url:
        API_BASE_URL = traefik_url  # Use Traefik URL directly
        logger.info(f"Using Traefik routing: {traefik_url}")
    else:
        API_BASE_URL = "http://localhost:8000"  # Fallback only if ServiceURLManager fails
        logger.warning("ServiceURLManager not working, using fallback")
except ImportError:
    # Fallback if ServiceURLManager not available
    API_BASE_URL = "http://localhost:8000"
    logger.warning("ServiceURLManager not available, using fallback")

API_V1_BASE = f"{API_BASE_URL}/api/v1"

logger.info(f"Using API Base URL: {API_BASE_URL}")


def check_api_availability():
    """Check if the API is available for testing."""
    try:
        response = requests.get(f"{API_BASE_URL}/health/", timeout=5)
        return response.status_code == 200
    except Exception as e:
        logger.warning(f"API availability check failed: {e}")
        return False


class TestRealAPIIntegration:
    """Real API integration tests using centralized ServiceURLManager."""

    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Setup method run before each test."""
        if not check_api_availability():
            pytest.skip("API not available for integration testing")
            
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'application/json'
        })

    def test_api_health_endpoints(self):
        """Test all API health endpoints using centralized URLs."""
        logger.info("Testing API health endpoints...")

        # Test main health endpoint
        response = self.session.get(f"{API_BASE_URL}/health/")
        assert response.status_code == 200
        health_data = response.json()
        assert health_data["status"] == "healthy"
        logger.info(f"✅ Main health: {health_data['status']}")

        # Test individual service health endpoints
        health_endpoints = [
            "celery", "database", "elasticsearch", "kibana", 
            "minio", "redis", "vm-monitor"
        ]

        for endpoint in health_endpoints:
            logger.info(f"Testing health endpoint: {endpoint}")
            response = self.session.get(f"{API_V1_BASE}/health/{endpoint}")
            
            # Health endpoints should return 200 or 503 (service unavailable)
            assert response.status_code in [200, 503], f"Health endpoint {endpoint} failed: {response.status_code}"
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ Health endpoint {endpoint}: {data.get('status', 'unknown')}")
            else:
                logger.info(f"⚠️ Health endpoint {endpoint}: Service unavailable")

        logger.info("✅ API health endpoints test completed")

    def test_file_upload_workflow(self):
        """Test real file upload workflow using centralized URLs."""
        logger.info("Testing real file upload workflow...")

        # Create test file
        test_content = f"Test file content - {uuid.uuid4()}".encode()
        files = {
            'file': ('test_upload.txt', test_content, 'text/plain')
        }
        data = {
            'description': 'Real integration test file upload'
        }

        # Remove Content-Type header for multipart upload
        headers = {k: v for k, v in self.session.headers.items() if k.lower() != 'content-type'}

        # Test file upload
        response = requests.post(
            f"{API_V1_BASE}/files/upload",
            files=files,
            data=data,
            headers=headers
        )

        if response.status_code == 404:
            pytest.skip("File upload endpoint not available")

        assert response.status_code in [200, 201], f"Upload failed: {response.status_code} - {response.text}"
        upload_result = response.json()
        
        file_id = upload_result.get("file_id") or upload_result.get("id")
        assert file_id is not None, "No file ID returned from upload"
        
        logger.info(f"✅ File uploaded successfully: {file_id}")

        # Test file metadata retrieval
        response = self.session.get(f"{API_V1_BASE}/files/{file_id}")
        if response.status_code == 200:
            metadata = response.json()
            logger.info(f"✅ File metadata retrieved: {metadata.get('filename', 'unknown')}")
        else:
            logger.info(f"⚠️ File metadata not available: {response.status_code}")

        logger.info("✅ File upload workflow test completed")

    def test_vm_management_workflow(self):
        """Test real VM management workflow using centralized URLs."""
        logger.info("Testing real VM management workflow...")

        # Create test VM
        vm_data = {
            "name": f"test-integration-vm-{int(time.time())}",
            "template": "ubuntu/focal64",
            "vm_type": "docker",
            "memory_mb": 256,
            "cpus": 1,
            "description": "Real integration test VM"
        }

        response = self.session.post(f"{API_V1_BASE}/vms/", json=vm_data)
        
        if response.status_code == 404:
            pytest.skip("VM creation endpoint not available")
            
        if response.status_code != 201:
            pytest.skip(f"VM creation failed: {response.status_code} - {response.text}")

        vm_result = response.json()
        vm_id = vm_result.get("vm_id") or vm_result.get("id")
        assert vm_id is not None, "No VM ID returned from creation"
        
        logger.info(f"✅ VM created successfully: {vm_id}")

        try:
            # Test VM status
            response = self.session.get(f"{API_V1_BASE}/vms/{vm_id}")
            if response.status_code == 200:
                vm_status = response.json()
                logger.info(f"✅ VM status retrieved: {vm_status.get('status', 'unknown')}")

            # Test VM list
            response = self.session.get(f"{API_V1_BASE}/vms/")
            if response.status_code == 200:
                vms_list = response.json()
                logger.info(f"✅ VM list retrieved: {len(vms_list)} VMs")

        finally:
            # Cleanup - delete the test VM
            try:
                delete_response = self.session.delete(f"{API_V1_BASE}/vms/{vm_id}")
                if delete_response.status_code == 200:
                    logger.info("✅ Test VM cleanup successful")
                else:
                    logger.warning(f"⚠️ VM cleanup failed: {delete_response.status_code}")
            except Exception as e:
                logger.warning(f"⚠️ VM cleanup error: {e}")

        logger.info("✅ VM management workflow test completed")

    def test_service_url_manager_integration(self):
        """Test ServiceURLManager integration across different environments."""
        logger.info("Testing ServiceURLManager integration...")

        try:
            from service_urls import ServiceURLManager
            
            # Test different environments
            environments = ['development', 'local', 'local-direct']
            
            for env in environments:
                try:
                    manager = ServiceURLManager(env)
                    api_url = manager.get_service_url('api')
                    logger.info(f"✅ Environment {env}: API URL = {api_url}")
                    
                    # Test API endpoint generation
                    upload_endpoint = manager.get_api_endpoint('files', 'upload')
                    logger.info(f"✅ Environment {env}: Upload endpoint = {upload_endpoint}")
                    
                except ValueError as e:
                    logger.info(f"⚠️ Environment {env}: {e}")
                    
            logger.info("✅ ServiceURLManager integration test completed")
            
        except ImportError:
            pytest.skip("ServiceURLManager not available")

    def test_centralized_configuration_validation(self):
        """Test that centralized configuration is working properly."""
        logger.info("Testing centralized configuration validation...")

        try:
            from service_urls import ServiceURLManager
            
            # Test configuration loading
            manager = ServiceURLManager('development')
            
            # Test service URLs
            services = ['api', 'frontend', 'elasticsearch', 'kibana']
            for service in services:
                try:
                    url = manager.get_service_url(service)
                    logger.info(f"✅ Service {service}: {url}")
                except ValueError:
                    logger.info(f"⚠️ Service {service}: Not available in development environment")
            
            # Test API endpoints
            endpoints = [
                ('files', 'upload'),
                ('files', 'list'),
                ('vms', 'create'),
                ('health', 'system')
            ]
            
            for category, endpoint in endpoints:
                try:
                    url = manager.get_api_endpoint(category, endpoint)
                    logger.info(f"✅ API endpoint {category}/{endpoint}: {url}")
                except (ValueError, KeyError):
                    logger.info(f"⚠️ API endpoint {category}/{endpoint}: Not configured")
            
            logger.info("✅ Centralized configuration validation completed")
            
        except ImportError:
            pytest.skip("ServiceURLManager not available")

    def test_admin_endpoints_integration(self):
        """Test admin and management endpoints."""
        logger.info("Testing admin endpoints integration...")

        # Test admin health
        response = self.session.get(f"{API_V1_BASE}/admin/admin/health")
        assert response.status_code in [200, 403, 404], f"Admin health failed: {response.status_code}"

        if response.status_code == 200:
            admin_data = response.json()
            logger.info(f"✅ Admin health: {admin_data.get('status', 'unknown')}")
        else:
            logger.info(f"⚠️ Admin health: {response.status_code} (may require auth or not implemented)")

        # Test docs rebuild (should be safe)
        response = self.session.post(f"{API_V1_BASE}/admin/admin/docs/rebuild")
        assert response.status_code in [200, 202, 403, 404, 422], f"Docs rebuild failed: {response.status_code}"
        logger.info(f"✅ Docs rebuild endpoint: {response.status_code}")

        logger.info("✅ Admin endpoints integration test completed")

    def test_ecs_logging_integration(self):
        """Test ECS logging and event management endpoints."""
        logger.info("Testing ECS logging integration...")

        # Test ECS health
        response = self.session.get(f"{API_V1_BASE}/ecs/ecs/health")
        assert response.status_code in [200, 503], f"ECS health failed: {response.status_code}"

        if response.status_code == 200:
            ecs_data = response.json()
            logger.info(f"✅ ECS health: {ecs_data.get('status', 'unknown')}")
        else:
            logger.info("⚠️ ECS service unavailable")

        # Test ECS indices
        response = self.session.get(f"{API_V1_BASE}/ecs/ecs/indices")
        assert response.status_code in [200, 503], f"ECS indices failed: {response.status_code}"

        if response.status_code == 200:
            indices_data = response.json()
            logger.info(f"✅ ECS indices: {len(indices_data) if isinstance(indices_data, list) else 'data available'}")

        # Test VM events (with fake VM ID)
        fake_vm_id = "test-vm-12345"
        response = self.session.get(f"{API_V1_BASE}/ecs/ecs/events/{fake_vm_id}")
        assert response.status_code in [200, 404, 422, 503], f"VM events failed: {response.status_code}"
        logger.info(f"✅ VM events endpoint: {response.status_code}")

        logger.info("✅ ECS logging integration test completed")

    def test_advanced_file_operations(self):
        """Test advanced file operations and workflows."""
        logger.info("Testing advanced file operations...")

        # First upload a test file
        test_content = f"Advanced test file - {uuid.uuid4()}".encode()
        files = {'file': ('advanced_test.txt', test_content, 'text/plain')}
        data = {'description': 'Advanced file operations test'}

        headers = {k: v for k, v in self.session.headers.items() if k.lower() != 'content-type'}
        response = requests.post(f"{API_V1_BASE}/files/upload", files=files, data=data, headers=headers)

        if response.status_code == 404:
            pytest.skip("File upload endpoint not available")

        if response.status_code not in [200, 201]:
            pytest.skip(f"File upload failed: {response.status_code}")

        upload_result = response.json()
        file_id = upload_result.get("file_id") or upload_result.get("id")

        try:
            # Test download URL generation
            response = self.session.get(f"{API_V1_BASE}/files/{file_id}/download-url")
            assert response.status_code in [200, 404], f"Download URL failed: {response.status_code}"

            if response.status_code == 200:
                download_data = response.json()
                logger.info(f"✅ Download URL generated: {download_data.get('download_url', 'URL available')}")
            else:
                logger.info("⚠️ Download URL endpoint not implemented")

            # Test file metadata
            response = self.session.get(f"{API_V1_BASE}/files/{file_id}/metadata")
            assert response.status_code in [200, 404], f"File metadata failed: {response.status_code}"

            if response.status_code == 200:
                metadata = response.json()
                logger.info(f"✅ File metadata: {metadata.get('filename', 'metadata available')}")
            else:
                # Try alternative metadata endpoint
                response = self.session.get(f"{API_V1_BASE}/files/{file_id}")
                if response.status_code == 200:
                    metadata = response.json()
                    logger.info(f"✅ File info: {metadata.get('filename', 'info available')}")

            # Test file listing
            response = self.session.get(f"{API_V1_BASE}/files/")
            assert response.status_code in [200, 404], f"File listing failed: {response.status_code}"

            if response.status_code == 200:
                files_list = response.json()
                logger.info(f"✅ File listing: {len(files_list) if isinstance(files_list, list) else 'files available'}")

        finally:
            # Cleanup - try to delete the file
            try:
                delete_response = self.session.delete(f"{API_V1_BASE}/files/{file_id}")
                if delete_response.status_code == 200:
                    logger.info("✅ File cleanup successful")
            except Exception:
                pass  # Cleanup is best effort

        logger.info("✅ Advanced file operations test completed")

    def test_vm_actions_workflow(self):
        """Test VM action endpoints (start, stop, pause, resume)."""
        logger.info("Testing VM actions workflow...")

        # Create test VM first
        vm_data = {
            "name": f"test-actions-vm-{int(time.time())}",
            "template": "ubuntu/focal64",
            "vm_type": "docker",
            "memory_mb": 256,
            "cpus": 1,
            "description": "Test VM for actions"
        }

        response = self.session.post(f"{API_V1_BASE}/vms/", json=vm_data)

        if response.status_code == 404:
            pytest.skip("VM creation endpoint not available")

        if response.status_code != 201:
            pytest.skip(f"VM creation failed: {response.status_code}")

        vm_result = response.json()
        vm_id = vm_result.get("vm_id") or vm_result.get("id")

        try:
            # Test VM actions
            actions = ["start", "stop", "pause", "resume"]
            for action in actions:
                action_data = {"action": action}
                response = self.session.post(f"{API_V1_BASE}/vms/{vm_id}/action", json=action_data)

                # Actions may fail depending on VM state, so accept various codes
                assert response.status_code in [200, 400, 404, 409, 422, 500], f"VM action {action} failed: {response.status_code}"
                logger.info(f"✅ VM action '{action}': {response.status_code}")

            # Test VM logs
            response = self.session.get(f"{API_V1_BASE}/vms/{vm_id}/logs")
            assert response.status_code in [200, 404], f"VM logs failed: {response.status_code}"

            if response.status_code == 200:
                logs_data = response.json()
                logger.info(f"✅ VM logs retrieved: {len(logs_data) if isinstance(logs_data, list) else 'logs available'}")
            else:
                logger.info("⚠️ VM logs endpoint not implemented")

        finally:
            # Cleanup
            try:
                self.session.delete(f"{API_V1_BASE}/vms/{vm_id}")
                logger.info("✅ VM actions test cleanup successful")
            except Exception:
                pass

        logger.info("✅ VM actions workflow test completed")

    def test_file_injection_workflow(self):
        """Test file injection into VMs workflow."""
        logger.info("Testing file injection workflow...")

        # Upload a test file first
        test_content = f"Injection test file - {uuid.uuid4()}".encode()
        files = {'file': ('injection_test.txt', test_content, 'text/plain')}
        data = {'description': 'File for injection testing'}

        headers = {k: v for k, v in self.session.headers.items() if k.lower() != 'content-type'}
        upload_response = requests.post(f"{API_V1_BASE}/files/upload", files=files, data=data, headers=headers)

        if upload_response.status_code not in [200, 201]:
            pytest.skip("File upload failed for injection test")

        file_id = upload_response.json().get("file_id") or upload_response.json().get("id")

        # Create test VM
        vm_data = {
            "name": f"test-injection-vm-{int(time.time())}",
            "template": "ubuntu/focal64",
            "vm_type": "docker",
            "memory_mb": 256,
            "cpus": 1,
            "description": "Test VM for file injection"
        }

        vm_response = self.session.post(f"{API_V1_BASE}/vms/", json=vm_data)

        if vm_response.status_code != 201:
            pytest.skip("VM creation failed for injection test")

        vm_id = vm_response.json().get("vm_id") or vm_response.json().get("id")

        try:
            # Test file injection
            injection_data = {
                "file_id": file_id,
                "target_path": "/tmp/injected_file.txt",
                "permissions": "0644"
            }

            response = self.session.post(f"{API_V1_BASE}/vms/{vm_id}/inject", json=injection_data)
            assert response.status_code in [200, 201, 400, 404, 422], f"File injection failed: {response.status_code}"

            if response.status_code in [200, 201]:
                injection_result = response.json()
                logger.info(f"✅ File injection initiated: {injection_result.get('injection_id', 'success')}")
            else:
                logger.info("⚠️ File injection endpoint not implemented")

        finally:
            # Cleanup
            try:
                self.session.delete(f"{API_V1_BASE}/vms/{vm_id}")
                self.session.delete(f"{API_V1_BASE}/files/{file_id}")
                logger.info("✅ File injection test cleanup successful")
            except Exception:
                pass

        logger.info("✅ File injection workflow test completed")
