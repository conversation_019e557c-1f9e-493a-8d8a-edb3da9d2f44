"""
Real Vagrant VM Integration Tests - No Mocks
Tests actual Vagrant VMs including Windows and Linux
"""

import pytest
import asyncio
import subprocess
import time
import uuid
import os
import tempfile
import shutil
from typing import Dict, List, Optional
from pathlib import Path

import httpx
import yaml

# Test configuration
API_BASE_URL = "http://localhost:8000"
TEST_TIMEOUT = 600  # 10 minutes for VM operations (VMs take longer)
VAGRANT_TEST_DIR = "/tmp/turdparty_vagrant_tests"
CREATED_VMS = []
CREATED_VAGRANT_VMS = []


@pytest.fixture(scope="session", autouse=True)
def setup_vagrant_environment():
    """Setup Vagrant environment for testing."""
    # Check if Vagrant is available
    try:
        result = subprocess.run(["vagrant", "--version"], capture_output=True, text=True)
        if result.returncode != 0:
            pytest.skip("Vagrant not available")
        print(f"✅ Vagrant available: {result.stdout.strip()}")
    except FileNotFoundError:
        pytest.skip("Vagrant not installed")
    
    # Check if VirtualBox is available
    try:
        result = subprocess.run(["VBoxManage", "--version"], capture_output=True, text=True)
        if result.returncode != 0:
            pytest.skip("VirtualBox not available")
        print(f"✅ VirtualBox available: {result.stdout.strip()}")
    except FileNotFoundError:
        pytest.skip("VirtualBox not installed")
    
    # Create test directory
    os.makedirs(VAGRANT_TEST_DIR, exist_ok=True)
    
    yield
    
    # Cleanup
    cleanup_vagrant_vms()
    if os.path.exists(VAGRANT_TEST_DIR):
        shutil.rmtree(VAGRANT_TEST_DIR, ignore_errors=True)


def cleanup_vagrant_vms():
    """Clean up all created Vagrant VMs."""
    global CREATED_VMS, CREATED_VAGRANT_VMS
    
    # Cleanup VMs via API
    for vm_id in CREATED_VMS:
        try:
            response = httpx.delete(f"{API_BASE_URL}/api/v1/vms/{vm_id}?force=true")
            if response.status_code in [200, 404]:
                print(f"🧹 Cleaned up VM: {vm_id}")
        except Exception as e:
            print(f"⚠️ Failed to cleanup VM {vm_id}: {e}")
    
    # Cleanup Vagrant VMs directly
    for vm_dir in CREATED_VAGRANT_VMS:
        try:
            if os.path.exists(vm_dir):
                subprocess.run(
                    ["vagrant", "destroy", "-f"],
                    cwd=vm_dir,
                    capture_output=True,
                    timeout=120
                )
                print(f"🧹 Destroyed Vagrant VM in {vm_dir}")
        except Exception as e:
            print(f"⚠️ Failed to destroy Vagrant VM in {vm_dir}: {e}")
    
    CREATED_VMS.clear()
    CREATED_VAGRANT_VMS.clear()


def create_vagrantfile(vm_dir: str, template: str, memory_mb: int = 1024, cpus: int = 1) -> str:
    """Create a Vagrantfile for testing."""
    vagrantfile_content = f'''
Vagrant.configure("2") do |config|
  config.vm.box = "{template}"
  
  config.vm.provider "virtualbox" do |vb|
    vb.memory = {memory_mb}
    vb.cpus = {cpus}
    vb.gui = false
  end
  
  # Configure network
  config.vm.network "private_network", type: "dhcp"
  
  # Provision script
  config.vm.provision "shell", inline: <<-SHELL
    echo "TurdParty VM provisioned at $(date)" > /tmp/turdparty_provision.txt
    echo "Template: {template}" >> /tmp/turdparty_provision.txt
    echo "Memory: {memory_mb}MB" >> /tmp/turdparty_provision.txt
    echo "CPUs: {cpus}" >> /tmp/turdparty_provision.txt
  SHELL
end
'''
    
    vagrantfile_path = os.path.join(vm_dir, "Vagrantfile")
    with open(vagrantfile_path, "w") as f:
        f.write(vagrantfile_content)
    
    return vagrantfile_path


class TestRealVagrantLinuxVMs:
    """Test real Vagrant Linux VMs."""
    
    @pytest.mark.slow
    def test_vagrant_ubuntu_vm_lifecycle(self):
        """Test complete Vagrant Ubuntu VM lifecycle."""
        print("\n🐧 Testing Vagrant Ubuntu VM lifecycle...")
        
        vm_name = f"test-vagrant-ubuntu-{int(time.time())}"
        vm_dir = os.path.join(VAGRANT_TEST_DIR, vm_name)
        os.makedirs(vm_dir, exist_ok=True)
        CREATED_VAGRANT_VMS.append(vm_dir)
        
        # Create Vagrantfile
        template = "ubuntu/focal64"
        create_vagrantfile(vm_dir, template, memory_mb=1024, cpus=1)
        
        try:
            # Test VM creation via API
            vm_data = {
                "name": vm_name,
                "template": template,
                "vm_type": "vagrant",
                "memory_mb": 1024,
                "cpus": 1,
                "domain": "TurdParty",
                "description": "Real Vagrant Ubuntu VM test"
            }
            
            with httpx.Client(timeout=60.0) as client:
                # Create VM
                response = client.post(f"{API_BASE_URL}/api/v1/vms/", json=vm_data)
                assert response.status_code == 201, f"VM creation failed: {response.text}"
                
                vm_info = response.json()
                vm_id = vm_info["vm_id"]
                CREATED_VMS.append(vm_id)
                
                print(f"✅ Vagrant VM created via API: {vm_id}")
                
                # Test direct Vagrant operations
                self._test_direct_vagrant_operations(vm_dir, template)
                
                # Test VM status monitoring
                self._monitor_vm_status(client, vm_id, timeout=300)
        
        except Exception as e:
            print(f"❌ Vagrant Ubuntu VM test failed: {e}")
            raise
    
    @pytest.mark.slow
    def test_vagrant_centos_vm_creation(self):
        """Test Vagrant CentOS VM creation."""
        print("\n🔴 Testing Vagrant CentOS VM...")
        
        vm_name = f"test-vagrant-centos-{int(time.time())}"
        vm_dir = os.path.join(VAGRANT_TEST_DIR, vm_name)
        os.makedirs(vm_dir, exist_ok=True)
        CREATED_VAGRANT_VMS.append(vm_dir)
        
        template = "centos/7"
        create_vagrantfile(vm_dir, template, memory_mb=1024, cpus=1)
        
        try:
            # Test direct Vagrant creation
            print(f"📦 Creating CentOS VM with template: {template}")
            
            # Vagrant up
            result = subprocess.run(
                ["vagrant", "up"],
                cwd=vm_dir,
                capture_output=True,
                text=True,
                timeout=600  # 10 minutes
            )
            
            if result.returncode == 0:
                print("✅ CentOS VM created successfully")
                
                # Test VM is running
                status_result = subprocess.run(
                    ["vagrant", "status"],
                    cwd=vm_dir,
                    capture_output=True,
                    text=True
                )
                
                if "running" in status_result.stdout:
                    print("✅ CentOS VM is running")
                    
                    # Test SSH connectivity
                    ssh_result = subprocess.run(
                        ["vagrant", "ssh", "-c", "cat /etc/centos-release"],
                        cwd=vm_dir,
                        capture_output=True,
                        text=True,
                        timeout=60
                    )
                    
                    if ssh_result.returncode == 0:
                        print(f"✅ CentOS version: {ssh_result.stdout.strip()}")
                    else:
                        print(f"⚠️ SSH test failed: {ssh_result.stderr}")
            else:
                print(f"❌ CentOS VM creation failed: {result.stderr}")
        
        except subprocess.TimeoutExpired:
            print("⏰ CentOS VM creation timed out")
        except Exception as e:
            print(f"❌ CentOS VM test failed: {e}")
    
    def _test_direct_vagrant_operations(self, vm_dir: str, template: str):
        """Test direct Vagrant operations."""
        print(f"🔧 Testing direct Vagrant operations in {vm_dir}")
        
        try:
            # Vagrant up
            print("📦 Running 'vagrant up'...")
            up_result = subprocess.run(
                ["vagrant", "up"],
                cwd=vm_dir,
                capture_output=True,
                text=True,
                timeout=600  # 10 minutes
            )
            
            if up_result.returncode == 0:
                print("✅ Vagrant up successful")
                
                # Check status
                status_result = subprocess.run(
                    ["vagrant", "status"],
                    cwd=vm_dir,
                    capture_output=True,
                    text=True
                )
                print(f"📊 Vagrant status: {status_result.stdout}")
                
                # Test SSH
                ssh_result = subprocess.run(
                    ["vagrant", "ssh", "-c", "cat /tmp/turdparty_provision.txt"],
                    cwd=vm_dir,
                    capture_output=True,
                    text=True,
                    timeout=60
                )
                
                if ssh_result.returncode == 0:
                    print("✅ SSH connectivity successful")
                    print(f"Provision info: {ssh_result.stdout.strip()}")
                else:
                    print(f"⚠️ SSH test failed: {ssh_result.stderr}")
                
                # Test halt
                print("🛑 Testing 'vagrant halt'...")
                halt_result = subprocess.run(
                    ["vagrant", "halt"],
                    cwd=vm_dir,
                    capture_output=True,
                    text=True,
                    timeout=120
                )
                
                if halt_result.returncode == 0:
                    print("✅ Vagrant halt successful")
                else:
                    print(f"⚠️ Vagrant halt failed: {halt_result.stderr}")
            
            else:
                print(f"❌ Vagrant up failed: {up_result.stderr}")
        
        except subprocess.TimeoutExpired:
            print("⏰ Vagrant operation timed out")
        except Exception as e:
            print(f"❌ Vagrant operation failed: {e}")
    
    def _monitor_vm_status(self, client: httpx.Client, vm_id: str, timeout: int = 300):
        """Monitor VM status through API."""
        print(f"👁️ Monitoring VM status: {vm_id}")
        
        start_time = time.time()
        last_status = None
        
        while time.time() - start_time < timeout:
            try:
                response = client.get(f"{API_BASE_URL}/api/v1/vms/{vm_id}")
                if response.status_code == 200:
                    vm_info = response.json()
                    current_status = vm_info["status"]
                    
                    if current_status != last_status:
                        print(f"📊 VM Status changed: {last_status} -> {current_status}")
                        last_status = current_status
                    
                    if current_status in ["running", "failed", "terminated"]:
                        print(f"🎯 Final VM status: {current_status}")
                        break
                
                time.sleep(10)
            
            except Exception as e:
                print(f"⚠️ Status check failed: {e}")
                time.sleep(10)


class TestRealVagrantWindowsVMs:
    """Test real Vagrant Windows VMs (if available)."""
    
    @pytest.mark.slow
    @pytest.mark.windows
    def test_vagrant_windows_vm_creation(self):
        """Test Vagrant Windows VM creation (requires Windows box)."""
        print("\n🪟 Testing Vagrant Windows VM...")
        
        # Note: This test requires a Windows Vagrant box to be available
        # Common Windows boxes: "gusztavvargadr/windows-10" or "Microsoft/EdgeOnWindows10"
        
        vm_name = f"test-vagrant-windows-{int(time.time())}"
        vm_dir = os.path.join(VAGRANT_TEST_DIR, vm_name)
        os.makedirs(vm_dir, exist_ok=True)
        CREATED_VAGRANT_VMS.append(vm_dir)
        
        # Use a lightweight Windows box if available
        windows_template = "gusztavvargadr/windows-10"
        
        # Create Windows-specific Vagrantfile
        vagrantfile_content = f'''
Vagrant.configure("2") do |config|
  config.vm.box = "{windows_template}"
  
  config.vm.provider "virtualbox" do |vb|
    vb.memory = 2048  # Windows needs more memory
    vb.cpus = 2
    vb.gui = false
  end
  
  # Configure network
  config.vm.network "private_network", type: "dhcp"
  
  # Windows-specific configuration
  config.vm.communicator = "winrm"
  config.winrm.username = "vagrant"
  config.winrm.password = "vagrant"
  
  # Provision script for Windows
  config.vm.provision "shell", inline: <<-SHELL
    echo "TurdParty Windows VM provisioned at $(Get-Date)" > C:\\temp\\turdparty_provision.txt
    echo "Template: {windows_template}" >> C:\\temp\\turdparty_provision.txt
    echo "Memory: 2048MB" >> C:\\temp\\turdparty_provision.txt
    echo "CPUs: 2" >> C:\\temp\\turdparty_provision.txt
  SHELL
end
'''
        
        vagrantfile_path = os.path.join(vm_dir, "Vagrantfile")
        with open(vagrantfile_path, "w") as f:
            f.write(vagrantfile_content)
        
        try:
            # Check if Windows box is available
            box_list_result = subprocess.run(
                ["vagrant", "box", "list"],
                capture_output=True,
                text=True
            )
            
            if windows_template not in box_list_result.stdout:
                print(f"⚠️ Windows box '{windows_template}' not available, skipping test")
                pytest.skip(f"Windows box '{windows_template}' not available")
            
            # Test Windows VM creation
            print(f"🪟 Creating Windows VM with template: {windows_template}")
            
            # This will take a long time for Windows VMs
            result = subprocess.run(
                ["vagrant", "up"],
                cwd=vm_dir,
                capture_output=True,
                text=True,
                timeout=1800  # 30 minutes for Windows
            )
            
            if result.returncode == 0:
                print("✅ Windows VM created successfully")
                
                # Test WinRM connectivity
                winrm_result = subprocess.run(
                    ["vagrant", "winrm", "-c", "Get-ComputerInfo | Select-Object WindowsProductName"],
                    cwd=vm_dir,
                    capture_output=True,
                    text=True,
                    timeout=120
                )
                
                if winrm_result.returncode == 0:
                    print(f"✅ Windows version: {winrm_result.stdout.strip()}")
                else:
                    print(f"⚠️ WinRM test failed: {winrm_result.stderr}")
            else:
                print(f"❌ Windows VM creation failed: {result.stderr}")
        
        except subprocess.TimeoutExpired:
            print("⏰ Windows VM creation timed out (this is common)")
        except Exception as e:
            print(f"❌ Windows VM test failed: {e}")


class TestRealVMResourceManagement:
    """Test real VM resource management."""
    
    def test_vm_memory_allocation(self):
        """Test VM memory allocation in real VMs."""
        print("\n💾 Testing VM memory allocation...")
        
        memory_configs = [512, 1024, 2048]  # MB
        
        for memory_mb in memory_configs:
            vm_name = f"test-memory-{memory_mb}-{int(time.time())}"
            vm_dir = os.path.join(VAGRANT_TEST_DIR, vm_name)
            os.makedirs(vm_dir, exist_ok=True)
            CREATED_VAGRANT_VMS.append(vm_dir)
            
            template = "ubuntu/focal64"
            create_vagrantfile(vm_dir, template, memory_mb=memory_mb, cpus=1)
            
            try:
                # Test memory allocation
                print(f"🧠 Testing {memory_mb}MB memory allocation...")
                
                result = subprocess.run(
                    ["vagrant", "up"],
                    cwd=vm_dir,
                    capture_output=True,
                    text=True,
                    timeout=300
                )
                
                if result.returncode == 0:
                    # Check actual memory in VM
                    memory_check = subprocess.run(
                        ["vagrant", "ssh", "-c", "free -m | grep Mem | awk '{print $2}'"],
                        cwd=vm_dir,
                        capture_output=True,
                        text=True,
                        timeout=60
                    )
                    
                    if memory_check.returncode == 0:
                        actual_memory = int(memory_check.stdout.strip())
                        print(f"✅ Allocated: {memory_mb}MB, Actual: {actual_memory}MB")
                        
                        # Allow some tolerance for system overhead
                        assert actual_memory >= memory_mb * 0.9, f"Memory allocation failed: {actual_memory} < {memory_mb * 0.9}"
                    
                    # Cleanup immediately to save resources
                    subprocess.run(["vagrant", "destroy", "-f"], cwd=vm_dir, timeout=60)
                
            except Exception as e:
                print(f"⚠️ Memory test failed for {memory_mb}MB: {e}")


if __name__ == "__main__":
    # Run tests with verbose output
    pytest.main([__file__, "-v", "-s", "--tb=short", "-m", "not windows"])
