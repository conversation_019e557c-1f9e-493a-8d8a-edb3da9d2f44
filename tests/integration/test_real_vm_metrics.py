"""
💩🎉TurdParty🎉💩 Real VM Metrics Integration Tests

This test suite ensures that ALL VM metrics functionality uses real implementations
with NO mocks, simulations, or fake data generation.

Tests cover:
- Real Docker container metrics collection
- Real Vagrant VM metrics collection  
- Real process monitoring
- Real network statistics
- Real resource usage tracking
- Error handling with real failure scenarios
"""

import pytest
import asyncio
import time
import logging
import subprocess
from typing import Dict, Any, List
from unittest.mock import patch

# Import the real services (no mocks allowed!)
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../services/api/src'))
from services.vm_metrics_service import vm_metrics_service

logger = logging.getLogger(__name__)


class TestRealVMMetrics:
    """Test suite for real VM metrics - NO MOCKS ALLOWED"""
    
    @pytest.fixture(autouse=True)
    async def setup_real_service(self):
        """Initialize the real VM metrics service"""
        await vm_metrics_service.initialize()
        yield
        # Cleanup if needed
    
    def test_no_mock_imports_in_service(self):
        """Ensure VM metrics service doesn't import any mock libraries"""
        import services.vm_metrics_service as vm_module
        
        # Check source code for mock imports
        import inspect
        source = inspect.getsource(vm_module)
        
        forbidden_imports = [
            'unittest.mock',
            'mock',
            'pytest.mock',
            'MagicMock',
            'Mock(',
            'AsyncMock',
            'patch('
        ]
        
        for forbidden in forbidden_imports:
            assert forbidden not in source, f"Found forbidden mock import/usage: {forbidden}"
        
        logger.info("✅ VM metrics service contains no mock imports")
    
    @pytest.mark.asyncio
    async def test_real_docker_metrics_collection(self):
        """Test real Docker container metrics collection"""
        # Use a real running container (API container)
        container_id = "turdpartycollab_api"
        
        metrics = await vm_metrics_service.get_vm_metrics(container_id, "docker")
        
        # Verify real data structure
        assert isinstance(metrics, dict)
        assert metrics["vm_id"] == container_id
        assert metrics["vm_type"] == "docker"
        assert "timestamp" in metrics
        assert "status" in metrics
        
        # Verify real metrics (not mock values)
        assert isinstance(metrics["cpu_percent"], (int, float))
        assert isinstance(metrics["memory_percent"], (int, float))
        assert isinstance(metrics["memory_used_mb"], (int, float))
        assert isinstance(metrics["network_rx_bytes"], int)
        assert isinstance(metrics["network_tx_bytes"], int)
        assert isinstance(metrics["uptime_seconds"], int)
        
        # Verify process list is real
        assert isinstance(metrics["top_processes"], list)
        if metrics["top_processes"]:
            process = metrics["top_processes"][0]
            assert "pid" in process
            assert "name" in process
            assert isinstance(process["pid"], int)
            assert process["pid"] > 0  # Real PIDs are positive
        
        # Verify timestamp is recent (within last minute)
        current_time = int(time.time() * 1000)
        assert abs(current_time - metrics["timestamp"]) < 60000
        
        logger.info(f"✅ Real Docker metrics collected: CPU={metrics['cpu_percent']}%, Memory={metrics['memory_used_mb']}MB")
    
    @pytest.mark.asyncio
    async def test_real_docker_multiple_containers(self):
        """Test metrics collection from multiple real containers"""
        # Test with multiple real containers
        containers = ["turdpartycollab_api", "turdpartycollab_elasticsearch", "turdpartycollab_redis"]
        
        results = []
        for container_id in containers:
            try:
                metrics = await vm_metrics_service.get_vm_metrics(container_id, "docker")
                results.append((container_id, metrics))
                
                # Verify each container has unique metrics
                assert metrics["vm_id"] == container_id
                assert metrics["vm_type"] == "docker"
                
                logger.info(f"✅ {container_id}: CPU={metrics['cpu_percent']}%, Memory={metrics['memory_used_mb']}MB")
                
            except Exception as e:
                logger.warning(f"Container {container_id} not available: {e}")
        
        # Ensure we got metrics from at least one container
        assert len(results) > 0, "No real containers available for testing"
        
        # Verify metrics are different between containers (not mock data)
        if len(results) > 1:
            metrics1 = results[0][1]
            metrics2 = results[1][1]
            
            # At least one metric should be different (proving they're real)
            different_metrics = (
                metrics1["memory_used_mb"] != metrics2["memory_used_mb"] or
                metrics1["cpu_percent"] != metrics2["cpu_percent"] or
                metrics1["uptime_seconds"] != metrics2["uptime_seconds"]
            )
            assert different_metrics, "Metrics are identical - possible mock data!"
    
    @pytest.mark.asyncio
    async def test_real_docker_cli_integration(self):
        """Test that Docker CLI commands work correctly"""
        container_id = "turdpartycollab_api"
        
        # Test Docker CLI directly
        result = subprocess.run(
            ["docker", "stats", "--no-stream", "--format", "json", container_id],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        assert result.returncode == 0, f"Docker CLI failed: {result.stderr}"
        
        # Parse and verify CLI output
        import json
        stats_data = json.loads(result.stdout.strip())
        
        assert "CPUPerc" in stats_data
        assert "MemUsage" in stats_data
        assert "NetIO" in stats_data
        
        # Now test our service uses the same data
        metrics = await vm_metrics_service.get_vm_metrics(container_id, "docker")
        
        # Verify our service gets real data (within reasonable range of CLI)
        cli_cpu = float(stats_data["CPUPerc"].rstrip('%'))
        service_cpu = metrics["cpu_percent"]
        
        # Allow some variance due to timing differences
        assert abs(cli_cpu - service_cpu) < 10.0, f"CPU metrics don't match CLI: {cli_cpu} vs {service_cpu}"
        
        logger.info(f"✅ Docker CLI integration verified: CLI CPU={cli_cpu}%, Service CPU={service_cpu}%")
    
    @pytest.mark.asyncio
    async def test_real_error_handling(self):
        """Test real error handling with non-existent containers"""
        fake_container_id = "non_existent_container_12345"
        
        metrics = await vm_metrics_service.get_vm_metrics(fake_container_id, "docker")
        
        # Should return error metrics, not crash
        assert metrics["status"] == "error"
        assert "error" in metrics
        assert metrics["vm_id"] == "unknown"  # Error state
        assert metrics["cpu_percent"] == 0.0
        assert metrics["memory_used_mb"] == 0.0
        
        logger.info(f"✅ Real error handling verified: {metrics['error']}")
    
    @pytest.mark.asyncio
    async def test_real_metrics_streaming(self):
        """Test real-time metrics streaming"""
        container_id = "turdpartycollab_api"
        
        # Collect metrics over time to verify they change (proving they're real)
        metrics_samples = []
        
        for i in range(3):
            metrics = await vm_metrics_service.get_vm_metrics(container_id, "docker")
            metrics_samples.append({
                "timestamp": metrics["timestamp"],
                "cpu_percent": metrics["cpu_percent"],
                "memory_used_mb": metrics["memory_used_mb"]
            })
            
            if i < 2:  # Don't sleep after last iteration
                await asyncio.sleep(2)  # Wait 2 seconds between samples
        
        # Verify timestamps are different (proving real-time collection)
        timestamps = [sample["timestamp"] for sample in metrics_samples]
        assert len(set(timestamps)) == 3, "Timestamps should be unique for real-time data"
        
        # Verify timestamps are in chronological order
        assert timestamps[0] < timestamps[1] < timestamps[2], "Timestamps should increase"
        
        logger.info(f"✅ Real-time streaming verified: {len(metrics_samples)} samples collected")
        for i, sample in enumerate(metrics_samples):
            logger.info(f"   Sample {i+1}: CPU={sample['cpu_percent']}%, Memory={sample['memory_used_mb']}MB")
    
    def test_no_hardcoded_mock_values(self):
        """Ensure no hardcoded mock values exist in the service"""
        import services.vm_metrics_service as vm_module
        import inspect
        
        source = inspect.getsource(vm_module)
        
        # Common mock values that should not appear
        suspicious_values = [
            "25.5",      # Common mock CPU percentage
            "45.2",      # Common mock memory percentage  
            "1024.0",    # Common mock memory MB
            "1048576",   # Common mock network bytes (1MB)
            "524288",    # Common mock network bytes (512KB)
            "3600",      # Common mock uptime (1 hour)
            "test_vm",   # Mock VM ID
            "mock",      # Any reference to mock
            "fake",      # Any reference to fake
            "dummy"      # Any reference to dummy
        ]
        
        for suspicious in suspicious_values:
            # Allow these values in comments or error messages, but not in return statements
            lines_with_value = [line for line in source.split('\n') if suspicious in line]
            for line in lines_with_value:
                stripped = line.strip()
                # Skip comments and docstrings
                if stripped.startswith('#') or stripped.startswith('"""') or stripped.startswith("'''"):
                    continue
                # Skip error messages and logs
                if 'logger.' in line or 'error' in line.lower() or 'log' in line.lower():
                    continue
                # If it's in a return statement or assignment, it's suspicious
                if 'return' in line or '=' in line:
                    logger.warning(f"Suspicious hardcoded value '{suspicious}' found in: {stripped}")
        
        logger.info("✅ No obvious hardcoded mock values found")
    
    @pytest.mark.asyncio
    async def test_real_process_monitoring(self):
        """Test real process monitoring within containers"""
        container_id = "turdpartycollab_api"
        
        metrics = await vm_metrics_service.get_vm_metrics(container_id, "docker")
        
        processes = metrics["top_processes"]
        assert isinstance(processes, list)
        
        if processes:
            # Verify process data looks real
            for process in processes[:3]:  # Check first 3 processes
                assert isinstance(process["pid"], int)
                assert process["pid"] > 0
                assert isinstance(process["name"], str)
                assert len(process["name"]) > 0
                assert isinstance(process["cpu_percent"], (int, float))
                assert process["cpu_percent"] >= 0
                
                logger.info(f"   Process: PID={process['pid']}, Name={process['name']}, CPU={process['cpu_percent']}%")
        
        logger.info(f"✅ Real process monitoring verified: {len(processes)} processes found")

    @pytest.mark.asyncio
    async def test_real_network_statistics(self):
        """Test real network I/O statistics collection"""
        container_id = "turdpartycollab_api"

        # Get initial metrics
        metrics1 = await vm_metrics_service.get_vm_metrics(container_id, "docker")
        initial_rx = metrics1["network_rx_bytes"]
        initial_tx = metrics1["network_tx_bytes"]

        # Make some network activity (API call to generate traffic)
        import requests
        try:
            requests.get("http://api.turdparty.localhost/api/v1/status", timeout=5)
        except:
            pass  # Network activity attempted, continue with test

        await asyncio.sleep(2)  # Allow metrics to update

        # Get updated metrics
        metrics2 = await vm_metrics_service.get_vm_metrics(container_id, "docker")
        updated_rx = metrics2["network_rx_bytes"]
        updated_tx = metrics2["network_tx_bytes"]

        # Verify network counters are real (should be non-zero and potentially different)
        assert isinstance(initial_rx, int)
        assert isinstance(initial_tx, int)
        assert isinstance(updated_rx, int)
        assert isinstance(updated_tx, int)

        # Network counters should be non-negative
        assert initial_rx >= 0
        assert initial_tx >= 0
        assert updated_rx >= 0
        assert updated_tx >= 0

        logger.info(f"✅ Real network stats: RX={updated_rx} bytes, TX={updated_tx} bytes")

    @pytest.mark.asyncio
    async def test_real_memory_tracking(self):
        """Test real memory usage tracking"""
        container_id = "turdpartycollab_api"

        metrics = await vm_metrics_service.get_vm_metrics(container_id, "docker")

        # Verify memory metrics are realistic
        memory_used_mb = metrics["memory_used_mb"]
        memory_percent = metrics["memory_percent"]
        memory_used_bytes = metrics["memory_used_bytes"]
        memory_limit_bytes = metrics["memory_limit_bytes"]

        # Basic sanity checks for real memory data
        assert isinstance(memory_used_mb, (int, float))
        assert isinstance(memory_percent, (int, float))
        assert isinstance(memory_used_bytes, int)
        assert isinstance(memory_limit_bytes, int)

        # Memory should be positive
        assert memory_used_mb > 0
        assert memory_used_bytes > 0
        assert memory_limit_bytes > 0

        # Memory percentage should be reasonable
        assert 0 <= memory_percent <= 100

        # Consistency check: bytes and MB should match
        calculated_mb = memory_used_bytes / (1024 * 1024)
        assert abs(calculated_mb - memory_used_mb) < 1.0  # Allow small rounding differences

        # Memory limit should be reasonable (at least 100MB for a real container)
        assert memory_limit_bytes > 100 * 1024 * 1024

        logger.info(f"✅ Real memory tracking: {memory_used_mb}MB used ({memory_percent}%)")

    @pytest.mark.asyncio
    async def test_real_uptime_calculation(self):
        """Test real container uptime calculation"""
        container_id = "turdpartycollab_api"

        metrics = await vm_metrics_service.get_vm_metrics(container_id, "docker")
        uptime_seconds = metrics["uptime_seconds"]

        # Verify uptime is realistic
        assert isinstance(uptime_seconds, int)
        assert uptime_seconds >= 0

        # For a running container, uptime should be at least a few seconds
        if metrics["status"] == "running":
            assert uptime_seconds > 0

        # Uptime shouldn't be impossibly large (less than 1 year)
        max_uptime = 365 * 24 * 60 * 60  # 1 year in seconds
        assert uptime_seconds < max_uptime

        # Convert to human readable
        hours = uptime_seconds // 3600
        minutes = (uptime_seconds % 3600) // 60
        seconds = uptime_seconds % 60

        logger.info(f"✅ Real uptime: {uptime_seconds}s ({hours}h {minutes}m {seconds}s)")

    @pytest.mark.asyncio
    async def test_real_container_status(self):
        """Test real container status detection"""
        container_id = "turdpartycollab_api"

        metrics = await vm_metrics_service.get_vm_metrics(container_id, "docker")
        status = metrics["status"]

        # Status should be a real Docker status
        valid_statuses = ["running", "stopped", "paused", "restarting", "removing", "dead", "created"]
        assert status in valid_statuses, f"Invalid status: {status}"

        # For our test container, it should be running
        assert status == "running", f"Test container should be running, got: {status}"

        logger.info(f"✅ Real container status: {status}")

    def test_service_initialization_real(self):
        """Test that service initialization uses real Docker client"""
        # Reset the service to test initialization
        vm_metrics_service.docker_client = None

        # Initialize should create real Docker client
        asyncio.run(vm_metrics_service.initialize())

        # Verify Docker client was created (not mocked)
        assert vm_metrics_service.docker_client is not None

        # Try to use the client (should work with real Docker)
        try:
            # This should work if Docker is available
            containers = vm_metrics_service.docker_client.containers.list()
            assert isinstance(containers, list)
            logger.info(f"✅ Real Docker client initialized: {len(containers)} containers found")
        except Exception as e:
            # If Docker client fails, it should be a real Docker error, not a mock error
            assert "mock" not in str(e).lower()
            assert "fake" not in str(e).lower()
            logger.info(f"✅ Real Docker client error (expected): {e}")

    @pytest.mark.asyncio
    async def test_concurrent_metrics_collection(self):
        """Test concurrent real metrics collection"""
        container_id = "turdpartycollab_api"

        # Collect metrics concurrently
        tasks = []
        for i in range(5):
            task = asyncio.create_task(
                vm_metrics_service.get_vm_metrics(container_id, "docker")
            )
            tasks.append(task)

        results = await asyncio.gather(*tasks)

        # Verify all results are valid
        assert len(results) == 5

        for i, metrics in enumerate(results):
            assert metrics["vm_id"] == container_id
            assert metrics["vm_type"] == "docker"
            assert isinstance(metrics["timestamp"], int)

            logger.info(f"   Concurrent result {i+1}: CPU={metrics['cpu_percent']}%, Memory={metrics['memory_used_mb']}MB")

        # Timestamps should be close but potentially different (proving real-time collection)
        timestamps = [result["timestamp"] for result in results]
        timestamp_range = max(timestamps) - min(timestamps)

        # Should complete within a few seconds
        assert timestamp_range < 10000  # Less than 10 seconds difference

        logger.info(f"✅ Concurrent metrics collection: {len(results)} results in {timestamp_range}ms range")
