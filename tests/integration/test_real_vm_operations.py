"""
Real VM Integration Tests - No Mocks
Tests actual Docker containers and VM creation/management
"""

import pytest
import asyncio
import docker
import time
import uuid
import subprocess
import os
from typing import Dict, List, Optional
from datetime import datetime, timedelta

import httpx
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Test configuration
API_BASE_URL = "http://localhost:8000"
TEST_TIMEOUT = 300  # 5 minutes for VM operations
DOCKER_CLIENT = None
CREATED_CONTAINERS = []
CREATED_VMS = []


@pytest.fixture(scope="session", autouse=True)
def setup_docker_client():
    """Setup Docker client for real container testing."""
    global DOCKER_CLIENT
    try:
        DOCKER_CLIENT = docker.from_env()
        # Test Docker connection
        DOCKER_CLIENT.ping()
        print("✅ Docker client connected successfully")
    except Exception as e:
        pytest.skip(f"Docker not available: {e}")
    
    yield
    
    # Cleanup all created containers
    cleanup_test_containers()


def cleanup_test_containers():
    """Clean up all test containers."""
    global CREATED_CONTAINERS, CREATED_VMS
    
    if DOCKER_CLIENT:
        for container_id in CREATED_CONTAINERS:
            try:
                container = DOCKER_CLIENT.containers.get(container_id)
                container.stop(timeout=5)
                container.remove(force=True)
                print(f"🧹 Cleaned up container: {container_id[:12]}")
            except Exception as e:
                print(f"⚠️ Failed to cleanup container {container_id[:12]}: {e}")
    
    # Cleanup VMs via API
    for vm_id in CREATED_VMS:
        try:
            response = httpx.delete(f"{API_BASE_URL}/api/v1/vms/{vm_id}?force=true")
            if response.status_code in [200, 404]:
                print(f"🧹 Cleaned up VM: {vm_id}")
        except Exception as e:
            print(f"⚠️ Failed to cleanup VM {vm_id}: {e}")
    
    CREATED_CONTAINERS.clear()
    CREATED_VMS.clear()


class TestRealDockerVMOperations:
    """Test real Docker VM operations."""
    
    def test_docker_ubuntu_vm_lifecycle(self):
        """Test complete Docker Ubuntu VM lifecycle."""
        print("\n🐳 Testing Docker Ubuntu VM lifecycle...")
        
        # Create VM via API
        vm_data = {
            "name": f"test-ubuntu-{int(time.time())}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 512,
            "cpus": 1,
            "domain": "TurdParty",
            "description": "Real Ubuntu Docker VM test"
        }
        
        with httpx.Client(timeout=30.0) as client:
            # Create VM
            response = client.post(f"{API_BASE_URL}/api/v1/vms/", json=vm_data)
            assert response.status_code == 201, f"VM creation failed: {response.text}"
            
            vm_info = response.json()
            vm_id = vm_info["vm_id"]
            CREATED_VMS.append(vm_id)
            
            print(f"✅ VM created: {vm_id}")
            
            # Wait for VM to be created (check status)
            max_wait = 60  # 1 minute
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                status_response = client.get(f"{API_BASE_URL}/api/v1/vms/{vm_id}")
                if status_response.status_code == 200:
                    vm_status = status_response.json()
                    print(f"📊 VM Status: {vm_status['status']}")
                    
                    if vm_status["status"] in ["running", "failed"]:
                        break
                
                time.sleep(5)
            
            # Verify VM is running
            final_status = client.get(f"{API_BASE_URL}/api/v1/vms/{vm_id}")
            assert final_status.status_code == 200
            vm_details = final_status.json()
            
            print(f"🎯 Final VM Status: {vm_details['status']}")
            
            # If VM creation succeeded, verify Docker container exists
            if vm_details["status"] == "running":
                # Find the Docker container
                containers = DOCKER_CLIENT.containers.list(
                    filters={"label": f"turdparty.vm.name={vm_data['name']}"}
                )
                
                assert len(containers) > 0, "Docker container not found"
                container = containers[0]
                CREATED_CONTAINERS.append(container.id)
                
                print(f"✅ Docker container found: {container.name}")
                
                # Test container is actually running Ubuntu
                exec_result = container.exec_run("cat /etc/os-release")
                assert exec_result.exit_code == 0
                assert b"Ubuntu" in exec_result.output
                print("✅ Container is running Ubuntu")
                
                # Test VM actions
                self._test_vm_actions(client, vm_id)
            
            else:
                print(f"⚠️ VM creation failed with status: {vm_details['status']}")
                if vm_details.get("error_message"):
                    print(f"Error: {vm_details['error_message']}")
    
    def test_docker_alpine_vm_creation(self):
        """Test Docker Alpine VM creation and basic operations."""
        print("\n🏔️ Testing Docker Alpine VM...")
        
        vm_data = {
            "name": f"test-alpine-{int(time.time())}",
            "template": "alpine:latest",
            "vm_type": "docker",
            "memory_mb": 256,
            "cpus": 1,
            "domain": "TurdParty",
            "description": "Real Alpine Docker VM test"
        }
        
        with httpx.Client(timeout=30.0) as client:
            response = client.post(f"{API_BASE_URL}/api/v1/vms/", json=vm_data)
            assert response.status_code == 201
            
            vm_info = response.json()
            vm_id = vm_info["vm_id"]
            CREATED_VMS.append(vm_id)
            
            print(f"✅ Alpine VM created: {vm_id}")
            
            # Wait for creation
            time.sleep(30)
            
            # Check if container was created
            containers = DOCKER_CLIENT.containers.list(
                filters={"label": f"turdparty.vm.name={vm_data['name']}"}
            )
            
            if containers:
                container = containers[0]
                CREATED_CONTAINERS.append(container.id)
                
                # Test it's actually Alpine
                exec_result = container.exec_run("cat /etc/alpine-release")
                if exec_result.exit_code == 0:
                    print("✅ Container is running Alpine Linux")
                    print(f"Alpine version: {exec_result.output.decode().strip()}")
    
    def test_docker_resource_limits(self):
        """Test Docker container resource limits are applied."""
        print("\n📊 Testing Docker resource limits...")
        
        vm_data = {
            "name": f"test-resources-{int(time.time())}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 1024,  # 1GB
            "cpus": 2,
            "domain": "TurdParty",
            "description": "Resource limits test"
        }
        
        with httpx.Client(timeout=30.0) as client:
            response = client.post(f"{API_BASE_URL}/api/v1/vms/", json=vm_data)
            assert response.status_code == 201
            
            vm_info = response.json()
            vm_id = vm_info["vm_id"]
            CREATED_VMS.append(vm_id)
            
            # Wait for creation
            time.sleep(30)
            
            # Find container and check resource limits
            containers = DOCKER_CLIENT.containers.list(
                filters={"label": f"turdparty.vm.name={vm_data['name']}"}
            )
            
            if containers:
                container = containers[0]
                CREATED_CONTAINERS.append(container.id)
                
                # Reload container to get latest stats
                container.reload()
                
                # Check memory limit (Docker API returns in bytes)
                host_config = container.attrs.get("HostConfig", {})
                memory_limit = host_config.get("Memory", 0)
                
                expected_memory = vm_data["memory_mb"] * 1024 * 1024  # Convert to bytes
                
                if memory_limit > 0:
                    print(f"✅ Memory limit applied: {memory_limit / (1024*1024):.0f}MB")
                    assert abs(memory_limit - expected_memory) < 1024*1024  # Allow 1MB tolerance
                
                # Check CPU limit
                cpu_count = host_config.get("CpuCount", 0)
                if cpu_count > 0:
                    print(f"✅ CPU limit applied: {cpu_count} cores")
                    assert cpu_count == vm_data["cpus"]
    
    def _test_vm_actions(self, client: httpx.Client, vm_id: str):
        """Test VM actions on a running VM."""
        print(f"⚡ Testing VM actions for {vm_id}")
        
        # Test START action (should already be running)
        start_response = client.post(
            f"{API_BASE_URL}/api/v1/vms/{vm_id}/action",
            json={"action": "start", "force": false}
        )
        assert start_response.status_code == 200
        start_result = start_response.json()
        print(f"✅ START action: {start_result.get('message', 'Success')}")
        
        # Test STOP action
        stop_response = client.post(
            f"{API_BASE_URL}/api/v1/vms/{vm_id}/action",
            json={"action": "stop", "force": false}
        )
        assert stop_response.status_code == 200
        stop_result = stop_response.json()
        print(f"✅ STOP action: {stop_result.get('message', 'Success')}")
        
        # Wait a bit for stop to process
        time.sleep(10)
        
        # Test DESTROY action
        destroy_response = client.post(
            f"{API_BASE_URL}/api/v1/vms/{vm_id}/action",
            json={"action": "destroy", "force": true}
        )
        assert destroy_response.status_code == 200
        destroy_result = destroy_response.json()
        print(f"✅ DESTROY action: {destroy_result.get('message', 'Success')}")


class TestRealVMTemplates:
    """Test real VM template functionality."""
    
    def test_template_to_docker_image_mapping(self):
        """Test that templates correctly map to Docker images."""
        print("\n🎯 Testing template to Docker image mapping...")
        
        template_mappings = [
            ("ubuntu:20.04", "ubuntu:20.04"),
            ("ubuntu:22.04", "ubuntu:22.04"),
            ("alpine:latest", "alpine:latest"),
        ]
        
        for template, expected_image in template_mappings:
            vm_data = {
                "name": f"test-template-{template.replace(':', '-')}-{int(time.time())}",
                "template": template,
                "vm_type": "docker",
                "memory_mb": 256,
                "cpus": 1,
                "domain": "TurdParty"
            }
            
            with httpx.Client(timeout=30.0) as client:
                response = client.post(f"{API_BASE_URL}/api/v1/vms/", json=vm_data)
                
                if response.status_code == 201:
                    vm_info = response.json()
                    vm_id = vm_info["vm_id"]
                    CREATED_VMS.append(vm_id)
                    
                    print(f"✅ Template {template} -> VM created: {vm_id}")
                    
                    # Wait for container creation
                    time.sleep(20)
                    
                    # Verify correct Docker image was used
                    containers = DOCKER_CLIENT.containers.list(
                        filters={"label": f"turdparty.vm.name={vm_data['name']}"}
                    )
                    
                    if containers:
                        container = containers[0]
                        CREATED_CONTAINERS.append(container.id)
                        
                        # Get container image
                        container.reload()
                        image_tags = container.image.tags
                        
                        # Check if expected image is in tags
                        image_match = any(expected_image in tag for tag in image_tags)
                        if image_match:
                            print(f"✅ Correct image used: {image_tags}")
                        else:
                            print(f"⚠️ Image mismatch. Expected: {expected_image}, Got: {image_tags}")


class TestRealVMFileInjection:
    """Test real file injection into VMs."""
    
    def test_file_injection_to_docker_vm(self):
        """Test injecting a real file into a Docker VM."""
        print("\n💉 Testing file injection to Docker VM...")
        
        # Create a test file
        test_file_content = f"Test file created at {datetime.now()}\nVM injection test\n"
        test_file_path = "/tmp/test_injection_file.txt"
        
        with open(test_file_path, "w") as f:
            f.write(test_file_content)
        
        try:
            # Create VM
            vm_data = {
                "name": f"test-injection-{int(time.time())}",
                "template": "ubuntu:20.04",
                "vm_type": "docker",
                "memory_mb": 512,
                "cpus": 1,
                "domain": "TurdParty",
                "description": "File injection test VM"
            }
            
            with httpx.Client(timeout=30.0) as client:
                response = client.post(f"{API_BASE_URL}/api/v1/vms/", json=vm_data)
                assert response.status_code == 201
                
                vm_info = response.json()
                vm_id = vm_info["vm_id"]
                CREATED_VMS.append(vm_id)
                
                # Wait for VM creation
                time.sleep(30)
                
                # Find the container
                containers = DOCKER_CLIENT.containers.list(
                    filters={"label": f"turdparty.vm.name={vm_data['name']}"}
                )
                
                if containers:
                    container = containers[0]
                    CREATED_CONTAINERS.append(container.id)
                    
                    # Test file injection using docker cp
                    try:
                        # Copy file to container
                        with open(test_file_path, "rb") as f:
                            container.put_archive("/tmp/", f.read())
                        
                        # Verify file exists in container
                        exec_result = container.exec_run("cat /tmp/test_injection_file.txt")
                        
                        if exec_result.exit_code == 0:
                            injected_content = exec_result.output.decode()
                            assert test_file_content in injected_content
                            print("✅ File injection successful")
                            print(f"Injected content: {injected_content.strip()}")
                        else:
                            print(f"⚠️ File not found in container: {exec_result.output.decode()}")
                    
                    except Exception as e:
                        print(f"⚠️ File injection failed: {e}")
        
        finally:
            # Cleanup test file
            if os.path.exists(test_file_path):
                os.remove(test_file_path)


class TestRealVMNetworking:
    """Test real VM networking functionality."""
    
    def test_docker_vm_network_isolation(self):
        """Test Docker VM network isolation."""
        print("\n🌐 Testing Docker VM network isolation...")
        
        vm_data = {
            "name": f"test-network-{int(time.time())}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 512,
            "cpus": 1,
            "domain": "TurdParty",
            "description": "Network isolation test"
        }
        
        with httpx.Client(timeout=30.0) as client:
            response = client.post(f"{API_BASE_URL}/api/v1/vms/", json=vm_data)
            assert response.status_code == 201
            
            vm_info = response.json()
            vm_id = vm_info["vm_id"]
            CREATED_VMS.append(vm_id)
            
            # Wait for creation
            time.sleep(30)
            
            # Find container and check network
            containers = DOCKER_CLIENT.containers.list(
                filters={"label": f"turdparty.vm.name={vm_data['name']}"}
            )
            
            if containers:
                container = containers[0]
                CREATED_CONTAINERS.append(container.id)
                
                # Check network configuration
                container.reload()
                networks = container.attrs["NetworkSettings"]["Networks"]
                
                print(f"📊 Container networks: {list(networks.keys())}")
                
                # Verify container has network connectivity (try multiple methods)
                connectivity_tests = [
                    ("ping -c 1 *******", "ping"),
                    ("wget -q --spider http://google.com", "wget"),
                    ("curl -s --head http://google.com", "curl"),
                    ("nc -z ******* 53", "netcat"),
                ]

                connectivity_success = False
                for test_cmd, test_name in connectivity_tests:
                    try:
                        exec_result = container.exec_run(test_cmd, timeout=5)
                        if exec_result.exit_code == 0:
                            print(f"✅ Container has external network connectivity (via {test_name})")
                            connectivity_success = True
                            break
                    except Exception as e:
                        print(f"⚠️ Network test {test_name} failed: {e}")
                        continue

                if not connectivity_success:
                    print("ℹ️ Network connectivity tests failed (commands may not be available in container)")
                
                # Check if container is on turdpartycollab network
                if "turdpartycollab_net" in networks:
                    network_info = networks["turdpartycollab_net"]
                    ip_address = network_info.get("IPAddress")
                    if ip_address:
                        print(f"✅ Container on turdpartycollab_net: {ip_address}")


if __name__ == "__main__":
    # Run tests with verbose output
    pytest.main([__file__, "-v", "-s", "--tb=short"])
