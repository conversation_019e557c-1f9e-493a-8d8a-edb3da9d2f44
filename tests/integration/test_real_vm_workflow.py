"""
Real VM Workflow Integration Tests
Tests complete end-to-end workflow: File Upload -> VM Creation -> File Injection -> Monitoring
"""

import pytest
import asyncio
import time
import uuid
import os
import tempfile
import hashlib
from typing import Dict, List, Optional
from datetime import datetime

import httpx
import docker
from minio import Minio
from minio.error import S3Error

# Test configuration
API_BASE_URL = "http://localhost:8000"
MINIO_ENDPOINT = "localhost:9000"
MINIO_ACCESS_KEY = "minioadmin"
MINIO_SECRET_KEY = "minioadmin"
MINIO_BUCKET = "turdparty-uploads"

CREATED_VMS = []
UPLOADED_FILES = []
DOCKER_CLIENT = None


@pytest.fixture(scope="session", autouse=True)
def setup_workflow_environment():
    """Setup environment for workflow testing."""
    global DOCKER_CLIENT
    
    # Setup Docker client
    try:
        DOCKER_CLIENT = docker.from_env()
        DOCKER_CLIENT.ping()
        print("✅ Docker client connected")
    except Exception as e:
        pytest.skip(f"Docker not available: {e}")
    
    # Setup MinIO client
    try:
        minio_client = Minio(
            MINIO_ENDPOINT,
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=False
        )
        
        # Test MinIO connection
        minio_client.list_buckets()
        print("✅ MinIO client connected")
        
        # Ensure bucket exists
        if not minio_client.bucket_exists(MINIO_BUCKET):
            minio_client.make_bucket(MINIO_BUCKET)
            print(f"✅ Created bucket: {MINIO_BUCKET}")
        
    except Exception as e:
        pytest.skip(f"MinIO not available: {e}")
    
    yield
    
    # Cleanup
    cleanup_workflow_resources()


def cleanup_workflow_resources():
    """Clean up all workflow test resources."""
    global CREATED_VMS, UPLOADED_FILES
    
    # Cleanup VMs
    for vm_id in CREATED_VMS:
        try:
            response = httpx.delete(f"{API_BASE_URL}/api/v1/vms/{vm_id}?force=true")
            if response.status_code in [200, 404]:
                print(f"🧹 Cleaned up VM: {vm_id}")
        except Exception as e:
            print(f"⚠️ Failed to cleanup VM {vm_id}: {e}")
    
    # Cleanup Docker containers
    if DOCKER_CLIENT:
        containers = DOCKER_CLIENT.containers.list(
            all=True,
            filters={"label": "turdparty.vm=true"}
        )
        for container in containers:
            try:
                container.stop(timeout=5)
                container.remove(force=True)
                print(f"🧹 Cleaned up container: {container.name}")
            except Exception as e:
                print(f"⚠️ Failed to cleanup container: {e}")
    
    # Cleanup MinIO files
    try:
        minio_client = Minio(
            MINIO_ENDPOINT,
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=False
        )
        
        for file_name in UPLOADED_FILES:
            try:
                minio_client.remove_object(MINIO_BUCKET, file_name)
                print(f"🧹 Cleaned up file: {file_name}")
            except Exception as e:
                print(f"⚠️ Failed to cleanup file {file_name}: {e}")
    except Exception as e:
        print(f"⚠️ MinIO cleanup failed: {e}")
    
    CREATED_VMS.clear()
    UPLOADED_FILES.clear()


class TestRealVMWorkflow:
    """Test complete VM workflow with real components."""
    
    def test_complete_file_analysis_workflow(self):
        """Test complete file analysis workflow: Upload -> VM -> Inject -> Monitor."""
        print("\n🔄 Testing complete file analysis workflow...")
        
        # Step 1: Create test file
        test_file_content = self._create_test_malware_sample()
        test_file_path = "/tmp/test_malware_sample.exe"
        
        with open(test_file_path, "wb") as f:
            f.write(test_file_content)
        
        try:
            # Step 2: Upload file to MinIO
            file_uuid = self._upload_file_to_minio(test_file_path)
            print(f"✅ File uploaded to MinIO: {file_uuid}")
            
            # Step 3: Create VM for analysis
            vm_id = self._create_analysis_vm()
            print(f"✅ Analysis VM created: {vm_id}")
            
            # Step 4: Wait for VM to be ready
            vm_ready = self._wait_for_vm_ready(vm_id, timeout=120)
            if not vm_ready:
                pytest.fail("VM failed to become ready")
            
            # Step 5: Inject file into VM
            injection_success = self._inject_file_into_vm(vm_id, file_uuid)
            if injection_success:
                print("✅ File injection successful")
            else:
                print("⚠️ File injection failed or not implemented")
            
            # Step 6: Monitor VM runtime
            self._monitor_vm_runtime(vm_id, duration=60)
            
            # Step 7: Verify VM auto-termination (simulate)
            self._test_vm_termination(vm_id)
            
            print("✅ Complete workflow test passed")
        
        finally:
            # Cleanup test file
            if os.path.exists(test_file_path):
                os.remove(test_file_path)
    
    def test_multiple_vm_concurrent_analysis(self):
        """Test multiple VMs running concurrent analysis."""
        print("\n🔀 Testing concurrent VM analysis...")
        
        vm_configs = [
            {"name": "concurrent-ubuntu", "template": "ubuntu:20.04", "memory_mb": 512},
            {"name": "concurrent-alpine", "template": "alpine:latest", "memory_mb": 256},
            {"name": "concurrent-ubuntu2", "template": "ubuntu:22.04", "memory_mb": 512},
        ]
        
        created_vms = []
        
        try:
            # Create multiple VMs
            for config in vm_configs:
                vm_data = {
                    "name": f"{config['name']}-{int(time.time())}",
                    "template": config["template"],
                    "vm_type": "docker",
                    "memory_mb": config["memory_mb"],
                    "cpus": 1,
                    "domain": "TurdParty",
                    "description": f"Concurrent analysis VM - {config['name']}"
                }
                
                with httpx.Client(timeout=30.0) as client:
                    response = client.post(f"{API_BASE_URL}/api/v1/vms/", json=vm_data)
                    if response.status_code == 201:
                        vm_info = response.json()
                        vm_id = vm_info["vm_id"]
                        created_vms.append(vm_id)
                        CREATED_VMS.append(vm_id)
                        print(f"✅ Created VM: {config['name']} ({vm_id})")
            
            # Wait for all VMs to be ready
            print(f"⏳ Waiting for {len(created_vms)} VMs to be ready...")
            time.sleep(60)  # Give VMs time to start
            
            # Check status of all VMs
            ready_count = 0
            with httpx.Client(timeout=30.0) as client:
                for vm_id in created_vms:
                    response = client.get(f"{API_BASE_URL}/api/v1/vms/{vm_id}")
                    if response.status_code == 200:
                        vm_info = response.json()
                        status = vm_info["status"]
                        print(f"📊 VM {vm_id}: {status}")
                        if status == "running":
                            ready_count += 1
            
            print(f"✅ {ready_count}/{len(created_vms)} VMs are running")
            
            # Test concurrent operations
            if ready_count > 0:
                self._test_concurrent_vm_operations(created_vms[:ready_count])
        
        except Exception as e:
            print(f"❌ Concurrent VM test failed: {e}")
            raise
    
    def test_vm_resource_monitoring(self):
        """Test VM resource monitoring and metrics collection."""
        print("\n📊 Testing VM resource monitoring...")
        
        # Create VM with specific resource allocation
        vm_data = {
            "name": f"monitoring-test-{int(time.time())}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 1024,
            "cpus": 2,
            "domain": "TurdParty",
            "description": "Resource monitoring test VM"
        }
        
        with httpx.Client(timeout=30.0) as client:
            response = client.post(f"{API_BASE_URL}/api/v1/vms/", json=vm_data)
            assert response.status_code == 201
            
            vm_info = response.json()
            vm_id = vm_info["vm_id"]
            CREATED_VMS.append(vm_id)
            
            # Wait for VM to be ready
            time.sleep(30)
            
            # Monitor VM metrics
            self._collect_vm_metrics(vm_id, vm_data["name"])
            
            # Test runtime calculations
            response = client.get(f"{API_BASE_URL}/api/v1/vms/{vm_id}")
            if response.status_code == 200:
                vm_details = response.json()
                runtime_minutes = vm_details.get("runtime_minutes", 0)
                is_expired = vm_details.get("is_expired", False)
                
                print(f"📊 VM Runtime: {runtime_minutes:.2f} minutes")
                print(f"📊 VM Expired: {is_expired}")
                
                # Runtime should be positive but less than 30 minutes for new VM
                assert 0 <= runtime_minutes < 30, f"Invalid runtime: {runtime_minutes}"
                assert not is_expired, "New VM should not be expired"
    
    def _create_test_malware_sample(self) -> bytes:
        """Create a test file that simulates a malware sample."""
        # Create a harmless test file that looks like a PE executable
        pe_header = b"MZ\x90\x00"  # DOS header
        test_content = b"This is a test malware sample for TurdParty analysis\n"
        test_content += b"Created at: " + datetime.now().isoformat().encode()
        test_content += b"\n" + b"A" * 1000  # Padding
        
        return pe_header + test_content
    
    def _upload_file_to_minio(self, file_path: str) -> str:
        """Upload file to MinIO and return UUID."""
        minio_client = Minio(
            MINIO_ENDPOINT,
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=False
        )
        
        # Generate UUID for file
        file_uuid = str(uuid.uuid4())
        
        # Upload file
        minio_client.fput_object(
            MINIO_BUCKET,
            file_uuid,
            file_path,
            metadata={"original-name": os.path.basename(file_path)}
        )
        
        UPLOADED_FILES.append(file_uuid)
        return file_uuid
    
    def _create_analysis_vm(self) -> str:
        """Create a VM for malware analysis."""
        vm_data = {
            "name": f"analysis-vm-{int(time.time())}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 1024,
            "cpus": 2,
            "domain": "TurdParty",
            "description": "Malware analysis VM"
        }
        
        with httpx.Client(timeout=30.0) as client:
            response = client.post(f"{API_BASE_URL}/api/v1/vms/", json=vm_data)
            assert response.status_code == 201
            
            vm_info = response.json()
            vm_id = vm_info["vm_id"]
            CREATED_VMS.append(vm_id)
            
            return vm_id
    
    def _wait_for_vm_ready(self, vm_id: str, timeout: int = 120) -> bool:
        """Wait for VM to be ready."""
        start_time = time.time()
        
        with httpx.Client(timeout=30.0) as client:
            while time.time() - start_time < timeout:
                response = client.get(f"{API_BASE_URL}/api/v1/vms/{vm_id}")
                if response.status_code == 200:
                    vm_info = response.json()
                    status = vm_info["status"]
                    
                    if status == "running":
                        return True
                    elif status == "failed":
                        print(f"❌ VM failed: {vm_info.get('error_message', 'Unknown error')}")
                        return False
                
                time.sleep(5)
        
        return False
    
    def _inject_file_into_vm(self, vm_id: str, file_uuid: str) -> bool:
        """Inject file into VM (simulated for now)."""
        # This would normally use the file injection API
        # For now, we'll simulate it by checking if the VM has a container
        
        containers = DOCKER_CLIENT.containers.list(
            filters={"label": "turdparty.vm=true"}
        )
        
        for container in containers:
            # Check if this container belongs to our VM
            vm_name = container.labels.get("turdparty.vm.name", "")
            if vm_name:
                # Simulate file injection by creating a file in the container
                try:
                    exec_result = container.exec_run(
                        f"echo 'Injected file: {file_uuid}' > /tmp/injected_file.txt"
                    )
                    if exec_result.exit_code == 0:
                        print(f"✅ Simulated file injection into container: {container.name}")
                        return True
                except Exception as e:
                    print(f"⚠️ File injection simulation failed: {e}")
        
        return False
    
    def _monitor_vm_runtime(self, vm_id: str, duration: int = 60):
        """Monitor VM runtime for specified duration."""
        print(f"👁️ Monitoring VM {vm_id} for {duration} seconds...")
        
        start_time = time.time()
        
        with httpx.Client(timeout=30.0) as client:
            while time.time() - start_time < duration:
                response = client.get(f"{API_BASE_URL}/api/v1/vms/{vm_id}")
                if response.status_code == 200:
                    vm_info = response.json()
                    runtime = vm_info.get("runtime_minutes", 0)
                    status = vm_info["status"]
                    
                    print(f"📊 Runtime: {runtime:.2f}min, Status: {status}")
                
                time.sleep(10)
    
    def _test_vm_termination(self, vm_id: str):
        """Test VM termination."""
        print(f"🛑 Testing VM termination: {vm_id}")
        
        with httpx.Client(timeout=30.0) as client:
            # Test stop action
            response = client.post(
                f"{API_BASE_URL}/api/v1/vms/{vm_id}/action",
                json={"action": "stop", "force": true}
            )
            
            if response.status_code == 200:
                print("✅ VM stop action queued")
            else:
                print(f"⚠️ VM stop failed: {response.text}")
    
    def _test_concurrent_vm_operations(self, vm_ids: List[str]):
        """Test operations on multiple VMs concurrently."""
        print(f"⚡ Testing concurrent operations on {len(vm_ids)} VMs...")
        
        with httpx.Client(timeout=30.0) as client:
            # Test concurrent status checks
            for vm_id in vm_ids:
                response = client.get(f"{API_BASE_URL}/api/v1/vms/{vm_id}")
                if response.status_code == 200:
                    vm_info = response.json()
                    print(f"📊 VM {vm_id}: {vm_info['status']}")
    
    def _collect_vm_metrics(self, vm_id: str, vm_name: str):
        """Collect VM metrics from Docker container."""
        containers = DOCKER_CLIENT.containers.list(
            filters={"label": f"turdparty.vm.name={vm_name}"}
        )
        
        if containers:
            container = containers[0]
            
            # Get container stats
            stats = container.stats(stream=False)
            
            # Extract memory usage
            memory_usage = stats["memory_stats"].get("usage", 0)
            memory_limit = stats["memory_stats"].get("limit", 0)
            
            if memory_limit > 0:
                memory_percent = (memory_usage / memory_limit) * 100
                print(f"📊 Memory usage: {memory_usage / (1024*1024):.1f}MB ({memory_percent:.1f}%)")
            
            # Extract CPU usage
            cpu_stats = stats.get("cpu_stats", {})
            if cpu_stats:
                print(f"📊 CPU stats available: {bool(cpu_stats)}")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s", "--tb=short"])
