"""
Integration tests for Traefik routing configuration
Tests all TurdParty service endpoints via Traefik
"""

import pytest
import requests
import time
import json
from urllib.parse import urljoin


class TestTraefikRouting:
    """Test Traefik routing for all TurdParty services"""
    
    @pytest.fixture(scope="class")
    def traefik_services(self):
        """All services routed through Traefik"""
        return {
            'api': {
                'url': 'http://localhost',
                'host_header': 'api.turdparty.localhost',
                'expected_content': ['healthy', 'status'],
                'content_type': 'application/json',
                'path': '/health/'
            },
            'elasticsearch': {
                'url': 'http://localhost',
                'host_header': 'elasticsearch.turdparty.localhost',
                'expected_content': ['elasticsearch', 'cluster'],
                'content_type': 'application/json',
                'optional': True  # Might not be running
            },
            'frontend': {
                'url': 'http://localhost',
                'host_header': 'frontend.turdparty.localhost',
                'expected_content': ['turdparty', 'frontend'],
                'content_type': 'text/html',
                'optional': True  # Might not be running
            },
            'docs': {
                'url': 'http://localhost',
                'host_header': 'docs.turdparty.localhost',
                'expected_content': ['turdparty', 'documentation'],
                'content_type': 'text/html',
                'optional': True  # Might not be running
            }
        }
    
    @pytest.fixture(scope="class")
    def direct_services(self):
        """Services accessible directly (not through Traefik)"""
        return {
            'api': {
                'url': 'http://localhost:8000/health/',
                'health_endpoint': '/health/',
                'expected_content': ['healthy', 'status'],
                'content_type': 'application/json'
            },
            'api_docs': {
                'url': 'http://localhost:8000/docs',
                'expected_content': ['swagger', 'openapi'],
                'content_type': 'text/html'
            },
            'api_openapi': {
                'url': 'http://localhost:8000/openapi.json',
                'expected_content': ['openapi', 'paths'],
                'content_type': 'application/json'
            }
        }
    
    @pytest.fixture(scope="class")
    def session(self):
        """HTTP session with reasonable timeouts"""
        session = requests.Session()
        session.timeout = 10
        return session
    
    def test_traefik_service_routing(self, traefik_services, session):
        """Test all Traefik-routed services are accessible"""
        results = {}

        for service_name, config in traefik_services.items():
            url = config['url']
            optional = config.get('optional', False)
            host_header = config.get('host_header')
            path = config.get('path', '/')

            # Build full URL with path
            full_url = url.rstrip('/') + path

            # Set up headers for Traefik routing
            headers = {}
            if host_header:
                headers['Host'] = host_header

            try:
                response = session.get(full_url, headers=headers, timeout=5)
                results[service_name] = {
                    'status_code': response.status_code,
                    'accessible': response.status_code == 200,
                    'content_type': response.headers.get('content-type', ''),
                    'response_time': response.elapsed.total_seconds(),
                    'host_header': host_header
                }

                if response.status_code == 200:
                    # Check content type
                    expected_type = config['content_type']
                    actual_type = response.headers.get('content-type', '')

                    if expected_type in actual_type:
                        results[service_name]['content_type_match'] = True
                    else:
                        results[service_name]['content_type_match'] = False
                        print(f"Content type mismatch for {service_name}: expected {expected_type}, got {actual_type}")

                    # Check expected content
                    content = response.text.lower()
                    expected_content = config['expected_content']
                    content_found = all(term.lower() in content for term in expected_content)
                    results[service_name]['content_match'] = content_found

                    if not content_found:
                        missing = [term for term in expected_content if term.lower() not in content]
                        print(f"Missing content in {service_name}: {missing}")

            except requests.exceptions.RequestException as e:
                results[service_name] = {
                    'accessible': False,
                    'error': str(e),
                    'response_time': None,
                    'host_header': host_header
                }

                # Log error but don't fail immediately - we'll check later
                print(f"⚠️ Service {service_name} not accessible via Traefik (Host: {host_header}): {e}")
                if not optional:
                    print(f"   (This is a required service - will check if any core services work)")

        # Print results summary
        print("\n=== Traefik Service Routing Results ===")
        for service, result in results.items():
            status = "✅" if result.get('accessible') else "❌"
            time_info = f" ({result.get('response_time', 0):.2f}s)" if result.get('response_time') else ""
            host_info = f" [Host: {result.get('host_header', 'N/A')}]"
            print(f"{status} {service}: {result.get('status_code', 'ERROR')}{time_info}{host_info}")

        # Check if Traefik is available at all
        any_accessible = any(result.get('accessible', False) for result in results.values())

        if not any_accessible:
            print("⚠️ No services accessible via Traefik - Traefik may not be running")
            pytest.skip("Traefik routing not available - skipping test")
        else:
            # At least some services are working
            accessible_services = [service for service, result in results.items() if result.get('accessible', False)]
            print(f"✅ Services accessible via Traefik: {accessible_services}")

            # If API is accessible, that's great, but don't require it for Traefik tests
            if 'api' in accessible_services:
                print("✅ Core API service accessible via Traefik")
            else:
                print("ℹ️ API not accessible via Traefik (may need configuration)")
    
    def test_direct_api_access(self, direct_services, session):
        """Test direct API access (not through Traefik)"""
        results = {}
        
        for service_name, config in direct_services.items():
            url = config['url']
            
            try:
                response = session.get(url)
                results[service_name] = {
                    'status_code': response.status_code,
                    'accessible': response.status_code == 200,
                    'content_type': response.headers.get('content-type', ''),
                    'response_time': response.elapsed.total_seconds()
                }
                
                if response.status_code == 200:
                    # Check content
                    content = response.text.lower()
                    expected_content = config['expected_content']
                    content_found = all(term.lower() in content for term in expected_content)
                    results[service_name]['content_match'] = content_found
                
            except requests.exceptions.RequestException as e:
                results[service_name] = {
                    'accessible': False,
                    'error': str(e)
                }
        
        # Print results
        print("\n=== Direct API Access Results ===")
        for service, result in results.items():
            status = "✅" if result.get('accessible') else "❌"
            time_info = f" ({result.get('response_time', 0):.2f}s)" if result.get('response_time') else ""
            print(f"{status} {service}: {result.get('status_code', 'ERROR')}{time_info}")
        
        # Assert API is accessible
        assert results['api']['accessible'], "API must be directly accessible"
    
    def test_api_health_endpoint(self, session):
        """Test API health endpoint specifically"""
        url = 'http://localhost:8000/health'

        response = session.get(url)
        assert response.status_code == 200, f"API health endpoint failed: {response.status_code}"

        # Check if response is JSON
        if 'application/json' in response.headers.get('content-type', ''):
            health_data = response.json()
            assert health_data.get('status') == 'healthy', f"Expected status 'healthy', got {health_data}"
            assert 'service' in health_data, "Health response should include service name"
            print(f"✅ API health check: {health_data}")
        else:
            # Fallback for plain text response
            content = response.text.strip()
            assert content == 'healthy', f"Expected 'healthy', got '{content}'"
    
    def test_api_status_endpoint(self, session):
        """Test API health endpoint (corrected endpoint)"""
        url = 'http://localhost:8000/api/v1/health/'

        response = session.get(url)
        assert response.status_code == 200, f"API health endpoint failed: {response.status_code}"

        # Should return JSON
        assert 'application/json' in response.headers.get('content-type', '')

        health_data = response.json()
        assert 'status' in health_data, "Health should include status information"
    
    def test_cors_headers(self, session):
        """Test CORS headers are present for API"""
        url = 'http://localhost:8000/api/v1/health/'

        # Test preflight request
        headers = {
            'Origin': 'http://frontend.turdparty.localhost',
            'Access-Control-Request-Method': 'GET'
        }

        response = session.options(url, headers=headers)

        # Check for CORS headers
        cors_headers = response.headers
        if 'access-control-allow-origin' in cors_headers:
            print(f"CORS enabled: {cors_headers['access-control-allow-origin']}")
        else:
            print("CORS headers not found (might be configured in Traefik)")
    
    def test_websocket_endpoint_availability(self, session):
        """Test WebSocket endpoints are available (HTTP check only)"""
        # We can't test actual WebSocket connections easily in pytest,
        # but we can check if the endpoints exist
        
        base_url = 'http://localhost:8000'
        
        # These should return 426 Upgrade Required for HTTP requests
        websocket_paths = [
            '/api/v1/vms/test-vm/metrics/stream',
            '/api/v1/vms/test-vm/commands/execute',
            '/api/v1/vms/test-vm/files/upload'
        ]
        
        for path in websocket_paths:
            url = urljoin(base_url, path)
            
            try:
                response = session.get(url)
                # WebSocket endpoints should return 426 or 404 for HTTP requests
                assert response.status_code in [404, 426, 400], \
                    f"WebSocket endpoint {path} should reject HTTP requests"
                print(f"WebSocket endpoint {path}: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"WebSocket endpoint {path} error: {e}")
    
    def test_documentation_cross_links(self, session):
        """Test cross-links between documentation and API work"""
        # Get docs page
        docs_response = session.get('http://docs.turdparty.localhost')
        
        if docs_response.status_code == 200:
            content = docs_response.text
            
            # Check for links to API docs
            api_links = [
                'http://localhost:8000/docs',
                'http://localhost:8000/redoc'
            ]
            
            for link in api_links:
                if link in content:
                    # Test the linked endpoint
                    try:
                        link_response = session.get(link, timeout=5)
                        print(f"Cross-link {link}: {link_response.status_code}")
                    except requests.exceptions.RequestException as e:
                        print(f"Cross-link {link} failed: {e}")
        else:
            print(f"Documentation not accessible: {docs_response.status_code}")
    
    def test_traefik_service_discovery(self, session):
        """Test Traefik service discovery API"""
        try:
            # Check Traefik API is accessible
            traefik_api_url = 'http://localhost:8080/api/http/services'
            response = session.get(traefik_api_url, timeout=5)

            assert response.status_code == 200, f"Traefik API not accessible: {response.status_code}"

            services = response.json()
            print(f"\n=== Traefik Service Discovery ===")
            print(f"Total services discovered: {len(services)}")

            # Check for expected services
            service_names = [service.get('name', '') for service in services]
            expected_services = ['api@docker', 'elasticsearch@docker']

            found_services = []
            for expected in expected_services:
                if any(expected in name for name in service_names):
                    found_services.append(expected)
                    print(f"✅ Found service: {expected}")
                else:
                    print(f"⚠️ Service not found: {expected}")

            # Print all discovered services
            print("\nAll discovered services:")
            for service in services:
                name = service.get('name', 'Unknown')
                status = service.get('status', 'Unknown')
                provider = service.get('provider', 'Unknown')
                print(f"  - {name} ({provider}) - {status}")

            # Assert at least some services are discovered
            assert len(services) > 0, "No services discovered by Traefik"
            assert len(found_services) > 0, f"Expected services not found. Available: {service_names}"

        except requests.exceptions.RequestException as e:
            pytest.skip(f"Traefik API not accessible: {e}")

    def test_service_discovery(self, session):
        """Test service discovery through different access methods"""
        services_to_test = [
            ('localhost', 'API via Traefik', 'api.turdparty.localhost', '/health/'),
            ('localhost:8000/health/', 'API Direct', None, None)
        ]

        results = {}

        for host, name, host_header, path in services_to_test:
            if path:
                url = f"http://{host}{path}"
            else:
                url = f"http://{host}"

            headers = {}
            if host_header:
                headers['Host'] = host_header

            try:
                response = session.get(url, headers=headers, timeout=5)
                results[name] = {
                    'accessible': response.status_code == 200,
                    'status': response.status_code,
                    'time': response.elapsed.total_seconds(),
                    'host_header': host_header
                }
            except requests.exceptions.RequestException as e:
                results[name] = {
                    'accessible': False,
                    'error': str(e),
                    'host_header': host_header
                }

        print("\n=== Service Discovery Results ===")
        for service, result in results.items():
            status = "✅" if result.get('accessible') else "❌"
            time_info = f" ({result.get('time', 0):.2f}s)" if result.get('time') else ""
            host_info = f" [Host: {result.get('host_header')}]" if result.get('host_header') else ""
            print(f"{status} {service}: {result.get('status', 'ERROR')}{time_info}{host_info}")

        # At least API should be accessible
        assert results['API Direct']['accessible'], "API must be accessible directly"
    
    @pytest.mark.slow
    def test_load_balancing_consistency(self, session):
        """Test load balancing consistency (multiple requests)"""
        url = 'http://localhost:8000/health'

        responses = []
        for i in range(5):
            try:
                response = session.get(url)
                responses.append({
                    'status': response.status_code,
                    'content': response.text.strip(),
                    'time': response.elapsed.total_seconds()
                })
                time.sleep(0.1)  # Small delay between requests
            except requests.exceptions.RequestException as e:
                responses.append({'error': str(e)})

        # All responses should be consistent
        successful_responses = [r for r in responses if 'error' not in r]
        assert len(successful_responses) >= 4, "Most requests should succeed"

        # Check consistency (ignoring timestamp differences)
        if successful_responses:
            # Parse JSON responses and check status consistency
            try:
                first_response = json.loads(successful_responses[0]['content'])
                first_status = first_response.get('status')
                first_service = first_response.get('service')

                for response in successful_responses:
                    parsed_response = json.loads(response['content'])
                    assert parsed_response.get('status') == first_status, \
                        "Load balanced responses should have consistent status"
                    assert parsed_response.get('service') == first_service, \
                        "Load balanced responses should have consistent service name"

                print(f"✅ Load balancing consistency verified: status={first_status}, service={first_service}")

            except json.JSONDecodeError:
                # Fallback to string comparison for non-JSON responses
                first_content = successful_responses[0]['content']
                for response in successful_responses:
                    assert response['content'] == first_content, \
                        "Load balanced responses should be consistent"

        # Check average response time
        times = [r['time'] for r in successful_responses if 'time' in r]
        if times:
            avg_time = sum(times) / len(times)
            print(f"Average response time: {avg_time:.3f}s")
            assert avg_time < 1.0, f"Average response time too high: {avg_time:.3f}s"
    
    def test_error_page_routing(self, session):
        """Test error page routing through Traefik"""
        # Test 404 pages on different services
        services = [
            'http://docs.turdparty.localhost/nonexistent',
            'http://frontend.turdparty.localhost/nonexistent',
            'http://localhost:8000/nonexistent'
        ]
        
        for url in services:
            try:
                response = session.get(url)
                # Should return 404
                assert response.status_code == 404, \
                    f"Non-existent page should return 404: {url}"
                print(f"404 handling OK: {url}")
            except requests.exceptions.RequestException as e:
                print(f"Error testing 404 for {url}: {e}")


if __name__ == "__main__":
    # Run tests with verbose output
    pytest.main([__file__, "-v", "--tb=short"])
