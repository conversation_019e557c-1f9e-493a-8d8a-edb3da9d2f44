"""
Fast VM Operations Integration Tests - Tier 1 (Lightweight)
Docker-only VM tests with pre-pulled images for rapid CI/CD feedback.
Target runtime: < 25 seconds
"""

import pytest
import time
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from typing import Dict, List, Any


class TestVMOperationsLightIntegration:
    """Fast, Docker-only VM operations tests for CI/CD pipelines."""

    @pytest.fixture(scope="class")
    def vm_config(self):
        """Lightweight VM test configuration."""
        return {
            "api_base_url": "http://localhost:8000/api/v1",
            "vm_timeout": 5,  # 5 seconds vs 2 minutes
            "operation_timeout": 3,  # 3 seconds
            "pre_pulled_images": [
                "alpine:latest",
                "ubuntu:latest",
                "busybox:latest"
            ],
            "max_concurrent_vms": 3,
            "min_memory_mb": 64,
            "max_memory_mb": 256
        }

    def test_01_vm_lifecycle_basic_light(self, vm_config):
        """Test basic VM lifecycle with Docker containers."""
        print("🔄 Testing VM lifecycle basic (light)...")
        
        mock_vm_id = f"light-vm-{int(time.time())}"
        
        with patch('requests.post') as mock_post, patch('requests.get') as mock_get, patch('requests.delete') as mock_delete:
            # Mock VM creation
            mock_post.return_value.status_code = 201
            mock_post.return_value.json.return_value = {
                "vm_id": mock_vm_id,
                "vm_type": "docker",
                "template": "alpine:latest",
                "status": "creating",
                "memory_mb": 128,
                "cpus": 1
            }
            
            # Mock VM status progression
            status_sequence = [
                {"status": "creating", "progress": 0},
                {"status": "running", "progress": 100, "ip_address": "**********", "container_id": "abc123"}
            ]
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.side_effect = status_sequence
            
            # Mock VM deletion
            mock_delete.return_value.status_code = 200
            mock_delete.return_value.json.return_value = {"status": "deleted"}
            
            import requests
            
            # Test VM creation
            vm_data = {
                "name": f"light-test-{int(time.time())}",
                "template": "alpine:latest",
                "vm_type": "docker",
                "memory_mb": 128,
                "cpus": 1,
                "domain": "TurdParty"
            }
            
            start_time = time.time()
            response = requests.post(f"{vm_config['api_base_url']}/vms/", json=vm_data)
            creation_time = time.time() - start_time
            
            assert response.status_code == 201
            assert creation_time < vm_config["vm_timeout"]
            
            vm_info = response.json()
            assert vm_info["vm_id"] == mock_vm_id
            assert vm_info["vm_type"] == "docker"
            
            # Test VM status check
            response = requests.get(f"{vm_config['api_base_url']}/vms/{mock_vm_id}")
            assert response.status_code == 200
            
            # Test VM deletion
            response = requests.delete(f"{vm_config['api_base_url']}/vms/{mock_vm_id}")
            assert response.status_code == 200
            
        print(f"✅ VM lifecycle basic (light) passed: {mock_vm_id} in {creation_time:.3f}s")

    def test_02_vm_template_validation_light(self, vm_config):
        """Test VM template validation with pre-pulled images."""
        print("🖼️ Testing VM template validation (light)...")
        
        with patch('requests.post') as mock_post, patch('requests.get') as mock_get:
            # Mock template validation
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = {
                "templates": vm_config["pre_pulled_images"],
                "available": True
            }
            
            # Mock VM creation with different templates
            mock_post.return_value.status_code = 201
            mock_post.return_value.json.side_effect = [
                {"vm_id": f"alpine-vm-{i}", "template": template}
                for i, template in enumerate(vm_config["pre_pulled_images"])
            ]
            
            import requests
            
            # Test template availability
            response = requests.get(f"{vm_config['api_base_url']}/templates/")
            assert response.status_code == 200
            
            templates_info = response.json()
            available_templates = templates_info["templates"]
            
            # Test VM creation with each template
            created_vms = []
            for template in vm_config["pre_pulled_images"]:
                assert template in available_templates
                
                vm_data = {
                    "name": f"template-test-{template.replace(':', '-')}",
                    "template": template,
                    "vm_type": "docker",
                    "memory_mb": vm_config["min_memory_mb"],
                    "cpus": 1
                }
                
                response = requests.post(f"{vm_config['api_base_url']}/vms/", json=vm_data)
                assert response.status_code == 201
                
                vm_info = response.json()
                assert vm_info["template"] == template
                created_vms.append(vm_info["vm_id"])
                
        print(f"✅ VM template validation (light) passed: {len(created_vms)} templates tested")

    def test_03_vm_resource_limits_light(self, vm_config):
        """Test VM resource limits with lightweight configurations."""
        print("⚙️ Testing VM resource limits (light)...")
        
        resource_configs = [
            {"memory_mb": vm_config["min_memory_mb"], "cpus": 1},
            {"memory_mb": 128, "cpus": 1},
            {"memory_mb": vm_config["max_memory_mb"], "cpus": 2}
        ]
        
        with patch('requests.post') as mock_post, patch('requests.get') as mock_get:
            # Mock VM creation with resource limits
            mock_post.return_value.status_code = 201
            mock_post.return_value.json.side_effect = [
                {
                    "vm_id": f"resource-vm-{i}",
                    "memory_mb": config["memory_mb"],
                    "cpus": config["cpus"],
                    "resource_limits": config
                }
                for i, config in enumerate(resource_configs)
            ]
            
            # Mock resource monitoring
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = {
                "memory_usage": 45.2,  # MB
                "cpu_usage": 12.5,     # %
                "status": "running"
            }
            
            import requests
            
            # Test different resource configurations
            for i, config in enumerate(resource_configs):
                vm_data = {
                    "name": f"resource-test-{i}",
                    "template": "alpine:latest",
                    "vm_type": "docker",
                    "memory_mb": config["memory_mb"],
                    "cpus": config["cpus"]
                }
                
                response = requests.post(f"{vm_config['api_base_url']}/vms/", json=vm_data)
                assert response.status_code == 201
                
                vm_info = response.json()
                assert vm_info["memory_mb"] == config["memory_mb"]
                assert vm_info["cpus"] == config["cpus"]
                
                # Test resource monitoring
                vm_id = vm_info["vm_id"]
                response = requests.get(f"{vm_config['api_base_url']}/vms/{vm_id}/metrics")
                assert response.status_code == 200
                
                metrics = response.json()
                assert "memory_usage" in metrics
                assert "cpu_usage" in metrics
                
        print(f"✅ VM resource limits (light) passed: {len(resource_configs)} configurations tested")

    def test_04_vm_command_execution_light(self, vm_config):
        """Test VM command execution with mocked operations."""
        print("💻 Testing VM command execution (light)...")
        
        test_commands = [
            {"command": "echo 'Hello World'", "expected_output": "Hello World"},
            {"command": "pwd", "expected_output": "/"},
            {"command": "whoami", "expected_output": "root"},
            {"command": "uname -s", "expected_output": "Linux"}
        ]
        
        with patch('requests.post') as mock_post:
            # Mock command execution
            mock_post.return_value.status_code = 200
            mock_post.return_value.json.side_effect = [
                {
                    "execution_id": f"exec-{i}",
                    "command": cmd["command"],
                    "status": "completed",
                    "exit_code": 0,
                    "output": cmd["expected_output"],
                    "duration": 0.1
                }
                for i, cmd in enumerate(test_commands)
            ]
            
            import requests
            
            # Test command execution
            vm_id = "light-vm-test"
            execution_results = []
            
            for cmd in test_commands:
                exec_data = {
                    "command": cmd["command"],
                    "timeout": vm_config["operation_timeout"]
                }
                
                start_time = time.time()
                response = requests.post(f"{vm_config['api_base_url']}/vms/{vm_id}/execute", json=exec_data)
                exec_time = time.time() - start_time
                
                assert response.status_code == 200
                assert exec_time < vm_config["operation_timeout"]
                
                result = response.json()
                assert result["exit_code"] == 0
                assert result["output"] == cmd["expected_output"]
                
                execution_results.append({
                    "command": cmd["command"],
                    "duration": exec_time,
                    "success": True
                })
                
        avg_exec_time = sum(r["duration"] for r in execution_results) / len(execution_results)
        
        print(f"✅ VM command execution (light) passed: {len(test_commands)} commands, avg={avg_exec_time:.3f}s")

    def test_05_vm_file_operations_light(self, vm_config):
        """Test VM file operations with small test files."""
        print("📁 Testing VM file operations (light)...")
        
        file_operations = [
            {
                "operation": "upload",
                "file_path": "/tmp/test1.txt",
                "content": "Test file 1 content",
                "size": 18
            },
            {
                "operation": "download",
                "file_path": "/tmp/test1.txt",
                "expected_content": "Test file 1 content"
            },
            {
                "operation": "list",
                "directory": "/tmp",
                "expected_files": ["test1.txt"]
            },
            {
                "operation": "delete",
                "file_path": "/tmp/test1.txt"
            }
        ]
        
        with patch('requests.post') as mock_post, patch('requests.get') as mock_get, patch('requests.delete') as mock_delete:
            # Mock file operations
            mock_responses = {
                "upload": (mock_post, 201, {"file_path": "/tmp/test1.txt", "size": 18, "status": "uploaded"}),
                "download": (mock_get, 200, {"content": "Test file 1 content", "size": 18}),
                "list": (mock_get, 200, {"files": ["test1.txt"], "directory": "/tmp"}),
                "delete": (mock_delete, 200, {"status": "deleted", "file_path": "/tmp/test1.txt"})
            }
            
            import requests
            
            vm_id = "light-vm-files"
            operation_times = {}
            
            for op in file_operations:
                operation_type = op["operation"]
                mock_method, status_code, response_data = mock_responses[operation_type]
                
                mock_method.return_value.status_code = status_code
                mock_method.return_value.json.return_value = response_data
                
                start_time = time.time()
                
                if operation_type == "upload":
                    files = {"file": ("test1.txt", op["content"])}
                    response = requests.post(f"{vm_config['api_base_url']}/vms/{vm_id}/files", files=files)
                elif operation_type == "download":
                    response = requests.get(f"{vm_config['api_base_url']}/vms/{vm_id}/files{op['file_path']}")
                elif operation_type == "list":
                    response = requests.get(f"{vm_config['api_base_url']}/vms/{vm_id}/files{op['directory']}")
                elif operation_type == "delete":
                    response = requests.delete(f"{vm_config['api_base_url']}/vms/{vm_id}/files{op['file_path']}")
                
                op_time = time.time() - start_time
                operation_times[operation_type] = op_time
                
                assert response.status_code == status_code
                assert op_time < vm_config["operation_timeout"]
                
                # Validate response content
                result = response.json()
                if operation_type == "download":
                    assert result["content"] == op["expected_content"]
                elif operation_type == "list":
                    assert op["expected_files"][0] in result["files"]
                    
        total_file_ops_time = sum(operation_times.values())
        
        print(f"✅ VM file operations (light) passed: {len(file_operations)} operations in {total_file_ops_time:.3f}s")

    def test_06_vm_networking_light(self, vm_config):
        """Test VM networking with mocked network operations."""
        print("🌐 Testing VM networking (light)...")
        
        network_tests = [
            {"test": "connectivity", "target": "*******", "expected": "reachable"},
            {"test": "dns_resolution", "hostname": "google.com", "expected": "resolved"},
            {"test": "port_scan", "port": 80, "expected": "open"},
            {"test": "bandwidth", "expected_mbps": 100}
        ]
        
        with patch('requests.post') as mock_post:
            # Mock network tests
            mock_post.return_value.status_code = 200
            mock_post.return_value.json.side_effect = [
                {
                    "test_type": test["test"],
                    "result": test.get("expected", "success"),
                    "duration": 0.05,
                    "status": "passed"
                }
                for test in network_tests
            ]
            
            import requests
            
            vm_id = "light-vm-network"
            network_results = []
            
            for test in network_tests:
                test_data = {
                    "test_type": test["test"],
                    "timeout": vm_config["operation_timeout"]
                }
                
                if "target" in test:
                    test_data["target"] = test["target"]
                if "hostname" in test:
                    test_data["hostname"] = test["hostname"]
                if "port" in test:
                    test_data["port"] = test["port"]
                    
                start_time = time.time()
                response = requests.post(f"{vm_config['api_base_url']}/vms/{vm_id}/network/test", json=test_data)
                test_time = time.time() - start_time
                
                assert response.status_code == 200
                assert test_time < vm_config["operation_timeout"]
                
                result = response.json()
                assert result["status"] == "passed"
                
                network_results.append({
                    "test": test["test"],
                    "duration": test_time,
                    "success": True
                })
                
        avg_network_time = sum(r["duration"] for r in network_results) / len(network_results)
        
        print(f"✅ VM networking (light) passed: {len(network_tests)} tests, avg={avg_network_time:.3f}s")

    def test_07_vm_concurrent_operations_light(self, vm_config):
        """Test concurrent VM operations with lightweight load."""
        print("🔀 Testing VM concurrent operations (light)...")
        
        concurrent_vms = vm_config["max_concurrent_vms"]
        
        with patch('requests.post') as mock_post, patch('requests.get') as mock_get:
            # Mock concurrent VM creation
            mock_post.return_value.status_code = 201
            mock_post.return_value.json.side_effect = [
                {"vm_id": f"concurrent-{i}", "status": "creating"}
                for i in range(concurrent_vms)
            ]
            
            # Mock VM status checks
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = {"status": "running"}
            
            import requests
            from concurrent.futures import ThreadPoolExecutor, as_completed
            
            def create_vm(vm_index):
                """Create a single VM."""
                vm_data = {
                    "name": f"concurrent-light-{vm_index}",
                    "template": "alpine:latest",
                    "vm_type": "docker",
                    "memory_mb": vm_config["min_memory_mb"],
                    "cpus": 1
                }
                
                start_time = time.time()
                response = requests.post(f"{vm_config['api_base_url']}/vms/", json=vm_data)
                creation_time = time.time() - start_time
                
                return {
                    "vm_index": vm_index,
                    "vm_id": response.json()["vm_id"],
                    "creation_time": creation_time,
                    "status_code": response.status_code
                }
            
            # Test concurrent VM creation
            start_time = time.time()
            results = []
            
            with ThreadPoolExecutor(max_workers=concurrent_vms) as executor:
                futures = [executor.submit(create_vm, i) for i in range(concurrent_vms)]
                
                for future in as_completed(futures):
                    result = future.result()
                    results.append(result)
                    
            total_concurrent_time = time.time() - start_time
            
            # Validate concurrent operations
            assert len(results) == concurrent_vms
            assert total_concurrent_time < 10.0, f"Concurrent operations took {total_concurrent_time:.2f}s, expected < 10s"
            
            # Check all VMs were created successfully
            successful_creations = [r for r in results if r["status_code"] == 201]
            assert len(successful_creations) == concurrent_vms
            
            avg_creation_time = sum(r["creation_time"] for r in results) / len(results)
            
        print(f"✅ VM concurrent operations (light) passed: {concurrent_vms} VMs in {total_concurrent_time:.2f}s, avg={avg_creation_time:.3f}s")

    def test_08_vm_error_handling_light(self, vm_config):
        """Test VM error handling scenarios."""
        print("⚠️ Testing VM error handling (light)...")
        
        error_scenarios = [
            {"scenario": "invalid_template", "status_code": 400, "error": "Template not found"},
            {"scenario": "insufficient_memory", "status_code": 400, "error": "Insufficient memory"},
            {"scenario": "vm_not_found", "status_code": 404, "error": "VM not found"},
            {"scenario": "operation_timeout", "status_code": 408, "error": "Operation timeout"}
        ]
        
        with patch('requests.post') as mock_post, patch('requests.get') as mock_get:
            import requests
            
            for scenario in error_scenarios:
                # Mock error response
                mock_post.return_value.status_code = scenario["status_code"]
                mock_post.return_value.json.return_value = {"error": scenario["error"]}
                mock_get.return_value.status_code = scenario["status_code"]
                mock_get.return_value.json.return_value = {"error": scenario["error"]}
                
                if scenario["scenario"] == "invalid_template":
                    vm_data = {"name": "error-test", "template": "nonexistent:latest", "vm_type": "docker"}
                    response = requests.post(f"{vm_config['api_base_url']}/vms/", json=vm_data)
                elif scenario["scenario"] == "insufficient_memory":
                    vm_data = {"name": "error-test", "template": "alpine:latest", "memory_mb": 999999}
                    response = requests.post(f"{vm_config['api_base_url']}/vms/", json=vm_data)
                elif scenario["scenario"] == "vm_not_found":
                    response = requests.get(f"{vm_config['api_base_url']}/vms/nonexistent-vm")
                elif scenario["scenario"] == "operation_timeout":
                    response = requests.post(f"{vm_config['api_base_url']}/vms/test-vm/execute", json={"command": "sleep 999"})
                
                assert response.status_code == scenario["status_code"]
                
                error_info = response.json()
                assert "error" in error_info
                assert scenario["error"] in error_info["error"]
                
        print(f"✅ VM error handling (light) passed: {len(error_scenarios)} scenarios tested")


if __name__ == "__main__":
    # Run fast VM operations tests
    pytest.main([__file__, "-v", "--tb=short", "--disable-warnings"])
