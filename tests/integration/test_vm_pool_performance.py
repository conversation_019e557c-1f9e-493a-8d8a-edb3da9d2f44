#!/usr/bin/env python3
"""
📈 Enhanced VM Pool Performance Benchmarking Suite

Comprehensive performance testing and benchmarking for the enhanced VM provisioning
queue system with detailed metrics collection and analysis.

Features:
- Load testing with concurrent requests
- Allocation time benchmarking
- Queue processing performance
- Elasticsearch logging performance
- Resource utilization monitoring
- Performance regression detection
"""

import asyncio
import json
import time
import statistics
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any
import sys

import httpx
import psutil
from concurrent.futures import ThreadPoolExecutor

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.service_urls import ServiceURLManager


class VMPoolPerformanceBenchmark:
    """Performance benchmarking suite for enhanced VM pools."""
    
    def __init__(self):
        self.url_manager = ServiceURLManager('local')
        self.api_base = self.url_manager.get_service_url('api')
        self.elasticsearch_url = self.url_manager.get_service_url('elasticsearch')
        
        self.benchmark_session = f"perf-{int(time.time())}"
        self.results = {
            "session_id": self.benchmark_session,
            "start_time": datetime.now().isoformat(),
            "benchmarks": {},
            "system_metrics": {},
            "summary": {}
        }
        
        print(f"📈 VM Pool Performance Benchmark Suite")
        print(f"   Session ID: {self.benchmark_session}")
        print(f"   API Base: {self.api_base}")
    
    async def benchmark_api_response_times(self, iterations: int = 100) -> Dict[str, Any]:
        """Benchmark API response times."""
        print(f"\n⏱️ Benchmarking API Response Times ({iterations} iterations)...")
        
        response_times = []
        errors = 0
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            for i in range(iterations):
                start_time = time.time()
                try:
                    response = await client.get(f"{self.api_base}/api/v1/pools/status")
                    response_time = time.time() - start_time
                    
                    if response.status_code == 200:
                        response_times.append(response_time)
                    else:
                        errors += 1
                        
                except Exception:
                    errors += 1
                
                if (i + 1) % 20 == 0:
                    print(f"   Progress: {i + 1}/{iterations}")
        
        if response_times:
            results = {
                "iterations": iterations,
                "successful_requests": len(response_times),
                "errors": errors,
                "min_time": min(response_times),
                "max_time": max(response_times),
                "mean_time": statistics.mean(response_times),
                "median_time": statistics.median(response_times),
                "p95_time": self._percentile(response_times, 95),
                "p99_time": self._percentile(response_times, 99),
                "std_dev": statistics.stdev(response_times) if len(response_times) > 1 else 0
            }
            
            print(f"   ✅ API Response Times:")
            print(f"      Mean: {results['mean_time']:.3f}s")
            print(f"      Median: {results['median_time']:.3f}s")
            print(f"      P95: {results['p95_time']:.3f}s")
            print(f"      P99: {results['p99_time']:.3f}s")
            print(f"      Errors: {errors}/{iterations}")
            
            return results
        else:
            return {"error": "No successful requests"}
    
    async def benchmark_concurrent_load(self, concurrent_users: int = 20, requests_per_user: int = 10) -> Dict[str, Any]:
        """Benchmark concurrent load handling."""
        print(f"\n🚀 Benchmarking Concurrent Load ({concurrent_users} users, {requests_per_user} requests each)...")
        
        async def user_session(user_id: int):
            """Simulate a user session with multiple requests."""
            session_times = []
            errors = 0
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                for req_id in range(requests_per_user):
                    start_time = time.time()
                    try:
                        response = await client.get(f"{self.api_base}/api/v1/pools/status")
                        request_time = time.time() - start_time
                        
                        if response.status_code == 200:
                            session_times.append(request_time)
                        else:
                            errors += 1
                    except Exception:
                        errors += 1
            
            return {
                "user_id": user_id,
                "response_times": session_times,
                "errors": errors
            }
        
        # Run concurrent user sessions
        start_time = time.time()
        tasks = [user_session(i) for i in range(concurrent_users)]
        user_results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # Aggregate results
        all_response_times = []
        total_errors = 0
        total_requests = 0
        
        for user_result in user_results:
            all_response_times.extend(user_result["response_times"])
            total_errors += user_result["errors"]
            total_requests += len(user_result["response_times"]) + user_result["errors"]
        
        if all_response_times:
            results = {
                "concurrent_users": concurrent_users,
                "requests_per_user": requests_per_user,
                "total_requests": total_requests,
                "successful_requests": len(all_response_times),
                "total_errors": total_errors,
                "total_time": total_time,
                "requests_per_second": len(all_response_times) / total_time,
                "mean_response_time": statistics.mean(all_response_times),
                "median_response_time": statistics.median(all_response_times),
                "p95_response_time": self._percentile(all_response_times, 95),
                "p99_response_time": self._percentile(all_response_times, 99)
            }
            
            print(f"   ✅ Concurrent Load Results:")
            print(f"      Requests/sec: {results['requests_per_second']:.2f}")
            print(f"      Success rate: {(len(all_response_times)/total_requests)*100:.1f}%")
            print(f"      Mean response: {results['mean_response_time']:.3f}s")
            print(f"      P95 response: {results['p95_response_time']:.3f}s")
            
            return results
        else:
            return {"error": "No successful requests in concurrent load test"}
    
    async def benchmark_allocation_performance(self, allocation_count: int = 10) -> Dict[str, Any]:
        """Benchmark VM allocation performance."""
        print(f"\n🏗️ Benchmarking VM Allocation Performance ({allocation_count} allocations)...")
        
        allocation_times = []
        successful_allocations = 0
        timeouts = 0
        errors = 0
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            for i in range(allocation_count):
                allocation_request = {
                    "priority": 3,
                    "requester_id": f"perf-test-{self.benchmark_session}-{i}",
                    "timeout_seconds": 30,
                    "metadata": {
                        "performance_test": True,
                        "allocation_id": i,
                        "session": self.benchmark_session
                    }
                }
                
                start_time = time.time()
                try:
                    response = await client.post(
                        f"{self.api_base}/api/v1/pools/ubuntu:20.04/allocate",
                        json=allocation_request
                    )
                    allocation_time = time.time() - start_time
                    
                    if response.status_code in [200, 201]:
                        allocation_times.append(allocation_time)
                        successful_allocations += 1
                    elif response.status_code in [408, 504]:
                        timeouts += 1
                    else:
                        errors += 1
                        
                except Exception:
                    errors += 1
                
                print(f"   Progress: {i + 1}/{allocation_count}")
        
        if allocation_times:
            results = {
                "allocation_count": allocation_count,
                "successful_allocations": successful_allocations,
                "timeouts": timeouts,
                "errors": errors,
                "min_allocation_time": min(allocation_times),
                "max_allocation_time": max(allocation_times),
                "mean_allocation_time": statistics.mean(allocation_times),
                "median_allocation_time": statistics.median(allocation_times),
                "p95_allocation_time": self._percentile(allocation_times, 95),
                "success_rate": (successful_allocations / allocation_count) * 100
            }
            
            print(f"   ✅ Allocation Performance:")
            print(f"      Success rate: {results['success_rate']:.1f}%")
            print(f"      Mean time: {results['mean_allocation_time']:.2f}s")
            print(f"      Median time: {results['median_allocation_time']:.2f}s")
            print(f"      P95 time: {results['p95_allocation_time']:.2f}s")
            
            return results
        else:
            return {
                "error": "No successful allocations",
                "timeouts": timeouts,
                "errors": errors
            }
    
    async def benchmark_elasticsearch_performance(self, log_queries: int = 50) -> Dict[str, Any]:
        """Benchmark Elasticsearch logging performance."""
        print(f"\n📊 Benchmarking Elasticsearch Performance ({log_queries} queries)...")
        
        query_times = []
        successful_queries = 0
        errors = 0
        
        today = datetime.now().strftime('%Y.%m.%d')
        index_name = f"ecs-turdparty-celery-{today}"
        
        search_query = {
            "query": {
                "range": {
                    "@timestamp": {
                        "gte": "now-1h"
                    }
                }
            },
            "size": 10,
            "sort": [{"@timestamp": {"order": "desc"}}]
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            for i in range(log_queries):
                start_time = time.time()
                try:
                    response = await client.post(
                        f"{self.elasticsearch_url}/{index_name}/_search",
                        json=search_query
                    )
                    query_time = time.time() - start_time
                    
                    if response.status_code == 200:
                        query_times.append(query_time)
                        successful_queries += 1
                    else:
                        errors += 1
                        
                except Exception:
                    errors += 1
                
                if (i + 1) % 10 == 0:
                    print(f"   Progress: {i + 1}/{log_queries}")
        
        if query_times:
            results = {
                "log_queries": log_queries,
                "successful_queries": successful_queries,
                "errors": errors,
                "min_query_time": min(query_times),
                "max_query_time": max(query_times),
                "mean_query_time": statistics.mean(query_times),
                "median_query_time": statistics.median(query_times),
                "p95_query_time": self._percentile(query_times, 95),
                "queries_per_second": successful_queries / sum(query_times) if query_times else 0
            }
            
            print(f"   ✅ Elasticsearch Performance:")
            print(f"      Success rate: {(successful_queries/log_queries)*100:.1f}%")
            print(f"      Mean query: {results['mean_query_time']:.3f}s")
            print(f"      Queries/sec: {results['queries_per_second']:.2f}")
            
            return results
        else:
            return {"error": "No successful Elasticsearch queries"}
    
    def collect_system_metrics(self) -> Dict[str, Any]:
        """Collect system performance metrics."""
        print("\n🖥️ Collecting System Metrics...")
        
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            metrics = {
                "cpu_percent": cpu_percent,
                "memory_total_gb": memory.total / (1024**3),
                "memory_used_gb": memory.used / (1024**3),
                "memory_percent": memory.percent,
                "disk_total_gb": disk.total / (1024**3),
                "disk_used_gb": disk.used / (1024**3),
                "disk_percent": (disk.used / disk.total) * 100,
                "timestamp": datetime.now().isoformat()
            }
            
            print(f"   ✅ System Metrics:")
            print(f"      CPU: {metrics['cpu_percent']:.1f}%")
            print(f"      Memory: {metrics['memory_percent']:.1f}%")
            print(f"      Disk: {metrics['disk_percent']:.1f}%")
            
            return metrics
        except Exception as e:
            return {"error": f"Failed to collect system metrics: {e}"}
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile of a dataset."""
        if not data:
            return 0.0
        sorted_data = sorted(data)
        index = (percentile / 100) * (len(sorted_data) - 1)
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            lower = sorted_data[int(index)]
            upper = sorted_data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))

    async def run_full_benchmark_suite(self) -> Dict[str, Any]:
        """Run the complete performance benchmark suite."""
        print(f"\n📈 Starting Enhanced VM Pool Performance Benchmark Suite")
        print(f"   Session ID: {self.benchmark_session}")
        print(f"   Timestamp: {datetime.now().isoformat()}")
        print("=" * 80)

        # Collect initial system metrics
        self.results["system_metrics"]["start"] = self.collect_system_metrics()

        # Run benchmarks
        benchmarks = [
            ("api_response_times", lambda: self.benchmark_api_response_times(100)),
            ("concurrent_load", lambda: self.benchmark_concurrent_load(10, 5)),
            ("allocation_performance", lambda: self.benchmark_allocation_performance(5)),
            ("elasticsearch_performance", lambda: self.benchmark_elasticsearch_performance(25))
        ]

        for benchmark_name, benchmark_func in benchmarks:
            try:
                print(f"\n{'='*20} {benchmark_name.upper()} {'='*20}")
                start_time = time.time()

                result = await benchmark_func()

                duration = time.time() - start_time
                self.results["benchmarks"][benchmark_name] = {
                    "result": result,
                    "duration": duration
                }

                print(f"\n   📊 {benchmark_name} completed in {duration:.2f}s")

            except Exception as e:
                print(f"\n   ❌ {benchmark_name} failed: {e}")
                self.results["benchmarks"][benchmark_name] = {
                    "error": str(e),
                    "duration": 0
                }

        # Collect final system metrics
        self.results["system_metrics"]["end"] = self.collect_system_metrics()

        # Generate summary
        self.results["end_time"] = datetime.now().isoformat()
        self.results["summary"] = self._generate_summary()

        # Print summary
        self._print_summary()

        return self.results

    def _generate_summary(self) -> Dict[str, Any]:
        """Generate performance summary."""
        summary = {
            "total_benchmarks": len(self.results["benchmarks"]),
            "successful_benchmarks": 0,
            "failed_benchmarks": 0,
            "performance_scores": {},
            "recommendations": []
        }

        for benchmark_name, benchmark_data in self.results["benchmarks"].items():
            if "error" in benchmark_data:
                summary["failed_benchmarks"] += 1
            else:
                summary["successful_benchmarks"] += 1

                # Calculate performance scores (0-100)
                result = benchmark_data["result"]

                if benchmark_name == "api_response_times" and "mean_time" in result:
                    # Good: <100ms, Excellent: <50ms
                    score = max(0, 100 - (result["mean_time"] * 1000))
                    summary["performance_scores"]["api_response"] = min(100, score)

                elif benchmark_name == "concurrent_load" and "requests_per_second" in result:
                    # Good: >50 req/s, Excellent: >100 req/s
                    score = min(100, (result["requests_per_second"] / 100) * 100)
                    summary["performance_scores"]["concurrent_load"] = score

                elif benchmark_name == "allocation_performance" and "success_rate" in result:
                    # Score based on success rate and speed
                    success_score = result["success_rate"]
                    speed_score = max(0, 100 - (result.get("mean_allocation_time", 30) * 3))
                    summary["performance_scores"]["allocation"] = (success_score + speed_score) / 2

                elif benchmark_name == "elasticsearch_performance" and "mean_query_time" in result:
                    # Good: <200ms, Excellent: <100ms
                    score = max(0, 100 - (result["mean_query_time"] * 500))
                    summary["performance_scores"]["elasticsearch"] = min(100, score)

        # Generate recommendations
        if summary["performance_scores"]:
            avg_score = sum(summary["performance_scores"].values()) / len(summary["performance_scores"])
            summary["overall_score"] = avg_score

            if avg_score >= 80:
                summary["recommendations"].append("✅ Excellent performance across all metrics")
            elif avg_score >= 60:
                summary["recommendations"].append("⚠️ Good performance with room for optimization")
            else:
                summary["recommendations"].append("❌ Performance issues detected - optimization needed")

            # Specific recommendations
            for metric, score in summary["performance_scores"].items():
                if score < 60:
                    summary["recommendations"].append(f"🔧 Optimize {metric} performance (score: {score:.1f})")

        return summary

    def _print_summary(self):
        """Print performance benchmark summary."""
        print("\n" + "="*80)
        print("🎯 ENHANCED VM POOL PERFORMANCE BENCHMARK SUMMARY")
        print("="*80)

        summary = self.results["summary"]

        print(f"Session ID: {self.benchmark_session}")
        print(f"Total Benchmarks: {summary['total_benchmarks']}")
        print(f"Successful: {summary['successful_benchmarks']}")
        print(f"Failed: {summary['failed_benchmarks']}")

        if summary["performance_scores"]:
            print(f"\n📊 Performance Scores (0-100):")
            for metric, score in summary["performance_scores"].items():
                status = "🟢" if score >= 80 else "🟡" if score >= 60 else "🔴"
                print(f"   {status} {metric}: {score:.1f}")

            if "overall_score" in summary:
                overall_status = "🟢" if summary["overall_score"] >= 80 else "🟡" if summary["overall_score"] >= 60 else "🔴"
                print(f"\n{overall_status} Overall Score: {summary['overall_score']:.1f}")

        if summary["recommendations"]:
            print(f"\n💡 Recommendations:")
            for rec in summary["recommendations"]:
                print(f"   {rec}")

    def save_results(self, filename: str = None):
        """Save benchmark results to file."""
        if not filename:
            filename = f"vm_pool_benchmark_{self.benchmark_session}.json"

        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)

        print(f"\n📄 Benchmark results saved to: {filename}")


async def main():
    """Main benchmark runner."""
    benchmark = VMPoolPerformanceBenchmark()
    results = await benchmark.run_full_benchmark_suite()
    benchmark.save_results()

    return results


if __name__ == "__main__":
    asyncio.run(main())
