"""
Enhanced VM Queue Management Testing

Tests VM pool allocation, queue management, spawning workflows, and injection
processes with comprehensive validation and performance metrics.
"""

import pytest
import asyncio
import time
import json
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, List

# Import VM management components
from services.workers.tasks.basic_vm_availability_manager import (
    availability_manager, maintain_vm_pools, allocate_vm_immediately, get_pool_status
)

# Import API models for validation
from services.api.src.models.vm_instance import VMStatus


class TestVMQueueManagement:
    """Comprehensive VM queue management tests."""
    
    def test_pool_status_comprehensive(self):
        """Test comprehensive pool status retrieval and validation."""
        status = availability_manager.get_pool_status()
        
        assert isinstance(status, dict), "Pool status should be a dictionary"
        assert len(status) > 0, "Pool status should not be empty"
        
        # Get actual available templates from status
        available_templates = list(status.keys())
        print(f"Available templates: {available_templates}")

        # Validate that we have at least some templates
        assert len(available_templates) >= 2, "Should have at least 2 VM templates configured"
        
        for template in available_templates:
            assert template in status, f"Template {template} missing from pool status"
            
            pool_info = status[template]
            
            # Validate pool structure
            required_fields = [
                "ready", "total", "creating", "running", "terminated",
                "min_ready", "max_total", "needs_provisioning", "can_provision"
            ]
            
            for field in required_fields:
                assert field in pool_info, f"Field {field} missing from {template} pool info"
                
            # Validate field types and constraints
            assert isinstance(pool_info["ready"], int), f"{template}: ready should be int"
            assert isinstance(pool_info["total"], int), f"{template}: total should be int"
            assert pool_info["ready"] >= 0, f"{template}: ready should be non-negative"
            assert pool_info["total"] >= 0, f"{template}: total should be non-negative"
            assert pool_info["ready"] <= pool_info["total"], f"{template}: ready should not exceed total"
            assert pool_info["min_ready"] > 0, f"{template}: min_ready should be positive"
            assert pool_info["max_total"] >= pool_info["min_ready"], f"{template}: max_total should be >= min_ready"
            
        print(f"✅ Pool status validation passed for {len(status)} templates")
        return status
    
    def test_pool_maintenance_execution(self):
        """Test pool maintenance task execution and results."""
        print("🔧 Testing pool maintenance execution...")
        
        # Get initial status
        initial_status = availability_manager.get_pool_status()
        
        # Execute maintenance task
        start_time = time.time()
        result = maintain_vm_pools.delay()
        maintenance_result = result.get(timeout=120)  # 2 minutes timeout
        execution_time = time.time() - start_time
        
        # Validate maintenance result structure
        assert maintenance_result is not None, "Maintenance result should not be None"
        assert isinstance(maintenance_result, dict), "Maintenance result should be a dictionary"
        
        required_fields = ["maintenance_completed", "actions_taken", "pool_status", "duration_ms"]
        for field in required_fields:
            assert field in maintenance_result, f"Field {field} missing from maintenance result"
        
        assert maintenance_result["maintenance_completed"] is True, "Maintenance should complete successfully"
        assert isinstance(maintenance_result["actions_taken"], list), "Actions taken should be a list"
        assert isinstance(maintenance_result["pool_status"], dict), "Pool status should be a dictionary"
        assert maintenance_result["duration_ms"] > 0, "Duration should be positive"
        
        # Validate actions structure
        for action in maintenance_result["actions_taken"]:
            assert isinstance(action, dict), "Each action should be a dictionary"
            assert "action" in action, "Action should have 'action' field"
            assert "template" in action, "Action should have 'template' field"
            assert "vm_id" in action, "Action should have 'vm_id' field"
            assert action["action"] == "provision", "Action type should be 'provision'"
        
        print(f"✅ Pool maintenance completed in {execution_time:.2f}s")
        print(f"   Actions taken: {len(maintenance_result['actions_taken'])}")
        print(f"   Maintenance duration: {maintenance_result['duration_ms']}ms")
        
        return maintenance_result
    
    def test_vm_allocation_workflow(self):
        """Test VM allocation workflow with different templates."""
        print("🚀 Testing VM allocation workflow...")
        
        # Get available templates and test the first one (usually ubuntu:20.04)
        available_templates = list(availability_manager.get_pool_status().keys())
        templates_to_test = [available_templates[0]] if available_templates else []
        allocation_results = {}
        
        for template in templates_to_test:
            print(f"   Testing allocation for {template}...")
            
            # Ensure pool maintenance has run
            maintain_vm_pools.delay().get(timeout=60)
            time.sleep(5)  # Allow time for VMs to be created
            
            # Attempt allocation
            start_time = time.time()
            result = allocate_vm_immediately.delay(template, f"test_user_{uuid.uuid4().hex[:8]}")
            allocation_result = result.get(timeout=60)
            allocation_time = time.time() - start_time
            
            allocation_results[template] = {
                "result": allocation_result,
                "allocation_time": allocation_time
            }
            
            # Validate allocation result
            assert allocation_result is not None, f"Allocation result for {template} should not be None"
            assert isinstance(allocation_result, dict), f"Allocation result for {template} should be a dictionary"
            assert "success" in allocation_result, f"Allocation result for {template} should have 'success' field"
            assert "message" in allocation_result, f"Allocation result for {template} should have 'message' field"
            
            if allocation_result["success"]:
                # Immediate allocation succeeded
                assert "vm_id" in allocation_result, f"Successful allocation for {template} should have vm_id"
                assert "vm" in allocation_result, f"Successful allocation for {template} should have vm details"
                assert "allocated_at" in allocation_result, f"Successful allocation for {template} should have allocated_at"
                
                vm_details = allocation_result["vm"]
                assert vm_details["template"] == template, f"VM template should match requested template"
                assert vm_details["status"] in ["running", "ready"], f"VM should be in running or ready state"
                
                print(f"   ✅ {template}: Immediate allocation succeeded in {allocation_time:.2f}s")
                print(f"      VM ID: {allocation_result['vm_id']}")
                print(f"      VM Status: {vm_details['status']}")
                
            else:
                # VM is being provisioned
                assert allocation_result.get("status") == "provisioning", f"Failed allocation should indicate provisioning"
                print(f"   ⏳ {template}: VM being provisioned, estimated wait: {allocation_result.get('estimated_wait_minutes', 'unknown')} minutes")
        
        return allocation_results
    
    def test_pool_performance_metrics(self):
        """Test pool performance and response times."""
        print("📊 Testing pool performance metrics...")
        
        # Test pool status query performance
        status_times = []
        for i in range(5):
            start_time = time.time()
            status = availability_manager.get_pool_status()
            query_time = time.time() - start_time
            status_times.append(query_time)
            assert len(status) > 0, f"Pool status query {i+1} should return data"
        
        avg_status_time = sum(status_times) / len(status_times)
        max_status_time = max(status_times)
        
        # Performance assertions
        assert avg_status_time < 2.0, f"Average pool status query time ({avg_status_time:.3f}s) should be < 2s"
        assert max_status_time < 5.0, f"Maximum pool status query time ({max_status_time:.3f}s) should be < 5s"
        
        # Test pool status task performance
        task_times = []
        for i in range(3):
            start_time = time.time()
            result = get_pool_status.delay()
            status = result.get(timeout=30)
            task_time = time.time() - start_time
            task_times.append(task_time)
            assert isinstance(status, dict), f"Pool status task {i+1} should return dictionary"
            assert len(status) > 0, f"Pool status task {i+1} should return data"
        
        avg_task_time = sum(task_times) / len(task_times)
        max_task_time = max(task_times)
        
        # Task performance assertions
        assert avg_task_time < 10.0, f"Average pool status task time ({avg_task_time:.3f}s) should be < 10s"
        assert max_task_time < 30.0, f"Maximum pool status task time ({max_task_time:.3f}s) should be < 30s"
        
        print(f"✅ Pool performance metrics:")
        print(f"   Average status query time: {avg_status_time:.3f}s")
        print(f"   Maximum status query time: {max_status_time:.3f}s")
        print(f"   Average status task time: {avg_task_time:.3f}s")
        print(f"   Maximum status task time: {max_task_time:.3f}s")
        
        return {
            "status_query": {"avg": avg_status_time, "max": max_status_time},
            "status_task": {"avg": avg_task_time, "max": max_task_time}
        }
    
    def test_concurrent_allocations(self):
        """Test concurrent VM allocations to validate queue management."""
        print("🔄 Testing concurrent VM allocations...")
        
        # Use first available template
        available_templates = list(availability_manager.get_pool_status().keys())
        template = available_templates[0] if available_templates else "ubuntu:20.04"
        num_concurrent = 3
        
        # Ensure pool has VMs
        maintain_vm_pools.delay().get(timeout=60)
        time.sleep(10)
        
        # Launch concurrent allocations
        allocation_tasks = []
        start_time = time.time()
        
        for i in range(num_concurrent):
            task = allocate_vm_immediately.delay(template, f"concurrent_user_{i}")
            allocation_tasks.append(task)
        
        # Collect results
        results = []
        for i, task in enumerate(allocation_tasks):
            try:
                result = task.get(timeout=60)
                results.append({"index": i, "result": result, "success": True})
            except Exception as e:
                results.append({"index": i, "error": str(e), "success": False})
        
        total_time = time.time() - start_time
        
        # Analyze results
        successful_allocations = [r for r in results if r["success"] and r["result"].get("success")]
        failed_allocations = [r for r in results if not r["success"] or not r["result"].get("success")]
        
        print(f"✅ Concurrent allocation test completed in {total_time:.2f}s")
        print(f"   Successful allocations: {len(successful_allocations)}/{num_concurrent}")
        print(f"   Failed allocations: {len(failed_allocations)}/{num_concurrent}")
        
        # At least some allocations should succeed if pool has VMs
        # This is not a hard requirement as it depends on pool state
        for result in successful_allocations:
            allocation_result = result["result"]
            assert "vm_id" in allocation_result, "Successful allocation should have vm_id"
            assert "vm" in allocation_result, "Successful allocation should have vm details"
        
        return {
            "total_time": total_time,
            "successful": len(successful_allocations),
            "failed": len(failed_allocations),
            "results": results
        }
    
    def test_pool_configuration_validation(self):
        """Test pool configuration validation and constraints."""
        print("⚙️ Testing pool configuration validation...")
        
        configs = availability_manager.pool_configs
        
        # Validate configuration structure
        assert isinstance(configs, dict), "Pool configs should be a dictionary"
        assert len(configs) > 0, "Pool configs should not be empty"
        
        expected_templates = [
            "ubuntu:20.04",
            "ubuntu:22.04",
            "alpine:latest",
            "10Baht/windows10-turdparty"
        ]
        
        for template in expected_templates:
            assert template in configs, f"Template {template} should have configuration"
            
            config = configs[template]
            
            # Validate configuration fields
            assert hasattr(config, 'template'), f"{template}: config should have template field"
            assert hasattr(config, 'min_ready'), f"{template}: config should have min_ready field"
            assert hasattr(config, 'max_total'), f"{template}: config should have max_total field"
            assert hasattr(config, 'vm_type'), f"{template}: config should have vm_type field"
            assert hasattr(config, 'memory_mb'), f"{template}: config should have memory_mb field"
            assert hasattr(config, 'cpus'), f"{template}: config should have cpus field"
            
            # Validate configuration values
            assert config.template == template, f"{template}: template field should match key"
            assert config.min_ready > 0, f"{template}: min_ready should be positive"
            assert config.max_total >= config.min_ready, f"{template}: max_total should be >= min_ready"
            assert config.vm_type in ["docker", "vagrant"], f"{template}: vm_type should be docker or vagrant"
            assert config.memory_mb > 0, f"{template}: memory_mb should be positive"
            assert config.cpus > 0, f"{template}: cpus should be positive"
            
            print(f"   ✅ {template}: min_ready={config.min_ready}, max_total={config.max_total}, "
                  f"vm_type={config.vm_type}, memory={config.memory_mb}MB, cpus={config.cpus}")
        
        return configs


class TestVMQueueManagementIntegration:
    """Integration tests for VM queue management with API."""
    
    def test_api_integration_placeholder(self):
        """Placeholder for API integration tests."""
        # This would test the actual API endpoints for VM allocation
        # For now, we'll validate that the manager works correctly
        
        status = availability_manager.get_pool_status()
        assert isinstance(status, dict), "Pool status should be accessible"
        
        # In a full integration test, you would:
        # 1. Make HTTP requests to /api/v1/vm-allocation/allocate
        # 2. Verify response schemas and status codes
        # 3. Test error handling for invalid requests
        # 4. Test authentication and rate limiting
        # 5. Validate database state changes
        # 6. Test WebSocket connections for real-time updates
        
        print("✅ API integration placeholder completed")


if __name__ == "__main__":
    # Run tests manually for debugging
    print("🧪 Running VM Queue Management Tests...")
    
    test_instance = TestVMQueueManagement()
    
    try:
        # Run core tests
        print("\n1. Testing pool status...")
        status = test_instance.test_pool_status_comprehensive()
        
        print("\n2. Testing pool maintenance...")
        maintenance = test_instance.test_pool_maintenance_execution()
        
        print("\n3. Testing VM allocation...")
        allocations = test_instance.test_vm_allocation_workflow()
        
        print("\n4. Testing performance metrics...")
        performance = test_instance.test_pool_performance_metrics()
        
        print("\n5. Testing concurrent allocations...")
        concurrent = test_instance.test_concurrent_allocations()
        
        print("\n6. Testing configuration validation...")
        configs = test_instance.test_pool_configuration_validation()
        
        print("\n✅ All VM queue management tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
