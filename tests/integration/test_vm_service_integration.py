"""
VM Service Integration Tests for TurdParty.

This module provides comprehensive integration testing for VM lifecycle operations,
including creation, management, monitoring, and cleanup.
"""

import asyncio
import time
from unittest.mock import AsyncMock, MagicMock, patch
import uuid

import pytest


class TestVMServiceIntegration:
    """Integration tests for VM service operations."""
    
    @pytest.fixture
    def mock_vm_service(self):
        """Mock VM service with realistic behavior."""
        with patch('api.services.vm_service.VMService') as mock_service:
            # Mock VM creation
            mock_service.create_vm = AsyncMock(return_value={
                "vm_id": "test-vm-123",
                "name": "test-vm",
                "status": "creating",
                "vm_type": "docker",
                "created_at": "2025-06-14T15:30:00Z"
            })
            
            # Mock VM status checking
            mock_service.get_vm_status = AsyncMock(return_value={
                "vm_id": "test-vm-123",
                "status": "running",
                "ip_address": "**********",
                "uptime_seconds": 120
            })
            
            # Mock VM operations
            mock_service.start_vm = AsyncMock(return_value={"status": "starting"})
            mock_service.stop_vm = AsyncMock(return_value={"status": "stopping"})
            mock_service.delete_vm = AsyncMock(return_value={"status": "deleted"})
            
            # Mock VM listing
            mock_service.list_vms = AsyncMock(return_value={
                "vms": [
                    {
                        "vm_id": "test-vm-123",
                        "name": "test-vm",
                        "status": "running",
                        "vm_type": "docker"
                    }
                ],
                "total": 1
            })
            
            yield mock_service
    
    @pytest.mark.asyncio
    async def test_vm_creation_lifecycle(self, mock_vm_service):
        """Test complete VM creation lifecycle."""
        # Create VM
        vm_data = {
            "name": "integration-test-vm",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 512,
            "cpus": 1
        }
        
        result = await mock_vm_service.create_vm(vm_data)
        assert result["vm_id"] == "test-vm-123"
        assert result["status"] == "creating"
        
        # Check VM status
        status = await mock_vm_service.get_vm_status("test-vm-123")
        assert status["status"] == "running"
        assert "ip_address" in status
        
        # List VMs
        vm_list = await mock_vm_service.list_vms()
        assert vm_list["total"] == 1
        assert len(vm_list["vms"]) == 1
    
    @pytest.mark.asyncio
    async def test_vm_start_stop_operations(self, mock_vm_service):
        """Test VM start and stop operations."""
        vm_id = "test-vm-123"
        
        # Start VM
        start_result = await mock_vm_service.start_vm(vm_id)
        assert start_result["status"] == "starting"
        
        # Stop VM
        stop_result = await mock_vm_service.stop_vm(vm_id)
        assert stop_result["status"] == "stopping"
    
    @pytest.mark.asyncio
    async def test_vm_deletion(self, mock_vm_service):
        """Test VM deletion."""
        vm_id = "test-vm-123"
        
        # Delete VM
        delete_result = await mock_vm_service.delete_vm(vm_id)
        assert delete_result["status"] == "deleted"
    
    @pytest.mark.asyncio
    async def test_vm_error_handling(self, mock_vm_service):
        """Test VM service error handling."""
        # Mock error scenarios
        mock_vm_service.create_vm.side_effect = Exception("VM creation failed")
        
        with pytest.raises(Exception) as exc_info:
            await mock_vm_service.create_vm({"name": "failing-vm"})
        
        assert "VM creation failed" in str(exc_info.value)


class TestVMMetricsIntegration:
    """Integration tests for VM metrics collection."""
    
    @pytest.fixture
    def mock_metrics_service(self):
        """Mock VM metrics service."""
        with patch('api.services.vm_metrics_service.VMMetricsService') as mock_service:
            # Mock metrics collection
            mock_service.get_vm_metrics = AsyncMock(return_value={
                "vm_id": "test-vm-123",
                "timestamp": int(time.time() * 1000),
                "cpu_percent": 25.5,
                "memory_percent": 45.2,
                "memory_used_mb": 512.0,
                "network_rx_bytes": 1048576,
                "network_tx_bytes": 524288,
                "status": "running",
                "uptime_seconds": 3600
            })
            
            # Mock streaming metrics
            async def mock_stream():
                for i in range(5):
                    yield {
                        "vm_id": "test-vm-123",
                        "timestamp": int(time.time() * 1000) + i * 1000,
                        "cpu_percent": 25.5 + i,
                        "memory_percent": 45.2 + i,
                        "status": "running"
                    }
                    await asyncio.sleep(0.1)
            
            mock_service.stream_vm_metrics = AsyncMock(return_value=mock_stream())
            
            yield mock_service
    
    @pytest.mark.asyncio
    async def test_vm_metrics_collection(self, mock_metrics_service):
        """Test VM metrics collection."""
        vm_id = "test-vm-123"
        
        metrics = await mock_metrics_service.get_vm_metrics(vm_id)
        
        assert metrics["vm_id"] == vm_id
        assert "cpu_percent" in metrics
        assert "memory_percent" in metrics
        assert "timestamp" in metrics
        assert metrics["status"] == "running"
    
    @pytest.mark.asyncio
    async def test_vm_metrics_streaming(self, mock_metrics_service):
        """Test VM metrics streaming."""
        vm_id = "test-vm-123"
        
        metrics_stream = await mock_metrics_service.stream_vm_metrics(vm_id)
        collected_metrics = []
        
        async for metrics in metrics_stream:
            collected_metrics.append(metrics)
            if len(collected_metrics) >= 3:
                break
        
        assert len(collected_metrics) == 3
        assert all(m["vm_id"] == vm_id for m in collected_metrics)
        assert all("cpu_percent" in m for m in collected_metrics)


class TestVMFileOperations:
    """Integration tests for VM file operations."""
    
    @pytest.fixture
    def mock_file_service(self):
        """Mock VM file operations service."""
        with patch('api.services.vm_file_service.VMFileService') as mock_service:
            # Mock file upload
            mock_service.upload_file = AsyncMock(return_value={
                "file_id": str(uuid.uuid4()),
                "target_path": "/tmp/uploaded_file.txt",
                "status": "uploaded",
                "size_bytes": 1024
            })
            
            # Mock file download
            mock_service.download_file = AsyncMock(return_value={
                "file_id": str(uuid.uuid4()),
                "content": b"file content",
                "size_bytes": 12
            })
            
            # Mock file listing
            mock_service.list_files = AsyncMock(return_value={
                "files": [
                    {
                        "path": "/tmp/file1.txt",
                        "size_bytes": 1024,
                        "modified_at": "2025-06-14T15:30:00Z"
                    },
                    {
                        "path": "/tmp/file2.txt", 
                        "size_bytes": 2048,
                        "modified_at": "2025-06-14T15:31:00Z"
                    }
                ],
                "total": 2
            })
            
            yield mock_service
    
    @pytest.mark.asyncio
    async def test_vm_file_upload(self, mock_file_service):
        """Test file upload to VM."""
        vm_id = "test-vm-123"
        file_data = {
            "content": b"test file content",
            "target_path": "/tmp/test_file.txt",
            "permissions": "644"
        }
        
        result = await mock_file_service.upload_file(vm_id, file_data)
        
        assert "file_id" in result
        assert result["target_path"] == "/tmp/test_file.txt"
        assert result["status"] == "uploaded"
    
    @pytest.mark.asyncio
    async def test_vm_file_download(self, mock_file_service):
        """Test file download from VM."""
        vm_id = "test-vm-123"
        file_path = "/tmp/test_file.txt"
        
        result = await mock_file_service.download_file(vm_id, file_path)
        
        assert "content" in result
        assert "size_bytes" in result
        assert result["content"] == b"file content"
    
    @pytest.mark.asyncio
    async def test_vm_file_listing(self, mock_file_service):
        """Test listing files in VM."""
        vm_id = "test-vm-123"
        directory = "/tmp"
        
        result = await mock_file_service.list_files(vm_id, directory)
        
        assert "files" in result
        assert result["total"] == 2
        assert len(result["files"]) == 2
        assert all("path" in f for f in result["files"])


class TestVMCommandExecution:
    """Integration tests for VM command execution."""
    
    @pytest.fixture
    def mock_command_service(self):
        """Mock VM command execution service."""
        with patch('api.services.vm_command_service.VMCommandService') as mock_service:
            # Mock command execution
            mock_service.execute_command = AsyncMock(return_value={
                "command_id": str(uuid.uuid4()),
                "command": "echo 'hello world'",
                "exit_code": 0,
                "stdout": "hello world\n",
                "stderr": "",
                "execution_time_ms": 150
            })
            
            # Mock command status
            mock_service.get_command_status = AsyncMock(return_value={
                "command_id": str(uuid.uuid4()),
                "status": "completed",
                "exit_code": 0,
                "started_at": "2025-06-14T15:30:00Z",
                "completed_at": "2025-06-14T15:30:01Z"
            })
            
            yield mock_service
    
    @pytest.mark.asyncio
    async def test_vm_command_execution(self, mock_command_service):
        """Test command execution in VM."""
        vm_id = "test-vm-123"
        command_data = {
            "command": "echo 'hello world'",
            "working_directory": "/tmp",
            "timeout_seconds": 30
        }
        
        result = await mock_command_service.execute_command(vm_id, command_data)
        
        assert "command_id" in result
        assert result["exit_code"] == 0
        assert result["stdout"] == "hello world\n"
        assert result["stderr"] == ""
    
    @pytest.mark.asyncio
    async def test_vm_command_status_check(self, mock_command_service):
        """Test checking command execution status."""
        command_id = str(uuid.uuid4())
        
        status = await mock_command_service.get_command_status(command_id)
        
        assert status["status"] == "completed"
        assert status["exit_code"] == 0
        assert "started_at" in status
        assert "completed_at" in status


@pytest.mark.integration
class TestVMWorkflowIntegration:
    """Integration tests for complete VM workflows."""
    
    @pytest.mark.asyncio
    async def test_complete_vm_analysis_workflow(self):
        """Test complete VM analysis workflow."""
        with patch('api.services.vm_service.VMService') as mock_vm_service, \
             patch('api.services.vm_file_service.VMFileService') as mock_file_service, \
             patch('api.services.vm_command_service.VMCommandService') as mock_command_service, \
             patch('api.services.vm_metrics_service.VMMetricsService') as mock_metrics_service:
            
            # Setup mocks
            mock_vm_service.create_vm = AsyncMock(return_value={"vm_id": "workflow-vm-123"})
            mock_vm_service.start_vm = AsyncMock(return_value={"status": "running"})
            mock_file_service.upload_file = AsyncMock(return_value={"file_id": "file-123"})
            mock_command_service.execute_command = AsyncMock(return_value={"exit_code": 0})
            mock_metrics_service.get_vm_metrics = AsyncMock(return_value={"cpu_percent": 25.0})
            mock_vm_service.stop_vm = AsyncMock(return_value={"status": "stopped"})
            mock_vm_service.delete_vm = AsyncMock(return_value={"status": "deleted"})
            
            # 1. Create VM
            vm_result = await mock_vm_service.create_vm({
                "name": "workflow-test-vm",
                "template": "ubuntu:20.04"
            })
            vm_id = vm_result["vm_id"]
            
            # 2. Start VM
            await mock_vm_service.start_vm(vm_id)
            
            # 3. Upload file
            await mock_file_service.upload_file(vm_id, {
                "content": b"malware content",
                "target_path": "/tmp/malware.exe"
            })
            
            # 4. Execute analysis command
            await mock_command_service.execute_command(vm_id, {
                "command": "/tmp/malware.exe",
                "timeout_seconds": 300
            })
            
            # 5. Collect metrics
            await mock_metrics_service.get_vm_metrics(vm_id)
            
            # 6. Stop VM
            await mock_vm_service.stop_vm(vm_id)
            
            # 7. Delete VM
            await mock_vm_service.delete_vm(vm_id)
            
            # Verify all operations were called
            mock_vm_service.create_vm.assert_called_once()
            mock_vm_service.start_vm.assert_called_once_with(vm_id)
            mock_file_service.upload_file.assert_called_once()
            mock_command_service.execute_command.assert_called_once()
            mock_metrics_service.get_vm_metrics.assert_called_once_with(vm_id)
            mock_vm_service.stop_vm.assert_called_once_with(vm_id)
            mock_vm_service.delete_vm.assert_called_once_with(vm_id)
