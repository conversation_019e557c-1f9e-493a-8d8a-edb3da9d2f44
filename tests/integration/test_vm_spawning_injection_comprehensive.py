"""
Comprehensive VM Spawning and Injection Testing

Tests VM spawning performance, queue management, file injection workflows,
and monitoring using Traefik URLs and ServiceURLManager from the host system.
"""

import pytest
import asyncio
import time
import json
import uuid
import tempfile
import requests
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime, timezone

# Import ServiceURLManager
from utils.service_urls import ServiceURLManager


class TestVMSpawningPerformance:
    """Performance tests for VM spawning and allocation."""
    
    @pytest.fixture(scope="class")
    def service_manager(self):
        """Get ServiceURLManager instance."""
        return ServiceURLManager("development")
    
    @pytest.fixture(scope="class")
    def api_url(self, service_manager):
        """Get API base URL using Traefik."""
        return service_manager.get_service_url("api")
    
    def test_api_health_and_connectivity(self, api_url):
        """Test API health and Traefik connectivity."""
        print(f"🔗 Testing API connectivity via Traefik...")
        print(f"   API URL: {api_url}")
        
        response = requests.get(f"{api_url}/health", timeout=30)
        
        assert response.status_code == 200, f"API health check failed: {response.status_code}"
        
        health_data = response.json()
        print(f"   ✅ API health check passed")
        print(f"   Status: {health_data.get('status', 'unknown')}")
        print(f"   Timestamp: {health_data.get('timestamp', 'unknown')}")
        
        # Assert for pytest validation
        assert health_data.get("status") == "healthy", "API should report healthy status"
    
    def test_vm_pool_status_via_api(self, api_url):
        """Test VM pool status retrieval via API."""
        print(f"📊 Testing VM pool status via API...")
        
        response = requests.get(f"{api_url}/api/v1/vm-allocation/pools/status", timeout=30)
        
        print(f"   Pool status response: {response.status_code}")
        
        if response.status_code == 200:
            pool_status = response.json()
            print(f"   ✅ Pool status retrieved successfully")
            
            for template, status in pool_status.items():
                print(f"   Template {template}:")
                print(f"     Ready: {status.get('ready', 0)}")
                print(f"     Total: {status.get('total', 0)}")
                print(f"     Needs provisioning: {status.get('needs_provisioning', False)}")
            
            # Assert for pytest validation
            assert len(pool_status) > 0, "Pool status should contain at least one template"
        else:
            print(f"   ⚠️ Pool status not available: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            pytest.skip(f"Pool status not available: {response.status_code}")
    
    def test_vm_allocation_performance(self, api_url):
        """Test VM allocation performance and timing."""
        print(f"⚡ Testing VM allocation performance...")
        
        allocation_data = {
            "template": "ubuntu:20.04",
            "requester_id": f"perf_test_{uuid.uuid4().hex[:8]}"
        }
        
        start_time = time.time()
        response = requests.post(
            f"{api_url}/api/v1/vm-allocation/allocate",
            json=allocation_data,
            timeout=120
        )
        allocation_time = time.time() - start_time
        
        print(f"   Allocation response: {response.status_code} (took {allocation_time:.2f}s)")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get("success"):
                vm_id = result["vm_id"]
                vm_details = result["vm"]
                duration_ms = result.get("duration_ms", allocation_time * 1000)
                
                print(f"   ✅ VM allocated successfully: {vm_id}")
                print(f"   VM status: {vm_details['status']}")
                print(f"   Allocation duration: {duration_ms}ms")
                print(f"   VM template: {vm_details['template']}")
                print(f"   VM memory: {vm_details['memory_mb']}MB")
                print(f"   VM CPUs: {vm_details['cpus']}")
                
                # Performance assertions
                assert duration_ms < 10000, f"Allocation should take < 10s, took {duration_ms}ms"
                assert vm_details["status"] in ["running", "ready", "creating"], f"VM should be in valid state, got {vm_details['status']}"
                assert vm_id is not None, "VM should have a valid ID"
            else:
                print(f"   ⏳ VM allocation in progress: {result.get('message', 'unknown')}")
                return {
                    "success": False,
                    "status": "provisioning",
                    "message": result.get("message", "unknown")
                }
        else:
            print(f"   ❌ VM allocation failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            # This is expected when pool endpoints are not available
            assert response.status_code in [404, 503], f"Expected 404/503 for missing pool endpoints, got {response.status_code}"
    
    def test_vm_creation_performance(self, api_url):
        """Test direct VM creation performance."""
        print(f"🖥️ Testing direct VM creation performance...")
        
        vm_data = {
            "name": f"perf-test-vm-{uuid.uuid4().hex[:8]}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 1024,
            "cpus": 1,
            "domain": "TurdParty",
            "description": "Performance test VM",
            "auto_start": True
        }
        
        start_time = time.time()
        response = requests.post(
            f"{api_url}/api/v1/vms/",
            json=vm_data,
            timeout=180  # 3 minutes for VM creation
        )
        creation_time = time.time() - start_time
        
        print(f"   VM creation response: {response.status_code} (took {creation_time:.2f}s)")
        
        if response.status_code == 201:
            vm_result = response.json()
            vm_id = vm_result["vm_id"]
            
            print(f"   ✅ VM created successfully: {vm_id}")
            print(f"   VM name: {vm_result['name']}")
            print(f"   VM status: {vm_result['status']}")
            print(f"   Creation time: {creation_time:.2f}s")
            
            # Performance assertions
            assert creation_time < 300, f"VM creation should take < 5 minutes, took {creation_time:.2f}s"
            assert vm_result["status"] in ["creating", "running", "ready"], f"VM should be in valid state, got {vm_result['status']}"
            assert vm_result["template"] == "ubuntu:20.04", "VM should have correct template"
            assert vm_id is not None, "VM should have a valid ID"
        else:
            print(f"   ❌ VM creation failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return {"success": False, "error": response.text}
    
    def test_vm_list_and_filtering(self, api_url):
        """Test VM listing and filtering performance."""
        print(f"📋 Testing VM listing and filtering...")
        
        # Test basic VM listing
        start_time = time.time()
        response = requests.get(f"{api_url}/api/v1/vms/", timeout=30)
        list_time = time.time() - start_time
        
        print(f"   VM list response: {response.status_code} (took {list_time:.3f}s)")
        
        if response.status_code == 200:
            vm_list = response.json()
            
            if isinstance(vm_list, dict) and "vms" in vm_list:
                vms = vm_list["vms"]
                total = vm_list.get("total", len(vms))
            else:
                vms = vm_list if isinstance(vm_list, list) else []
                total = len(vms)
            
            print(f"   ✅ VM list retrieved: {len(vms)} VMs shown, {total} total")
            print(f"   List query time: {list_time:.3f}s")
            
            # Performance assertion
            assert list_time < 5.0, f"VM listing should take < 5s, took {list_time:.3f}s"
            assert total > 0, "Should have at least some VMs in the system"

            # Test filtering by status
            if vms:
                statuses = set(vm.get("status", "unknown") for vm in vms[:10])
                print(f"   VM statuses found: {list(statuses)}")
                assert len(statuses) > 0, "Should have VMs with status information"

                # Test filtering by template
                templates = set(vm.get("template", "unknown") for vm in vms[:10])
                print(f"   VM templates found: {list(templates)}")
                assert len(templates) > 0, "Should have VMs with template information"
        else:
            print(f"   ❌ VM listing failed: {response.status_code}")
            return {"success": False, "error": response.text}


class TestFileInjectionWorkflow:
    """File injection workflow tests with performance metrics."""
    
    @pytest.fixture(scope="class")
    def service_manager(self):
        """Get ServiceURLManager instance."""
        return ServiceURLManager("development")
    
    @pytest.fixture(scope="class")
    def api_url(self, service_manager):
        """Get API base URL using Traefik."""
        return service_manager.get_service_url("api")
    
    @pytest.fixture
    def test_script_file(self):
        """Create a test script file for injection."""
        content = """#!/bin/bash
echo "=== TurdParty File Injection Test ==="
echo "Execution time: $(date)"
echo "Current directory: $(pwd)"
echo "Current user: $(whoami)"
echo "System info: $(uname -a)"
echo "Available disk space:"
df -h | head -5
echo "Running processes:"
ps aux | head -10
echo "=== Test completed successfully ==="
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.sh', delete=False) as f:
            f.write(content)
            test_file = Path(f.name)
        
        yield test_file
        
        # Cleanup
        if test_file.exists():
            test_file.unlink()
    
    def test_file_upload_performance(self, api_url, test_script_file):
        """Test file upload performance."""
        print(f"📤 Testing file upload performance...")
        print(f"   Test file: {test_script_file}")
        print(f"   File size: {test_script_file.stat().st_size} bytes")
        
        with open(test_script_file, 'rb') as f:
            files = {'file': (test_script_file.name, f, 'application/x-shellscript')}
            data = {'description': 'TurdParty performance test script'}
            
            start_time = time.time()
            response = requests.post(
                f"{api_url}/api/v1/files/upload",
                files=files,
                data=data,
                timeout=60
            )
            upload_time = time.time() - start_time
        
        print(f"   Upload response: {response.status_code} (took {upload_time:.3f}s)")
        
        if response.status_code == 200:
            result = response.json()
            file_id = result["file_id"]
            
            print(f"   ✅ File uploaded successfully: {file_id}")
            print(f"   Filename: {result['filename']}")
            print(f"   Upload time: {upload_time:.3f}s")
            
            # Performance assertion
            assert upload_time < 30.0, f"File upload should take < 30s, took {upload_time:.3f}s"
            assert result["status"] == "stored", f"File should be stored successfully, got {result['status']}"
            assert result["file_size"] > 0, "File should have non-zero size"
            assert file_id is not None, "File should have a valid ID"
        else:
            print(f"   ❌ File upload failed: {response.status_code}")
            return {"success": False, "error": response.text}
    
    def test_complete_injection_workflow(self, api_url, test_script_file):
        """Test complete file injection workflow with performance metrics."""
        print(f"🔄 Testing complete injection workflow...")

        # Step 1: Upload file directly in this test
        print(f"📤 Uploading test file...")

        with open(test_script_file, 'rb') as f:
            files = {"file": (test_script_file.name, f, "application/x-sh")}
            data = {"description": "Test script for injection workflow"}

            start_time = time.time()
            response = requests.post(f"{api_url}/api/v1/files/upload", files=files, data=data, timeout=30)
            upload_time = time.time() - start_time

            print(f"   Upload response: {response.status_code} (took {upload_time:.3f}s)")

            if response.status_code != 200:
                pytest.skip(f"File upload failed: {response.status_code}")

            result = response.json()
            file_id = result["file_id"]
            print(f"   ✅ File uploaded successfully: {file_id}")

        # Step 2: Create VM directly in this test
        print(f"🖥️ Creating VM for injection...")

        vm_data = {
            "name": f"injection-test-vm-{uuid.uuid4().hex[:8]}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 1024,
            "cpus": 1,
            "disk_gb": 20,
            "description": "Injection workflow test VM"
        }

        start_time = time.time()
        response = requests.post(f"{api_url}/api/v1/vms/", json=vm_data, timeout=30)
        creation_time = time.time() - start_time

        print(f"   VM creation response: {response.status_code} (took {creation_time:.2f}s)")

        if response.status_code != 201:
            pytest.skip(f"VM creation failed: {response.status_code}")

        vm_result = response.json()
        vm_id = vm_result["vm_id"]
        print(f"   ✅ VM created successfully: {vm_id}")

        # Step 2.5: Wait for VM to be running (or skip if not ready)
        print(f"   Checking VM status...")

        max_wait_time = 60  # Wait up to 60 seconds for VM to start
        wait_start = time.time()
        vm_ready = False

        while time.time() - wait_start < max_wait_time:
            response = requests.get(f"{api_url}/api/v1/vms/{vm_id}", timeout=10)
            if response.status_code == 200:
                vm_status = response.json()
                current_status = vm_status.get("status", "unknown")
                print(f"   VM status: {current_status}")

                if current_status in ["running", "ready"]:
                    vm_ready = True
                    break
                elif current_status in ["failed", "error", "terminated"]:
                    pytest.skip(f"VM failed to start: {current_status}")

            time.sleep(5)  # Wait 5 seconds before checking again

        if not vm_ready:
            pytest.skip(f"VM did not start within {max_wait_time} seconds - this is expected for integration tests")

        # Step 3: Inject file
        print(f"   Injecting file {file_id} into VM {vm_id}...")

        injection_data = {
            "file_uuid": file_id,
            "injection_path": f"/tmp/{test_script_file.name}",
            "execute_after_injection": True,
            "permissions": "0755"
        }

        start_time = time.time()
        response = requests.post(
            f"{api_url}/api/v1/vms/{vm_id}/inject",
            json=injection_data,
            timeout=300
        )
        injection_time = time.time() - start_time

        print(f"   Injection response: {response.status_code} (took {injection_time:.2f}s)")

        if response.status_code in [200, 202]:
            injection_result = response.json()
            print(f"   ✅ File injection initiated successfully")

            # Performance assertions
            assert injection_time < 60.0, f"Injection initiation should take < 60s, took {injection_time:.2f}s"
            assert upload_time < 30.0, f"File upload should take < 30s, took {upload_time:.3f}s"
            assert creation_time < 300.0, f"VM creation should take < 5 minutes, took {creation_time:.2f}s"
            assert file_id is not None, "File should have a valid ID"
            assert vm_id is not None, "VM should have a valid ID"
        else:
            print(f"   ❌ File injection failed: {response.status_code}")
            pytest.fail(f"File injection failed: {response.status_code} - {response.text}")


if __name__ == "__main__":
    # Run tests manually for debugging
    print("🧪 Running Comprehensive VM Spawning and Injection Tests...")
    
    try:
        service_manager = ServiceURLManager("development")
        api_url = service_manager.get_service_url("api")
        
        print(f"\nAPI URL: {api_url}")
        
        # Test VM spawning performance
        spawning_test = TestVMSpawningPerformance()
        
        print("\n1. Testing API connectivity...")
        health = spawning_test.test_api_health_and_connectivity(api_url)
        
        print("\n2. Testing VM pool status...")
        pool_status = spawning_test.test_vm_pool_status_via_api(api_url)
        
        print("\n3. Testing VM allocation performance...")
        allocation = spawning_test.test_vm_allocation_performance(api_url)
        
        print("\n4. Testing VM listing performance...")
        listing = spawning_test.test_vm_list_and_filtering(api_url)
        
        # Test file injection workflow
        injection_test = TestFileInjectionWorkflow()
        
        print("\n5. Creating test file...")
        import tempfile
        content = "#!/bin/bash\necho 'Test execution successful'\n"
        with tempfile.NamedTemporaryFile(mode='w', suffix='.sh', delete=False) as f:
            f.write(content)
            test_file = Path(f.name)
        
        print("\n6. Testing file upload performance...")
        upload = injection_test.test_file_upload_performance(api_url, test_file)
        
        print("\n7. Testing complete injection workflow...")
        workflow = injection_test.test_complete_injection_workflow(api_url, test_file)
        
        print("\n✅ All comprehensive tests completed successfully!")
        
        # Cleanup
        test_file.unlink()
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
