"""
Fast Workflow Integration Tests - Tier 1 (Lightweight)
Container-based workflow tests for rapid CI/CD feedback.
Target runtime: < 45 seconds
"""

import pytest
import time
import json
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from typing import Dict, List, Any


class TestWorkflowLightIntegration:
    """Fast, container-based workflow integration tests for CI/CD pipelines."""

    @pytest.fixture(scope="class")
    def light_config(self):
        """Lightweight workflow configuration."""
        return {
            "api_base_url": "http://localhost:8000/api/v1",
            "minio_endpoint": "localhost:9000",
            "elasticsearch_url": "http://localhost:9200",
            "test_file_size": 1024,  # 1KB vs 8MB
            "monitoring_duration": 10,  # 10 seconds vs 5 minutes
            "vm_startup_timeout": 30,  # 30 seconds vs 2 minutes
            "container_image": "alpine:latest",  # Fast, pre-pulled image
            "max_concurrent_vms": 2
        }

    @pytest.fixture(scope="class")
    def test_file_light(self, light_config):
        """Create a small test file for fast operations."""
        content = f"Test file for workflow - {datetime.now()}\n" * 50  # ~1KB
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(content)
            temp_path = f.name
            
        yield {
            "path": temp_path,
            "content": content,
            "size": len(content.encode()),
            "filename": os.path.basename(temp_path)
        }
        
        # Cleanup
        if os.path.exists(temp_path):
            os.remove(temp_path)

    def test_01_initiate_workflow_light(self, light_config, test_file_light):
        """Test workflow initiation with lightweight operations."""
        print("🚀 Testing workflow initiation (light)...")
        
        with patch('requests.post') as mock_post, patch('requests.get') as mock_get:
            # Mock file upload
            mock_post.return_value.status_code = 201
            mock_post.return_value.json.return_value = {
                "file_id": f"light-file-{int(time.time())}",
                "filename": test_file_light["filename"],
                "size": test_file_light["size"],
                "status": "uploaded",
                "blake3_hash": "mock_blake3_hash",
                "uuid": f"uuid-{int(time.time())}"
            }
            
            import requests
            
            # Test file upload
            with open(test_file_light["path"], 'rb') as f:
                files = {'file': (test_file_light["filename"], f)}
                data = {'description': 'Light workflow test file'}
                
                response = requests.post(f"{light_config['api_base_url']}/files/upload", files=files, data=data)
                assert response.status_code == 201
                
                upload_result = response.json()
                assert "file_id" in upload_result
                assert upload_result["size"] == test_file_light["size"]
                
        print("✅ Workflow initiation (light) passed")

    def test_02_container_vm_creation_light(self, light_config):
        """Test fast container-based VM creation."""
        print("🐳 Testing container VM creation (light)...")
        
        mock_vm_id = f"light-vm-{int(time.time())}"
        
        with patch('requests.post') as mock_post, patch('requests.get') as mock_get:
            # Mock VM creation (Docker container)
            mock_post.return_value.status_code = 201
            mock_post.return_value.json.return_value = {
                "vm_id": mock_vm_id,
                "vm_type": "docker",
                "template": light_config["container_image"],
                "status": "creating",
                "estimated_startup": "30s"
            }
            
            # Mock VM status progression
            status_responses = [
                {"status": "creating", "progress": 25},
                {"status": "starting", "progress": 75},
                {"status": "running", "progress": 100, "ip_address": "**********"}
            ]
            
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.side_effect = status_responses
            
            import requests
            
            # Test VM creation
            vm_data = {
                "name": f"light-workflow-vm-{int(time.time())}",
                "template": light_config["container_image"],
                "vm_type": "docker",
                "memory_mb": 256,  # Minimal memory
                "cpus": 1,
                "domain": "TurdParty",
                "auto_start": True
            }
            
            response = requests.post(f"{light_config['api_base_url']}/vms/", json=vm_data)
            assert response.status_code == 201
            
            vm_info = response.json()
            assert vm_info["vm_id"] == mock_vm_id
            assert vm_info["vm_type"] == "docker"
            
            # Test VM status progression
            for expected_status in ["creating", "starting", "running"]:
                response = requests.get(f"{light_config['api_base_url']}/vms/{mock_vm_id}")
                assert response.status_code == 200
                
                vm_status = response.json()
                if vm_status["status"] == "running":
                    assert "ip_address" in vm_status
                    break
                    
        print(f"✅ Container VM creation (light) passed: {mock_vm_id}")

    def test_03_fast_file_injection_light(self, light_config, test_file_light):
        """Test fast file injection into container."""
        print("💉 Testing fast file injection (light)...")
        
        mock_injection_id = f"light-injection-{int(time.time())}"
        
        with patch('requests.post') as mock_post, patch('requests.get') as mock_get:
            # Mock file injection
            mock_post.return_value.status_code = 200
            mock_post.return_value.json.return_value = {
                "injection_id": mock_injection_id,
                "status": "started",
                "file_size": test_file_light["size"],
                "target_path": "/tmp/test_file.txt",
                "estimated_duration": "5s"
            }
            
            # Mock injection status
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = {
                "injection_id": mock_injection_id,
                "status": "completed",
                "duration": 3.2,
                "file_path": "/tmp/test_file.txt",
                "verification": "success"
            }
            
            import requests
            
            # Test file injection
            injection_data = {
                "file_path": test_file_light["path"],
                "injection_path": "/tmp/test_file.txt",
                "verify_injection": True
            }
            
            response = requests.post(f"{light_config['api_base_url']}/vms/light-vm/inject", json=injection_data)
            assert response.status_code == 200
            
            injection_result = response.json()
            assert injection_result["injection_id"] == mock_injection_id
            assert injection_result["file_size"] == test_file_light["size"]
            
            # Test injection completion
            response = requests.get(f"{light_config['api_base_url']}/vms/light-vm/injections/{mock_injection_id}")
            assert response.status_code == 200
            
            status = response.json()
            assert status["status"] == "completed"
            assert status["verification"] == "success"
            
        print(f"✅ Fast file injection (light) passed: {mock_injection_id}")

    def test_04_lightweight_monitoring_light(self, light_config):
        """Test lightweight monitoring with short duration."""
        print("📊 Testing lightweight monitoring (light)...")
        
        # Simulate 10-second monitoring vs 5-minute
        start_time = time.time()
        
        mock_events = []
        for i in range(5):  # Generate 5 mock events
            event = {
                "timestamp": datetime.now().isoformat(),
                "event_type": ["file_access", "process_start", "network_connect"][i % 3],
                "vm_id": "light-vm",
                "details": f"Mock event {i+1}",
                "severity": "info"
            }
            mock_events.append(event)
            time.sleep(0.1)  # 100ms between events
            
        monitoring_duration = time.time() - start_time
        assert monitoring_duration < light_config["monitoring_duration"]
        
        with patch('requests.post') as mock_post:
            # Mock ELK data submission
            mock_post.return_value.status_code = 201
            mock_post.return_value.json.return_value = {
                "indexed": len(mock_events),
                "index": "turdparty-light-test",
                "duration": monitoring_duration
            }
            
            import requests
            
            # Test event submission to ELK
            elk_data = {
                "events": mock_events,
                "vm_id": "light-vm",
                "monitoring_duration": monitoring_duration
            }
            
            response = requests.post(f"{light_config['elasticsearch_url']}/turdparty-light/_bulk", json=elk_data)
            assert response.status_code == 201
            
            result = response.json()
            assert result["indexed"] == len(mock_events)
            
        print(f"✅ Lightweight monitoring (light) passed: {len(mock_events)} events in {monitoring_duration:.2f}s")

    def test_05_concurrent_workflow_light(self, light_config):
        """Test concurrent workflow operations."""
        print("🔀 Testing concurrent workflow (light)...")
        
        concurrent_vms = light_config["max_concurrent_vms"]
        
        with patch('requests.post') as mock_post, patch('requests.get') as mock_get:
            # Mock concurrent VM creation
            vm_ids = [f"concurrent-vm-{i}-{int(time.time())}" for i in range(concurrent_vms)]
            
            mock_post.return_value.status_code = 201
            mock_post.return_value.json.side_effect = [
                {"vm_id": vm_id, "status": "creating"} for vm_id in vm_ids
            ]
            
            # Mock all VMs becoming ready
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = {"status": "running"}
            
            import requests
            
            # Test concurrent VM creation
            start_time = time.time()
            created_vms = []
            
            for i in range(concurrent_vms):
                vm_data = {
                    "name": f"concurrent-light-{i}",
                    "template": light_config["container_image"],
                    "vm_type": "docker",
                    "memory_mb": 128,  # Minimal for concurrency
                    "cpus": 1
                }
                
                response = requests.post(f"{light_config['api_base_url']}/vms/", json=vm_data)
                assert response.status_code == 201
                
                vm_info = response.json()
                created_vms.append(vm_info["vm_id"])
                
            creation_time = time.time() - start_time
            assert creation_time < 10.0, f"Concurrent creation took {creation_time}s, expected < 10s"
            
            # Test concurrent status checks
            for vm_id in created_vms:
                response = requests.get(f"{light_config['api_base_url']}/vms/{vm_id}")
                assert response.status_code == 200
                
                status = response.json()
                assert status["status"] == "running"
                
        print(f"✅ Concurrent workflow (light) passed: {concurrent_vms} VMs in {creation_time:.2f}s")

    def test_06_workflow_error_recovery_light(self, light_config):
        """Test workflow error recovery scenarios."""
        print("⚠️ Testing workflow error recovery (light)...")
        
        with patch('requests.post') as mock_post, patch('requests.get') as mock_get:
            # Test VM creation failure and retry
            mock_post.side_effect = [
                Mock(status_code=500, json=lambda: {"error": "Resource temporarily unavailable"}),
                Mock(status_code=201, json=lambda: {"vm_id": "recovery-vm", "status": "creating"})
            ]
            
            import requests
            
            # First attempt should fail
            vm_data = {"name": "recovery-test", "template": "alpine:latest", "vm_type": "docker"}
            
            response = requests.post(f"{light_config['api_base_url']}/vms/", json=vm_data)
            assert response.status_code == 500
            
            # Retry should succeed
            response = requests.post(f"{light_config['api_base_url']}/vms/", json=vm_data)
            assert response.status_code == 201
            
            # Test injection failure handling
            mock_post.side_effect = None
            mock_post.return_value.status_code = 400
            mock_post.return_value.json.return_value = {"error": "File not found"}
            
            injection_data = {"file_path": "/nonexistent/file.txt"}
            response = requests.post(f"{light_config['api_base_url']}/vms/recovery-vm/inject", json=injection_data)
            assert response.status_code == 400
            
            error_info = response.json()
            assert "error" in error_info
            
        print("✅ Workflow error recovery (light) passed")

    def test_07_resource_cleanup_light(self, light_config):
        """Test resource cleanup in workflow."""
        print("🧹 Testing resource cleanup (light)...")
        
        with patch('requests.delete') as mock_delete, patch('requests.get') as mock_get:
            # Mock resource listing
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = {
                "vms": ["light-vm-1", "light-vm-2"],
                "files": ["light-file-1", "light-file-2"],
                "injections": ["light-injection-1"]
            }
            
            # Mock successful cleanup
            mock_delete.return_value.status_code = 200
            mock_delete.return_value.json.return_value = {"status": "deleted"}
            
            import requests
            
            # Test resource listing
            response = requests.get(f"{light_config['api_base_url']}/resources/light-workflow")
            assert response.status_code == 200
            
            resources = response.json()
            total_resources = len(resources["vms"]) + len(resources["files"]) + len(resources["injections"])
            
            # Test cleanup
            cleanup_start = time.time()
            
            for vm_id in resources["vms"]:
                response = requests.delete(f"{light_config['api_base_url']}/vms/{vm_id}")
                assert response.status_code == 200
                
            for file_id in resources["files"]:
                response = requests.delete(f"{light_config['api_base_url']}/files/{file_id}")
                assert response.status_code == 200
                
            cleanup_time = time.time() - cleanup_start
            assert cleanup_time < 5.0, f"Cleanup took {cleanup_time}s, expected < 5s"
            
        print(f"✅ Resource cleanup (light) passed: {total_resources} resources in {cleanup_time:.2f}s")

    def test_08_workflow_performance_validation_light(self, light_config):
        """Test workflow performance meets light requirements."""
        print("⚡ Testing workflow performance (light)...")
        
        # Test overall workflow timing
        workflow_start = time.time()
        
        # Simulate complete light workflow
        operations = [
            ("file_upload", 0.5),
            ("vm_creation", 5.0),
            ("file_injection", 2.0),
            ("monitoring", 10.0),
            ("cleanup", 1.0)
        ]
        
        operation_times = {}
        for operation, expected_max in operations:
            op_start = time.time()
            time.sleep(0.1)  # Simulate operation
            op_time = time.time() - op_start
            
            operation_times[operation] = op_time
            assert op_time < expected_max, f"{operation} took {op_time}s, expected < {expected_max}s"
            
        total_workflow_time = time.time() - workflow_start
        assert total_workflow_time < 45.0, f"Total workflow took {total_workflow_time}s, expected < 45s"
        
        print(f"✅ Workflow performance (light) passed: {total_workflow_time:.2f}s total")
        
        # Print operation breakdown
        for operation, op_time in operation_times.items():
            print(f"   {operation}: {op_time:.3f}s")


if __name__ == "__main__":
    # Run fast workflow tests
    pytest.main([__file__, "-v", "--tb=short", "--disable-warnings"])
