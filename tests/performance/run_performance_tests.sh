#!/bin/bash

# TurdParty Performance Benchmark Runner
# Comprehensive performance testing for VM operations and MinIO storage

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${BLUE}🎯 TurdParty Performance Benchmark Suite${NC}"
echo "=============================================="

# Check if we're in the correct directory
if [ ! -f "run_benchmarks.py" ]; then
    echo -e "${RED}❌ Error: This script must be run from the tests/performance directory${NC}"
    exit 1
fi

# Check if API is running
echo -e "${YELLOW}🔍 Checking if TurdParty API is running...${NC}"

API_URL="http://localhost:8000"
if ! curl -f "$API_URL/health/" >/dev/null 2>&1; then
    echo -e "${RED}❌ TurdParty API is not accessible at $API_URL${NC}"
    echo -e "${YELLOW}💡 Please start the API service first:${NC}"
    echo "   cd ../../compose && docker compose up -d"
    exit 1
fi

echo -e "${GREEN}✅ API is running and accessible${NC}"

# Parse command line arguments
RUN_VM=true
RUN_MINIO=true
ITERATIONS=3
QUICK_MODE=false
OUTPUT_FILE=""
PYTHON_CMD="python3"

while [[ $# -gt 0 ]]; do
    case $1 in
        --vm-only)
            RUN_MINIO=false
            shift
            ;;
        --minio-only)
            RUN_VM=false
            shift
            ;;
        --quick)
            QUICK_MODE=true
            ITERATIONS=2
            shift
            ;;
        --iterations)
            ITERATIONS="$2"
            shift 2
            ;;
        --output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        --python)
            PYTHON_CMD="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --vm-only           Run only VM benchmarks"
            echo "  --minio-only        Run only MinIO benchmarks"
            echo "  --quick             Run quick benchmarks (fewer iterations)"
            echo "  --iterations <n>    Number of iterations (default: 3)"
            echo "  --output <file>     Output filename for results"
            echo "  --python <cmd>      Python command to use (default: python3)"
            echo "  --help              Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                              # Run all benchmarks"
            echo "  $0 --vm-only --quick            # Quick VM benchmarks only"
            echo "  $0 --minio-only --iterations 5  # MinIO benchmarks with 5 iterations"
            echo "  $0 --output my_results.json     # Save results to specific file"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ Unknown option: $1${NC}"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check Python and required packages
echo -e "${YELLOW}🐍 Checking Python environment...${NC}"

if ! command -v $PYTHON_CMD &> /dev/null; then
    echo -e "${RED}❌ Python command '$PYTHON_CMD' not found${NC}"
    exit 1
fi

# Check if required packages are available
REQUIRED_PACKAGES="requests psutil"
for package in $REQUIRED_PACKAGES; do
    if ! $PYTHON_CMD -c "import $package" 2>/dev/null; then
        echo -e "${YELLOW}⚠️  Installing required Python package: $package${NC}"
        $PYTHON_CMD -m pip install $package --user
    fi
done

echo -e "${GREEN}✅ Python environment ready${NC}"

# Prepare benchmark arguments
BENCHMARK_ARGS=""

if [ "$RUN_VM" = false ]; then
    BENCHMARK_ARGS="$BENCHMARK_ARGS --minio-only"
elif [ "$RUN_MINIO" = false ]; then
    BENCHMARK_ARGS="$BENCHMARK_ARGS --vm-only"
fi

if [ "$QUICK_MODE" = true ]; then
    BENCHMARK_ARGS="$BENCHMARK_ARGS --quick"
else
    BENCHMARK_ARGS="$BENCHMARK_ARGS --iterations $ITERATIONS"
fi

if [ -n "$OUTPUT_FILE" ]; then
    BENCHMARK_ARGS="$BENCHMARK_ARGS --output $OUTPUT_FILE"
fi

# Display benchmark configuration
echo ""
echo -e "${PURPLE}📋 Benchmark Configuration:${NC}"
echo "  VM Benchmarks: $([ "$RUN_VM" = true ] && echo "✅ Enabled" || echo "❌ Disabled")"
echo "  MinIO Benchmarks: $([ "$RUN_MINIO" = true ] && echo "✅ Enabled" || echo "❌ Disabled")"
echo "  Iterations: $ITERATIONS"
echo "  Quick Mode: $([ "$QUICK_MODE" = true ] && echo "✅ Enabled" || echo "❌ Disabled")"
echo "  Python Command: $PYTHON_CMD"
if [ -n "$OUTPUT_FILE" ]; then
    echo "  Output File: $OUTPUT_FILE"
fi

# Warning for resource-intensive tests
if [ "$RUN_VM" = true ]; then
    echo ""
    echo -e "${YELLOW}⚠️  WARNING: VM benchmarks will create and destroy multiple VMs${NC}"
    echo -e "${YELLOW}   This may consume significant system resources and time${NC}"
    echo -e "${YELLOW}   Ensure you have sufficient Docker resources available${NC}"
fi

echo ""
echo -e "${BLUE}🚀 Starting performance benchmarks...${NC}"
echo "=============================================="

# Create data directory if it doesn't exist
mkdir -p ../data

# Run benchmarks
START_TIME=$(date +%s)

if command -v nix-shell &> /dev/null; then
    echo -e "${BLUE}Using Nix environment for better reproducibility...${NC}"
    nix-shell -p python3 python3Packages.requests python3Packages.psutil --run "$PYTHON_CMD run_benchmarks.py $BENCHMARK_ARGS"
else
    $PYTHON_CMD run_benchmarks.py $BENCHMARK_ARGS
fi

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

# Summary
echo ""
echo "=============================================="
echo -e "${BLUE}📊 Benchmark Execution Summary${NC}"
echo "=============================================="

echo -e "${GREEN}✅ Benchmarks completed successfully!${NC}"
echo -e "${BLUE}⏱️  Total execution time: ${DURATION}s${NC}"

if [ "$RUN_VM" = true ]; then
    echo -e "${GREEN}✅ VM Performance: Tested startup, interactions, and resource usage${NC}"
fi

if [ "$RUN_MINIO" = true ]; then
    echo -e "${GREEN}✅ MinIO Performance: Tested uploads, downloads, and throughput${NC}"
fi

echo ""
echo -e "${YELLOW}📈 Performance Insights:${NC}"
echo "  • Check the generated JSON file for detailed metrics"
echo "  • Compare results across different system configurations"
echo "  • Use results to optimize VM and storage configurations"
echo "  • Monitor trends over time for performance regression detection"

echo ""
echo -e "${BLUE}📚 Next Steps:${NC}"
echo "  • Analyze results in tests/data/ directory"
echo "  • Compare with baseline performance metrics"
echo "  • Integrate benchmarks into CI/CD pipeline"
echo "  • Set up performance monitoring alerts"

echo ""
echo -e "${GREEN}🎉 TurdParty performance benchmarking completed!${NC}"
