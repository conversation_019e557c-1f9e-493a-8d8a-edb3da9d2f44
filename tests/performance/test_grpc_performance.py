"""
Performance Tests for gRPC VM Injection System

Tests system performance, throughput, and resource utilization
under various load conditions.
"""

import concurrent.futures
import json
import statistics
import subprocess
import time
import uuid
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
from pathlib import Path

# Performance Test Configuration
API_BASE_URL = "http://api.turdparty.localhost/api/v1"
TEST_FILE_ID = "902c5e5f-1109-4c1d-ba68-a50da622395d"


@dataclass
class PerformanceMetrics:
    """Container for performance test results."""
    operation: str
    total_operations: int
    successful_operations: int
    failed_operations: int
    total_time: float
    average_time: float
    min_time: float
    max_time: float
    throughput_ops_per_sec: float
    p95_time: float
    p99_time: float


class GRPCPerformanceTester:
    """Performance testing suite for gRPC VM injection system."""

    def __init__(self):
        self.test_vms = []
        self.results = []
        self.api_base = "http://api.turdparty.localhost/api/v1"

    def http_request(self, method: str, endpoint: str, data: Dict = None) -> Dict[str, Any]:
        """Make HTTP request using curl."""
        url = f"{self.api_base}{endpoint}"

        if method.upper() == "GET":
            cmd = ["curl", "-s", url]
        elif method.upper() == "POST":
            cmd = ["curl", "-s", "-X", "POST", "-H", "Content-Type: application/json"]
            if data:
                cmd.extend(["-d", json.dumps(data)])
            cmd.append(url)
        elif method.upper() == "DELETE":
            cmd = ["curl", "-s", "-X", "DELETE", url]
        else:
            raise ValueError(f"Unsupported method: {method}")

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0 and result.stdout.strip():
                try:
                    return {"success": True, "data": json.loads(result.stdout)}
                except json.JSONDecodeError:
                    return {"success": True, "data": {"raw": result.stdout}}
            else:
                return {"success": False, "error": result.stderr or "Request failed"}
        except subprocess.TimeoutExpired:
            return {"success": False, "error": "Request timeout"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def cleanup_resources(self):
        """Clean up test resources."""
        for vm_id in self.test_vms:
            try:
                self.http_request("DELETE", f"/vms/{vm_id}")
            except Exception:
                pass
        self.test_vms.clear()
    
    def measure_operation_time(self, operation_func, *args, **kwargs) -> Tuple[Any, float]:
        """Measure execution time of an operation."""
        start_time = time.time()
        result = operation_func(*args, **kwargs)
        end_time = time.time()
        return result, end_time - start_time
    
    def calculate_metrics(self, operation: str, times: List[float], 
                         successes: int, failures: int) -> PerformanceMetrics:
        """Calculate performance metrics from timing data."""
        total_ops = successes + failures
        total_time = sum(times)
        
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            p95_time = statistics.quantiles(times, n=20)[18] if len(times) >= 20 else max_time
            p99_time = statistics.quantiles(times, n=100)[98] if len(times) >= 100 else max_time
        else:
            avg_time = min_time = max_time = p95_time = p99_time = 0
        
        throughput = successes / total_time if total_time > 0 else 0
        
        return PerformanceMetrics(
            operation=operation,
            total_operations=total_ops,
            successful_operations=successes,
            failed_operations=failures,
            total_time=total_time,
            average_time=avg_time,
            min_time=min_time,
            max_time=max_time,
            throughput_ops_per_sec=throughput,
            p95_time=p95_time,
            p99_time=p99_time
        )
    
    def test_vm_creation_performance(self, vm_count: int = 10, vm_type: str = "docker") -> PerformanceMetrics:
        """Test VM creation performance."""
        print(f"\n📊 Testing {vm_type} VM creation performance ({vm_count} VMs)...")
        
        times = []
        successes = 0
        failures = 0
        
        for i in range(vm_count):
            vm_config = {
                "name": f"perf-test-{vm_type}-{i}-{uuid.uuid4().hex[:8]}",
                "template": "ubuntu:20.04" if vm_type == "docker" else "10Baht/windows10-turdparty",
                "vm_type": vm_type,
                "memory_mb": 1024 if vm_type == "docker" else 4096,
                "cpus": 1 if vm_type == "docker" else 2,
                "disk_gb": 10 if vm_type == "docker" else 50,
                "description": f"Performance test {vm_type} VM {i}"
            }
            
            try:
                response, duration = self.measure_operation_time(
                    self.http_request, "POST", "/vms/", vm_config
                )

                if response["success"]:
                    vm_data = response["data"]
                    self.test_vms.append(vm_data["vm_id"])
                    times.append(duration)
                    successes += 1
                    print(f"  ✅ VM {i+1}/{vm_count} created in {duration:.2f}s")
                else:
                    failures += 1
                    print(f"  ❌ VM {i+1}/{vm_count} failed: {response['error']}")

            except Exception as e:
                failures += 1
                print(f"  ❌ VM {i+1}/{vm_count} error: {e}")
        
        metrics = self.calculate_metrics(f"{vm_type}_vm_creation", times, successes, failures)
        self.results.append(metrics)
        return metrics
    
    def test_injection_performance(self, injection_count: int = 20) -> PerformanceMetrics:
        """Test file injection performance."""
        print(f"\n💉 Testing file injection performance ({injection_count} injections)...")
        
        # First, ensure we have enough VMs
        min_vms_needed = min(injection_count, 10)
        current_vm_count = len(self.test_vms)
        
        if current_vm_count < min_vms_needed:
            print(f"  Creating {min_vms_needed - current_vm_count} additional VMs...")
            self.test_vm_creation_performance(min_vms_needed - current_vm_count, "docker")
        
        # Wait for VMs to be ready
        ready_vms = []
        for vm_id in self.test_vms[:min_vms_needed]:
            try:
                start_time = time.time()
                while time.time() - start_time < 60:  # 60s timeout
                    response = requests.get(f"{API_BASE_URL}/vms/{vm_id}")
                    if response.status_code == 200:
                        vm_data = response.json()
                        if vm_data.get("status") == "running":
                            ready_vms.append(vm_id)
                            break
                    time.sleep(2)
            except Exception:
                continue
        
        print(f"  Using {len(ready_vms)} ready VMs for injection tests")
        
        times = []
        successes = 0
        failures = 0
        
        for i in range(injection_count):
            vm_id = ready_vms[i % len(ready_vms)]  # Round-robin VM selection
            
            injection_config = {
                "file_id": TEST_FILE_ID,
                "injection_path": f"/tmp/perf-test-{i}.exe",
                "execute_after_injection": False,  # Skip execution for performance
                "permissions": "0755"
            }
            
            try:
                response, duration = self.measure_operation_time(
                    requests.post, f"{API_BASE_URL}/vms/{vm_id}/inject", json=injection_config
                )
                
                if response.status_code == 200:
                    times.append(duration)
                    successes += 1
                    print(f"  ✅ Injection {i+1}/{injection_count} queued in {duration:.2f}s")
                else:
                    failures += 1
                    print(f"  ❌ Injection {i+1}/{injection_count} failed: {response.status_code}")
                    
            except Exception as e:
                failures += 1
                print(f"  ❌ Injection {i+1}/{injection_count} error: {e}")
        
        metrics = self.calculate_metrics("file_injection", times, successes, failures)
        self.results.append(metrics)
        return metrics
    
    def test_concurrent_operations(self, concurrent_count: int = 5) -> PerformanceMetrics:
        """Test concurrent VM operations."""
        print(f"\n🔄 Testing concurrent operations ({concurrent_count} parallel)...")
        
        def create_vm_and_inject(index: int) -> Tuple[bool, float]:
            """Create VM and inject file concurrently."""
            start_time = time.time()
            
            try:
                # Create VM
                vm_config = {
                    "name": f"concurrent-test-{index}-{uuid.uuid4().hex[:8]}",
                    "template": "ubuntu:20.04",
                    "vm_type": "docker",
                    "memory_mb": 1024,
                    "cpus": 1,
                    "disk_gb": 10,
                    "description": f"Concurrent test VM {index}"
                }
                
                response = requests.post(f"{API_BASE_URL}/vms/", json=vm_config)
                if response.status_code != 200:
                    return False, time.time() - start_time
                
                vm_data = response.json()
                vm_id = vm_data["vm_id"]
                
                # Wait for VM ready (with timeout)
                ready_timeout = time.time() + 60
                while time.time() < ready_timeout:
                    response = requests.get(f"{API_BASE_URL}/vms/{vm_id}")
                    if response.status_code == 200:
                        vm_status = response.json()
                        if vm_status.get("status") == "running":
                            break
                    time.sleep(1)
                else:
                    return False, time.time() - start_time
                
                # Inject file
                injection_config = {
                    "file_id": TEST_FILE_ID,
                    "injection_path": f"/tmp/concurrent-{index}.exe",
                    "execute_after_injection": False,
                    "permissions": "0755"
                }
                
                response = requests.post(f"{API_BASE_URL}/vms/{vm_id}/inject", json=injection_config)
                success = response.status_code == 200
                
                # Add to cleanup list
                self.test_vms.append(vm_id)
                
                return success, time.time() - start_time
                
            except Exception as e:
                print(f"  ❌ Concurrent operation {index} error: {e}")
                return False, time.time() - start_time
        
        # Execute concurrent operations
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_count) as executor:
            futures = [
                executor.submit(create_vm_and_inject, i) 
                for i in range(concurrent_count)
            ]
            
            results = []
            for i, future in enumerate(concurrent.futures.as_completed(futures)):
                success, duration = future.result()
                results.append((success, duration))
                status = "✅" if success else "❌"
                print(f"  {status} Concurrent operation {i+1} completed in {duration:.2f}s")
        
        # Calculate metrics
        times = [duration for success, duration in results]
        successes = sum(1 for success, _ in results if success)
        failures = len(results) - successes
        
        metrics = self.calculate_metrics("concurrent_operations", times, successes, failures)
        self.results.append(metrics)
        return metrics
    
    def test_system_limits(self) -> Dict[str, Any]:
        """Test system limits and resource constraints."""
        print("\n🚨 Testing system limits...")
        
        limits_results = {
            "max_concurrent_vms": 0,
            "max_injection_rate": 0,
            "resource_exhaustion_point": None
        }
        
        # Test maximum concurrent VMs
        print("  Testing maximum concurrent VMs...")
        vm_count = 0
        max_vms = 20  # Safety limit
        
        for i in range(max_vms):
            vm_config = {
                "name": f"limit-test-{i}-{uuid.uuid4().hex[:8]}",
                "template": "ubuntu:20.04",
                "vm_type": "docker",
                "memory_mb": 512,  # Smaller VMs for limit testing
                "cpus": 1,
                "disk_gb": 5,
                "description": f"Limit test VM {i}"
            }
            
            try:
                response = requests.post(f"{API_BASE_URL}/vms/", json=vm_config, timeout=30)
                if response.status_code == 200:
                    vm_data = response.json()
                    self.test_vms.append(vm_data["vm_id"])
                    vm_count += 1
                    print(f"    ✅ VM {vm_count} created")
                else:
                    print(f"    ❌ VM creation failed at {vm_count}: {response.status_code}")
                    break
            except Exception as e:
                print(f"    ❌ VM creation error at {vm_count}: {e}")
                break
        
        limits_results["max_concurrent_vms"] = vm_count
        print(f"  📊 Maximum concurrent VMs: {vm_count}")
        
        return limits_results
    
    def generate_performance_report(self) -> str:
        """Generate comprehensive performance report."""
        report = "\n" + "="*80 + "\n"
        report += "🎯 gRPC VM INJECTION SYSTEM - PERFORMANCE REPORT\n"
        report += "="*80 + "\n"
        
        for metrics in self.results:
            report += f"\n📊 {metrics.operation.upper()} PERFORMANCE:\n"
            report += f"  Total Operations: {metrics.total_operations}\n"
            report += f"  Successful: {metrics.successful_operations} ({metrics.successful_operations/metrics.total_operations*100:.1f}%)\n"
            report += f"  Failed: {metrics.failed_operations}\n"
            report += f"  Total Time: {metrics.total_time:.2f}s\n"
            report += f"  Average Time: {metrics.average_time:.2f}s\n"
            report += f"  Min/Max Time: {metrics.min_time:.2f}s / {metrics.max_time:.2f}s\n"
            report += f"  95th Percentile: {metrics.p95_time:.2f}s\n"
            report += f"  99th Percentile: {metrics.p99_time:.2f}s\n"
            report += f"  Throughput: {metrics.throughput_ops_per_sec:.2f} ops/sec\n"
            report += "-" * 50 + "\n"
        
        return report


if __name__ == "__main__":
    # Run performance tests
    tester = GRPCPerformanceTester()
    
    try:
        print("🚀 Starting gRPC VM Injection Performance Tests...")
        
        # Test VM creation performance
        tester.test_vm_creation_performance(10, "docker")
        tester.test_vm_creation_performance(5, "vagrant")
        
        # Test injection performance
        tester.test_injection_performance(15)
        
        # Test concurrent operations
        tester.test_concurrent_operations(5)
        
        # Test system limits
        limits = tester.test_system_limits()
        
        # Generate and display report
        report = tester.generate_performance_report()
        print(report)
        
        # Save report to file
        report_file = Path("performance_report.txt")
        with open(report_file, "w") as f:
            f.write(report)
        print(f"📄 Performance report saved to: {report_file}")
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        raise
    finally:
        print("🧹 Cleaning up test resources...")
        tester.cleanup_resources()
