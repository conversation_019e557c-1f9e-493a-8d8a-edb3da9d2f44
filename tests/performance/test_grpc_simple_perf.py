#!/usr/bin/env python3
"""
Simple Performance Tests for gRPC VM Injection System

Basic performance testing using curl for HTTP calls.
"""

import json
import subprocess
import statistics
import time
import uuid
from typing import List, Dict, Any


class SimplePerformanceTester:
    """Simple performance tester for gRPC VM injection system."""
    
    def __init__(self):
        self.api_base = "http://api.turdparty.localhost/api/v1"
        self.test_file_id = "902c5e5f-1109-4c1d-ba68-a50da622395d"
        self.test_vms = []
        
    def http_request(self, method: str, endpoint: str, data: Dict = None) -> Dict[str, Any]:
        """Make HTTP request using curl."""
        url = f"{self.api_base}{endpoint}"
        
        if method.upper() == "GET":
            cmd = ["curl", "-s", url]
        elif method.upper() == "POST":
            cmd = ["curl", "-s", "-X", "POST", "-H", "Content-Type: application/json"]
            if data:
                cmd.extend(["-d", json.dumps(data)])
            cmd.append(url)
        elif method.upper() == "DELETE":
            cmd = ["curl", "-s", "-X", "DELETE", url]
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        try:
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            duration = time.time() - start_time
            
            if result.returncode == 0 and result.stdout.strip():
                try:
                    return {"success": True, "data": json.loads(result.stdout), "duration": duration}
                except json.JSONDecodeError:
                    return {"success": True, "data": {"raw": result.stdout}, "duration": duration}
            else:
                return {"success": False, "error": result.stderr or "Request failed", "duration": duration}
        except subprocess.TimeoutExpired:
            return {"success": False, "error": "Request timeout", "duration": 30.0}
        except Exception as e:
            return {"success": False, "error": str(e), "duration": 0.0}
    
    def cleanup_resources(self):
        """Clean up test VMs."""
        for vm_id in self.test_vms:
            try:
                self.http_request("DELETE", f"/vms/{vm_id}")
            except Exception:
                pass
        self.test_vms.clear()
    
    def test_vm_creation_performance(self, vm_count: int = 5, vm_type: str = "docker") -> Dict[str, Any]:
        """Test VM creation performance."""
        print(f"\n📊 Testing {vm_type} VM creation performance ({vm_count} VMs)")
        print("-" * 60)
        
        times = []
        successes = 0
        failures = 0
        
        for i in range(vm_count):
            vm_config = {
                "name": f"perf-{vm_type}-{i}-{uuid.uuid4().hex[:8]}",
                "template": "ubuntu:20.04" if vm_type == "docker" else "10Baht/windows10-turdparty",
                "vm_type": vm_type,
                "memory_mb": 1024 if vm_type == "docker" else 4096,
                "cpus": 1 if vm_type == "docker" else 2,
                "disk_gb": 10 if vm_type == "docker" else 50,
                "description": f"Performance test {vm_type} VM {i}"
            }
            
            response = self.http_request("POST", "/vms/", vm_config)
            
            if response["success"]:
                vm_data = response["data"]
                self.test_vms.append(vm_data["vm_id"])
                times.append(response["duration"])
                successes += 1
                print(f"  ✅ VM {i+1}/{vm_count} created in {response['duration']:.2f}s")
            else:
                failures += 1
                print(f"  ❌ VM {i+1}/{vm_count} failed: {response['error']}")
        
        # Calculate metrics
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            total_time = sum(times)
            throughput = successes / total_time if total_time > 0 else 0
        else:
            avg_time = min_time = max_time = total_time = throughput = 0
        
        results = {
            "operation": f"{vm_type}_vm_creation",
            "total_operations": vm_count,
            "successful_operations": successes,
            "failed_operations": failures,
            "total_time": total_time,
            "average_time": avg_time,
            "min_time": min_time,
            "max_time": max_time,
            "throughput_ops_per_sec": throughput
        }
        
        print(f"  📈 Results: {successes}/{vm_count} successful, avg: {avg_time:.2f}s, throughput: {throughput:.2f} ops/sec")
        return results
    
    def test_injection_performance(self, injection_count: int = 10) -> Dict[str, Any]:
        """Test file injection performance."""
        print(f"\n💉 Testing file injection performance ({injection_count} injections)")
        print("-" * 60)
        
        # Ensure we have enough VMs
        min_vms_needed = min(injection_count, 5)
        if len(self.test_vms) < min_vms_needed:
            print(f"  Creating {min_vms_needed} VMs for injection testing...")
            self.test_vm_creation_performance(min_vms_needed, "docker")
        
        # Wait for VMs to be ready
        ready_vms = []
        for vm_id in self.test_vms[:min_vms_needed]:
            # Quick check if VM is ready
            response = self.http_request("GET", f"/vms/{vm_id}")
            if response["success"] and response["data"].get("status") == "running":
                ready_vms.append(vm_id)
        
        if not ready_vms:
            print("  ❌ No ready VMs available for injection testing")
            return {"error": "No ready VMs"}
        
        print(f"  Using {len(ready_vms)} ready VMs")
        
        times = []
        successes = 0
        failures = 0
        
        for i in range(injection_count):
            vm_id = ready_vms[i % len(ready_vms)]  # Round-robin
            
            injection_config = {
                "file_id": self.test_file_id,
                "injection_path": f"/tmp/perf-test-{i}.exe",
                "execute_after_injection": False,  # Skip execution for performance
                "permissions": "0755"
            }
            
            response = self.http_request("POST", f"/vms/{vm_id}/inject", injection_config)
            
            if response["success"]:
                times.append(response["duration"])
                successes += 1
                print(f"  ✅ Injection {i+1}/{injection_count} queued in {response['duration']:.2f}s")
            else:
                failures += 1
                print(f"  ❌ Injection {i+1}/{injection_count} failed: {response['error']}")
        
        # Calculate metrics
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            total_time = sum(times)
            throughput = successes / total_time if total_time > 0 else 0
        else:
            avg_time = min_time = max_time = total_time = throughput = 0
        
        results = {
            "operation": "file_injection",
            "total_operations": injection_count,
            "successful_operations": successes,
            "failed_operations": failures,
            "total_time": total_time,
            "average_time": avg_time,
            "min_time": min_time,
            "max_time": max_time,
            "throughput_ops_per_sec": throughput
        }
        
        print(f"  📈 Results: {successes}/{injection_count} successful, avg: {avg_time:.2f}s, throughput: {throughput:.2f} ops/sec")
        return results
    
    def test_system_responsiveness(self) -> Dict[str, Any]:
        """Test system responsiveness under load."""
        print(f"\n⚡ Testing system responsiveness")
        print("-" * 60)
        
        # Test API response times
        response_times = []
        
        for i in range(10):
            response = self.http_request("GET", "/vms/")
            if response["success"]:
                response_times.append(response["duration"])
                print(f"  ✅ API call {i+1}/10: {response['duration']:.3f}s")
            else:
                print(f"  ❌ API call {i+1}/10 failed")
        
        if response_times:
            avg_response = statistics.mean(response_times)
            max_response = max(response_times)
            min_response = min(response_times)
        else:
            avg_response = max_response = min_response = 0
        
        results = {
            "operation": "api_responsiveness",
            "total_calls": 10,
            "successful_calls": len(response_times),
            "average_response_time": avg_response,
            "min_response_time": min_response,
            "max_response_time": max_response
        }
        
        print(f"  📈 API responsiveness: avg {avg_response:.3f}s, min {min_response:.3f}s, max {max_response:.3f}s")
        return results
    
    def generate_performance_report(self, results: List[Dict[str, Any]]) -> str:
        """Generate performance report."""
        report = "\n" + "="*80 + "\n"
        report += "🎯 gRPC VM INJECTION SYSTEM - PERFORMANCE REPORT\n"
        report += "="*80 + "\n"
        
        for result in results:
            if "error" in result:
                continue
                
            report += f"\n📊 {result['operation'].upper().replace('_', ' ')}:\n"
            report += f"  Total Operations: {result.get('total_operations', 'N/A')}\n"
            
            if 'successful_operations' in result:
                success_rate = result['successful_operations'] / result['total_operations'] * 100
                report += f"  Successful: {result['successful_operations']} ({success_rate:.1f}%)\n"
                report += f"  Failed: {result.get('failed_operations', 0)}\n"
            
            if 'average_time' in result:
                report += f"  Average Time: {result['average_time']:.3f}s\n"
                report += f"  Min/Max Time: {result['min_time']:.3f}s / {result['max_time']:.3f}s\n"
            
            if 'throughput_ops_per_sec' in result:
                report += f"  Throughput: {result['throughput_ops_per_sec']:.2f} ops/sec\n"
            
            if 'average_response_time' in result:
                report += f"  Average Response: {result['average_response_time']:.3f}s\n"
            
            report += "-" * 50 + "\n"
        
        return report
    
    def run_performance_tests(self) -> bool:
        """Run all performance tests."""
        print("🚀 Starting gRPC VM Injection Performance Tests")
        print("=" * 80)
        
        results = []
        
        try:
            # Test VM creation performance
            results.append(self.test_vm_creation_performance(5, "docker"))
            results.append(self.test_vm_creation_performance(3, "vagrant"))
            
            # Test injection performance
            results.append(self.test_injection_performance(8))
            
            # Test system responsiveness
            results.append(self.test_system_responsiveness())
            
            # Generate report
            report = self.generate_performance_report(results)
            print(report)
            
            # Save report
            with open("performance_report.txt", "w") as f:
                f.write(report)
            print("📄 Performance report saved to: performance_report.txt")
            
            return True
            
        except Exception as e:
            print(f"❌ Performance test failed: {e}")
            import traceback
            traceback.print_exc()
            return False


if __name__ == "__main__":
    tester = SimplePerformanceTester()
    
    try:
        success = tester.run_performance_tests()
        exit_code = 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Performance tests interrupted by user")
        exit_code = 130
    except Exception as e:
        print(f"\n❌ Performance test suite failed: {e}")
        exit_code = 1
    finally:
        print("\n🧹 Cleaning up test resources...")
        tester.cleanup_resources()
    
    exit(exit_code)
