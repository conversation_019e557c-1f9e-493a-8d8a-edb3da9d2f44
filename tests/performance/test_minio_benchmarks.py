"""
MinIO Performance Benchmarks for TurdParty
Tests file upload/download speeds, storage operations, and throughput
"""
import time
import json
import logging
import statistics
import requests
import hashlib
import os
from datetime import datetime
from typing import Dict, List, Any
import concurrent.futures
import threading

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test configuration
API_BASE_URL = "http://localhost:8000"
API_V1_BASE = f"{API_BASE_URL}/api/v1"

class MinIOBenchmarkTests:
    """MinIO performance benchmark test suite"""
    
    def __init__(self):
        self.session = requests.Session()
        
        # Benchmark results storage
        self.benchmark_results = {
            'file_upload_performance': [],
            'file_download_performance': [],
            'concurrent_operations': [],
            'storage_throughput': [],
            'file_size_scaling': [],
            'test_metadata': {
                'timestamp': datetime.now(datetime.UTC).isoformat(),
                'api_endpoint': API_BASE_URL
            }
        }
        
        # Test file storage
        self.test_files = []
    
    def benchmark_file_upload_performance(self, iterations: int = 5):
        """Benchmark file upload performance with different file sizes"""
        logger.info(f"📤 Benchmarking file upload performance ({iterations} iterations)...")
        
        # Test different file sizes
        file_sizes = [
            (1024, "1KB"),           # Small files
            (10 * 1024, "10KB"),     # Medium files  
            (100 * 1024, "100KB"),   # Large files
            (1024 * 1024, "1MB"),    # Very large files
            (5 * 1024 * 1024, "5MB") # Extra large files
        ]
        
        for file_size, size_label in file_sizes:
            upload_times = []
            
            logger.info(f"Testing {size_label} files...")
            
            for i in range(iterations):
                # Create test file
                test_content = self._generate_test_content(file_size)
                test_filename = f"benchmark_{size_label}_{i}.bin"
                
                # Measure upload time
                start_time = time.time()
                file_id = self._upload_test_file(test_filename, test_content)
                upload_time = time.time() - start_time
                
                if file_id:
                    upload_times.append(upload_time)
                    self.test_files.append(file_id)
                    
                    # Calculate throughput
                    throughput_mbps = (file_size / (1024 * 1024)) / upload_time
                    logger.info(f"  Upload {i+1}: {upload_time:.3f}s ({throughput_mbps:.2f} MB/s)")
                else:
                    logger.warning(f"Failed to upload {test_filename}")
            
            if upload_times:
                avg_time = statistics.mean(upload_times)
                avg_throughput = (file_size / (1024 * 1024)) / avg_time
                
                result = {
                    'file_size_bytes': file_size,
                    'file_size_label': size_label,
                    'iterations': len(upload_times),
                    'avg_upload_time': avg_time,
                    'min_upload_time': min(upload_times),
                    'max_upload_time': max(upload_times),
                    'avg_throughput_mbps': avg_throughput,
                    'all_times': upload_times
                }
                
                self.benchmark_results['file_upload_performance'].append(result)
                
                logger.info(f"✅ {size_label}: Avg={avg_time:.3f}s, Throughput={avg_throughput:.2f} MB/s")
    
    def benchmark_file_download_performance(self):
        """Benchmark file download performance"""
        logger.info("📥 Benchmarking file download performance...")
        
        if not self.test_files:
            logger.warning("No test files available for download benchmarking")
            return
        
        download_times = []
        
        for file_id in self.test_files[:10]:  # Test first 10 files
            try:
                # Get download URL
                start_time = time.time()
                response = self.session.get(f"{API_V1_BASE}/files/{file_id}/download-url")
                
                if response.status_code == 200:
                    download_data = response.json()
                    download_url = download_data.get('download_url')
                    
                    if download_url:
                        # Download file
                        download_response = requests.get(download_url)
                        download_time = time.time() - start_time
                        
                        if download_response.status_code == 200:
                            download_times.append(download_time)
                            file_size = len(download_response.content)
                            throughput = (file_size / (1024 * 1024)) / download_time
                            
                            logger.info(f"  Download: {download_time:.3f}s ({throughput:.2f} MB/s)")
                
            except Exception as e:
                logger.warning(f"Download failed for {file_id}: {e}")
        
        if download_times:
            result = {
                'files_tested': len(download_times),
                'avg_download_time': statistics.mean(download_times),
                'min_download_time': min(download_times),
                'max_download_time': max(download_times),
                'all_times': download_times
            }
            
            self.benchmark_results['file_download_performance'].append(result)
            
            logger.info(f"✅ Download: Avg={result['avg_download_time']:.3f}s")
    
    def benchmark_concurrent_operations(self, concurrent_uploads: int = 5):
        """Benchmark concurrent file operations"""
        logger.info(f"🔄 Benchmarking concurrent operations ({concurrent_uploads} parallel uploads)...")
        
        # Prepare test files
        test_files_data = []
        for i in range(concurrent_uploads):
            content = self._generate_test_content(50 * 1024)  # 50KB files
            filename = f"concurrent_test_{i}.bin"
            test_files_data.append((filename, content))
        
        # Test concurrent uploads
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_uploads) as executor:
            # Submit all upload tasks
            future_to_file = {
                executor.submit(self._upload_test_file, filename, content): filename
                for filename, content in test_files_data
            }
            
            # Collect results
            upload_results = []
            for future in concurrent.futures.as_completed(future_to_file):
                filename = future_to_file[future]
                try:
                    file_id = future.result()
                    if file_id:
                        upload_results.append(file_id)
                        self.test_files.append(file_id)
                except Exception as e:
                    logger.warning(f"Concurrent upload failed for {filename}: {e}")
        
        total_time = time.time() - start_time
        
        result = {
            'concurrent_uploads': concurrent_uploads,
            'successful_uploads': len(upload_results),
            'total_time': total_time,
            'avg_time_per_file': total_time / len(upload_results) if upload_results else 0,
            'files_per_second': len(upload_results) / total_time if total_time > 0 else 0
        }
        
        self.benchmark_results['concurrent_operations'].append(result)
        
        logger.info(f"✅ Concurrent: {len(upload_results)}/{concurrent_uploads} uploads in {total_time:.2f}s "
                   f"({result['files_per_second']:.2f} files/s)")
    
    def benchmark_storage_throughput(self):
        """Benchmark overall storage throughput"""
        logger.info("📊 Benchmarking storage throughput...")
        
        # Test sustained throughput with multiple file sizes
        throughput_tests = [
            (10, 10 * 1024, "10KB x 10"),      # Many small files
            (5, 100 * 1024, "100KB x 5"),     # Medium files
            (2, 1024 * 1024, "1MB x 2")       # Large files
        ]
        
        for file_count, file_size, test_label in throughput_tests:
            logger.info(f"Testing {test_label}...")
            
            total_bytes = file_count * file_size
            start_time = time.time()
            
            successful_uploads = 0
            for i in range(file_count):
                content = self._generate_test_content(file_size)
                filename = f"throughput_{test_label.replace(' ', '_')}_{i}.bin"
                
                file_id = self._upload_test_file(filename, content)
                if file_id:
                    successful_uploads += 1
                    self.test_files.append(file_id)
            
            total_time = time.time() - start_time
            actual_bytes = successful_uploads * file_size
            throughput_mbps = (actual_bytes / (1024 * 1024)) / total_time if total_time > 0 else 0
            
            result = {
                'test_label': test_label,
                'file_count': file_count,
                'file_size_bytes': file_size,
                'successful_uploads': successful_uploads,
                'total_bytes': actual_bytes,
                'total_time': total_time,
                'throughput_mbps': throughput_mbps
            }
            
            self.benchmark_results['storage_throughput'].append(result)
            
            logger.info(f"✅ {test_label}: {throughput_mbps:.2f} MB/s "
                       f"({successful_uploads}/{file_count} files)")
    
    def benchmark_file_size_scaling(self):
        """Benchmark how performance scales with file size"""
        logger.info("📈 Benchmarking file size scaling...")
        
        # Test exponentially increasing file sizes
        base_sizes = [1024, 2048, 4096, 8192, 16384, 32768]  # 1KB to 32KB
        
        for size in base_sizes:
            size_label = f"{size//1024}KB" if size >= 1024 else f"{size}B"
            
            # Test 3 files of each size
            times = []
            for i in range(3):
                content = self._generate_test_content(size)
                filename = f"scaling_{size_label}_{i}.bin"
                
                start_time = time.time()
                file_id = self._upload_test_file(filename, content)
                upload_time = time.time() - start_time
                
                if file_id:
                    times.append(upload_time)
                    self.test_files.append(file_id)
            
            if times:
                avg_time = statistics.mean(times)
                throughput = (size / (1024 * 1024)) / avg_time
                
                result = {
                    'file_size_bytes': size,
                    'file_size_label': size_label,
                    'avg_upload_time': avg_time,
                    'throughput_mbps': throughput,
                    'samples': len(times)
                }
                
                self.benchmark_results['file_size_scaling'].append(result)
                
                logger.info(f"✅ {size_label}: {avg_time:.3f}s ({throughput:.3f} MB/s)")
    
    def _generate_test_content(self, size: int) -> bytes:
        """Generate test content of specified size"""
        # Create pseudo-random content for realistic testing
        content = bytearray()
        pattern = b"TurdParty-Benchmark-Test-Data-"
        
        while len(content) < size:
            content.extend(pattern)
        
        return bytes(content[:size])
    
    def _upload_test_file(self, filename: str, content: bytes) -> str:
        """Upload a test file and return file ID"""
        try:
            files = {'file': (filename, content, 'application/octet-stream')}
            data = {'description': f'Benchmark test file: {filename}'}
            
            # Remove Content-Type header for multipart upload
            headers = {k: v for k, v in self.session.headers.items() 
                      if k.lower() != 'content-type'}
            
            response = requests.post(
                f"{API_V1_BASE}/files/upload",
                files=files,
                data=data,
                headers=headers
            )
            
            if response.status_code == 200:
                return response.json().get('file_id')
                
        except Exception as e:
            logger.error(f"Upload failed for {filename}: {e}")
        
        return None
    
    def cleanup_test_files(self):
        """Clean up test files (optional - files will be cleaned up by system)"""
        logger.info(f"🗑️ Cleaning up {len(self.test_files)} test files...")
        
        # Note: The API doesn't have a delete endpoint for files
        # Files are managed by the system and cleaned up automatically
        # This is just for logging purposes
        
        self.test_files.clear()
        logger.info("✅ Test file cleanup completed")
    
    def save_benchmark_results(self, filename: str = None):
        """Save benchmark results to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"minio_benchmark_results_{timestamp}.json"
        
        filepath = f"tests/data/{filename}"
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'w') as f:
            json.dump(self.benchmark_results, f, indent=2)
        
        logger.info(f"📊 Benchmark results saved to {filepath}")
        return filepath
    
    def print_summary(self):
        """Print benchmark summary"""
        logger.info("\n" + "="*60)
        logger.info("🎯 MINIO BENCHMARK SUMMARY")
        logger.info("="*60)
        
        # Upload Performance
        if self.benchmark_results['file_upload_performance']:
            logger.info("\n📤 File Upload Performance:")
            for result in self.benchmark_results['file_upload_performance']:
                logger.info(f"  {result['file_size_label']}: {result['avg_throughput_mbps']:.2f} MB/s avg")
        
        # Download Performance
        if self.benchmark_results['file_download_performance']:
            logger.info("\n📥 File Download Performance:")
            for result in self.benchmark_results['file_download_performance']:
                logger.info(f"  {result['files_tested']} files: {result['avg_download_time']:.3f}s avg")
        
        # Concurrent Operations
        if self.benchmark_results['concurrent_operations']:
            logger.info("\n🔄 Concurrent Operations:")
            for result in self.benchmark_results['concurrent_operations']:
                logger.info(f"  {result['concurrent_uploads']} parallel: {result['files_per_second']:.2f} files/s")
        
        # Storage Throughput
        if self.benchmark_results['storage_throughput']:
            logger.info("\n📊 Storage Throughput:")
            for result in self.benchmark_results['storage_throughput']:
                logger.info(f"  {result['test_label']}: {result['throughput_mbps']:.2f} MB/s")

def run_minio_benchmarks():
    """Run complete MinIO benchmark suite"""
    logger.info("🎯 Starting MinIO Performance Benchmarks...")
    
    benchmarks = MinIOBenchmarkTests()
    
    try:
        # Run all benchmark tests
        benchmarks.benchmark_file_upload_performance(iterations=3)
        benchmarks.benchmark_file_download_performance()
        benchmarks.benchmark_concurrent_operations(concurrent_uploads=3)
        benchmarks.benchmark_storage_throughput()
        benchmarks.benchmark_file_size_scaling()
        
        # Save and display results
        results_file = benchmarks.save_benchmark_results()
        benchmarks.print_summary()
        
        # Cleanup
        benchmarks.cleanup_test_files()
        
        logger.info(f"\n🎉 MinIO benchmarks completed! Results saved to {results_file}")
        
    except Exception as e:
        logger.error(f"❌ Benchmark failed: {e}")
        raise

if __name__ == "__main__":
    run_minio_benchmarks()
