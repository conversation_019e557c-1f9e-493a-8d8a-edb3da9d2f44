# Testing dependencies for TurdParty
# Comprehensive testing framework requirements

# Core testing frameworks
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-html>=3.2.0
pytest-xdist>=3.3.0
pytest-mock>=3.11.0
pytest-timeout>=2.1.0

# BDD testing
behave>=1.2.6
behave-html-formatter>=0.9.10

# E2E testing
playwright>=1.37.0
pytest-playwright>=0.4.0

# API testing
requests>=2.31.0
httpx>=0.24.0
responses>=0.23.0

# Database testing
pytest-postgresql>=5.0.0
sqlalchemy-utils>=0.41.0

# Mocking and fixtures
factory-boy>=3.3.0
faker>=19.0.0
freezegun>=1.2.0

# Performance testing
locust>=2.16.0
pytest-benchmark>=4.0.0

# Code quality
ruff>=0.0.280
mypy>=1.5.0
black>=23.7.0
isort>=5.12.0

# Coverage and reporting
coverage>=7.3.0
coverage-badge>=1.1.0

# Utilities
pydantic>=2.0.0
python-dotenv>=1.0.0
click>=8.1.0

# MinIO testing
minio>=7.1.0

# Elasticsearch testing
elasticsearch>=8.9.0
elasticsearch-dsl>=8.9.0

# Redis testing
redis>=4.6.0
fakeredis>=2.17.0

# Celery testing
celery>=5.3.0

# Docker testing
docker>=6.1.0
testcontainers>=3.7.0

# Logging
loguru>=0.7.0
python-json-logger>=2.0.0

# Date/time utilities
python-dateutil>=2.8.0
pytz>=2023.3

# File handling
pathlib2>=2.3.7
tempfile-utils>=0.1.0

# Network testing
aiohttp>=3.8.0
websockets>=11.0.0

# Security testing
bandit>=1.7.0
safety>=2.3.0

# Documentation testing
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0

# Development utilities
ipython>=8.14.0
ipdb>=0.13.0
rich>=13.5.0
