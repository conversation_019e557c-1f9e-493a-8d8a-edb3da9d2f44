"""
Real API Layer Tests - No Mocks

This module tests API layer functionality using real service integration
with central service URL management, providing reliable end-to-end testing.
"""

import asyncio
import json
import pytest
import time
import uuid
from typing import Dict, Any


class TestRealAPIServiceIntegration:
    """Test API service integration with real backend services"""

    @pytest.mark.asyncio
    async def test_api_health_with_real_services(self, real_service_integration):
        """Test API health endpoint with real service dependencies"""
        integration = real_service_integration
        api_client = integration["api_client"]
        
        # Test basic health endpoint
        response = await api_client.get("/health")
        
        # Should return health status
        assert response.status_code in [200, 503]  # 503 if dependencies not ready
        data = response.json()
        assert "status" in data
        assert "service" in data
        
        if response.status_code == 200:
            assert data["status"] in ["healthy", "degraded"]
            assert data["service"] == "turdparty-api"

    @pytest.mark.asyncio
    async def test_api_service_discovery(self, real_service_integration):
        """Test API service discovery and URL resolution"""
        integration = real_service_integration
        service_urls = integration["service_urls"]
        
        # Verify service URLs are properly configured
        assert "api" in service_urls
        assert "minio" in service_urls
        assert service_urls["api"].startswith("http")
        assert service_urls["minio"].startswith("http")
        
        # Test API base URL accessibility
        api_client = integration["api_client"]
        response = await api_client.get("/")
        
        # Should get some response (even 404 is fine, means service is running)
        assert response.status_code in [200, 404, 405]

    @pytest.mark.asyncio
    async def test_api_openapi_schema_real(self, real_service_integration):
        """Test OpenAPI schema generation with real API"""
        integration = real_service_integration
        api_client = integration["api_client"]
        
        # Get OpenAPI schema
        response = await api_client.get("/openapi.json")
        
        if response.status_code == 200:
            schema = response.json()
            
            # Verify schema structure
            assert "openapi" in schema
            assert "info" in schema
            assert "paths" in schema
            
            # Check for expected API endpoints
            paths = schema["paths"]
            expected_paths = ["/health", "/docs", "/redoc"]
            
            for path in expected_paths:
                if path in paths:
                    assert isinstance(paths[path], dict)

    @pytest.mark.asyncio
    async def test_api_cors_headers_real(self, real_service_integration):
        """Test CORS headers with real API"""
        integration = real_service_integration
        api_client = integration["api_client"]
        
        # Test OPTIONS request for CORS
        response = await api_client.options("/health")
        
        if response.status_code in [200, 204]:
            # Check for CORS headers
            headers = response.headers
            cors_headers = [
                "access-control-allow-origin",
                "access-control-allow-methods",
                "access-control-allow-headers"
            ]
            
            # At least some CORS headers should be present
            cors_present = any(header in headers for header in cors_headers)
            # Note: This might not be present if CORS is not configured, which is OK


class TestRealAPIEndpoints:
    """Test specific API endpoints with real services"""

    @pytest.mark.asyncio
    async def test_files_api_integration_real(self, real_service_integration):
        """Test files API with real MinIO backend"""
        integration = real_service_integration
        api_client = integration["api_client"]
        
        # Test files list endpoint
        response = await api_client.get("/api/v1/files/")
        
        # Should return files list (even if empty)
        if response.status_code == 200:
            data = response.json()
            assert isinstance(data, (list, dict))
            
            if isinstance(data, dict):
                # Might be paginated response
                assert "files" in data or "items" in data or len(data) >= 0

    @pytest.mark.asyncio
    async def test_vms_api_integration_real(self, real_service_integration):
        """Test VMs API with real Docker backend"""
        integration = real_service_integration
        api_client = integration["api_client"]
        
        # Test VMs list endpoint
        response = await api_client.get("/api/v1/vms/")
        
        # Should return VMs list (even if empty)
        if response.status_code == 200:
            data = response.json()
            assert isinstance(data, (list, dict))
            
            if isinstance(data, dict):
                # Might be paginated response
                assert "vms" in data or "items" in data or len(data) >= 0

    @pytest.mark.asyncio
    async def test_health_endpoints_real(self, real_service_integration):
        """Test health endpoints with real service dependencies"""
        integration = real_service_integration
        api_client = integration["api_client"]
        
        # Test various health endpoints
        health_endpoints = [
            "/health",
            "/api/v1/health",
            "/api/v1/health/minio",
            "/api/v1/health/elasticsearch",
            "/api/v1/health/database"
        ]
        
        for endpoint in health_endpoints:
            response = await api_client.get(endpoint)
            
            # Health endpoints should exist and return proper status
            if response.status_code in [200, 503]:
                data = response.json()
                assert "status" in data or "health" in data or "state" in data

    @pytest.mark.asyncio
    async def test_api_error_handling_real(self, real_service_integration):
        """Test API error handling with real services"""
        integration = real_service_integration
        api_client = integration["api_client"]
        
        # Test 404 for non-existent endpoint
        response = await api_client.get("/api/v1/nonexistent")
        assert response.status_code == 404
        
        # Test 405 for wrong method
        response = await api_client.patch("/health")
        assert response.status_code == 405
        
        # Test validation error with invalid data
        response = await api_client.post(
            "/api/v1/files/upload",
            json={"invalid": "data"}
        )
        assert response.status_code in [400, 422, 405]  # Various validation errors


class TestRealAPIWorkflows:
    """Test complete API workflows with real services"""

    @pytest.mark.asyncio
    async def test_file_upload_workflow_real(self, real_service_integration):
        """Test complete file upload workflow with real services"""
        integration = real_service_integration
        api_client = integration["api_client"]
        
        # Prepare test file
        test_content = b"#!/bin/bash\necho 'Real API workflow test'\nexit 0\n"
        filename = f"api_test_{int(time.time())}.sh"
        
        # Test file upload
        files = {"file": (filename, test_content, "application/x-sh")}
        response = await api_client.post("/api/v1/files/upload", files=files)
        
        if response.status_code == 201:
            upload_data = response.json()
            
            # Verify upload response
            assert "file_id" in upload_data or "id" in upload_data
            assert "filename" in upload_data or "name" in upload_data
            
            file_id = upload_data.get("file_id") or upload_data.get("id")
            
            # Test file retrieval
            if file_id:
                response = await api_client.get(f"/api/v1/files/{file_id}")
                
                if response.status_code == 200:
                    file_data = response.json()
                    assert file_data["id"] == file_id or file_data["file_id"] == file_id

    @pytest.mark.asyncio
    async def test_vm_creation_workflow_real(self, real_service_integration):
        """Test VM creation workflow with real Docker backend"""
        integration = real_service_integration
        api_client = integration["api_client"]
        
        # Prepare VM creation request
        vm_data = {
            "name": f"api_test_vm_{int(time.time())}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 256,
            "cpus": 1,
            "domain": "TurdParty"
        }
        
        # Test VM creation
        response = await api_client.post("/api/v1/vms/", json=vm_data)
        
        if response.status_code == 201:
            vm_response = response.json()
            
            # Verify VM creation response
            assert "vm_id" in vm_response or "id" in vm_response
            assert "name" in vm_response
            assert vm_response["name"] == vm_data["name"]
            
            vm_id = vm_response.get("vm_id") or vm_response.get("id")
            
            try:
                # Test VM status retrieval
                if vm_id:
                    response = await api_client.get(f"/api/v1/vms/{vm_id}")
                    
                    if response.status_code == 200:
                        vm_status = response.json()
                        assert vm_status["vm_id"] == vm_id or vm_status["id"] == vm_id
                        assert "status" in vm_status
                
            finally:
                # Cleanup: Delete the VM
                if vm_id:
                    await api_client.delete(f"/api/v1/vms/{vm_id}?force=true")

    @pytest.mark.asyncio
    async def test_service_integration_workflow_real(self, real_service_integration):
        """Test complete service integration workflow"""
        integration = real_service_integration
        api_client = integration["api_client"]
        minio_client = integration["minio_client"]
        vm_service = integration["vm_service"]
        
        # Test that all services are accessible through API
        workflow_steps = []
        
        # Step 1: Check API health
        response = await api_client.get("/health")
        workflow_steps.append(("API Health", response.status_code in [200, 503]))
        
        # Step 2: Check MinIO through API
        response = await api_client.get("/api/v1/health/minio")
        if response.status_code in [200, 503]:
            workflow_steps.append(("MinIO Health", True))
        else:
            # Direct MinIO check as fallback
            try:
                buckets = minio_client.list_buckets()
                workflow_steps.append(("MinIO Direct", True))
            except:
                workflow_steps.append(("MinIO Direct", False))
        
        # Step 3: Check VM service integration
        try:
            await vm_service.initialize()
            workflow_steps.append(("VM Service", True))
        except:
            workflow_steps.append(("VM Service", False))
        
        # Verify at least some services are working
        working_services = sum(1 for _, status in workflow_steps if status)
        assert working_services >= 1, f"No services working: {workflow_steps}"


class TestRealAPIPerformance:
    """Test API performance with real services"""

    @pytest.mark.asyncio
    async def test_api_response_times_real(self, real_service_integration):
        """Test API response times with real services"""
        integration = real_service_integration
        api_client = integration["api_client"]
        
        # Test response times for various endpoints
        endpoints = [
            "/health",
            "/api/v1/files/",
            "/api/v1/vms/",
            "/openapi.json"
        ]
        
        response_times = []
        
        for endpoint in endpoints:
            start_time = time.time()
            response = await api_client.get(endpoint)
            response_time = time.time() - start_time
            
            if response.status_code in [200, 404, 503]:
                response_times.append((endpoint, response_time))
        
        # Verify reasonable response times (under 5 seconds)
        for endpoint, response_time in response_times:
            assert response_time < 5.0, f"{endpoint} took {response_time:.2f}s"

    @pytest.mark.asyncio
    async def test_concurrent_api_requests_real(self, real_service_integration):
        """Test concurrent API requests with real services"""
        integration = real_service_integration
        api_client = integration["api_client"]
        
        # Make concurrent requests to health endpoint
        async def make_request():
            response = await api_client.get("/health")
            return response.status_code
        
        # Execute 5 concurrent requests
        tasks = [make_request() for _ in range(5)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count successful responses
        successful = sum(1 for result in results if isinstance(result, int) and result in [200, 503])
        
        # At least 3 out of 5 should succeed
        assert successful >= 3, f"Only {successful}/5 concurrent requests succeeded"

    @pytest.mark.asyncio
    async def test_api_load_handling_real(self, real_service_integration):
        """Test API load handling with real services"""
        integration = real_service_integration
        api_client = integration["api_client"]
        
        # Test rapid sequential requests
        request_count = 10
        start_time = time.time()
        
        successful_requests = 0
        for i in range(request_count):
            try:
                response = await api_client.get("/health")
                if response.status_code in [200, 503]:
                    successful_requests += 1
            except Exception:
                pass  # Count as failed request
        
        total_time = time.time() - start_time
        
        # Verify reasonable performance
        assert successful_requests >= request_count * 0.8  # 80% success rate
        assert total_time < 30.0  # Should complete within 30 seconds
