"""
Unit Tests for gRPC Components

Tests individual components of the gRPC VM injection system
including VM type detection, gRPC client, and injection logic.
"""

import asyncio
import pytest
import tempfile
import uuid
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path
import sys
import os

# Add the services directory to the path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'services'))


class TestVMTypeDetection:
    """Test VM type detection logic."""
    
    def create_mock_vm_instance(self, template: str, vm_id: str):
        """Create a mock VM instance for testing."""
        mock_vm = Mock()
        mock_vm.template = template
        mock_vm.vm_id = vm_id
        mock_vm.name = f"test-vm-{uuid.uuid4().hex[:8]}"
        return mock_vm
    
    def test_docker_vm_detection(self):
        """Test detection of Docker VMs."""
        # Import the function we're testing
        try:
            from workers.tasks.injection_tasks import _is_vagrant_vm
        except ImportError:
            pytest.skip("Cannot import injection_tasks module")
        
        # Test Docker container ID (64 character hex)
        docker_vm = self.create_mock_vm_instance(
            "ubuntu:20.04", 
            "7a623736f17efb74e44c0db81fbd8ddb2163b8538589027b18b4997c7b65485f"
        )
        
        assert not _is_vagrant_vm(docker_vm), "Docker VM should not be detected as Vagrant"
    
    def test_vagrant_vm_detection_by_template(self):
        """Test detection of Vagrant VMs by template."""
        try:
            from workers.tasks.injection_tasks import _is_vagrant_vm
        except ImportError:
            pytest.skip("Cannot import injection_tasks module")
        
        test_cases = [
            "10Baht/windows10-turdparty",
            "gusztavvargadr/windows-10",
            "Microsoft/EdgeOnWindows10",
            "windows/server-2019"
        ]
        
        for template in test_cases:
            vagrant_vm = self.create_mock_vm_instance(template, str(uuid.uuid4()))
            assert _is_vagrant_vm(vagrant_vm), f"Template '{template}' should be detected as Vagrant"
    
    def test_vagrant_vm_detection_by_uuid(self):
        """Test detection of Vagrant VMs by UUID format."""
        try:
            from workers.tasks.injection_tasks import _is_vagrant_vm
        except ImportError:
            pytest.skip("Cannot import injection_tasks module")
        
        # Test UUID format (36 characters with 4 dashes)
        vagrant_vm = self.create_mock_vm_instance(
            "some-template", 
            "73503d21-f5f4-474f-b9f4-92cce82621d3"
        )
        
        assert _is_vagrant_vm(vagrant_vm), "UUID format VM ID should be detected as Vagrant"
    
    def test_edge_cases(self):
        """Test edge cases in VM type detection."""
        try:
            from workers.tasks.injection_tasks import _is_vagrant_vm
        except ImportError:
            pytest.skip("Cannot import injection_tasks module")
        
        # Test None values
        mock_vm = Mock()
        mock_vm.template = None
        mock_vm.vm_id = None
        assert not _is_vagrant_vm(mock_vm), "VM with None values should default to Docker"
        
        # Test empty strings
        mock_vm.template = ""
        mock_vm.vm_id = ""
        assert not _is_vagrant_vm(mock_vm), "VM with empty values should default to Docker"
        
        # Test missing attributes
        mock_vm = Mock()
        del mock_vm.template
        del mock_vm.vm_id
        assert not _is_vagrant_vm(mock_vm), "VM with missing attributes should default to Docker"


class TestGRPCClient:
    """Test gRPC client functionality."""
    
    @pytest.fixture
    def mock_grpc_client(self):
        """Create a mock gRPC client for testing."""
        try:
            from grpc.vm_client import VMGRPCClient
            return VMGRPCClient("localhost:40000")
        except ImportError:
            pytest.skip("Cannot import gRPC client module")
    
    @pytest.mark.asyncio
    async def test_grpc_client_connection(self, mock_grpc_client):
        """Test gRPC client connection logic."""
        # Mock the socket connection
        with patch('socket.socket') as mock_socket:
            mock_sock_instance = Mock()
            mock_socket.return_value = mock_sock_instance
            mock_sock_instance.connect_ex.return_value = 0  # Success
            
            result = await mock_grpc_client.connect()
            assert result is True, "Connection should succeed when port is open"
            
            # Test connection failure
            mock_sock_instance.connect_ex.return_value = 1  # Failure
            result = await mock_grpc_client.connect()
            assert result is False, "Connection should fail when port is closed"
    
    @pytest.mark.asyncio
    async def test_grpc_vm_creation(self, mock_grpc_client):
        """Test gRPC VM creation simulation."""
        vm_config = {
            "name": "test-vm",
            "template": "10Baht/windows10-turdparty",
            "memory_mb": 4096,
            "cpus": 2,
            "disk_gb": 50
        }
        
        # Mock successful connection
        mock_grpc_client._connected = True
        
        result = await mock_grpc_client.create_vm(vm_config)
        
        assert result["success"] is True, "VM creation should succeed"
        assert result["vm_name"] == "test-vm", "VM name should match config"
        assert result["template"] == "10Baht/windows10-turdparty", "Template should match config"
        assert "vm_id" in result, "Result should contain VM ID"
        assert result["provider"] == "vagrant-grpc", "Provider should be vagrant-grpc"
    
    @pytest.mark.asyncio
    async def test_grpc_file_injection(self, mock_grpc_client):
        """Test gRPC file injection simulation."""
        vm_id = str(uuid.uuid4())
        file_path = "/tmp/test-file"
        target_path = "C:\\TurdParty\\test.exe"
        
        # Mock successful connection
        mock_grpc_client._connected = True
        
        result = await mock_grpc_client.inject_file(
            vm_id=vm_id,
            file_path=file_path,
            target_path=target_path,
            permissions="0755",
            execute=True
        )
        
        assert result["success"] is True, "File injection should succeed"
        assert result["vm_id"] == vm_id, "VM ID should match"
        assert result["target_path"] == target_path, "Target path should match"
        assert result["executed"] is True, "Execute flag should be set"
        assert "injection_id" in result, "Result should contain injection ID"


class TestInjectionLogic:
    """Test file injection logic and routing."""
    
    def create_mock_vm_instance(self, vm_type: str):
        """Create mock VM instance for injection testing."""
        mock_vm = Mock()
        if vm_type == "docker":
            mock_vm.template = "ubuntu:20.04"
            mock_vm.vm_id = "7a623736f17efb74e44c0db81fbd8ddb2163b8538589027b18b4997c7b65485f"
            mock_vm.name = "test-docker-vm"
        else:  # vagrant
            mock_vm.template = "10Baht/windows10-turdparty"
            mock_vm.vm_id = "73503d21-f5f4-474f-b9f4-92cce82621d3"
            mock_vm.name = "test-vagrant-vm"
            mock_vm.ip_address = "*************"
        
        return mock_vm
    
    def test_injection_routing_docker(self):
        """Test that Docker VMs use Docker injection method."""
        try:
            from workers.tasks.injection_tasks import _inject_file_into_vm, _is_vagrant_vm
        except ImportError:
            pytest.skip("Cannot import injection_tasks module")
        
        docker_vm = self.create_mock_vm_instance("docker")
        assert not _is_vagrant_vm(docker_vm), "Should be detected as Docker VM"
        
        # Mock the Docker injection function
        with patch('workers.tasks.injection_tasks._inject_file_via_docker') as mock_docker_inject:
            mock_docker_inject.return_value = {"success": True, "method": "docker"}
            
            file_content = b"test file content"
            filename = "test.exe"
            injection_config = {"target_path": "/tmp/test.exe", "permissions": "0755"}
            
            result = _inject_file_into_vm(docker_vm, file_content, filename, injection_config)
            
            mock_docker_inject.assert_called_once()
            assert result["method"] == "docker", "Should use Docker injection method"
    
    def test_injection_routing_vagrant(self):
        """Test that Vagrant VMs use gRPC injection method."""
        try:
            from workers.tasks.injection_tasks import _inject_file_into_vm, _is_vagrant_vm
        except ImportError:
            pytest.skip("Cannot import injection_tasks module")
        
        vagrant_vm = self.create_mock_vm_instance("vagrant")
        assert _is_vagrant_vm(vagrant_vm), "Should be detected as Vagrant VM"
        
        # Mock the gRPC injection function
        with patch('workers.tasks.injection_tasks._inject_file_via_grpc') as mock_grpc_inject:
            mock_grpc_inject.return_value = {"success": True, "method": "grpc"}
            
            file_content = b"test file content"
            filename = "test.exe"
            injection_config = {"target_path": "C:\\TurdParty\\test.exe", "permissions": "0755"}
            
            result = _inject_file_into_vm(vagrant_vm, file_content, filename, injection_config)
            
            mock_grpc_inject.assert_called_once()
            assert result["method"] == "grpc", "Should use gRPC injection method"
    
    def test_grpc_fallback_to_ssh(self):
        """Test gRPC fallback to SSH when gRPC client unavailable."""
        try:
            from workers.tasks.injection_tasks import _inject_file_via_grpc
        except ImportError:
            pytest.skip("Cannot import injection_tasks module")
        
        vagrant_vm = self.create_mock_vm_instance("vagrant")
        file_content = b"test file content"
        filename = "test.exe"
        injection_config = {"target_path": "C:\\TurdParty\\test.exe", "permissions": "0755"}
        
        # Mock ImportError to simulate missing gRPC client
        with patch('workers.tasks.injection_tasks._inject_file_via_ssh') as mock_ssh_inject:
            mock_ssh_inject.return_value = {"success": True, "method": "ssh_fallback"}
            
            # This should trigger the ImportError path and fall back to SSH
            result = _inject_file_via_grpc(vagrant_vm, file_content, filename, injection_config)
            
            # The function should fall back to SSH injection
            assert result["method"] == "ssh_fallback", "Should fall back to SSH injection"


class TestConfigurationManagement:
    """Test configuration management for gRPC system."""
    
    def test_vagrant_config_loading(self):
        """Test Vagrant configuration loading."""
        try:
            from api.src.config.vagrant import get_vagrant_config
            config = get_vagrant_config()
            
            assert hasattr(config, 'grpc_endpoint'), "Config should have gRPC endpoint"
            assert hasattr(config, 'grpc_port'), "Config should have gRPC port"
            assert hasattr(config, 'mode'), "Config should have mode"
            
            # Test endpoint format
            endpoint = config.grpc_endpoint
            assert ":" in endpoint, "Endpoint should contain port separator"
            
            host, port = endpoint.split(":")
            assert len(host) > 0, "Host should not be empty"
            assert port.isdigit(), "Port should be numeric"
            
        except ImportError:
            pytest.skip("Cannot import Vagrant config module")
    
    def test_grpc_client_factory(self):
        """Test gRPC client factory function."""
        try:
            from grpc.vm_client import create_vm_grpc_client
            
            # Test with default endpoint
            client = create_vm_grpc_client()
            assert client is not None, "Client should be created"
            assert hasattr(client, 'endpoint'), "Client should have endpoint"
            
            # Test with custom endpoint
            custom_endpoint = "custom-host:9999"
            client = create_vm_grpc_client(custom_endpoint)
            assert client.endpoint == custom_endpoint, "Client should use custom endpoint"
            
        except ImportError:
            pytest.skip("Cannot import gRPC client factory")


if __name__ == "__main__":
    # Run unit tests manually if executed directly
    import subprocess
    import sys
    
    print("🧪 Running gRPC Component Unit Tests...")
    
    # Run pytest on this file
    result = subprocess.run([
        sys.executable, "-m", "pytest", __file__, "-v", "--tb=short"
    ], capture_output=True, text=True)
    
    print(result.stdout)
    if result.stderr:
        print("STDERR:", result.stderr)
    
    if result.returncode == 0:
        print("✅ All unit tests passed!")
    else:
        print("❌ Some unit tests failed!")
        sys.exit(result.returncode)
