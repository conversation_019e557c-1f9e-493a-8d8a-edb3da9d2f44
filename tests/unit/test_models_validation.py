"""
Unit tests for Pydantic model validation.

Comprehensive tests for all data models, validation rules, and serialization.
Ensures PEP8, PEP257, and PEP484 compliance.
"""

from datetime import UTC, datetime

from pydantic import ValidationError
import pytest

from api.models.file_injection import (
    ELKLogEntry,
    FileInjectionCreate,
    FileInjectionResponse,
    FileInjectionStatus,
    FileInjectionUpdate,
    InjectionStatus,
)


class TestInjectionStatus:
    """Test suite for InjectionStatus enum."""

    def test_injection_status_values(self) -> None:
        """Test that all injection status values are correct."""
        assert InjectionStatus.PENDING == "pending"
        assert InjectionStatus.IN_PROGRESS == "in_progress"
        assert InjectionStatus.COMPLETED == "completed"
        assert InjectionStatus.FAILED == "failed"

    def test_injection_status_string_conversion(self) -> None:
        """Test string conversion of injection status."""
        assert InjectionStatus.PENDING.value == "pending"
        assert InjectionStatus.COMPLETED.value == "completed"


class TestFileInjectionCreate:
    """Test suite for FileInjectionCreate model."""

    def test_create_valid_model(self) -> None:
        """Test creation of valid FileInjectionCreate model."""
        # Arrange
        data = {
            "filename": "test_script.sh",
            "target_path": "/app/scripts/test_script.sh",
            "permissions": "0755",
            "description": "Test script for deployment"
        }

        # Act
        model = FileInjectionCreate(**data)

        # Assert
        assert model.filename == "test_script.sh"
        assert model.target_path == "/app/scripts/test_script.sh"
        assert model.permissions == "0755"
        assert model.description == "Test script for deployment"

    def test_create_minimal_model(self) -> None:
        """Test creation with minimal required fields."""
        # Arrange
        data = {
            "filename": "test.sh",
            "target_path": "/app/test.sh"
        }

        # Act
        model = FileInjectionCreate(**data)

        # Assert
        assert model.filename == "test.sh"
        assert model.target_path == "/app/test.sh"
        assert model.permissions == "0755"  # Default value
        assert model.description is None

    def test_create_missing_required_fields(self) -> None:
        """Test validation error with missing required fields."""
        # Arrange
        data = {
            "filename": "test.sh"
            # Missing target_path
        }

        # Act & Assert
        with pytest.raises(ValidationError) as exc_info:
            FileInjectionCreate(**data)

        assert "target_path" in str(exc_info.value)

    def test_create_empty_filename(self) -> None:
        """Test that empty filename is accepted by Pydantic but should be validated in service layer."""
        # Arrange
        data = {
            "filename": "",
            "target_path": "/app/test.sh"
        }

        # Act
        model = FileInjectionCreate(**data)

        # Assert - Pydantic accepts empty string, validation happens in service layer
        assert model.filename == ""

    def test_create_invalid_permissions_format(self) -> None:
        """Test validation with invalid permissions format."""
        # Arrange
        data = {
            "filename": "test.sh",
            "target_path": "/app/test.sh",
            "permissions": "invalid"
        }

        # Act
        model = FileInjectionCreate(**data)

        # Assert - Pydantic accepts string, validation happens in service layer
        assert model.permissions == "invalid"

    def test_create_json_schema(self) -> None:
        """Test that JSON schema example is valid."""
        # Act
        schema = FileInjectionCreate.model_json_schema()

        # Assert
        assert "example" in schema
        example = schema["example"]

        # Validate the example
        model = FileInjectionCreate(**example)
        assert model.filename == "my_script.sh"


class TestFileInjectionResponse:
    """Test suite for FileInjectionResponse model."""

    def test_response_valid_model(self) -> None:
        """Test creation of valid FileInjectionResponse model."""
        # Arrange
        data = {
            "id": "550e8400-e29b-41d4-a716-446655440000",
            "filename": "test_script.sh",
            "target_path": "/app/scripts/test_script.sh",
            "permissions": "0755",
            "status": InjectionStatus.COMPLETED,
            "description": "Test script",
            "created_at": datetime.now(UTC),
            "updated_at": datetime.now(UTC),
            "file_size": 1024,
            "file_hash": "abc123def456"
        }

        # Act
        model = FileInjectionResponse(**data)

        # Assert
        assert model.id == "550e8400-e29b-41d4-a716-446655440000"
        assert model.filename == "test_script.sh"
        assert model.status == InjectionStatus.COMPLETED
        assert model.file_size == 1024

    def test_response_optional_fields(self) -> None:
        """Test response model with optional fields as None."""
        # Arrange
        data = {
            "id": "test-id",
            "filename": "test.sh",
            "target_path": "/app/test.sh",
            "permissions": "0644",
            "status": InjectionStatus.PENDING,
            "created_at": datetime.now(UTC),
            "updated_at": datetime.now(UTC),
            # Optional fields not provided
        }

        # Act
        model = FileInjectionResponse(**data)

        # Assert
        assert model.description is None
        assert model.file_size is None
        assert model.file_hash is None

    def test_response_status_enum_validation(self) -> None:
        """Test status field accepts valid enum values."""
        # Arrange
        base_data = {
            "id": "test-id",
            "filename": "test.sh",
            "target_path": "/app/test.sh",
            "permissions": "0644",
            "created_at": datetime.now(UTC),
            "updated_at": datetime.now(UTC),
        }

        # Act & Assert
        for status in InjectionStatus:
            data = {**base_data, "status": status}
            model = FileInjectionResponse(**data)
            assert model.status == status

    def test_response_invalid_status(self) -> None:
        """Test validation error with invalid status."""
        # Arrange
        data = {
            "id": "test-id",
            "filename": "test.sh",
            "target_path": "/app/test.sh",
            "permissions": "0644",
            "status": "invalid_status",
            "created_at": datetime.now(UTC),
            "updated_at": datetime.now(UTC),
        }

        # Act & Assert
        with pytest.raises(ValidationError):
            FileInjectionResponse(**data)


class TestFileInjectionStatus:
    """Test suite for FileInjectionStatus model."""

    def test_status_valid_model(self) -> None:
        """Test creation of valid FileInjectionStatus model."""
        # Arrange
        data = {
            "id": "test-injection-123",
            "status": InjectionStatus.IN_PROGRESS,
            "progress": 75,
            "message": "Processing file",
            "details": {"step": "copying", "total_steps": 4},
            "updated_at": datetime.now(UTC)
        }

        # Act
        model = FileInjectionStatus(**data)

        # Assert
        assert model.id == "test-injection-123"
        assert model.status == InjectionStatus.IN_PROGRESS
        assert model.progress == 75
        assert model.message == "Processing file"
        assert model.details["step"] == "copying"

    def test_status_default_progress(self) -> None:
        """Test default progress value."""
        # Arrange
        data = {
            "id": "test-id",
            "status": InjectionStatus.PENDING,
            "updated_at": datetime.now(UTC)
        }

        # Act
        model = FileInjectionStatus(**data)

        # Assert
        assert model.progress == 0

    def test_status_progress_validation(self) -> None:
        """Test progress value validation."""
        # Arrange
        base_data = {
            "id": "test-id",
            "status": InjectionStatus.IN_PROGRESS,
            "updated_at": datetime.now(UTC)
        }

        # Test valid progress values
        for progress in [0, 50, 100]:
            data = {**base_data, "progress": progress}
            model = FileInjectionStatus(**data)
            assert model.progress == progress

        # Test invalid progress values (should still work, validation in service)
        for progress in [-1, 101, 150]:
            data = {**base_data, "progress": progress}
            model = FileInjectionStatus(**data)
            assert model.progress == progress


class TestFileInjectionUpdate:
    """Test suite for FileInjectionUpdate model."""

    def test_update_all_fields(self) -> None:
        """Test update model with all fields."""
        # Arrange
        data = {
            "status": InjectionStatus.COMPLETED,
            "progress": 100,
            "message": "Injection completed successfully",
            "details": {"completion_time": "2024-01-15T10:35:00Z"}
        }

        # Act
        model = FileInjectionUpdate(**data)

        # Assert
        assert model.status == InjectionStatus.COMPLETED
        assert model.progress == 100
        assert model.message == "Injection completed successfully"
        assert model.details["completion_time"] == "2024-01-15T10:35:00Z"

    def test_update_partial_fields(self) -> None:
        """Test update model with partial fields."""
        # Arrange
        data = {
            "status": InjectionStatus.FAILED,
            "message": "File not found"
        }

        # Act
        model = FileInjectionUpdate(**data)

        # Assert
        assert model.status == InjectionStatus.FAILED
        assert model.message == "File not found"
        assert model.progress is None
        assert model.details is None

    def test_update_empty_model(self) -> None:
        """Test update model with no fields."""
        # Act
        model = FileInjectionUpdate()

        # Assert
        assert model.status is None
        assert model.progress is None
        assert model.message is None
        assert model.details is None


class TestELKLogEntry:
    """Test suite for ELKLogEntry model."""

    def test_elk_log_entry_valid(self) -> None:
        """Test creation of valid ELKLogEntry model."""
        # Arrange
        data = {
            "event_type": "file_injection_created",
            "injection_id": "test-injection-123",
            "message": "File injection created successfully",
            "details": {"filename": "test.sh", "size": 1024}
        }

        # Act
        model = ELKLogEntry(**data)

        # Assert
        assert model.event_type == "file_injection_created"
        assert model.injection_id == "test-injection-123"
        assert model.message == "File injection created successfully"
        assert model.details["filename"] == "test.sh"
        assert model.service == "turdparty-api"  # Default value
        assert model.level == "INFO"  # Default value

    def test_elk_log_entry_defaults(self) -> None:
        """Test ELKLogEntry default values."""
        # Arrange
        data = {
            "event_type": "test_event",
            "message": "Test message"
        }

        # Act
        model = ELKLogEntry(**data)

        # Assert
        assert model.service == "turdparty-api"
        assert model.level == "INFO"
        assert model.injection_id is None
        assert model.details is None
        assert isinstance(model.timestamp, datetime)

    def test_elk_log_entry_custom_values(self) -> None:
        """Test ELKLogEntry with custom values."""
        # Arrange
        custom_timestamp = datetime.now(UTC)
        data = {
            "timestamp": custom_timestamp,
            "service": "custom-service",
            "level": "ERROR",
            "event_type": "error_event",
            "message": "Error occurred"
        }

        # Act
        model = ELKLogEntry(**data)

        # Assert
        assert model.timestamp == custom_timestamp
        assert model.service == "custom-service"
        assert model.level == "ERROR"

    def test_elk_log_entry_serialization(self) -> None:
        """Test ELKLogEntry serialization to dict."""
        # Arrange
        data = {
            "event_type": "test_event",
            "message": "Test message",
            "details": {"key": "value"}
        }
        model = ELKLogEntry(**data)

        # Act
        serialized = model.model_dump()

        # Assert
        assert "timestamp" in serialized
        assert "service" in serialized
        assert "level" in serialized
        assert "event_type" in serialized
        assert "message" in serialized
        assert serialized["details"]["key"] == "value"


class TestModelSerialization:
    """Test suite for model serialization and deserialization."""

    def test_file_injection_create_json_roundtrip(self) -> None:
        """Test JSON serialization roundtrip for FileInjectionCreate."""
        # Arrange
        original_data = {
            "filename": "test.sh",
            "target_path": "/app/test.sh",
            "permissions": "0755",
            "description": "Test file"
        }
        model = FileInjectionCreate(**original_data)

        # Act
        json_str = model.model_dump_json()
        recreated_model = FileInjectionCreate.model_validate_json(json_str)

        # Assert
        assert recreated_model.filename == model.filename
        assert recreated_model.target_path == model.target_path
        assert recreated_model.permissions == model.permissions
        assert recreated_model.description == model.description

    def test_file_injection_response_json_roundtrip(self) -> None:
        """Test JSON serialization roundtrip for FileInjectionResponse."""
        # Arrange
        timestamp = datetime.now(UTC)
        original_data = {
            "id": "test-id",
            "filename": "test.sh",
            "target_path": "/app/test.sh",
            "permissions": "0755",
            "status": InjectionStatus.COMPLETED,
            "created_at": timestamp,
            "updated_at": timestamp,
            "file_size": 1024,
            "file_hash": "abc123"
        }
        model = FileInjectionResponse(**original_data)

        # Act
        json_str = model.model_dump_json()
        recreated_model = FileInjectionResponse.model_validate_json(json_str)

        # Assert
        assert recreated_model.id == model.id
        assert recreated_model.filename == model.filename
        assert recreated_model.status == model.status
        assert recreated_model.file_size == model.file_size
