"""
Unit tests for API routes.

Tests all API endpoints including file injection, health checks, and error handling.
Ensures PEP8, PEP257, and PEP484 compliance.
"""

from typing import Any
from unittest.mock import AsyncMock, MagicMock, patch

from fastapi import status
from fastapi.testclient import TestClient
import pytest


class TestHealthRoutes:
    """Test suite for health check routes."""

    def test_health_endpoint(self, client: TestClient) -> None:
        """Test the health check endpoint."""
        # Act
        response = client.get("/health")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "turdparty-api"

    def test_health_endpoint_with_dependencies(self, client: TestClient) -> None:
        """Test health endpoint with dependency checks."""
        # Act
        response = client.get("/health?check_dependencies=true")

        # Assert
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_503_SERVICE_UNAVAILABLE]
        data = response.json()
        assert "status" in data
        assert "service" in data


class TestFileInjectionRoutes:
    """Test suite for file injection API routes."""

    @pytest.fixture
    def mock_file_injection_service(self) -> MagicMock:
        """Create a mock file injection service."""
        mock_service = MagicMock()
        mock_service.create_injection = AsyncMock()
        mock_service.get_by_id = AsyncMock()
        mock_service.get_all = AsyncMock()
        mock_service.get_status = AsyncMock()
        mock_service.process_injection = AsyncMock()
        mock_service.delete = AsyncMock()
        return mock_service

    @pytest.fixture
    def mock_elk_logger(self) -> MagicMock:
        """Create a mock ELK logger."""
        mock_logger = MagicMock()
        mock_logger.log_file_injection_event = AsyncMock()
        mock_logger.log_installation_base = AsyncMock()
        mock_logger.log_system_event = AsyncMock()
        return mock_logger

    def test_create_file_injection_success(
        self,
        client: TestClient,
        mock_file_injection_service: MagicMock,
        mock_elk_logger: MagicMock,
        injection_response_data: dict[str, Any],
    ) -> None:
        """Test successful file injection creation."""
        # Arrange
        mock_file_injection_service.create_injection.return_value = MagicMock(**injection_response_data)

        with patch("api.v1.routes.file_injection.file_injection_service", mock_file_injection_service), \
             patch("api.v1.routes.file_injection.elk_logger", mock_elk_logger):

            # Act
            response = client.post(
                "/api/v1/file_injection/",
                files={"file": ("test_script.sh", b"#!/bin/bash\necho 'test'\n", "application/x-sh")},
                data={
                    "target_path": "/app/scripts/test_script.sh",
                    "permissions": "0755",
                    "description": "Test script"
                }
            )

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["filename"] == "test_script.sh"
        assert data["status"] == "pending"
        mock_file_injection_service.create_injection.assert_called_once()
        mock_elk_logger.log_file_injection_event.assert_called_once()

    def test_create_file_injection_no_file(self, client: TestClient) -> None:
        """Test file injection creation without file."""
        # Act
        response = client.post(
            "/api/v1/file_injection/",
            data={
                "target_path": "/app/scripts/test_script.sh",
                "permissions": "0755"
            }
        )

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_get_file_injection_success(
        self,
        client: TestClient,
        mock_file_injection_service: MagicMock,
        injection_response_data: dict[str, Any],
    ) -> None:
        """Test successful file injection retrieval."""
        # Arrange
        injection_id = injection_response_data["id"]
        mock_file_injection_service.get_by_id.return_value = MagicMock(**injection_response_data)

        with patch("api.v1.routes.file_injection.file_injection_service", mock_file_injection_service):
            # Act
            response = client.get(f"/api/v1/file_injection/{injection_id}")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == injection_id
        mock_file_injection_service.get_by_id.assert_called_once_with(injection_id)

    def test_get_file_injection_not_found(
        self,
        client: TestClient,
        mock_file_injection_service: MagicMock,
    ) -> None:
        """Test file injection retrieval with non-existent ID."""
        # Arrange
        injection_id = "non-existent-id"
        mock_file_injection_service.get_by_id.return_value = None

        with patch("api.v1.routes.file_injection.file_injection_service", mock_file_injection_service):
            # Act
            response = client.get(f"/api/v1/file_injection/{injection_id}")

        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_list_file_injections_success(
        self,
        client: TestClient,
        mock_file_injection_service: MagicMock,
        injection_response_data: dict[str, Any],
    ) -> None:
        """Test successful file injection listing."""
        # Arrange
        mock_file_injection_service.get_all.return_value = [MagicMock(**injection_response_data)]

        with patch("api.v1.routes.file_injection.file_injection_service", mock_file_injection_service):
            # Act
            response = client.get("/api/v1/file_injection/")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 1
        assert data[0]["id"] == injection_response_data["id"]

    def test_list_file_injections_with_pagination(
        self,
        client: TestClient,
        mock_file_injection_service: MagicMock,
    ) -> None:
        """Test file injection listing with pagination."""
        # Arrange
        mock_file_injection_service.get_all.return_value = []

        with patch("api.v1.routes.file_injection.file_injection_service", mock_file_injection_service):
            # Act
            response = client.get("/api/v1/file_injection/?skip=10&limit=5")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        mock_file_injection_service.get_all.assert_called_once_with(skip=10, limit=5, status_filter=None, authenticated=False)

    def test_get_injection_status_success(
        self,
        client: TestClient,
        mock_file_injection_service: MagicMock,
        injection_status_data: dict[str, Any],
    ) -> None:
        """Test successful injection status retrieval."""
        # Arrange
        injection_id = injection_status_data["id"]
        mock_file_injection_service.get_status.return_value = MagicMock(**injection_status_data)

        with patch("api.v1.routes.file_injection.file_injection_service", mock_file_injection_service):
            # Act
            response = client.get(f"/api/v1/file_injection/{injection_id}/status")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == injection_id
        assert data["status"] == "completed"

    def test_process_injection_success(
        self,
        client: TestClient,
        mock_file_injection_service: MagicMock,
        mock_elk_logger: MagicMock,
        injection_status_data: dict[str, Any],
    ) -> None:
        """Test successful injection processing."""
        # Arrange
        injection_id = injection_status_data["id"]
        mock_file_injection_service.process_injection.return_value = MagicMock(**injection_status_data)

        with patch("api.v1.routes.file_injection.file_injection_service", mock_file_injection_service), \
             patch("api.v1.routes.file_injection.elk_logger", mock_elk_logger):

            # Act
            response = client.post(f"/api/v1/file_injection/{injection_id}/process")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == injection_id
        mock_file_injection_service.process_injection.assert_called_once_with(injection_id)
        mock_elk_logger.log_installation_base.assert_called_once()

    def test_delete_injection_success(
        self,
        client: TestClient,
        mock_file_injection_service: MagicMock,
        mock_elk_logger: MagicMock,
    ) -> None:
        """Test successful injection deletion."""
        # Arrange
        injection_id = "test-injection-123"

        with patch("api.v1.routes.file_injection.file_injection_service", mock_file_injection_service), \
             patch("api.v1.routes.file_injection.elk_logger", mock_elk_logger):

            # Act
            response = client.delete(f"/api/v1/file_injection/{injection_id}")

        # Assert
        assert response.status_code == status.HTTP_204_NO_CONTENT
        mock_file_injection_service.delete.assert_called_once_with(injection_id)
        mock_elk_logger.log_file_injection_event.assert_called_once()


class TestErrorHandling:
    """Test suite for API error handling."""

    def test_404_not_found(self, client: TestClient) -> None:
        """Test 404 error handling."""
        # Act
        response = client.get("/api/v1/nonexistent")

        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_405_method_not_allowed(self, client: TestClient) -> None:
        """Test 405 error handling."""
        # Act
        response = client.patch("/health")

        # Assert
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED

    def test_422_validation_error(self, client: TestClient) -> None:
        """Test 422 validation error handling."""
        # Act
        response = client.post(
            "/api/v1/file_injection/",
            json={"invalid": "data"}
        )

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
