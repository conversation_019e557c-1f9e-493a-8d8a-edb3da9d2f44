"""
Real Storage Layer Tests - No Mocks

This module tests storage layer functionality using real MinIO containers
instead of mocks, providing more reliable and realistic testing.
"""

import asyncio
import hashlib
import io
import pytest
import time
import uuid
from typing import Dict, Any


class TestRealMinIOOperations:
    """Test MinIO operations with real containers"""

    @pytest.mark.asyncio
    async def test_upload_file_real(self, real_minio_client):
        """Test real file upload to MinIO"""
        client, bucket = real_minio_client
        
        # Arrange
        file_content = b"#!/bin/bash\necho 'Hello from real MinIO test'\nexit 0\n"
        filename = "test_script.sh"
        object_key = f"test_{int(time.time())}_{filename}"
        
        # Act
        result = client.put_object(
            bucket,
            object_key,
            io.BytesIO(file_content),
            len(file_content),
            content_type="application/x-sh",
            metadata={
                "original-filename": filename,
                "file-hash": hashlib.sha256(file_content).hexdigest()
            }
        )
        
        # Assert
        assert result.object_name == object_key
        assert result.bucket_name == bucket
        
        # Verify file exists
        stat = client.stat_object(bucket, object_key)
        assert stat.size == len(file_content)
        assert stat.metadata.get("original-filename") == filename

    @pytest.mark.asyncio
    async def test_download_file_real(self, real_minio_client):
        """Test real file download from MinIO"""
        client, bucket = real_minio_client
        
        # Arrange - Upload file first
        file_content = b"#!/bin/bash\necho 'Download test'\nexit 0\n"
        filename = "download_test.sh"
        object_key = f"download_{int(time.time())}_{filename}"
        
        client.put_object(
            bucket,
            object_key,
            io.BytesIO(file_content),
            len(file_content),
            content_type="application/x-sh"
        )
        
        # Act
        response = client.get_object(bucket, object_key)
        downloaded_content = response.read()
        response.close()
        response.release_conn()
        
        # Assert
        assert downloaded_content == file_content

    @pytest.mark.asyncio
    async def test_file_exists_real(self, real_minio_client):
        """Test file existence checking with real MinIO"""
        client, bucket = real_minio_client
        
        # Arrange - Upload file
        file_content = b"Test file for existence check"
        object_key = f"exists_test_{int(time.time())}.txt"
        
        client.put_object(
            bucket,
            object_key,
            io.BytesIO(file_content),
            len(file_content)
        )
        
        # Act & Assert - File should exist
        try:
            stat = client.stat_object(bucket, object_key)
            assert stat.size == len(file_content)
            file_exists = True
        except:
            file_exists = False
        
        assert file_exists is True
        
        # Act & Assert - Non-existent file should not exist
        try:
            client.stat_object(bucket, "non_existent_file.txt")
            non_existent_exists = True
        except:
            non_existent_exists = False
        
        assert non_existent_exists is False

    @pytest.mark.asyncio
    async def test_delete_file_real(self, real_minio_client):
        """Test real file deletion from MinIO"""
        client, bucket = real_minio_client
        
        # Arrange - Upload file
        file_content = b"File to be deleted"
        object_key = f"delete_test_{int(time.time())}.txt"
        
        client.put_object(
            bucket,
            object_key,
            io.BytesIO(file_content),
            len(file_content)
        )
        
        # Verify file exists
        stat = client.stat_object(bucket, object_key)
        assert stat.size == len(file_content)
        
        # Act - Delete file
        client.remove_object(bucket, object_key)
        
        # Assert - File should no longer exist
        try:
            client.stat_object(bucket, object_key)
            file_exists = True
        except:
            file_exists = False
        
        assert file_exists is False

    @pytest.mark.asyncio
    async def test_list_objects_real(self, real_minio_client):
        """Test listing objects in real MinIO bucket"""
        client, bucket = real_minio_client
        
        # Arrange - Upload multiple files
        test_files = [
            (f"list_test_1_{int(time.time())}.txt", b"Content 1"),
            (f"list_test_2_{int(time.time())}.txt", b"Content 2"),
            (f"list_test_3_{int(time.time())}.txt", b"Content 3")
        ]
        
        uploaded_keys = []
        for filename, content in test_files:
            client.put_object(
                bucket,
                filename,
                io.BytesIO(content),
                len(content)
            )
            uploaded_keys.append(filename)
        
        # Act - List objects
        objects = list(client.list_objects(bucket, prefix="list_test_"))
        
        # Assert
        assert len(objects) >= 3  # At least our 3 test files
        object_names = [obj.object_name for obj in objects]
        
        for key in uploaded_keys:
            assert key in object_names

    @pytest.mark.asyncio
    async def test_large_file_upload_download_real(self, real_minio_client):
        """Test large file operations with real MinIO"""
        client, bucket = real_minio_client
        
        # Arrange - Create a large file (1MB)
        large_content = b"x" * (1024 * 1024)  # 1MB
        object_key = f"large_file_{int(time.time())}.bin"
        
        # Act - Upload large file
        result = client.put_object(
            bucket,
            object_key,
            io.BytesIO(large_content),
            len(large_content),
            content_type="application/octet-stream"
        )
        
        # Assert upload
        assert result.object_name == object_key
        stat = client.stat_object(bucket, object_key)
        assert stat.size == len(large_content)
        
        # Act - Download large file
        response = client.get_object(bucket, object_key)
        downloaded_content = response.read()
        response.close()
        response.release_conn()
        
        # Assert download
        assert len(downloaded_content) == len(large_content)
        assert downloaded_content == large_content

    @pytest.mark.asyncio
    async def test_presigned_url_real(self, real_minio_client):
        """Test presigned URL generation with real MinIO"""
        client, bucket = real_minio_client
        
        # Arrange - Upload file
        file_content = b"Content for presigned URL test"
        object_key = f"presigned_test_{int(time.time())}.txt"
        
        client.put_object(
            bucket,
            object_key,
            io.BytesIO(file_content),
            len(file_content)
        )
        
        # Act - Generate presigned URL
        from datetime import timedelta
        url = client.presigned_get_object(bucket, object_key, expires=timedelta(hours=1))
        
        # Assert
        assert url is not None
        assert isinstance(url, str)
        assert bucket in url
        assert object_key in url
        
        # Test URL works (basic validation)
        import requests
        try:
            response = requests.get(url, timeout=10)
            assert response.status_code == 200
            assert response.content == file_content
        except requests.exceptions.RequestException:
            # URL might not be accessible from test environment, which is OK
            pass


class TestRealStorageService:
    """Test Storage Service with real MinIO backend"""

    @pytest.mark.asyncio
    async def test_storage_service_upload_real(self, real_storage_service):
        """Test storage service upload with real MinIO"""
        # Arrange
        file_id = str(uuid.uuid4())
        filename = "service_test.sh"
        file_content = b"#!/bin/bash\necho 'Storage service test'\n"
        
        # Act
        result = await real_storage_service.upload_file(
            file_id=file_id,
            filename=filename,
            file_content=file_content,
            content_type="application/x-sh"
        )
        
        # Assert
        assert result["success"] is True
        assert result["file_id"] == file_id
        assert result["object_key"] is not None
        assert result["bucket"] is not None
        assert result["file_size"] == len(file_content)
        assert "file_hash" in result

    @pytest.mark.asyncio
    async def test_storage_service_download_real(self, real_storage_service):
        """Test storage service download with real MinIO"""
        # Arrange - Upload file first
        file_id = str(uuid.uuid4())
        filename = "download_service_test.txt"
        file_content = b"Content for download test"
        
        upload_result = await real_storage_service.upload_file(
            file_id=file_id,
            filename=filename,
            file_content=file_content
        )
        
        # Act
        download_result = await real_storage_service.download_file(
            bucket_name=upload_result["bucket"],
            object_key=upload_result["object_key"]
        )
        
        # Assert
        assert download_result["success"] is True
        assert download_result["content"] == file_content
        assert download_result["file_size"] == len(file_content)

    @pytest.mark.asyncio
    async def test_storage_service_file_exists_real(self, real_storage_service):
        """Test storage service file existence check with real MinIO"""
        # Arrange - Upload file first
        file_id = str(uuid.uuid4())
        filename = "exists_service_test.txt"
        file_content = b"File existence test"
        
        upload_result = await real_storage_service.upload_file(
            file_id=file_id,
            filename=filename,
            file_content=file_content
        )
        
        # Act & Assert - File should exist
        exists = real_storage_service.file_exists(
            upload_result["bucket"],
            upload_result["object_key"]
        )
        assert exists is True
        
        # Act & Assert - Non-existent file should not exist
        non_exists = real_storage_service.file_exists(
            upload_result["bucket"],
            "non_existent_file.txt"
        )
        assert non_exists is False

    @pytest.mark.asyncio
    async def test_storage_service_delete_real(self, real_storage_service):
        """Test storage service file deletion with real MinIO"""
        # Arrange - Upload file first
        file_id = str(uuid.uuid4())
        filename = "delete_service_test.txt"
        file_content = b"File to be deleted by service"
        
        upload_result = await real_storage_service.upload_file(
            file_id=file_id,
            filename=filename,
            file_content=file_content
        )
        
        # Verify file exists
        assert real_storage_service.file_exists(
            upload_result["bucket"],
            upload_result["object_key"]
        ) is True
        
        # Act
        delete_result = await real_storage_service.delete_file(
            bucket_name=upload_result["bucket"],
            object_key=upload_result["object_key"]
        )
        
        # Assert
        assert delete_result["success"] is True
        assert real_storage_service.file_exists(
            upload_result["bucket"],
            upload_result["object_key"]
        ) is False

    @pytest.mark.asyncio
    async def test_storage_service_concurrent_operations_real(self, real_storage_service):
        """Test concurrent storage operations with real MinIO"""
        import asyncio
        
        # Arrange
        file_contents = [
            (f"concurrent_test_{i}.txt", f"Content {i}".encode())
            for i in range(5)
        ]
        
        # Act - Upload files concurrently
        upload_tasks = []
        for i, (filename, content) in enumerate(file_contents):
            task = real_storage_service.upload_file(
                file_id=str(uuid.uuid4()),
                filename=filename,
                file_content=content
            )
            upload_tasks.append(task)
        
        upload_results = await asyncio.gather(*upload_tasks)
        
        # Assert uploads
        assert len(upload_results) == 5
        for result in upload_results:
            assert result["success"] is True
        
        # Act - Download files concurrently
        download_tasks = []
        for result in upload_results:
            task = real_storage_service.download_file(
                bucket_name=result["bucket"],
                object_key=result["object_key"]
            )
            download_tasks.append(task)
        
        download_results = await asyncio.gather(*download_tasks)
        
        # Assert downloads
        assert len(download_results) == 5
        for result in download_results:
            assert result["success"] is True
            assert len(result["content"]) > 0
