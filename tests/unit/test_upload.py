from pathlib import Path

import requests

# Create a test file
test_file = Path("test.txt")
with test_file.open("w") as f:
    f.write("Test file content")

# Upload the file
url = "http://localhost:8000/api/v1/file_upload/"
with test_file.open("rb") as file_handle:
    files = {"file": file_handle}
    data = {"description": "Test file"}

    response = requests.post(url, files=files, data=data)
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.text}")

# Clean up
test_file.unlink()
