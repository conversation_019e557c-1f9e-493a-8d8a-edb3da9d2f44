"""
Unit tests for VM injection service.
Tests VM file injection functionality with proper pytest structure.
"""

import pytest
import asyncio
import logging
from pathlib import Path
from unittest.mock import Mock, AsyncMock

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestVMInjectionService:
    """Test VM injection service functionality."""

    @pytest.mark.asyncio
    async def test_vm_injection_service_mock(self):
        """Test VM injection service with mocked dependencies."""
        vm_id = "test-vm-123"
        source_path = "/tmp/test_file.txt"
        target_path = "/tmp/injection_test/test_file.txt"

        # Create a mock vagrant client directly
        mock_vagrant_client = Mock()

        # Mock SSH client
        mock_ssh_client = Mock()
        mock_vagrant_client.ssh_client = mock_ssh_client

        # Mock successful operations
        mock_vagrant_client.connect = AsyncMock(return_value=True)
        mock_vagrant_client.status = AsyncMock(return_value={"status": "running"})
        mock_vagrant_client.execute_command = AsyncMock(return_value={
            "exit_code": 0,
            "stdout": "File injection successful",
            "stderr": ""
        })
        mock_vagrant_client.close = AsyncMock()

        # Test the injection process
        await self._perform_vm_injection(mock_vagrant_client, vm_id, source_path, target_path)

        # Verify the calls were made
        mock_vagrant_client.connect.assert_called_once()
        mock_vagrant_client.status.assert_called_once_with(vm_id)
        assert mock_vagrant_client.execute_command.call_count >= 2  # Copy and verify commands
        mock_vagrant_client.close.assert_called_once()

    async def _perform_vm_injection(self, vagrant_client, vm_id: str, source_path: str, target_path: str):
        """Helper method to perform VM injection."""
        # Connect to the service
        connected = await vagrant_client.connect()
        assert connected, "Should connect successfully"

        # Get the VM status
        status = await vagrant_client.status(vm_id)
        logger.info(f"VM status: {status}")

        # Inject the file
        logger.info(f"Injecting file {source_path} into VM {vm_id} at {target_path}")

        # Create the command to copy the file to the VM
        copy_command = f"cp {source_path} {target_path} && chmod 0755 {target_path}"

        # Execute the command
        result = await vagrant_client.execute_command(vm_id, copy_command, sudo=True)
        logger.info(f"File injection result: {result}")
        assert result["exit_code"] == 0, "File injection should succeed"

        # Verify the file injection
        verify_command = f"ls -la {target_path}"
        verify_result = await vagrant_client.execute_command(vm_id, verify_command)
        logger.info(f"File verification result: {verify_result}")

        # Close the client
        await vagrant_client.close()

    @pytest.mark.asyncio
    async def test_vm_injection_connection_failure(self):
        """Test VM injection with connection failure."""
        mock_vagrant_client = Mock()

        # Mock connection failure
        mock_vagrant_client.connect = AsyncMock(return_value=False)
        mock_vagrant_client.close = AsyncMock()

        # Test connection failure handling
        connected = await mock_vagrant_client.connect()
        assert not connected, "Connection should fail"

        # Should still close cleanly
        await mock_vagrant_client.close()

    @pytest.mark.asyncio
    async def test_vm_injection_command_failure(self):
        """Test VM injection with command execution failure."""
        mock_vagrant_client = Mock()

        # Mock successful connection but failed command
        mock_vagrant_client.connect = AsyncMock(return_value=True)
        mock_vagrant_client.status = AsyncMock(return_value={"status": "running"})
        mock_vagrant_client.execute_command = AsyncMock(return_value={
            "exit_code": 1,
            "stdout": "",
            "stderr": "Permission denied"
        })
        mock_vagrant_client.close = AsyncMock()

        # Test command failure
        connected = await mock_vagrant_client.connect()
        assert connected

        result = await mock_vagrant_client.execute_command("test-vm", "test-command")
        assert result["exit_code"] != 0, "Command should fail"
        assert "Permission denied" in result["stderr"]

        await mock_vagrant_client.close()


