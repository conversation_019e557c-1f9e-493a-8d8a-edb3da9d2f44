"""
Unit tests for VM Management Routes
"""
import pytest
import json
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient
from fastapi.websockets import WebSocketDisconnect

from api.v1.application import get_application
from api.models.vm_management import V<PERSON>reateRequest, VMType


class TestVMManagementRoutes:
    """Test VM Management REST API routes"""

    @pytest.fixture
    def client(self):
        """Create test client"""
        app = get_application()
        return TestClient(app)

    @pytest.fixture
    def sample_vm_data(self):
        """Sample VM creation data"""
        return {
            "name": "test-vm",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 512,
            "cpus": 1,
            "domain": "TurdParty",
            "description": "Test VM"
        }

    def test_get_vm_templates(self, client):
        """Test getting VM templates"""
        response = client.get("/api/v1/vms/templates")
        
        assert response.status_code == 200
        templates = response.json()
        assert isinstance(templates, list)
        assert len(templates) > 0
        
        # Check template structure
        template = templates[0]
        assert "template_id" in template
        assert "name" in template
        assert "vm_type" in template
        assert "resource_requirements" in template

    def test_create_vm_success(self, client, sample_vm_data):
        """Test successful VM creation"""
        response = client.post("/api/v1/vms/", json=sample_vm_data)
        
        assert response.status_code == 201
        vm = response.json()
        assert vm["name"] == sample_vm_data["name"]
        assert vm["template"] == sample_vm_data["template"]
        assert vm["vm_type"] == sample_vm_data["vm_type"]
        assert "vm_id" in vm
        assert "created_at" in vm

    def test_create_vm_invalid_data(self, client):
        """Test VM creation with invalid data"""
        invalid_data = {
            "name": "",  # Invalid empty name
            "template": "invalid:template",
            "vm_type": "invalid_type",
            "memory_mb": -1,  # Invalid negative memory
            "cpus": 0,  # Invalid zero CPUs
            "domain": "InvalidDomain"
        }
        
        response = client.post("/api/v1/vms/", json=invalid_data)
        
        assert response.status_code == 422  # Validation error

    def test_list_vms_empty(self, client):
        """Test listing VMs when none exist"""
        response = client.get("/api/v1/vms/")
        
        assert response.status_code == 200
        data = response.json()
        assert "vms" in data
        assert "total" in data
        assert data["total"] == 0

    def test_list_vms_with_pagination(self, client, sample_vm_data):
        """Test VM listing with pagination"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        assert create_response.status_code == 201
        
        # Test pagination
        response = client.get("/api/v1/vms/?skip=0&limit=5")
        
        assert response.status_code == 200
        data = response.json()
        assert data["skip"] == 0
        assert data["limit"] == 5
        assert data["total"] >= 1

    def test_get_vm_by_id(self, client, sample_vm_data):
        """Test getting VM by ID"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        vm_id = create_response.json()["vm_id"]
        
        # Get the VM
        response = client.get(f"/api/v1/vms/{vm_id}")
        
        assert response.status_code == 200
        vm = response.json()
        assert vm["vm_id"] == vm_id
        assert vm["name"] == sample_vm_data["name"]

    def test_get_vm_not_found(self, client):
        """Test getting non-existent VM"""
        response = client.get("/api/v1/vms/nonexistent-id")
        
        assert response.status_code == 404

    def test_vm_action_start(self, client, sample_vm_data):
        """Test VM start action"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        vm_id = create_response.json()["vm_id"]
        
        # Stop the VM first (to test start)
        stop_response = client.post(f"/api/v1/vms/{vm_id}/action", json={"action": "stop"})
        assert stop_response.status_code == 200
        
        # Start the VM
        response = client.post(f"/api/v1/vms/{vm_id}/action", json={"action": "start"})
        
        assert response.status_code == 200
        result = response.json()
        assert result["action"] == "start"
        assert result["vm_id"] == vm_id
        assert "task_id" in result

    def test_vm_action_stop(self, client, sample_vm_data):
        """Test VM stop action"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        vm_id = create_response.json()["vm_id"]
        
        # Stop the VM
        response = client.post(f"/api/v1/vms/{vm_id}/action", json={"action": "stop"})
        
        assert response.status_code == 200
        result = response.json()
        assert result["action"] == "stop"
        assert result["status"] == "stopped"

    def test_vm_action_invalid(self, client, sample_vm_data):
        """Test invalid VM action"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        vm_id = create_response.json()["vm_id"]
        
        # Try invalid action
        response = client.post(f"/api/v1/vms/{vm_id}/action", json={"action": "invalid_action"})
        
        assert response.status_code == 422  # Validation error

    def test_vm_action_not_found(self, client):
        """Test VM action on non-existent VM"""
        response = client.post("/api/v1/vms/nonexistent/action", json={"action": "start"})
        
        assert response.status_code == 404

    def test_delete_vm_success(self, client, sample_vm_data):
        """Test successful VM deletion"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        vm_id = create_response.json()["vm_id"]
        
        # Delete the VM
        response = client.delete(f"/api/v1/vms/{vm_id}?force=true")
        
        assert response.status_code == 200
        result = response.json()
        assert result["vm_id"] == vm_id
        assert "task_id" in result

    def test_delete_vm_running_without_force(self, client, sample_vm_data):
        """Test deleting running VM without force"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        vm_id = create_response.json()["vm_id"]
        
        # Try to delete without force
        response = client.delete(f"/api/v1/vms/{vm_id}")
        
        assert response.status_code == 400

    def test_delete_vm_not_found(self, client):
        """Test deleting non-existent VM"""
        response = client.delete("/api/v1/vms/nonexistent?force=true")
        
        assert response.status_code == 404


class TestVMWebSocketRoutes:
    """Test VM WebSocket routes"""

    @pytest.fixture
    def client(self):
        """Create test client"""
        app = get_application()
        return TestClient(app)

    @pytest.fixture
    def sample_vm_data(self):
        """Sample VM creation data"""
        return {
            "name": "test-websocket-vm",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 512,
            "cpus": 1,
            "domain": "TurdParty"
        }

    def test_vm_metrics_websocket_connection(self, client, sample_vm_data):
        """Test WebSocket connection for VM metrics"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        vm_id = create_response.json()["vm_id"]
        
        # Test WebSocket connection
        with client.websocket_connect(f"/api/v1/vms/{vm_id}/metrics/stream?vm_type=docker") as websocket:
            # Should receive metrics data
            data = websocket.receive_json()
            assert "vm_id" in data
            assert "timestamp" in data
            assert "cpu_percent" in data
            assert "memory_percent" in data

    def test_vm_metrics_websocket_invalid_vm(self, client):
        """Test WebSocket connection for non-existent VM"""
        with client.websocket_connect("/api/v1/vms/nonexistent/metrics/stream") as websocket:
            # Should receive error or close connection
            try:
                data = websocket.receive_json()
                # If we get data, it should contain an error
                if "error" not in data:
                    # Or the connection should close
                    pass
            except WebSocketDisconnect:
                # Connection closed, which is acceptable
                pass

    def test_command_execution_websocket(self, client, sample_vm_data):
        """Test WebSocket command execution"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        vm_id = create_response.json()["vm_id"]
        
        # Test command execution WebSocket
        with client.websocket_connect(f"/api/v1/vms/{vm_id}/commands/execute") as websocket:
            # Send a command
            command_data = {
                "command": "echo 'Hello World'",
                "working_directory": "/tmp"
            }
            websocket.send_json(command_data)
            
            # Should receive command output
            response = websocket.receive_json()
            assert "type" in response
            assert response["type"] in ["command_output", "error"]

    @patch('api.v1.routes.vm_management.vm_metrics_service')
    def test_metrics_websocket_with_mock_service(self, mock_service, client):
        """Test metrics WebSocket with mocked service"""
        # Mock the metrics service
        mock_service.stream_vm_metrics = AsyncMock()
        mock_service.stream_vm_metrics.return_value = iter([
            {
                "vm_id": "test_vm",
                "timestamp": 1699123456789,
                "cpu_percent": 25.5,
                "memory_percent": 45.2,
                "status": "running"
            }
        ])
        
        with client.websocket_connect("/api/v1/vms/test_vm/metrics/stream") as websocket:
            data = websocket.receive_json()
            assert data["vm_id"] == "test_vm"
            assert data["cpu_percent"] == 25.5

    def test_file_upload_websocket_connection(self, client, sample_vm_data):
        """Test file upload WebSocket connection"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        vm_id = create_response.json()["vm_id"]
        
        # Test file upload WebSocket
        with client.websocket_connect(f"/api/v1/vms/{vm_id}/files/upload") as websocket:
            # Send upload initiation
            upload_data = {
                "target_path": "/tmp/test_file.txt"
            }
            websocket.send_text(json.dumps(upload_data))
            
            # Should receive ready response
            response = websocket.receive_json()
            assert response["type"] == "upload_ready"


class TestVMManagementValidation:
    """Test VM management data validation"""

    def test_vm_create_request_validation(self):
        """Test VM creation request validation"""
        # Valid request
        valid_data = {
            "name": "test-vm",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 512,
            "cpus": 1,
            "domain": "TurdParty"
        }
        
        request = VMCreateRequest(**valid_data)
        assert request.name == "test-vm"
        assert request.vm_type == VMType.DOCKER

    def test_vm_create_request_invalid_name(self):
        """Test VM creation with invalid name"""
        invalid_data = {
            "name": "test@vm!",  # Invalid characters
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 512,
            "cpus": 1,
            "domain": "TurdParty"
        }
        
        with pytest.raises(ValueError):
            VMCreateRequest(**invalid_data)

    def test_vm_create_request_invalid_template(self):
        """Test VM creation with invalid template"""
        invalid_data = {
            "name": "test-vm",
            "template": "invalid:template",
            "vm_type": "docker",
            "memory_mb": 512,
            "cpus": 1,
            "domain": "TurdParty"
        }
        
        with pytest.raises(ValueError):
            VMCreateRequest(**invalid_data)

    def test_vm_create_request_invalid_memory(self):
        """Test VM creation with invalid memory"""
        invalid_data = {
            "name": "test-vm",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 100,  # Below minimum
            "cpus": 1,
            "domain": "TurdParty"
        }
        
        with pytest.raises(ValueError):
            VMCreateRequest(**invalid_data)

    def test_vm_create_request_invalid_cpus(self):
        """Test VM creation with invalid CPU count"""
        invalid_data = {
            "name": "test-vm",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 512,
            "cpus": 0,  # Invalid zero CPUs
            "domain": "TurdParty"
        }
        
        with pytest.raises(ValueError):
            VMCreateRequest(**invalid_data)


class TestVMManagementErrorHandling:
    """Test error handling in VM management"""

    @pytest.fixture
    def client(self):
        """Create test client"""
        app = get_application()
        return TestClient(app)

    def test_internal_server_error_handling(self, client):
        """Test internal server error handling"""
        with patch('api.v1.routes.vm_management.mock_vms', side_effect=Exception("Database error")):
            response = client.get("/api/v1/vms/")
            # Should handle the error gracefully
            assert response.status_code in [500, 200]  # Depending on implementation

    def test_websocket_error_handling(self, client):
        """Test WebSocket error handling"""
        with patch('api.services.vm_metrics_service.vm_metrics_service.stream_vm_metrics', 
                   side_effect=Exception("Service error")):
            try:
                with client.websocket_connect("/api/v1/vms/test/metrics/stream") as websocket:
                    data = websocket.receive_json()
                    # Should receive error message
                    assert "error" in data or "message" in data
            except WebSocketDisconnect:
                # Connection closed due to error, which is acceptable
                pass

    def test_concurrent_vm_operations(self, client):
        """Test concurrent VM operations"""
        import threading
        import time
        
        results = []
        
        def create_vm(vm_name):
            vm_data = {
                "name": vm_name,
                "template": "ubuntu:20.04",
                "vm_type": "docker",
                "memory_mb": 512,
                "cpus": 1,
                "domain": "TurdParty"
            }
            response = client.post("/api/v1/vms/", json=vm_data)
            results.append(response.status_code)
        
        # Create multiple VMs concurrently
        threads = []
        for i in range(5):
            thread = threading.Thread(target=create_vm, args=[f"concurrent-vm-{i}"])
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All should succeed
        assert all(status == 201 for status in results)
