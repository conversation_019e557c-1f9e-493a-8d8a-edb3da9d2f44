"""
Real Worker Services Tests - No Mocks

This module tests worker services functionality using real Celery workers,
Redis broker, and service integration instead of mocks.
"""

import asyncio
import io
import pytest
import time
import uuid
from typing import Dict, Any


class TestRealCeleryIntegration:
    """Test Celery integration with real Redis broker"""

    def test_celery_app_configuration_real(self, real_celery_app):
        """Test Celery app configuration with real Redis"""
        app = real_celery_app
        
        # Verify app configuration
        assert app.conf.broker_url.startswith("redis://")
        assert app.conf.result_backend.startswith("redis://")
        assert app.conf.task_serializer == 'json'
        assert app.conf.result_serializer == 'json'

    def test_celery_broker_connection_real(self, real_celery_app):
        """Test Celery broker connection with real Redis"""
        app = real_celery_app
        
        # Test broker connection
        try:
            # Get broker connection
            with app.connection() as conn:
                conn.ensure_connection(max_retries=3)
            
            broker_connected = True
        except Exception:
            broker_connected = False
        
        assert broker_connected, "Failed to connect to Redis broker"

    def test_celery_task_registration_real(self, real_celery_app):
        """Test task registration with real Celery app"""
        app = real_celery_app
        
        # Register a simple test task
        @app.task
        def test_task(x, y):
            return x + y
        
        # Verify task is registered (check for full module path)
        task_names = list(app.tasks.keys())
        assert any('test_task' in name for name in task_names), f"test_task not found in {task_names}"

    def test_celery_task_execution_real(self, real_celery_app):
        """Test task execution with real Celery"""
        app = real_celery_app
        
        # Register and execute a simple task
        @app.task
        def add_numbers(x, y):
            return x + y
        
        # Execute task synchronously for testing
        result = add_numbers.apply(args=[2, 3])
        assert result.result == 5
        assert result.successful()


class TestRealFileOperationsWorker:
    """Test file operations worker with real MinIO"""

    def test_download_file_from_minio_real(self, real_worker_services):
        """Test file download from MinIO with real worker"""
        services = real_worker_services
        file_worker = services["file_worker"]
        minio_client = services["minio_client"]
        bucket = services["minio_bucket"]
        
        # Upload test file to MinIO
        test_content = b"#!/bin/bash\necho 'Worker test file'\nexit 0\n"
        object_key = f"worker_test_{int(time.time())}.sh"
        
        minio_client.put_object(
            bucket,
            object_key,
            io.BytesIO(test_content),
            len(test_content),
            content_type="application/x-sh"
        )
        
        # Test download with real worker
        result = file_worker.download_file_from_minio(object_key)
        
        # Verify download
        assert result["success"] is True
        assert result["content"] == test_content
        assert result["size"] == len(test_content)

    def test_download_nonexistent_file_real(self, real_worker_services):
        """Test download of non-existent file"""
        services = real_worker_services
        file_worker = services["file_worker"]
        
        # Test download of non-existent file
        result = file_worker.download_file_from_minio("nonexistent_file.txt")
        
        # Should fail gracefully
        assert result["success"] is False
        assert "error" in result

    def test_validate_file_real(self, real_worker_services):
        """Test file validation with real worker"""
        services = real_worker_services
        file_worker = services["file_worker"]
        
        # Test valid file
        valid_content = b"#!/bin/bash\necho 'Valid script'\n"
        result = file_worker.validate_file(valid_content, "valid_script.sh")
        
        assert result["success"] is True
        assert result["file_size"] == len(valid_content)
        assert result["filename"] == "valid_script.sh"

    def test_validate_empty_file_real(self, real_worker_services):
        """Test validation of empty file"""
        services = real_worker_services
        file_worker = services["file_worker"]
        
        # Test empty file
        result = file_worker.validate_file(b"", "empty.txt")
        
        assert result["success"] is False
        assert "Empty file" in result["error"]

    def test_validate_large_file_real(self, real_worker_services):
        """Test validation of large file"""
        services = real_worker_services
        file_worker = services["file_worker"]
        
        # Test file that's too large (over 100MB)
        large_content = b"x" * (101 * 1024 * 1024)  # 101MB
        result = file_worker.validate_file(large_content, "large_file.bin")
        
        assert result["success"] is False
        assert "too large" in result["error"]


class TestRealVMOperationsWorker:
    """Test VM operations worker with real Docker"""

    @pytest.mark.asyncio
    async def test_create_vm_for_processing_real(self, real_worker_services):
        """Test VM creation for processing with real Docker"""
        services = real_worker_services
        vm_worker = services["vm_worker"]
        
        # Test VM creation
        result = await vm_worker.create_vm_for_processing("ubuntu:20.04")
        
        try:
            # Verify VM creation
            assert result["success"] is True
            assert "vm_id" in result
            assert "vm_name" in result
            assert result["status"] == "running"
            
            vm_id = result["vm_id"]
            
            # Verify VM exists in service
            vm_service = services["vm_service"]
            assert vm_id in vm_service.vms
            
        finally:
            # Cleanup
            if result.get("success") and "vm_id" in result:
                await vm_worker.destroy_vm(result["vm_id"])

    @pytest.mark.asyncio
    async def test_destroy_vm_real(self, real_worker_services):
        """Test VM destruction with real Docker"""
        services = real_worker_services
        vm_worker = services["vm_worker"]
        
        # Create VM first
        create_result = await vm_worker.create_vm_for_processing("ubuntu:20.04")
        assert create_result["success"] is True
        
        vm_id = create_result["vm_id"]
        
        # Test VM destruction
        destroy_result = await vm_worker.destroy_vm(vm_id)
        
        # Verify destruction
        assert destroy_result["success"] is True
        
        # Verify VM no longer exists in service
        vm_service = services["vm_service"]
        assert vm_id not in vm_service.vms

    @pytest.mark.asyncio
    async def test_destroy_nonexistent_vm_real(self, real_worker_services):
        """Test destruction of non-existent VM"""
        services = real_worker_services
        vm_worker = services["vm_worker"]
        
        # Test destruction of non-existent VM
        result = await vm_worker.destroy_vm("nonexistent-vm-id")
        
        # Should fail gracefully
        assert result["success"] is False
        assert "error" in result


class TestRealWorkflowOrchestrator:
    """Test workflow orchestrator with real services"""

    @pytest.mark.asyncio
    async def test_complete_workflow_real(self, real_worker_services):
        """Test complete file processing workflow with real services"""
        services = real_worker_services
        workflow_orchestrator = services["workflow_orchestrator"]
        minio_client = services["minio_client"]
        bucket = services["minio_bucket"]
        
        # Setup: Upload test file to MinIO
        test_content = b"#!/bin/bash\necho 'Workflow test file'\nexit 0\n"
        object_key = f"workflow_test_{int(time.time())}.sh"
        workflow_id = str(uuid.uuid4())
        
        minio_client.put_object(
            bucket,
            object_key,
            io.BytesIO(test_content),
            len(test_content),
            content_type="application/x-sh"
        )
        
        # Execute workflow
        result = await workflow_orchestrator.execute_file_processing_workflow(
            object_key, workflow_id
        )
        
        # Verify workflow execution
        assert result["success"] is True
        assert result["workflow_id"] == workflow_id
        assert "vm_id" in result
        assert "results" in result
        
        # Verify workflow steps
        results = result["results"]
        step_names = [step[0] for step in results]
        
        expected_steps = ["download", "validate", "create_vm", "inject", "cleanup"]
        for step in expected_steps:
            assert step in step_names
        
        # Verify each step succeeded
        for step_name, step_result in results:
            if step_name != "cleanup":  # Cleanup might fail if VM already destroyed
                assert step_result["success"] is True, f"Step {step_name} failed: {step_result}"

    @pytest.mark.asyncio
    async def test_workflow_with_invalid_file_real(self, real_worker_services):
        """Test workflow with invalid file"""
        services = real_worker_services
        workflow_orchestrator = services["workflow_orchestrator"]
        workflow_id = str(uuid.uuid4())
        
        # Execute workflow with non-existent file
        result = await workflow_orchestrator.execute_file_processing_workflow(
            "nonexistent_file.txt", workflow_id
        )
        
        # Should fail at download step
        assert result["success"] is False
        assert "Download failed" in result["error"]
        assert "results" in result
        
        # Should have attempted download step
        results = result["results"]
        assert len(results) >= 1
        assert results[0][0] == "download"
        assert results[0][1]["success"] is False

    @pytest.mark.asyncio
    async def test_workflow_with_empty_file_real(self, real_worker_services):
        """Test workflow with empty file"""
        services = real_worker_services
        workflow_orchestrator = services["workflow_orchestrator"]
        minio_client = services["minio_client"]
        bucket = services["minio_bucket"]
        
        # Setup: Upload empty file to MinIO
        empty_content = b""
        object_key = f"empty_workflow_test_{int(time.time())}.txt"
        workflow_id = str(uuid.uuid4())
        
        minio_client.put_object(
            bucket,
            object_key,
            io.BytesIO(empty_content),
            len(empty_content),
            content_type="text/plain"
        )
        
        # Execute workflow
        result = await workflow_orchestrator.execute_file_processing_workflow(
            object_key, workflow_id
        )
        
        # Should fail at validation step
        assert result["success"] is False
        assert "Validation failed" in result["error"]
        
        # Should have download and validate steps
        results = result["results"]
        assert len(results) >= 2
        assert results[0][0] == "download"
        assert results[0][1]["success"] is True  # Download should succeed
        assert results[1][0] == "validate"
        assert results[1][1]["success"] is False  # Validation should fail


class TestRealWorkerServiceIntegration:
    """Test worker service integration with all real services"""

    @pytest.mark.asyncio
    async def test_end_to_end_integration_real(self, real_worker_services):
        """Test end-to-end integration with all real services"""
        services = real_worker_services
        
        # Test all services are available
        assert "celery_app" in services
        assert "file_worker" in services
        assert "vm_worker" in services
        assert "workflow_orchestrator" in services
        assert "minio_client" in services
        assert "vm_service" in services
        
        # Test Celery app is configured
        celery_app = services["celery_app"]
        assert celery_app.conf.broker_url.startswith("redis://")
        
        # Test MinIO client is working
        minio_client = services["minio_client"]
        bucket = services["minio_bucket"]
        assert minio_client.bucket_exists(bucket)
        
        # Test VM service is working
        vm_service = services["vm_service"]
        assert hasattr(vm_service, 'docker_client')

    def test_worker_service_error_handling_real(self, real_worker_services):
        """Test error handling in worker services"""
        services = real_worker_services
        file_worker = services["file_worker"]
        
        # Test graceful error handling
        result = file_worker.download_file_from_minio("definitely_nonexistent_file.xyz")
        
        assert result["success"] is False
        assert "error" in result
        assert isinstance(result["error"], str)

    @pytest.mark.asyncio
    async def test_concurrent_worker_operations_real(self, real_worker_services):
        """Test concurrent worker operations"""
        services = real_worker_services
        vm_worker = services["vm_worker"]
        
        # Create multiple VMs concurrently
        tasks = []
        for i in range(3):
            task = vm_worker.create_vm_for_processing("ubuntu:20.04")
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify results
        successful_vms = []
        for result in results:
            if isinstance(result, dict) and result.get("success"):
                successful_vms.append(result["vm_id"])
        
        # At least 2 out of 3 should succeed (resource constraints)
        assert len(successful_vms) >= 2
        
        # Cleanup all successful VMs
        for vm_id in successful_vms:
            await vm_worker.destroy_vm(vm_id)
