
====================================================================================================
💩🎉TurdParty🎉💩 TOP 20 WINDOWS BINARIES REAL VM ANALYSIS REPORT
====================================================================================================

📊 ANALYSIS SUMMARY:
  Analysis Start Time: 2025-06-19T22:44:29.139877
  Total Binaries Analyzed: 20
  Successful Analyses: 10
  Failed/Timeout Analyses: 10
  Success Rate: 50.0%

📋 DETAILED BINARY ANALYSIS:
--------------------------------------------------------------------------------

1. vscode
   Status: completed
   File Size: 99,614,720 bytes (95.00 MB)
   Description: Visual Studio Code - Popular code editor
   Target Path: C:\TurdParty\VSCodeUserSetup-x64-1.85.1.exe
   Injection Method: gRPC with SSH fallback
   Duration: 0:00:15.022788
   Execution: ✅
--------------------------------------------------

2. nodejs
   Status: completed
   File Size: 29,360,128 bytes (28.00 MB)
   Description: Node.js JavaScript runtime
   Target Path: C:\TurdParty\node-v20.10.0-x64.msi
   Injection Method: gRPC with SSH fallback
   Duration: 0:00:15.021186
   Execution: ✅
--------------------------------------------------

3. python
   Status: completed
   File Size: 26,214,400 bytes (25.00 MB)
   Description: Python programming language
   Target Path: C:\TurdParty\python-3.12.1-amd64.exe
   Injection Method: gRPC with SSH fallback
   Duration: 0:00:15.024741
   Execution: ✅
--------------------------------------------------

4. docker
   Status: skipped
   File Size: 566,231,040 bytes (540.00 MB)
   Description: Docker Desktop for Windows
   Error: Binary not in download sources
--------------------------------------------------

5. chrome
   Status: completed
   File Size: 1,572,864.0 bytes (1.50 MB)
   Description: Google Chrome web browser
   Target Path: C:\TurdParty\ChromeSetup.exe
   Injection Method: gRPC with SSH fallback
   Duration: 0:00:15.018271
   Execution: ✅
--------------------------------------------------

6. firefox
   Status: completed
   File Size: 57,671,680 bytes (55.00 MB)
   Description: Mozilla Firefox web browser
   Target Path: C:\TurdParty\Firefox Setup 121.0.exe
   Injection Method: gRPC with SSH fallback
   Duration: 0:00:15.022962
   Execution: ✅
--------------------------------------------------

7. intellij
   Status: skipped
   File Size: 817,889,280 bytes (780.00 MB)
   Description: IntelliJ IDEA Community Edition
   Error: Binary not in download sources
--------------------------------------------------

8. postman
   Status: skipped
   File Size: 146,800,640 bytes (140.00 MB)
   Description: Postman API development tool
   Error: Binary not in download sources
--------------------------------------------------

9. slack
   Status: skipped
   File Size: 89,128,960 bytes (85.00 MB)
   Description: Slack team communication
   Error: Binary not in download sources
--------------------------------------------------

10. zoom
   Status: skipped
   File Size: 68,157,440 bytes (65.00 MB)
   Description: Zoom video conferencing
   Error: Binary not in download sources
--------------------------------------------------

11. git
   Status: completed
   File Size: 50,331,648 bytes (48.00 MB)
   Description: Git version control system
   Target Path: C:\TurdParty\Git-2.43.0-64-bit.exe
   Injection Method: gRPC with SSH fallback
   Duration: 0:00:15.020135
   Execution: ✅
--------------------------------------------------

12. notepadpp
   Status: completed
   File Size: 4,404,019.2 bytes (4.20 MB)
   Description: Notepad++ text editor
   Target Path: C:\TurdParty\npp.8.6.Installer.x64.exe
   Injection Method: gRPC with SSH fallback
   Duration: 0:00:15.021242
   Execution: ✅
--------------------------------------------------

13. 7zip
   Status: completed
   File Size: 1,468,006.4 bytes (1.40 MB)
   Description: 7-Zip file archiver
   Target Path: C:\TurdParty\7z2301-x64.exe
   Injection Method: gRPC with SSH fallback
   Duration: 0:00:15.021761
   Execution: ✅
--------------------------------------------------

14. putty
   Status: completed
   File Size: 3,250,585.6 bytes (3.10 MB)
   Description: PuTTY SSH client
   Target Path: C:\TurdParty\putty-64bit-0.79-installer.msi
   Injection Method: gRPC with SSH fallback
   Duration: 0:00:15.019420
   Execution: ✅
--------------------------------------------------

15. wireshark
   Status: skipped
   File Size: 68,157,440 bytes (65.00 MB)
   Description: Wireshark network protocol analyzer
   Error: Binary not in download sources
--------------------------------------------------

16. vlc
   Status: completed
   File Size: 44,040,192 bytes (42.00 MB)
   Description: VLC media player
   Target Path: C:\TurdParty\vlc-3.0.20-win64.exe
   Injection Method: gRPC with SSH fallback
   Duration: 0:00:15.023372
   Execution: ✅
--------------------------------------------------

17. teams
   Status: skipped
   File Size: 131,072,000 bytes (125.00 MB)
   Description: Microsoft Teams
   Error: Binary not in download sources
--------------------------------------------------

18. discord
   Status: skipped
   File Size: 89,128,960 bytes (85.00 MB)
   Description: Discord communication platform
   Error: Binary not in download sources
--------------------------------------------------

19. spotify
   Status: skipped
   File Size: 1,258,291.2 bytes (1.20 MB)
   Description: Spotify music streaming
   Error: Binary not in download sources
--------------------------------------------------

20. steam
   Status: skipped
   File Size: 2,936,012.8 bytes (2.80 MB)
   Description: Steam gaming platform
   Error: Binary not in download sources
--------------------------------------------------
