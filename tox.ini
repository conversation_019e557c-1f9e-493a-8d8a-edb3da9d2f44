[tox]
envlist = 
    py311
    security
    performance
    property
    coverage
    docs
    lint
isolated_build = true
skip_missing_interpreters = true

[gh-actions]
python =
    3.11: py311

[testenv]
deps = 
    pytest>=7.4.0
    pytest-asyncio>=0.21.0
    pytest-cov>=4.1.0
    pytest-xdist>=3.5.0
    pytest-mock>=3.12.0
    pytest-timeout>=2.2.0
    httpx>=0.25.0
    fastapi>=0.104.0
    pydantic>=2.5.0
commands = 
    pytest {posargs:tests/unit/}

[testenv:security]
deps = 
    {[testenv]deps}
    bandit>=1.7.5
    safety>=2.3.0
commands = 
    bandit -r api/ services/ -f json -o reports/bandit-report.json
    bandit -r api/ services/ -f txt
    safety check --json --output reports/safety-report.json
    safety check

[testenv:performance]
deps = 
    {[testenv]deps}
    pytest-benchmark>=4.0.0
    locust>=2.17.0
commands = 
    pytest tests/performance/ --benchmark-only --benchmark-json=reports/benchmark-report.json {posargs}

[testenv:property]
deps = 
    {[testenv]deps}
    hypothesis>=6.92.0
    factory-boy>=3.3.0
    faker>=20.1.0
commands = 
    pytest tests/property/ {posargs}

[testenv:coverage]
deps = 
    {[testenv]deps}
    coverage[toml]>=7.3.0
commands = 
    coverage erase
    coverage run -m pytest tests/unit/ tests/integration/
    coverage report --show-missing --fail-under=80
    coverage html -d reports/coverage-html
    coverage json -o reports/coverage.json

[testenv:docs]
deps = 
    sphinx>=7.2.0
    sphinx-rtd-theme>=1.3.0
    myst-parser>=2.0.0
commands = 
    sphinx-build -W -b html docs/ docs/_build/html

[testenv:lint]
deps = 
    ruff>=0.1.6
    mypy>=1.7.0
    types-requests
    types-redis
    types-python-dateutil
commands = 
    ruff check api/ services/ tests/
    ruff format --check api/ services/ tests/
    mypy api/ services/

[testenv:integration]
deps = 
    {[testenv]deps}
    testcontainers>=3.7.0
    docker>=6.1.0
commands = 
    pytest tests/integration/ {posargs}

[testenv:e2e]
deps = 
    {[testenv]deps}
    playwright>=1.40.0
commands = 
    playwright install
    pytest tests/e2e/ {posargs}

[testenv:load]
deps = 
    locust>=2.17.0
    requests>=2.31.0
commands = 
    locust -f tests/load/locustfile.py --headless -u 10 -r 2 -t 30s --host=http://localhost:8000

[testenv:mutation]
deps = 
    {[testenv]deps}
    mutmut>=2.4.0
commands = 
    mutmut run --paths-to-mutate=api/,services/

[testenv:clean]
deps = 
skip_install = true
commands = 
    python -c "import shutil; shutil.rmtree('reports', ignore_errors=True)"
    python -c "import shutil; shutil.rmtree('.coverage', ignore_errors=True)"
    python -c "import shutil; shutil.rmtree('htmlcov', ignore_errors=True)"
    python -c "import shutil; shutil.rmtree('.pytest_cache', ignore_errors=True)"
    python -c "import shutil; shutil.rmtree('.tox', ignore_errors=True)"
    python -c "import shutil; shutil.rmtree('__pycache__', ignore_errors=True)"

[testenv:dev]
deps = 
    {[testenv]deps}
    pytest-benchmark>=4.0.0
    hypothesis>=6.92.0
    factory-boy>=3.3.0
    faker>=20.1.0
    ruff>=0.1.6
    mypy>=1.7.0
    bandit>=1.7.5
    safety>=2.3.0
    pre-commit>=3.6.0
commands = 
    pre-commit install
    pytest tests/unit/ --cov=api --cov=services --cov-report=html --cov-report=term-missing

[testenv:ci]
deps = 
    {[testenv]deps}
    pytest-benchmark>=4.0.0
    hypothesis>=6.92.0
    bandit>=1.7.5
    safety>=2.3.0
    coverage[toml]>=7.3.0
commands = 
    pytest tests/unit/ tests/integration/ --cov=api --cov=services --cov-report=xml --cov-report=term-missing --junitxml=reports/junit.xml
    bandit -r api/ services/ -f json -o reports/bandit-report.json
    safety check --json --output reports/safety-report.json

[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov-report=term-missing
    --cov-fail-under=80
markers =
    unit: Unit tests
    integration: Integration tests
    performance: Performance tests
    security: Security tests
    slow: Slow running tests
    benchmark: Benchmark tests
    property: Property-based tests
    e2e: End-to-end tests
    load: Load tests
