#!/usr/bin/env python3
"""
TurdParty Run Tracker - Multiple Execution Management
Tracks multiple runs of the same UUID and provides run numbering for footprint comparison.
"""

import json
import os
import threading
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import uuid

class RunTracker:
    """Tracks multiple runs of the same UUID for footprint comparison."""
    
    def __init__(self, storage_path: str = "/tmp/turdparty_runs.json"):
        """Initialize run tracker with persistent storage."""
        self.storage_path = Path(storage_path)
        self.lock = threading.Lock()
        self._runs_data = self._load_runs_data()
    
    def _load_runs_data(self) -> Dict:
        """Load existing runs data from storage."""
        try:
            if self.storage_path.exists():
                with open(self.storage_path, 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Warning: Could not load runs data: {e}")
        
        return {
            "runs": {},  # file_id -> list of run records
            "metadata": {
                "created": datetime.now(timezone.utc).isoformat(),
                "version": "1.0.0"
            }
        }
    
    def _save_runs_data(self):
        """Save runs data to persistent storage."""
        try:
            with open(self.storage_path, 'w') as f:
                json.dump(self._runs_data, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save runs data: {e}")
    
    def start_new_run(self, file_id: str, workflow_id: str, vm_id: str) -> Tuple[int, str]:
        """
        Start a new run for a file UUID.
        
        Returns:
            Tuple[int, str]: (run_number, run_id)
        """
        with self.lock:
            if file_id not in self._runs_data["runs"]:
                self._runs_data["runs"][file_id] = []
            
            run_number = len(self._runs_data["runs"][file_id]) + 1
            run_id = f"{file_id}-run-{run_number}"
            
            run_record = {
                "run_number": run_number,
                "run_id": run_id,
                "file_id": file_id,
                "workflow_id": workflow_id,
                "vm_id": vm_id,
                "start_time": datetime.now(timezone.utc).isoformat(),
                "status": "running",
                "install_events": 0,
                "runtime_events": 0,
                "install_uuid": None,
                "runtime_uuids": []
            }
            
            self._runs_data["runs"][file_id].append(run_record)
            self._save_runs_data()
            
            return run_number, run_id
    
    def complete_run(self, file_id: str, run_number: int, 
                    install_events: int = 0, runtime_events: int = 0,
                    install_uuid: Optional[str] = None, 
                    runtime_uuids: Optional[List[str]] = None):
        """Mark a run as completed with event counts."""
        with self.lock:
            if file_id in self._runs_data["runs"]:
                runs = self._runs_data["runs"][file_id]
                for run in runs:
                    if run["run_number"] == run_number:
                        run["status"] = "completed"
                        run["end_time"] = datetime.now(timezone.utc).isoformat()
                        run["install_events"] = install_events
                        run["runtime_events"] = runtime_events
                        if install_uuid:
                            run["install_uuid"] = install_uuid
                        if runtime_uuids:
                            run["runtime_uuids"] = runtime_uuids
                        break
                
                self._save_runs_data()
    
    def get_run_info(self, file_id: str, run_number: Optional[int] = None) -> Dict:
        """Get run information for a file UUID."""
        with self.lock:
            if file_id not in self._runs_data["runs"]:
                return {}
            
            runs = self._runs_data["runs"][file_id]
            
            if run_number is None:
                # Return all runs
                return {
                    "file_id": file_id,
                    "total_runs": len(runs),
                    "runs": runs
                }
            else:
                # Return specific run
                for run in runs:
                    if run["run_number"] == run_number:
                        return run
                return {}
    
    def get_run_comparison(self, file_id: str, run1: int, run2: int) -> Dict:
        """Compare two runs of the same file UUID."""
        with self.lock:
            if file_id not in self._runs_data["runs"]:
                return {"error": "File ID not found"}
            
            runs = self._runs_data["runs"][file_id]
            run1_data = None
            run2_data = None
            
            for run in runs:
                if run["run_number"] == run1:
                    run1_data = run
                elif run["run_number"] == run2:
                    run2_data = run
            
            if not run1_data or not run2_data:
                return {"error": "One or both runs not found"}
            
            return {
                "file_id": file_id,
                "comparison": {
                    "run_1": run1_data,
                    "run_2": run2_data,
                    "differences": {
                        "install_events_delta": run2_data["install_events"] - run1_data["install_events"],
                        "runtime_events_delta": run2_data["runtime_events"] - run1_data["runtime_events"],
                        "time_difference": self._calculate_time_diff(run1_data, run2_data)
                    }
                }
            }
    
    def _calculate_time_diff(self, run1: Dict, run2: Dict) -> str:
        """Calculate time difference between two runs."""
        try:
            if "end_time" in run1 and "end_time" in run2:
                time1 = datetime.fromisoformat(run1["end_time"].replace('Z', '+00:00'))
                time2 = datetime.fromisoformat(run2["end_time"].replace('Z', '+00:00'))
                diff = abs((time2 - time1).total_seconds())
                return f"{diff:.2f} seconds"
        except Exception:
            pass
        return "unknown"
    
    def get_all_files_summary(self) -> Dict:
        """Get summary of all tracked files and their runs."""
        with self.lock:
            summary = {
                "total_files": len(self._runs_data["runs"]),
                "total_runs": sum(len(runs) for runs in self._runs_data["runs"].values()),
                "files": {}
            }
            
            for file_id, runs in self._runs_data["runs"].items():
                summary["files"][file_id] = {
                    "total_runs": len(runs),
                    "latest_run": runs[-1] if runs else None,
                    "status": runs[-1]["status"] if runs else "unknown"
                }
            
            return summary
    
    def cleanup_old_runs(self, days_old: int = 30):
        """Clean up runs older than specified days."""
        cutoff_date = datetime.now(timezone.utc).timestamp() - (days_old * 24 * 60 * 60)
        
        with self.lock:
            files_to_remove = []
            
            for file_id, runs in self._runs_data["runs"].items():
                # Filter out old runs
                recent_runs = []
                for run in runs:
                    try:
                        run_time = datetime.fromisoformat(run["start_time"].replace('Z', '+00:00')).timestamp()
                        if run_time > cutoff_date:
                            recent_runs.append(run)
                    except Exception:
                        # Keep runs with invalid timestamps
                        recent_runs.append(run)
                
                if recent_runs:
                    self._runs_data["runs"][file_id] = recent_runs
                else:
                    files_to_remove.append(file_id)
            
            # Remove files with no recent runs
            for file_id in files_to_remove:
                del self._runs_data["runs"][file_id]
            
            self._save_runs_data()
            
            return {
                "cleaned_files": len(files_to_remove),
                "remaining_files": len(self._runs_data["runs"])
            }


# Global run tracker instance
_run_tracker = None

def get_run_tracker() -> RunTracker:
    """Get global run tracker instance."""
    global _run_tracker
    if _run_tracker is None:
        _run_tracker = RunTracker()
    return _run_tracker


def start_file_run(file_id: str, workflow_id: str, vm_id: str) -> Tuple[int, str]:
    """Convenience function to start a new run."""
    return get_run_tracker().start_new_run(file_id, workflow_id, vm_id)


def complete_file_run(file_id: str, run_number: int, **kwargs):
    """Convenience function to complete a run."""
    return get_run_tracker().complete_run(file_id, run_number, **kwargs)


def get_file_runs(file_id: str, run_number: Optional[int] = None) -> Dict:
    """Convenience function to get run information."""
    return get_run_tracker().get_run_info(file_id, run_number)


def compare_runs(file_id: str, run1: int, run2: int) -> Dict:
    """Convenience function to compare two runs."""
    return get_run_tracker().get_run_comparison(file_id, run1, run2)


if __name__ == "__main__":
    # Example usage
    tracker = RunTracker()
    
    # Start a new run
    file_id = "test-uuid-123"
    run_num, run_id = tracker.start_new_run(file_id, "workflow-1", "vm-1")
    print(f"Started run {run_num} with ID {run_id}")
    
    # Complete the run
    tracker.complete_run(file_id, run_num, install_events=5, runtime_events=10)
    
    # Start another run
    run_num2, run_id2 = tracker.start_new_run(file_id, "workflow-1", "vm-1")
    tracker.complete_run(file_id, run_num2, install_events=7, runtime_events=12)
    
    # Compare runs
    comparison = tracker.get_run_comparison(file_id, 1, 2)
    print(f"Comparison: {json.dumps(comparison, indent=2)}")
    
    # Get summary
    summary = tracker.get_all_files_summary()
    print(f"Summary: {json.dumps(summary, indent=2)}")
