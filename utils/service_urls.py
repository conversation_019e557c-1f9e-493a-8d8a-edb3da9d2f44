#!/usr/bin/env python3
"""
TurdParty Service URL Configuration Manager

Centralized URL management system for the TurdParty malware analysis platform.
Provides dynamic service URL generation, environment-specific configuration,
and comprehensive API endpoint management across different deployment scenarios.

Key Features:
    - Multi-environment support (development, staging, production, local)
    - Dynamic service URL generation with subdomain and port management
    - Comprehensive API endpoint configuration and formatting
    - Health check URL generation for monitoring integration
    - Traefik reverse proxy integration with proper routing
    - Environment switching and configuration validation

Supported Environments:
    1. **Development**: Local development with Traefik routing
    2. **Staging**: Pre-production environment with external domains
    3. **Production**: Production deployment with secure HTTPS
    4. **Local**: Direct service access without reverse proxy
    5. **Local-Direct**: Direct port access for testing and debugging

Service Categories:
    - **Core Services**: API, frontend, database, Redis
    - **ELK Stack**: Elasticsearch, Logstash, Kibana, Filebeat
    - **Storage**: MinIO object storage with bucket management
    - **Monitoring**: Status dashboard, Celery Flower, documentation
    - **Infrastructure**: VM management, worker services

API Endpoint Categories:
    - **Files**: Upload, download, metadata management
    - **VMs**: Creation, management, lifecycle operations
    - **Health**: Service health checks and status monitoring
    - **Analytics**: Reporting, metrics, and analysis endpoints
    - **WebSocket**: Real-time communication endpoints

Configuration Management:
    - JSON-based configuration with environment-specific overrides
    - Service discovery integration for dynamic environments
    - Port and subdomain management with conflict resolution
    - Protocol selection (HTTP/HTTPS) based on environment

Usage:
    # Basic service URL retrieval
    manager = ServiceURLManager('development')
    api_url = manager.get_service_url('api')

    # API endpoint generation
    upload_endpoint = manager.get_api_endpoint('files', 'upload')
    vm_create_endpoint = manager.get_api_endpoint('vms', 'create')

    # Health check URLs for monitoring
    health_urls = manager.health_check_urls()

Integration:
    - Docker Compose service discovery
    - Traefik reverse proxy routing
    - Kubernetes service mesh compatibility
    - CI/CD pipeline environment management
"""

import json
import os
from pathlib import Path
from typing import Dict, Optional, Any
from urllib.parse import urljoin


class ServiceURLManager:
    """
    Manages service URLs across different environments and domains.

    Provides centralized URL management for the TurdParty platform with
    support for multiple deployment environments, dynamic service discovery,
    and comprehensive API endpoint generation.
    """
    
    def __init__(self, environment: str = None, config_path: str = None):
        """
        Initialize the URL manager.
        
        Args:
            environment: Environment name (development, staging, production, local)
            config_path: Path to service-urls.json config file
        """
        self.environment = environment or os.getenv('TURDPARTY_ENV', 'development')
        
        # Default config path
        if config_path is None:
            project_root = Path(__file__).parent.parent
            config_path = project_root / 'config' / 'service-urls.json'
        
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.env_config = self.config['environments'].get(self.environment)
        
        if not self.env_config:
            raise ValueError(f"Environment '{self.environment}' not found in config")
    
    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from JSON file.

        Returns:
            dict: Complete service URL configuration including environments and endpoints

        Raises:
            FileNotFoundError: If configuration file doesn't exist
            json.JSONDecodeError: If configuration file is invalid JSON
        """
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Service URL config not found: {self.config_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in config file: {e}")
    
    def get_service_url(self, service_name: str, include_health: bool = False) -> str:
        """
        Get the full URL for a service.
        
        Args:
            service_name: Name of the service (api, frontend, elasticsearch, etc.)
            include_health: Whether to append the health endpoint
            
        Returns:
            Full URL for the service
        """
        service_config = self.env_config['services'].get(service_name)
        if not service_config:
            raise ValueError(f"Service '{service_name}' not found in environment '{self.environment}'")
        
        # Build base URL
        protocol = self.env_config['protocol']
        domain = self.env_config['domain']
        subdomain = service_config['subdomain']
        port = service_config['port']
        path = service_config['path']
        
        # Construct URL
        if subdomain:
            host = f"{subdomain}.{domain}"
        else:
            host = domain
        
        if port:
            host = f"{host}:{port}"
        
        base_url = f"{protocol}://{host}"
        
        if path:
            base_url = urljoin(base_url, path)
        
        # Add health endpoint if requested
        if include_health:
            health_endpoint = service_config['health_endpoint']
            if health_endpoint:
                base_url = urljoin(base_url, health_endpoint.lstrip('/'))
        
        return base_url
    
    def get_api_endpoint(self, category: str, endpoint: str, **kwargs) -> str:
        """
        Get a full API endpoint URL.
        
        Args:
            category: API category (files, vms, health, analytics)
            endpoint: Endpoint name within the category
            **kwargs: Parameters to format into the endpoint path
            
        Returns:
            Full API endpoint URL
        """
        api_endpoints = self.config['api_endpoints']
        
        if category not in api_endpoints:
            raise ValueError(f"API category '{category}' not found")
        
        if endpoint not in api_endpoints[category]:
            raise ValueError(f"Endpoint '{endpoint}' not found in category '{category}'")
        
        endpoint_path = api_endpoints[category][endpoint]
        
        # Format parameters into the path
        if kwargs:
            endpoint_path = endpoint_path.format(**kwargs)
        
        # Get base API URL and combine with endpoint
        api_base_url = self.get_service_url('api')
        return urljoin(api_base_url, endpoint_path.lstrip('/'))
    
    def get_elasticsearch_index(self, index_type: str) -> str:
        """Get Elasticsearch index pattern for a specific type."""
        indices = self.config['elasticsearch_indices']
        if index_type not in indices:
            raise ValueError(f"Index type '{index_type}' not found")
        return indices[index_type]
    
    def get_minio_bucket(self, bucket_type: str) -> str:
        """Get MinIO bucket name for a specific type."""
        buckets = self.config['minio_buckets']
        if bucket_type not in buckets:
            raise ValueError(f"Bucket type '{bucket_type}' not found")
        return buckets[bucket_type]
    
    def get_all_service_urls(self, include_health: bool = False) -> Dict[str, str]:
        """
        Get URLs for all services in the current environment.

        Args:
            include_health: Whether to append health check endpoints

        Returns:
            dict: Mapping of service names to their complete URLs
        """
        urls = {}
        for service_name in self.env_config['services'].keys():
            urls[service_name] = self.get_service_url(service_name, include_health)
        return urls
    
    def health_check_urls(self) -> Dict[str, str]:
        """
        Get health check URLs for all services.

        Returns:
            dict: Mapping of service names to their health check URLs
        """
        return self.get_all_service_urls(include_health=True)
    
    def switch_environment(self, new_environment: str):
        """
        Switch to a different environment.

        Args:
            new_environment: Name of the environment to switch to

        Raises:
            ValueError: If the specified environment doesn't exist
        """
        if new_environment not in self.config['environments']:
            raise ValueError(f"Environment '{new_environment}' not found")
        
        self.environment = new_environment
        self.env_config = self.config['environments'][new_environment]
    
    def list_environments(self) -> list:
        """
        List all available environments.

        Returns:
            list: Names of all configured environments
        """
        return list(self.config['environments'].keys())
    
    def list_services(self) -> list:
        """
        List all services in the current environment.

        Returns:
            list: Names of all services configured for the current environment
        """
        return list(self.env_config['services'].keys())
    
    def get_environment_info(self) -> Dict[str, Any]:
        """
        Get information about the current environment.

        Returns:
            dict: Environment information including domain, protocol, and services
        """
        return {
            'environment': self.environment,
            'domain': self.env_config['domain'],
            'protocol': self.env_config['protocol'],
            'services': list(self.env_config['services'].keys())
        }


# Convenience functions for common use cases
def get_url_manager(environment: str = None) -> ServiceURLManager:
    """
    Get a configured URL manager instance.

    Args:
        environment: Environment name (defaults to TURDPARTY_ENV or 'development')

    Returns:
        ServiceURLManager: Configured URL manager instance
    """
    return ServiceURLManager(environment)


def get_service_url(service_name: str, environment: str = None) -> str:
    """
    Quick function to get a service URL.

    Args:
        service_name: Name of the service to get URL for
        environment: Environment name (optional)

    Returns:
        str: Complete service URL
    """
    manager = get_url_manager(environment)
    return manager.get_service_url(service_name)


def get_api_endpoint(category: str, endpoint: str, environment: str = None, **kwargs) -> str:
    """
    Quick function to get an API endpoint URL.

    Args:
        category: API category (files, vms, health, analytics)
        endpoint: Endpoint name within the category
        environment: Environment name (optional)
        **kwargs: Parameters to format into the endpoint path

    Returns:
        str: Complete API endpoint URL
    """
    manager = get_url_manager(environment)
    return manager.get_api_endpoint(category, endpoint, **kwargs)


# Example usage and testing
if __name__ == "__main__":
    # Test the URL manager
    print("🔧 Testing TurdParty Service URL Manager")
    print("=" * 50)
    
    # Test different environments
    for env in ['development', 'staging', 'production', 'local']:
        print(f"\n📍 Environment: {env}")
        try:
            manager = ServiceURLManager(env)
            
            # Show environment info
            info = manager.get_environment_info()
            print(f"   Domain: {info['domain']}")
            print(f"   Protocol: {info['protocol']}")
            
            # Show key service URLs
            api_url = manager.get_service_url('api')
            frontend_url = manager.get_service_url('frontend')
            elasticsearch_url = manager.get_service_url('elasticsearch')
            
            print(f"   API: {api_url}")
            print(f"   Frontend: {frontend_url}")
            print(f"   Elasticsearch: {elasticsearch_url}")
            
            # Show API endpoints
            upload_endpoint = manager.get_api_endpoint('files', 'upload')
            vm_create_endpoint = manager.get_api_endpoint('vms', 'create')
            
            print(f"   Upload API: {upload_endpoint}")
            print(f"   VM Create API: {vm_create_endpoint}")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n✅ URL Manager testing complete!")
