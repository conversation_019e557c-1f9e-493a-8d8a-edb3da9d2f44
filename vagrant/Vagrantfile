# TurdParty Vagrantfile
# Configured for malware analysis VMs with TurdParty integration

Vagrant.configure("2") do |config|
  # Default box - Ubuntu 20.04 LTS (secure and well-supported)
  config.vm.box = "generic/ubuntu2004"
  config.vm.box_check_update = false
  
  # VM configuration for malware analysis
  config.vm.provider "virtualbox" do |vb|
    vb.memory = 2048
    vb.cpus = 2
    vb.name = "turdparty-analysis-vm"
    
    # Enable nested virtualization if supported
    vb.customize ["modifyvm", :id, "--nested-hw-virt", "on"]
    
    # Disable audio and USB for security
    vb.customize ["modifyvm", :id, "--audio", "none"]
    vb.customize ["modifyvm", :id, "--usb", "off"]
    
    # Network isolation settings
    vb.customize ["modifyvm", :id, "--natdnshostresolver1", "on"]
    vb.customize ["modifyvm", :id, "--natdnsproxy1", "on"]
  end
  
  # Alternative provider: libvirt (for Linux hosts)
  config.vm.provider "libvirt" do |libvirt|
    libvirt.memory = 2048
    libvirt.cpus = 2
    libvirt.driver = "kvm"
    libvirt.nested = true
    libvirt.cpu_mode = "host-passthrough"
  end
  
  # Network configuration for TurdParty integration
  # SSH access
  config.vm.network "forwarded_port", guest: 22, host: 2222, auto_correct: true
  
  # TurdParty gRPC communication
  config.vm.network "forwarded_port", guest: 40000, host: 40000, auto_correct: true
  config.vm.network "forwarded_port", guest: 50051, host: 50051, auto_correct: true
  
  # HTTP/HTTPS for web-based malware
  config.vm.network "forwarded_port", guest: 80, host: 8080, auto_correct: true
  config.vm.network "forwarded_port", guest: 443, host: 8443, auto_correct: true
  
  # Common malware communication ports (for monitoring)
  config.vm.network "forwarded_port", guest: 53, host: 5353, auto_correct: true    # DNS
  config.vm.network "forwarded_port", guest: 25, host: 2525, auto_correct: true    # SMTP
  config.vm.network "forwarded_port", guest: 21, host: 2121, auto_correct: true    # FTP
  
  # Windows-specific ports (if using Windows VMs)
  config.vm.network "forwarded_port", guest: 3389, host: 3389, auto_correct: true  # RDP
  config.vm.network "forwarded_port", guest: 5985, host: 5985, auto_correct: true  # WinRM HTTP
  config.vm.network "forwarded_port", guest: 5986, host: 5986, auto_correct: true  # WinRM HTTPS
  
  # Shared folder for file injection and results
  config.vm.synced_folder "./shared", "/vagrant/shared", create: true
  config.vm.synced_folder "./samples", "/vagrant/samples", create: true
  
  # Disable default shared folder for security
  config.vm.synced_folder ".", "/vagrant", disabled: true
  
  # Provisioning script for TurdParty agent installation
  config.vm.provision "shell", inline: <<-SHELL
    # Update system packages
    apt-get update
    apt-get upgrade -y
    
    # Install essential packages
    apt-get install -y python3 python3-pip curl wget unzip git
    apt-get install -y build-essential linux-headers-$(uname -r)
    
    # Install monitoring and analysis tools
    apt-get install -y htop iotop nethogs tcpdump wireshark-common
    apt-get install -y strace ltrace gdb
    
    # Install Python packages for TurdParty agent
    pip3 install --upgrade pip
    pip3 install psutil requests websockets grpcio grpcio-tools
    pip3 install watchdog python-magic yara-python
    
    # Create TurdParty directories
    mkdir -p /opt/turdparty/{agent,logs,samples,results}
    mkdir -p /var/log/turdparty
    
    # Create TurdParty user for agent
    useradd -r -s /bin/false -d /opt/turdparty turdparty
    chown -R turdparty:turdparty /opt/turdparty
    chown -R turdparty:turdparty /var/log/turdparty
    
    # Install TurdParty monitoring agent (placeholder)
    cat > /opt/turdparty/agent/monitor.py << 'EOF'
#!/usr/bin/env python3
"""
TurdParty VM Monitoring Agent
Monitors system activity and reports to TurdParty platform
"""

import json
import time
import psutil
import logging
import requests
from datetime import datetime, timezone

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/turdparty/agent.log'),
        logging.StreamHandler()
    ]
)

class TurdPartyAgent:
    def __init__(self):
        self.api_endpoint = "http://host.docker.internal:8000/api/v1"
        self.vm_id = None
        self.running = True
        
    def collect_metrics(self):
        """Collect system metrics"""
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent,
            'network_io': dict(psutil.net_io_counters()._asdict()),
            'process_count': len(psutil.pids()),
            'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0]
        }
    
    def send_metrics(self, metrics):
        """Send metrics to TurdParty API"""
        try:
            response = requests.post(
                f"{self.api_endpoint}/metrics/vm",
                json=metrics,
                timeout=5
            )
            if response.status_code == 200:
                logging.debug("Metrics sent successfully")
            else:
                logging.warning(f"Failed to send metrics: {response.status_code}")
        except Exception as e:
            logging.error(f"Error sending metrics: {e}")
    
    def run(self):
        """Main monitoring loop"""
        logging.info("TurdParty Agent starting...")
        
        while self.running:
            try:
                metrics = self.collect_metrics()
                self.send_metrics(metrics)
                time.sleep(10)  # Send metrics every 10 seconds
            except KeyboardInterrupt:
                logging.info("Agent stopping...")
                self.running = False
            except Exception as e:
                logging.error(f"Agent error: {e}")
                time.sleep(30)  # Wait before retrying

if __name__ == "__main__":
    agent = TurdPartyAgent()
    agent.run()
EOF
    
    chmod +x /opt/turdparty/agent/monitor.py
    
    # Create systemd service for TurdParty agent
    cat > /etc/systemd/system/turdparty-agent.service << 'EOF'
[Unit]
Description=TurdParty VM Monitoring Agent
After=network.target

[Service]
Type=simple
User=turdparty
Group=turdparty
WorkingDirectory=/opt/turdparty/agent
ExecStart=/usr/bin/python3 /opt/turdparty/agent/monitor.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
    
    # Enable and start the agent service
    systemctl daemon-reload
    systemctl enable turdparty-agent
    systemctl start turdparty-agent
    
    # Create status file
    echo "TurdParty VM ready for analysis - $(date)" > /opt/turdparty/status
    echo "Agent installed and running" >> /opt/turdparty/status
    
    # Log completion
    echo "TurdParty VM provisioning completed successfully"
  SHELL
  
  # Post-provisioning message
  config.vm.post_up_message = <<-MSG
    TurdParty Analysis VM is ready!
    
    VM Details:
    - SSH: vagrant ssh (or ssh vagrant@localhost -p 2222)
    - gRPC: localhost:40000, localhost:50051
    - Web: http://localhost:8080, https://localhost:8443
    - RDP: localhost:3389 (Windows VMs)
    
    TurdParty Integration:
    - Agent: systemctl status turdparty-agent
    - Logs: tail -f /var/log/turdparty/agent.log
    - Status: cat /opt/turdparty/status
    
    File Locations:
    - Samples: /vagrant/samples
    - Results: /opt/turdparty/results
    - Shared: /vagrant/shared
  MSG
end
