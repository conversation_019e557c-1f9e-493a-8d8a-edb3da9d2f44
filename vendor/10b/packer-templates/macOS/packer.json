{"builders": [{"accelerator": "kvm", "boot_wait": "2s", "disk_cache": "unsafe", "disk_interface": "virtio", "disk_size": "40000M", "cd_files": ["./files/virtio-drivers/*"], "cd_label": "cidata", "floppy_files": [], "format": "qcow2", "headless": "false", "host_port_max": "6985", "host_port_min": "5985", "iso_checksum": "md5:{{user `iso_md5`}}", "iso_url": "{{user `iso_path`}}", "net_device": "virtio-net", "qemuargs": [["-m", "5048M"], ["-cpu", "host"], ["-m", "5048M"], ["-smp", "cpus=6"]], "shutdown_command": "shutdown ", "skip_compaction": false, "type": "qemu", "vm_name": "macOS-13.7", "ssh_username": "vagrant", "ssh_password": "vagrant", "ssh_timeout": "69m", "ssh_handshake_attempts": 420}], "post-processors": [{"keep_input_artifact": false, "output": "../../vagrant_templates/macOS-13.7.box", "type": "vagrant", "vagrantfile_template": "vagrantfile.template"}], "provisioners": [], "variables": {"build_uuid": "", "iso_md5": "", "iso_path": "", "out_path": "", "switch_name": "<PERSON><PERSON><PERSON>"}}