#!/bin/bash
# https://github.com/kholia/OSX-KVM/blob/master/OpenCore-Boot.sh
# TODO: Create OpenCore QCOW2 image (https://github.com/kholia/OSX-KVM/tree/master/OpenCore OR/AND https://github.com/thenickdude/KVM-Opencore)
ALLOCATED_RAM="4096" # MiB
CPU_SOCKETS="1"
CPU_CORES="2"
CPU_THREADS="4"

REPO_PATH="."
# prep OVMF
cp /nix/store/cspaja242a9l1nx5y68aadyvjp4kbc7w-OVMF-202402-fd/FV/OVMF_CODE.fd .
cp /nix/store/cspaja242a9l1nx5y68aadyvjp4kbc7w-OVMF-202402-fd/FV/OVMF_VARS.fd .
# OVMF
OVMF_CODE="OVMF_CODE.fd"
OVMF_VARS="OVMF_VARS.fd"

MY_OPTIONS="+ssse3,+sse4.2,+popcnt,+avx,+aes,+xsave,+xsaveopt,check"

qemu-system-x86_64 \
  -enable-kvm -m "$ALLOCATED_RAM" -cpu Penryn,kvm=on,vendor=GenuineIntel,+invtsc,vmware-cpuid-freq=on,"$MY_OPTIONS" \
  -machine q35 \
  -device qemu-xhci,id=xhci \
  -device usb-kbd,bus=xhci.0 -device usb-tablet,bus=xhci.0 \
  -smp "$CPU_THREADS",cores="$CPU_CORES",sockets="$CPU_SOCKETS" \
  -device usb-ehci,id=ehci \
  -device isa-applesmc,osk="ourhardworkbythesewordsguardedpleasedontsteal(c)AppleComputerInc" \
  -drive if=pflash,format=raw,readonly=on,file="$OVMF_CODE" \
  -drive if=pflash,format=raw,file="$OVMF_VARS" \
  -smbios type=2 \
  -device ich9-intel-hda -device hda-duplex \
  -device ich9-ahci,id=sata \
  -cdrom OpenCore/OpenCore-v21.iso \
  -monitor stdio \
  -device vmware-svga
  #-drive id=OpenCoreBoot,if=none,snapshot=on,format=qcow2,file="$REPO_PATH/OpenCore/OpenCore.qcow2" \
  #-device ide-hd,bus=sata.2,drive=OpenCoreBoot \
  # add once OpenCore is running
  #-device ide-hd,bus=sata.3,drive=InstallMedia \
  #-drive id=InstallMedia,if=none,file="$REPO_PATH/BaseSystem.img",format=raw \
  #-drive id=MacHDD,if=none,file="$REPO_PATH/mac_hdd_ng.img",format=qcow2 \
  #-device ide-hd,bus=sata.4,drive=MacHDD \
  #-netdev user,id=net0,hostfwd=tcp::2222-:22 -device virtio-net-pci,netdev=net0,id=net0,mac=52:54:00:c9:18:27 \
