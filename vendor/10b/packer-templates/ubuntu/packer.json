{"variables": {"is_headless": "false", "user_name": "ubuntu", "user_pass": "ubuntu"}, "builders": [{"type": "qemu", "iso_urls": ["ubuntu-24.04.1-desktop-amd64.iso", "https://releases.ubuntu.com/24.04.1/ubuntu-24.04.1-desktop-amd64.iso"], "iso_checksum": "sha256:c2e6f4dc37ac944e2ed507f87c6188dd4d3179bf4a3f9e110d3c88d1f3294bdc", "headless": "{{ user `is_headless` }}", "boot_key_interval": "50ms", "boot_wait": "5s", "disk_size": "5000M", "http_directory": "http", "memory": 5048, "format": "qcow2", "display": "sdl", "accelerator": "kvm", "vm_name": "ubuntu-22.04-packer.img", "net_device": "virtio-net", "disk_interface": "virtio", "boot_command": ["c<wait>linux /casper/vmlinuz --- autoinstall ds='nocloud-net;s=http://{{ .HTTPIP }}:{{ .HTTPPort }}/'", "<enter><wait2s>", "initrd /casper/initrd", "<enter><wait2s>", "boot<enter><wait2s>"], "qemuargs": [["-m", "5048M"], ["-cpu", "host"], ["-m", "5048M"], ["-smp", "cpus=4"]], "ssh_username": "{{ user `user_name` }}", "ssh_password": "{{ user `user_pass` }}", "ssh_timeout": "60m", "ssh_handshake_attempts": 420}], "provisioners": [{"type": "shell", "execute_command": "echo '{{ user `user_name` }}' | {{.Vars}} sudo -S -E bash '{{.Path}}'", "script": "scripts/init.sh"}]}