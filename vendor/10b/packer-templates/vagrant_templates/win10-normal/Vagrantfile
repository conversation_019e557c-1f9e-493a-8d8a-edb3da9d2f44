# -*- mode: ruby -*-
# vi: set ft=ruby :

# All Vagrant configuration is done below. The "2" in Vagrant.configure
# configures the configuration version (we support older styles for
# backwards compatibility). Please don't change it unless you know what
# you're doing.
Vagrant.configure("2") do |config|

    #  config.vm.define :test_vm1 do |test_vm1|
    #    test_vm1.vm.network :private_network, 
    #        :mode => "bridge",
    #        :type => "bridge",
    #	:dev => "virbr0",
    #        :ip => "*************"
    #  end
    config.vm.network :private_network, ip: "*************", type: "dhcp", auto_config: false
    #config.vm.guest = :linux
    #  config.vm.define :test_vm1 do |test_vm1|
    #    test_vm1.vm.network :public_network,
    #      :dev => "virbr0",
    #      :mode => "bridge",
    #      :type => "bridge"
    #  end
    #config.vm.network :public_network, dev: "enp8s1"
    #config.vm.network :public_network, :bridge: 'eth1'
    #config.vm.network "public_network", bridge: "eth1"
    #config.vm.synced_folder ".", "/vagrant", type: "nfs", disabled: true
    #config.vm.network "public_network", auto_config: false
    #config.vm.network "private_network", type: "dhcp"
      # The most common configuration options are documented and commented below.
      # For a complete reference, please see the online documentation at
      # https://docs.vagrantup.com.
    
    
    
    
    
    # SED REPLACE HERE
    #  
    
      config.vm.provider :libvirt do |libvirt|
        # very useful when having mouse issues when viewing VM via VNC
        libvirt.input :type => "tablet", :bus => "usb"
        # we want guest utils being available, (e.g. for getting IP)
        libvirt.channel :type  => 'unix',     :target_name => 'org.qemu.guest_agent.0', :target_type => 'virtio'
        libvirt.driver = "kvm"
        libvirt.default_prefix = ""
        libvirt.disk_bus       = "virtio"
        libvirt.driver         = "kvm"
        #libvirt.graphics_type  = "spice"
        libvirt.memory         = 4096
        libvirt.cpus         = 4
        libvirt.nic_model_type = "virtio"
        libvirt.sound_type     = "ich6"
        libvirt.video_type     = "qxl"
        libvirt.graphics_ip = '0.0.0.0'
    #    libvirt.graphics_port = 5901
        libvirt.graphics_type = "vnc"
      end
      # Every Vagrant development environment requires a box. You can search for
      # boxes at https://vagrantcloud.com/search.
    
      config.vm.box = "REPLACENAME"
      config.vm.define "REPLACENAME"
    
    
      #config.vm.network :public_network, :dev => "virbr0", :mode => "bridge", :type => "bridge"
      #config.vm.network :forwarded_port, guest: 3389, host: 3389
      #config.vm.communicator = "ssh"
    config.vm.communicator = "winrm"
    #    config.vm.synced_folder ".", "/vagrant", SharedFoldersEnableSymlinksCreate: false
    
    
       config.vm.synced_folder '.', "/cygdrive/c/vagrant-rsync",
        type: "rsync",
    #    rsync__args: ["--verbose","--archive", "--delete", "-z", "--copy-links", "--no-owner", "--no-group", "-e"],
        rsync__exclude: [".git/",".vagrant/"],
        rsync__auto: true,
        disabled: false, nfs: false, nfs_prune: false, server_setup: false
    
     config.nfs.verify_installed = false
      config.vm.synced_folder '.', '/vagrant', disabled: true
    
      
        config.vm.guest = :windows
      #config.ssh.username = "root"
      #config.ssh.password = "root"
      #config.ssh.insert_key = false
      #config.vm.hostname = "svr1"
      config.vm.post_up_message = "Thanks SECD team for the source image!"
      #config.ssh.password = "root"
      #config.vm.synced_folder ".", "/vagrant", SharedFoldersEnableSymlinksCreate: false
      # RSYNC here
    
    
      
      # Disable automatic box update checking. If you disable this, then
      # boxes will only be checked for updates when the user runs
      # `vagrant box outdated`. This is not recommended.
      # config.vm.box_check_update = false
    
      # Create a forwarded port mapping which allows access to a specific port
      # within the machine from a port on the host machine. In the example below,
      # accessing "localhost:8080" will access port 80 on the guest machine.
      # NOTE: This will enable public access to the opened port
      # config.vm.network "forwarded_port", guest: 80, host: 8080
    
      # Create a forwarded port mapping which allows access to a specific port
      # within the machine from a port on the host machine and only allow access
      # via 127.0.0.1 to disable public access
      # config.vm.network "forwarded_port", guest: 80, host: 8080, host_ip: "127.0.0.1"
    
      # Create a private network, which allows host-only access to the machine
      # using a specific IP.
      # config.vm.network "private_network", ip: "*************"
    
      # Create a public network, which generally matched to bridged network.
      # Bridged networks make the machine appear as another physical device on
      # your network.
      # config.vm.network "public_network"
    
      # Share an additional folder to the guest VM. The first argument is
      # the path on the host to the actual folder. The second argument is
      # the path on the guest to mount the folder. And the optional third
      # argument is a set of non-required options.
      # config.vm.synced_folder "../data", "/vagrant_data"
    
      # Provider-specific configuration so you can fine-tune various
      # backing providers for Vagrant. These expose provider-specific options.
      # Example for VirtualBox:
      #
      # config.vm.provider "virtualbox" do |vb|
      #   # Display the VirtualBox GUI when booting the machine
      #   vb.gui = true
      #
      #   # Customize the amount of memory on the VM:
      #   vb.memory = "1024"
      # end
      #
      # View the documentation for the provider you are using for more
      # information on available options.
    
      # Enable provisioning with a shell script. Additional provisioners such as
      # Ansible, Chef, Docker, Puppet and Salt are also available. Please see the
      # documentation for more information about their specific syntax and use.
      # config.vm.provision "shell", inline: <<-SHELL
      #   apt-get update
      #   apt-get install -y apache2
      # SHELL
    end
    
    