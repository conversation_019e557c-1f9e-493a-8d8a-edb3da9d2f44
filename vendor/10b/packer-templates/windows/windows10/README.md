# Windows 10 Packer 

## Pre Installed Packages

* choco

## Dependencies

### Packer Plugins

```bash
packer plugins install "github.com/hashicorp/qemu" ">=1.1.0"
packer plugins install "github.com/rgl/windows-update" "0.16.8"
packer plugins install "github.com/hashicorp/vagrant" "~> 1"
```

## Build
Use the `build.sh` script, and provide the ISO path as variable:
```bash
bash build.sh /home/<USER>/Downloads/Win10_2004_English_x64.iso
```

### Debug Build

```bash
nix-shell -p $remote_desktop
```

## References

1. https://www.detectionlab.network/deployment/libvirt/
2. https://os.click/en/Windows:Windows_10:22H2:19045.4780:Multi-Edition:en-us:x64