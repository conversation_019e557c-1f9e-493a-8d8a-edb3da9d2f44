#!/bin/bash
# define work dir here $WORKDIR so we do not need to do path traversal
# xhost +si:localuser:root
# bash build.sh /root/Packer_Projects/Packer-Windows10/
if [ "$#" -ne 1 ]; then
    echo "[!] Error: provide path to ISO"
    exit -1
fi
echo "[+] Starting build with ISO: $1" 
mkdir ../../vagrant_templates/win10-normal
echo "[+] Target folder created"
echo "[+] Copying Vagrant template config"
cp Vagrantfile ../../vagrant_templates/win10-normal/
date > start
echo "[+] Calculating SHA256 hash of $1"
hash=$(sha256sum $1 | awk '{ print $1 }')
echo "[+] SHA256 hash is: $hash"
echo "[+] Starting build"
DISPLAY=:0 PACKER_LOG=1 packer build -only=qemu -var build_uuid=`uuidgen` -var "iso_path=$1" -var "iso_sha256=$hash" -var "out_path=../vagrant_templates/win10-normal" packer.json
retVal=$?
if [ $retVal -ne 0 ]; then
    echo "Error"
    exit $retVal
fi
date >> start
echo "Done, see times:"
cat start
#cd -
#pwd