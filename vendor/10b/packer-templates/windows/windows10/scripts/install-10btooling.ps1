# Installation of tools needed for monitoring of VM

# Download Fibratus Release 2.2.2
$fibratusURL = "https://github.com/rabbitstack/fibratus/releases/download/v2.2.1/fibratus-2.2.1-amd64.msi"
Invoke-WebRequest $fibratusURL -OutFile c:\fibratus-2.2.1-amd64.msi

# Fibratus installation
cd C:\
./fibratus-2.2.1-amd64.msi

# TurdParty Fibratus configuration
Write-Host "🔧 Configuring Fibratus for TurdParty..." -ForegroundColor Green

# Copy TurdParty Fibratus configuration files
Copy-Item "C:\temp\fibratus-turdparty.yml" "C:\temp\" -Force
Copy-Item "C:\temp\configure-fibratus.ps1" "C:\temp\" -Force

# Run TurdParty Fibratus configuration
& "C:\temp\configure-fibratus.ps1"

Write-Host "✅ Fibratus configured for TurdParty malware analysis" -ForegroundColor Green

# Drop 10Baht agent (frida process/file/registry/network tree tracer, custom hooks, etc.)
