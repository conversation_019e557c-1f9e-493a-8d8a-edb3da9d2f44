echo "done" >> C:\\Users\\<USER>\\Desktop\\provisiones 
echo "+ installing guest agent" >> C:\guest-agent-setup
msiexec /qb /x C:\\qemu-ga-x86_64.msi
msiexec.exe /i C:\qemu-ga-x86_64.msi /qn /l*v "C:\Users\<USER>\Desktop\qemu.log"
echo "+ installed guest agent" >> C:\guest-agent-setup
dir a:\\ >> C:\guest-agent-setup
echo "+ installing qemu agent serial PCI drivers" >> C:\guest-agent-setup
whoami >> C:\Users\<USER>\Desktop\provisiones
echo "+ installing qemu/kvm balloon service" >> C:\guest-agent-setup
blnsvr.exe -i
echo "+ disabling power saving and screen lock" >> C:\guest-agent-setup
powercfg -change -monitor-timeout-ac 0
Set-ItemProperty -Path ‘HKCU:\Software\Policies\Microsoft\Windows\Control Panel\Desktop\’ -Name ScreenSaveTimeOut -Value 0
Set-ItemProperty -Path ‘HKCU:\Software\Policies\Microsoft\Windows\Control Panel\Desktop\’ -Name ScreenSaveActive -Value 0
Set-ItemProperty -Path ‘HKCU:\Software\Policies\Microsoft\Windows\Control Panel\Desktop\’ -Name ScreenSaverIsSecure -Value 0


echo "+ installing choco for package management" >> C:\guest-agent-setup
$chocoExePath = 'C:\ProgramData\Chocolatey\bin'

if ($($env:Path).ToLower().Contains($($chocoExePath).ToLower())) {
  echo "Chocolatey found in PATH, skipping install..."
  Exit
}

# Add to system PATH
$systemPath = [Environment]::GetEnvironmentVariable('Path',[System.EnvironmentVariableTarget]::Machine)
$systemPath += ';' + $chocoExePath
[Environment]::SetEnvironmentVariable("PATH", $systemPath, [System.EnvironmentVariableTarget]::Machine)

# Update local process' path
$userPath = [Environment]::GetEnvironmentVariable('Path',[System.EnvironmentVariableTarget]::User)
if($userPath) {
  $env:Path = $systemPath + ";" + $userPath
} else {
  $env:Path = $systemPath
}

# Run the installer
iex ((new-object net.webclient).DownloadString('https://chocolatey.org/install.ps1'))

#rem make symlink for c:/vagrant share
#mklink /D "C:\Program Files\OpenSSH\vagrant" "C:\vagrant"

echo "done" >> C:\Users\<USER>\Desktop\provisiones

# install dependencies
choco.exe install rsync -y
choco.exe install 7zip -y
choco.exe install openssh -params '"/SSHServerFeature /KeyBasedAuthenticationFeature"' -y
choco.exe install openssh -y --force
choco.exe install dos2unix -y

# install keys for SSH auth
cd C:\Users\<USER>\
mkdir .ssh
cd .ssh
echo "ssh-rsa TODODODODODO vagrant" > .\authorized_keys
dos2unix.exe .\authorized_keys

#
$stream = [IO.File]::OpenWrite('C:\ProgramData\ssh\sshd_config')
$stream.SetLength($stream.Length - 130)
$stream.Close()
$stream.Dispose()

#net stop sshd
#net start sshd


exit 0