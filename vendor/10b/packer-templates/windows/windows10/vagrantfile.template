# -*- mode: ruby -*-
# vi: set ft=ruby :

Vagrant.require_version ">= 1.6.2"

Vagrant.configure("2") do |config|
    config.vm.box = "REPLACENAME"
#    config.vm.define = "REPLACENAME"
    config.vm.communicator = "winrm"
    config.vm.synced_folder ".", "/vagrant", SharedFoldersEnableSymlinksCreate: false

    config.vm.guest = :windows

    config.vm.network :forwarded_port, guest: 3389, host: 3399, id: "rdp", auto_correct: true
    config.vm.network :forwarded_port, guest: 5985, host: 5985, id: "winrm", auto_correct: true

    config.vm.provider "vmware_fusion" do |v, override|
        v.vmx["memsize"] = "2048"
        v.vmx["numvcpus"] = "2"
        v.vmx["ethernet0.virtualDev"] = "vmxnet3"
        v.vmx["scsi0.virtualDev"] = "lsisas1068"
    end

    config.vm.provider "vmware_workstation" do |v, override|
        v.vmx["memsize"] = "2048"
        v.vmx["numvcpus"] = "2"
        v.vmx["ethernet0.virtualDev"] = "vmxnet3"
        v.vmx["scsi0.virtualDev"] = "lsisas1068"
    end

    config.vm.provider "virtualbox" do |v, override|
        v.customize ["modifyvm", :id, "--memory", 2048]
        v.customize ["modifyvm", :id, "--cpus", 2]
	v.customize ["modifyvm", :id, "--vram", 99]
	v.customize ["setextradata", "global", "GUI/SuppressMessages", "all" ]
    end

end
