Check: https://github.com/quickemu-project/quickemu


```
  virtualisation = {
    libvirtd = {
      enable = true;
      qemu = {
        swtpm.enable = true;
        ovmf.enable = true;
        ovmf.packages = [ pkgs.OVMFFull.fd ];
      };
    };
    spiceUSBRedirection.enable = true;
  };
  services.spice-vdagentd.enable = true;
```

https://github.com/rgl/windows-vagrant/blob/master/windows-11-23h2/autounattend.xml


# Important
chmod 777 -R files/virtio-drivers

TODO:
find OVMF.fd path and put it into packer.json
finish helper.sh
https://discuss.hashicorp.com/t/winrm-does-not-connect-packer-build-for-windows-11-in-virtualbox-in-ubuntu-20-04-vm-on-kvm-host-unraid/42514



          [
            "-bios",
            "/nix/store/zhbj009n57myjhqk9nqzj5z3xmipmm4x-OVMF-202402-fd/FV/OVMF.fd"
          ]

          or

                    [  
            "-drive",
            "if=pflash,format=raw,readonly=on,unit=0,file=/run/libvirt/nix-ovmf/OVMF_CODE.fd"
          ],
          [ 
            "-drive",
            "if=pflash,format=raw,readonly=on,unit=1,file=/run/libvirt/nix-ovmf/OVMF_VARS.fd"
          ],


      "boot_command": [
        "<leftShiftOn><f10><leftShiftOff><wait>",
        "reg add HKLM\\SYSTEM\\Setup\\LabConfig /t REG_DWORD /v BypassTPMCheck /d 1<return>",
        "reg add HKLM\\SYSTEM\\Setup\\LabConfig /t REG_DWORD /v BypassSecureBootCheck /d 1<return><wait>",
        "reg add HKLM\\System\\Setup\\LabConfig /t REG_DWORD /v BypassRAMCheck /d 1<return><wait>",
        "reg add HKLM\\System\\Setup\\LabConfig /t REG_DWORD /v BypassCPUCheck /d 1<return><wait>",
        "reg add HKLM\\System\\Setup\\LabConfig /t REG_DWORD /v BypassStorageCheck /d 1<return><wait>",
        "exit<return>",
        "<wait><return>"
      ],

                  "floppy_files": [
          "config/Autounattend.xml",
          "files/virtio-drivers/NetKVM/w10/amd64/*.cat",
          "files/virtio-drivers/NetKVM/w10/amd64/*.inf",
          "files/virtio-drivers/NetKVM/w10/amd64/*.sys",
          "files/virtio-drivers/vioscsi/w10/amd64/*.cat",
          "files/virtio-drivers/vioscsi/w10/amd64/*.inf",
          "files/virtio-drivers/vioscsi/w10/amd64/*.sys",
          "files/virtio-drivers/viostor/w10/amd64/*.cat",
          "files/virtio-drivers/viostor/w10/amd64/*.inf",
          "files/virtio-drivers/viostor/w10/amd64/*.sys",
          "files/virtio-drivers/Balloon/w10/amd64/*.inf",
          "files/virtio-drivers/Balloon/w10/amd64/*.sys",
          "files/virtio-drivers/Balloon/w10/amd64/*.cat",
          "files/virtio-drivers/qemupciserial/w10/amd64/*.cat",
          "files/virtio-drivers/qemupciserial/w10/amd64/*.inf",
          "files/virtio-drivers/qemupciserial/w10/amd64/*.sys",
          "files/virtio-drivers/vioserial/w10/amd64/vioser.sys",
          "files/virtio-drivers/vioserial/w10/amd64/vioser.inf",
          "files/virtio-drivers/vioserial/w10/amd64/vioser.cat",
          "scripts/configure-winrm.ps1",
          "scripts/update-windows.ps1"
        ],



# Autounattend.xml Generator
https://schneegans.de/windows/unattend-generator/
Source: https://github.com/cschneegans/unattend-generator/

