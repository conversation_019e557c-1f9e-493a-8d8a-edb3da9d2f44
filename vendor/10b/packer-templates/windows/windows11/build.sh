#!/bin/bash
# define work dir here $WORKDIR so we do not need to do path traversal
# xhost +si:localuser:root
# bash build.sh /home/<USER>/Downloads/windows-11-24h2.iso 

# setup environment variables
export ENV_EFI_CODE_SECURE=/run/libvirt/nix-ovmf/OVMF_CODE.fd ENV_EFI_VARS_SECURE=/run/libvirt/nix-ovmf/OVMF_VARS.fd

if [ "$#" -ne 1 ]; then
    echo "[!] Error: provide path to ISO"
    exit -1
fi
echo "[+] Starting build with ISO: $1" 
mkdir ../../vagrant_templates/win11-normal
echo "[+] Target folder created"
echo "[+] Copying Vagrant template config"
cp Vagrantfile ../../vagrant_templates/win11-normal/
date > start
echo "[+] Calculating hash of $1"
hash=$(md5sum $1 | awk '{ print $1 }')
echo "[+] Hash is: $hash"
echo "[+] Starting build"
DISPLAY=:0 PACKER_LOG=1 packer build -only=qemu -var build_uuid=`uuidgen` -var "iso_path=$1" -var "iso_md5=$hash" -var "out_path=../vagrant_templates/win11-normal" packer.json
retVal=$?
if [ $retVal -ne 0 ]; then
    echo "Error"
    exit $retVal
fi
date >> start
echo "Done, see times:"
cat start
#cd -
#pwd