#!/bin/bash
cwd=$(pwd)
rm -rf virtio-drivers
mkdir virtio-drivers
rm -rf /tmp/virtio-drivers
cd /tmp && mkdir virtio-drivers
cd virtio-drivers
curl -L -O https://fedorapeople.org/groups/virt/virtio-win/direct-downloads/stable-virtio/virtio-win.iso
mkdir mnt
sudo mount -o loop virtio-win.iso mnt/
cp -R mnt/* $cwd/virtio-drivers
find virtio-drivers -type d -exec chmod u+rwx {} \;
sudo umount mnt/
cd $cwd