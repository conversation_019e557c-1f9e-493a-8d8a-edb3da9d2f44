{"builders": [{"accelerator": "kvm", "boot_wait": "2s", "communicator": "winrm", "disk_cache": "unsafe", "disk_interface": "ide", "disk_size": "75000M", "cd_files": ["./files/virtio-drivers/*"], "cd_label": "cidata", "floppy_files": ["config/Autounattend.xml", "files/virtio-drivers/NetKVM/w11/amd64/*.cat", "files/virtio-drivers/NetKVM/w11/amd64/*.inf", "files/virtio-drivers/NetKVM/w11/amd64/*.sys", "files/virtio-drivers/vioscsi/w11/amd64/*.cat", "files/virtio-drivers/vioscsi/w11/amd64/*.inf", "files/virtio-drivers/vioscsi/w11/amd64/*.sys", "files/virtio-drivers/viostor/w11/amd64/*.cat", "files/virtio-drivers/viostor/w11/amd64/*.inf", "files/virtio-drivers/viostor/w11/amd64/*.sys", "files/virtio-drivers/Balloon/w11/amd64/*.inf", "files/virtio-drivers/Balloon/w11/amd64/*.sys", "files/virtio-drivers/Balloon/w11/amd64/*.cat", "files/virtio-drivers/qemupciserial/w11/amd64/*.cat", "files/virtio-drivers/qemupciserial/w11/amd64/*.inf", "files/virtio-drivers/qemupciserial/w11/amd64/*.sys", "files/virtio-drivers/vioserial/w11/amd64/vioser.sys", "files/virtio-drivers/vioserial/w11/amd64/vioser.inf", "files/virtio-drivers/vioserial/w11/amd64/vioser.cat", "scripts/configure-winrm.ps1", "scripts/update-windows.ps1"], "format": "qcow2", "headless": "false", "host_port_max": "6985", "host_port_min": "5985", "iso_checksum": "md5:{{user `iso_md5`}}", "iso_url": "{{user `iso_path`}}", "net_device": "virtio-net", "vtpm": "true", "qemuargs": [["-enable-kvm"], ["-cpu", "host"], ["-machine", "q35,smm=on"], ["-m", "5048M"], ["-m", "5048M"], ["-smp", "cpus=6"], ["-vga", "qxl"], ["-usb"], ["-device", "virtio-keyboard"], ["-device", "virtio-gpu-pci"], ["-device", "virtio-mouse"]], "shutdown_command": "shutdown /s /t 5 /f /d p:4:1 /c \"Packer Shutdown\"", "skip_compaction": false, "firmware": "efi", "efi_firmware_code": "/nix/store/cspaja242a9l1nx5y68aadyvjp4kbc7w-OVMF-202402-fd/FV/OVMF_CODE.ms.fd", "efi_firmware_vars": "/nix/store/cspaja242a9l1nx5y68aadyvjp4kbc7w-OVMF-202402-fd/FV/OVMF_VARS.ms.fd", "boot_command": ["<return>", "<return>", "<wait20s>", "<leftShiftOn><f10><leftShiftOff><wait>", "exit<wait20><return><return>"], "type": "qemu", "vm_name": "win-11", "winrm_insecure": "true", "winrm_password": "vagrant", "winrm_timeout": "12h", "winrm_use_ssl": "false", "winrm_username": "vagrant"}], "post-processors": [{"keep_input_artifact": false, "output": "../../vagrant_templates/win11-normal/windows11.box", "type": "vagrant", "vagrantfile_template": "vagrantfile.template"}], "provisioners": [{"type": "windows-update"}, {"destination": "C:\\qemu-ga-x86_64.msi", "source": "files/virtio-drivers/guest-agent/qemu-ga-x86_64.msi", "type": "file"}, {"destination": "C:\\blnsvr.pdb", "source": "files/virtio-drivers/Balloon/w10/amd64/blnsvr.pdb", "type": "file"}, {"destination": "C:\\blnsvr.exe", "source": "files/virtio-drivers/Balloon/w10/amd64/blnsvr.exe", "type": "file"}, {"destination": "C:\\balloon.pdb", "source": "files/virtio-drivers/Balloon/w10/amd64/balloon.pdb", "type": "file"}, {"destination": "C:\\balloon.inf", "source": "files/virtio-drivers/Balloon/w10/amd64/balloon.inf", "type": "file"}, {"destination": "C:\\balloon.sys", "source": "files/virtio-drivers/Balloon/w10/amd64/balloon.sys", "type": "file"}, {"destination": "C:\\balloon.cat", "source": "files/virtio-drivers/Balloon/w10/amd64/balloon.cat", "type": "file"}, {"destination": "C:\\vioser.sys", "source": "files/virtio-drivers/vioserial/w10/amd64/vioser.sys", "type": "file"}, {"destination": "C:\\vioser.inf", "source": "files/virtio-drivers/vioserial/w10/amd64/vioser.inf", "type": "file"}, {"destination": "C:\\vioser.cat", "source": "files/virtio-drivers/vioserial/w10/amd64/vioser.cat", "type": "file"}, {"destination": "C:\\vioser.pdb", "source": "files/virtio-drivers/vioserial/w10/amd64/vioser.pdb", "type": "file"}, {"elevated_password": "vagrant", "elevated_user": "vagrant", "scripts": ["scripts/install-guest-tools.ps1", "scripts/enable-rdp.ps1", "scripts/disable-hibernate.ps1", "scripts/disable-autologin.ps1", "scripts/enable-uac.ps1", "scripts/no-expiration.ps1", "scripts/install-10btooling.ps1"], "type": "powershell"}, {"restart_check_command": "powershell -command \"& {if ((get-content C:\\ProgramData\\lastboot.txt) -eq (Get-WmiObject win32_operatingsystem).LastBootUpTime) {Write-Output 'Sleeping for 600 seconds to wait for reboot'; start-sleep 600} else {Write-Output 'Reboot complete'}}\"", "restart_command": "powershell \"& {(Get-WmiObject win32_operatingsystem).LastBootUpTime > C:\\ProgramData\\lastboot.txt; Restart-Computer -force}\"", "type": "windows-restart"}], "variables": {"build_uuid": "", "iso_md5": "", "iso_path": "", "out_path": "", "switch_name": "<PERSON><PERSON><PERSON>"}}